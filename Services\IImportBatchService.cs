using ParaHockeyApp.Models.Entities;
using ParaHockeyApp.ViewModels;

namespace ParaHockeyApp.Services
{
    /// <summary>
    /// Service for managing import batch operations and summary reporting
    /// </summary>
    public interface IImportBatchService
    {
        /// <summary>
        /// Gets all import batches with pagination and filtering
        /// </summary>
        /// <param name="pageNumber">Page number (1-based)</param>
        /// <param name="pageSize">Number of items per page</param>
        /// <param name="statusFilter">Optional status filter</param>
        /// <param name="searchTerm">Optional search term for filename</param>
        /// <param name="fromDate">Optional start date filter</param>
        /// <param name="toDate">Optional end date filter</param>
        /// <returns>Paginated list of import batches</returns>
        Task<ImportBatchListViewModel> GetImportBatchesAsync(
            int pageNumber = 1, 
            int pageSize = 20, 
            string? statusFilter = null, 
            string? searchTerm = null, 
            DateTime? fromDate = null, 
            DateTime? toDate = null);

        /// <summary>
        /// Gets detailed summary for a specific import batch
        /// </summary>
        /// <param name="batchId">The import batch ID</param>
        /// <returns>Detailed batch summary view model</returns>
        Task<ImportBatchViewModel> GetBatchSummaryAsync(int batchId);

        /// <summary>
        /// Gets queue status for all statuses within a batch
        /// </summary>
        /// <param name="batchId">The import batch ID</param>
        /// <returns>List of queue status view models</returns>
        Task<List<ImportQueueStatusViewModel>> GetQueueStatusesAsync(int batchId);

        /// <summary>
        /// Gets import progress information for real-time updates
        /// </summary>
        /// <param name="batchId">The import batch ID</param>
        /// <returns>Import progress view model</returns>
        Task<ImportProgressViewModel> GetImportProgressAsync(int batchId);

        /// <summary>
        /// Updates batch statistics by recalculating from temp members
        /// </summary>
        /// <param name="batchId">The import batch ID</param>
        /// <returns>Updated batch summary</returns>
        Task<ImportBatchViewModel> RefreshBatchStatisticsAsync(int batchId);

        /// <summary>
        /// Deletes an import batch and all associated temp members
        /// </summary>
        /// <param name="batchId">The import batch ID</param>
        /// <param name="deletedBy">Username of the person deleting</param>
        /// <returns>True if successfully deleted</returns>
        Task<bool> DeleteImportBatchAsync(int batchId, string deletedBy);

        /// <summary>
        /// Gets recent import activity for dashboard
        /// </summary>
        /// <param name="days">Number of days to look back</param>
        /// <returns>List of recent import batches</returns>
        Task<List<ImportBatchViewModel>> GetRecentImportActivityAsync(int days = 7);

        /// <summary>
        /// Gets import statistics for reporting
        /// </summary>
        /// <param name="fromDate">Start date for statistics</param>
        /// <param name="toDate">End date for statistics</param>
        /// <returns>Import statistics summary</returns>
        Task<ImportStatisticsViewModel> GetImportStatisticsAsync(DateTime fromDate, DateTime toDate);
    }

    /// <summary>
    /// View model for import statistics reporting
    /// </summary>
    public class ImportStatisticsViewModel
    {
        public DateTime FromDate { get; set; }
        public DateTime ToDate { get; set; }
        public int TotalBatches { get; set; }
        public int TotalMembersProcessed { get; set; }
        public int TotalMembersCreated { get; set; }
        public int TotalMembersMerged { get; set; }
        public int TotalDuplicatesResolved { get; set; }
        public int TotalValidationErrors { get; set; }
        public double AverageProcessingTime { get; set; } // in minutes
        public double SuccessRate => TotalMembersProcessed > 0 ? 
            ((TotalMembersCreated + TotalMembersMerged) / (double)TotalMembersProcessed) * 100 : 0;

        // Top uploaders
        public List<UploaderStatistic> TopUploaders { get; set; } = new();
        
        // Daily activity
        public List<DailyImportStatistic> DailyActivity { get; set; } = new();
    }

    /// <summary>
    /// Statistics for individual uploaders
    /// </summary>
    public class UploaderStatistic
    {
        public string UploaderName { get; set; } = string.Empty;
        public int BatchCount { get; set; }
        public int MembersProcessed { get; set; }
        public double SuccessRate { get; set; }
    }

    /// <summary>
    /// Daily import activity statistics
    /// </summary>
    public class DailyImportStatistic
    {
        public DateTime Date { get; set; }
        public int BatchCount { get; set; }
        public int MembersProcessed { get; set; }
        public int MembersCreated { get; set; }
    }
}