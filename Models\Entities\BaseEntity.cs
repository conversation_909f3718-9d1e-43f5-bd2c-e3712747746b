using System.ComponentModel.DataAnnotations;

namespace ParaHockeyApp.Models.Entities
{
    /// <summary>
    /// Base entity class that provides common properties for all entities.
    /// Enhanced with comprehensive audit fields to track who created/modified each entity
    /// and whether it was done through admin panel or member self-service.
    /// </summary>
    public abstract class BaseEntity
    {
        /// <summary>
        /// Primary key identifier
        /// </summary>
        [Key]
        public int Id { get; set; }

        // === EXISTING FIELDS (PRESERVED) ===
        
        /// <summary>
        /// Date when the entity was created (UTC)
        /// </summary>
        [Required]
        public DateTime DateCreated { get; set; } = DateTime.UtcNow;

        /// <summary>
        /// Whether the entity is active (soft delete pattern)
        /// </summary>
        [Required]
        public bool IsActive { get; set; } = true;

        // === NEW AUDIT FIELDS ===

        /// <summary>
        /// Date when the entity was last modified (UTC)
        /// NULL if never modified since creation
        /// </summary>
        public DateTime? DateModified { get; set; }

        /// <summary>
        /// Member ID who created this entity (for self-service operations)
        /// NULL if created by admin or system
        /// </summary>
        public int? CreatedByMemberId { get; set; }

        /// <summary>
        /// Admin ID who created this entity (for admin operations)
        /// NULL if created by member or system
        /// </summary>
        public int? CreatedByAdminId { get; set; }

        /// <summary>
        /// Member ID who last modified this entity (for self-service operations)
        /// NULL if modified by admin or system, or never modified
        /// </summary>
        public int? ModifiedByMemberId { get; set; }

        /// <summary>
        /// Admin ID who last modified this entity (for admin operations)
        /// NULL if modified by member or system, or never modified
        /// </summary>
        public int? ModifiedByAdminId { get; set; }

        /// <summary>
        /// Source of the creation action (System, SelfService, AdminPanel)
        /// </summary>
        [Required]
        public ActionSource CreatedBySource { get; set; } = ActionSource.System;

        /// <summary>
        /// Source of the last modification action (System, SelfService, AdminPanel)
        /// NULL if never modified since creation
        /// </summary>
        public ActionSource? ModifiedBySource { get; set; }

        // === NAVIGATION PROPERTIES ===

        /// <summary>
        /// Navigation property to the member who created this entity
        /// </summary>
        public virtual Member? CreatedByMember { get; set; }

        /// <summary>
        /// Navigation property to the admin who created this entity
        /// </summary>
        public virtual AdminUser? CreatedByAdmin { get; set; }

        /// <summary>
        /// Navigation property to the member who last modified this entity
        /// </summary>
        public virtual Member? ModifiedByMember { get; set; }

        /// <summary>
        /// Navigation property to the admin who last modified this entity
        /// </summary>
        public virtual AdminUser? ModifiedByAdmin { get; set; }

        // === COMPUTED PROPERTIES ===

        /// <summary>
        /// Virtual property that can be overridden by derived classes to provide custom validation logic
        /// </summary>
        public virtual bool EstValide => IsActive;

        /// <summary>
        /// Display formatted creation date
        /// </summary>
        public string DateCreationFormatted => DateCreated.ToString("yyyy-MM-dd HH:mm");

        /// <summary>
        /// Display formatted modification date
        /// </summary>
        public string? DateModifiedFormatted => DateModified?.ToString("yyyy-MM-dd HH:mm");

        /// <summary>
        /// Who created this entity (for display purposes)
        /// </summary>
        public string CreatedByDisplay => CreatedBySource switch
        {
            ActionSource.System => "System",
            ActionSource.SelfService => CreatedByMember != null ? $"{CreatedByMember.FirstName} {CreatedByMember.LastName} (Self)" : "Member (Self)",
            ActionSource.AdminPanel => CreatedByAdmin != null ? $"{CreatedByAdmin.Name} (Admin)" : "Admin",
            _ => "Unknown"
        };

        /// <summary>
        /// Who last modified this entity (for display purposes)
        /// </summary>
        public string? ModifiedByDisplay => ModifiedBySource switch
        {
            ActionSource.System => "System",
            ActionSource.SelfService => ModifiedByMember != null ? $"{ModifiedByMember.FirstName} {ModifiedByMember.LastName} (Self)" : "Member (Self)",
            ActionSource.AdminPanel => ModifiedByAdmin != null ? $"{ModifiedByAdmin.Name} (Admin)" : "Admin",
            null => null,
            _ => "Unknown"
        };

        /// <summary>
        /// Whether this entity has been modified since creation
        /// </summary>
        public bool HasBeenModified => DateModified.HasValue;
    }
}