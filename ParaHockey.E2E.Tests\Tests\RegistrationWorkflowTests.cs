using Xunit;
using FluentAssertions;
using ParaHockey.E2E.Tests.Infrastructure;
using ParaHockey.E2E.Tests.PageObjects;
using OpenQA.Selenium;

namespace ParaHockey.E2E.Tests.Tests
{
    public class RegistrationWorkflowTests : BaseTest, IDisposable
    {
        private readonly RegistrationPage _registrationPage;

        public RegistrationWorkflowTests()
        {
            _registrationPage = new RegistrationPage(Driver, Wait);
        }

        [Fact]
        public void JuniorRegistration_ShouldShowParentFields()
        {
            try
            {
                // Arrange
                NavigateToRegistrationPage();

                // Act
                _registrationPage.SelectRegistrationType("Junior");
                Thread.Sleep(2000); // Wait for dynamic fields

                // Assert
                _registrationPage.IsParentSectionVisible().Should().BeTrue("Parent section should be visible for Junior registration");
                _registrationPage.IsEmergencyContactSectionVisible().Should().BeFalse("Emergency contact section should be hidden for Junior registration");
            }
            catch (Exception ex)
            {
                TakeScreenshot("JuniorRegistration_ShouldShowParentFields");
                throw;
            }
        }

        [Theory]
        [InlineData("Development")]
        [InlineData("Elite")]
        [InlineData("Coach")]
        [InlineData("Volunteer")]
        public void AdultRegistration_ShouldShowEmergencyContactFields(string registrationType)
        {
            try
            {
                // Arrange
                NavigateToRegistrationPage();

                // Act
                _registrationPage.SelectRegistrationType(registrationType);
                Thread.Sleep(2000); // Wait for dynamic fields

                // Assert
                _registrationPage.IsEmergencyContactSectionVisible().Should().BeTrue($"Emergency contact section should be visible for {registrationType} registration");
                _registrationPage.IsParentSectionVisible().Should().BeFalse($"Parent section should be hidden for {registrationType} registration");
            }
            catch (Exception ex)
            {
                TakeScreenshot($"AdultRegistration_{registrationType}_ShouldShowEmergencyContactFields");
                throw;
            }
        }

        [Fact]
        public void CompleteJuniorRegistration_ShouldSucceed()
        {
            try
            {
                // Arrange
                NavigateToRegistrationPage();

                // Act
                // Fill basic information
                _registrationPage.FillBasicInformation("Emma", "Johnson", TestData.JuniorBirthDate, "female");
                
                // Fill address
                _registrationPage.FillAddress("456 Youth St", "Quebec City", "QC", TestData.ValidPostalCode);
                
                // Fill contact information
                _registrationPage.FillContactInformation(TestData.ValidPhone, "mobile", TestData.ValidEmail);
                
                // Select Junior registration type
                _registrationPage.SelectRegistrationType("Junior");
                Thread.Sleep(2000);
                
                // Fill parent information
                _registrationPage.FillParentInformation("Sarah", "Johnson", "<EMAIL>", "(*************");
                
                // Submit form
                _registrationPage.SubmitForm();
                Thread.Sleep(3000);

                // Assert
                // Check if we're redirected or if success message appears
                var currentUrl = Driver.Url;
                currentUrl.Should().NotContain("/Register", "Should be redirected away from registration page after successful submission");
            }
            catch (Exception ex)
            {
                TakeScreenshot("CompleteJuniorRegistration_ShouldSucceed");
                throw;
            }
        }

        [Fact]
        public void CompleteAdultRegistration_ShouldSucceed()
        {
            try
            {
                // Arrange
                NavigateToRegistrationPage();

                // Act
                // Fill basic information
                _registrationPage.FillBasicInformation("Michael", "Thompson", TestData.AdultBirthDate, "male");
                
                // Fill address
                _registrationPage.FillAddress("789 Adult Ave", "Montreal", "QC", TestData.ValidPostalCode);
                
                // Fill contact information
                _registrationPage.FillContactInformation(TestData.ValidPhone, "mobile", TestData.ValidEmail);
                
                // Select Development registration type
                _registrationPage.SelectRegistrationType("Development");
                Thread.Sleep(2000);
                
                // Fill emergency contact information
                _registrationPage.FillEmergencyContactInformation("Lisa", "Thompson", "<EMAIL>", "(*************");
                
                // Submit form
                _registrationPage.SubmitForm();
                Thread.Sleep(3000);

                // Assert
                var currentUrl = Driver.Url;
                currentUrl.Should().NotContain("/Register", "Should be redirected away from registration page after successful submission");
            }
            catch (Exception ex)
            {
                TakeScreenshot("CompleteAdultRegistration_ShouldSucceed");
                throw;
            }
        }

        [Fact]
        public void RegistrationTypeChange_ShouldUpdateDynamicFields()
        {
            try
            {
                // Arrange
                NavigateToRegistrationPage();

                // Act & Assert - Switch from Junior to Adult
                _registrationPage.SelectRegistrationType("Junior");
                Thread.Sleep(2000);
                _registrationPage.IsParentSectionVisible().Should().BeTrue("Parent section should be visible for Junior");

                _registrationPage.SelectRegistrationType("Development");
                Thread.Sleep(2000);
                _registrationPage.IsEmergencyContactSectionVisible().Should().BeTrue("Emergency contact section should be visible for Development");
                _registrationPage.IsParentSectionVisible().Should().BeFalse("Parent section should be hidden when switching from Junior to Development");

                // Switch back to Junior
                _registrationPage.SelectRegistrationType("Junior");
                Thread.Sleep(2000);
                _registrationPage.IsParentSectionVisible().Should().BeTrue("Parent section should be visible again for Junior");
                _registrationPage.IsEmergencyContactSectionVisible().Should().BeFalse("Emergency contact section should be hidden when switching back to Junior");
            }
            catch (Exception ex)
            {
                TakeScreenshot("RegistrationTypeChange_ShouldUpdateDynamicFields");
                throw;
            }
        }

        [Fact]
        public void IncompleteJuniorRegistration_ShouldShowValidationErrors()
        {
            try
            {
                // Arrange
                NavigateToRegistrationPage();

                // Act - Fill only partial information
                _registrationPage.FillBasicInformation("Emma", "Johnson", TestData.JuniorBirthDate, "female");
                _registrationPage.SelectRegistrationType("Junior");
                Thread.Sleep(2000);
                
                // Submit without filling required fields
                _registrationPage.SubmitForm();
                Thread.Sleep(2000);

                // Assert
                _registrationPage.HasValidationErrors().Should().BeTrue("Incomplete form should show validation errors");
                
                // Should still be on registration page
                var currentUrl = Driver.Url;
                currentUrl.Should().Contain("/Register", "Should remain on registration page when validation fails");
            }
            catch (Exception ex)
            {
                TakeScreenshot("IncompleteJuniorRegistration_ShouldShowValidationErrors");
                throw;
            }
        }

        [Fact]
        public void RegistrationForm_ShouldMaintainStateOnValidationError()
        {
            try
            {
                // Arrange
                NavigateToRegistrationPage();

                // Act
                var testFirstName = "TestUser";
                var testLastName = "ValidationTest";
                var testEmail = "<EMAIL>";

                _registrationPage.FillBasicInformation(testFirstName, testLastName, TestData.AdultBirthDate, "male");
                _registrationPage.FillContactInformation(TestData.ValidPhone, "mobile", testEmail);
                
                // Submit incomplete form (missing address)
                _registrationPage.SubmitForm();
                Thread.Sleep(2000);

                // Assert - Form should maintain entered values
                _registrationPage.GetFirstNameValue().Should().Be(testFirstName, "First name should be preserved after validation error");
                _registrationPage.GetLastNameValue().Should().Be(testLastName, "Last name should be preserved after validation error");
                _registrationPage.GetEmailValue().Should().Be(testEmail, "Email should be preserved after validation error");
            }
            catch (Exception ex)
            {
                TakeScreenshot("RegistrationForm_ShouldMaintainStateOnValidationError");
                throw;
            }
        }

        [Fact]
        public void MultipleRegistrationTypes_ShouldAllowSwitching()
        {
            try
            {
                // Arrange
                NavigateToRegistrationPage();

                // Test all registration types
                var registrationTypes = new[] { "Junior", "Development", "Elite", "Coach", "Volunteer" };

                foreach (var type in registrationTypes)
                {
                    // Act
                    _registrationPage.SelectRegistrationType(type);
                    Thread.Sleep(1500);

                    // Assert - Check that the radio button is selected
                    var radioButton = Driver.FindElement(By.Id($"reg-{Array.IndexOf(registrationTypes, type) + 1}"));
                    radioButton.Selected.Should().BeTrue($"{type} registration type should be selectable");
                }
            }
            catch (Exception ex)
            {
                TakeScreenshot("MultipleRegistrationTypes_ShouldAllowSwitching");
                throw;
            }
        }

        [Fact]
        public void FormSubmission_ShouldPreventDoubleSubmission()
        {
            try
            {
                // Arrange
                NavigateToRegistrationPage();

                // Fill complete valid form
                _registrationPage.FillBasicInformation("John", "DoubleSubmit", TestData.AdultBirthDate, "male");
                _registrationPage.FillAddress("123 Test St", "Montreal", "QC", TestData.ValidPostalCode);
                _registrationPage.FillContactInformation(TestData.ValidPhone, "mobile", TestData.ValidEmail);
                _registrationPage.SelectRegistrationType("Development");
                Thread.Sleep(2000);
                _registrationPage.FillEmergencyContactInformation("Jane", "Emergency", "<EMAIL>", "(*************");

                // Act - Try to submit multiple times quickly
                _registrationPage.SubmitForm();
                
                // Try to click submit again immediately (double-click simulation)
                try
                {
                    var submitButton = Driver.FindElement(By.CssSelector("button[type='submit']"));
                    submitButton.Click();
                }
                catch (Exception)
                {
                    // Expected if button is disabled or form is being processed
                }

                Thread.Sleep(3000);

                // Assert - Should have been redirected only once
                var currentUrl = Driver.Url;
                currentUrl.Should().NotContain("/Register", "Should be redirected after successful submission");
            }
            catch (Exception ex)
            {
                TakeScreenshot("FormSubmission_ShouldPreventDoubleSubmission");
                throw;
            }
        }

        public new void Dispose()
        {
            base.Dispose();
        }
    }
}