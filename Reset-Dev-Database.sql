-- Resets the DEVELOPMENT database by dropping and recreating it.
-- Run this on your DEVELOPMENT server.
USE master;
GO

IF DB_ID('ParaHockeyDB_DEV') IS NOT NULL
BEGIN
    PRINT 'Dropping ParaHockeyDB_DEV...';
    ALTER DATABASE ParaHockeyDB_DEV SET SINGLE_USER WITH ROLLBACK IMMEDIATE;
    DROP DATABASE ParaHockeyDB_DEV;
    PRINT 'ParaHockeyDB_DEV dropped.';
END
GO

PRINT 'Creating ParaHockeyDB_DEV...';
CREATE DATABASE ParaHockeyDB_DEV;
PRINT '<PERSON>Hock<PERSON><PERSON><PERSON>_<PERSON>V created.';
GO