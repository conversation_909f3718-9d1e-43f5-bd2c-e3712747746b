using Microsoft.EntityFrameworkCore.Storage;

namespace ParaHockeyApp.Services
{
    /// <summary>
    /// Service for managing database transactions with proper rollback capabilities
    /// </summary>
    public interface ITransactionService
    {
        /// <summary>
        /// Executes an operation within a database transaction
        /// </summary>
        /// <typeparam name="T">Return type of the operation</typeparam>
        /// <param name="operation">The operation to execute</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Result of the operation</returns>
        Task<T> ExecuteInTransactionAsync<T>(Func<IDbContextTransaction, Task<T>> operation, CancellationToken cancellationToken = default);

        /// <summary>
        /// Executes an operation within a database transaction (void return)
        /// </summary>
        /// <param name="operation">The operation to execute</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Task representing the operation</returns>
        Task ExecuteInTransactionAsync(Func<IDbContextTransaction, Task> operation, CancellationToken cancellationToken = default);

        /// <summary>
        /// Executes multiple operations within a single transaction
        /// </summary>
        /// <param name="operations">List of operations to execute</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Results of all operations</returns>
        Task<List<object>> ExecuteBatchInTransactionAsync(List<Func<IDbContextTransaction, Task<object>>> operations, CancellationToken cancellationToken = default);

        /// <summary>
        /// Creates a new transaction scope for manual management
        /// </summary>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Transaction scope that must be disposed</returns>
        Task<ITransactionScope> BeginTransactionScopeAsync(CancellationToken cancellationToken = default);

        /// <summary>
        /// Gets the current transaction if one is active
        /// </summary>
        /// <returns>Current transaction or null if none active</returns>
        IDbContextTransaction? GetCurrentTransaction();
    }

    /// <summary>
    /// Transaction scope for manual transaction management
    /// </summary>
    public interface ITransactionScope : IDisposable
    {
        /// <summary>
        /// The underlying database transaction
        /// </summary>
        IDbContextTransaction Transaction { get; }

        /// <summary>
        /// Commits the transaction
        /// </summary>
        /// <param name="cancellationToken">Cancellation token</param>
        Task CommitAsync(CancellationToken cancellationToken = default);

        /// <summary>
        /// Rolls back the transaction
        /// </summary>
        /// <param name="cancellationToken">Cancellation token</param>
        Task RollbackAsync(CancellationToken cancellationToken = default);

        /// <summary>
        /// Whether the transaction has been committed or rolled back
        /// </summary>
        bool IsCompleted { get; }
    }

    /// <summary>
    /// Transaction execution result
    /// </summary>
    /// <typeparam name="T">Type of the result</typeparam>
    public class TransactionResult<T>
    {
        public bool Success { get; set; }
        public T? Result { get; set; }
        public Exception? Exception { get; set; }
        public string? ErrorMessage { get; set; }
        public TimeSpan ExecutionTime { get; set; }
    }

    /// <summary>
    /// Batch transaction execution result
    /// </summary>
    public class BatchTransactionResult
    {
        public bool AllSuccessful { get; set; }
        public List<object?> Results { get; set; } = new();
        public List<Exception> Exceptions { get; set; } = new();
        public int SuccessfulCount { get; set; }
        public int FailedCount { get; set; }
        public TimeSpan TotalExecutionTime { get; set; }
    }
}