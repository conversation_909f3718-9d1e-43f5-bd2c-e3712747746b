using System.ComponentModel.DataAnnotations;

namespace ParaHockeyApp.DTOs
{
    /// <summary>
    /// Request DTO for member export operations, extending MemberSearchRequest with export-specific properties
    /// Supports all search and filtering capabilities plus export format and field selection options
    /// </summary>
    public class MemberExportRequest : MemberSearchRequest
    {
        /// <summary>
        /// The format for the exported file (CSV or Excel)
        /// </summary>
        public ExportFormat Format { get; set; } = ExportFormat.Excel;

        /// <summary>
        /// Whether to include column headers in the export
        /// </summary>
        public bool IncludeHeaders { get; set; } = true;

        /// <summary>
        /// Optional list of specific fields to include in the export
        /// If null or empty, all available fields will be exported
        /// </summary>
        public List<string>? SelectedFields { get; set; }

        /// <summary>
        /// Gets the file extension based on the selected format
        /// </summary>
        /// <returns>File extension including the dot (e.g., ".xlsx", ".csv")</returns>
        public string GetFileExtension()
        {
            return Format switch
            {
                ExportFormat.Excel => ".xlsx",
                ExportFormat.CSV => ".csv",
                _ => ".csv"
            };
        }

        /// <summary>
        /// Gets the MIME type based on the selected format
        /// </summary>
        /// <returns>MIME type string for the selected format</returns>
        public string GetMimeType()
        {
            return Format switch
            {
                ExportFormat.Excel => "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
                ExportFormat.CSV => "text/csv",
                _ => "text/csv"
            };
        }
    }
}