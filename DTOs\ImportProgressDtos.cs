using ParaHockeyApp.Models.Entities;

namespace ParaHockeyApp.DTOs
{
    /// <summary>
    /// Progress update information for streaming import
    /// </summary>
    public class ImportProgressUpdate
    {
        public int BatchId { get; set; }
        public string Stage { get; set; } = string.Empty;
        public int ProcessedRows { get; set; }
        public int TotalRows { get; set; }
        public double PercentageComplete => TotalRows > 0 ? (ProcessedRows / (double)TotalRows) * 100 : 0;
        public string StatusMessage { get; set; } = string.Empty;
        public DateTime LastUpdated { get; set; } = DateTime.UtcNow;
        public List<string> Warnings { get; set; } = new();
        public List<string> Errors { get; set; } = new();
        public bool IsCompleted { get; set; }
        public bool HasErrors => Errors.Any();
    }

    /// <summary>
    /// Current progress information for an import batch
    /// </summary>
    public class ImportProgressInfo
    {
        public int BatchId { get; set; }
        public string FileName { get; set; } = string.Empty;
        public ImportStatus Status { get; set; }
        public string CurrentStage { get; set; } = string.Empty;
        public int ProcessedRows { get; set; }
        public int TotalRows { get; set; }
        public double PercentageComplete => TotalRows > 0 ? (ProcessedRows / (double)TotalRows) * 100 : 0;
        public DateTime StartedAt { get; set; }
        public DateTime? CompletedAt { get; set; }
        public TimeSpan ElapsedTime => (CompletedAt ?? DateTime.UtcNow) - StartedAt;
        public string? ErrorMessage { get; set; }
        public bool CanBeCancelled => Status == ImportStatus.InProgress;
        public List<string> ProcessingStages { get; set; } = new();
        public int CurrentStageIndex { get; set; }
    }

    /// <summary>
    /// Import status enumeration
    /// </summary>
    public enum ImportStatus
    {
        Pending = 0,
        InProgress = 1,
        Completed = 2,
        Failed = 3,
        Cancelled = 4
    }

    /// <summary>
    /// Validation result for streaming import
    /// </summary>
    public class StreamingValidationResult
    {
        public bool IsValid { get; set; }
        public bool RecommendStreaming { get; set; }
        public long FileSizeBytes { get; set; }
        public int EstimatedRowCount { get; set; }
        public List<string> Warnings { get; set; } = new();
        public List<string> Errors { get; set; } = new();
        public string RecommendedStrategy { get; set; } = string.Empty;
        public int RecommendedBatchSize { get; set; }
    }
}
