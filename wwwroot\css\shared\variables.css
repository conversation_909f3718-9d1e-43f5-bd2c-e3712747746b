/* ParaHockey Design System - CSS Variables */
:root {
    /* Color Palette */
    --ph-primary: #0d6efd;
    --ph-primary-hover: #0b5ed7;
    --ph-primary-active: #0a58ca;
    --ph-primary-light: #e7f3ff;
    --ph-primary-dark: #084298;
    
    --ph-secondary: #6c757d;
    --ph-secondary-hover: #5c636a;
    --ph-secondary-light: #f8f9fa;
    --ph-secondary-dark: #495057;
    
    --ph-success: #198754;
    --ph-success-hover: #157347;
    --ph-success-light: #d1e7dd;
    --ph-success-dark: #0f5132;
    
    --ph-warning: #ffc107;
    --ph-warning-hover: #ffca2c;
    --ph-warning-light: #fff3cd;
    --ph-warning-dark: #856404;
    
    --ph-danger: #dc3545;
    --ph-danger-hover: #c82333;
    --ph-danger-light: #f8d7da;
    --ph-danger-dark: #721c24;
    
    --ph-info: #17a2b8;
    --ph-info-hover: #138496;
    --ph-info-light: #d1ecf1;
    --ph-info-dark: #0c5460;
    
    /* Neutral Colors */
    --ph-white: #ffffff;
    --ph-light: #f8f9fa;
    --ph-lighter: #e9ecef;
    --ph-gray-100: #f8f9fa;
    --ph-gray-200: #e9ecef;
    --ph-gray-300: #dee2e6;
    --ph-gray-400: #ced4da;
    --ph-gray-500: #adb5bd;
    --ph-gray-600: #6c757d;
    --ph-gray-700: #495057;
    --ph-gray-800: #343a40;
    --ph-gray-900: #212529;
    --ph-dark: #212529;
    
    /* Semantic Color Tokens for Theme Support */
    --ph-bg-primary: #ffffff;
    --ph-bg-secondary: #f8f9fa;
    --ph-text-primary: #212529;
    --ph-text-secondary: #6c757d;
    --ph-border: #dee2e6;
    --ph-link: #0d6efd;
    --ph-link-hover: #0b5ed7;
    
    /* Typography */
    --ph-font-family-base: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
    --ph-font-family-heading: "Inter", var(--ph-font-family-base);
    --ph-font-family-mono: "Consolas", "Monaco", "Courier New", monospace;
    
    --ph-font-size-xs: 0.75rem;     /* 12px */
    --ph-font-size-sm: 0.875rem;    /* 14px */
    --ph-font-size-base: 1rem;      /* 16px */
    --ph-font-size-lg: 1.125rem;    /* 18px */
    --ph-font-size-xl: 1.25rem;     /* 20px */
    --ph-font-size-2xl: 1.5rem;     /* 24px */
    --ph-font-size-3xl: 1.875rem;   /* 30px */
    --ph-font-size-4xl: 2.25rem;    /* 36px */
    --ph-font-size-5xl: 3rem;       /* 48px */
    
    --ph-font-weight-light: 300;
    --ph-font-weight-normal: 400;
    --ph-font-weight-medium: 500;
    --ph-font-weight-semibold: 600;
    --ph-font-weight-bold: 700;
    --ph-font-weight-extrabold: 800;
    
    --ph-line-height-tight: 1.25;
    --ph-line-height-normal: 1.5;
    --ph-line-height-relaxed: 1.75;
    
    /* Spacing */
    --ph-spacing-xs: 0.25rem;    /* 4px */
    --ph-spacing-sm: 0.5rem;     /* 8px */
    --ph-spacing-md: 0.75rem;    /* 12px */
    --ph-spacing-base: 1rem;     /* 16px */
    --ph-spacing-lg: 1.5rem;     /* 24px */
    --ph-spacing-xl: 2rem;       /* 32px */
    --ph-spacing-2xl: 2.5rem;    /* 40px */
    --ph-spacing-3xl: 3rem;      /* 48px */
    --ph-spacing-4xl: 4rem;      /* 64px */
    --ph-spacing-5xl: 5rem;      /* 80px */
    
    /* Border Radius */
    --ph-radius-none: 0;
    --ph-radius-sm: 0.125rem;     /* 2px */
    --ph-radius-base: 0.25rem;    /* 4px */
    --ph-radius-md: 0.375rem;     /* 6px */
    --ph-radius-lg: 0.5rem;       /* 8px */
    --ph-radius-xl: 0.75rem;      /* 12px */
    --ph-radius-2xl: 1rem;        /* 16px */
    --ph-radius-3xl: 1.5rem;      /* 24px */
    --ph-radius-full: 9999px;
    
    /* Shadows */
    --ph-shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --ph-shadow-base: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
    --ph-shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --ph-shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --ph-shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    --ph-shadow-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
    --ph-shadow-inner: inset 0 2px 4px 0 rgba(0, 0, 0, 0.06);
    
    /* Focus Ring */
    --ph-focus-ring-color: rgba(13, 110, 253, 0.25);
    --ph-focus-ring-width: 0.25rem;
    --ph-focus-ring-offset: 2px;
    
    /* Transitions */
    --ph-transition-fast: 150ms ease-in-out;
    --ph-transition-base: 250ms ease-in-out;
    --ph-transition-slow: 350ms ease-in-out;
    
    /* Z-Index Scale */
    --ph-z-dropdown: 1000;
    --ph-z-sticky: 1020;
    --ph-z-fixed: 1030;
    --ph-z-modal-backdrop: 1040;
    --ph-z-modal: 1050;
    --ph-z-popover: 1060;
    --ph-z-tooltip: 1070;
    --ph-z-toast: 1080;
    
    /* Breakpoints (for reference in media queries) */
    --ph-breakpoint-xs: 0;
    --ph-breakpoint-sm: 576px;
    --ph-breakpoint-md: 768px;
    --ph-breakpoint-lg: 992px;
    --ph-breakpoint-xl: 1200px;
    --ph-breakpoint-xxl: 1400px;
    
    /* Component Specific Variables */
    --ph-navbar-height: 4rem;
    --ph-sidebar-width: 16rem;
    --ph-sidebar-collapsed-width: 4rem;
    --ph-footer-height: 3.5rem;
    
    /* Form Controls */
    --ph-input-height: 2.5rem;
    --ph-input-padding-x: 0.75rem;
    --ph-input-padding-y: 0.5rem;
    --ph-input-border-width: 1px;
    --ph-input-border-color: var(--ph-gray-300);
    --ph-input-border-radius: var(--ph-radius-md);
    --ph-input-focus-border-color: var(--ph-primary);
    --ph-input-focus-box-shadow: 0 0 0 var(--ph-focus-ring-width) var(--ph-focus-ring-color);
    
    /* Buttons */
    --ph-btn-padding-x: 1rem;
    --ph-btn-padding-y: 0.5rem;
    --ph-btn-font-size: var(--ph-font-size-base);
    --ph-btn-border-radius: var(--ph-radius-md);
    --ph-btn-border-width: 1px;
    --ph-btn-transition: all var(--ph-transition-fast);
    
    /* Cards */
    --ph-card-border-width: 1px;
    --ph-card-border-color: var(--ph-gray-200);
    --ph-card-border-radius: var(--ph-radius-lg);
    --ph-card-padding: var(--ph-spacing-lg);
    --ph-card-shadow: var(--ph-shadow-sm);
    --ph-card-hover-shadow: var(--ph-shadow-md);
    
    /* Tables */
    --ph-table-cell-padding-x: 0.75rem;
    --ph-table-cell-padding-y: 0.5rem;
    --ph-table-border-color: var(--ph-gray-200);
    --ph-table-hover-bg: var(--ph-gray-50);
    --ph-table-striped-bg: rgba(0, 0, 0, 0.025);
}

/* Dark mode variables */
@media (prefers-color-scheme: dark) {
    :root {
        /* Update neutral colors for dark mode */
        --ph-white: #1a1a1a;
        --ph-light: #2d2d2d;
        --ph-lighter: #404040;
        --ph-gray-100: #2d2d2d;
        --ph-gray-200: #404040;
        --ph-gray-300: #525252;
        --ph-gray-400: #737373;
        --ph-gray-500: #a3a3a3;
        --ph-gray-600: #d4d4d4;
        --ph-gray-700: #e5e5e5;
        --ph-gray-800: #f5f5f5;
        --ph-gray-900: #ffffff;
        --ph-dark: #ffffff;
        
        /* Semantic color tokens for dark mode */
        --ph-bg-primary: #1a1a1a;
        --ph-bg-secondary: #2d2d2d;
        --ph-text-primary: #e5e5e5;
        --ph-text-secondary: #a3a3a3;
        --ph-border: #404040;
        --ph-link: #66b3ff;
        --ph-link-hover: #4da6ff;
        
        /* Adjust focus ring for dark mode */
        --ph-focus-ring-color: rgba(102, 179, 255, 0.25);
    }
}

/* Mobile-first responsive font sizes */
@media (max-width: 767.98px) {
    :root {
        --ph-font-size-base: 0.875rem;    /* 14px on mobile */
        --ph-font-size-lg: 1rem;          /* 16px on mobile */
        --ph-font-size-xl: 1.125rem;      /* 18px on mobile */
        --ph-font-size-2xl: 1.25rem;      /* 20px on mobile */
        --ph-font-size-3xl: 1.5rem;       /* 24px on mobile */
        --ph-font-size-4xl: 1.875rem;     /* 30px on mobile */
        --ph-font-size-5xl: 2.25rem;      /* 36px on mobile */
    }
}