
# Task Plan for Production Registration Fix

## Phase 1: Immediate Fix & Investigation

*   [x] **Task 1.1 (REQ-1.1, REQ-2.2):** Fix HTML warnings on the registration page. Edit `Views/Members/Register.cshtml` to add appropriate `autocomplete` attributes to all form inputs and ensure all `<label>` `for` attributes correctly reference field `id`s.
*   [x] **Task 1.2 (REQ-1.1):** Search the entire solution for the string `IsProduction()` to identify any C# code that behaves differently in the Production environment. Pay close attention to `MembersController.cs` and `MemberService.cs`.
*   [x] **Task 1.3 (REQ-1.1):** Search client-side JavaScript files (in `wwwroot/js`) for any logic that checks the window location, hostname, or any injected variables to alter behavior. Look for strings like `window.location.hostname` or environment-specific flags.
*   [x] **Task 1.4 (REQ-1.2, REQ-2.1):** Perform a three-way comparison (diff) of `appsettings.Development.json`, `appsettings.Test.json`, and `appsettings.Production.json`. Document every difference.
*   [x] **Task 1.5 (REQ-1.1):** Based on the findings from tasks 1.2-1.4, formulate a hypothesis for the root cause of the registration failure. This may involve inspecting logs if available.
*   [x] **Task 1.6 (REQ-1.1, AC-4.1):** Implement the code and configuration changes required to fix the production registration issue.
*   [ ] **Task 1.7 (AC-4.1, AC-4.3):** Deploy the fix to a staging or pre-production environment that mirrors Production. Manually test the registration process to confirm the fix. Verify that the browser console warnings are gone. [REQUIRES DEPLOYMENT ACCESS]

## Phase 2: Codebase Standardization & Prevention

*   [x] **Task 2.1 (REQ-1.2):** Refactor any remaining environment-specific logic discovered in Task 1.2 that is not an approved exception. Move configuration values to `appsettings.*.json` files.
*   [x] **Task 2.2 (REQ-2.2):** Expand the HTML audit from Task 1.1 to other key forms in the application (e.g., Login, Admin pages) to fix `autocomplete` and `label` issues proactively.
*   [x] **Task 2.3 (REQ-2.3):** Create a `PULL_REQUEST_TEMPLATE.md` file at the root of the repository. This template will include a checklist requiring developers to confirm that their changes have been tested in a non-production environment and that any new configurations have been added to all relevant `appsettings.*.json` files.
*   [x] **Task 2.4 (REQ-2.3):** Document the approved list of exceptions for environment differences (Test buttons, colors) in a central, visible location, such as a new `ENVIRONMENT_GUIDELINES.md` file.

## Phase 3: Final Verification

*   [x] **Task 3.1 (AC-4.2):** Conduct a final review of the codebase to ensure all planned changes have been implemented correctly and no new discrepancies have been introduced.
*   [ ] **Task 3.2 (USER-3.1):** Communicate the fix and the new preventative measures to the development and administration teams.
