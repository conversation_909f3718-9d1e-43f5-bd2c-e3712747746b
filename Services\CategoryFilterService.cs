using System.Collections.Generic;

namespace ParaHockeyApp.Services
{
    /// <summary>
    /// Implementation of ICategoryFilterService using an in-memory dictionary for composite category filtering.
    /// Maps single category selections to lists of categories to include in filters.
    /// </summary>
    public class CategoryFilterService : ICategoryFilterService
    {
        // Static mapping cached on first access
        private static readonly Dictionary<int, List<int>> _categoryMappings = new Dictionary<int, List<int>>
        {
            // First Shift (ID: 10) -> show First Shift + Pratique First Shift (ID: 13)
            { 10, new List<int> { 10, 13 } },
            
            // Pratique (ID: 1) -> show Pratique + Pratique First Shift (ID: 13)  
            { 1, new List<int> { 1, 13 } }
        };

        /// <summary>
        /// Gets the list of category IDs to include when filtering by the selected category.
        /// </summary>
        /// <param name="selectedCategoryId">The category ID selected by the user</param>
        /// <returns>List of category IDs to include in the filter</returns>
        public List<int> GetFilterCategoryIds(int selectedCategoryId)
        {
            // Return mapped categories if they exist, otherwise return the single selected category
            if (_categoryMappings.TryGetValue(selectedCategoryId, out var mappedCategories))
            {
                return mappedCategories;
            }

            // Default: return just the selected category
            return new List<int> { selectedCategoryId };
        }
    }
}