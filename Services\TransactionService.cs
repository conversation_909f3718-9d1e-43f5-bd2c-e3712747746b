using Microsoft.EntityFrameworkCore.Storage;
using ParaHockeyApp.Models;
using System.Diagnostics;

namespace ParaHockeyApp.Services
{
    /// <summary>
    /// Service for managing database transactions with proper rollback capabilities
    /// </summary>
    public class TransactionService : ITransactionService
    {
        private readonly ApplicationContext _context;
        private readonly ILogger<TransactionService> _logger;

        public TransactionService(
            ApplicationContext context,
            ILogger<TransactionService> logger)
        {
            _context = context;
            _logger = logger;
        }

        /// <summary>
        /// Executes an operation within a database transaction
        /// </summary>
        public async Task<T> ExecuteInTransactionAsync<T>(Func<IDbContextTransaction, Task<T>> operation, CancellationToken cancellationToken = default)
        {
            var stopwatch = Stopwatch.StartNew();
            var transactionId = Guid.NewGuid().ToString("N")[..8];

            _logger.LogDebug("Starting transaction {TransactionId}", transactionId);

            using var transaction = await _context.Database.BeginTransactionAsync(cancellationToken);
            
            try
            {
                var result = await operation(transaction);
                await transaction.CommitAsync(cancellationToken);
                
                stopwatch.Stop();
                _logger.LogDebug("Transaction {TransactionId} committed successfully in {ElapsedMs}ms", 
                    transactionId, stopwatch.ElapsedMilliseconds);
                
                return result;
            }
            catch (Exception ex)
            {
                stopwatch.Stop();
                _logger.LogError(ex, "Transaction {TransactionId} failed after {ElapsedMs}ms, rolling back", 
                    transactionId, stopwatch.ElapsedMilliseconds);
                
                try
                {
                    await transaction.RollbackAsync(cancellationToken);
                    _logger.LogDebug("Transaction {TransactionId} rolled back successfully", transactionId);
                }
                catch (Exception rollbackEx)
                {
                    _logger.LogError(rollbackEx, "Failed to rollback transaction {TransactionId}", transactionId);
                }
                
                throw;
            }
        }

        /// <summary>
        /// Executes an operation within a database transaction (void return)
        /// </summary>
        public async Task ExecuteInTransactionAsync(Func<IDbContextTransaction, Task> operation, CancellationToken cancellationToken = default)
        {
            await ExecuteInTransactionAsync(async (transaction) =>
            {
                await operation(transaction);
                return 0; // Dummy return value
            }, cancellationToken);
        }

        /// <summary>
        /// Executes multiple operations within a single transaction
        /// </summary>
        public async Task<List<object>> ExecuteBatchInTransactionAsync(List<Func<IDbContextTransaction, Task<object>>> operations, CancellationToken cancellationToken = default)
        {
            if (!operations.Any())
                return new List<object>();

            var stopwatch = Stopwatch.StartNew();
            var transactionId = Guid.NewGuid().ToString("N")[..8];
            var results = new List<object>();

            _logger.LogDebug("Starting batch transaction {TransactionId} with {OperationCount} operations", 
                transactionId, operations.Count);

            using var transaction = await _context.Database.BeginTransactionAsync(cancellationToken);
            
            try
            {
                for (int i = 0; i < operations.Count; i++)
                {
                    cancellationToken.ThrowIfCancellationRequested();
                    
                    _logger.LogDebug("Executing operation {OperationIndex} of {TotalOperations} in transaction {TransactionId}", 
                        i + 1, operations.Count, transactionId);
                    
                    var result = await operations[i](transaction);
                    results.Add(result);
                }

                await transaction.CommitAsync(cancellationToken);
                
                stopwatch.Stop();
                _logger.LogDebug("Batch transaction {TransactionId} committed successfully with {ResultCount} results in {ElapsedMs}ms", 
                    transactionId, results.Count, stopwatch.ElapsedMilliseconds);
                
                return results;
            }
            catch (Exception ex)
            {
                stopwatch.Stop();
                _logger.LogError(ex, "Batch transaction {TransactionId} failed after {ElapsedMs}ms, rolling back", 
                    transactionId, stopwatch.ElapsedMilliseconds);
                
                try
                {
                    await transaction.RollbackAsync(cancellationToken);
                    _logger.LogDebug("Batch transaction {TransactionId} rolled back successfully", transactionId);
                }
                catch (Exception rollbackEx)
                {
                    _logger.LogError(rollbackEx, "Failed to rollback batch transaction {TransactionId}", transactionId);
                }
                
                throw;
            }
        }

        /// <summary>
        /// Creates a new transaction scope for manual management
        /// </summary>
        public async Task<ITransactionScope> BeginTransactionScopeAsync(CancellationToken cancellationToken = default)
        {
            var transaction = await _context.Database.BeginTransactionAsync(cancellationToken);
            var transactionId = Guid.NewGuid().ToString("N")[..8];
            
            _logger.LogDebug("Created transaction scope {TransactionId}", transactionId);
            
            return new TransactionScope(transaction, transactionId, _logger);
        }

        /// <summary>
        /// Gets the current transaction if one is active
        /// </summary>
        public IDbContextTransaction? GetCurrentTransaction()
        {
            return _context.Database.CurrentTransaction;
        }

        /// <summary>
        /// Implementation of transaction scope
        /// </summary>
        private class TransactionScope : ITransactionScope
        {
            private readonly ILogger _logger;
            private readonly string _transactionId;
            private bool _disposed;

            public IDbContextTransaction Transaction { get; }
            public bool IsCompleted { get; private set; }

            public TransactionScope(IDbContextTransaction transaction, string transactionId, ILogger logger)
            {
                Transaction = transaction;
                _transactionId = transactionId;
                _logger = logger;
            }

            public async Task CommitAsync(CancellationToken cancellationToken = default)
            {
                if (IsCompleted)
                    throw new InvalidOperationException("Transaction has already been completed");

                try
                {
                    await Transaction.CommitAsync(cancellationToken);
                    IsCompleted = true;
                    _logger.LogDebug("Transaction scope {TransactionId} committed", _transactionId);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Failed to commit transaction scope {TransactionId}", _transactionId);
                    throw;
                }
            }

            public async Task RollbackAsync(CancellationToken cancellationToken = default)
            {
                if (IsCompleted)
                    throw new InvalidOperationException("Transaction has already been completed");

                try
                {
                    await Transaction.RollbackAsync(cancellationToken);
                    IsCompleted = true;
                    _logger.LogDebug("Transaction scope {TransactionId} rolled back", _transactionId);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Failed to rollback transaction scope {TransactionId}", _transactionId);
                    throw;
                }
            }

            public void Dispose()
            {
                if (_disposed)
                    return;

                if (!IsCompleted)
                {
                    try
                    {
                        // Synchronous rollback as fallback
                        Transaction.Rollback();
                        _logger.LogWarning("Transaction scope {TransactionId} was disposed without explicit commit/rollback, rolling back", _transactionId);
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "Failed to rollback transaction scope {TransactionId} during disposal", _transactionId);
                    }
                }

                Transaction.Dispose();
                _disposed = true;
            }
        }
    }
}