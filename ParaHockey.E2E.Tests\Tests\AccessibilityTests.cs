using ParaHockey.E2E.Tests.Infrastructure;
using Xunit;
using FluentAssertions;

namespace ParaHockey.E2E.Tests.Tests
{
    /// <summary>
    /// Color-contrast consistency and accessibility tests
    /// Ensures WCAG AA compliance across light, dark, and forced-colors themes
    /// </summary>
    [Trait("Category", "Accessibility")]
    public class AccessibilityTests : BaseTest
    {
        [Fact]
        public void HomePage_ShouldMeetAccessibilityStandards_InLightMode()
        {
            NavigateToHomePage();
            RunAccessibilityTestWithTheme("HomePage", "light");
        }

        [Fact]
        public void HomePage_ShouldMeetAccessibilityStandards_InDarkMode()
        {
            NavigateToHomePage();
            RunAccessibilityTestWithTheme("HomePage", "dark");
        }

        [Fact]
        public void HomePage_ShouldMeetAccessibilityStandards_InForcedColorsMode()
        {
            NavigateToHomePage();
            RunAccessibilityTestWithTheme("HomePage", "forced-colors");
        }

        [Fact]
        public void RegistrationPage_ShouldMeetAccessibilityStandards_InLightMode()
        {
            NavigateToRegistrationPage();
            RunAccessibilityTestWithTheme("RegistrationPage", "light");
        }

        [Fact]
        public void RegistrationPage_ShouldMeetAccessibilityStandards_InDarkMode()
        {
            NavigateToRegistrationPage();
            RunAccessibilityTestWithTheme("RegistrationPage", "dark");
        }

        [Fact]
        public void RegistrationPage_ShouldMeetAccessibilityStandards_InForcedColorsMode()
        {
            NavigateToRegistrationPage();
            RunAccessibilityTestWithTheme("RegistrationPage", "forced-colors");
        }

        [Fact]
        public void AdminPage_ShouldMeetAccessibilityStandards_InLightMode()
        {
            Driver.Navigate().GoToUrl($"{Config.BaseUrl}/Admin");
            WaitForPageLoad();
            RunAccessibilityTestWithTheme("AdminPage", "light");
        }

        [Fact]
        public void AdminPage_ShouldMeetAccessibilityStandards_InDarkMode()
        {
            Driver.Navigate().GoToUrl($"{Config.BaseUrl}/Admin");
            WaitForPageLoad();
            RunAccessibilityTestWithTheme("AdminPage", "dark");
        }

        [Fact]
        public void AdminPage_ShouldMeetAccessibilityStandards_InForcedColorsMode()
        {
            Driver.Navigate().GoToUrl($"{Config.BaseUrl}/Admin");
            WaitForPageLoad();
            RunAccessibilityTestWithTheme("AdminPage", "forced-colors");
        }

        [Fact]
        public void ColorContrastTokens_ShouldBeConsistent_AcrossThemes()
        {
            NavigateToHomePage();

            // Test that semantic tokens are properly defined in each theme
            var lightModeTokens = GetComputedColorTokens("light");
            var darkModeTokens = GetComputedColorTokens("dark");
            var forcedColorsTokens = GetComputedColorTokens("forced-colors");

            // Verify tokens exist in all themes
            lightModeTokens.Should().ContainKey("--ph-bg-primary");
            lightModeTokens.Should().ContainKey("--ph-text-primary");
            lightModeTokens.Should().ContainKey("--ph-link");
            lightModeTokens.Should().ContainKey("--ph-border");

            darkModeTokens.Should().ContainKey("--ph-bg-primary");
            darkModeTokens.Should().ContainKey("--ph-text-primary");
            darkModeTokens.Should().ContainKey("--ph-link");
            darkModeTokens.Should().ContainKey("--ph-border");

            forcedColorsTokens.Should().ContainKey("--ph-bg-primary");
            forcedColorsTokens.Should().ContainKey("--ph-text-primary");
            forcedColorsTokens.Should().ContainKey("--ph-link");
            forcedColorsTokens.Should().ContainKey("--ph-border");

            // Verify tokens have different values between light and dark modes
            lightModeTokens["--ph-bg-primary"].Should().NotBe(darkModeTokens["--ph-bg-primary"], 
                "Light and dark themes should have different background colors");
            lightModeTokens["--ph-text-primary"].Should().NotBe(darkModeTokens["--ph-text-primary"], 
                "Light and dark themes should have different text colors");
        }

        [Fact]
        public void ThemeListener_ShouldRespondToSystemPreferences()
        {
            NavigateToHomePage();

            // Verify theme listener is loaded
            var themeListenerExists = (bool)((OpenQA.Selenium.IJavaScriptExecutor)Driver)
                .ExecuteScript("return typeof window.ParaHockeyTheme !== 'undefined';");
            
            themeListenerExists.Should().BeTrue("ParaHockeyTheme utilities should be available");

            // Test theme detection utilities
            var isDarkResult = ((OpenQA.Selenium.IJavaScriptExecutor)Driver)
                .ExecuteScript("return window.ParaHockeyTheme.isDark();");
            var isForcedColorsResult = ((OpenQA.Selenium.IJavaScriptExecutor)Driver)
                .ExecuteScript("return window.ParaHockeyTheme.isForcedColors();");

            // Verify the utilities return boolean values
            isDarkResult.Should().NotBeNull("isDark() should return a value");
            isForcedColorsResult.Should().NotBeNull("isForcedColors() should return a value");
        }

        /// <summary>
        /// Gets computed CSS custom property values for the specified theme
        /// </summary>
        private Dictionary<string, string> GetComputedColorTokens(string theme)
        {
            // Apply theme (but don't run accessibility test)
            switch (theme.ToLower())
            {
                case "dark":
                    ((OpenQA.Selenium.IJavaScriptExecutor)Driver).ExecuteScript(@"
                        document.documentElement.classList.add('ph-dark');
                    ");
                    break;
                case "forced-colors":
                    ((OpenQA.Selenium.IJavaScriptExecutor)Driver).ExecuteScript(@"
                        document.documentElement.classList.add('ph-forced-colors');
                    ");
                    break;
                default:
                    ((OpenQA.Selenium.IJavaScriptExecutor)Driver).ExecuteScript(@"
                        document.documentElement.classList.remove('ph-dark', 'ph-forced-colors');
                    ");
                    break;
            }

            Thread.Sleep(100); // Small delay for theme to apply

            // Get computed token values
            var bgPrimary = (string)((OpenQA.Selenium.IJavaScriptExecutor)Driver)
                .ExecuteScript("return getComputedStyle(document.documentElement).getPropertyValue('--ph-bg-primary').trim();");
            var textPrimary = (string)((OpenQA.Selenium.IJavaScriptExecutor)Driver)
                .ExecuteScript("return getComputedStyle(document.documentElement).getPropertyValue('--ph-text-primary').trim();");
            var link = (string)((OpenQA.Selenium.IJavaScriptExecutor)Driver)
                .ExecuteScript("return getComputedStyle(document.documentElement).getPropertyValue('--ph-link').trim();");
            var border = (string)((OpenQA.Selenium.IJavaScriptExecutor)Driver)
                .ExecuteScript("return getComputedStyle(document.documentElement).getPropertyValue('--ph-border').trim();");

            return new Dictionary<string, string>
            {
                { "--ph-bg-primary", bgPrimary ?? "" },
                { "--ph-text-primary", textPrimary ?? "" },
                { "--ph-link", link ?? "" },
                { "--ph-border", border ?? "" }
            };
        }
    }
}