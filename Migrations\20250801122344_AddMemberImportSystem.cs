﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace ParaHockeyApp.Migrations
{
    /// <inheritdoc />
    public partial class AddMemberImportSystem : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "MemberImportBatches",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    ImportBatchId = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    FileName = table.Column<string>(type: "nvarchar(255)", maxLength: 255, nullable: false),
                    UploadedBy = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: false),
                    UploadedAtUtc = table.Column<DateTime>(type: "datetime2", nullable: false),
                    TotalRows = table.Column<int>(type: "int", nullable: false),
                    CreatedCount = table.Column<int>(type: "int", nullable: false),
                    DuplicateCount = table.Column<int>(type: "int", nullable: false),
                    NeedsFixCount = table.Column<int>(type: "int", nullable: false),
                    MergedCount = table.Column<int>(type: "int", nullable: false),
                    RejectedCount = table.Column<int>(type: "int", nullable: false),
                    ConfigurationJson = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    Status = table.Column<string>(type: "nvarchar(20)", maxLength: 20, nullable: false),
                    ErrorMessage = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    DateCreated = table.Column<DateTime>(type: "datetime2", nullable: false),
                    IsActive = table.Column<bool>(type: "bit", nullable: false),
                    DateModified = table.Column<DateTime>(type: "datetime2", nullable: true),
                    CreatedByMemberId = table.Column<int>(type: "int", nullable: true),
                    CreatedByAdminId = table.Column<int>(type: "int", nullable: true),
                    ModifiedByMemberId = table.Column<int>(type: "int", nullable: true),
                    ModifiedByAdminId = table.Column<int>(type: "int", nullable: true),
                    CreatedBySource = table.Column<int>(type: "int", nullable: false),
                    ModifiedBySource = table.Column<int>(type: "int", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_MemberImportBatches", x => x.Id);
                    table.ForeignKey(
                        name: "FK_MemberImportBatches_AdminUsers_CreatedByAdminId",
                        column: x => x.CreatedByAdminId,
                        principalTable: "AdminUsers",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "FK_MemberImportBatches_AdminUsers_ModifiedByAdminId",
                        column: x => x.ModifiedByAdminId,
                        principalTable: "AdminUsers",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "FK_MemberImportBatches_Members_CreatedByMemberId",
                        column: x => x.CreatedByMemberId,
                        principalTable: "Members",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "FK_MemberImportBatches_Members_ModifiedByMemberId",
                        column: x => x.ModifiedByMemberId,
                        principalTable: "Members",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateTable(
                name: "TempMembers",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    TempMemberId = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    ImportBatchId = table.Column<int>(type: "int", nullable: false),
                    HcrNumber = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: true),
                    FirstName = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    LastName = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    DateOfBirth = table.Column<DateTime>(type: "datetime2", nullable: true),
                    Email = table.Column<string>(type: "nvarchar(254)", maxLength: 254, nullable: true),
                    Phone = table.Column<string>(type: "nvarchar(20)", maxLength: 20, nullable: true),
                    Address = table.Column<string>(type: "nvarchar(200)", maxLength: 200, nullable: true),
                    City = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: true),
                    PostalCode = table.Column<string>(type: "nvarchar(10)", maxLength: 10, nullable: true),
                    GenderText = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: true),
                    ProvinceText = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: true),
                    PhoneTypeText = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: true),
                    RegistrationTypeText = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: true),
                    GenderId = table.Column<int>(type: "int", nullable: true),
                    ProvinceId = table.Column<int>(type: "int", nullable: true),
                    PhoneTypeId = table.Column<int>(type: "int", nullable: true),
                    RegistrationTypeId = table.Column<int>(type: "int", nullable: true),
                    ExistingMemberId = table.Column<int>(type: "int", nullable: true),
                    Status = table.Column<int>(type: "int", nullable: false),
                    ValidationErrorsJson = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    RawSourceJson = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    ParentData = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    EmergencyContactData = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    DateCreated = table.Column<DateTime>(type: "datetime2", nullable: false),
                    IsActive = table.Column<bool>(type: "bit", nullable: false),
                    DateModified = table.Column<DateTime>(type: "datetime2", nullable: true),
                    CreatedByMemberId = table.Column<int>(type: "int", nullable: true),
                    CreatedByAdminId = table.Column<int>(type: "int", nullable: true),
                    ModifiedByMemberId = table.Column<int>(type: "int", nullable: true),
                    ModifiedByAdminId = table.Column<int>(type: "int", nullable: true),
                    CreatedBySource = table.Column<int>(type: "int", nullable: false),
                    ModifiedBySource = table.Column<int>(type: "int", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_TempMembers", x => x.Id);
                    table.ForeignKey(
                        name: "FK_TempMembers_AdminUsers_CreatedByAdminId",
                        column: x => x.CreatedByAdminId,
                        principalTable: "AdminUsers",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "FK_TempMembers_AdminUsers_ModifiedByAdminId",
                        column: x => x.ModifiedByAdminId,
                        principalTable: "AdminUsers",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "FK_TempMembers_Genders_GenderId",
                        column: x => x.GenderId,
                        principalTable: "Genders",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "FK_TempMembers_MemberImportBatches_ImportBatchId",
                        column: x => x.ImportBatchId,
                        principalTable: "MemberImportBatches",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_TempMembers_Members_CreatedByMemberId",
                        column: x => x.CreatedByMemberId,
                        principalTable: "Members",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "FK_TempMembers_Members_ExistingMemberId",
                        column: x => x.ExistingMemberId,
                        principalTable: "Members",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "FK_TempMembers_Members_ModifiedByMemberId",
                        column: x => x.ModifiedByMemberId,
                        principalTable: "Members",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "FK_TempMembers_PhoneTypes_PhoneTypeId",
                        column: x => x.PhoneTypeId,
                        principalTable: "PhoneTypes",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "FK_TempMembers_Provinces_ProvinceId",
                        column: x => x.ProvinceId,
                        principalTable: "Provinces",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "FK_TempMembers_RegistrationTypes_RegistrationTypeId",
                        column: x => x.RegistrationTypeId,
                        principalTable: "RegistrationTypes",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateIndex(
                name: "IX_MemberImportBatches_CreatedByAdminId",
                table: "MemberImportBatches",
                column: "CreatedByAdminId");

            migrationBuilder.CreateIndex(
                name: "IX_MemberImportBatches_CreatedByMemberId",
                table: "MemberImportBatches",
                column: "CreatedByMemberId");

            migrationBuilder.CreateIndex(
                name: "IX_MemberImportBatches_ImportBatchId",
                table: "MemberImportBatches",
                column: "ImportBatchId",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_MemberImportBatches_ModifiedByAdminId",
                table: "MemberImportBatches",
                column: "ModifiedByAdminId");

            migrationBuilder.CreateIndex(
                name: "IX_MemberImportBatches_ModifiedByMemberId",
                table: "MemberImportBatches",
                column: "ModifiedByMemberId");

            migrationBuilder.CreateIndex(
                name: "IX_MemberImportBatches_UploadedAtUtc",
                table: "MemberImportBatches",
                column: "UploadedAtUtc");

            migrationBuilder.CreateIndex(
                name: "IX_MemberImportBatches_UploadedBy",
                table: "MemberImportBatches",
                column: "UploadedBy");

            migrationBuilder.CreateIndex(
                name: "IX_TempMembers_CreatedByAdminId",
                table: "TempMembers",
                column: "CreatedByAdminId");

            migrationBuilder.CreateIndex(
                name: "IX_TempMembers_CreatedByMemberId",
                table: "TempMembers",
                column: "CreatedByMemberId");

            migrationBuilder.CreateIndex(
                name: "IX_TempMembers_Email",
                table: "TempMembers",
                column: "Email");

            migrationBuilder.CreateIndex(
                name: "IX_TempMembers_ExistingMemberId",
                table: "TempMembers",
                column: "ExistingMemberId");

            migrationBuilder.CreateIndex(
                name: "IX_TempMembers_GenderId",
                table: "TempMembers",
                column: "GenderId");

            migrationBuilder.CreateIndex(
                name: "IX_TempMembers_HcrNumber",
                table: "TempMembers",
                column: "HcrNumber");

            migrationBuilder.CreateIndex(
                name: "IX_TempMembers_ImportBatchId",
                table: "TempMembers",
                column: "ImportBatchId");

            migrationBuilder.CreateIndex(
                name: "IX_TempMembers_ModifiedByAdminId",
                table: "TempMembers",
                column: "ModifiedByAdminId");

            migrationBuilder.CreateIndex(
                name: "IX_TempMembers_ModifiedByMemberId",
                table: "TempMembers",
                column: "ModifiedByMemberId");

            migrationBuilder.CreateIndex(
                name: "IX_TempMembers_Name_DOB",
                table: "TempMembers",
                columns: new[] { "LastName", "FirstName", "DateOfBirth" });

            migrationBuilder.CreateIndex(
                name: "IX_TempMembers_PhoneTypeId",
                table: "TempMembers",
                column: "PhoneTypeId");

            migrationBuilder.CreateIndex(
                name: "IX_TempMembers_ProvinceId",
                table: "TempMembers",
                column: "ProvinceId");

            migrationBuilder.CreateIndex(
                name: "IX_TempMembers_RegistrationTypeId",
                table: "TempMembers",
                column: "RegistrationTypeId");

            migrationBuilder.CreateIndex(
                name: "IX_TempMembers_Status",
                table: "TempMembers",
                column: "Status");

            migrationBuilder.CreateIndex(
                name: "IX_TempMembers_TempMemberId",
                table: "TempMembers",
                column: "TempMemberId",
                unique: true);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "TempMembers");

            migrationBuilder.DropTable(
                name: "MemberImportBatches");
        }
    }
}
