# Simple Browser Auto-Fill for ParaHockey Registration
# Uses modern WebDriver approach

param(
    [Parameter(Mandatory=$true)]
    [ValidateSet("<PERSON>", "Development", "Elite", "Coach", "Volunteer")]
    [string]$MemberType,
    
    [Parameter(Mandatory=$false)]
    [int]$Count = 1
)

# Counter for incremental names
$counterFile = "registration-counter.txt"
$baseCounter = 1
if (Test-Path $counterFile) {
    $baseCounter = [int](Get-Content $counterFile)
}

# Sample data
$firstNames = @("<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>")
$lastNames = @("<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "G<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Lavoie", "Fortin", "Gagné")
$cities = @("Montréal", "Québec", "Laval", "Gatineau", "<PERSON>ue<PERSON>", "Sherbrooke")
$addresses = @("123 rue Principale", "456 avenue des Érables", "789 boulevard Saint-Laurent", "321 rue de la Paix")

function Get-RandomPostalCode {
    $letters = "ABCDEFGHIJKLMNOPQRSTUVWXYZ"
    $l1 = $letters[(Get-Random -Maximum 26)]
    $n1 = Get-Random -Maximum 10
    $l2 = $letters[(Get-Random -Maximum 26)]
    $n2 = Get-Random -Maximum 10
    $l3 = $letters[(Get-Random -Maximum 26)]
    $n3 = Get-Random -Maximum 10
    return "$l1$n1$l2 $n2$l3$n3"
}

function Get-RandomPhone {
    $area = Get-Random -Minimum 400 -Maximum 999
    $exchange = Get-Random -Minimum 200 -Maximum 999
    $number = Get-Random -Minimum 1000 -Maximum 9999
    return "($area) $exchange-$number"
}

function Get-RandomEmail {
    param($firstName, $lastName, $counter)
    $domains = @("gmail.com", "outlook.com", "yahoo.ca")
    $domain = $domains[(Get-Random -Maximum $domains.Count)]
    return "$firstName.$lastName$counter@$domain".ToLower()
}

function Get-RandomBirthDate {
    param($memberType)
    $today = Get-Date
    switch ($memberType) {
        "Junior" { $yearsAgo = Get-Random -Minimum 8 -Maximum 18 }
        "Development" { $yearsAgo = Get-Random -Minimum 16 -Maximum 26 }
        "Elite" { $yearsAgo = Get-Random -Minimum 18 -Maximum 36 }
        "Coach" { $yearsAgo = Get-Random -Minimum 25 -Maximum 56 }
        "Volunteer" { $yearsAgo = Get-Random -Minimum 18 -Maximum 71 }
    }
    return $today.AddYears(-$yearsAgo).ToString("yyyy-MM-dd")
}

function Get-RegistrationTypeId {
    param($memberType)
    switch ($memberType) {
        "Junior" { return "1" }
        "Development" { return "2" }
        "Elite" { return "3" }
        "Coach" { return "4" }
        "Volunteer" { return "5" }
    }
}

Write-Host "🏒 Simple Browser Auto-Fill for ParaHockey" -ForegroundColor Cyan
Write-Host "===========================================" -ForegroundColor Cyan
Write-Host ""

# Generate and display data for manual entry
for ($i = 0; $i -lt $Count; $i++) {
    $currentCounter = $baseCounter + $i
    
    $firstName = ($firstNames | Get-Random) + $currentCounter
    $lastName = ($lastNames | Get-Random) + $currentCounter
    $email = Get-RandomEmail -firstName $firstName -lastName $lastName -counter $currentCounter
    $phone = Get-RandomPhone
    $birthDate = Get-RandomBirthDate -memberType $MemberType
    $address = $addresses | Get-Random
    $city = $cities | Get-Random
    $postalCode = Get-RandomPostalCode
    $registrationTypeId = Get-RegistrationTypeId -memberType $MemberType
    
    $genderId = Get-Random -Minimum 1 -Maximum 4
    $provinceId = if ((Get-Random -Maximum 10) -lt 7) { 1 } else { Get-Random -Minimum 1 -Maximum 14 }
    $phoneTypeId = if ((Get-Random -Maximum 10) -lt 8) { 1 } else { 2 }
    
    Write-Host "👤 Member #$currentCounter ($MemberType)" -ForegroundColor Green
    Write-Host "   First Name: $firstName" -ForegroundColor White
    Write-Host "   Last Name: $lastName" -ForegroundColor White
    Write-Host "   Email: $email" -ForegroundColor White
    Write-Host "   Phone: $phone" -ForegroundColor White
    Write-Host "   Birth Date: $birthDate" -ForegroundColor White
    Write-Host "   Address: $address" -ForegroundColor White
    Write-Host "   City: $city" -ForegroundColor White
    Write-Host "   Postal Code: $postalCode" -ForegroundColor White
    Write-Host "   Registration Type: $MemberType (Select option $registrationTypeId)" -ForegroundColor White
    Write-Host "   Gender: Select option $genderId" -ForegroundColor White
    Write-Host "   Province: Select option $provinceId (Quebec is usually 1)" -ForegroundColor White
    Write-Host "   Phone Type: Select option $phoneTypeId (Mobile is usually 1)" -ForegroundColor White
    
    if ($MemberType -eq "Junior") {
        $parentFirstName = ($firstNames | Get-Random) + "_Parent$currentCounter"
        $parentLastName = $lastName
        $parentEmail = Get-RandomEmail -firstName $parentFirstName -lastName $parentLastName -counter $currentCounter
        $parentPhone = Get-RandomPhone
        
        Write-Host "   👨‍👩‍👧‍👦 Parent Information:" -ForegroundColor Cyan
        Write-Host "      Parent Type: Parent" -ForegroundColor White
        Write-Host "      Parent First Name: $parentFirstName" -ForegroundColor White
        Write-Host "      Parent Last Name: $parentLastName" -ForegroundColor White
        Write-Host "      Parent Phone: $parentPhone" -ForegroundColor White
        Write-Host "      Parent Email: $parentEmail" -ForegroundColor White
    } else {
        $emergencyFirstName = ($firstNames | Get-Random) + "_Emergency$currentCounter"
        $emergencyLastName = $lastName
        $emergencyEmail = Get-RandomEmail -firstName $emergencyFirstName -lastName $emergencyLastName -counter $currentCounter
        $emergencyPhone = Get-RandomPhone
        
        Write-Host "   🚨 Emergency Contact:" -ForegroundColor Cyan
        Write-Host "      Emergency First Name: $emergencyFirstName" -ForegroundColor White
        Write-Host "      Emergency Last Name: $emergencyLastName" -ForegroundColor White
        Write-Host "      Emergency Phone: $emergencyPhone" -ForegroundColor White
        Write-Host "      Emergency Email: $emergencyEmail" -ForegroundColor White
        Write-Host "      Relation: Friend" -ForegroundColor White
    }
    
    Write-Host ""
    Write-Host "=" * 50 -ForegroundColor Gray
    Write-Host ""
}

# Update counter
$newCounter = $baseCounter + $Count
$newCounter | Out-File -FilePath $counterFile -Encoding UTF8

# Open browser to registration page
Write-Host "🌐 Opening registration page in your default browser..." -ForegroundColor Green
Start-Process "http://localhost:5285/Members/Register"

Write-Host ""
Write-Host "✅ Generated $Count member(s) of type '$MemberType'" -ForegroundColor Green
Write-Host "📊 Next counter will start at: $newCounter" -ForegroundColor Yellow
Write-Host ""
Write-Host "🖱️ Now manually copy the data above into the registration form!" -ForegroundColor Cyan
Write-Host "💡 The registration page should have opened in your browser" -ForegroundColor Yellow