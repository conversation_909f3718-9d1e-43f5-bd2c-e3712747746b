using ParaHockeyApp.Models.Entities;

namespace ParaHockeyApp.ViewModels
{
    /// <summary>
    /// View model for displaying import batch summary information
    /// </summary>
    public class ImportBatchViewModel
    {
        public int ImportBatchId { get; set; }
        public string FileName { get; set; } = string.Empty;
        public DateTime UploadedAtUtc { get; set; }
        public string UploadedBy { get; set; } = string.Empty;
        public int TotalRows { get; set; }
        public string Status { get; set; } = string.Empty;
        public string? ErrorMessage { get; set; }

        // Status counts
        public int CreatedCount { get; set; }
        public int DuplicateCount { get; set; }
        public int NeedsFixCount { get; set; }
        public int MergedCount { get; set; }
        public int RejectedCount { get; set; }
        public int ReadyToCreateCount { get; set; }
        public int ImportedCount { get; set; }

        // Calculated properties
        public int ProcessedCount => CreatedCount + MergedCount + RejectedCount;
        public int PendingCount => DuplicateCount + NeedsFixCount + ReadyToCreateCount + ImportedCount;
        public double ProgressPercentage => TotalRows > 0 ? (ProcessedCount / (double)TotalRows) * 100 : 0;
        public bool IsCompleted => PendingCount == 0 && ProcessedCount > 0;
        public bool HasErrors => !string.IsNullOrEmpty(ErrorMessage) || Status == "Failed";
        public bool CanProcessMore => PendingCount > 0 && !HasErrors;

        // Display helpers
        public string UploadedAtLocal => UploadedAtUtc.ToLocalTime().ToString("yyyy-MM-dd HH:mm:ss");
        public string ProgressText => $"{ProcessedCount} / {TotalRows} processed ({ProgressPercentage:F1}%)";
        public string StatusBadgeClass => Status.ToLower() switch
        {
            "completed" => "badge-success",
            "processing" => "badge-warning",
            "failed" => "badge-danger",
            _ => "badge-secondary"
        };

        // Queue navigation links
        public Dictionary<TempMemberStatus, int> QueueCounts => new()
        {
            { TempMemberStatus.Imported, ImportedCount },
            { TempMemberStatus.ReadyToCreate, ReadyToCreateCount },
            { TempMemberStatus.NeedsFix, NeedsFixCount },
            { TempMemberStatus.Duplicate, DuplicateCount },
            { TempMemberStatus.Created, CreatedCount },
            { TempMemberStatus.Merged, MergedCount },
            { TempMemberStatus.Rejected, RejectedCount }
        };
    }

    /// <summary>
    /// View model for listing import batches
    /// </summary>
    public class ImportBatchListViewModel
    {
        public List<ImportBatchViewModel> Batches { get; set; } = new();
        public int PageNumber { get; set; } = 1;
        public int PageSize { get; set; } = 20;
        public int TotalCount { get; set; }
        public int TotalPages => (int)Math.Ceiling((double)TotalCount / PageSize);
        public bool HasPreviousPage => PageNumber > 1;
        public bool HasNextPage => PageNumber < TotalPages;

        // Filter properties
        public string? StatusFilter { get; set; }
        public string? SearchTerm { get; set; }
        public DateTime? FromDate { get; set; }
        public DateTime? ToDate { get; set; }

        // Summary statistics
        public int TotalBatches => TotalCount;
        public int CompletedBatches => Batches.Count(b => b.IsCompleted);
        public int ProcessingBatches => Batches.Count(b => b.CanProcessMore);
        public int FailedBatches => Batches.Count(b => b.HasErrors);
    }

    /// <summary>
    /// View model for import batch queue status
    /// </summary>
    public class ImportQueueStatusViewModel
    {
        public int ImportBatchId { get; set; }
        public string FileName { get; set; } = string.Empty;
        public TempMemberStatus QueueType { get; set; }
        public int TotalItems { get; set; }
        public int ProcessedItems { get; set; }
        public int RemainingItems => TotalItems - ProcessedItems;
        public double ProcessingPercentage => TotalItems > 0 ? (ProcessedItems / (double)TotalItems) * 100 : 0;

        // Queue-specific properties
        public string QueueDisplayName => QueueType switch
        {
            TempMemberStatus.Imported => "Recently Imported",
            TempMemberStatus.ReadyToCreate => "Ready to Create",
            TempMemberStatus.NeedsFix => "Needs Fixing",
            TempMemberStatus.Duplicate => "Duplicates to Resolve",
            TempMemberStatus.Created => "Successfully Created",
            TempMemberStatus.Merged => "Merged with Existing",
            TempMemberStatus.Rejected => "Rejected",
            _ => QueueType.ToString()
        };

        public string QueueDescription => QueueType switch
        {
            TempMemberStatus.Imported => "Members imported from Excel file, awaiting processing",
            TempMemberStatus.ReadyToCreate => "Validated members ready to be created as new members",
            TempMemberStatus.NeedsFix => "Members with validation errors that need manual correction",
            TempMemberStatus.Duplicate => "Potential duplicate members requiring resolution",
            TempMemberStatus.Created => "Members successfully created from import data",
            TempMemberStatus.Merged => "Members merged with existing member records",
            TempMemberStatus.Rejected => "Members rejected during processing",
            _ => $"Members with status: {QueueType}"
        };

        public string QueueIconClass => QueueType switch
        {
            TempMemberStatus.Imported => "fas fa-upload",
            TempMemberStatus.ReadyToCreate => "fas fa-plus-circle",
            TempMemberStatus.NeedsFix => "fas fa-exclamation-triangle",
            TempMemberStatus.Duplicate => "fas fa-copy",
            TempMemberStatus.Created => "fas fa-check-circle",
            TempMemberStatus.Merged => "fas fa-compress-arrows-alt",
            TempMemberStatus.Rejected => "fas fa-times-circle",
            _ => "fas fa-question-circle"
        };

        public string QueueColorClass => QueueType switch
        {
            TempMemberStatus.Imported => "text-info",
            TempMemberStatus.ReadyToCreate => "text-success",
            TempMemberStatus.NeedsFix => "text-warning",
            TempMemberStatus.Duplicate => "text-primary",
            TempMemberStatus.Created => "text-success",
            TempMemberStatus.Merged => "text-info",
            TempMemberStatus.Rejected => "text-danger",
            _ => "text-secondary"
        };

        public bool RequiresAction => QueueType == TempMemberStatus.ReadyToCreate || 
                                     QueueType == TempMemberStatus.NeedsFix || 
                                     QueueType == TempMemberStatus.Duplicate;
    }

    /// <summary>
    /// View model for import progress tracking
    /// </summary>
    public class ImportProgressViewModel
    {
        public int ImportBatchId { get; set; }
        public string FileName { get; set; } = string.Empty;
        public string CurrentStage { get; set; } = string.Empty;
        public int CurrentStep { get; set; }
        public int TotalSteps { get; set; }
        public double StageProgress => TotalSteps > 0 ? (CurrentStep / (double)TotalSteps) * 100 : 0;
        public string StatusMessage { get; set; } = string.Empty;
        public DateTime LastUpdated { get; set; } = DateTime.UtcNow;
        public bool IsProcessing { get; set; }
        public bool HasError { get; set; }
        public string? ErrorMessage { get; set; }

        // Progress stages
        public List<ImportStage> Stages { get; set; } = new()
        {
            new ImportStage { Name = "Upload", Description = "File upload and validation", IsCompleted = false },
            new ImportStage { Name = "Parse", Description = "Reading Excel data", IsCompleted = false },
            new ImportStage { Name = "Validate", Description = "Data validation", IsCompleted = false },
            new ImportStage { Name = "Duplicate Check", Description = "Checking for duplicates", IsCompleted = false },
            new ImportStage { Name = "Ready", Description = "Ready for processing", IsCompleted = false }
        };
    }

    /// <summary>
    /// Represents a stage in the import process
    /// </summary>
    public class ImportStage
    {
        public string Name { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public bool IsCompleted { get; set; }
        public bool IsActive { get; set; }
        public string? ErrorMessage { get; set; }
    }
}