using System.ComponentModel.DataAnnotations;

namespace ParaHockeyApp.Models.Entities
{
    /// <summary>
    /// Lookup table for Admin Types - explains what each AdminType code means
    /// </summary>
    public class AdminTypeEntity : BaseEntity
    {
        [Required]
        public int TypeCode { get; set; } // 0, 3, 9
        
        [Required]
        [StringLength(50)]
        public string TypeName { get; set; } = string.Empty; // "Disabled", "Normal Admin", "Master Admin"
        
        [StringLength(200)]
        public string? Description { get; set; } // Detailed description of what this admin type can do
        
        [Required]
        public int SortOrder { get; set; } // For display ordering
        
        // Navigation property
        public virtual ICollection<AdminUser> AdminUsers { get; set; } = new List<AdminUser>();
    }
}