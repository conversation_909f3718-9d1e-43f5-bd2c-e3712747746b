using System.ComponentModel.DataAnnotations;
using ParaHockeyApp.Models.Entities;

namespace ParaHockeyApp.DTOs
{
    /// <summary>
    /// DTO for member registration form
    /// Matches the registration form from the sketch
    /// </summary>
    public class MemberRegistrationDto
    {
        // Basic Information
        [Required(ErrorMessageResourceName = "ValidationRequired", ErrorMessageResourceType = typeof(Resources.SharedResource))]
        [StringLength(50, ErrorMessageResourceName = "ValidationStringLength", ErrorMessageResourceType = typeof(Resources.SharedResource))]
        [Display(Name = "First Name")]
        public string FirstName { get; set; } = string.Empty;

        [Required(ErrorMessageResourceName = "ValidationRequired", ErrorMessageResourceType = typeof(Resources.SharedResource))]
        [StringLength(50, ErrorMessageResourceName = "ValidationStringLength", ErrorMessageResourceType = typeof(Resources.SharedResource))]
        [Display(Name = "Last Name")]
        public string LastName { get; set; } = string.Empty;

        [Required(ErrorMessageResourceName = "ValidationRequired", ErrorMessageResourceType = typeof(Resources.SharedResource))]
        [DataType(DataType.Date)]
        [Display(Name = "Date of Birth")]
        public DateTime? DateOfBirth { get; set; }

        [Required(ErrorMessageResourceName = "ValidationRequired", ErrorMessageResourceType = typeof(Resources.SharedResource))]
        [Display(Name = "Gender")]
        public Gender Gender { get; set; } = null!;

        // Address Information
        [Required(ErrorMessageResourceName = "ValidationRequired", ErrorMessageResourceType = typeof(Resources.SharedResource))]
        [StringLength(200, ErrorMessageResourceName = "ValidationStringLength", ErrorMessageResourceType = typeof(Resources.SharedResource))]
        [Display(Name = "Address")]
        public string Address { get; set; } = string.Empty;

        [Required(ErrorMessageResourceName = "ValidationRequired", ErrorMessageResourceType = typeof(Resources.SharedResource))]
        [StringLength(100, ErrorMessageResourceName = "ValidationStringLength", ErrorMessageResourceType = typeof(Resources.SharedResource))]
        [Display(Name = "City")]
        public string City { get; set; } = string.Empty;

        [Required(ErrorMessageResourceName = "ValidationRequired", ErrorMessageResourceType = typeof(Resources.SharedResource))]
        [StringLength(50, ErrorMessageResourceName = "ValidationStringLength", ErrorMessageResourceType = typeof(Resources.SharedResource))]
        [Display(Name = "Province")]
        public string Province { get; set; } = string.Empty;

        [Required(ErrorMessageResourceName = "ValidationRequired", ErrorMessageResourceType = typeof(Resources.SharedResource))]
        [RegularExpression(@"^[A-Za-z]\d[A-Za-z][\s-]?\d[A-Za-z]\d$", ErrorMessageResourceName = "ValidationPostalCode", ErrorMessageResourceType = typeof(Resources.SharedResource))]
        [Display(Name = "Postal Code")]
        public string PostalCode { get; set; } = string.Empty;

        // Contact Information
        [Required(ErrorMessageResourceName = "ValidationRequired", ErrorMessageResourceType = typeof(Resources.SharedResource))]
        [Phone(ErrorMessageResourceName = "ValidationPhone", ErrorMessageResourceType = typeof(Resources.SharedResource))]
        [Display(Name = "Phone")]
        public string Phone { get; set; } = string.Empty;

        [Required(ErrorMessageResourceName = "ValidationRequired", ErrorMessageResourceType = typeof(Resources.SharedResource))]
        [Display(Name = "Phone Type")]
        public PhoneType PhoneType { get; set; } = null!;

        [Required(ErrorMessageResourceName = "ValidationRequired", ErrorMessageResourceType = typeof(Resources.SharedResource))]
        [EmailAddress(ErrorMessageResourceName = "ValidationEmail", ErrorMessageResourceType = typeof(Resources.SharedResource))]
        [RegularExpression(@"^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$",
            ErrorMessageResourceName = "ValidationEmailFormat",
            ErrorMessageResourceType = typeof(Resources.SharedResource))]
        [Display(Name = "Email")]
        public string Email { get; set; } = string.Empty;

        // Registration Information
        [Required(ErrorMessageResourceName = "ValidationRequired", ErrorMessageResourceType = typeof(Resources.SharedResource))]
        [Display(Name = "Registration Type")]
        public RegistrationType RegistrationType { get; set; } = null!;
    }

    /// <summary>
    /// DTO for displaying registered member information
    /// </summary>
    public class MemberResponseDto
    {
        public int Id { get; set; }
        public string NomComplet { get; set; } = string.Empty;
        public string Courriel { get; set; } = string.Empty;
        public int Age { get; set; }
        public string TypeInscription { get; set; } = string.Empty;
        public string Ville { get; set; } = string.Empty;
        public string Province { get; set; } = string.Empty;
        public DateTime DateInscription { get; set; }
        public bool EstActif { get; set; }
    }
}