#!/usr/bin/env pwsh
# Kill ChromeDriver Processes Script

Write-Host "🔍 Checking for ChromeDriver and Chrome processes..." -ForegroundColor Yellow

# Function to safely kill processes
function Kill-ProcessesSafely {
    param($ProcessName, $DisplayName)
    
    $processes = Get-Process -Name $ProcessName -ErrorAction SilentlyContinue
    if ($processes) {
        Write-Host "Found $($processes.Count) $DisplayName process(es)" -ForegroundColor Red
        foreach ($process in $processes) {
            try {
                Write-Host "  Killing $DisplayName (PID: $($process.Id))" -ForegroundColor Yellow
                $process.Kill()
                $process.WaitForExit(5000)  # Wait up to 5 seconds
                Write-Host "  ✅ Successfully killed $DisplayName (PID: $($process.Id))" -ForegroundColor Green
            }
            catch {
                Write-Host "  ❌ Failed to kill $DisplayName (PID: $($process.Id)): $($_.Exception.Message)" -ForegroundColor Red
            }
        }
    }
    else {
        Write-Host "✅ No $DisplayName processes found" -ForegroundColor Green
    }
}

# Kill ChromeDriver processes
Write-Host "`n🎯 Killing ChromeDriver processes..." -ForegroundColor Cyan
Kill-ProcessesSafely "chromedriver" "ChromeDriver"

# Kill Chrome processes (test instances)
Write-Host "`n🎯 Killing Chrome processes..." -ForegroundColor Cyan
Kill-ProcessesSafely "chrome" "Chrome"

# Alternative process names
Write-Host "`n🎯 Checking for alternative process names..." -ForegroundColor Cyan
Kill-ProcessesSafely "GoogleChromeForTesting" "Chrome for Testing"
Kill-ProcessesSafely "msedgedriver" "EdgeDriver"
Kill-ProcessesSafely "geckodriver" "GeckoDriver (Firefox)"

# Wait a moment for processes to fully terminate
Write-Host "`n⏳ Waiting for processes to fully terminate..." -ForegroundColor Yellow
Start-Sleep -Seconds 2

# Final check
Write-Host "`n🔍 Final check for remaining processes..." -ForegroundColor Cyan
$remainingChrome = Get-Process -Name "chrome" -ErrorAction SilentlyContinue
$remainingChromeDriver = Get-Process -Name "chromedriver" -ErrorAction SilentlyContinue

if ($remainingChrome -or $remainingChromeDriver) {
    Write-Host "⚠️  Some processes may still be running. You might need to:" -ForegroundColor Yellow
    Write-Host "   1. Close all Chrome browser windows manually" -ForegroundColor White
    Write-Host "   2. Use Task Manager to force-close stubborn processes" -ForegroundColor White
    Write-Host "   3. Restart your computer if issues persist" -ForegroundColor White
}
else {
    Write-Host "🎉 All ChromeDriver and Chrome processes have been cleaned up!" -ForegroundColor Green
    Write-Host "`n✅ You can now run your tests safely" -ForegroundColor Green
}

Write-Host "`n💡 Pro tip: Run this script before starting tests to avoid conflicts" -ForegroundColor Blue