using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using ParaHockeyApp.Models;
using ParaHockeyApp.Models.Configuration;
using ParaHockeyApp.Models.Entities;
using Microsoft.Extensions.Options;
using Microsoft.AspNetCore.Authentication;
using Microsoft.AspNetCore.Authentication.Cookies;
using System.Security.Claims;
using Microsoft.Identity.Web;
using ParaHockeyApp.Services;
using Microsoft.Extensions.Localization;
using ParaHockeyApp.Resources;
using System.Text;
using System.Globalization;
using ParaHockeyApp.DTOs;

namespace ParaHockeyApp.Controllers
{
    public class AdminController : BaseMvcController
    {
        private readonly ApplicationContext _context;
        private readonly EnvironmentSettings _environmentSettings;
        private readonly IConfiguration _configuration;
        private readonly IAuditLogService _auditLogService;
        private readonly IAuditLogProcessingService _auditLogProcessingService;
        private readonly IEventService _eventService;
        private readonly IMemberSearchService _memberSearchService;
        private readonly IMemberExportService _memberExportService;
        private readonly IEventExportService _eventExportService;
        private readonly IEmptyStateService _emptyStateService;
        private readonly IPageAuditService _pageAuditService;
        private readonly ISecurityAuditService _securityAuditService;
        private readonly IAccessibilityAuditService _accessibilityAuditService;

        public AdminController(
            ApplicationContext context, 
            ILogger<AdminController> logger, 
            IOptions<EnvironmentSettings> environmentSettings,
            IConfiguration configuration,
            IAuditLogService auditLogService,
            IAuditLogProcessingService auditLogProcessingService,
            IEventService eventService,
            IStringLocalizer<SharedResourceMarker> localizer,
            IMemberSearchService memberSearchService,
            IMemberExportService memberExportService,
            IEventExportService eventExportService,
            IEmptyStateService emptyStateService,
            IPageAuditService pageAuditService,
            ISecurityAuditService securityAuditService,
            IAccessibilityAuditService accessibilityAuditService)
            : base(logger, localizer)
        {
            _context = context;
            _environmentSettings = environmentSettings.Value;
            _configuration = configuration;
            _auditLogService = auditLogService;
            _auditLogProcessingService = auditLogProcessingService;
            _eventService = eventService;
            _memberSearchService = memberSearchService;
            _memberExportService = memberExportService;
            _eventExportService = eventExportService;
            _emptyStateService = emptyStateService;
            _pageAuditService = pageAuditService;
            _securityAuditService = securityAuditService;
            _accessibilityAuditService = accessibilityAuditService;
            _pageAuditService = pageAuditService;
        }

        [HttpGet]
        public async Task<IActionResult> Index()
        {
            // FIRST: Check if member is logged in and clear member session for admin login
            var memberSessionData = HttpContext.Session.GetString("MemberSessionData");
            if (!string.IsNullOrEmpty(memberSessionData))
            {
                _logger.LogInformation("Member session detected during admin access - clearing member session for admin login");
                HttpContext.Session.Remove("MemberSessionData");
            }

            // Debug: Log user info
            _logger.LogInformation("=== ADMIN INDEX DEBUG ===");
            _logger.LogInformation("IsAuthenticated: {IsAuth}", User.Identity?.IsAuthenticated);
            _logger.LogInformation("User Name: {Name}", User.Identity?.Name);
            _logger.LogInformation("UseAuthentication: {UseAuth}", _environmentSettings.UseAuthentication);
            
            foreach (var claim in User.Claims)
            {
                _logger.LogInformation("Claim: {Type} = {Value}", claim.Type, claim.Value);
            }

            // Check authentication
            if (!User.Identity?.IsAuthenticated == true)
            {
                _logger.LogWarning("User not authenticated, redirecting to challenge");
                return Challenge(Microsoft.AspNetCore.Authentication.OpenIdConnect.OpenIdConnectDefaults.AuthenticationScheme);
            }

            // If no admins exist, redirect to setup
            if (!await HasAdminsAsync())
            {
                _logger.LogInformation("No admins exist, redirecting to setup");
                return RedirectToAction("SetupMasterAdmin");
            }
            
            // Check if user is admin
            var isAdmin = await IsUserAdminAsync();
            _logger.LogInformation("IsUserAdmin result: {IsAdmin}", isAdmin);
            
            if (!isAdmin)
            {
                _logger.LogWarning("User is not admin, redirecting to AccessDenied");
                return RedirectToAction("AccessDenied");
            }
            
            // NOTE: Member session clearing moved to authentication events, not page access
            
            _logger.LogInformation("Admin dashboard accessed by user: {User}", User.Identity?.Name ?? "Unknown");

            // Get dashboard statistics
            var memberCount = await _context.Members.CountAsync();
            var parentCount = await _context.Parents.CountAsync();
            var emergencyContactCount = await _context.EmergencyContacts.CountAsync();
            var recentMembers = await _context.Members
                .OrderByDescending(m => m.DateCreated)
                .Take(5)
                .Select(m => new
                {
                    m.Id,
                    m.FirstName,
                    m.LastName,
                    m.Email,
                    m.DateCreated
                })
                .ToListAsync();

            // Get recent audit activity and process it
            var recentAuditLogs = await _auditLogService.GetRecentAuditLogsAsync(1, 20);
            var recentAuditLogViewModels = await _auditLogProcessingService.ProcessAuditLogsAsync(recentAuditLogs);
            
            // Take only the first 5 consolidated items for display
            recentAuditLogViewModels = recentAuditLogViewModels.Take(5).ToList();

            ViewBag.MemberCount = memberCount;
            ViewBag.ParentCount = parentCount;
            ViewBag.EmergencyContactCount = emergencyContactCount;
            ViewBag.RecentMembers = recentMembers;
            ViewBag.RecentAuditLogs = recentAuditLogViewModels;
            ViewBag.CurrentUser = User.Identity?.Name ?? "Administrator";
            ViewBag.Environment = _environmentSettings.Name;

            return View();
        }

        [HttpGet]
        public async Task<IActionResult> Members(MemberSearchRequest request, string search = "", int page = 1, int pageSize = 20)
        {
            if (!await IsUserAdminAsync())
            {
                return RedirectToAction("AccessDenied");
            }

            // Handle backward compatibility with old search parameter
            if (!string.IsNullOrEmpty(search) && string.IsNullOrEmpty(request.SearchTerm))
            {
                request.SearchTerm = search;
                _logger.LogInformation("Using backward compatibility: search parameter '{Search}' converted to SearchTerm", search);
            }

            // Set default pagination if not provided
            if (request.Page == 0) request.Page = page;
            if (request.PageSize == 0) request.PageSize = pageSize;

            _logger.LogInformation("Member search requested: SearchTerm='{SearchTerm}', Page={Page}, PageSize={PageSize}", 
                request.SearchTerm ?? "null", request.Page, request.PageSize);

            // Validate the request
            if (!request.IsValid())
            {
                ModelState.AddModelError("", _localizer["InvalidSearchParameters"].Value);
            }

            // Perform the search using the enhanced service
            var searchResult = await _memberSearchService.SearchMembersAsync(request);

            _logger.LogInformation("Search completed: TotalCount={TotalCount}, MembersCount={MembersCount}", 
                searchResult.TotalCount, searchResult.Members.Count);

            // Get registration types for filter dropdown
            ViewBag.RegistrationTypes = await _memberSearchService.GetRegistrationTypesAsync();

            // Pass search results to view
            ViewBag.SearchResult = searchResult;
            ViewBag.SearchRequest = request;

            // Backward compatibility - some views might still expect these
            ViewBag.TotalMembers = searchResult.TotalCount;
            ViewBag.CurrentPage = searchResult.CurrentPage;
            ViewBag.PageSize = searchResult.PageSize;
            ViewBag.TotalPages = searchResult.TotalPages;
            ViewBag.SearchTerm = request.SearchTerm;

            // Return the member list for the view
            var members = searchResult.Members.Select(item => new Member
            {
                Id = item.Id,
                FirstName = item.FirstName,
                LastName = item.LastName,
                Email = item.Email,
                Phone = item.Phone,
                DateOfBirth = item.DateOfBirth,
                Address = item.Address,
                City = item.City,
                PostalCode = item.PostalCode,
                IsActive = item.IsActive,
                DateCreated = item.DateCreated,
                DateModified = item.DateModified,
                RegistrationType = new RegistrationType 
                { 
                    DisplayNameKey = item.RegistrationTypeName 
                }
            }).ToList();

            _logger.LogInformation("Returning {MemberCount} members to view", members.Count);

            // Add empty state support
            if (searchResult.Members.Count == 0)
            {
                if (!string.IsNullOrEmpty(request.SearchTerm))
                {
                    // Search-specific empty state
                    ViewBag.EmptyState = _emptyStateService.GetSearchEmptyState("members", request.SearchTerm);
                }
                else
                {
                    // General empty state for no members
                    ViewBag.EmptyState = _emptyStateService.GetEmptyState("members", 
                        actionUrl: Url.Action("Register", "Members"),
                        actionText: _localizer["RegisterFirstMember"].Value);
                }
            }

            return View(members);
        }

        /// <summary>
        /// Advanced export action that handles both CSV and Excel export requests
        /// Supports all search and filtering capabilities with intelligent file naming
        /// </summary>
        [HttpGet]
        public async Task<IActionResult> ExportMembersAdvanced(MemberExportRequest request)
        {
            if (!await IsUserAdminAsync())
            {
                return RedirectToAction("AccessDenied");
            }

            try
            {
                // Validate the export request
                if (!ModelState.IsValid)
                {
                    _logger.LogWarning("Invalid export request: {Errors}", 
                        string.Join(", ", ModelState.Values.SelectMany(v => v.Errors).Select(e => e.ErrorMessage)));
                    SetErrorMessage("InvalidExportParameters");
                    return RedirectToAction(nameof(Members));
                }

                // Log export activity for audit purposes
                var userName = User.Identity?.Name ?? "Unknown";
                _logger.LogInformation("Member export requested by {User}: Format={Format}, SearchTerm='{SearchTerm}', Filters={Filters}", 
                    userName, request.Format, request.SearchTerm ?? "None", 
                    $"Province={request.Province}, City={request.City}, RegistrationTypes={string.Join(",", request.RegistrationTypeIds ?? new List<int>())}, IsActive={request.IsActive}");

                // Generate export based on format
                byte[] exportData;
                if (request.Format == ExportFormat.Excel)
                {
                    exportData = await _memberExportService.ExportToExcelAsync(request);
                }
                else
                {
                    exportData = await _memberExportService.ExportToCsvAsync(request);
                }

                // Generate intelligent filename
                var fileName = await _memberExportService.GenerateFileNameAsync(request);
                var mimeType = request.GetMimeType();

                // Log successful export with detailed information for audit purposes
                _logger.LogInformation("Export completed successfully: {FileName}, Size={Size} bytes, User={User}, Format={Format}, SearchTerm='{SearchTerm}', Filters={Filters}", 
                    fileName, exportData.Length, userName, request.Format, request.SearchTerm ?? "None",
                    $"Province={request.Province}, City={request.City}, RegistrationTypes={string.Join(",", request.RegistrationTypeIds ?? new List<int>())}, IsActive={request.IsActive}");

                return File(exportData, mimeType, fileName);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error exporting members: Format={Format}, User={User}", 
                    request.Format, User.Identity?.Name);
                SetErrorMessage("ErrorExportingMembers");
                return RedirectToAction(nameof(Members));
            }
        }

        /// <summary>
        /// AJAX endpoint for dynamic filter option loading
        /// Provides filter options based on filter type and optional search term
        /// </summary>
        [HttpGet]
        public async Task<IActionResult> GetFilterOptions(string filterType, string? searchTerm = null)
        {
            if (!await IsUserAdminAsync())
            {
                return HandleAjaxError(new UnauthorizedAccessException("Access denied"), nameof(GetFilterOptions));
            }

            try
            {
                // Validate filter type
                if (string.IsNullOrWhiteSpace(filterType))
                {
                    return Json(new { success = false, error = _localizer["ApiFilterTypeRequired"].Value });
                }

                // Get filter options from the export service
                var filterOptions = await _memberExportService.GetFilterOptionsAsync(filterType, searchTerm);

                if (!filterOptions.Success)
                {
                    _logger.LogWarning("Failed to get filter options: FilterType={FilterType}, Error={Error}", 
                        filterType, filterOptions.ErrorMessage);
                    return Json(new { success = false, error = filterOptions.ErrorMessage });
                }

                _logger.LogDebug("Filter options retrieved: FilterType={FilterType}, Count={Count}, SearchTerm='{SearchTerm}'", 
                    filterType, filterOptions.Options.Count, searchTerm ?? "None");

                return Json(new { 
                    success = true, 
                    filterType = filterOptions.FilterType,
                    options = filterOptions.Options,
                    totalCount = filterOptions.TotalCount
                });
            }
            catch (Exception ex)
            {
                return HandleAjaxError(ex, nameof(GetFilterOptions));
            }
        }

        /// <summary>
        /// Backward compatibility endpoint for existing export functionality
        /// Redirects to the new ExportMembersAdvanced action with CSV format
        /// </summary>
        [HttpGet]
        public async Task<IActionResult> ExportMembers(MemberSearchRequest request)
        {
            // Convert to MemberExportRequest with CSV format for backward compatibility
            var exportRequest = new MemberExportRequest
            {
                SearchTerm = request.SearchTerm,
                RegistrationTypeIds = request.RegistrationTypeIds,
                IsActive = request.IsActive,
                DateOfBirthFrom = request.DateOfBirthFrom,
                DateOfBirthTo = request.DateOfBirthTo,
                AgeFrom = request.AgeFrom,
                AgeTo = request.AgeTo,
                City = request.City,
                Province = request.Province,
                PostalCode = request.PostalCode,
                Page = request.Page,
                PageSize = request.PageSize,
                SortBy = request.SortBy,
                SortDescending = request.SortDescending,
                Format = ExportFormat.CSV, // Default to CSV for backward compatibility
                IncludeHeaders = true
            };

            return await ExportMembersAdvanced(exportRequest);
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> SaveSearch(MemberSearchRequest request, string searchName)
        {
            if (!await IsUserAdminAsync())
            {
                return Json(new { success = false, message = _localizer["ApiAccessDenied"].Value });
            }

            try
            {
                if (string.IsNullOrWhiteSpace(searchName))
                {
                    return Json(new { success = false, message = _localizer["SearchNameRequired"].Value });
                }

                var userId = User.Identity?.Name ?? "";
                var savedSearch = await _memberSearchService.SaveSearchAsync(request, searchName, userId);
                
                return Json(new { success = true, message = _localizer["SearchSavedSuccessfully"].Value, searchId = savedSearch.Id });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error saving search");
                return Json(new { success = false, message = _localizer["ErrorSavingSearch"].Value });
            }
        }

        [HttpGet]
        public async Task<IActionResult> GetSavedSearches()
        {
            if (!await IsUserAdminAsync())
            {
                return Json(new { success = false, message = _localizer["ApiAccessDenied"].Value });
            }

            try
            {
                var userId = User.Identity?.Name ?? "";
                var savedSearches = await _memberSearchService.GetSavedSearchesAsync(userId);
                
                return Json(new { success = true, searches = savedSearches });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving saved searches");
                return Json(new { success = false, message = _localizer["ErrorRetrievingSavedSearches"].Value });
            }
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> DeleteSavedSearch(int searchId)
        {
            if (!await IsUserAdminAsync())
            {
                return Json(new { success = false, message = _localizer["ApiAccessDenied"].Value });
            }

            try
            {
                var userId = User.Identity?.Name ?? "";
                var deleted = await _memberSearchService.DeleteSavedSearchAsync(searchId, userId);
                
                if (deleted)
                {
                    return Json(new { success = true, message = _localizer["SearchDeletedSuccessfully"].Value });
                }
                else
                {
                    return Json(new { success = false, message = _localizer["SearchNotFoundOrUnauthorized"].Value });
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting saved search");
                return Json(new { success = false, message = _localizer["ErrorDeletingSavedSearch"].Value });
            }
        }

        [HttpGet]
        public async Task<IActionResult> LoadSavedSearch(int searchId)
        {
            if (!await IsUserAdminAsync())
            {
                return Json(new { success = false, message = _localizer["ApiAccessDenied"].Value });
            }

            try
            {
                var userId = User.Identity?.Name ?? "";
                var searchRequest = await _memberSearchService.LoadSavedSearchAsync(searchId, userId);
                
                if (searchRequest != null)
                {
                    // Update last used timestamp
                    await _memberSearchService.UpdateSavedSearchLastUsedAsync(searchId, userId);
                    
                    return Json(new { success = true, searchRequest = searchRequest });
                }
                else
                {
                    return Json(new { success = false, message = _localizer["SearchNotFoundOrUnauthorized"].Value });
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error loading saved search");
                return Json(new { success = false, message = _localizer["ErrorLoadingSavedSearch"].Value });
            }
        }

        [HttpGet]
        public async Task<IActionResult> MemberDetails(int id)
        {
            if (!await IsUserAdminAsync())
            {
                return RedirectToAction("AccessDenied");
            }

            var member = await _context.Members
                .Include(m => m.Province)
                .Include(m => m.Gender)
                .Include(m => m.PhoneType)
                .Include(m => m.RegistrationType)
                .FirstOrDefaultAsync(m => m.Id == id);

            if (member == null)
            {
                SetErrorMessage("MemberNotFound");
                return RedirectToAction(nameof(Members));
            }

            var parents = await _context.Parents
                .Where(p => p.MemberId == id)
                .ToListAsync();

            var emergencyContact = await _context.EmergencyContacts
                .FirstOrDefaultAsync(ec => ec.MemberId == id);

            // Get comprehensive audit logs including parent and emergency contact changes
            var auditLogs = await _auditLogService.GetMemberComprehensiveAuditLogsAsync(id);
            var auditSummary = await _auditLogService.GetEntityAuditSummaryAsync("Member", id);

            ViewBag.Parents = parents;
            ViewBag.EmergencyContact = emergencyContact;
            ViewBag.AuditLogs = auditLogs;
            ViewBag.AuditSummary = auditSummary;

            return View(member);
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> DisableMember(int id)
        {
            if (!await IsUserAdminAsync())
            {
                return RedirectToAction("AccessDenied");
            }

            try
            {
                var member = await _context.Members.FindAsync(id);
                if (member == null)
                {
                    SetErrorMessage("MemberNotFound");
                    return RedirectToAction(nameof(Members));
                }

                member.IsActive = false;
                member.DateModified = DateTime.UtcNow;
                member.ModifiedBySource = ActionSource.AdminPanel;
                // Set ModifiedByAdminId if needed for audit trail

                await _context.SaveChangesAsync();

                _logger.LogWarning("Member disabled by admin: {User}, Member: {MemberName} ({MemberId})", 
                    User.Identity?.Name, $"{member.FirstName} {member.LastName}", id);

                SetSuccessMessage("MemberDisabledSuccessfully", member.FirstName, member.LastName);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error disabling member {MemberId}", id);
                SetErrorMessage("ErrorDisablingMember");
            }

            return RedirectToAction(nameof(Members));
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> EnableMember(int id)
        {
            if (!await IsUserAdminAsync())
            {
                return RedirectToAction("AccessDenied");
            }

            try
            {
                var member = await _context.Members.FindAsync(id);
                if (member == null)
                {
                    SetErrorMessage("MemberNotFound");
                    return RedirectToAction(nameof(Members));
                }

                member.IsActive = true;
                member.DateModified = DateTime.UtcNow;
                member.ModifiedBySource = ActionSource.AdminPanel;
                // Set ModifiedByAdminId if needed for audit trail

                await _context.SaveChangesAsync();

                _logger.LogInformation("Member enabled by admin: {User}, Member: {MemberName} ({MemberId})", 
                    User.Identity?.Name, $"{member.FirstName} {member.LastName}", id);

                SetSuccessMessage("MemberEnabledSuccessfully", member.FirstName, member.LastName);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error enabling member {MemberId}", id);
                SetErrorMessage("ErrorEnablingMember");
            }

            return RedirectToAction(nameof(Members));
        }

        [HttpGet]
        public async Task<IActionResult> SystemInfo()
        {
            if (!await IsUserAdminAsync())
            {
                return RedirectToAction("AccessDenied");
            }

            var systemInfo = new
            {
                Environment = _environmentSettings.Name,
                Theme = _environmentSettings.Theme,
                ShowBanner = _environmentSettings.ShowBanner,
                UseAuthentication = _environmentSettings.UseAuthentication,
                DatabaseType = _context.Database.IsSqlServer() ? "SQL Server" : "SQLite",
                TotalMembers = await _context.Members.CountAsync(),
                TotalParents = await _context.Parents.CountAsync(),
                TotalEmergencyContacts = await _context.EmergencyContacts.CountAsync(),
                DatabaseCreated = _context.Database.CanConnect(),
                ServerTime = DateTime.Now,
                UserAuthenticated = User.Identity?.IsAuthenticated ?? false,
                UserName = User.Identity?.Name ?? "Not authenticated",
                UserClaims = User.Claims.Select(c => new { c.Type, c.Value }).ToList()
            };

            return View(systemInfo);
        }

        [HttpGet]
        public async Task<IActionResult> AdminUsers()
        {
            if (!await IsUserAdminAsync())
            {
                return RedirectToAction("AccessDenied");
            }
            
            // Show current admins from database
            var admins = await _context.AdminUsers.ToListAsync();
            ViewBag.Admins = admins;
            ViewBag.CurrentUser = User.Identity?.Name;
            
            // Add empty state support for admin users
            if (admins.Count == 0)
            {
                ViewBag.EmptyState = _emptyStateService.GetEmptyState("admin", 
                    actionText: _localizer["SetupFirstAdmin"].Value,
                    actionUrl: null); // No direct action URL since adding admin is on the same page
            }
            
            return View();
        }
        
        [HttpGet]
        [AllowAnonymous]
        public async Task<IActionResult> SetupMasterAdmin()
        {
            // Only allow if no admins exist
            if (await HasAdminsAsync())
            {
                return RedirectToAction("Index");
            }
            
            if (User.Identity?.IsAuthenticated != true)
            {
                return Challenge(Microsoft.AspNetCore.Authentication.OpenIdConnect.OpenIdConnectDefaults.AuthenticationScheme);
            }
            
            ViewBag.CurrentUser = User.Identity?.Name;
            ViewBag.CurrentEmail = User.Claims.FirstOrDefault(c => c.Type == "preferred_username")?.Value ?? User.Identity?.Name;
            
            return View();
        }
        
        [HttpPost]
        [ValidateAntiForgeryToken]
        [AllowAnonymous]
        public async Task<IActionResult> SetupMasterAdmin(string confirm)
        {
            if (await HasAdminsAsync())
            {
                return RedirectToAction("Index");
            }
            
            if (User.Identity?.IsAuthenticated != true)
            {
                return Challenge(Microsoft.AspNetCore.Authentication.OpenIdConnect.OpenIdConnectDefaults.AuthenticationScheme);
            }
            
            var adminUser = new AdminUser
            {
                Email = User.Claims.FirstOrDefault(c => c.Type == "preferred_username")?.Value ?? User.Identity?.Name ?? string.Empty,
                Name = User.Identity?.Name ?? string.Empty,
                AdminType = AdminType.Master,
                DateCreated = DateTime.UtcNow
            };
            
            _context.AdminUsers.Add(adminUser);
            await _context.SaveChangesAsync();
            
            _logger.LogInformation("Master admin created: {Email}", adminUser.Email);
            
            SetSuccessMessage("MasterAdminCreatedSuccessfully");
            return RedirectToAction("Index");
        }
        
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> AddAdmin(string email, string name, bool isMasterAdmin = false)
        {
            if (!await IsUserAdminAsync())
            {
                return RedirectToAction("AccessDenied");
            }
            
            // Only Master Admins can create other Master Admins
            if (isMasterAdmin && !await IsUserMasterAdminAsync())
            {
                SetErrorMessage("OnlyMasterAdminsCanCreate");
                return RedirectToAction("AdminUsers");
            }
            
            if (string.IsNullOrEmpty(email) || string.IsNullOrEmpty(name))
            {
                SetErrorMessage("EmailAndNameRequired");
                return RedirectToAction("AdminUsers");
            }
            
            var existingAdmin = await _context.AdminUsers.FirstOrDefaultAsync(a => a.Email == email);
            if (existingAdmin != null)
            {
                SetErrorMessage("AdminAlreadyExists");
                return RedirectToAction("AdminUsers");
            }
            
            var adminUser = new AdminUser
            {
                Email = email,
                Name = name,
                AdminType = isMasterAdmin ? AdminType.Master : AdminType.Normal,
                DateCreated = DateTime.UtcNow
            };
            
            _context.AdminUsers.Add(adminUser);
            await _context.SaveChangesAsync();
            
            _logger.LogInformation("Admin added by {CurrentUser}: {Email} (Master: {IsMaster})", 
                User.Identity?.Name, email, isMasterAdmin);
            
            var adminType = isMasterAdmin ? "Master Admin" : "Admin";
            SetSuccessMessage("AdminAddedSuccessfully", adminType, name);
            return RedirectToAction("AdminUsers");
        }
        
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> RemoveAdmin(int id)
        {
            if (!await IsUserAdminAsync())
            {
                return RedirectToAction("AccessDenied");
            }
            
            var admin = await _context.AdminUsers.FindAsync(id);
            if (admin == null)
            {
                SetErrorMessage("AdminNotFound");
                return RedirectToAction("AdminUsers");
            }
            
            if (admin.AdminType == AdminType.Master)
            {
                SetErrorMessage("CannotRemoveMasterAdmin");
                return RedirectToAction("AdminUsers");
            }
            
            _context.AdminUsers.Remove(admin);
            await _context.SaveChangesAsync();
            
            _logger.LogInformation("Admin removed by {CurrentUser}: {Email}", User.Identity?.Name, admin.Email);
            
            SetSuccessMessage("AdminRemovedSuccessfully", admin.Name);
            return RedirectToAction("AdminUsers");
        }
        
        public IActionResult AccessDenied()
        {
            return View();
        }
        
        private async Task<bool> IsUserAdminAsync()
        {
            if (User.Identity?.IsAuthenticated != true)
            {
                return false;
            }
            
            var userEmail = User.Claims.FirstOrDefault(c => c.Type == "preferred_username")?.Value ?? User.Identity?.Name;
            
            // Allow login for any admin with AdminType > 0 (3=Normal, 9=Master)
            // Block login for AdminType = 0 (Disabled) and non-admins
            var adminUser = await _context.AdminUsers.FirstOrDefaultAsync(a => a.Email == userEmail);
            return adminUser?.CanLogin == true;
        }
        
        private async Task<bool> HasAdminsAsync()
        {
            return await _context.AdminUsers.AnyAsync(a => (int)a.AdminType > 0);
        }
        
        private async Task<bool> IsUserMasterAdminAsync()
        {
            if (User.Identity?.IsAuthenticated != true)
            {
                return false;
            }
            
            var userEmail = User.Claims.FirstOrDefault(c => c.Type == "preferred_username")?.Value ?? User.Identity?.Name;
            return await _context.AdminUsers.AnyAsync(a => a.Email == userEmail && a.AdminType == AdminType.Master);
        }

        // Calendar functionality placeholder actions
        [HttpGet]
        public async Task<IActionResult> Calendar()
        {
            if (!await IsUserAdminAsync())
            {
                return RedirectToAction("AccessDenied");
            }

            // Get event categories for the filter dropdown
            var eventCategories = await _eventService.GetAllEventCategoriesSortedAsync();
            
            // Get upcoming events for the sidebar
            var upcomingEvents = await _eventService.GetUpcomingEventsAsync(5);
            
            // Get recent events for the sidebar
            var recentEvents = await _eventService.GetRecentEventsAsync(5);
            
            // Get statistics
            var totalEvents = await _eventService.GetEventCountAsync();
            var publishedEvents = await _eventService.GetEventCountAsync(true);
            
            ViewBag.EventCategories = eventCategories;
            ViewBag.UpcomingEvents = upcomingEvents;
            ViewBag.RecentEvents = recentEvents;
            ViewBag.TotalEvents = totalEvents;
            ViewBag.PublishedEvents = publishedEvents;
            
            // Add empty state support for admin calendar events
            ViewBag.UpcomingEventsEmptyState = _emptyStateService.GetEmptyState("events", "upcoming");
            ViewBag.RecentEventsEmptyState = _emptyStateService.GetEmptyState("events", "recent");
            
            return View();
        }

        [HttpGet]
        public async Task<IActionResult> GetCalendarEvents(DateTime? start = null, DateTime? end = null, int? categoryId = null)
        {
            if (!await IsUserAdminAsync())
            {
                return Forbid();
            }

            var startDate = start ?? DateTime.Now.AddMonths(-1);
            var endDate = end ?? DateTime.Now.AddMonths(2);

            var events = await _eventService.GetCalendarEventsJsonAsync(startDate, endDate, false, categoryId);
            return Json(events);
        }

        [HttpPost]
        public async Task<IActionResult> CreateEvent([FromBody] Event eventData)
        {
            if (!await IsUserAdminAsync())
            {
                return Forbid();
            }

            // Log the received data for debugging
            _logger.LogInformation("CreateEvent called with data: Title={Title}, StartDate={StartDate}, EndDate={EndDate}, CategoryId={CategoryId}", 
                eventData?.Title ?? "NULL", eventData?.StartDate, eventData?.EndDate, eventData?.EventCategoryId);

            if (!ModelState.IsValid)
            {
                var errors = ModelState
                    .Where(x => x.Value.Errors.Count > 0)
                    .Select(x => new { Field = x.Key, Errors = x.Value.Errors.Select(e => e.ErrorMessage) })
                    .ToList();
                
                _logger.LogWarning("Model validation failed: {Errors}", string.Join(", ", errors.Select(e => $"{e.Field}: {string.Join(", ", e.Errors)}")));
                return Json(new { success = false, error = _localizer["ApiValidationFailed"].Value, details = errors });
            }

            if (eventData == null)
            {
                return Json(new { success = false, error = _localizer["ApiEventDataRequired"].Value });
            }

            if (string.IsNullOrWhiteSpace(eventData.Title))
            {
                return Json(new { success = false, error = _localizer["ApiEventTitleRequired"].Value });
            }

            if (eventData.EventCategoryId <= 0)
            {
                return Json(new { success = false, error = _localizer["ApiEventCategoryRequired"].Value });
            }

            try
            {
                var createdEvent = await _eventService.CreateEventAsync(eventData);
                return Json(new { success = true, eventId = createdEvent.Id });
            }
            catch (Exception ex)
            {
                return HandleAjaxError(ex, nameof(CreateEvent));
            }
        }

        [HttpPost]
        public async Task<IActionResult> UpdateEvent(int id, [FromBody] Event eventData)
        {
            if (!await IsUserAdminAsync())
            {
                return Forbid();
            }

            try
            {
                eventData.Id = id;
                var updatedEvent = await _eventService.UpdateEventAsync(eventData);
                return Json(new { success = true });
            }
            catch (Exception ex)
            {
                return HandleAjaxError(ex, nameof(UpdateEvent));
            }
        }

        [HttpPost]
        public async Task<IActionResult> DeleteEvent(int id)
        {
            if (!await IsUserAdminAsync())
            {
                return Forbid();
            }

            try
            {
                var result = await _eventService.DeleteEventAsync(id);
                return Json(new { success = result });
            }
            catch (Exception ex)
            {
                return HandleAjaxError(ex, nameof(DeleteEvent));
            }
        }

        [HttpPost]
        public async Task<IActionResult> PublishEvent(int id)
        {
            if (!await IsUserAdminAsync())
            {
                return Forbid();
            }

            try
            {
                var result = await _eventService.PublishEventAsync(id);
                return Json(new { success = result });
            }
            catch (Exception ex)
            {
                return HandleAjaxError(ex, nameof(PublishEvent));
            }
        }

        [HttpPost]
        public async Task<IActionResult> UnpublishEvent(int id)
        {
            if (!await IsUserAdminAsync())
            {
                return Forbid();
            }

            try
            {
                var result = await _eventService.UnpublishEventAsync(id);
                return Json(new { success = result });
            }
            catch (Exception ex)
            {
                return HandleAjaxError(ex, nameof(UnpublishEvent));
            }
        }

        [HttpGet]
        public async Task<IActionResult> GetEventDetails(int id)
        {
            if (!await IsUserAdminAsync())
            {
                return Forbid();
            }

            var eventEntity = await _eventService.GetEventByIdAsync(id);
            if (eventEntity == null)
            {
                return NotFound();
            }

            return Json(new
            {
                id = eventEntity.Id,
                title = eventEntity.Title,
                description = eventEntity.Description,
                startDate = eventEntity.StartDate.ToString("yyyy-MM-ddTHH:mm"),
                endDate = eventEntity.EndDate.ToString("yyyy-MM-ddTHH:mm"),
                location = eventEntity.Location,
                eventCategoryId = eventEntity.EventCategoryId,
                requiresRegistration = eventEntity.RequiresRegistration,
                maxParticipants = eventEntity.MaxParticipants,
                isPublished = eventEntity.IsPublished,
                currentRegistrations = eventEntity.CurrentRegistrations,
                availableSpots = eventEntity.AvailableSpots
            });
        }

        [HttpGet]
        public async Task<IActionResult> GetEventRegistrations(int id)
        {
            if (!await IsUserAdminAsync())
            {
                return Forbid();
            }

            var registrations = await _eventService.GetEventRegistrationsAsync(id);
            var eventDetails = await _eventService.GetEventByIdAsync(id);
            
            return Json(new {
                registrations = registrations.Select(r => new
                {
                    id = r.Id,
                    memberName = $"{r.Member.FirstName} {r.Member.LastName}",
                    memberEmail = r.Member.Email,
                    memberId = r.MemberId,
                    status = r.Status.ToString(),
                    statusColor = GetStatusColor(r.Status),
                    registrationDate = r.RegistrationDate.ToString("yyyy-MM-dd HH:mm"),
                    confirmationDate = r.ConfirmationDate?.ToString("yyyy-MM-dd HH:mm"),
                    memberNotes = r.MemberNotes,
                    adminNotes = r.AdminNotes,
                    guestCount = r.GuestCount,
                    totalParticipants = r.TotalParticipants,
                    attended = r.Attended,
                    canModify = r.CanBeCancelled,
                    isPastEvent = eventDetails?.StartDate < DateTime.Now
                }),
                summary = new {
                    totalRegistrations = registrations.Count,
                    confirmedCount = registrations.Count(r => r.Status == RegistrationStatus.Confirmed),
                    pendingCount = registrations.Count(r => r.Status == RegistrationStatus.Pending),
                    waitlistedCount = registrations.Count(r => r.Status == RegistrationStatus.Waitlisted),
                    cancelledCount = registrations.Count(r => r.Status == RegistrationStatus.Cancelled),
                    totalParticipants = registrations.Where(r => r.Status == RegistrationStatus.Confirmed)
                                                   .Sum(r => r.TotalParticipants),
                    eventCapacity = eventDetails?.MaxParticipants ?? -1,
                    eventTitle = eventDetails?.Title ?? ""
                }
            });
        }
        
        private string GetStatusColor(RegistrationStatus status) => status switch
        {
            RegistrationStatus.Confirmed => "success",
            RegistrationStatus.Pending => "warning",
            RegistrationStatus.Waitlisted => "info",
            RegistrationStatus.Rejected => "danger",
            RegistrationStatus.Cancelled => "secondary",
            _ => "secondary"
        };

        [HttpPost]
        public async Task<IActionResult> UpdateRegistrationStatus([FromBody] UpdateRegistrationStatusRequest request)
        {
            if (!await IsUserAdminAsync())
            {
                return Forbid();
            }

            // Input validation
            if (request == null)
            {
                return Json(new { success = false, error = "Invalid request data" });
            }

            if (request.RegistrationId <= 0)
            {
                return Json(new { success = false, error = "Invalid registration ID" });
            }

            if (!Enum.IsDefined(typeof(RegistrationStatus), request.Status))
            {
                return Json(new { success = false, error = "Invalid registration status" });
            }

            // Admin note validation
            if (!string.IsNullOrEmpty(request.AdminNotes) && request.AdminNotes.Length > 1000)
            {
                return Json(new { success = false, error = "Admin notes cannot exceed 1000 characters" });
            }

            try
            {
                // Get registration and event details for business rule validation
                var registration = await _eventService.GetEventRegistrationByIdAsync(request.RegistrationId);
                if (registration == null)
                {
                    return Json(new { success = false, error = "Registration not found" });
                }

                var eventDetails = await _eventService.GetEventByIdAsync(registration.EventId);
                if (eventDetails == null)
                {
                    return Json(new { success = false, error = "Event not found" });
                }

                // Business rule validation: Cannot confirm if event is full
                if (request.Status == RegistrationStatus.Confirmed && eventDetails.IsFull && registration.Status != RegistrationStatus.Confirmed)
                {
                    return Json(new { success = false, error = "Cannot confirm registration - event is at capacity" });
                }

                // Business rule validation: Cannot modify registrations for past events (except attendance)
                if (eventDetails.StartDate < DateTime.Now && registration.Status != request.Status)
                {
                    return Json(new { success = false, error = "Cannot modify registration status for past events" });
                }

                var success = await _eventService.UpdateRegistrationStatusAsync(
                    request.RegistrationId,
                    request.Status,
                    request.AdminNotes,
                    GetCurrentAdminContext());

                if (success)
                {
                    return Json(new { success = true, message = "Registration status updated successfully" });
                }

                return Json(new { success = false, error = "Failed to update registration status" });
            }
            catch (ArgumentException argEx)
            {
                _logger.LogWarning(argEx, "Invalid argument for registration status update: {RegistrationId}", request.RegistrationId);
                return Json(new { success = false, error = argEx.Message });
            }
            catch (InvalidOperationException opEx)
            {
                _logger.LogWarning(opEx, "Invalid operation for registration status update: {RegistrationId}", request.RegistrationId);
                return Json(new { success = false, error = opEx.Message });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating registration status for {RegistrationId}", request.RegistrationId);
                return Json(new { success = false, error = "An error occurred while updating the registration" });
            }
        }

        [HttpPost]
        public async Task<IActionResult> MarkAttendance([FromBody] MarkAttendanceRequest request)
        {
            if (!await IsUserAdminAsync())
            {
                return Forbid();
            }

            var success = await _eventService.MarkAttendanceAsync(request.RegistrationId, request.Attended, GetCurrentAdminContext());

            return Json(new {
                success = success,
                message = success ? "Attendance updated" : "Failed to update attendance"
            });
        }

        [HttpPost]
        public async Task<IActionResult> AddAdminNote([FromBody] AddAdminNoteRequest request)
        {
            if (!await IsUserAdminAsync())
            {
                return Forbid();
            }

            var success = await _eventService.AddAdminNoteAsync(request.RegistrationId, request.AdminNote, GetCurrentAdminContext());

            return Json(new {
                success = success,
                message = success ? "Note added successfully" : "Failed to add note"
            });
        }

        [HttpGet]
        public async Task<IActionResult> ExportEventRegistrations(int eventId, string status = "", string format = "CSV")
        {
            if (!await IsUserAdminAsync())
            {
                return Forbid();
            }

            var registrations = await _eventService.GetEventRegistrationsAsync(eventId);

            if (!string.IsNullOrEmpty(status))
            {
                var statusEnum = Enum.Parse<RegistrationStatus>(status);
                registrations = registrations.Where(r => r.Status == statusEnum).ToList();
            }

            var csvContent = GenerateRegistrationsCsv(registrations);
            var eventDetails = await _eventService.GetEventByIdAsync(eventId);
            var fileName = $"{eventDetails?.Title?.Replace(" ", "_") ?? "Event"}_Registrations_{DateTime.Now:yyyyMMdd}.csv";

            // Log the export action for audit purposes
            await _auditLogService.LogActionAsync(
                $"Exported {registrations.Count} registrations for event '{eventDetails?.Title}' (EventId: {eventId}). Filter: {(string.IsNullOrEmpty(status) ? "All" : status)}",
                null, // No specific member
                ActionSource.AdminPanel,
                GetCurrentAdminContext().AdminName,
                GetCurrentAdminContext().IPAddress);

            return File(Encoding.UTF8.GetBytes(csvContent), "text/csv", fileName);
        }

        private string GenerateRegistrationsCsv(List<EventRegistration> registrations)
        {
            var csv = new StringBuilder();
            csv.AppendLine("Member Name,Email,Phone,Status,Registration Date,Confirmation Date,Guest Count,Total Participants,Emergency Contact,Emergency Phone,Member Notes,Admin Notes,Attended");

            foreach (var registration in registrations)
            {
                csv.AppendLine($"\"{registration.Member.FirstName} {registration.Member.LastName}\"," +
                              $"\"{registration.Member.Email}\"," +
                              $"\"{registration.Member.Phone ?? ""}\"," +
                              $"\"{registration.Status}\"," +
                              $"\"{registration.RegistrationDate:yyyy-MM-dd HH:mm}\"," +
                              $"\"{registration.ConfirmationDate?.ToString("yyyy-MM-dd HH:mm") ?? ""}\"," +
                              $"{registration.GuestCount}," +
                              $"{registration.TotalParticipants}," +
                              $"\"{registration.EmergencyContact ?? ""}\"," +
                              $"\"{registration.EmergencyPhone ?? ""}\"," +
                              $"\"{registration.MemberNotes?.Replace("\"", "\"\"") ?? ""}\"," +
                              $"\"{registration.AdminNotes?.Replace("\"", "\"\"") ?? ""}\"," +
                              $"\"{registration.Attended?.ToString() ?? ""}\"");
            }

            return csv.ToString();
        }

        private AdminContext GetCurrentAdminContext()
        {
            return new AdminContext
            {
                AdminName = User.Identity?.Name ?? "Unknown Admin",
                IPAddress = HttpContext.Connection.RemoteIpAddress?.ToString() ?? "Unknown"
            };
        }

        [HttpGet]
        public async Task<IActionResult> Teams()
        {
            if (!await IsUserAdminAsync())
            {
                return RedirectToAction("AccessDenied");
            }

            // Placeholder for Teams functionality
            ViewBag.Message = "Teams Management functionality will be implemented here.";
            ViewBag.Features = new[]
            {
                "Create and manage teams",
                "Assign players and coaches",
                "Team roster management",
                "Season-based team organization"
            };
            return View();
        }

        [HttpGet]
        public async Task<IActionResult> Rankings()
        {
            if (!await IsUserAdminAsync())
            {
                return RedirectToAction("AccessDenied");
            }

            // Placeholder for Rankings functionality
            ViewBag.Message = "Rankings functionality will be implemented here.";
            ViewBag.Features = new[]
            {
                "Team standings and rankings",
                "Win/loss tracking",
                "Points calculation",
                "Season-based rankings"
            };
            return View();
        }

        [HttpGet]
        public async Task<IActionResult> Statistics()
        {
            if (!await IsUserAdminAsync())
            {
                return RedirectToAction("AccessDenied");
            }

            // Placeholder for Statistics functionality
            ViewBag.Message = "Statistics functionality will be implemented here.";
            ViewBag.Features = new[]
            {
                "Player performance statistics",
                "Team performance metrics",
                "Season comparison reports",
                "Manual statistics entry"
            };
            return View();
        }

        [HttpGet]
        public async Task<IActionResult> AllAuditHistory(int page = 1, int pageSize = 25, string entityType = "", string action = "")
        {
            if (!await IsUserAdminAsync())
            {
                return RedirectToAction("AccessDenied");
            }

            // Build query with filters
            var query = _context.AuditLogs.AsQueryable();

            if (!string.IsNullOrEmpty(entityType))
            {
                query = query.Where(al => al.EntityType == entityType);
                ViewBag.EntityType = entityType;
            }

            if (!string.IsNullOrEmpty(action))
            {
                query = query.Where(al => al.Action == action);
                ViewBag.Action = action;
            }

            // Get total count for pagination
            var totalRecords = await query.CountAsync();

            // Get paginated results
            var auditLogs = await query
                .OrderByDescending(al => al.Timestamp)
                .Skip((page - 1) * pageSize)
                .Take(pageSize)
                .ToListAsync();

            // Pagination info
            ViewBag.CurrentPage = page;
            ViewBag.PageSize = pageSize;
            ViewBag.TotalRecords = totalRecords;
            ViewBag.TotalPages = (int)Math.Ceiling((double)totalRecords / pageSize);

            // Filter options for dropdowns
            ViewBag.EntityTypes = await _context.AuditLogs
                .Select(al => al.EntityType)
                .Distinct()
                .OrderBy(et => et)
                .ToListAsync();

            ViewBag.Actions = await _context.AuditLogs
                .Select(al => al.Action)
                .Distinct()
                .OrderBy(a => a)
                .ToListAsync();

            // Add empty state support for audit logs
            if (auditLogs.Count == 0)
            {
                if (!string.IsNullOrEmpty(entityType) || !string.IsNullOrEmpty(action))
                {
                    // Search/filter-specific empty state
                    ViewBag.EmptyState = _emptyStateService.GetEmptyState("audit", "filtered", 
                        actionUrl: Url.Action("AllAuditHistory"),
                        actionText: _localizer["ViewAllRecords"].Value);
                }
                else
                {
                    // General empty state for no audit logs
                    ViewBag.EmptyState = _emptyStateService.GetEmptyState("audit");
                }
            }

            return View(auditLogs);
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> ImportEvents(IFormFile file)
        {
            if (!await IsUserAdminAsync())
            {
                return RedirectToAction("AccessDenied");
            }

            if (file == null || file.Length == 0)
            {
                SetErrorMessage("PleaseSelectFile");
                return RedirectToAction("Calendar");
            }

            _logger.LogInformation("Processing file upload: {FileName}, Size: {FileSize} bytes, ContentType: {ContentType}", 
                file.FileName, file.Length, file.ContentType);

            // Validate file size (max 10MB)
            if (file.Length > 10 * 1024 * 1024)
            {
                SetErrorMessage("FileTooLarge");
                return RedirectToAction("Calendar");
            }

            try
            {
                var importedEvents = new List<Event>();
                var errors = new List<string>();
                var fileExtension = Path.GetExtension(file.FileName).ToLower();

                // Category mapping from French names to EventCategory IDs
                var categoryMappings = await GetCategoryMappingsAsync();

                using (var stream = file.OpenReadStream())
                {
                    if (fileExtension == ".csv")
                    {
                        importedEvents = await _eventExportService.ParseCsvFileAsync(stream, categoryMappings, errors);
                    }
                    else if (fileExtension == ".xlsx")
                    {
                        importedEvents = await _eventExportService.ParseExcelFileAsync(stream, categoryMappings, errors);
                    }
                    else
                    {
                        SetErrorMessage("UnsupportedFileFormat");
                        return RedirectToAction("Calendar");
                    }
                }

                // Save imported events to database
                if (importedEvents.Any())
                {
                    _logger.LogInformation("Importing {Count} events", importedEvents.Count);
                    var duplicatesFound = 0;
                    var eventsCreated = 0;

                    foreach (var eventItem in importedEvents)
                    {
                        // Check for duplicates: same date, time, and location
                        var existingEvent = await _context.Events
                            .FirstOrDefaultAsync(e => 
                                e.StartDate == eventItem.StartDate && 
                                e.EndDate == eventItem.EndDate && 
                                e.Location == eventItem.Location &&
                                e.IsActive);

                        if (existingEvent != null)
                        {
                            _logger.LogInformation("Skipping duplicate event: {Title} on {Date}", eventItem.Title, eventItem.StartDate);
                            duplicatesFound++;
                        }
                        else
                        {
                            _logger.LogInformation("Creating event: {Title} on {Date}", eventItem.Title, eventItem.StartDate);
                            await _eventService.CreateEventAsync(eventItem);
                            eventsCreated++;
                        }
                    }

                    var successMessage = _localizer["EventsImportedSuccessfully", eventsCreated].Value;
                    if (duplicatesFound > 0)
                    {
                        successMessage += $" ({duplicatesFound} duplicates skipped)";
                    }
                    if (errors.Any())
                    {
                        successMessage += " " + _localizer["ImportWarnings", errors.Count].Value;
                        _logger.LogWarning("Import completed with {ErrorCount} errors: {Errors}", errors.Count, string.Join("; ", errors));
                    }
                    TempData["SuccessMessage"] = successMessage;
                }
                else
                {
                    _logger.LogWarning("No events were imported. Errors: {Errors}", string.Join("; ", errors));
                    SetErrorMessage("NoEventsToImport");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error importing events from file: {FileName}", file.FileName);
                SetErrorMessage("ImportError");
                TempData["ErrorDetails"] = ex.Message;
            }

            return RedirectToAction("Calendar");
        }

        [HttpGet]
        public async Task<IActionResult> ExportEvents(string format = "excel", int? year = null, int? month = null)
        {
            if (!await IsUserAdminAsync())
            {
                return RedirectToAction("AccessDenied");
            }

            try
            {
                _logger.LogInformation("ExportEvents called with format: {Format}, year: {Year}, month: {Month}", format, year, month);
                
                // Default to current month if not specified
                var targetDate = new DateTime(year ?? DateTime.Now.Year, month ?? DateTime.Now.Month, 1);
                var startDate = targetDate;
                var endDate = startDate.AddMonths(1).AddDays(-1);

                // Get events for the specified month
                var events = await _eventService.GetEventsForDateRangeAsync(startDate, endDate);
                
                _logger.LogInformation("Found {EventCount} events for export in {Format} format", events.Count, format);

                // Generate export based on format
                byte[] exportData;
                if (format.ToLowerInvariant() == "excel")
                {
                    exportData = await _eventExportService.ExportToExcelAsync(events, targetDate);
                }
                else
                {
                    exportData = await _eventExportService.ExportToCsvAsync(events, targetDate);
                }

                var fileName = _eventExportService.GenerateFileName(targetDate, format);
                var mimeType = _eventExportService.GetMimeType(format);

                _logger.LogInformation("Export completed successfully: {FileName}, Size={Size} bytes, Format={Format}", 
                    fileName, exportData.Length, format);

                return File(exportData, mimeType, fileName);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error exporting events for {Year}-{Month} in {Format} format", year, month, format);
                SetErrorMessage("ExportError");
                TempData["ErrorDetails"] = ex.Message;
                return RedirectToAction("Calendar");
            }
        }

        private async Task<Dictionary<string, int>> GetCategoryMappingsAsync()
        {
            var eventCategories = await _context.EventCategories.ToListAsync();
            
            // Create mapping from French category names to EventCategory IDs
            var mappings = new Dictionary<string, int>(StringComparer.OrdinalIgnoreCase);
            
            // Add specific mappings for common French categories
            var practiceCategory = eventCategories.FirstOrDefault(c => c.DisplayNameKey == "EventCategory_Practice");
            var trainingCategory = eventCategories.FirstOrDefault(c => c.DisplayNameKey == "EventCategory_Training");
            var gameCategory = eventCategories.FirstOrDefault(c => c.DisplayNameKey == "EventCategory_Game");
            var tournamentCategory = eventCategories.FirstOrDefault(c => c.DisplayNameKey == "EventCategory_Tournament");
            var tentativeCategory = eventCategories.FirstOrDefault(c => c.DisplayNameKey == "EventCategory_Tentative");
            var campCategory = eventCategories.FirstOrDefault(c => c.DisplayNameKey == "EventCategory_Camp");
            var serieCategory = eventCategories.FirstOrDefault(c => c.DisplayNameKey == "EventCategory_Serie");
            var meetingCategory = eventCategories.FirstOrDefault(c => c.DisplayNameKey == "EventCategory_Meeting");
            var firstShiftCategory = eventCategories.FirstOrDefault(c => c.DisplayNameKey == "EventCategory_FirstShift");
            var pratiqueFirstShiftCategory = eventCategories.FirstOrDefault(c => c.DisplayNameKey == "EventCategory_PratiqueFirstShift");
            var otherCategory = eventCategories.FirstOrDefault(c => c.DisplayNameKey == "EventCategory_Other");

            if (practiceCategory != null)
            {
                mappings.Add("Pratique", practiceCategory.Id);
                mappings.Add("Pratqiue", practiceCategory.Id); // Handle typo in CSV
            }

            if (tournamentCategory != null)
            {
                mappings.Add("Tournoi", tournamentCategory.Id);
                mappings.Add("Tournoi ", tournamentCategory.Id); // With trailing space
                mappings.Add("NHL Classic", tournamentCategory.Id);
                mappings.Add("Champ Canadien", tournamentCategory.Id);
                mappings.Add("Bell Capital Cup", tournamentCategory.Id);
                mappings.Add("Cruiser's Cup", tournamentCategory.Id);
                mappings.Add("New England Tournament", tournamentCategory.Id);
            }

            if (gameCategory != null)
            {
                mappings.Add("Match", gameCategory.Id);
                mappings.Add("Ligue SLAM", gameCategory.Id);
                mappings.Add("Finale SLAM", gameCategory.Id);
                mappings.Add("Match Parent/Enfant", gameCategory.Id);
                mappings.Add("Match Extérieur", gameCategory.Id);
            }

            if (trainingCategory != null)
            {
                mappings.Add("Formation", trainingCategory.Id);
                mappings.Add("Entraînement", trainingCategory.Id);
            }

            // Camp category mappings
            if (campCategory != null)
            {
                mappings.Add("Camp Provincial", campCategory.Id);
                mappings.Add("Camp", campCategory.Id);
            }

            // First Shift category mappings
            if (firstShiftCategory != null)
            {
                mappings.Add("First Shift", firstShiftCategory.Id);
            }

            // Pratique First Shift category mappings
            if (pratiqueFirstShiftCategory != null)
            {
                mappings.Add("Pratique+First Shift", pratiqueFirstShiftCategory.Id);
            }

            // Serie category mappings
            if (serieCategory != null)
            {
                mappings.Add("Serie M17", serieCategory.Id);
                mappings.Add("Serie Canadienne", serieCategory.Id);
                mappings.Add("Série", serieCategory.Id);
            }

            // Meeting category mappings
            if (meetingCategory != null)
            {
                mappings.Add("Réunion", meetingCategory.Id);
                mappings.Add("Meeting", meetingCategory.Id);
            }

            // Tentative category mappings
            if (tentativeCategory != null)
            {
                mappings.Add("Tentatif", tentativeCategory.Id);
                mappings.Add("Tentatif - Invitation", tentativeCategory.Id);
                mappings.Add("Tentatif - Extérieur", tentativeCategory.Id);
                mappings.Add("À Venir", tentativeCategory.Id);
            }

            // Tournament category mappings
            if (tournamentCategory != null)
            {
                mappings.Add("Défi Sportif", tournamentCategory.Id);
            }

            // Default to "Other" category if available
            if (otherCategory != null)
            {
                mappings.Add("", otherCategory.Id); // Empty category
                mappings.Add("Virtuelle", otherCategory.Id);
                mappings.Add("25", otherCategory.Id);
                
                // Category combinations - map to practice for now
                mappings.Add("Élite/Jr/Dev", practiceCategory?.Id ?? otherCategory.Id);
                mappings.Add("Junior/Dev", practiceCategory?.Id ?? otherCategory.Id);
                mappings.Add("Relève Élite", practiceCategory?.Id ?? otherCategory.Id);
                mappings.Add("Élite", practiceCategory?.Id ?? otherCategory.Id);
                mappings.Add("Junior", practiceCategory?.Id ?? otherCategory.Id);
                mappings.Add("Développement", practiceCategory?.Id ?? otherCategory.Id);
                mappings.Add("Hiboux Jr", practiceCategory?.Id ?? otherCategory.Id);
                mappings.Add("Hiboux Junior", practiceCategory?.Id ?? otherCategory.Id);
                mappings.Add("Élite Qc", practiceCategory?.Id ?? otherCategory.Id);
                mappings.Add("Développement Qc", practiceCategory?.Id ?? otherCategory.Id);
                mappings.Add("Élite / France", practiceCategory?.Id ?? otherCategory.Id);
                mappings.Add("Relève Élite / France", practiceCategory?.Id ?? otherCategory.Id);
                mappings.Add("Développement / France", practiceCategory?.Id ?? otherCategory.Id);
                mappings.Add("Pratique Montréal", practiceCategory?.Id ?? otherCategory.Id);
                mappings.Add("Pratique Ontario", practiceCategory?.Id ?? otherCategory.Id);
                mappings.Add("Québec Dev", practiceCategory?.Id ?? otherCategory.Id);
                mappings.Add("Équipe Qc", practiceCategory?.Id ?? otherCategory.Id);
                mappings.Add("Dev", practiceCategory?.Id ?? otherCategory.Id);
                mappings.Add("Équipe Qc et Dev Qc", practiceCategory?.Id ?? otherCategory.Id);
                mappings.Add("Toutes", practiceCategory?.Id ?? otherCategory.Id);
                mappings.Add("TIER II", practiceCategory?.Id ?? otherCategory.Id);
                mappings.Add("Invitation", practiceCategory?.Id ?? otherCategory.Id);
                mappings.Add("Laval", practiceCategory?.Id ?? otherCategory.Id);
                mappings.Add("Quebec", practiceCategory?.Id ?? otherCategory.Id);
                mappings.Add("3e-4e", practiceCategory?.Id ?? otherCategory.Id);
                mappings.Add("1er-2e", practiceCategory?.Id ?? otherCategory.Id);
                mappings.Add("SLAM Développement", practiceCategory?.Id ?? otherCategory.Id);
                mappings.Add("USA tier 2 et JR", practiceCategory?.Id ?? otherCategory.Id);
                mappings.Add("Remise équipement", practiceCategory?.Id ?? otherCategory.Id);
            }

            return mappings;
        }

        // Page Audit and Modernization Actions
        
        [HttpGet]
        public async Task<IActionResult> PageAudit()
        {
            if (!await IsUserAdminAsync())
            {
                return RedirectToAction("AccessDenied");
            }

            // Get the latest inventory
            var latestInventory = await _pageAuditService.GetLatestInventoryAsync();
            
            // Get summary statistics
            var auditSummary = await _pageAuditService.GenerateAuditSummaryAsync();
            
            // Get unresolved critical and high issues
            var criticalIssues = await _pageAuditService.GetUnresolvedFindingsAsync(Severity.Critical);
            var highIssues = await _pageAuditService.GetUnresolvedFindingsAsync(Severity.High);

            ViewBag.LatestInventory = latestInventory;
            ViewBag.AuditSummary = auditSummary;
            ViewBag.CriticalIssues = criticalIssues.Take(10).ToList(); // Show top 10
            ViewBag.HighIssues = highIssues.Take(10).ToList(); // Show top 10

            return View();
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> GenerateInventory()
        {
            if (!await IsUserAdminAsync())
            {
                return Json(new { success = false, message = _localizer["ApiAccessDenied"].Value });
            }

            try
            {
                var userName = User.Identity?.Name ?? "Unknown Admin";
                _logger.LogInformation("Generating page inventory requested by {User}", userName);

                var inventory = await _pageAuditService.GenerateInventoryAsync(userName);

                _logger.LogInformation("Page inventory generated successfully. Version: {Version}, Total Pages: {TotalPages}", 
                    inventory.Version, inventory.TotalPages);

                return Json(new { 
                    success = true, 
                    message = _localizer["InventoryGeneratedSuccessfully"].Value,
                    inventory = new {
                        version = inventory.Version,
                        totalPages = inventory.TotalPages,
                        highPriorityPages = inventory.HighPriorityPages,
                        mediumPriorityPages = inventory.MediumPriorityPages,
                        lowPriorityPages = inventory.LowPriorityPages,
                        generatedBy = inventory.GeneratedBy,
                        dateCreated = inventory.DateCreated.ToString("yyyy-MM-dd HH:mm")
                    }
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error generating page inventory");
                return Json(new { success = false, message = _localizer["ErrorGeneratingInventory"].Value });
            }
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> AuditPage(string pageName)
        {
            if (!await IsUserAdminAsync())
            {
                return Json(new { success = false, message = _localizer["ApiAccessDenied"].Value });
            }

            if (string.IsNullOrWhiteSpace(pageName))
            {
                return Json(new { success = false, message = _localizer["PageNameRequired"].Value });
            }

            try
            {
                var userName = User.Identity?.Name ?? "Unknown Admin";
                _logger.LogInformation("Page audit requested for {PageName} by {User}", pageName, userName);

                var auditResult = await _pageAuditService.AuditPageAsync(pageName, userName);

                _logger.LogInformation("Page audit completed for {PageName}. Overall Score: {Score}", 
                    pageName, auditResult.OverallScore);

                return Json(new { 
                    success = true, 
                    message = _localizer["PageAuditCompleted"].Value,
                    auditResult = new {
                        pageName = pageName,
                        overallScore = auditResult.OverallScore,
                        securityScore = auditResult.SecurityScore,
                        accessibilityScore = auditResult.AccessibilityScore,
                        performanceScore = auditResult.PerformanceScore,
                        localizationScore = auditResult.LocalizationScore,
                        criticalIssues = auditResult.CriticalIssues,
                        highIssues = auditResult.HighIssues,
                        mediumIssues = auditResult.MediumIssues,
                        lowIssues = auditResult.LowIssues,
                        totalIssues = auditResult.TotalIssues,
                        isPassing = auditResult.IsPassing,
                        auditVersion = auditResult.AuditVersion,
                        dateCreated = auditResult.DateCreated.ToString("yyyy-MM-dd HH:mm")
                    }
                });
            }
            catch (ArgumentException argEx)
            {
                _logger.LogWarning(argEx, "Invalid page name for audit: {PageName}", pageName);
                return Json(new { success = false, message = argEx.Message });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error auditing page {PageName}", pageName);
                return Json(new { success = false, message = _localizer["ErrorAuditingPage"].Value });
            }
        }

        [HttpGet]
        public async Task<IActionResult> GetPageInventory()
        {
            if (!await IsUserAdminAsync())
            {
                return Json(new { success = false, message = _localizer["ApiAccessDenied"].Value });
            }

            try
            {
                var inventory = await _pageAuditService.GetLatestInventoryAsync();
                
                if (inventory == null)
                {
                    return Json(new { 
                        success = false, 
                        message = _localizer["NoInventoryFound"].Value,
                        needsGeneration = true 
                    });
                }

                var pages = inventory.Pages.Select(p => new {
                    name = p.Name,
                    controller = p.Controller,
                    action = p.Action,
                    routes = p.Routes,
                    complexity = p.Complexity.ToString(),
                    priority = p.Priority,
                    isModernized = p.IsModernized,
                    modernizedDate = p.ModernizedDate?.ToString("yyyy-MM-dd"),
                    auditCount = p.AuditResults.Count,
                    lastAuditScore = p.AuditResults.OrderByDescending(ar => ar.DateCreated)
                                                  .FirstOrDefault()?.OverallScore
                }).OrderBy(p => p.priority).ThenBy(p => p.name).ToList();

                return Json(new { 
                    success = true,
                    inventory = new {
                        version = inventory.Version,
                        totalPages = inventory.TotalPages,
                        highPriorityPages = inventory.HighPriorityPages,
                        mediumPriorityPages = inventory.MediumPriorityPages,
                        lowPriorityPages = inventory.LowPriorityPages,
                        generatedBy = inventory.GeneratedBy,
                        dateCreated = inventory.DateCreated.ToString("yyyy-MM-dd HH:mm"),
                        pages = pages
                    }
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving page inventory");
                return Json(new { success = false, message = _localizer["ErrorRetrievingInventory"].Value });
            }
        }

        [HttpGet]
        public async Task<IActionResult> GetPageAuditHistory(string pageName)
        {
            if (!await IsUserAdminAsync())
            {
                return Json(new { success = false, message = _localizer["ApiAccessDenied"].Value });
            }

            if (string.IsNullOrWhiteSpace(pageName))
            {
                return Json(new { success = false, message = _localizer["PageNameRequired"].Value });
            }

            try
            {
                var auditHistory = await _pageAuditService.GetPageAuditHistoryAsync(pageName);
                
                var history = auditHistory.Select(ah => new {
                    auditVersion = ah.AuditVersion,
                    auditedBy = ah.AuditedBy,
                    status = ah.Status.ToString(),
                    overallScore = ah.OverallScore,
                    securityScore = ah.SecurityScore,
                    accessibilityScore = ah.AccessibilityScore,
                    performanceScore = ah.PerformanceScore,
                    localizationScore = ah.LocalizationScore,
                    criticalIssues = ah.CriticalIssues,
                    highIssues = ah.HighIssues,
                    mediumIssues = ah.MediumIssues,
                    lowIssues = ah.LowIssues,
                    totalIssues = ah.TotalIssues,
                    isPassing = ah.IsPassing,
                    dateCreated = ah.DateCreated.ToString("yyyy-MM-dd HH:mm"),
                    notes = ah.Notes
                }).ToList();

                return Json(new { 
                    success = true,
                    pageName = pageName,
                    auditHistory = history
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving audit history for page {PageName}", pageName);
                return Json(new { success = false, message = _localizer["ErrorRetrievingAuditHistory"].Value });
            }
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> ResolveFinding(int findingId, string resolutionNotes)
        {
            if (!await IsUserAdminAsync())
            {
                return Json(new { success = false, message = _localizer["ApiAccessDenied"].Value });
            }

            if (findingId <= 0)
            {
                return Json(new { success = false, message = _localizer["InvalidFindingId"].Value });
            }

            if (string.IsNullOrWhiteSpace(resolutionNotes))
            {
                return Json(new { success = false, message = _localizer["ResolutionNotesRequired"].Value });
            }

            try
            {
                var resolvedFinding = await _pageAuditService.ResolveFindingAsync(findingId, resolutionNotes);
                
                _logger.LogInformation("Finding {FindingId} resolved by {User}: {Notes}", 
                    findingId, User.Identity?.Name, resolutionNotes);

                return Json(new { 
                    success = true, 
                    message = _localizer["FindingResolvedSuccessfully"].Value,
                    finding = new {
                        id = resolvedFinding.Id,
                        isResolved = resolvedFinding.IsResolved,
                        resolvedDate = resolvedFinding.ResolvedDate?.ToString("yyyy-MM-dd HH:mm"),
                        resolutionNotes = resolvedFinding.ResolutionNotes
                    }
                });
            }
            catch (ArgumentException argEx)
            {
                _logger.LogWarning(argEx, "Invalid finding ID for resolution: {FindingId}", findingId);
                return Json(new { success = false, message = argEx.Message });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error resolving finding {FindingId}", findingId);
                return Json(new { success = false, message = _localizer["ErrorResolvingFinding"].Value });
            }
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> GeneratePageReviewPlan()
        {
            if (!await IsUserAdminAsync())
            {
                return Json(new { success = false, message = _localizer["ApiAccessDenied"].Value });
            }

            try
            {
                var userName = User.Identity?.Name ?? "Unknown Admin";
                _logger.LogInformation("Page Review Plan generation requested by {User}", userName);

                var reviewPlan = await _pageAuditService.GeneratePageReviewPlanAsync();

                _logger.LogInformation("Page Review Plan generated with {TotalPages} pages, estimated {EstimatedHours} hours", 
                    reviewPlan.TotalPages, reviewPlan.EstimatedHours);

                return Json(new { 
                    success = true, 
                    message = _localizer["PageReviewPlanGenerated"].Value,
                    reviewPlan = new {
                        totalPages = reviewPlan.TotalPages,
                        estimatedHours = reviewPlan.EstimatedHours,
                        generatedAt = reviewPlan.GeneratedAt.ToString("yyyy-MM-dd HH:mm"),
                        highPriorityPages = reviewPlan.HighPriorityPages.Select(p => new {
                            pageName = p.PageName,
                            controller = p.Controller,
                            action = p.Action,
                            complexity = p.Complexity.ToString(),
                            priority = p.Priority,
                            riskScore = p.RiskScore,
                            impactScore = p.ImpactScore,
                            estimatedHours = p.EstimatedHours,
                            riskFactors = p.RiskFactors,
                            impactFactors = p.ImpactFactors,
                            recommendations = p.Recommendations
                        }).ToList(),
                        mediumPriorityPages = reviewPlan.MediumPriorityPages.Select(p => new {
                            pageName = p.PageName,
                            controller = p.Controller,
                            action = p.Action,
                            complexity = p.Complexity.ToString(),
                            priority = p.Priority,
                            riskScore = p.RiskScore,
                            impactScore = p.ImpactScore,
                            estimatedHours = p.EstimatedHours,
                            riskFactors = p.RiskFactors,
                            impactFactors = p.ImpactFactors,
                            recommendations = p.Recommendations
                        }).ToList(),
                        lowPriorityPages = reviewPlan.LowPriorityPages.Select(p => new {
                            pageName = p.PageName,
                            controller = p.Controller,
                            action = p.Action,
                            complexity = p.Complexity.ToString(),
                            priority = p.Priority,
                            riskScore = p.RiskScore,
                            impactScore = p.ImpactScore,
                            estimatedHours = p.EstimatedHours,
                            riskFactors = p.RiskFactors,
                            impactFactors = p.ImpactFactors,
                            recommendations = p.Recommendations
                        }).ToList(),
                        recommendedOrder = reviewPlan.RecommendedOrder
                    }
                });
            }
            catch (InvalidOperationException invEx)
            {
                _logger.LogWarning(invEx, "Cannot generate review plan: {Message}", invEx.Message);
                return Json(new { success = false, message = invEx.Message });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error generating page review plan");
                return Json(new { success = false, message = _localizer["ErrorGeneratingReviewPlan"].Value });
            }
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> GenerateInitialAuditReports()
        {
            if (!await IsUserAdminAsync())
            {
                return Json(new { success = false, message = _localizer["ApiAccessDenied"].Value });
            }

            try
            {
                var userName = User.Identity?.Name ?? "Unknown Admin";
                _logger.LogInformation("Initial audit reports generation requested by {User}", userName);

                var auditResults = await _pageAuditService.GenerateInitialAuditReportsAsync(userName);

                _logger.LogInformation("Generated {AuditCount} initial audit reports", auditResults.Count);

                return Json(new { 
                    success = true, 
                    message = _localizer["InitialAuditReportsGenerated"].Value,
                    auditResults = auditResults.Select(ar => new {
                        pageName = ar.PageInfo.Name,
                        auditVersion = ar.AuditVersion,
                        status = ar.Status.ToString(),
                        overallScore = ar.OverallScore,
                        securityScore = ar.SecurityScore,
                        accessibilityScore = ar.AccessibilityScore,
                        performanceScore = ar.PerformanceScore,
                        localizationScore = ar.LocalizationScore,
                        criticalIssues = ar.CriticalIssues,
                        highIssues = ar.HighIssues,
                        mediumIssues = ar.MediumIssues,
                        lowIssues = ar.LowIssues,
                        totalIssues = ar.TotalIssues,
                        isPassing = ar.IsPassing,
                        dateCreated = ar.DateCreated.ToString("yyyy-MM-dd HH:mm")
                    }).ToList(),
                    summary = new {
                        totalPages = auditResults.Count,
                        passingPages = auditResults.Count(ar => ar.IsPassing),
                        failingPages = auditResults.Count(ar => !ar.IsPassing),
                        averageScore = auditResults.Any() ? auditResults.Average(ar => ar.OverallScore) : 0,
                        totalCriticalIssues = auditResults.Sum(ar => ar.CriticalIssues),
                        totalHighIssues = auditResults.Sum(ar => ar.HighIssues),
                        totalMediumIssues = auditResults.Sum(ar => ar.MediumIssues),
                        totalLowIssues = auditResults.Sum(ar => ar.LowIssues)
                    }
                });
            }
            catch (InvalidOperationException invEx)
            {
                _logger.LogWarning(invEx, "Cannot generate initial audit reports: {Message}", invEx.Message);
                return Json(new { success = false, message = invEx.Message });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error generating initial audit reports");
                return Json(new { success = false, message = _localizer["ErrorGeneratingAuditReports"].Value });
            }
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> RunSecurityScan()
        {
            if (!await IsUserAdminAsync())
            {
                return Json(new { success = false, message = _localizer["ApiAccessDenied"].Value });
            }

            try
            {
                var userName = User.Identity?.Name ?? "Unknown Admin";
                _logger.LogInformation("Security scan requested by {User}", userName);

                // Generate comprehensive security report
                var securityReport = await _securityAuditService.GenerateSecurityReportAsync();

                _logger.LogInformation("Security scan completed. {SecurePages}/{TotalPages} pages are secure", 
                    securityReport.SecurePagesCount, securityReport.TotalPagesAudited);

                return Json(new { 
                    success = true, 
                    message = "Security scan completed successfully",
                    securityReport = new {
                        totalPagesAudited = securityReport.TotalPagesAudited,
                        securePagesCount = securityReport.SecurePagesCount,
                        vulnerablePagesCount = securityReport.VulnerablePagesCount,
                        averageSecurityScore = Math.Round(securityReport.AverageSecurityScore, 1),
                        criticalIssuesCount = securityReport.CriticalIssuesCount,
                        highRiskIssuesCount = securityReport.HighRiskIssuesCount,
                        mediumRiskIssuesCount = securityReport.MediumRiskIssuesCount,
                        lowRiskIssuesCount = securityReport.LowRiskIssuesCount,
                        topVulnerabilities = securityReport.TopVulnerabilities,
                        recommendedActions = securityReport.RecommendedActions,
                        generatedAt = securityReport.GeneratedAt.ToString("yyyy-MM-dd HH:mm")
                    }
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error during security scan");
                return Json(new { success = false, message = "Error during security scan: " + ex.Message });
            }
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> ScanAntiForgeryTokens()
        {
            if (!await IsUserAdminAsync())
            {
                return Json(new { success = false, message = _localizer["ApiAccessDenied"].Value });
            }

            try
            {
                var userName = User.Identity?.Name ?? "Unknown Admin";
                _logger.LogInformation("Anti-forgery token scan requested by {User}", userName);

                var securityAuditService = HttpContext.RequestServices.GetRequiredService<ISecurityAuditService>();
                var results = await securityAuditService.ScanAntiForgeryTokensAsync();

                var criticalCount = results.Count(r => r.RiskLevel == SecurityRiskLevel.Critical);
                var highCount = results.Count(r => r.RiskLevel == SecurityRiskLevel.High);

                _logger.LogInformation("Anti-forgery token scan completed. Found {Critical} critical and {High} high risk issues", 
                    criticalCount, highCount);

                return Json(new { 
                    success = true, 
                    message = $"Scanned {results.Count} POST actions for CSRF protection",
                    results = results.Select(r => new {
                        formLocation = r.FormLocation,
                        actionMethod = r.ActionMethod,
                        viewPath = r.ViewPath,
                        hasAntiForgeryToken = r.HasAntiForgeryToken,
                        hasValidateAttribute = r.HasValidateAntiForgeryAttribute,
                        riskLevel = r.RiskLevel.ToString(),
                        recommendation = r.Recommendation
                    }).ToList(),
                    summary = new {
                        totalActions = results.Count,
                        criticalRisk = criticalCount,
                        highRisk = highCount,
                        mediumRisk = results.Count(r => r.RiskLevel == SecurityRiskLevel.Medium),
                        lowRisk = results.Count(r => r.RiskLevel == SecurityRiskLevel.Low)
                    }
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error during anti-forgery token scan");
                return Json(new { success = false, message = "Error during CSRF scan: " + ex.Message });
            }
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> AnalyzeAuthorization()
        {
            if (!await IsUserAdminAsync())
            {
                return Json(new { success = false, message = _localizer["ApiAccessDenied"].Value });
            }

            try
            {
                var userName = User.Identity?.Name ?? "Unknown Admin";
                _logger.LogInformation("Authorization analysis requested by {User}", userName);

                var securityAuditService = HttpContext.RequestServices.GetRequiredService<ISecurityAuditService>();
                var results = await securityAuditService.AnalyzeAuthorizationAsync();

                var criticalCount = results.Count(r => r.RiskLevel == SecurityRiskLevel.Critical);
                var highCount = results.Count(r => r.RiskLevel == SecurityRiskLevel.High);

                _logger.LogInformation("Authorization analysis completed. Found {Critical} critical and {High} high risk issues", 
                    criticalCount, highCount);

                return Json(new { 
                    success = true, 
                    message = $"Analyzed {results.Count} controller actions for authorization",
                    results = results.Select(r => new {
                        controllerName = r.ControllerName,
                        actionName = r.ActionName,
                        fullActionPath = r.FullActionPath,
                        hasAuthorizeAttribute = r.HasAuthorizeAttribute,
                        hasAllowAnonymousAttribute = r.HasAllowAnonymousAttribute,
                        requiredRoles = r.RequiredRoles,
                        requiredPolicies = r.RequiredPolicies,
                        isPublicEndpoint = r.IsPublicEndpoint,
                        riskLevel = r.RiskLevel.ToString(),
                        recommendation = r.Recommendation
                    }).ToList(),
                    summary = new {
                        totalActions = results.Count,
                        criticalRisk = criticalCount,
                        highRisk = highCount,
                        mediumRisk = results.Count(r => r.RiskLevel == SecurityRiskLevel.Medium),
                        lowRisk = results.Count(r => r.RiskLevel == SecurityRiskLevel.Low),
                        authorizedActions = results.Count(r => r.HasAuthorizeAttribute),
                        publicEndpoints = results.Count(r => r.IsPublicEndpoint)
                    }
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error during authorization analysis");
                return Json(new { success = false, message = "Error during authorization analysis: " + ex.Message });
            }
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> ScanCsrfProtection()
        {
            if (!await IsUserAdminAsync())
            {
                return Json(new { success = false, message = _localizer["ApiAccessDenied"].Value });
            }

            try
            {
                var userName = User.Identity?.Name ?? "Unknown Admin";
                _logger.LogInformation("CSRF protection scan requested by {User}", userName);

                var securityAuditService = HttpContext.RequestServices.GetRequiredService<ISecurityAuditService>();
                var results = await securityAuditService.ScanCsrfProtectionAsync();

                var vulnerableCount = results.Count(r => r.RiskLevel == SecurityRiskLevel.High || r.RiskLevel == SecurityRiskLevel.Critical);
                var protectedCount = results.Count - vulnerableCount;

                _logger.LogInformation("CSRF protection scan completed. {Protected} protected, {Vulnerable} vulnerable actions", 
                    protectedCount, vulnerableCount);

                return Json(new { 
                    success = true, 
                    message = $"Scanned {results.Count} actions for CSRF protection",
                    report = new {
                        totalActions = results.Count,
                        protectedActions = protectedCount,
                        unprotectedActions = vulnerableCount,
                        protectionRate = results.Count > 0 ? Math.Round((double)protectedCount / results.Count * 100, 1) : 100.0,
                        vulnerableActions = results.Where(r => r.RiskLevel == SecurityRiskLevel.High || r.RiskLevel == SecurityRiskLevel.Critical)
                            .Select(r => new {
                                controller = r.ControllerName,
                                action = r.ActionName,
                                riskLevel = r.RiskLevel.ToString(),
                                recommendation = r.Recommendation
                            }).ToList(),
                        recommendations = new List<string>
                        {
                            "Add [ValidateAntiForgeryToken] to all state-changing actions",
                            "Use asp-antiforgery=\"true\" in all forms",
                            "Implement CSRF protection for AJAX requests",
                            "Review and secure all POST, PUT, DELETE endpoints"
                        }
                    }
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error during CSRF protection scan");
                return Json(new { success = false, message = "Error during CSRF protection scan: " + ex.Message });
            }
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> RunAccessibilityAudit()
        {
            if (!await IsUserAdminAsync())
            {
                return Json(new { success = false, message = _localizer["ApiAccessDenied"].Value });
            }

            try
            {
                var userName = User.Identity?.Name ?? "Unknown Admin";
                _logger.LogInformation("Accessibility audit requested by {User}", userName);

                var accessibilityReport = await _accessibilityAuditService.GenerateAccessibilityReportAsync();

                _logger.LogInformation("Accessibility audit completed. {CompliantPages}/{TotalPages} pages are compliant", 
                    accessibilityReport.CompliantPages, accessibilityReport.TotalPages);

                return Json(new { 
                    success = true, 
                    message = "Accessibility audit completed successfully",
                    report = new {
                        totalPages = accessibilityReport.TotalPages,
                        compliantPages = accessibilityReport.CompliantPages,
                        nonCompliantPages = accessibilityReport.NonCompliantPages,
                        averageAccessibilityScore = Math.Round(accessibilityReport.AverageAccessibilityScore, 1),
                        overallComplianceLevel = accessibilityReport.OverallComplianceLevel.ToString(),
                        criticalIssues = accessibilityReport.CriticalIssues,
                        seriousIssues = accessibilityReport.SeriousIssues,
                        moderateIssues = accessibilityReport.ModerateIssues,
                        minorIssues = accessibilityReport.MinorIssues,
                        topIssues = accessibilityReport.TopIssues.Select(issue => new {
                            pageName = issue.PageName,
                            wcagLevel = issue.WcagLevel.ToString(),
                            category = issue.Category,
                            issue = issue.Issue,
                            description = issue.Description,
                            recommendation = issue.Recommendation,
                            impact = issue.Impact.ToString()
                        }).ToList(),
                        recommendedActions = accessibilityReport.RecommendedActions,
                        generatedDate = accessibilityReport.GeneratedDate.ToString("yyyy-MM-dd HH:mm")
                    }
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error running accessibility audit");
                return Json(new { success = false, message = "Error running accessibility audit: " + ex.Message });
            }
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> AuditPageAccessibility(string pageName)
        {
            if (!await IsUserAdminAsync())
            {
                return Json(new { success = false, message = _localizer["ApiAccessDenied"].Value });
            }

            if (string.IsNullOrEmpty(pageName))
            {
                return Json(new { success = false, message = "Page name is required" });
            }

            try
            {
                var userName = User.Identity?.Name ?? "Unknown Admin";
                _logger.LogInformation("Page accessibility audit requested for {PageName} by {User}", pageName, userName);

                var auditResult = await _accessibilityAuditService.AuditPageAccessibilityAsync(pageName);

                _logger.LogInformation("Page accessibility audit completed for {PageName} with score {Score}%", 
                    pageName, auditResult.AccessibilityScore);

                return Json(new { 
                    success = true, 
                    message = $"Accessibility audit completed for {pageName}",
                    result = new {
                        pageName = auditResult.PageName,
                        accessibilityScore = auditResult.AccessibilityScore,
                        complianceLevel = auditResult.ComplianceLevel.ToString(),
                        hasLandmarkStructure = auditResult.HasLandmarkStructure,
                        hasProperHeadingHierarchy = auditResult.HasProperHeadingHierarchy,
                        hasFormLabels = auditResult.HasFormLabels,
                        hasAltTextForImages = auditResult.HasAltTextForImages,
                        hasKeyboardNavigation = auditResult.HasKeyboardNavigation,
                        hasScreenReaderSupport = auditResult.HasScreenReaderSupport,
                        hasColorContrastCompliance = auditResult.HasColorContrastCompliance,
                        hasFocusManagement = auditResult.HasFocusManagement,
                        issues = auditResult.Issues.Select(i => new {
                            wcagLevel = i.WcagLevel.ToString(),
                            wcagCriterion = i.WcagCriterion,
                            category = i.Category,
                            issue = i.Issue,
                            description = i.Description,
                            location = i.Location,
                            recommendation = i.Recommendation,
                            fixCode = i.FixCode,
                            isAutoFixable = i.IsAutoFixable,
                            impact = i.Impact.ToString()
                        }).ToList(),
                        recommendations = auditResult.Recommendations.Select(r => new {
                            title = r.Title,
                            description = r.Description,
                            priority = r.Priority.ToString(),
                            implementation = r.Implementation,
                            estimatedHours = r.EstimatedHours,
                            wcagReference = r.WcagReference
                        }).ToList(),
                        auditDate = auditResult.AuditDate.ToString("yyyy-MM-dd HH:mm")
                    }
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error auditing page accessibility for {PageName}", pageName);
                return Json(new { success = false, message = $"Error auditing page accessibility: {ex.Message}" });
            }
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> RunAxeCoreTest(string pageName)
        {
            if (!await IsUserAdminAsync())
            {
                return Json(new { success = false, message = _localizer["ApiAccessDenied"].Value });
            }

            if (string.IsNullOrEmpty(pageName))
            {
                return Json(new { success = false, message = "Page name is required" });
            }

            try
            {
                var userName = User.Identity?.Name ?? "Unknown Admin";
                _logger.LogInformation("Axe-core test requested for {PageName} by {User}", pageName, userName);

                var testResult = await _accessibilityAuditService.RunAxeCoreTestAsync(pageName);

                _logger.LogInformation("Axe-core test completed for {PageName}. Violations: {Violations}", 
                    pageName, testResult.ViolationsCount);

                return Json(new { 
                    success = true, 
                    message = $"Axe-core test completed for {pageName}",
                    result = new {
                        testPassed = testResult.TestPassed,
                        violationsCount = testResult.ViolationsCount,
                        incompleteCount = testResult.IncompleteCount,
                        violations = testResult.Violations.Select(v => new {
                            id = v.Id,
                            description = v.Description,
                            impact = v.Impact,
                            tags = v.Tags,
                            nodes = v.Nodes,
                            helpUrl = v.HelpUrl
                        }).ToList(),
                        incomplete = testResult.Incomplete.Select(i => new {
                            id = i.Id,
                            description = i.Description,
                            nodes = i.Nodes,
                            helpUrl = i.HelpUrl
                        }).ToList(),
                        testDate = testResult.TestDate.ToString("yyyy-MM-dd HH:mm")
                    }
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error running axe-core test for {PageName}", pageName);
                return Json(new { success = false, message = $"Error running axe-core test: {ex.Message}" });
            }
        }
    }
}
