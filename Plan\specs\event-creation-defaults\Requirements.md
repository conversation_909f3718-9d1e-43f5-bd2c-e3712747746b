# Requirements: Event Creation & Visibility Defaults

## 1. Functional Requirements

### 1.1. Admin Calendar: Default Checkbox States
- **REQ-1.1.1:** When an admin creates a new event, the `Requires Registration` and `Publish Event` checkboxes must be checked by default for all categories **except** "Tentatif" and "Practice".
- **REQ-1.1.2:** If the "Tentatif" category is selected, both `Requires Registration` and `Publish Event` must be **unchecked** by default.
- **REQ-1.1.3:** If the "Practice" category is selected, `Requires Registration` must be **unchecked** and `Publish Event` must be **checked** by default.
- **REQ-1.1.4:** An admin must be able to manually override the default checkbox states at any time. The system must always respect the user's final manual selection.

### 1.2. Event Import: Default States
- **REQ-1.2.1:** When importing events (CSV/Excel), the `IsPublished` and `RequiresRegistration` fields must be set according to the same default logic defined in REQ-1.1.1, REQ-1.1.2, and REQ-1.1.3, based on the event's category.

### 1.3. Calendar Visibility Rules
- **REQ-1.3.1:** All non-admin calendars (Public, Members, Subscribe) must **only** display events where `IsPublished` is `true`.
- **REQ-1.3.2:** The `/Events/Subscribe` page must **only** display events that are both `IsPublished` is `true` AND are open for registration (i.e., the registration deadline has not passed and the event is not full).

## 2. Non-Functional Requirements

- **NFR-2.1 (Usability):** The default checkbox logic should be handled client-side in the admin UI to provide immediate feedback to the user as they select a category.
- **NFR-2.2 (Consistency):** The business logic for setting default states must be centralized in the `EventService` to ensure consistency between manual creation and bulk import.

## 3. Acceptance Criteria

- **AC-1:** Create a new event with the "Tournament" category; verify both checkboxes are checked.
- **AC-2:** Create a new event and change the category to "Tentatif"; verify both checkboxes become unchecked.
- **AC-3:** Create a new event and change the category to "Practice"; verify "Requires Registration" is unchecked and "Publish Event" is checked.
- **AC-4:** Select the "Tentatif" category, then manually check both boxes; verify they remain checked.
- **AC-5:** Import a CSV file containing events with "Tentatif", "Practice", and "Game" categories; verify the resulting events in the database have the correct `IsPublished` and `RequiresRegistration` values.
- **AC-6:** As a public user, navigate to the `/Home/PublicCalendar` and verify that an unpublished event does not appear.
- **AC-7:** As a public user, navigate to `/Events/Subscribe` and verify that an event that is not open for registration does not appear.
