﻿// <auto-generated />
using System;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Metadata;
using Microsoft.EntityFrameworkCore.Migrations;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;
using ParaHockeyApp.Models;

#nullable disable

namespace ParaHockeyApp.Migrations
{
    [DbContext(typeof(ApplicationContext))]
    [Migration("20250721205515_UpdateEventCategories")]
    partial class UpdateEventCategories
    {
        /// <inheritdoc />
        protected override void BuildTargetModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder
                .HasAnnotation("ProductVersion", "8.0.0")
                .HasAnnotation("Relational:MaxIdentifierLength", 128);

            SqlServerModelBuilderExtensions.UseIdentityColumns(modelBuilder);

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityRole", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("ConcurrencyStamp")
                        .IsConcurrencyToken()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Name")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<string>("NormalizedName")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.HasKey("Id");

                    b.HasIndex("NormalizedName")
                        .IsUnique()
                        .HasDatabaseName("RoleNameIndex")
                        .HasFilter("[NormalizedName] IS NOT NULL");

                    b.ToTable("AspNetRoles", (string)null);
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityRoleClaim<string>", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("ClaimType")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ClaimValue")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("RoleId")
                        .IsRequired()
                        .HasColumnType("nvarchar(450)");

                    b.HasKey("Id");

                    b.HasIndex("RoleId");

                    b.ToTable("AspNetRoleClaims", (string)null);
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserClaim<string>", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("ClaimType")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ClaimValue")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("UserId")
                        .IsRequired()
                        .HasColumnType("nvarchar(450)");

                    b.HasKey("Id");

                    b.HasIndex("UserId");

                    b.ToTable("AspNetUserClaims", (string)null);
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserLogin<string>", b =>
                {
                    b.Property<string>("LoginProvider")
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("ProviderKey")
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("ProviderDisplayName")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("UserId")
                        .IsRequired()
                        .HasColumnType("nvarchar(450)");

                    b.HasKey("LoginProvider", "ProviderKey");

                    b.HasIndex("UserId");

                    b.ToTable("AspNetUserLogins", (string)null);
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserRole<string>", b =>
                {
                    b.Property<string>("UserId")
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("RoleId")
                        .HasColumnType("nvarchar(450)");

                    b.HasKey("UserId", "RoleId");

                    b.HasIndex("RoleId");

                    b.ToTable("AspNetUserRoles", (string)null);
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserToken<string>", b =>
                {
                    b.Property<string>("UserId")
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("LoginProvider")
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("Name")
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("Value")
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("UserId", "LoginProvider", "Name");

                    b.ToTable("AspNetUserTokens", (string)null);
                });

            modelBuilder.Entity("ParaHockeyApp.Models.Entities.AdminTypeEntity", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<int?>("CreatedByAdminId")
                        .HasColumnType("int");

                    b.Property<int?>("CreatedByMemberId")
                        .HasColumnType("int");

                    b.Property<int>("CreatedBySource")
                        .HasColumnType("int");

                    b.Property<DateTime>("DateCreated")
                        .HasColumnType("datetime2");

                    b.Property<DateTime?>("DateModified")
                        .HasColumnType("datetime2");

                    b.Property<string>("Description")
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.Property<bool>("IsActive")
                        .HasColumnType("bit");

                    b.Property<int?>("ModifiedByAdminId")
                        .HasColumnType("int");

                    b.Property<int?>("ModifiedByMemberId")
                        .HasColumnType("int");

                    b.Property<int?>("ModifiedBySource")
                        .HasColumnType("int");

                    b.Property<int>("SortOrder")
                        .HasColumnType("int");

                    b.Property<int>("TypeCode")
                        .HasColumnType("int");

                    b.Property<string>("TypeName")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.HasKey("Id");

                    b.HasIndex("CreatedByAdminId");

                    b.HasIndex("CreatedByMemberId");

                    b.HasIndex("ModifiedByAdminId");

                    b.HasIndex("ModifiedByMemberId");

                    b.HasIndex("TypeCode")
                        .IsUnique()
                        .HasDatabaseName("IX_AdminTypes_TypeCode");

                    b.ToTable("AdminTypes");
                });

            modelBuilder.Entity("ParaHockeyApp.Models.Entities.AdminUser", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<int>("AdminType")
                        .HasColumnType("int");

                    b.Property<int>("AdminTypeId")
                        .HasColumnType("int");

                    b.Property<int?>("CreatedByAdminId")
                        .HasColumnType("int");

                    b.Property<int?>("CreatedByMemberId")
                        .HasColumnType("int");

                    b.Property<int>("CreatedBySource")
                        .HasColumnType("int");

                    b.Property<DateTime>("DateCreated")
                        .HasColumnType("datetime2");

                    b.Property<DateTime?>("DateModified")
                        .HasColumnType("datetime2");

                    b.Property<string>("Email")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<bool>("IsActive")
                        .HasColumnType("bit");

                    b.Property<int?>("ModifiedByAdminId")
                        .HasColumnType("int");

                    b.Property<int?>("ModifiedByMemberId")
                        .HasColumnType("int");

                    b.Property<int?>("ModifiedBySource")
                        .HasColumnType("int");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.HasKey("Id");

                    b.HasIndex("AdminTypeId");

                    b.HasIndex("CreatedByAdminId");

                    b.HasIndex("CreatedByMemberId");

                    b.HasIndex("Email")
                        .IsUnique()
                        .HasDatabaseName("IX_AdminUsers_Email");

                    b.HasIndex("ModifiedByAdminId");

                    b.HasIndex("ModifiedByMemberId");

                    b.ToTable("AdminUsers");
                });

            modelBuilder.Entity("ParaHockeyApp.Models.Entities.AppUser", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("nvarchar(450)");

                    b.Property<int>("AccessFailedCount")
                        .HasColumnType("int");

                    b.Property<string>("ConcurrencyStamp")
                        .IsConcurrencyToken()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Email")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<bool>("EmailConfirmed")
                        .HasColumnType("bit");

                    b.Property<bool>("LockoutEnabled")
                        .HasColumnType("bit");

                    b.Property<DateTimeOffset?>("LockoutEnd")
                        .HasColumnType("datetimeoffset");

                    b.Property<string>("NormalizedEmail")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<string>("NormalizedUserName")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<string>("PasswordHash")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("PhoneNumber")
                        .HasColumnType("nvarchar(max)");

                    b.Property<bool>("PhoneNumberConfirmed")
                        .HasColumnType("bit");

                    b.Property<string>("SecurityStamp")
                        .HasColumnType("nvarchar(max)");

                    b.Property<bool>("TwoFactorEnabled")
                        .HasColumnType("bit");

                    b.Property<string>("UserName")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.HasKey("Id");

                    b.HasIndex("NormalizedEmail")
                        .HasDatabaseName("EmailIndex");

                    b.HasIndex("NormalizedUserName")
                        .IsUnique()
                        .HasDatabaseName("UserNameIndex")
                        .HasFilter("[NormalizedUserName] IS NOT NULL");

                    b.ToTable("AspNetUsers", (string)null);
                });

            modelBuilder.Entity("ParaHockeyApp.Models.Entities.AuditLog", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("Action")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("nvarchar(20)");

                    b.Property<string>("Description")
                        .HasMaxLength(1000)
                        .HasColumnType("nvarchar(1000)");

                    b.Property<int>("EntityId")
                        .HasColumnType("int");

                    b.Property<string>("EntityType")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<string>("IPAddress")
                        .HasMaxLength(45)
                        .HasColumnType("nvarchar(45)");

                    b.Property<string>("NewValues")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("OldValues")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int?>("PerformedByAdminId")
                        .HasColumnType("int");

                    b.Property<int?>("PerformedByMemberId")
                        .HasColumnType("int");

                    b.Property<int>("PerformedBySource")
                        .HasColumnType("int");

                    b.Property<string>("PerformerName")
                        .IsRequired()
                        .HasMaxLength(101)
                        .HasColumnType("nvarchar(101)");

                    b.Property<DateTime>("Timestamp")
                        .HasColumnType("datetime2");

                    b.HasKey("Id");

                    b.HasIndex("PerformedByAdminId")
                        .HasDatabaseName("IX_AuditLogs_PerformedByAdmin");

                    b.HasIndex("PerformedByMemberId")
                        .HasDatabaseName("IX_AuditLogs_PerformedByMember");

                    b.HasIndex("Timestamp")
                        .HasDatabaseName("IX_AuditLogs_Timestamp");

                    b.HasIndex("EntityType", "EntityId")
                        .HasDatabaseName("IX_AuditLogs_Entity");

                    b.ToTable("AuditLogs");
                });

            modelBuilder.Entity("ParaHockeyApp.Models.Entities.EmergencyContact", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<int?>("CreatedByAdminId")
                        .HasColumnType("int");

                    b.Property<int?>("CreatedByMemberId")
                        .HasColumnType("int");

                    b.Property<int>("CreatedBySource")
                        .HasColumnType("int");

                    b.Property<DateTime>("DateCreated")
                        .HasColumnType("datetime2");

                    b.Property<DateTime?>("DateModified")
                        .HasColumnType("datetime2");

                    b.Property<string>("Email")
                        .HasMaxLength(254)
                        .HasColumnType("nvarchar(254)");

                    b.Property<string>("FirstName")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<bool>("IsActive")
                        .HasColumnType("bit");

                    b.Property<string>("LastName")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<int>("MemberId")
                        .HasColumnType("int");

                    b.Property<int?>("ModifiedByAdminId")
                        .HasColumnType("int");

                    b.Property<int?>("ModifiedByMemberId")
                        .HasColumnType("int");

                    b.Property<int?>("ModifiedBySource")
                        .HasColumnType("int");

                    b.Property<string>("Phone")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("nvarchar(20)");

                    b.Property<string>("RelationToUser")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.HasKey("Id");

                    b.HasIndex("CreatedByAdminId");

                    b.HasIndex("CreatedByMemberId");

                    b.HasIndex("MemberId");

                    b.HasIndex("ModifiedByAdminId");

                    b.HasIndex("ModifiedByMemberId");

                    b.ToTable("EmergencyContacts");
                });

            modelBuilder.Entity("ParaHockeyApp.Models.Entities.Event", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("ContactEmail")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("ContactPerson")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("ContactPhone")
                        .HasMaxLength(20)
                        .HasColumnType("nvarchar(20)");

                    b.Property<int?>("CreatedByAdminId")
                        .HasColumnType("int");

                    b.Property<int?>("CreatedByMemberId")
                        .HasColumnType("int");

                    b.Property<int>("CreatedBySource")
                        .HasColumnType("int");

                    b.Property<DateTime>("DateCreated")
                        .HasColumnType("datetime2");

                    b.Property<DateTime?>("DateModified")
                        .HasColumnType("datetime2");

                    b.Property<string>("Description")
                        .HasMaxLength(2000)
                        .HasColumnType("nvarchar(2000)");

                    b.Property<DateTime>("EndDate")
                        .HasColumnType("datetime2");

                    b.Property<int>("EventCategoryId")
                        .HasColumnType("int");

                    b.Property<bool>("IsActive")
                        .HasColumnType("bit");

                    b.Property<bool>("IsAllDay")
                        .HasColumnType("bit");

                    b.Property<bool>("IsPublished")
                        .HasColumnType("bit");

                    b.Property<bool>("IsRecurring")
                        .HasColumnType("bit");

                    b.Property<string>("Location")
                        .HasMaxLength(300)
                        .HasColumnType("nvarchar(300)");

                    b.Property<int>("MaxParticipants")
                        .HasColumnType("int");

                    b.Property<int?>("ModifiedByAdminId")
                        .HasColumnType("int");

                    b.Property<int?>("ModifiedByMemberId")
                        .HasColumnType("int");

                    b.Property<int?>("ModifiedBySource")
                        .HasColumnType("int");

                    b.Property<string>("OrganizerNotes")
                        .HasMaxLength(1000)
                        .HasColumnType("nvarchar(1000)");

                    b.Property<int>("Priority")
                        .HasColumnType("int");

                    b.Property<string>("RecurrencePattern")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<DateTime?>("RegistrationDeadline")
                        .HasColumnType("datetime2");

                    b.Property<bool>("RequiresRegistration")
                        .HasColumnType("bit");

                    b.Property<DateTime>("StartDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("Title")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.HasKey("Id");

                    b.HasIndex("CreatedByAdminId");

                    b.HasIndex("CreatedByMemberId");

                    b.HasIndex("EventCategoryId")
                        .HasDatabaseName("IX_Events_EventCategoryId");

                    b.HasIndex("IsPublished")
                        .HasDatabaseName("IX_Events_IsPublished");

                    b.HasIndex("ModifiedByAdminId");

                    b.HasIndex("ModifiedByMemberId");

                    b.HasIndex("StartDate")
                        .HasDatabaseName("IX_Events_StartDate");

                    b.ToTable("Events");
                });

            modelBuilder.Entity("ParaHockeyApp.Models.Entities.EventCategory", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("Color")
                        .IsRequired()
                        .HasMaxLength(7)
                        .HasColumnType("nvarchar(7)");

                    b.Property<int?>("CreatedByAdminId")
                        .HasColumnType("int");

                    b.Property<int?>("CreatedByMemberId")
                        .HasColumnType("int");

                    b.Property<int>("CreatedBySource")
                        .HasColumnType("int");

                    b.Property<DateTime>("DateCreated")
                        .HasColumnType("datetime2");

                    b.Property<DateTime?>("DateModified")
                        .HasColumnType("datetime2");

                    b.Property<string>("DescriptionKey")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)");

                    b.Property<string>("DisplayNameKey")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<int>("DisplayOrder")
                        .HasColumnType("int");

                    b.Property<string>("IconClass")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<bool>("IsActive")
                        .HasColumnType("bit");

                    b.Property<int>("MaxParticipants")
                        .HasColumnType("int");

                    b.Property<int?>("ModifiedByAdminId")
                        .HasColumnType("int");

                    b.Property<int?>("ModifiedByMemberId")
                        .HasColumnType("int");

                    b.Property<int?>("ModifiedBySource")
                        .HasColumnType("int");

                    b.Property<bool>("RequiresRegistration")
                        .HasColumnType("bit");

                    b.HasKey("Id");

                    b.HasIndex("CreatedByAdminId");

                    b.HasIndex("CreatedByMemberId");

                    b.HasIndex("DisplayOrder")
                        .HasDatabaseName("IX_EventCategories_DisplayOrder");

                    b.HasIndex("ModifiedByAdminId");

                    b.HasIndex("ModifiedByMemberId");

                    b.ToTable("EventCategories");
                });

            modelBuilder.Entity("ParaHockeyApp.Models.Entities.EventRegistration", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("AdminNotes")
                        .HasMaxLength(1000)
                        .HasColumnType("nvarchar(1000)");

                    b.Property<bool?>("Attended")
                        .HasColumnType("bit");

                    b.Property<DateTime?>("CancellationDate")
                        .HasColumnType("datetime2");

                    b.Property<DateTime?>("ConfirmationDate")
                        .HasColumnType("datetime2");

                    b.Property<int?>("CreatedByAdminId")
                        .HasColumnType("int");

                    b.Property<int?>("CreatedByMemberId")
                        .HasColumnType("int");

                    b.Property<int>("CreatedBySource")
                        .HasColumnType("int");

                    b.Property<DateTime>("DateCreated")
                        .HasColumnType("datetime2");

                    b.Property<DateTime?>("DateModified")
                        .HasColumnType("datetime2");

                    b.Property<string>("EmergencyContact")
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.Property<string>("EmergencyPhone")
                        .HasMaxLength(20)
                        .HasColumnType("nvarchar(20)");

                    b.Property<int>("EventId")
                        .HasColumnType("int");

                    b.Property<int>("GuestCount")
                        .HasColumnType("int");

                    b.Property<string>("GuestNames")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)");

                    b.Property<bool>("IsActive")
                        .HasColumnType("bit");

                    b.Property<int>("MemberId")
                        .HasColumnType("int");

                    b.Property<string>("MemberNotes")
                        .HasMaxLength(1000)
                        .HasColumnType("nvarchar(1000)");

                    b.Property<int?>("ModifiedByAdminId")
                        .HasColumnType("int");

                    b.Property<int?>("ModifiedByMemberId")
                        .HasColumnType("int");

                    b.Property<int?>("ModifiedBySource")
                        .HasColumnType("int");

                    b.Property<DateTime>("RegistrationDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("SpecialRequirements")
                        .HasMaxLength(1000)
                        .HasColumnType("nvarchar(1000)");

                    b.Property<int>("Status")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("CreatedByAdminId");

                    b.HasIndex("CreatedByMemberId");

                    b.HasIndex("MemberId");

                    b.HasIndex("ModifiedByAdminId");

                    b.HasIndex("ModifiedByMemberId");

                    b.HasIndex("Status")
                        .HasDatabaseName("IX_EventRegistrations_Status");

                    b.HasIndex("EventId", "MemberId")
                        .IsUnique()
                        .HasDatabaseName("IX_EventRegistrations_EventId_MemberId");

                    b.ToTable("EventRegistrations");
                });

            modelBuilder.Entity("ParaHockeyApp.Models.Entities.Gender", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("DisplayNameKey")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.HasKey("Id");

                    b.ToTable("Genders");
                });

            modelBuilder.Entity("ParaHockeyApp.Models.Entities.Member", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("Address")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.Property<string>("City")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<int?>("CreatedByAdminId")
                        .HasColumnType("int");

                    b.Property<int?>("CreatedByMemberId")
                        .HasColumnType("int");

                    b.Property<int>("CreatedBySource")
                        .HasColumnType("int");

                    b.Property<DateTime>("DateCreated")
                        .HasColumnType("datetime2");

                    b.Property<DateTime?>("DateModified")
                        .HasColumnType("datetime2");

                    b.Property<DateTime>("DateOfBirth")
                        .HasColumnType("datetime2");

                    b.Property<string>("Email")
                        .IsRequired()
                        .HasMaxLength(254)
                        .HasColumnType("nvarchar(254)");

                    b.Property<string>("FirstName")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<int>("GenderId")
                        .HasColumnType("int");

                    b.Property<string>("HQc_Id")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<bool>("IsActive")
                        .HasColumnType("bit");

                    b.Property<string>("LastName")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<int?>("ModifiedByAdminId")
                        .HasColumnType("int");

                    b.Property<int?>("ModifiedByMemberId")
                        .HasColumnType("int");

                    b.Property<int?>("ModifiedBySource")
                        .HasColumnType("int");

                    b.Property<string>("Phone")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("nvarchar(20)");

                    b.Property<int>("PhoneTypeId")
                        .HasColumnType("int");

                    b.Property<string>("PostalCode")
                        .IsRequired()
                        .HasMaxLength(10)
                        .HasColumnType("nvarchar(10)");

                    b.Property<int>("ProvinceId")
                        .HasColumnType("int");

                    b.Property<int>("RegistrationTypeId")
                        .HasColumnType("int");

                    b.Property<string>("UserId")
                        .HasColumnType("nvarchar(450)");

                    b.HasKey("Id");

                    b.HasIndex("City")
                        .HasDatabaseName("IX_Members_City");

                    b.HasIndex("CreatedByAdminId");

                    b.HasIndex("CreatedByMemberId");

                    b.HasIndex("GenderId");

                    b.HasIndex("HQc_Id")
                        .IsUnique()
                        .HasFilter("[HQc_Id] IS NOT NULL");

                    b.HasIndex("IsActive")
                        .HasDatabaseName("IX_Members_IsActive");

                    b.HasIndex("ModifiedByAdminId");

                    b.HasIndex("ModifiedByMemberId");

                    b.HasIndex("PhoneTypeId");

                    b.HasIndex("ProvinceId");

                    b.HasIndex("RegistrationTypeId");

                    b.HasIndex("UserId");

                    b.HasIndex("IsActive", "City")
                        .HasDatabaseName("IX_Members_IsActive_City");

                    b.HasIndex("IsActive", "RegistrationTypeId")
                        .HasDatabaseName("IX_Members_IsActive_RegistrationTypeId");

                    b.ToTable("Members");
                });

            modelBuilder.Entity("ParaHockeyApp.Models.Entities.MemberLog", b =>
                {
                    b.Property<int>("NoLog")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("NoLog"));

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasMaxLength(1000)
                        .HasColumnType("nvarchar(1000)");

                    b.Property<int>("EditorId")
                        .HasColumnType("int");

                    b.Property<string>("EditorName")
                        .IsRequired()
                        .HasMaxLength(101)
                        .HasColumnType("nvarchar(101)");

                    b.Property<DateTime>("LogDate")
                        .HasColumnType("datetime2");

                    b.Property<int>("MemberId")
                        .HasColumnType("int");

                    b.HasKey("NoLog");

                    b.HasIndex("EditorId");

                    b.HasIndex("LogDate");

                    b.HasIndex("MemberId");

                    b.ToTable("MemberLogs");
                });

            modelBuilder.Entity("ParaHockeyApp.Models.Entities.Parent", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<int?>("CreatedByAdminId")
                        .HasColumnType("int");

                    b.Property<int?>("CreatedByMemberId")
                        .HasColumnType("int");

                    b.Property<int>("CreatedBySource")
                        .HasColumnType("int");

                    b.Property<DateTime>("DateCreated")
                        .HasColumnType("datetime2");

                    b.Property<DateTime?>("DateModified")
                        .HasColumnType("datetime2");

                    b.Property<string>("Email")
                        .HasMaxLength(254)
                        .HasColumnType("nvarchar(254)");

                    b.Property<string>("FirstName")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<bool>("IsActive")
                        .HasColumnType("bit");

                    b.Property<string>("LastName")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<int>("MemberId")
                        .HasColumnType("int");

                    b.Property<int?>("ModifiedByAdminId")
                        .HasColumnType("int");

                    b.Property<int?>("ModifiedByMemberId")
                        .HasColumnType("int");

                    b.Property<int?>("ModifiedBySource")
                        .HasColumnType("int");

                    b.Property<string>("ParentType")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<string>("Phone")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("nvarchar(20)");

                    b.HasKey("Id");

                    b.HasIndex("CreatedByAdminId");

                    b.HasIndex("CreatedByMemberId");

                    b.HasIndex("MemberId");

                    b.HasIndex("ModifiedByAdminId");

                    b.HasIndex("ModifiedByMemberId");

                    b.ToTable("Parents");
                });

            modelBuilder.Entity("ParaHockeyApp.Models.Entities.PhoneType", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("DisplayNameKey")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.HasKey("Id");

                    b.ToTable("PhoneTypes");
                });

            modelBuilder.Entity("ParaHockeyApp.Models.Entities.Province", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("Code")
                        .IsRequired()
                        .HasMaxLength(2)
                        .HasColumnType("nvarchar(2)");

                    b.Property<string>("DisplayNameKey")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.HasKey("Id");

                    b.ToTable("Provinces");
                });

            modelBuilder.Entity("ParaHockeyApp.Models.Entities.RegistrationType", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("DescriptionKey")
                        .IsRequired()
                        .HasMaxLength(250)
                        .HasColumnType("nvarchar(250)");

                    b.Property<string>("DisplayNameKey")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.HasKey("Id");

                    b.ToTable("RegistrationTypes");
                });

            modelBuilder.Entity("ParaHockeyApp.Models.Entities.SavedSearch", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<int?>("CreatedByAdminId")
                        .HasColumnType("int");

                    b.Property<int?>("CreatedByMemberId")
                        .HasColumnType("int");

                    b.Property<int>("CreatedBySource")
                        .HasColumnType("int");

                    b.Property<DateTime>("DateCreated")
                        .HasColumnType("datetime2");

                    b.Property<DateTime?>("DateModified")
                        .HasColumnType("datetime2");

                    b.Property<string>("Description")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)");

                    b.Property<bool>("IsActive")
                        .HasColumnType("bit");

                    b.Property<DateTime>("LastUsed")
                        .HasColumnType("datetime2");

                    b.Property<int?>("ModifiedByAdminId")
                        .HasColumnType("int");

                    b.Property<int?>("ModifiedByMemberId")
                        .HasColumnType("int");

                    b.Property<int?>("ModifiedBySource")
                        .HasColumnType("int");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("SearchCriteriaJson")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("UsageCount")
                        .HasColumnType("int");

                    b.Property<string>("UserId")
                        .IsRequired()
                        .HasMaxLength(450)
                        .HasColumnType("nvarchar(450)");

                    b.HasKey("Id");

                    b.HasIndex("CreatedByAdminId");

                    b.HasIndex("CreatedByMemberId");

                    b.HasIndex("ModifiedByAdminId");

                    b.HasIndex("ModifiedByMemberId");

                    b.ToTable("SavedSearches");
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityRoleClaim<string>", b =>
                {
                    b.HasOne("Microsoft.AspNetCore.Identity.IdentityRole", null)
                        .WithMany()
                        .HasForeignKey("RoleId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserClaim<string>", b =>
                {
                    b.HasOne("ParaHockeyApp.Models.Entities.AppUser", null)
                        .WithMany()
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserLogin<string>", b =>
                {
                    b.HasOne("ParaHockeyApp.Models.Entities.AppUser", null)
                        .WithMany()
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserRole<string>", b =>
                {
                    b.HasOne("Microsoft.AspNetCore.Identity.IdentityRole", null)
                        .WithMany()
                        .HasForeignKey("RoleId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("ParaHockeyApp.Models.Entities.AppUser", null)
                        .WithMany()
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserToken<string>", b =>
                {
                    b.HasOne("ParaHockeyApp.Models.Entities.AppUser", null)
                        .WithMany()
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("ParaHockeyApp.Models.Entities.AdminTypeEntity", b =>
                {
                    b.HasOne("ParaHockeyApp.Models.Entities.AdminUser", "CreatedByAdmin")
                        .WithMany()
                        .HasForeignKey("CreatedByAdminId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("ParaHockeyApp.Models.Entities.Member", "CreatedByMember")
                        .WithMany()
                        .HasForeignKey("CreatedByMemberId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("ParaHockeyApp.Models.Entities.AdminUser", "ModifiedByAdmin")
                        .WithMany()
                        .HasForeignKey("ModifiedByAdminId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("ParaHockeyApp.Models.Entities.Member", "ModifiedByMember")
                        .WithMany()
                        .HasForeignKey("ModifiedByMemberId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.Navigation("CreatedByAdmin");

                    b.Navigation("CreatedByMember");

                    b.Navigation("ModifiedByAdmin");

                    b.Navigation("ModifiedByMember");
                });

            modelBuilder.Entity("ParaHockeyApp.Models.Entities.AdminUser", b =>
                {
                    b.HasOne("ParaHockeyApp.Models.Entities.AdminTypeEntity", "AdminTypeEntity")
                        .WithMany("AdminUsers")
                        .HasForeignKey("AdminTypeId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("ParaHockeyApp.Models.Entities.AdminUser", "CreatedByAdmin")
                        .WithMany()
                        .HasForeignKey("CreatedByAdminId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("ParaHockeyApp.Models.Entities.Member", "CreatedByMember")
                        .WithMany()
                        .HasForeignKey("CreatedByMemberId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("ParaHockeyApp.Models.Entities.AdminUser", "ModifiedByAdmin")
                        .WithMany()
                        .HasForeignKey("ModifiedByAdminId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("ParaHockeyApp.Models.Entities.Member", "ModifiedByMember")
                        .WithMany()
                        .HasForeignKey("ModifiedByMemberId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.Navigation("AdminTypeEntity");

                    b.Navigation("CreatedByAdmin");

                    b.Navigation("CreatedByMember");

                    b.Navigation("ModifiedByAdmin");

                    b.Navigation("ModifiedByMember");
                });

            modelBuilder.Entity("ParaHockeyApp.Models.Entities.AuditLog", b =>
                {
                    b.HasOne("ParaHockeyApp.Models.Entities.AdminUser", "PerformedByAdmin")
                        .WithMany()
                        .HasForeignKey("PerformedByAdminId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("ParaHockeyApp.Models.Entities.Member", "PerformedByMember")
                        .WithMany()
                        .HasForeignKey("PerformedByMemberId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.Navigation("PerformedByAdmin");

                    b.Navigation("PerformedByMember");
                });

            modelBuilder.Entity("ParaHockeyApp.Models.Entities.EmergencyContact", b =>
                {
                    b.HasOne("ParaHockeyApp.Models.Entities.AdminUser", "CreatedByAdmin")
                        .WithMany()
                        .HasForeignKey("CreatedByAdminId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("ParaHockeyApp.Models.Entities.Member", "CreatedByMember")
                        .WithMany()
                        .HasForeignKey("CreatedByMemberId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("ParaHockeyApp.Models.Entities.Member", "Member")
                        .WithMany()
                        .HasForeignKey("MemberId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("ParaHockeyApp.Models.Entities.AdminUser", "ModifiedByAdmin")
                        .WithMany()
                        .HasForeignKey("ModifiedByAdminId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("ParaHockeyApp.Models.Entities.Member", "ModifiedByMember")
                        .WithMany()
                        .HasForeignKey("ModifiedByMemberId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.Navigation("CreatedByAdmin");

                    b.Navigation("CreatedByMember");

                    b.Navigation("Member");

                    b.Navigation("ModifiedByAdmin");

                    b.Navigation("ModifiedByMember");
                });

            modelBuilder.Entity("ParaHockeyApp.Models.Entities.Event", b =>
                {
                    b.HasOne("ParaHockeyApp.Models.Entities.AdminUser", "CreatedByAdmin")
                        .WithMany()
                        .HasForeignKey("CreatedByAdminId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("ParaHockeyApp.Models.Entities.Member", "CreatedByMember")
                        .WithMany()
                        .HasForeignKey("CreatedByMemberId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("ParaHockeyApp.Models.Entities.EventCategory", "EventCategory")
                        .WithMany("Events")
                        .HasForeignKey("EventCategoryId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("ParaHockeyApp.Models.Entities.AdminUser", "ModifiedByAdmin")
                        .WithMany()
                        .HasForeignKey("ModifiedByAdminId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("ParaHockeyApp.Models.Entities.Member", "ModifiedByMember")
                        .WithMany()
                        .HasForeignKey("ModifiedByMemberId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.Navigation("CreatedByAdmin");

                    b.Navigation("CreatedByMember");

                    b.Navigation("EventCategory");

                    b.Navigation("ModifiedByAdmin");

                    b.Navigation("ModifiedByMember");
                });

            modelBuilder.Entity("ParaHockeyApp.Models.Entities.EventCategory", b =>
                {
                    b.HasOne("ParaHockeyApp.Models.Entities.AdminUser", "CreatedByAdmin")
                        .WithMany()
                        .HasForeignKey("CreatedByAdminId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("ParaHockeyApp.Models.Entities.Member", "CreatedByMember")
                        .WithMany()
                        .HasForeignKey("CreatedByMemberId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("ParaHockeyApp.Models.Entities.AdminUser", "ModifiedByAdmin")
                        .WithMany()
                        .HasForeignKey("ModifiedByAdminId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("ParaHockeyApp.Models.Entities.Member", "ModifiedByMember")
                        .WithMany()
                        .HasForeignKey("ModifiedByMemberId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.Navigation("CreatedByAdmin");

                    b.Navigation("CreatedByMember");

                    b.Navigation("ModifiedByAdmin");

                    b.Navigation("ModifiedByMember");
                });

            modelBuilder.Entity("ParaHockeyApp.Models.Entities.EventRegistration", b =>
                {
                    b.HasOne("ParaHockeyApp.Models.Entities.AdminUser", "CreatedByAdmin")
                        .WithMany()
                        .HasForeignKey("CreatedByAdminId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("ParaHockeyApp.Models.Entities.Member", "CreatedByMember")
                        .WithMany()
                        .HasForeignKey("CreatedByMemberId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("ParaHockeyApp.Models.Entities.Event", "Event")
                        .WithMany("EventRegistrations")
                        .HasForeignKey("EventId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("ParaHockeyApp.Models.Entities.Member", "Member")
                        .WithMany()
                        .HasForeignKey("MemberId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("ParaHockeyApp.Models.Entities.AdminUser", "ModifiedByAdmin")
                        .WithMany()
                        .HasForeignKey("ModifiedByAdminId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("ParaHockeyApp.Models.Entities.Member", "ModifiedByMember")
                        .WithMany()
                        .HasForeignKey("ModifiedByMemberId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.Navigation("CreatedByAdmin");

                    b.Navigation("CreatedByMember");

                    b.Navigation("Event");

                    b.Navigation("Member");

                    b.Navigation("ModifiedByAdmin");

                    b.Navigation("ModifiedByMember");
                });

            modelBuilder.Entity("ParaHockeyApp.Models.Entities.Member", b =>
                {
                    b.HasOne("ParaHockeyApp.Models.Entities.AdminUser", "CreatedByAdmin")
                        .WithMany()
                        .HasForeignKey("CreatedByAdminId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("ParaHockeyApp.Models.Entities.Member", "CreatedByMember")
                        .WithMany()
                        .HasForeignKey("CreatedByMemberId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("ParaHockeyApp.Models.Entities.Gender", "Gender")
                        .WithMany()
                        .HasForeignKey("GenderId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("ParaHockeyApp.Models.Entities.AdminUser", "ModifiedByAdmin")
                        .WithMany()
                        .HasForeignKey("ModifiedByAdminId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("ParaHockeyApp.Models.Entities.Member", "ModifiedByMember")
                        .WithMany()
                        .HasForeignKey("ModifiedByMemberId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("ParaHockeyApp.Models.Entities.PhoneType", "PhoneType")
                        .WithMany()
                        .HasForeignKey("PhoneTypeId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("ParaHockeyApp.Models.Entities.Province", "Province")
                        .WithMany()
                        .HasForeignKey("ProvinceId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("ParaHockeyApp.Models.Entities.RegistrationType", "RegistrationType")
                        .WithMany()
                        .HasForeignKey("RegistrationTypeId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("ParaHockeyApp.Models.Entities.AppUser", "User")
                        .WithMany()
                        .HasForeignKey("UserId");

                    b.Navigation("CreatedByAdmin");

                    b.Navigation("CreatedByMember");

                    b.Navigation("Gender");

                    b.Navigation("ModifiedByAdmin");

                    b.Navigation("ModifiedByMember");

                    b.Navigation("PhoneType");

                    b.Navigation("Province");

                    b.Navigation("RegistrationType");

                    b.Navigation("User");
                });

            modelBuilder.Entity("ParaHockeyApp.Models.Entities.MemberLog", b =>
                {
                    b.HasOne("ParaHockeyApp.Models.Entities.Member", "Member")
                        .WithMany("MemberLogs")
                        .HasForeignKey("MemberId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Member");
                });

            modelBuilder.Entity("ParaHockeyApp.Models.Entities.Parent", b =>
                {
                    b.HasOne("ParaHockeyApp.Models.Entities.AdminUser", "CreatedByAdmin")
                        .WithMany()
                        .HasForeignKey("CreatedByAdminId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("ParaHockeyApp.Models.Entities.Member", "CreatedByMember")
                        .WithMany()
                        .HasForeignKey("CreatedByMemberId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("ParaHockeyApp.Models.Entities.Member", "Member")
                        .WithMany()
                        .HasForeignKey("MemberId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("ParaHockeyApp.Models.Entities.AdminUser", "ModifiedByAdmin")
                        .WithMany()
                        .HasForeignKey("ModifiedByAdminId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("ParaHockeyApp.Models.Entities.Member", "ModifiedByMember")
                        .WithMany()
                        .HasForeignKey("ModifiedByMemberId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.Navigation("CreatedByAdmin");

                    b.Navigation("CreatedByMember");

                    b.Navigation("Member");

                    b.Navigation("ModifiedByAdmin");

                    b.Navigation("ModifiedByMember");
                });

            modelBuilder.Entity("ParaHockeyApp.Models.Entities.SavedSearch", b =>
                {
                    b.HasOne("ParaHockeyApp.Models.Entities.AdminUser", "CreatedByAdmin")
                        .WithMany()
                        .HasForeignKey("CreatedByAdminId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("ParaHockeyApp.Models.Entities.Member", "CreatedByMember")
                        .WithMany()
                        .HasForeignKey("CreatedByMemberId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("ParaHockeyApp.Models.Entities.AdminUser", "ModifiedByAdmin")
                        .WithMany()
                        .HasForeignKey("ModifiedByAdminId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("ParaHockeyApp.Models.Entities.Member", "ModifiedByMember")
                        .WithMany()
                        .HasForeignKey("ModifiedByMemberId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.Navigation("CreatedByAdmin");

                    b.Navigation("CreatedByMember");

                    b.Navigation("ModifiedByAdmin");

                    b.Navigation("ModifiedByMember");
                });

            modelBuilder.Entity("ParaHockeyApp.Models.Entities.AdminTypeEntity", b =>
                {
                    b.Navigation("AdminUsers");
                });

            modelBuilder.Entity("ParaHockeyApp.Models.Entities.Event", b =>
                {
                    b.Navigation("EventRegistrations");
                });

            modelBuilder.Entity("ParaHockeyApp.Models.Entities.EventCategory", b =>
                {
                    b.Navigation("Events");
                });

            modelBuilder.Entity("ParaHockeyApp.Models.Entities.Member", b =>
                {
                    b.Navigation("MemberLogs");
                });
#pragma warning restore 612, 618
        }
    }
}
