@using ParaHockeyApp.Controllers
@model DuplicateResolutionViewModel
@{
    ViewData["Title"] = @Localizer["Import_ResolveDuplicate"];
}

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1 class="h3 mb-0">@Localizer["Import_ResolveDuplicate"]</h1>
                <a href="@Url.Action("Duplicates", "TempMembers", new { batchId = Model.TempMember.ImportBatchId })" 
                   class="btn btn-outline-secondary">
                    <i class="fas fa-arrow-left"></i> @Localizer["Import_BackToDuplicates"]
                </a>
            </div>

            <div class="alert alert-info">
                <i class="fas fa-info-circle"></i>
                @Localizer["Import_DuplicateResolveInstructions"]
            </div>

            <div class="row">
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header bg-primary text-white">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-file-import"></i> @Localizer["Import_ImportedData"]
                            </h5>
                        </div>
                        <div class="card-body" id="tempMemberData">
                            <dl class="row">
                                <dt class="col-sm-4">@Localizer["Member_FirstName"]:</dt>
                                <dd class="col-sm-8">@Model.TempMember.FirstName</dd>
                                
                                <dt class="col-sm-4">@Localizer["Member_LastName"]:</dt>
                                <dd class="col-sm-8">@Model.TempMember.LastName</dd>
                                
                                <dt class="col-sm-4">@Localizer["Member_Email"]:</dt>
                                <dd class="col-sm-8">@Model.TempMember.Email</dd>
                                
                                <dt class="col-sm-4">@Localizer["Member_Phone"]:</dt>
                                <dd class="col-sm-8">@Model.TempMember.Phone</dd>
                                
                                <dt class="col-sm-4">@Localizer["Member_DateOfBirth"]:</dt>
                                <dd class="col-sm-8">@Model.TempMember.DateOfBirth?.ToString("yyyy-MM-dd")</dd>
                                
                                <dt class="col-sm-4">@Localizer["Member_Address"]:</dt>
                                <dd class="col-sm-8">@Model.TempMember.Address</dd>
                                
                                <dt class="col-sm-4">@Localizer["Member_City"]:</dt>
                                <dd class="col-sm-8">@Model.TempMember.City</dd>
                                
                                <dt class="col-sm-4">@Localizer["Member_PostalCode"]:</dt>
                                <dd class="col-sm-8">@Model.TempMember.PostalCode</dd>
                            </dl>
                        </div>
                    </div>
                </div>

                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header bg-warning text-dark">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-user"></i> @Localizer["Import_ExistingMember"]
                            </h5>
                        </div>
                        <div class="card-body" id="existingMemberData">
                            <dl class="row">
                                <dt class="col-sm-4">@Localizer["Member_FirstName"]:</dt>
                                <dd class="col-sm-8">@Model.ExistingMember.FirstName</dd>
                                
                                <dt class="col-sm-4">@Localizer["Member_LastName"]:</dt>
                                <dd class="col-sm-8">@Model.ExistingMember.LastName</dd>
                                
                                <dt class="col-sm-4">@Localizer["Member_Email"]:</dt>
                                <dd class="col-sm-8">@Model.ExistingMember.Email</dd>
                                
                                <dt class="col-sm-4">@Localizer["Member_Phone"]:</dt>
                                <dd class="col-sm-8">@Model.ExistingMember.Phone</dd>
                                
                                <dt class="col-sm-4">@Localizer["Member_DateOfBirth"]:</dt>
                                <dd class="col-sm-8">@Model.ExistingMember.DateOfBirth.ToString("yyyy-MM-dd")</dd>
                                
                                <dt class="col-sm-4">@Localizer["Member_Address"]:</dt>
                                <dd class="col-sm-8">@Model.ExistingMember.Address</dd>
                                
                                <dt class="col-sm-4">@Localizer["Member_City"]:</dt>
                                <dd class="col-sm-8">@Model.ExistingMember.City</dd>
                                
                                <dt class="col-sm-4">@Localizer["Member_PostalCode"]:</dt>
                                <dd class="col-sm-8">@Model.ExistingMember.PostalCode</dd>
                            </dl>
                        </div>
                    </div>
                </div>
            </div>

            <div class="card mt-4">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-exchange-alt"></i> @Localizer["Import_FieldSelection"]
                    </h5>
                </div>
                <div class="card-body">
                    <div id="fieldComparisons">
                        @foreach (var field in Model.FieldComparisons)
                        {
                            @if (!field.Value.AreIdentical)
                            {
                                <div class="row mb-3 field-comparison" data-field="@field.Key">
                                    <div class="col-md-2">
                                        <strong>@Localizer[$"Member_{field.Key}"]:</strong>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="btn-group w-100" role="group">
                                            <input type="radio" class="btn-check field-choice" 
                                                   name="@field.Key" value="temp" id="@($"{field.Key}_temp")">
                                            <label class="btn btn-outline-primary" for="@($"{field.Key}_temp")">
                                                @Localizer["Import_UseImported"]: "@field.Value.TempValue"
                                            </label>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="btn-group w-100" role="group">
                                            <input type="radio" class="btn-check field-choice" 
                                                   name="@field.Key" value="existing" id="@($"{field.Key}_existing")" checked>
                                            <label class="btn btn-outline-warning" for="@($"{field.Key}_existing")">
                                                @Localizer["Import_KeepExisting"]: "@field.Value.ExistingValue"
                                            </label>
                                        </div>
                                    </div>
                                </div>
                            }
                        }
                    </div>

                    <div class="d-flex justify-content-end mt-4">
                        <button type="button" class="btn btn-success me-2" id="finalizeBtn" disabled>
                            <i class="fas fa-check"></i> @Localizer["Import_Finalize"]
                        </button>
                        <button type="button" class="btn btn-outline-secondary" onclick="history.back()">
                            <i class="fas fa-times"></i> @Localizer["Common_Cancel"]
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Confirmation Modal -->
<div class="modal fade" id="confirmationModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">@Localizer["Import_ConfirmMerge"]</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>@Localizer["Import_ConfirmMergeMessage"]</p>
                <div id="changesList"></div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-success" id="acceptBtn">
                    <i class="fas fa-check"></i> @Localizer["Common_Accept"]
                </button>
                <button type="button" class="btn btn-outline-secondary" data-bs-dismiss="modal">
                    <i class="fas fa-edit"></i> @Localizer["Common_Edit"]
                </button>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script>
        const tempMemberId = '@Model.TempMember.TempMemberId';
        let selectedChoices = {};

        // Enable finalize button when all fields are selected
        function checkCanFinalize() {
            const diffFields = document.querySelectorAll('.field-comparison').length;
            const selectedFields = Object.keys(selectedChoices).length;
            
            document.getElementById('finalizeBtn').disabled = diffFields === 0 || selectedFields < diffFields;
        }

        // Handle field selection
        document.querySelectorAll('.field-choice').forEach(radio => {
            radio.addEventListener('change', function() {
                selectedChoices[this.name] = this.value;
                checkCanFinalize();
            });
        });

        // Handle finalize button
        document.getElementById('finalizeBtn').addEventListener('click', function() {
            fetch('@Url.Action("PreviewMerge", "TempMembers")', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'RequestVerificationToken': $('input[name="__RequestVerificationToken"]').val()
                },
                body: JSON.stringify({ tempMemberId: tempMemberId, fieldChoices: selectedChoices })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showConfirmationModal(data.preview);
                } else {
                    alert('Error: ' + data.error);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('An error occurred while previewing the merge.');
            });
        });

        function showConfirmationModal(preview) {
            const changesList = document.getElementById('changesList');
            
            if (preview.hasChanges) {
                let changesHtml = '<table class="table table-sm"><thead><tr><th>Field</th><th>Current</th><th>New</th></tr></thead><tbody>';
                
                for (const [field, change] of Object.entries(preview.changes)) {
                    if (change.willChange) {
                        changesHtml += `<tr><td><strong>${field}</strong></td><td>${change.currentValue}</td><td class="text-success">${change.newValue}</td></tr>`;
                    }
                }
                
                changesHtml += '</tbody></table>';
                changesList.innerHTML = changesHtml;
            } else {
                changesList.innerHTML = '<p class="text-muted">No changes will be made.</p>';
            }

            new bootstrap.Modal(document.getElementById('confirmationModal')).show();
        }

        // Handle accept button
        document.getElementById('acceptBtn').addEventListener('click', function() {
            fetch('@Url.Action("ConfirmMerge", "TempMembers")', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'RequestVerificationToken': $('input[name="__RequestVerificationToken"]').val()
                },
                body: JSON.stringify({ tempMemberId: tempMemberId, fieldChoices: selectedChoices })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert(data.message);
                    window.location.href = '@Url.Action("Duplicates", "TempMembers", new { batchId = Model.TempMember.ImportBatchId })';
                } else {
                    alert('Error: ' + data.error);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('An error occurred while applying the merge.');
            });
        });

        // Initialize
        checkCanFinalize();
    </script>
}