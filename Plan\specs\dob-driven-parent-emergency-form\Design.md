# Design: DOB-Driven Parent/Emergency Form Logic

## Overview

This design enables dynamic control of the registration flow based on the entered date of birth (DOB), ensuring legal and business compliance for underage and adult members. All logic is configuration-driven and fully localized.

## Architecture & Data Flow

```mermaid
graph TD
    A[User enters DOB] --> B{Is Age < AgeOfMajority?}
    B -- Yes --> C[Show Parent Form]
    B -- No --> D[Show Emergency Contact Form]
    C --> E[On completion, show Membership Type Selection]
    D --> E
    E --> F{Is Age < AgeOfMajority?}
    F -- Yes --> G[All membership types enabled]
    F -- No --> H["Junior" type disabled]
```

**Note:** The display of the Parent or Emergency Contact form is determined solely by the entered date of birth (age), not by the membership type selection. Membership type selection always occurs after the appropriate form is completed.

### Key Interfaces

-   **IRegistrationFlowService**: Determines next form based on DOB and configures membership type availability.
-   **IEnvironmentConfigurationService**: Supplies AgeOfMajority value from configuration.

### UI Components

-   **DOB Entry Step**: Triggers immediate transition to Parent or Emergency Contact form based only on age (not membership type).
-   **Parent Form**: Shown only if under 18, regardless of membership type.
-   **Emergency Contact Form**: Shown only if 18 or older, regardless of membership type.
-   **Membership Type Selection**: Always shown after the above forms; "Junior" is disabled for 18+ with tooltip.

### Localization

-   All new UI text, tooltips, and error messages are added to `SharedResource.resx`.

### Accessibility

-   Disabled membership options use `aria-disabled` and have descriptive tooltips for screen readers.

## Configuration

-   `appsettings.json`:
    ```json
    "Registration": {
      "AgeOfMajority": 18
    }
    ```
-   `EnvironmentSettings.cs`:
    ```csharp
    public int AgeOfMajority { get; set; } = 18;
    ```

## Service Layer Logic

```csharp
public class RegistrationFlowService : IRegistrationFlowService {
    private readonly IEnvironmentConfigurationService _envConfig;
    public RegistrationFlowService(IEnvironmentConfigurationService envConfig) {
        _envConfig = envConfig;
    }
    public bool IsUnderAge(DateTime dob) => CalculateAge(dob) < _envConfig.AgeOfMajority;
    public bool IsMembershipTypeAllowed(string type, DateTime dob) {
        if (type == "Junior" && !IsUnderAge(dob)) return false;
        return true;
    }
    // ...
}
```

## Traceability Matrix

| Requirement    | Design Element                                        |
| -------------- | ----------------------------------------------------- |
| FR1.1, FR1.2   | RegistrationFlowService, UI flow, Mermaid diagram     |
| FR1.3          | UI step order, RegistrationFlowService                |
| FR2.1, FR2.2   | Membership Type Selection UI, RegistrationFlowService |
| FR3.1, FR3.2   | UI transitions, tooltips, localization                |
| NFR1.1         | SharedResource.resx, localization pattern             |
| NFR2.1         | ARIA attributes, tooltips                             |
| NFR3.1, NFR3.2 | Service layer, config-driven age                      |

## Diagrams

See Mermaid diagram above for flow.

## Extensibility

-   Age of majority can be changed via configuration without code changes.
-   Additional membership restrictions can be added in the service layer.

## Error Handling

-   If DOB is missing or invalid, show localized error and do not proceed.
-   If configuration is missing, fallback to default age (18) and log a warning.
