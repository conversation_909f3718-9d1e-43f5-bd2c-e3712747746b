namespace ParaHockeyApp.Models.Enums
{
    /// <summary>
    /// WCAG compliance levels
    /// </summary>
    public enum WcagComplianceLevel
    {
        NonCompliant = 0,
        PartiallyCompliant = 1,
        AA_Compliant = 2,
        AAA_Compliant = 3
    }

    /// <summary>
    /// WCAG criterion levels
    /// </summary>
    public enum WcagLevel
    {
        A = 1,
        AA = 2,
        AAA = 3
    }

    /// <summary>
    /// Impact level of accessibility issues
    /// </summary>
    public enum AccessibilityImpact
    {
        Minor = 1,
        Moderate = 2,
        Serious = 3,
        Critical = 4
    }

    /// <summary>
    /// Types of accessibility fixes that can be applied
    /// </summary>
    [Flags]
    public enum AccessibilityFixType
    {
        None = 0,
        AltText = 1,
        FormLabels = 2,
        HeadingStructure = 4,
        Landmarks = 8,
        AriaAttributes = 16,
        ColorContrast = 32,
        FocusManagement = 64,
        All = AltText | FormLabels | HeadingStructure | Landmarks | AriaAttributes | ColorContrast | FocusManagement
    }
}