@using Microsoft.Extensions.Options
@using Microsoft.Extensions.Localization
@using Microsoft.AspNetCore.Mvc.Localization
@inject IOptions<ParaHockeyApp.Models.Configuration.EnvironmentSettings> EnvSettings
@inject IHtmlLocalizer<ParaHockeyApp.Resources.SharedResourceMarker> SharedLocalizer

<!DOCTYPE html>
<html lang="@System.Globalization.CultureInfo.CurrentCulture.TwoLetterISOLanguageName">

<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <meta name="color-scheme" content="light dark">
    <title>@ViewData["Title"] - Parahockey</title>

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">

    <!-- Critical CSS (inlined for performance) -->
    <style data-critical="true">
        /* Critical above-the-fold styles will be inlined here */
        body { margin: 0; font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif; }
        .container { max-width: 1200px; margin: 0 auto; padding: 0 15px; }
        .navbar { background: #fff; border-bottom: 1px solid #dee2e6; padding: 0.5rem 0; }
        .btn { display: inline-block; padding: 0.375rem 0.75rem; border: 1px solid transparent; border-radius: 0.25rem; }
        .btn-primary { background-color: #007bff; border-color: #007bff; color: #fff; }
    </style>

    <!-- Bootstrap CSS (preloaded) -->
    <link rel="preload" href="~/lib/bootstrap/dist/css/bootstrap.min.css" as="style" onload="this.onload=null;this.rel='stylesheet'">
    <noscript><link rel="stylesheet" href="~/lib/bootstrap/dist/css/bootstrap.min.css"></noscript>
    
    <!-- Design System CSS (deferred) -->
    <link rel="preload" href="~/css/shared/variables.css" as="style" onload="this.onload=null;this.rel='stylesheet'" asp-append-version="true" />
    <link rel="preload" href="~/css/forced-colors.css" as="style" onload="this.onload=null;this.rel='stylesheet'" asp-append-version="true" />
    <link rel="preload" href="~/css/site.css" as="style" onload="this.onload=null;this.rel='stylesheet'" asp-append-version="true" />
    <link rel="preload" href="~/css/dark-overrides.css" as="style" onload="this.onload=null;this.rel='stylesheet'" asp-append-version="true" />

    <!-- Environment-specific CSS -->
    @if (EnvSettings.Value.IsTest)
    {
        <link rel="stylesheet" href="~/css/environment-test.css" asp-append-version="true" />
    }
    else if (EnvSettings.Value.IsProduction)
    {
        <link rel="stylesheet" href="~/css/environment-prod.css" asp-append-version="true" />
    }

    <!-- Hockey-themed favicon -->
    <link rel="shortcut icon" href="~/favicon.ico" />
    
    <!-- Global authentication state for JavaScript -->
    <script>
        // CRITICAL: Always get fresh authentication state on page load
        window.isAuthenticated = @Json.Serialize(User.Identity.IsAuthenticated);
        
        // DEBUGGING: Log detailed authentication state from server
        console.log('🔍 SERVER AUTHENTICATION STATE:');
        console.log('   User.Identity.IsAuthenticated:', @Json.Serialize(User.Identity.IsAuthenticated));
        console.log('   User.Identity.Name:', @Json.Serialize(User.Identity.Name ?? "NULL"));
        console.log('   User.Identity.AuthenticationType:', @Json.Serialize(User.Identity.AuthenticationType ?? "NULL"));
        console.log('   Request.Cookies Count:', @Json.Serialize(Context.Request.Cookies.Count));
        console.log('   Session IsAvailable:', @Json.Serialize(Context.Session.IsAvailable));
        
        // Log current cookies for debugging
        console.log('🍪 CURRENT COOKIES:');
        document.cookie.split(';').forEach(function(cookie) {
            console.log('   Cookie:', cookie.trim());
        });
        
        // Clear any cached authentication state on EVERY page load to prevent stale state
        console.log('🔄 Clearing cached authentication state on page load...');
        sessionStorage.clear();
        localStorage.removeItem('userContext');
        localStorage.removeItem('adminSession');
        localStorage.removeItem('memberSession');
        localStorage.removeItem('modalUserState');
        localStorage.removeItem('authenticationState');
        localStorage.removeItem('cachedUserState');
        
        // Clear browser cache for authentication-related requests
        if ('caches' in window) {
            caches.keys().then(function(names) {
                for (let name of names) {
                    if (name.includes('event') || name.includes('user') || name.includes('auth')) {
                        caches.delete(name);
                    }
                }
            });
        }
        
        console.log('✅ Fresh authentication state loaded:', window.isAuthenticated);
    </script>
</head>

<body class="@(User.Identity.IsAuthenticated ? "authenticated" : "anonymous")">
    <!-- Environment Indicator -->
    <partial name="_EnvironmentIndicator" />

    <header>
        <nav class="navbar navbar-expand-lg navbar-light bg-white border-bottom box-shadow mb-3">
            <div class="container-fluid">
                <a class="navbar-brand" asp-area="" asp-controller="Home" asp-action="Index">
                    <img src="~/assets/logos/parahockey-01.jpg" alt="Parahockey Logo"
                        style="height: 40px; border-radius: 50%;">
                    Parahockey
                </a>
                <button class="navbar-toggler" type="button" data-bs-toggle="collapse"
                    data-bs-target="#navbarSupportedContent" aria-controls="navbarSupportedContent"
                    aria-expanded="false" aria-label="Toggle navigation">
                    <span class="navbar-toggler-icon"></span>
                </button>
                <div class="collapse navbar-collapse" id="navbarSupportedContent">
                    <ul class="navbar-nav me-auto mb-2 mb-lg-0">
                        <li class="nav-item">
                            <a class="nav-link text-dark" asp-area="" asp-controller="Home"
                                asp-action="Index">@SharedLocalizer["Home"]</a>
                        </li>
                    </ul>

                    <ul class="navbar-nav ms-auto mb-2 mb-lg-0 align-items-center">
                        @{
                            var memberSessionData = Context.Session.GetString("MemberSessionData");
                            var hasMemberSession = false;
                            string memberFirstName = "";
                            
                            if (!string.IsNullOrEmpty(memberSessionData))
                            {
                                try
                                {
                                    var memberSession = System.Text.Json.JsonSerializer.Deserialize<ParaHockeyApp.Models.Session.MemberSession>(memberSessionData);
                                    // CRITICAL: Check if session is still valid (not expired)
                                    if (memberSession != null && memberSession.IsValidSession())
                                    {
                                        hasMemberSession = true;
                                        memberFirstName = memberSession.MemberName?.Split(' ').FirstOrDefault() ?? "";
                                    }
                                    else
                                    {
                                        // Session expired - clear it
                                        Context.Session.Remove("MemberSessionData");
                                        hasMemberSession = false;
                                    }
                                }
                                catch
                                {
                                    // If deserialization fails, clear the session
                                    Context.Session.Remove("MemberSessionData");
                                    hasMemberSession = false;
                                }
                            }
                        }
                        
                        @if (hasMemberSession)
                        {
                            <!-- Member Navigation -->
                            <li class="nav-item dropdown">
                                <a class="nav-link dropdown-toggle text-primary fw-bold" href="#" id="memberDropdown" role="button"
                                    data-bs-toggle="dropdown" aria-expanded="false">
                                    <i class="fas fa-user-circle me-1"></i>@(string.IsNullOrEmpty(memberFirstName) ? SharedLocalizer["MemberPortal"] : $"Bonjour {memberFirstName}")
                                </a>
                                <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="memberDropdown">
                                    <li><a class="dropdown-item" asp-controller="Members" asp-action="Dashboard">
                                        <i class="fas fa-tachometer-alt me-2"></i>@SharedLocalizer["MyDashboard"]
                                    </a></li>
                                    <li><a class="dropdown-item" asp-controller="Events" asp-action="Subscribe">
                                        <i class="fas fa-calendar-plus me-2"></i>@SharedLocalizer["SubscribeToEvents"]
                                    </a></li>
                                    <li><a class="dropdown-item" asp-controller="Members" asp-action="CalendarReadOnly">
                                        <i class="fas fa-calendar me-2"></i>@SharedLocalizer["ViewCalendar"]
                                    </a></li>
                                    <li><hr class="dropdown-divider"></li>
                                    <li><a class="dropdown-item" asp-controller="Members" asp-action="Logout">
                                        <i class="fas fa-sign-out-alt me-2"></i>@SharedLocalizer["Logout"]
                                    </a></li>
                                </ul>
                            </li>
                        }
                        else
                        {
                            <!-- Guest Navigation -->
                            <li class="nav-item">
                                <a class="nav-link text-dark" asp-controller="Members" asp-action="Login">
                                    <i class="fas fa-sign-in-alt me-1"></i>@SharedLocalizer["MemberLogin"]
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link text-dark" asp-controller="Members" asp-action="Register">
                                    <i class="fas fa-user-plus me-1"></i>@SharedLocalizer["MemberRegistration"]
                                </a>
                            </li>
                        }
                        
                        <li class="nav-item">
                            <a class="nav-link text-dark" asp-area="" asp-controller="Admin" asp-action="Index">@SharedLocalizer["Administration"]</a>
                        </li>
                        @if (User.Identity?.IsAuthenticated == true && !hasMemberSession)
                        {
                            <!-- Admin logout - ONLY show when admin is authenticated AND no member session -->
                            <li class="nav-item">
                                <a class="nav-link text-dark" asp-area="" asp-controller="Home" asp-action="Logout">
                                    <i class="fas fa-sign-out-alt me-1"></i>@SharedLocalizer["Logout"]
                                </a>
                            </li>
                        }
                        <li class="nav-item dropdown">
                            <a class="nav-link dropdown-toggle" href="#" id="languageDropdown" role="button"
                                data-bs-toggle="dropdown" aria-expanded="false">
                                @(System.Globalization.CultureInfo.CurrentCulture.TwoLetterISOLanguageName == "fr" ?
                                                                "Français" : "English")
                            </a>
                            <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="languageDropdown">
                                <li><a class="dropdown-item"
                                        href="@Url.Action("Switch", "Language", new { culture = "fr-CA", returnUrl = Context.Request.Path + Context.Request.QueryString })">Français</a>
                                </li>
                                <li><a class="dropdown-item"
                                        href="@Url.Action("Switch", "Language", new { culture = "en-CA", returnUrl = Context.Request.Path + Context.Request.QueryString })">English</a>
                                </li>
                            </ul>
                        </li>
                    </ul>
                </div>
            </div>
        </nav>
    </header>

    <div class="container">
        <!-- Development Tools -->
        <partial name="_DevelopmentTools" />
        
        <main role="main" class="pb-3">
            @RenderBody()
        </main>
    </div>

    <footer class="border-top footer text-muted mt-5">
        <div class="container">
            <div class="row">
                <div class="col-md-6 col-12 text-center text-md-start">
                    &copy; @DateTime.Now.Year - Parahockey -
                    <a asp-controller="Home" asp-action="Privacy">@SharedLocalizer["Privacy"]</a>
                </div>
                <div class="col-md-6 col-12 text-center text-md-end">
                    @SharedLocalizer["PoweredBy"] Parahockey @DateTime.Now.Year
                </div>
            </div>
        </div>
    </footer>

    <!-- Performance Optimization Scripts -->
    <script src="~/js/bundle-loader.js" asp-append-version="true"></script>
    <script src="~/js/image-optimization.js" asp-append-version="true"></script>
    <script src="~/js/critical-css.js" asp-append-version="true"></script>

    <!-- Bootstrap JavaScript (loaded via bundle loader) -->
    <script>
        // Initialize page-specific bundles
        document.addEventListener('DOMContentLoaded', function() {
            if (window.loadPageBundles) {
                const pageName = '@ViewContext.RouteData.Values["action"]' || 'home';
                window.loadPageBundles(pageName.toLowerCase());
            }
        });
    </script>
    <script src="~/lib/jquery/dist/jquery.min.js"></script>
    
    <!-- JavaScript Localization -->
    @{
        var formResetConfirmText = SharedLocalizer["JsFormResetConfirm"].Value ?? "";
        /* ✅ Pass a dummy token so String.Format succeeds
              The token is easy to replace in JavaScript later. */
        var verificationCodeMessageText = SharedLocalizer["JsVerificationCodeMessage", "{CODE}"].Value ?? "";
        var errorSendingCodeText = SharedLocalizer["JsErrorSendingCode"].Value ?? "";
    }
    <script>
        window.ParaHockeyLocalizations = {
            formResetConfirm: '@Html.Raw(formResetConfirmText.Replace("'", "\\'").Replace("\"", "\\\""))',
            verificationCodeMessage: '@Html.Raw(verificationCodeMessageText.Replace("'", "\\'").Replace("\"", "\\\""))',
            errorSendingCode: '@Html.Raw(errorSendingCodeText.Replace("'", "\\'").Replace("\"", "\\\""))'
        };
    </script>
    <script>
        console.log('jQuery loaded in _Layout:', typeof jQuery !== 'undefined');
    </script>
    <script src="https://code.jquery.com/ui/1.13.3/jquery-ui.min.js"></script>
    <script>
        console.log('jQuery UI loaded in _Layout:', typeof jQuery.ui !== 'undefined');
        console.log('jQuery.datepicker loaded in _Layout:', typeof jQuery.datepicker !== 'undefined');
    </script>
    <script src="~/js/datepicker-fr.js"></script>
    <script>
        console.log('datepicker-fr.js loaded in _Layout. $.datepicker.regional.fr:', typeof jQuery.datepicker !== 'undefined' && typeof jQuery.datepicker.regional !== 'undefined' && typeof jQuery.datepicker.regional.fr !== 'undefined');
    </script>
    <!-- jQuery Mask Plugin -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery.mask/1.14.16/jquery.mask.min.js"></script>
    <script src="~/lib/bootstrap/dist/js/bootstrap.bundle.min.js"></script>
    <script src="~/js/site.js" asp-append-version="true"></script>
    <!-- Theme Listener for Dark Mode Support -->
    <script src="~/js/theme-listener.js" asp-append-version="true"></script>

    @await RenderSectionAsync("Scripts", required: false)
</body>

</html>