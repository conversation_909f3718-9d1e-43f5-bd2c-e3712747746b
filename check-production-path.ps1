Write-Host "Checking IIS Website Configuration..."
Import-Module WebAdministration -ErrorAction SilentlyContinue

try {
    $websites = Get-Website | Where-Object { $_.Name -like "*ParaHockey*" }
    foreach ($site in $websites) {
        Write-Host "Site: $($site.Name) -> Path: $($site.PhysicalPath)" -ForegroundColor Cyan
        Write-Host "State: $($site.State)" -ForegroundColor Yellow
        
        if (Test-Path $site.PhysicalPath) {
            Write-Host "✅ Directory exists" -ForegroundColor Green
            $files = Get-ChildItem $site.PhysicalPath -File | Select-Object -First 5
            Write-Host "Sample files:" -ForegroundColor Gray
            foreach ($file in $files) {
                Write-Host "  - $($file.Name)" -ForegroundColor Gray
            }
        } else {
            Write-Host "❌ Directory does not exist!" -ForegroundColor Red
        }
        Write-Host ""
    }
} catch {
    Write-Host "Error: $_" -ForegroundColor Red
}