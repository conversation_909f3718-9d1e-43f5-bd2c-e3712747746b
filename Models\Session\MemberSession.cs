using System;

namespace ParaHockeyApp.Models.Session
{
    /// <summary>
    /// Represents a member session with validation state tracking
    /// </summary>
    public class MemberSession
    {
        public int MemberId { get; set; }
        public string MemberName { get; set; } = string.Empty;
        public string Email { get; set; } = string.Empty;
        public DateTime ValidationTime { get; set; }
        public DateTime ExpiryTime { get; set; }
        public string SessionToken { get; set; } = string.Empty;
        
        /// <summary>
        /// Indicates if the session is still active (not expired)
        /// </summary>
        public bool IsActive => DateTime.Now < ExpiryTime;
        
        /// <summary>
        /// Creates a new member session with default expiry time (30 minutes)
        /// </summary>
        public static MemberSession Create(int memberId, string memberName, string email)
        {
            return new MemberSession
            {
                MemberId = memberId,
                MemberName = memberName,
                Email = email,
                ValidationTime = DateTime.Now,
                ExpiryTime = DateTime.Now.AddMinutes(30), // 30 minute session
                SessionToken = Guid.NewGuid().ToString("N")
            };
        }
        
        /// <summary>
        /// Refreshes the session expiry time
        /// </summary>
        public void RefreshSession()
        {
            ExpiryTime = DateTime.Now.AddMinutes(30);
        }
        
        /// <summary>
        /// Validates if the session is still valid and not expired
        /// </summary>
        public bool IsValidSession()
        {
            return IsActive && !string.IsNullOrEmpty(SessionToken);
        }
    }
}