@using Microsoft.AspNetCore.Mvc.Localization
@inject IHtmlLocalizer<ParaHockeyApp.Resources.SharedResourceMarker> SharedLocalizer
@{
    ViewData["Title"] = SharedLocalizer["EventCalendar"];
    var memberSession = ViewBag.MemberSession as ParaHockeyApp.Models.Session.MemberSession;
}

<div class="container-fluid mt-4">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col">
            <h1 class="h2 text-primary">
                <i class="fas fa-calendar-alt"></i> @SharedLocalizer["EventCalendar"]
            </h1>
            <p class="text-muted">@SharedLocalizer["Welcome"], @memberSession?.MemberName! @SharedLocalizer["ViewEventsAndSchedule"]</p>
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item">@SharedLocalizer["MemberPortal"]</li>
                    <li class="breadcrumb-item"><a href="@Url.Action("Options", "Members")">@SharedLocalizer["Dashboard"]</a></li>
                    <li class="breadcrumb-item active" aria-current="page">@SharedLocalizer["Calendar"]</li>
                </ol>
            </nav>
        </div>
    </div>

    <!-- Navigation -->
    <div class="row mb-4">
        <div class="col">
            <a href="@Url.Action("Options", "Members")" class="btn btn-outline-primary">
                <i class="fas fa-arrow-left"></i> @SharedLocalizer["BackToDashboard"]
            </a>
        </div>
    </div>

    <div class="row mb-4">
        <!-- Calendar Controls -->
        <div class="col-md-8">
            <div class="card">
                <!-- Desktop Header -->
                <div class="card-header d-flex justify-content-between align-items-center calendar-desktop-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-calendar"></i> @SharedLocalizer["EventCalendar"]
                    </h5>
                    <div>
                        <!-- Read-only filter only -->
                        <div class="dropdown d-inline-block">
                            <button class="btn btn-outline-secondary btn-sm dropdown-toggle" type="button" id="filterDropdown" data-bs-toggle="dropdown">
                                <i class="fas fa-filter"></i> @SharedLocalizer["Filter"]
                            </button>
                            <ul class="dropdown-menu" aria-labelledby="filterDropdown">
                                <li><a class="dropdown-item" href="#" data-filter="all">@SharedLocalizer["AllEvents"]</a></li>
                                @if (ViewBag.EventCategories != null)
                                {
                                    @foreach (var category in ViewBag.EventCategories)
                                    {
                                        <li><a class="dropdown-item" href="#" data-filter="@category.Id">@SharedLocalizer[category.DisplayNameKey]</a></li>
                                    }
                                }
                            </ul>
                        </div>
                    </div>
                </div>

                <!-- Mobile Header -->
                <div class="card-header calendar-mobile-header">
                    <div class="d-flex justify-content-between align-items-center mb-2">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-calendar"></i> @SharedLocalizer["EventCalendar"]
                        </h5>
                    </div>
                    <div class="d-flex gap-2 flex-wrap">
                        <!-- Mobile Filter -->
                        <div class="dropdown">
                            <button class="btn btn-outline-secondary btn-sm dropdown-toggle" type="button" data-bs-toggle="dropdown">
                                <i class="fas fa-filter"></i> @SharedLocalizer["Filter"]
                            </button>
                            <ul class="dropdown-menu">
                                <li><a class="dropdown-item" href="#" data-filter="all">@SharedLocalizer["AllEvents"]</a></li>
                                @if (ViewBag.EventCategories != null)
                                {
                                    @foreach (var category in ViewBag.EventCategories)
                                    {
                                        <li><a class="dropdown-item" href="#" data-filter="@category.Id">@SharedLocalizer[category.DisplayNameKey]</a></li>
                                    }
                                }
                            </ul>
                        </div>
                    </div>
                </div>

                <div class="card-body p-0">
                    <!-- Calendar Container -->
                    <div id="calendar"></div>
                </div>
            </div>
        </div>

        <!-- Sidebar -->
        <div class="col-md-4">
            <!-- Event Statistics Card -->
            <div class="card mb-4">
                <div class="card-header bg-info text-white">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-chart-bar"></i> @SharedLocalizer["EventStatistics"]
                    </h5>
                </div>
                <div class="card-body">
                    <div class="text-center">
                        <div class="mb-3">
                            <h4 class="text-primary">@ViewBag.PublishedEvents</h4>
                            <p class="text-muted mb-0">@SharedLocalizer["PublishedEvents"]</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Upcoming Events Card -->
            <div class="card mb-4">
                <div class="card-header bg-warning text-dark">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-clock"></i> @SharedLocalizer["UpcomingEvents"]
                    </h5>
                </div>
                <div class="card-body">
                    @if (ViewBag.UpcomingEvents != null && ((List<ParaHockeyApp.Models.Entities.Event>)ViewBag.UpcomingEvents).Any())
                    {
                        @foreach (var evt in (List<ParaHockeyApp.Models.Entities.Event>)ViewBag.UpcomingEvents)
                        {
                            <div class="mb-3 pb-3 border-bottom">
                                <h6 class="mb-1">@evt.Title</h6>
                                <small class="text-muted">
                                    <i class="fas fa-calendar-day"></i> @evt.StartDate.ToString("MMM dd, yyyy")
                                    @if (!evt.IsAllDay)
                                    {
                                        <br><i class="fas fa-clock"></i> @evt.StartDate.ToString("HH:mm")
                                    }
                                    @if (!string.IsNullOrEmpty(evt.Location))
                                    {
                                        <br><i class="fas fa-map-marker-alt"></i> @evt.Location
                                    }
                                </small>
                            </div>
                        }
                    }
                    else
                    {
                        var upcomingEmptyState = ViewBag.UpcomingEventsEmptyState as ParaHockeyApp.Models.ViewModels.EmptyStateViewModel;
                        @if (upcomingEmptyState != null)
                        {
                            <div class="text-center py-3">
                                <i class="@upcomingEmptyState.IconClass fa-2x mb-3"></i>
                                <h6 class="text-muted">@upcomingEmptyState.Title</h6>
                                <p class="text-muted small">@upcomingEmptyState.Message</p>
                            </div>
                        }
                        else
                        {
                            <p class="text-muted text-center">@SharedLocalizer["NoUpcomingEvents"]</p>
                        }
                    }
                </div>
            </div>

            <!-- Recent Events Card -->
            <div class="card">
                <div class="card-header bg-secondary text-white">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-history"></i> @SharedLocalizer["RecentEvents"]
                    </h5>
                </div>
                <div class="card-body">
                    @if (ViewBag.RecentEvents != null && ((List<ParaHockeyApp.Models.Entities.Event>)ViewBag.RecentEvents).Any())
                    {
                        @foreach (var evt in (List<ParaHockeyApp.Models.Entities.Event>)ViewBag.RecentEvents)
                        {
                            <div class="mb-3 pb-3 border-bottom">
                                <h6 class="mb-1">@evt.Title</h6>
                                <small class="text-muted">
                                    <i class="fas fa-calendar-day"></i> @evt.StartDate.ToString("MMM dd, yyyy")
                                    @if (!evt.IsAllDay)
                                    {
                                        <br><i class="fas fa-clock"></i> @evt.StartDate.ToString("HH:mm")
                                    }
                                    @if (!string.IsNullOrEmpty(evt.Location))
                                    {
                                        <br><i class="fas fa-map-marker-alt"></i> @evt.Location
                                    }
                                </small>
                            </div>
                        }
                    }
                    else
                    {
                        var recentEmptyState = ViewBag.RecentEventsEmptyState as ParaHockeyApp.Models.ViewModels.EmptyStateViewModel;
                        @if (recentEmptyState != null)
                        {
                            <div class="text-center py-3">
                                <i class="@recentEmptyState.IconClass fa-2x mb-3"></i>
                                <h6 class="text-muted">@recentEmptyState.Title</h6>
                                <p class="text-muted small">@recentEmptyState.Message</p>
                            </div>
                        }
                        else
                        {
                            <p class="text-muted text-center">@SharedLocalizer["NoRecentEvents"]</p>
                        }
                    }
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Include shared Event Details Modal -->
@await Html.PartialAsync("_EventDetailsModal")

@section Scripts {
    <!-- FullCalendar CSS and JS -->
    <link href="https://cdn.jsdelivr.net/npm/fullcalendar@6.1.8/index.global.min.css" rel="stylesheet" />
    <script src="https://cdn.jsdelivr.net/npm/fullcalendar@6.1.8/index.global.min.js"></script>
    
    <!-- Event Details Modal JS -->
    <script src="~/js/event-details-modal.js"></script>

    <script>
        // Authentication flag for members
        const isAuthenticated = true; // Members are always authenticated
        
        document.addEventListener('DOMContentLoaded', function() {
            var calendarEl = document.getElementById('calendar');
            var calendar = new FullCalendar.Calendar(calendarEl, {
                initialView: 'dayGridMonth',
                headerToolbar: {
                    left: 'prev,next today',
                    center: 'title',
                    right: 'dayGridMonth,timeGridWeek,timeGridDay'
                },
                events: {
                    url: '@Url.Action("GetMemberCalendarEvents", "Members")',
                    method: 'GET',
                    failure: function() {
                        alert('@SharedLocalizer["ErrorLoadingEvents"]');
                    }
                },
                eventClick: function(info) {
                    // Prevent navigation
                    info.jsEvent.preventDefault();
                    
                    // Show event details modal using shared function
                    showEventDetailsModal(info.event.id, isAuthenticated, {
                        calendarType: 'members',
                        detailsUrl: '@Url.Action("GetPublicEventDetails", "Home")',
                        joinUrl: '@Url.Action("Join", "Events")',
                        leaveUrl: '@Url.Action("Leave", "Events")'
                    });
                },
                height: 'auto',
                contentHeight: 500,
                aspectRatio: 1.35,
                eventDisplay: 'block',
                displayEventTime: true,
                eventTimeFormat: {
                    hour: '2-digit',
                    minute: '2-digit',
                    meridiem: false
                },
                locale: '@(System.Globalization.CultureInfo.CurrentCulture.TwoLetterISOLanguageName)'
            });

            calendar.render();

            // Filter functionality
            document.querySelectorAll('[data-filter]').forEach(function(filterBtn) {
                filterBtn.addEventListener('click', function(e) {
                    e.preventDefault();
                    var filter = this.dataset.filter;
                    
                    // Remove existing filter sources
                    calendar.getEventSources().forEach(function(eventSource) {
                        eventSource.remove();
                    });
                    
                    // Add new event source with filter
                    var url = '@Url.Action("GetMemberCalendarEvents", "Members")';
                    if (filter !== 'all') {
                        url += '?categoryId=' + filter;
                    }
                    
                    calendar.addEventSource({
                        url: url,
                        method: 'GET',
                        failure: function() {
                            alert('@SharedLocalizer["ErrorLoadingEvents"]');
                        }
                    });
                });
            });
            
            // Add click handlers for upcoming and recent events
            document.querySelectorAll('.upcoming-event-item, .recent-event-item').forEach(function(item) {
                item.addEventListener('click', function() {
                    var eventId = this.dataset.eventId;
                    if (eventId) {
                        showEventDetailsModal(eventId, isAuthenticated, {
                            calendarType: 'members',
                            detailsUrl: '@Url.Action("GetPublicEventDetails", "Home")',
                            joinUrl: '@Url.Action("Join", "Events")',
                            leaveUrl: '@Url.Action("Leave", "Events")'
                        });
                    }
                });
                
                // Handle keyboard navigation
                item.addEventListener('keypress', function(e) {
                    if (e.key === 'Enter' || e.key === ' ') {
                        e.preventDefault();
                        var eventId = this.dataset.eventId;
                        if (eventId) {
                            showEventDetailsModal(eventId, isAuthenticated, {
                                detailsUrl: '@Url.Action("GetPublicEventDetails", "Home")',
                                joinUrl: '@Url.Action("Join", "Events")',
                                leaveUrl: '@Url.Action("Leave", "Events")'
                            });
                        }
                    }
                });
            });

        });
    </script>
}

<!-- Custom Styles -->
<style>
    .calendar-desktop-header {
        display: block;
    }
    
    .calendar-mobile-header {
        display: none;
    }
    
    @@media (max-width: 768px) {
        .calendar-desktop-header {
            display: none;
        }
        
        .calendar-mobile-header {
            display: block;
        }
    }
    
    #calendar {
        min-height: 500px;
    }
    
    .fc-event {
        cursor: pointer;
    }
    
    .fc-event:hover {
        opacity: 0.8;
    }
    
    .card-title {
        font-weight: 600;
    }
</style>