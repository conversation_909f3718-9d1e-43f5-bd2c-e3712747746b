using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using ParaHockeyApp.Services;
using ParaHockeyApp.ViewModels;
using ParaHockeyApp.Controllers;
using ParaHockeyApp.Attributes;
using ParaHockeyApp.DTOs;

namespace ParaHockeyApp.Controllers
{
    /// <summary>
    /// Controller for member import operations from Excel files
    /// </summary>
    [AdminAuthorize]
    public class ImportController : Controller
    {
        private readonly IMemberImportService _memberImportService;
        private readonly IImportBatchService _importBatchService;
        private readonly IUserContextService _userContextService;

        public ImportController(
            IMemberImportService memberImportService,
            IImportBatchService importBatchService,
            IUserContextService userContextService)
        {
            _memberImportService = memberImportService;
            _importBatchService = importBatchService;
            _userContextService = userContextService;
        }

        /// <summary>
        /// GET: Upload page for member import
        /// </summary>
        public IActionResult Upload()
        {
            var viewModel = new MemberImportUploadViewModel();
            return View(viewModel);
        }

        /// <summary>
        /// POST: Process uploaded Excel file
        /// </summary>
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Upload(MemberImportUploadViewModel model)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return View(model);
                }

                if (model.ExcelFile == null || model.ExcelFile.Length == 0)
                {
                    ModelState.AddModelError("ExcelFile", "Please select an Excel file to upload.");
                    return View(model);
                }

                // Validate file
                var validationResult = await _memberImportService.ValidateFileAsync(model.ExcelFile);
                if (!validationResult.IsValid)
                {
                    foreach (var error in validationResult.Errors)
                    {
                        ModelState.AddModelError("ExcelFile", error);
                    }
                    return View(model);
                }

                // Get current user
                var currentUser = _userContextService.GetCurrentUser()?.UserName ?? "Unknown";

                // Parse and stage the file
                var batchId = await _memberImportService.ParseAndStageAsync(model.ExcelFile, currentUser);

                // Redirect to batch summary
                return RedirectToAction("BatchSummary", new { id = batchId });
            }
            catch (Exception ex)
            {
                ModelState.AddModelError("", $"An error occurred while processing the file: {ex.Message}");
                return View(model);
            }
        }

        /// <summary>
        /// GET: Display batch summary after upload
        /// </summary>
        public async Task<IActionResult> BatchSummary(int id)
        {
            try
            {
                var batchSummary = await _importBatchService.GetBatchSummaryAsync(id);
                var queueStatuses = await _importBatchService.GetQueueStatusesAsync(id);

                var viewModel = new ImportBatchSummaryViewModel
                {
                    BatchSummary = batchSummary,
                    QueueStatuses = queueStatuses
                };

                return View(viewModel);
            }
            catch (ArgumentException)
            {
                return NotFound($"Import batch {id} not found.");
            }
            catch (Exception ex)
            {
                return View("Error", new ErrorViewModel 
                { 
                    Message = $"An error occurred while loading the batch summary: {ex.Message}" 
                });
            }
        }

        /// <summary>
        /// GET: List all import batches with filtering and pagination
        /// </summary>
        public async Task<IActionResult> History(int page = 1, int pageSize = 20, string? status = null, string? search = null, DateTime? fromDate = null, DateTime? toDate = null)
        {
            try
            {
                var batches = await _importBatchService.GetImportBatchesAsync(page, pageSize, status, search, fromDate, toDate);
                return View(batches);
            }
            catch (Exception ex)
            {
                return View("Error", new ErrorViewModel 
                { 
                    Message = $"An error occurred while loading the import history: {ex.Message}" 
                });
            }
        }

        /// <summary>
        /// GET: Import progress for real-time updates (AJAX endpoint)
        /// </summary>
        [HttpGet]
        public async Task<IActionResult> Progress(int id)
        {
            try
            {
                var progress = await _importBatchService.GetImportProgressAsync(id);
                return Json(progress);
            }
            catch (ArgumentException)
            {
                return NotFound();
            }
            catch (Exception)
            {
                return StatusCode(500, new { error = "Unable to get import progress" });
            }
        }

        /// <summary>
        /// POST: Delete an import batch
        /// </summary>
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> DeleteBatch(int id)
        {
            try
            {
                var currentUser = _userContextService.GetCurrentUser()?.UserName ?? "Unknown";
                var deleted = await _importBatchService.DeleteImportBatchAsync(id, currentUser);

                if (!deleted)
                {
                    TempData["ErrorMessage"] = "Import batch not found or could not be deleted.";
                }
                else
                {
                    TempData["SuccessMessage"] = "Import batch deleted successfully.";
                }

                return RedirectToAction("History");
            }
            catch (InvalidOperationException ex)
            {
                TempData["ErrorMessage"] = ex.Message;
                return RedirectToAction("History");
            }
            catch (Exception ex)
            {
                TempData["ErrorMessage"] = $"An error occurred while deleting the batch: {ex.Message}";
                return RedirectToAction("History");
            }
        }

        /// <summary>
        /// GET: Multi-file upload page for member import with relationships
        /// </summary>
        public IActionResult MultiFileUpload()
        {
            var viewModel = new MultiFileImportRequest();
            return View(viewModel);
        }

        /// <summary>
        /// POST: Process multi-file upload with relationship matching
        /// </summary>
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> MultiFileUpload(MultiFileImportRequest model)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return View(model);
                }

                // Validate that required files are provided
                if (model.MemberFile == null || model.MemberFile.Length == 0)
                {
                    ModelState.AddModelError("MemberFile", "Member file is required.");
                    return View(model);
                }

                if (model.ParentFile == null && model.EmergencyContactFile == null)
                {
                    ModelState.AddModelError("", "At least one relationship file (parent or emergency contact) must be provided.");
                    return View(model);
                }

                // Validate files
                var validationResult = await _memberImportService.ValidateMultiFileImportAsync(model);
                if (!validationResult.IsValid)
                {
                    foreach (var error in validationResult.AllErrors)
                    {
                        ModelState.AddModelError("", error);
                    }
                    return View(model);
                }

                // Get current user
                var currentUser = _userContextService.GetCurrentUser()?.UserName ?? "Unknown";

                // Process multi-file import
                var (batchId, matchingResult) = await _memberImportService.ProcessMultiFileImportAsync(model, currentUser);

                // Store matching results in TempData for the summary view
                TempData["MatchingResult"] = System.Text.Json.JsonSerializer.Serialize(matchingResult);

                // Redirect to batch summary
                return RedirectToAction("MultiFileBatchSummary", new { id = batchId });
            }
            catch (Exception ex)
            {
                ModelState.AddModelError("", $"An error occurred while processing the files: {ex.Message}");
                return View(model);
            }
        }

        /// <summary>
        /// POST: Validate multi-file import (AJAX endpoint)
        /// </summary>
        [HttpPost]
        public async Task<IActionResult> ValidateMultiFileImport(MultiFileImportRequest model)
        {
            try
            {
                var validationResult = await _memberImportService.ValidateMultiFileImportAsync(model);
                return Json(validationResult);
            }
            catch (Exception ex)
            {
                return Json(new MultiFileValidationResult
                {
                    IsValid = false,
                    GeneralErrors = new List<string> { $"Validation error: {ex.Message}" }
                });
            }
        }

        /// <summary>
        /// GET: Display multi-file batch summary with relationship matching results
        /// </summary>
        public async Task<IActionResult> MultiFileBatchSummary(int id)
        {
            try
            {
                var batchSummary = await _importBatchService.GetBatchSummaryAsync(id);
                var queueStatuses = await _importBatchService.GetQueueStatusesAsync(id);

                // Get relationship matching results from TempData
                RelationshipMatchingResult? matchingResult = null;
                if (TempData["MatchingResult"] is string matchingJson)
                {
                    matchingResult = System.Text.Json.JsonSerializer.Deserialize<RelationshipMatchingResult>(matchingJson);
                }

                var viewModel = new MultiFileImportBatchSummaryViewModel
                {
                    BatchSummary = new ImportBatchSummary
                    {
                        ImportBatchId = batchSummary.ImportBatchId,
                        FileName = batchSummary.FileName,
                        UploadedAtUtc = batchSummary.UploadedAtUtc,
                        UploadedBy = batchSummary.UploadedBy,
                        TotalRows = batchSummary.TotalRows,
                        CreatedCount = batchSummary.CreatedCount,
                        DuplicateCount = batchSummary.DuplicateCount,
                        NeedsFixCount = batchSummary.NeedsFixCount,
                        MergedCount = batchSummary.MergedCount,
                        RejectedCount = batchSummary.RejectedCount,
                        ReadyToCreateCount = batchSummary.ReadyToCreateCount,
                        Status = batchSummary.Status,
                        ErrorMessage = batchSummary.ErrorMessage
                    },
                    QueueStatuses = queueStatuses,
                    RelationshipMatchingResult = matchingResult ?? new RelationshipMatchingResult()
                };

                return View(viewModel);
            }
            catch (ArgumentException)
            {
                return NotFound($"Import batch {id} not found.");
            }
            catch (Exception ex)
            {
                return View("Error", new ErrorViewModel 
                { 
                    Message = $"An error occurred while loading the multi-file batch summary: {ex.Message}" 
                });
            }
        }

        /// <summary>
        /// GET: Manual relationship review interface for unmatched records
        /// </summary>
        public async Task<IActionResult> ReviewUnmatchedRelationships(int batchId)
        {
            try
            {
                var unmatchedRecords = await _memberImportService.GetUnmatchedRelationshipRecordsAsync(batchId);
                return View(unmatchedRecords);
            }
            catch (Exception ex)
            {
                return View("Error", new ErrorViewModel
                {
                    Message = $"An error occurred while loading unmatched relationships: {ex.Message}"
                });
            }
        }
    }

    /// <summary>
    /// View model for the upload page
    /// </summary>
    public class MemberImportUploadViewModel
    {
        public IFormFile? ExcelFile { get; set; }
        public string? Description { get; set; }
    }

    /// <summary>
    /// View model for batch summary display
    /// </summary>
    public class ImportBatchSummaryViewModel
    {
        public ImportBatchViewModel BatchSummary { get; set; } = null!;
        public List<ImportQueueStatusViewModel> QueueStatuses { get; set; } = new();
    }

    /// <summary>
    /// View model for multi-file import batch summary
    /// </summary>
    public class MultiFileImportBatchSummaryViewModel
    {
        public ImportBatchSummary BatchSummary { get; set; } = null!;
        public List<ImportQueueStatusViewModel> QueueStatuses { get; set; } = new();
        public RelationshipMatchingResult RelationshipMatchingResult { get; set; } = new();
    }

    /// <summary>
    /// Simple error view model
    /// </summary>
    public class ErrorViewModel
    {
        public string Message { get; set; } = string.Empty;
    }
}