using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Localization;
using ParaHockeyApp.Controllers;
using ParaHockeyApp.Models.Entities;
using ParaHockeyApp.Resources;
using ParaHockeyApp.Services;

namespace ParaHockeyApp.Controllers
{
    /// <summary>
    /// Controller for member import functionality
    /// </summary>
    [Authorize]
    public class ImportController : BaseMvcController
    {
        private readonly IMemberImportService _memberImportService;
        private readonly ITempMemberService _tempMemberService;

        public ImportController(
            IMemberImportService memberImportService,
            ITempMemberService tempMemberService,
            ILogger<ImportController> logger,
            IStringLocalizer<SharedResourceMarker> localizer)
            : base(logger, localizer)
        {
            _memberImportService = memberImportService;
            _tempMemberService = tempMemberService;
        }

        /// <summary>
        /// Display member import upload page
        /// </summary>
        [HttpGet]
        public IActionResult Members()
        {
            return View();
        }

        /// <summary>
        /// Process uploaded member import file
        /// </summary>
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Members(IFormFile file)
        {
            if (file == null || file.Length == 0)
            {
                ModelState.AddModelError("file", _localizer["Import_FileRequired"]);
                return View();
            }

            try
            {
                // Validate file
                if (!await _memberImportService.ValidateFileAsync(file))
                {
                    ModelState.AddModelError("file", _localizer["Import_InvalidFile"]);
                    return View();
                }

                // Get current admin email for tracking
                var adminEmail = GetCurrentAdminEmail();
                if (string.IsNullOrEmpty(adminEmail))
                {
                    ModelState.AddModelError("", _localizer["Import_AdminRequired"]);
                    return View();
                }

                // Parse and stage the file
                var batchId = await _memberImportService.ParseAndStageAsync(file, adminEmail);

                // Redirect to batch summary
                TempData["SuccessMessage"] = _localizer["Import_FileProcessedSuccessfully"];
                return RedirectToAction("Batch", new { id = batchId });
            }
            catch (Exception ex)
            {
                ModelState.AddModelError("", $"{_localizer["Import_ProcessingError"]}: {ex.Message}");
                return View();
            }
        }

        /// <summary>
        /// Display import batch summary and queue navigation
        /// </summary>
        [HttpGet]
        public async Task<IActionResult> Batch(Guid id)
        {
            try
            {
                // Find batch by GUID
                var batches = await _memberImportService.GetAllBatchesAsync();
                var batch = batches.FirstOrDefault(b => b.ImportBatchGuid == id);
                
                if (batch == null)
                {
                    TempData["ErrorMessage"] = _localizer["Import_BatchNotFound"];
                    return RedirectToAction("Index", "Admin");
                }

                return View(batch);
            }
            catch (Exception ex)
            {
                TempData["ErrorMessage"] = $"{_localizer["Import_BatchLoadError"]}: {ex.Message}";
                return RedirectToAction("Index", "Admin");
            }
        }

        /// <summary>
        /// Display list of all import batches
        /// </summary>
        [HttpGet]
        public async Task<IActionResult> History()
        {
            try
            {
                var batches = await _memberImportService.GetAllBatchesAsync();
                return View(batches);
            }
            catch (Exception ex)
            {
                TempData["ErrorMessage"] = $"{_localizer["Import_HistoryLoadError"]}: {ex.Message}";
                return View(new List<ImportBatchSummary>());
            }
        }

        /// <summary>
        /// Update batch statistics (AJAX endpoint)
        /// </summary>
        [HttpPost]
        public async Task<IActionResult> UpdateBatchStatistics(int batchId)
        {
            try
            {
                await _memberImportService.UpdateBatchStatisticsAsync(batchId);
                var summary = await _memberImportService.GetBatchSummaryAsync(batchId);
                return Json(new { success = true, summary });
            }
            catch (Exception ex)
            {
                return Json(new { success = false, error = ex.Message });
            }
        }

        /// <summary>
        /// Helper method to get current admin email from claims
        /// </summary>
        private string? GetCurrentAdminEmail()
        {
            // In development mode (NoAuth), use a default admin email
            if (User?.Identity?.Name == null)
            {
                return "<EMAIL>";
            }

            // In production, get from Azure AD claims
            return User.Identity.Name ?? User.FindFirst("preferred_username")?.Value;
        }
    }
}