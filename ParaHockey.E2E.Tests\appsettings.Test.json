{"TestSettings": {"BaseUrl": "http://localhost:5285", "HeadlessMode": false, "TimeoutSeconds": 30, "ScreenshotOnFailure": true, "ScreenshotPath": "./Screenshots", "ImplicitWaitSeconds": 10, "ExplicitWaitSeconds": 20}, "BrowserSettings": {"DefaultBrowser": "Chrome", "SupportedBrowsers": ["Chrome", "Firefox", "Edge"], "WindowSize": {"Width": 1920, "Height": 1080}, "MobileViewports": [{"Name": "iPhone 12", "Width": 390, "Height": 844}, {"Name": "iPad", "Width": 768, "Height": 1024}, {"Name": "Samsung Galaxy", "Width": 360, "Height": 640}]}, "TestData": {"ValidPostalCode": "H3B 2Y5", "InvalidPostalCode": "12345", "ValidPhone": "(*************", "InvalidPhone": "invalid", "ValidEmail": "<EMAIL>", "InvalidEmail": "invalid-email", "JuniorBirthDate": "2010-05-15", "MinorBirthDate": "2010-05-15", "AdultBirthDate": "1990-03-20", "ValidJunior": {"FirstName": "<PERSON>", "LastName": "<PERSON><PERSON>", "DateOfBirth": "2010-05-15", "Gender": "1", "Address": "123 Rue Principale", "City": "Montréal", "Province": "1", "PostalCode": "H1A 1A1", "Phone": "(*************", "PhoneType": "1", "Email": "<EMAIL>", "RegistrationType": "1"}, "ValidAdult": {"FirstName": "<PERSON>", "LastName": "Tremblay", "DateOfBirth": "1990-08-22", "Gender": "2", "Address": "456 Avenue des Érables", "City": "Québec", "Province": "1", "PostalCode": "G1A 1A1", "Phone": "(*************", "PhoneType": "1", "Email": "<EMAIL>", "RegistrationType": "3"}, "TestEmailPrefix": "test", "TestEmailDomain": "parahockey.test"}, "Languages": {"French": "fr-CA", "English": "en-CA"}}