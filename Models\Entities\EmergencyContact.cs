using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace ParaHockeyApp.Models.Entities
{
    /// <summary>
    /// Emergency contact details for members (non-junior registrations).
    /// </summary>
    public class EmergencyContact : BaseEntity
    {
        // Basic Information
        [Required]
        [StringLength(50)]
        public string FirstName { get; set; } = string.Empty;

        [Required]
        [StringLength(50)]
        public string LastName { get; set; } = string.Empty;

        // Relationship to the member (e.g., <PERSON>pouse, Friend)
        [Required]
        [StringLength(50)]
        public string RelationToUser { get; set; } = string.Empty;

        // Contact Information
        [Required]
        [StringLength(20)]
        public string Phone { get; set; } = string.Empty;

        [StringLength(254)]
        [EmailAddress]
        public string? Email { get; set; }

        // Foreign Key to Member
        [Required]
        public int MemberId { get; set; }
        public virtual Member Member { get; set; } = null!;

        [NotMapped]
        public string FullName => $"{FirstName} {LastName}";

        public override bool EstValide =>
            !string.IsNullOrWhiteSpace(FirstName) &&
            !string.IsNullOrWhiteSpace(LastName) &&
            !string.IsNullOrWhiteSpace(RelationToUser) &&
            !string.IsNullOrWhiteSpace(Phone) &&
            IsActive;
    }
}