# ParaHockey Application Runner
Clear-Host  # Clears the terminal before starting
Write-Host "Starting ParaHockey Application..." -ForegroundColor Cyan
Write-Host "URL: http://localhost:5285" -ForegroundColor Green
Write-Host "Environment: Development" -ForegroundColor Yellow
Write-Host ""

# Kill any existing instances of the application
Write-Host "Checking for existing application instances..." -ForegroundColor Yellow
try {
    Stop-Process -Name "ParaHockeyApp" -Force -ErrorAction SilentlyContinue
    Stop-Process -Name "dotnet" -Force -ErrorAction SilentlyContinue | Where-Object { $_.MainWindowTitle -like "*ParaHockey*" }
    Write-Host "Existing instances stopped." -ForegroundColor Green
} catch {
    Write-Host "No existing instances found." -ForegroundColor Green
}

Write-Host ""
Write-Host "Press Ctrl+C to stop the application" -ForegroundColor Gray
Write-Host ""

# Run the application
dotnet run --project ParaHockeyApp.csproj