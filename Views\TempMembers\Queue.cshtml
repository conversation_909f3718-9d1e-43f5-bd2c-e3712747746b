@model TempMemberQueueViewModel
@{
    ViewData["Title"] = $"Queue - {Model.QueueDisplayName}";
}

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <!-- Header -->
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h2 class="mb-1">
                        <i class="@Model.QueueIconClass @Model.QueueColorClass me-2"></i>
                        @Model.QueueDisplayName Queue
                    </h2>
                    <p class="text-muted mb-0">@Model.QueueDescription</p>
                    <small class="text-muted">Batch: @Model.BatchFileName</small>
                </div>
                <div>
                    <a href="@Url.Action("BatchSummary", "Import", new { id = Model.BatchId })" class="btn btn-outline-primary">
                        <i class="fas fa-arrow-left me-1"></i>Back to Summary
                    </a>
                </div>
            </div>

            <!-- Search and Filters -->
            <div class="card mb-4">
                <div class="card-body">
                    <form method="get" class="row g-3">
                        <input type="hidden" name="batchId" value="@Model.BatchId" />
                        <input type="hidden" name="status" value="@Model.Status" />
                        
                        <div class="col-md-6">
                            <label for="search" class="form-label">Search Members</label>
                            <input type="text" class="form-control" id="search" name="search" 
                                   value="@Model.SearchTerm" placeholder="Search by name, email...">
                        </div>
                        
                        <div class="col-md-3">
                            <label for="pageSize" class="form-label">Items per page</label>
                            <select class="form-select" id="pageSize" name="pageSize">
                                <option value="10" selected="@(Model.PageSize == 10)">10</option>
                                <option value="20" selected="@(Model.PageSize == 20)">20</option>
                                <option value="50" selected="@(Model.PageSize == 50)">50</option>
                                <option value="100" selected="@(Model.PageSize == 100)">100</option>
                            </select>
                        </div>
                        
                        <div class="col-md-3 d-flex align-items-end">
                            <button type="submit" class="btn btn-primary me-2">
                                <i class="fas fa-search me-1"></i>Search
                            </button>
                            <a href="@Url.Action("Queue", new { batchId = Model.BatchId, status = Model.Status })" 
                               class="btn btn-outline-secondary">Clear</a>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Bulk Actions -->
            @if (Model.TempMembers.Any())
            {
                <div class="card mb-4">
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="selectAll">
                                    <label class="form-check-label" for="selectAll">
                                        Select All (@Model.TempMembers.Count items)
                                    </label>
                                </div>
                            </div>
                            <div class="col-md-6 text-end">
                                @if (Model.AllowsBulkCreate)
                                {
                                    <button type="button" class="btn btn-success me-2" id="bulkCreateBtn" disabled>
                                        <i class="fas fa-plus-circle me-1"></i>Create Selected Members
                                    </button>
                                }
                                @if (Model.AllowsBulkReject)
                                {
                                    <button type="button" class="btn btn-danger me-2" id="bulkRejectBtn" disabled>
                                        <i class="fas fa-times-circle me-1"></i>Reject Selected
                                    </button>
                                }
                                @if (Model.ShowAutoMerge)
                                {
                                    <button type="button" class="btn btn-info" id="autoMergeBtn">
                                        <i class="fas fa-compress-arrows-alt me-1"></i>Auto-Merge Identical
                                    </button>
                                }
                            </div>
                        </div>
                    </div>
                </div>
            }

            <!-- Results Summary -->
            <div class="d-flex justify-content-between align-items-center mb-3">
                <div>
                    <span class="text-muted">
                        Showing @((Model.PageNumber - 1) * Model.PageSize + 1) to 
                        @Math.Min(Model.PageNumber * Model.PageSize, Model.TotalCount) 
                        of @Model.TotalCount results
                    </span>
                </div>
                @if (Model.TotalPages > 1)
                {
                    <nav aria-label="Page navigation">
                        <ul class="pagination pagination-sm mb-0">
                            <li class="page-item @(!Model.HasPreviousPage ? "disabled" : "")">
                                <a class="page-link" href="@Url.Action("Queue", new { 
                                    batchId = Model.BatchId, 
                                    status = Model.Status, 
                                    page = Model.PageNumber - 1, 
                                    pageSize = Model.PageSize,
                                    search = Model.SearchTerm 
                                })">Previous</a>
                            </li>
                            
                            @for (int i = Math.Max(1, Model.PageNumber - 2); i <= Math.Min(Model.TotalPages, Model.PageNumber + 2); i++)
                            {
                                <li class="page-item @(i == Model.PageNumber ? "active" : "")">
                                    <a class="page-link" href="@Url.Action("Queue", new { 
                                        batchId = Model.BatchId, 
                                        status = Model.Status, 
                                        page = i, 
                                        pageSize = Model.PageSize,
                                        search = Model.SearchTerm 
                                    })">@i</a>
                                </li>
                            }
                            
                            <li class="page-item @(!Model.HasNextPage ? "disabled" : "")">
                                <a class="page-link" href="@Url.Action("Queue", new { 
                                    batchId = Model.BatchId, 
                                    status = Model.Status, 
                                    page = Model.PageNumber + 1, 
                                    pageSize = Model.PageSize,
                                    search = Model.SearchTerm 
                                })">Next</a>
                            </li>
                        </ul>
                    </nav>
                }
            </div>

            <!-- Members Table -->
            @if (Model.TempMembers.Any())
            {
                <div class="card">
                    <div class="table-responsive">
                        <table class="table table-hover mb-0">
                            <thead class="table-light">
                                <tr>
                                    <th width="40"><input type="checkbox" id="selectAllHeader"></th>
                                    <th>Name</th>
                                    <th>Email</th>
                                    <th>Date of Birth</th>
                                    <th>Status</th>
                                    <th>Errors</th>
                                    <th width="200">Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach (var tempMember in Model.TempMembers)
                                {
                                    <tr>
                                        <td>
                                            <input type="checkbox" class="member-checkbox" value="@tempMember.TempMemberId">
                                        </td>
                                        <td>
                                            <strong>@tempMember.FirstName @tempMember.LastName</strong>
                                            @if (!string.IsNullOrEmpty(tempMember.Phone))
                                            {
                                                <br><small class="text-muted">@tempMember.Phone</small>
                                            }
                                        </td>
                                        <td>
                                            @if (!string.IsNullOrEmpty(tempMember.Email))
                                            {
                                                <span>@tempMember.Email</span>
                                            }
                                            else
                                            {
                                                <span class="text-muted">No email</span>
                                            }
                                        </td>
                                        <td>
                                            @if (tempMember.DateOfBirth.HasValue)
                                            {
                                                <span>@tempMember.DateOfBirth.Value.ToString("yyyy-MM-dd")</span>
                                            }
                                            else
                                            {
                                                <span class="text-muted">Not provided</span>
                                            }
                                        </td>
                                        <td>
                                            <span class="badge bg-@GetStatusBadgeColor(tempMember.Status)">
                                                @tempMember.Status
                                            </span>
                                        </td>
                                        <td>
                                            @if (!string.IsNullOrEmpty(tempMember.ValidationErrorsJson))
                                            {
                                                <span class="text-danger">
                                                    <i class="fas fa-exclamation-triangle me-1"></i>
                                                    Has errors
                                                </span>
                                            }
                                            else
                                            {
                                                <span class="text-success">
                                                    <i class="fas fa-check-circle me-1"></i>
                                                    Valid
                                                </span>
                                            }
                                        </td>
                                        <td>
                                            <div class="btn-group btn-group-sm" role="group">
                                                <a href="@Url.Action("Details", new { id = tempMember.TempMemberId })" 
                                                   class="btn btn-outline-info" title="View Details">
                                                    <i class="fas fa-eye"></i>
                                                </a>
                                                
                                                @if (tempMember.Status == TempMemberStatus.NeedsFix)
                                                {
                                                    <a href="@Url.Action("Edit", new { id = tempMember.TempMemberId })" 
                                                       class="btn btn-outline-primary" title="Edit">
                                                        <i class="fas fa-edit"></i>
                                                    </a>
                                                }
                                                
                                                @if (tempMember.Status == TempMemberStatus.ReadyToCreate)
                                                {
                                                    <a href="@Url.Action("Register", "Members", new { tempMemberId = tempMember.TempMemberId })" 
                                                       class="btn btn-outline-success" title="Create Member with Form">
                                                        <i class="fas fa-user-plus"></i>
                                                    </a>
                                                    <form method="post" action="@Url.Action("CreateMember")" class="d-inline">
                                                        @Html.AntiForgeryToken()
                                                        <input type="hidden" name="id" value="@tempMember.TempMemberId" />
                                                        <button type="submit" class="btn btn-outline-success" title="Create Member Directly"
                                                                onclick="return confirm('Create member from this temp record?')">
                                                            <i class="fas fa-plus-circle"></i>
                                                        </button>
                                                    </form>
                                                }
                                                
                                                @if (tempMember.Status == TempMemberStatus.Duplicate)
                                                {
                                                    <a href="@Url.Action("ResolveDuplicate", new { id = tempMember.TempMemberId })" 
                                                       class="btn btn-outline-warning" title="Resolve Duplicate">
                                                        <i class="fas fa-compress-arrows-alt"></i>
                                                    </a>
                                                }
                                                
                                                @if (tempMember.Status != TempMemberStatus.Created && 
                                                     tempMember.Status != TempMemberStatus.Merged && 
                                                     tempMember.Status != TempMemberStatus.Rejected)
                                                {
                                                    <form method="post" action="@Url.Action("Reject")" class="d-inline">
                                                        @Html.AntiForgeryToken()
                                                        <input type="hidden" name="id" value="@tempMember.TempMemberId" />
                                                        <button type="submit" class="btn btn-outline-danger" title="Reject"
                                                                onclick="return confirm('Reject this temp member record?')">
                                                            <i class="fas fa-times-circle"></i>
                                                        </button>
                                                    </form>
                                                }
                                            </div>
                                        </td>
                                    </tr>
                                }
                            </tbody>
                        </table>
                    </div>
                </div>
            }
            else
            {
                <div class="card">
                    <div class="card-body text-center py-5">
                        <i class="@Model.QueueIconClass fa-3x @Model.QueueColorClass mb-3"></i>
                        <h4>No temp members found</h4>
                        <p class="text-muted">
                            @if (!string.IsNullOrEmpty(Model.SearchTerm))
                            {
                                <span>No temp members match your search criteria.</span>
                            }
                            else
                            {
                                <span>This queue is currently empty.</span>
                            }
                        </p>
                        @if (!string.IsNullOrEmpty(Model.SearchTerm))
                        {
                            <a href="@Url.Action("Queue", new { batchId = Model.BatchId, status = Model.Status })" 
                               class="btn btn-primary">Clear Search</a>
                        }
                    </div>
                </div>
            }
        </div>
    </div>
</div>

<!-- Bulk Action Forms (Hidden) -->
<form id="bulkCreateForm" method="post" action="@Url.Action("BulkCreateMembers")" style="display: none;">
    @Html.AntiForgeryToken()
    <input type="hidden" name="batchId" value="@Model.BatchId" />
    <div id="bulkCreateSelectedIds"></div>
</form>

<form id="bulkRejectForm" method="post" action="@Url.Action("BulkReject")" style="display: none;">
    @Html.AntiForgeryToken()
    <input type="hidden" name="batchId" value="@Model.BatchId" />
    <input type="hidden" name="currentStatus" value="@Model.Status" />
    <div id="bulkRejectSelectedIds"></div>
</form>

<form id="autoMergeForm" method="post" action="@Url.Action("AutoMergeIdentical")" style="display: none;">
    @Html.AntiForgeryToken()
    <input type="hidden" name="batchId" value="@Model.BatchId" />
</form>

@functions {
    private string GetStatusBadgeColor(TempMemberStatus status)
    {
        return status switch
        {
            TempMemberStatus.Imported => "info",
            TempMemberStatus.ReadyToCreate => "success",
            TempMemberStatus.NeedsFix => "warning",
            TempMemberStatus.Duplicate => "primary",
            TempMemberStatus.Created => "success",
            TempMemberStatus.Merged => "info",
            TempMemberStatus.Rejected => "danger",
            _ => "secondary"
        };
    }
}

@section Scripts {
    <script>
        $(document).ready(function() {
            // Handle select all checkboxes
            $('#selectAll, #selectAllHeader').change(function() {
                const isChecked = this.checked;
                $('.member-checkbox').prop('checked', isChecked);
                updateBulkButtons();
                
                // Sync both select all checkboxes
                $('#selectAll, #selectAllHeader').prop('checked', isChecked);
            });

            // Handle individual checkboxes
            $('.member-checkbox').change(function() {
                updateBulkButtons();
                updateSelectAllState();
            });

            // Bulk create button
            $('#bulkCreateBtn').click(function() {
                const selectedIds = getSelectedIds();
                if (selectedIds.length === 0) {
                    alert('Please select at least one temp member to create.');
                    return;
                }
                
                if (confirm(`Create ${selectedIds.length} members from selected temp records?`)) {
                    // Add selected IDs to form
                    const container = $('#bulkCreateSelectedIds');
                    container.empty();
                    selectedIds.forEach(id => {
                        container.append(`<input type="hidden" name="selectedIds" value="${id}" />`);
                    });
                    
                    $('#bulkCreateForm').submit();
                }
            });

            // Bulk reject button
            $('#bulkRejectBtn').click(function() {
                const selectedIds = getSelectedIds();
                if (selectedIds.length === 0) {
                    alert('Please select at least one temp member to reject.');
                    return;
                }
                
                if (confirm(`Reject ${selectedIds.length} selected temp members?`)) {
                    // Add selected IDs to form
                    const container = $('#bulkRejectSelectedIds');
                    container.empty();
                    selectedIds.forEach(id => {
                        container.append(`<input type="hidden" name="selectedIds" value="${id}" />`);
                    });
                    
                    $('#bulkRejectForm').submit();
                }
            });

            // Auto merge button
            $('#autoMergeBtn').click(function() {
                if (confirm('Auto-merge all identical duplicate records in this batch? This action cannot be undone.')) {
                    $('#autoMergeForm').submit();
                }
            });

            function getSelectedIds() {
                return $('.member-checkbox:checked').map(function() {
                    return this.value;
                }).get();
            }

            function updateBulkButtons() {
                const selectedCount = $('.member-checkbox:checked').length;
                $('#bulkCreateBtn, #bulkRejectBtn').prop('disabled', selectedCount === 0);
            }

            function updateSelectAllState() {
                const totalCheckboxes = $('.member-checkbox').length;
                const checkedCheckboxes = $('.member-checkbox:checked').length;
                
                if (checkedCheckboxes === 0) {
                    $('#selectAll, #selectAllHeader').prop('indeterminate', false).prop('checked', false);
                } else if (checkedCheckboxes === totalCheckboxes) {
                    $('#selectAll, #selectAllHeader').prop('indeterminate', false).prop('checked', true);
                } else {
                    $('#selectAll, #selectAllHeader').prop('indeterminate', true).prop('checked', false);
                }
            }
        });
    </script>
}