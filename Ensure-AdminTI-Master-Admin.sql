-- ===============================================
-- Ensure <EMAIL> is Master Admin 
-- with AdminType = 9 in ALL environments
-- ===============================================

PRINT '🚀 Starting Master Admin <NAME_EMAIL>...';

-- Step 1: Add AdminType column if it doesn't exist
IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N'AdminUsers') AND name = 'AdminType')
BEGIN
    PRINT '📋 Adding AdminType column to AdminUsers table...';
    
    ALTER TABLE AdminUsers ADD AdminType INT NOT NULL DEFAULT 3;
    
    -- Update existing records based on IsMasterAdmin
    UPDATE AdminUsers 
    SET AdminType = CASE 
        WHEN IsMasterAdmin = 1 THEN 9 
        ELSE 3 
    END;
    
    PRINT '✅ AdminType column added and data migrated';
END
ELSE
BEGIN
    PRINT 'ℹ️ AdminType column already exists';
END

-- Step 1.5: Add IsActive column if it doesn't exist
IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N'AdminUsers') AND name = 'IsActive')
BEGIN
    PRINT '📋 Adding IsActive column to AdminUsers table...';
    
    ALTER TABLE AdminUsers ADD IsActive BIT NOT NULL DEFAULT 1;
    
    -- Set all existing admins to active
    UPDATE AdminUsers SET IsActive = 1;
    
    PRINT '✅ IsActive column added and all existing admins set to active';
END
ELSE
BEGIN
    PRINT 'ℹ️ IsActive column already exists';
END

-- Step 2: Ensure <EMAIL> exists as Master Admin
DECLARE @AdminEmail NVARCHAR(255) = '<EMAIL>';
DECLARE @AdminName NVARCHAR(100) = 'Admin TI';
DECLARE @AdminExists INT = 0;

-- Check if admin exists
SELECT @AdminExists = COUNT(*) FROM AdminUsers WHERE Email = @AdminEmail;

IF @AdminExists = 0
BEGIN
    PRINT '👤 Creating Master Admin: <EMAIL>...';
    
    INSERT INTO AdminUsers (Email, Name, AdminType, IsMasterAdmin, IsActive, DateCreated)
    VALUES (@AdminEmail, @AdminName, 9, 1, 1, GETUTCDATE());
    
    PRINT '✅ Master Admin created successfully';
END
ELSE
BEGIN
    PRINT '👤 Admin exists, ensuring Master Admin status...';
    
    -- Update existing admin to ensure Master status
    UPDATE AdminUsers 
    SET 
        AdminType = 9,
        IsMasterAdmin = 1,
        Name = @AdminName,
        IsActive = 1
    WHERE Email = @AdminEmail;
    
    PRINT '✅ Master Admin status updated';
END

-- Step 3: Verify the setup
PRINT '🔍 Verifying Master Admin setup...';

SELECT 
    Id,
    Email,
    Name,
    AdminType,
    IsMasterAdmin,
    IsActive,
    DateCreated,
    CASE AdminType
        WHEN 0 THEN 'Disabled'
        WHEN 3 THEN 'Normal Admin'
        WHEN 9 THEN 'Master Admin'
        ELSE 'Unknown'
    END as AdminTypeDescription
FROM AdminUsers 
WHERE Email = @AdminEmail;

-- Step 4: Show all current admins
PRINT '📊 Current Admin Users Status:';

SELECT 
    Id,
    Email,
    Name,
    AdminType,
    CASE AdminType
        WHEN 0 THEN 'Disabled'
        WHEN 3 THEN 'Normal Admin'
        WHEN 9 THEN 'Master Admin'
        ELSE 'Unknown'
    END as AdminTypeDescription,
    IsMasterAdmin as [Legacy_IsMasterAdmin],
    IsActive,
    DateCreated
FROM AdminUsers 
ORDER BY AdminType DESC, Email;

PRINT '🎉 Master Admin setup completed successfully!';
PRINT '';
PRINT '📝 Summary:';
PRINT '   - AdminType column: Present';
PRINT '   - <EMAIL>: Master Admin (AdminType = 9)';
PRINT '   - Ready for new AdminType system';