namespace ParaHockeyApp.Services
{
    /// <summary>
    /// Service for testing performance with maximum file sizes and row limits
    /// </summary>
    public interface IPerformanceTestService
    {
        /// <summary>
        /// Tests import performance with various file sizes
        /// </summary>
        /// <param name="testScenarios">List of test scenarios to run</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Performance test results</returns>
        Task<PerformanceTestResult> RunPerformanceTestsAsync(List<PerformanceTestScenario> testScenarios, CancellationToken cancellationToken = default);

        /// <summary>
        /// Generates test data files of specified sizes for performance testing
        /// </summary>
        /// <param name="rowCounts">Number of rows to generate for each test file</param>
        /// <returns>List of generated test file paths</returns>
        Task<List<string>> GenerateTestDataFilesAsync(List<int> rowCounts);

        /// <summary>
        /// Tests memory usage during import operations
        /// </summary>
        /// <param name="filePath">Path to test file</param>
        /// <param name="useStreaming">Whether to use streaming import</param>
        /// <returns>Memory usage statistics</returns>
        Task<MemoryUsageResult> TestMemoryUsageAsync(string filePath, bool useStreaming = false);

        /// <summary>
        /// Benchmarks database performance with bulk operations
        /// </summary>
        /// <param name="recordCounts">Number of records to test with</param>
        /// <returns>Database performance results</returns>
        Task<DatabasePerformanceResult> BenchmarkDatabasePerformanceAsync(List<int> recordCounts);

        /// <summary>
        /// Tests concurrent import operations
        /// </summary>
        /// <param name="concurrentImports">Number of concurrent imports to run</param>
        /// <param name="rowsPerImport">Number of rows in each import</param>
        /// <returns>Concurrency test results</returns>
        Task<ConcurrencyTestResult> TestConcurrentImportsAsync(int concurrentImports, int rowsPerImport);

        /// <summary>
        /// Validates system limits and recommendations
        /// </summary>
        /// <returns>System limits validation result</returns>
        Task<SystemLimitsResult> ValidateSystemLimitsAsync();
    }

    /// <summary>
    /// Performance test scenario
    /// </summary>
    public class PerformanceTestScenario
    {
        public string Name { get; set; } = string.Empty;
        public int RowCount { get; set; }
        public long FileSizeBytes { get; set; }
        public bool UseStreaming { get; set; }
        public int BatchSize { get; set; } = 500;
        public bool EnableConcurrency { get; set; }
        public TimeSpan ExpectedDuration { get; set; }
        public long ExpectedMemoryUsageMB { get; set; }
    }

    /// <summary>
    /// Performance test result
    /// </summary>
    public class PerformanceTestResult
    {
        public DateTime TestRunDate { get; set; } = DateTime.UtcNow;
        public List<ScenarioResult> ScenarioResults { get; set; } = new();
        public OverallPerformanceMetrics OverallMetrics { get; set; } = new();
        public List<string> Recommendations { get; set; } = new();
        public bool AllTestsPassed { get; set; }
        public string? FailureReason { get; set; }
    }

    /// <summary>
    /// Result for individual test scenario
    /// </summary>
    public class ScenarioResult
    {
        public string ScenarioName { get; set; } = string.Empty;
        public bool Success { get; set; }
        public TimeSpan ActualDuration { get; set; }
        public long PeakMemoryUsageMB { get; set; }
        public long AverageMemoryUsageMB { get; set; }
        public int RecordsProcessed { get; set; }
        public double RecordsPerSecond { get; set; }
        public int DatabaseConnections { get; set; }
        public List<string> PerformanceIssues { get; set; } = new();
        public Exception? Exception { get; set; }
    }

    /// <summary>
    /// Overall performance metrics
    /// </summary>
    public class OverallPerformanceMetrics
    {
        public TimeSpan TotalTestDuration { get; set; }
        public long MaxMemoryUsageMB { get; set; }
        public double AverageRecordsPerSecond { get; set; }
        public int TotalRecordsProcessed { get; set; }
        public int SuccessfulScenarios { get; set; }
        public int FailedScenarios { get; set; }
        public string SystemSpecs { get; set; } = string.Empty;
    }

    /// <summary>
    /// Memory usage test result
    /// </summary>
    public class MemoryUsageResult
    {
        public long InitialMemoryMB { get; set; }
        public long PeakMemoryMB { get; set; }
        public long FinalMemoryMB { get; set; }
        public long MemoryGrowthMB => PeakMemoryMB - InitialMemoryMB;
        public TimeSpan TestDuration { get; set; }
        public List<MemorySnapshot> MemorySnapshots { get; set; } = new();
        public bool ExceededThreshold { get; set; }
        public long ThresholdMB { get; set; } = 1024; // 1GB default threshold
    }

    /// <summary>
    /// Memory snapshot at a point in time
    /// </summary>
    public class MemorySnapshot
    {
        public DateTime Timestamp { get; set; }
        public long MemoryUsageMB { get; set; }
        public int RecordsProcessed { get; set; }
        public string Stage { get; set; } = string.Empty;
    }

    /// <summary>
    /// Database performance test result
    /// </summary>
    public class DatabasePerformanceResult
    {
        public List<DatabaseBenchmark> Benchmarks { get; set; } = new();
        public TimeSpan AverageInsertTime { get; set; }
        public TimeSpan AverageSelectTime { get; set; }
        public TimeSpan AverageUpdateTime { get; set; }
        public double OptimalBatchSize { get; set; }
        public List<string> PerformanceRecommendations { get; set; } = new();
    }

    /// <summary>
    /// Individual database benchmark
    /// </summary>
    public class DatabaseBenchmark
    {
        public int RecordCount { get; set; }
        public TimeSpan InsertDuration { get; set; }
        public TimeSpan SelectDuration { get; set; }
        public TimeSpan UpdateDuration { get; set; }
        public double RecordsPerSecond { get; set; }
        public int DatabaseConnections { get; set; }
        public bool CompletedSuccessfully { get; set; }
    }

    /// <summary>
    /// Concurrency test result
    /// </summary>
    public class ConcurrencyTestResult
    {
        public int ConcurrentImports { get; set; }
        public int RowsPerImport { get; set; }
        public TimeSpan TotalDuration { get; set; }
        public int SuccessfulImports { get; set; }
        public int FailedImports { get; set; }
        public List<string> ConcurrencyIssues { get; set; } = new();
        public double AverageImportDuration { get; set; }
        public bool DeadlocksDetected { get; set; }
        public int MaxConcurrentConnections { get; set; }
    }

    /// <summary>
    /// System limits validation result
    /// </summary>
    public class SystemLimitsResult
    {
        public long RecommendedMaxFileSizeMB { get; set; }
        public int RecommendedMaxRowCount { get; set; }
        public int RecommendedBatchSize { get; set; }
        public int RecommendedMaxConcurrentImports { get; set; }
        public long AvailableMemoryMB { get; set; }
        public string SystemSpecs { get; set; } = string.Empty;
        public List<string> SystemWarnings { get; set; } = new();
        public Dictionary<string, object> ConfigurationRecommendations { get; set; } = new();
    }
}