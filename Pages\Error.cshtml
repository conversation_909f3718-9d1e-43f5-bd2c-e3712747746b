@page
@model ErrorModel
@using Microsoft.Extensions.Localization
@inject IStringLocalizer<ParaHockeyApp.Resources.SharedResourceMarker> SharedLocalizer
@{
    ViewData["Title"] = SharedLocalizer["SystemErrorTitle"];
}

<h1 class="text-danger">@SharedLocalizer["SystemErrorTitle"].</h1>
<h2 class="text-danger">@SharedLocalizer["SystemErrorMessage"]</h2>

@if (Model.ShowRequestId)
{
    <p>
        <strong>@SharedLocalizer["SystemErrorRequestId"]</strong> <code>@Model.RequestId</code>
    </p>
}

<h3>@SharedLocalizer["SystemErrorDevelopmentMode"]</h3>
<p>
    @Html.Raw(SharedLocalizer["SystemErrorDevelopmentInfo"])
</p>
<p>
    @Html.Raw(SharedLocalizer["SystemErrorDevelopmentWarning"])
</p>