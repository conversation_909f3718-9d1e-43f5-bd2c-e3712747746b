/* ParaHockey Design System - Shared Components */

/* ===== BUTTONS ===== */
.ph-btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: var(--ph-btn-padding-y) var(--ph-btn-padding-x);
    font-family: var(--ph-font-family-base);
    font-size: var(--ph-btn-font-size);
    font-weight: var(--ph-font-weight-medium);
    line-height: 1.5;
    text-align: center;
    text-decoration: none;
    vertical-align: middle;
    cursor: pointer;
    user-select: none;
    border: var(--ph-btn-border-width) solid transparent;
    border-radius: var(--ph-btn-border-radius);
    transition: var(--ph-btn-transition);
    min-height: 2.5rem;
    gap: var(--ph-spacing-sm);
}

.ph-btn:focus {
    outline: 0;
    box-shadow: 0 0 0 var(--ph-focus-ring-width) var(--ph-focus-ring-color);
}

.ph-btn:disabled,
.ph-btn.disabled {
    pointer-events: none;
    opacity: 0.65;
}

/* But<PERSON> Variants */
.ph-btn-primary {
    color: var(--ph-white);
    background-color: var(--ph-primary);
    border-color: var(--ph-primary);
}

.ph-btn-primary:hover {
    color: var(--ph-white);
    background-color: var(--ph-primary-hover);
    border-color: var(--ph-primary-hover);
}

.ph-btn-primary:focus {
    box-shadow: 0 0 0 var(--ph-focus-ring-width) var(--ph-focus-ring-color);
}

.ph-btn-secondary {
    color: var(--ph-white);
    background-color: var(--ph-secondary);
    border-color: var(--ph-secondary);
}

.ph-btn-secondary:hover {
    color: var(--ph-white);
    background-color: var(--ph-secondary-hover);
    border-color: var(--ph-secondary-hover);
}

.ph-btn-success {
    color: var(--ph-white);
    background-color: var(--ph-success);
    border-color: var(--ph-success);
}

.ph-btn-success:hover {
    color: var(--ph-white);
    background-color: var(--ph-success-hover);
    border-color: var(--ph-success-hover);
}

.ph-btn-warning {
    color: var(--ph-gray-900);
    background-color: var(--ph-warning);
    border-color: var(--ph-warning);
}

.ph-btn-warning:hover {
    color: var(--ph-gray-900);
    background-color: var(--ph-warning-hover);
    border-color: var(--ph-warning-hover);
}

.ph-btn-danger {
    color: var(--ph-white);
    background-color: var(--ph-danger);
    border-color: var(--ph-danger);
}

.ph-btn-danger:hover {
    color: var(--ph-white);
    background-color: var(--ph-danger-hover);
    border-color: var(--ph-danger-hover);
}

.ph-btn-info {
    color: var(--ph-white);
    background-color: var(--ph-info);
    border-color: var(--ph-info);
}

.ph-btn-info:hover {
    color: var(--ph-white);
    background-color: var(--ph-info-hover);
    border-color: var(--ph-info-hover);
}

/* Outline Button Variants */
.ph-btn-outline-primary {
    color: var(--ph-primary);
    border-color: var(--ph-primary);
    background-color: transparent;
}

.ph-btn-outline-primary:hover {
    color: var(--ph-white);
    background-color: var(--ph-primary);
    border-color: var(--ph-primary);
}

.ph-btn-outline-secondary {
    color: var(--ph-secondary);
    border-color: var(--ph-secondary);
    background-color: transparent;
}

.ph-btn-outline-secondary:hover {
    color: var(--ph-white);
    background-color: var(--ph-secondary);
    border-color: var(--ph-secondary);
}

/* Button Sizes */
.ph-btn-sm {
    padding: 0.25rem 0.5rem;
    font-size: var(--ph-font-size-sm);
    border-radius: var(--ph-radius-sm);
    min-height: 2rem;
}

.ph-btn-lg {
    padding: 0.75rem 1.5rem;
    font-size: var(--ph-font-size-lg);
    border-radius: var(--ph-radius-lg);
    min-height: 3rem;
}

/* Button Groups */
.ph-btn-group {
    display: inline-flex;
    vertical-align: middle;
}

.ph-btn-group > .ph-btn {
    position: relative;
    flex: 1 1 auto;
}

.ph-btn-group > .ph-btn:not(:first-child) {
    margin-left: -1px;
    border-top-left-radius: 0;
    border-bottom-left-radius: 0;
}

.ph-btn-group > .ph-btn:not(:last-child) {
    border-top-right-radius: 0;
    border-bottom-right-radius: 0;
}

/* ===== CARDS ===== */
.ph-card {
    position: relative;
    display: flex;
    flex-direction: column;
    min-width: 0;
    word-wrap: break-word;
    background-color: var(--ph-bg-primary);
    background-clip: border-box;
    border: var(--ph-card-border-width) solid var(--ph-card-border-color);
    border-radius: var(--ph-card-border-radius);
    box-shadow: var(--ph-card-shadow);
    transition: box-shadow var(--ph-transition-fast);
}

.ph-card:hover {
    box-shadow: var(--ph-card-hover-shadow);
}

.ph-card-header {
    padding: var(--ph-card-padding);
    margin-bottom: 0;
    background-color: var(--ph-bg-secondary);
    border-bottom: var(--ph-card-border-width) solid var(--ph-card-border-color);
    border-top-left-radius: calc(var(--ph-card-border-radius) - var(--ph-card-border-width));
    border-top-right-radius: calc(var(--ph-card-border-radius) - var(--ph-card-border-width));
}

.ph-card-body {
    flex: 1 1 auto;
    padding: var(--ph-card-padding);
}

.ph-card-footer {
    padding: var(--ph-card-padding);
    background-color: var(--ph-bg-secondary);
    border-top: var(--ph-card-border-width) solid var(--ph-card-border-color);
    border-bottom-left-radius: calc(var(--ph-card-border-radius) - var(--ph-card-border-width));
    border-bottom-right-radius: calc(var(--ph-card-border-radius) - var(--ph-card-border-width));
}

.ph-card-title {
    margin-bottom: var(--ph-spacing-sm);
    font-size: var(--ph-font-size-lg);
    font-weight: var(--ph-font-weight-semibold);
    color: var(--ph-text-primary);
}

.ph-card-subtitle {
    margin-top: calc(-0.5 * var(--ph-spacing-sm));
    margin-bottom: 0;
    font-size: var(--ph-font-size-sm);
    color: var(--ph-text-secondary);
}

.ph-card-text:last-child {
    margin-bottom: 0;
}

/* Card Variants */
.ph-card-primary {
    border-color: var(--ph-primary);
}

.ph-card-primary .ph-card-header {
    background-color: var(--ph-primary);
    color: var(--ph-white);
    border-color: var(--ph-primary);
}

.ph-card-success {
    border-color: var(--ph-success);
}

.ph-card-success .ph-card-header {
    background-color: var(--ph-success);
    color: var(--ph-white);
    border-color: var(--ph-success);
}

/* ===== ALERTS ===== */
.ph-alert {
    position: relative;
    padding: var(--ph-spacing-base) var(--ph-spacing-lg);
    margin-bottom: var(--ph-spacing-base);
    border: 1px solid transparent;
    border-radius: var(--ph-radius-md);
    display: flex;
    align-items: flex-start;
    gap: var(--ph-spacing-sm);
}

.ph-alert-icon {
    flex-shrink: 0;
    margin-top: 0.125rem;
}

.ph-alert-content {
    flex: 1;
}

.ph-alert-title {
    font-weight: var(--ph-font-weight-semibold);
    margin-bottom: var(--ph-spacing-xs);
}

.ph-alert-description {
    margin: 0;
}

.ph-alert-dismissible {
    padding-right: 3rem;
}

.ph-alert-close {
    position: absolute;
    top: 0;
    right: 0;
    z-index: 2;
    padding: 1.25rem 1rem;
    color: inherit;
    background: none;
    border: 0;
    opacity: 0.5;
    cursor: pointer;
}

.ph-alert-close:hover {
    opacity: 0.75;
}

/* Alert Variants */
.ph-alert-primary {
    color: var(--ph-primary-dark);
    background-color: var(--ph-primary-light);
    border-color: var(--ph-primary);
}

.ph-alert-success {
    color: var(--ph-success-dark);
    background-color: var(--ph-success-light);
    border-color: var(--ph-success);
}

.ph-alert-warning {
    color: var(--ph-warning-dark);
    background-color: var(--ph-warning-light);
    border-color: var(--ph-warning);
}

.ph-alert-danger {
    color: var(--ph-danger-dark);
    background-color: var(--ph-danger-light);
    border-color: var(--ph-danger);
}

.ph-alert-info {
    color: var(--ph-info-dark);
    background-color: var(--ph-info-light);
    border-color: var(--ph-info);
}

/* ===== BADGES ===== */
.ph-badge {
    display: inline-block;
    padding: 0.25em 0.5em;
    font-size: 0.75em;
    font-weight: var(--ph-font-weight-semibold);
    line-height: 1;
    color: var(--ph-white);
    text-align: center;
    white-space: nowrap;
    vertical-align: baseline;
    border-radius: var(--ph-radius-base);
}

.ph-badge-primary {
    background-color: var(--ph-primary);
}

.ph-badge-secondary {
    background-color: var(--ph-secondary);
}

.ph-badge-success {
    background-color: var(--ph-success);
}

.ph-badge-warning {
    background-color: var(--ph-warning);
    color: var(--ph-gray-900);
}

.ph-badge-danger {
    background-color: var(--ph-danger);
}

.ph-badge-info {
    background-color: var(--ph-info);
}

.ph-badge-light {
    background-color: var(--ph-light);
    color: var(--ph-gray-900);
}

.ph-badge-dark {
    background-color: var(--ph-dark);
}

/* ===== NAVIGATION ===== */
.ph-nav {
    display: flex;
    flex-wrap: wrap;
    padding-left: 0;
    margin-bottom: 0;
    list-style: none;
}

.ph-nav-link {
    display: block;
    padding: var(--ph-spacing-sm) var(--ph-spacing-base);
    color: var(--ph-primary);
    text-decoration: none;
    transition: color var(--ph-transition-fast);
    border-radius: var(--ph-radius-md);
}

.ph-nav-link:hover,
.ph-nav-link:focus {
    color: var(--ph-primary-hover);
    background-color: var(--ph-gray-100);
}

.ph-nav-link.active {
    color: var(--ph-primary);
    background-color: var(--ph-primary-light);
    font-weight: var(--ph-font-weight-medium);
}

.ph-nav-link:disabled,
.ph-nav-link.disabled {
    color: var(--ph-gray-400);
    pointer-events: none;
    cursor: default;
}

/* ===== BREADCRUMBS ===== */
.ph-breadcrumb {
    display: flex;
    flex-wrap: wrap;
    padding: var(--ph-spacing-sm) 0;
    margin-bottom: var(--ph-spacing-base);
    list-style: none;
    background-color: transparent;
}

.ph-breadcrumb-item {
    display: flex;
    align-items: center;
}

.ph-breadcrumb-item + .ph-breadcrumb-item {
    padding-left: var(--ph-spacing-sm);
}

.ph-breadcrumb-item + .ph-breadcrumb-item::before {
    content: "/";
    padding-right: var(--ph-spacing-sm);
    color: var(--ph-gray-400);
}

.ph-breadcrumb-item.active {
    color: var(--ph-gray-600);
}

/* ===== PAGINATION ===== */
.ph-pagination {
    display: flex;
    padding-left: 0;
    list-style: none;
    border-radius: var(--ph-radius-md);
}

.ph-page-link {
    position: relative;
    display: block;
    padding: var(--ph-spacing-sm) var(--ph-spacing-md);
    margin-left: -1px;
    line-height: 1.25;
    color: var(--ph-primary);
    text-decoration: none;
    background-color: var(--ph-white);
    border: 1px solid var(--ph-gray-300);
    transition: all var(--ph-transition-fast);
}

.ph-page-link:hover {
    z-index: 2;
    color: var(--ph-primary-hover);
    background-color: var(--ph-gray-100);
    border-color: var(--ph-gray-300);
}

.ph-page-link:focus {
    z-index: 3;
    outline: 0;
    box-shadow: 0 0 0 var(--ph-focus-ring-width) var(--ph-focus-ring-color);
}

.ph-page-item:first-child .ph-page-link {
    margin-left: 0;
    border-top-left-radius: var(--ph-radius-md);
    border-bottom-left-radius: var(--ph-radius-md);
}

.ph-page-item:last-child .ph-page-link {
    border-top-right-radius: var(--ph-radius-md);
    border-bottom-right-radius: var(--ph-radius-md);
}

.ph-page-item.active .ph-page-link {
    z-index: 3;
    color: var(--ph-white);
    background-color: var(--ph-primary);
    border-color: var(--ph-primary);
}

.ph-page-item.disabled .ph-page-link {
    color: var(--ph-gray-400);
    pointer-events: none;
    background-color: var(--ph-white);
    border-color: var(--ph-gray-300);
}

/* ===== LOADING STATES ===== */
.ph-spinner {
    display: inline-block;
    width: 1rem;
    height: 1rem;
    vertical-align: -0.125em;
    border: 0.125em solid currentColor;
    border-right-color: transparent;
    border-radius: 50%;
    animation: ph-spinner-border 0.75s linear infinite;
}

@keyframes ph-spinner-border {
    to {
        transform: rotate(360deg);
    }
}

.ph-spinner-sm {
    width: 0.75rem;
    height: 0.75rem;
    border-width: 0.1em;
}

.ph-spinner-lg {
    width: 1.5rem;
    height: 1.5rem;
    border-width: 0.15em;
}

/* ===== UTILITIES ===== */
.ph-shadow-sm {
    box-shadow: var(--ph-shadow-sm) !important;
}

.ph-shadow {
    box-shadow: var(--ph-shadow-base) !important;
}

.ph-shadow-md {
    box-shadow: var(--ph-shadow-md) !important;
}

.ph-shadow-lg {
    box-shadow: var(--ph-shadow-lg) !important;
}

.ph-shadow-xl {
    box-shadow: var(--ph-shadow-xl) !important;
}

.ph-shadow-none {
    box-shadow: none !important;
}

/* Responsive utilities */
@media (max-width: 767.98px) {
    .ph-btn {
        min-height: 2.75rem; /* Larger touch targets on mobile */
    }
    
    .ph-card {
        border-radius: var(--ph-radius-md);
        margin-left: -0.5rem;
        margin-right: -0.5rem;
        border-left: none;
        border-right: none;
    }
    
    .ph-alert {
        border-radius: 0;
        margin-left: -1rem;
        margin-right: -1rem;
        border-left: none;
        border-right: none;
    }
}
/* ===== 
FILTER SYSTEM ===== */
.filter-container {
    display: flex;
    flex-wrap: wrap;
    gap: var(--ph-spacing-sm);
    align-items: center;
}

.filter-badge {
    display: inline-flex;
    align-items: center;
    background-color: var(--ph-gray-200);
    color: var(--ph-gray-800);
    border-radius: var(--ph-radius-base);
    padding: 0.25rem 0.5rem;
    margin-right: 0.5rem;
    margin-bottom: 0.5rem;
    font-size: var(--ph-font-size-sm);
}

.filter-badge .btn-close {
    font-size: 0.5rem;
    margin-left: 0.5rem;
    opacity: 0.5;
}

.filter-badge .btn-close:hover {
    opacity: 1;
}

.city-results-dropdown {
    max-height: 200px;
    overflow-y: auto;
    box-shadow: var(--ph-shadow-md);
    border: 1px solid var(--ph-gray-200);
    border-radius: var(--ph-radius-base);
    background-color: var(--ph-white);
    z-index: 1050;
}

.city-option {
    padding: 0.5rem 1rem;
    cursor: pointer;
    transition: background-color var(--ph-transition-fast);
}

.city-option:hover {
    background-color: var(--ph-gray-100);
}

/* ===== EXPORT SYSTEM ===== */
.export-format-option {
    display: flex;
    align-items: center;
    padding: 1rem;
    border: 1px solid var(--ph-gray-200);
    border-radius: var(--ph-radius-base);
    margin-bottom: 0.5rem;
    cursor: pointer;
    transition: all var(--ph-transition-fast);
}

.export-format-option:hover {
    background-color: var(--ph-gray-50);
    border-color: var(--ph-gray-300);
}

.export-format-option.selected {
    background-color: var(--ph-primary-light);
    border-color: var(--ph-primary);
}

.export-format-icon {
    font-size: 1.5rem;
    margin-right: 1rem;
}

.export-format-details {
    flex: 1;
}

.export-format-title {
    font-weight: var(--ph-font-weight-medium);
    margin-bottom: 0.25rem;
}

.export-format-description {
    font-size: var(--ph-font-size-sm);
    color: var(--ph-gray-600);
}

/* Responsive adjustments */
@media (max-width: 767.98px) {
    .filter-container {
        flex-direction: column;
        align-items: stretch;
    }
    
    .filter-container > * {
        width: 100%;
    }
    
    .export-container {
        margin-top: var(--ph-spacing-base);
    }
}