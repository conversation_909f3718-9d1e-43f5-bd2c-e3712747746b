using System.Collections.Generic;

namespace ParaHockey.Models.ViewModels
{
    public class CalendarOptionsViewModel
    {
        public bool ShowFilters { get; set; } = true;
        public bool IsReadOnly { get; set; } = true;
        public bool IsMiniView { get; set; } = false;
        public List<CategoryFilterViewModel> Categories { get; set; } = new List<CategoryFilterViewModel>();
        public int? SelectedCategoryId { get; set; }
    }

    public class CategoryFilterViewModel
    {
        public int Id { get; set; }
        public string Name { get; set; } = string.Empty;
        public string? Color { get; set; }
        public string? DisplayNameKey { get; set; }
    }
}