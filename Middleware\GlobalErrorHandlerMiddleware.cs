using Microsoft.Extensions.Localization;
using ParaHockeyApp.DTOs;
using ParaHockeyApp.Resources;
using ParaHockeyApp.Services;
using System.Net;
using System.Text.Json;

namespace ParaHockeyApp.Middleware
{
    /// <summary>
    /// Global error handling middleware that catches unhandled exceptions and provides
    /// consistent error responses across all environments while respecting environment-specific settings.
    /// </summary>
    public class GlobalErrorHandlerMiddleware
    {
        private readonly RequestDelegate _next;
        private readonly ILogger<GlobalErrorHandlerMiddleware> _logger;
        private readonly IStringLocalizer<SharedResourceMarker> _localizer;
        private readonly IServiceProvider _serviceProvider;

        public GlobalErrorHandlerMiddleware(
            RequestDelegate next,
            ILogger<GlobalErrorHandlerMiddleware> logger,
            IStringLocalizer<SharedResourceMarker> localizer,
            IServiceProvider serviceProvider)
        {
            _next = next;
            _logger = logger;
            _localizer = localizer;
            _serviceProvider = serviceProvider;
        }

        public async Task InvokeAsync(HttpContext context)
        {
            try
            {
                await _next(context);
            }
            catch (Exception exception)
            {
                _logger.LogError(exception, 
                    "An unhandled exception occurred. Request: {Method} {Path} from {RemoteIp}",
                    context.Request.Method, 
                    context.Request.Path, 
                    context.Connection.RemoteIpAddress);

                await HandleExceptionAsync(context, exception);
            }
        }

        private async Task HandleExceptionAsync(HttpContext context, Exception exception)
        {
            context.Response.ContentType = "application/json";

            // Get environment configuration to determine how to handle the error
            using var scope = _serviceProvider.CreateScope();
            var envConfigService = scope.ServiceProvider.GetService<IEnvironmentConfigurationService>();
            var errorConfig = envConfigService?.GetErrorHandlingConfiguration();

            var errorId = Guid.NewGuid().ToString();
            var errorResponse = CreateErrorResponse(exception, errorId, errorConfig);

            // Set appropriate HTTP status code
            context.Response.StatusCode = GetStatusCode(exception);

            // Log detailed error information if configured
            if (errorConfig?.LogStackTraces == true)
            {
                _logger.LogError(exception, 
                    "Error ID {ErrorId}: {ExceptionType} - {Message}. Stack trace: {StackTrace}",
                    errorId, 
                    exception.GetType().Name, 
                    exception.Message, 
                    exception.StackTrace);
            }
            else
            {
                _logger.LogError(exception, 
                    "Error ID {ErrorId}: {ExceptionType} - {Message}",
                    errorId, 
                    exception.GetType().Name, 
                    exception.Message);
            }

            // Return appropriate response based on request type
            if (IsApiRequest(context))
            {
                // Return JSON response for API requests
                var json = JsonSerializer.Serialize(errorResponse);
                await context.Response.WriteAsync(json);
            }
            else
            {
                // Redirect to error page for MVC requests
                await HandleMvcErrorAsync(context, errorResponse);
            }
        }

        /// <summary>
        /// Creates an appropriate error response based on the exception type and environment configuration.
        /// </summary>
        private ErrorResponseModel CreateErrorResponse(Exception exception, string errorId, ErrorHandlingConfiguration? errorConfig)
        {
            var showDetailedErrors = errorConfig?.ShowDetailedErrors ?? false;
            var showUserFriendlyMessages = errorConfig?.ShowUserFriendlyMessages ?? true;

            return exception switch
            {
                ArgumentException or ArgumentNullException => ErrorResponseModel.CreateValidationError(
                    showDetailedErrors ? exception.Message : _localizer["Error_ValidationFailed"]),

                UnauthorizedAccessException => ErrorResponseModel.Create(
                    showDetailedErrors ? exception.Message : _localizer["Error_Unauthorized"], 
                    false, 401),

                FileNotFoundException or DirectoryNotFoundException => ErrorResponseModel.CreateNotFoundError(
                    showDetailedErrors ? "resource" : _localizer["Error_ResourceNotFound"]),

                TimeoutException => ErrorResponseModel.Create(
                    showDetailedErrors ? exception.Message : _localizer["Error_RequestTimeout"], 
                    true, 408),

                InvalidOperationException => ErrorResponseModel.Create(
                    showDetailedErrors ? exception.Message : _localizer["Error_InvalidOperation"], 
                    false, 400),

                NotSupportedException => ErrorResponseModel.Create(
                    showDetailedErrors ? exception.Message : _localizer["Error_OperationNotSupported"], 
                    false, 501),

                // Database-related exceptions
                var dbException when IsDatabaseException(dbException) => ErrorResponseModel.CreateDatabaseError(
                    showDetailedErrors ? exception.Message : _localizer["Error_DatabaseError"]),

                // Default case for all other exceptions
                _ => ErrorResponseModel.Create(
                    showUserFriendlyMessages ? _localizer["Error_UnexpectedError"] : exception.Message,
                    false, 500)
            };

            bool IsDatabaseException(Exception ex)
            {
                var typeName = ex.GetType().Name;
                return typeName.Contains("Sql") || 
                       typeName.Contains("Database") || 
                       typeName.Contains("Connection") ||
                       ex.Message.Contains("database", StringComparison.OrdinalIgnoreCase);
            }
        }

        /// <summary>
        /// Determines the appropriate HTTP status code for the exception.
        /// </summary>
        private static int GetStatusCode(Exception exception) => exception switch
        {
            ArgumentException or ArgumentNullException => 400, // Bad Request
            UnauthorizedAccessException => 401, // Unauthorized
            FileNotFoundException or DirectoryNotFoundException => 404, // Not Found
            TimeoutException => 408, // Request Timeout
            InvalidOperationException => 400, // Bad Request
            NotSupportedException => 501, // Not Implemented
            _ => 500 // Internal Server Error
        };

        /// <summary>
        /// Determines if the request is an API request based on the path and accept headers.
        /// </summary>
        private static bool IsApiRequest(HttpContext context)
        {
            var path = context.Request.Path.ToString();
            var acceptHeader = context.Request.Headers["Accept"].ToString();

            return path.StartsWith("/api/", StringComparison.OrdinalIgnoreCase) ||
                   acceptHeader.Contains("application/json", StringComparison.OrdinalIgnoreCase);
        }

        /// <summary>
        /// Handles MVC errors by redirecting to an appropriate error page or returning HTML content.
        /// </summary>
        private async Task HandleMvcErrorAsync(HttpContext context, ErrorResponseModel errorResponse)
        {
            try
            {
                // For MVC requests, we'll store the error in TempData and redirect to error page
                context.Items["ErrorResponse"] = errorResponse;
                
                // Try to redirect to custom error page if it exists
                if (context.Response.StatusCode == 404)
                {
                    context.Request.Path = "/Home/NotFound";
                }
                else
                {
                    context.Request.Path = "/Home/Error";
                }

                // Execute the error page
                await _next(context);
            }
            catch
            {
                // If error page fails, return basic HTML response
                context.Response.ContentType = "text/html";
                var htmlResponse = $@"
                    <!DOCTYPE html>
                    <html>
                    <head>
                        <title>Error</title>
                        <meta charset='utf-8'>
                        <meta name='viewport' content='width=device-width, initial-scale=1.0'>
                        <link href='/css/site.css' rel='stylesheet'>
                    </head>
                    <body>
                        <div class='container mt-5'>
                            <div class='row justify-content-center'>
                                <div class='col-md-8'>
                                    <div class='alert alert-danger'>
                                        <h4><i class='fas fa-exclamation-triangle'></i> Error</h4>
                                        <p>{errorResponse.UserMessage}</p>
                                        <p class='mb-0'><small>Error ID: {errorResponse.ErrorId}</small></p>
                                    </div>
                                    <a href='/' class='btn btn-primary'>Return to Home</a>
                                </div>
                            </div>
                        </div>
                    </body>
                    </html>";

                await context.Response.WriteAsync(htmlResponse);
            }
        }
    }
}