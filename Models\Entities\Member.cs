using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace ParaHockeyApp.Models.Entities
{
    /// <summary>
    /// Player entity representing a Para Hockey player registration.
    /// This is the primary table for member information.
    /// </summary>
    public class Member : BaseEntity
    {
        // HQ Registration ID - For admin use. Null by default, but must be unique when populated.
        [StringLength(50)]
        public string? HQc_Id { get; set; }

        // Basic Information
        [Required]
        [StringLength(50)]
        public string FirstName { get; set; } = string.Empty;

        [Required]
        [StringLength(50)]
        public string LastName { get; set; } = string.Empty;

        [Required]
        [DataType(DataType.Date)]
        public DateTime DateOfBirth { get; set; }

        // Address Information
        [Required]
        [StringLength(200)]
        public string Address { get; set; } = string.Empty;

        [Required]
        [StringLength(100)]
        public string City { get; set; } = string.Empty;

        [Required]
        [StringLength(10)]
        public string PostalCode { get; set; } = string.Empty;

        // Contact Information
        [Required]
        [StringLength(20)]
        public string Phone { get; set; } = string.Empty;

        [Required]
        [StringLength(254)]
        [EmailAddress]
        public string Email { get; set; } = string.Empty;

        // Foreign key to IdentityUser
        public string? UserId { get; set; }
        [ForeignKey("UserId")]
        public virtual AppUser? User { get; set; }

        // --- Foreign Keys to Lookup Tables ---

        [Required]
        public int GenderId { get; set; }
        public virtual Gender Gender { get; set; } = null!;

        [Required]
        public int ProvinceId { get; set; }
        public virtual Province Province { get; set; } = null!;

        [Required]
        public int PhoneTypeId { get; set; }
        public virtual PhoneType PhoneType { get; set; } = null!;

        [Required]
        public int RegistrationTypeId { get; set; }
        public virtual RegistrationType RegistrationType { get; set; } = null!;

        // --- Navigation Properties ---

        /// <summary>
        /// Collection of all log entries for this member (audit trail)
        /// </summary>
        public virtual ICollection<MemberLog> MemberLogs { get; set; } = new List<MemberLog>();

        // --- Helper Properties (Not mapped to the database) ---

        [NotMapped]
        public string FullName => $"{FirstName} {LastName}";

        [NotMapped]
        public int Age => DateTime.Now.Year - DateOfBirth.Year -
                         (DateTime.Now.DayOfYear < DateOfBirth.DayOfYear ? 1 : 0);

        public override bool EstValide =>
            !string.IsNullOrWhiteSpace(FirstName) &&
            !string.IsNullOrWhiteSpace(LastName) &&
            !string.IsNullOrWhiteSpace(Email) &&
            IsActive;
    }
}