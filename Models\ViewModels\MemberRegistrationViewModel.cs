using Microsoft.AspNetCore.Mvc.Rendering;
using System.ComponentModel.DataAnnotations;
using System.Collections.Generic;
using System.Linq;
using ParaHockeyApp.Models.Configuration;
using Microsoft.Extensions.Localization;

namespace ParaHockeyApp.Models.ViewModels
{
    public class MemberRegistrationViewModel : IValidatableObject
    {
        // HQ Registration ID - Admin only field
        [StringLength(50, ErrorMessageResourceName = "ValidationStringLength", ErrorMessageResourceType = typeof(Resources.SharedResource))]
        [Display(Name = "HQc_Id")]
        public string? HQc_Id { get; set; }

        // Properties to bind form inputs
        [Required(ErrorMessageResourceName = "ValidationRequired", ErrorMessageResourceType = typeof(Resources.SharedResource))]
        [StringLength(50, MinimumLength = 2, ErrorMessageResourceName = "ValidationStringLength", ErrorMessageResourceType = typeof(Resources.SharedResource))]
        [Display(Name = "FirstName")]
        public string FirstName { get; set; } = string.Empty;

        [Required(ErrorMessageResourceName = "ValidationRequired", ErrorMessageResourceType = typeof(Resources.SharedResource))]
        [StringLength(50, MinimumLength = 2, ErrorMessageResourceName = "ValidationStringLength", ErrorMessageResourceType = typeof(Resources.SharedResource))]
        [Display(Name = "LastName")]
        public string LastName { get; set; } = string.Empty;

        [Required(ErrorMessageResourceName = "ValidationRequired", ErrorMessageResourceType = typeof(Resources.SharedResource))]
        [DataType(DataType.Date)]
        [Display(Name = "DateOfBirth")]
        public DateTime? DateOfBirth { get; set; }

        [Required(ErrorMessageResourceName = "ValidationRequired", ErrorMessageResourceType = typeof(Resources.SharedResource))]
        [StringLength(200, MinimumLength = 5, ErrorMessageResourceName = "ValidationAddressTooShort", ErrorMessageResourceType = typeof(Resources.SharedResource))]
        [Display(Name = "Address")]
        public string Address { get; set; } = string.Empty;

        [Required(ErrorMessageResourceName = "ValidationRequired", ErrorMessageResourceType = typeof(Resources.SharedResource))]
        [StringLength(100, MinimumLength = 2, ErrorMessageResourceName = "ValidationCityTooShort", ErrorMessageResourceType = typeof(Resources.SharedResource))]
        [Display(Name = "City")]
        public string City { get; set; } = string.Empty;

        [Required(ErrorMessageResourceName = "ValidationRequired", ErrorMessageResourceType = typeof(Resources.SharedResource))]
        [RegularExpression(@"^[A-Za-z]\d[A-Za-z][\s-]?\d[A-Za-z]\d$", ErrorMessageResourceName = "ValidationPostalCodeSpecific", ErrorMessageResourceType = typeof(Resources.SharedResource))]
        [StringLength(10)]
        [Display(Name = "PostalCode")]
        public string PostalCode { get; set; } = string.Empty;

        [Required(ErrorMessageResourceName = "ValidationRequired", ErrorMessageResourceType = typeof(Resources.SharedResource))]
        [RegularExpression(@"^\(?\d{3}\)?[\s.-]?\d{3}[\s.-]?\d{4}$", ErrorMessageResourceName = "ValidationPhoneSpecific", ErrorMessageResourceType = typeof(Resources.SharedResource))]
        [Display(Name = "Phone")]
        public string Phone { get; set; } = string.Empty;

        [Required(ErrorMessageResourceName = "ValidationRequired", ErrorMessageResourceType = typeof(Resources.SharedResource))]
        [RegularExpression(@"^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$", ErrorMessageResourceName = "ValidationEmailSpecific", ErrorMessageResourceType = typeof(Resources.SharedResource))]
        [Display(Name = "Email")]
        public string Email { get; set; } = string.Empty;

        // --- Foreign Key IDs ---
        [Required(ErrorMessageResourceName = "ValidationGenderRequired", ErrorMessageResourceType = typeof(Resources.SharedResource))]
        [Display(Name = "Gender")]
        public int GenderId { get; set; }

        [Required(ErrorMessageResourceName = "ValidationProvinceRequired", ErrorMessageResourceType = typeof(Resources.SharedResource))]
        [Display(Name = "Province")]
        public int ProvinceId { get; set; }

        [Required(ErrorMessageResourceName = "ValidationPhoneTypeRequired", ErrorMessageResourceType = typeof(Resources.SharedResource))]
        [Display(Name = "PhoneType")]
        public int PhoneTypeId { get; set; }

        [Required(ErrorMessageResourceName = "ValidationRegistrationTypeRequired", ErrorMessageResourceType = typeof(Resources.SharedResource))]
        [Display(Name = "RegistrationType")]
        public int RegistrationTypeId { get; set; }

        // --- Lists for dropdowns and radio buttons ---
        public IEnumerable<SelectListItem> Genders { get; set; } = new List<SelectListItem>();
        public IEnumerable<SelectListItem> Provinces { get; set; } = new List<SelectListItem>();
        public IEnumerable<SelectListItem> PhoneTypes { get; set; } = new List<SelectListItem>();
        public IEnumerable<SelectListItem> RegistrationTypes { get; set; } = new List<SelectListItem>();

        // --- Parent & Emergency Contact ---
        public List<ParentViewModel> Parents { get; set; } = new List<ParentViewModel>();
        public EmergencyContactViewModel EmergencyContact { get; set; } = new EmergencyContactViewModel();

        // --- Environment Settings ---
        public EnvironmentSettings EnvironmentSettings { get; set; } = new EnvironmentSettings();

        public IEnumerable<ValidationResult> Validate(ValidationContext validationContext)
        {
            // Get localizer from service provider in validation context
            var localizer = validationContext.GetService(typeof(IStringLocalizer<Resources.SharedResourceMarker>)) as IStringLocalizer<Resources.SharedResourceMarker>;
            
            // Fallback messages if localizer is not available
            var parentRequiredMessage = localizer?["ValidationParentRequired"] ?? "At least one parent is required for minors under 18.";
            var emergencyContactRequiredMessage = localizer?["ValidationEmergencyContactRequired"] ?? "Emergency contact is required.";
            
            // Skip all parent/emergency validation if no DateOfBirth provided
            if (!DateOfBirth.HasValue)
            {
                // No age-based validation if DOB is missing
                yield break;
            }
            
            // Calculate age from DateOfBirth to determine if parent or emergency contact validation is needed
            var today = DateTime.Today;
            var age = today.Year - DateOfBirth.Value.Year;
            if (DateOfBirth.Value.Date > today.AddYears(-age)) age--;
            
            // Use age of majority (18) to determine form type - same logic as JavaScript
            var ageOfMajority = 18; // Should match EnvironmentSettings.AgeOfMajority
            
            if (age < ageOfMajority)
            {
                // Validate Parents for Junior registration
                if (Parents == null || !Parents.Any())
                {
                    yield return new ValidationResult(parentRequiredMessage, new[] { nameof(Parents) });
                }
                else
                {
                    // Only validate parents that have at least some data entered
                    var parentsWithData = Parents.Where((parent, index) => parent != null && IsParentPartiallyFilled(parent))
                                                .Select((parent, index) => new { Parent = parent, Index = index })
                                                .ToList();

                    // Require at least one parent with data for Junior registration
                    if (!parentsWithData.Any())
                    {
                        yield return new ValidationResult(parentRequiredMessage, new[] { nameof(Parents) });
                    }
                    else
                    {
                        // Validate each parent that has data
                        foreach (var parentData in parentsWithData)
                        {
                            var parent = parentData.Parent;
                            var actualIndex = Parents.IndexOf(parent);
                            
                            // Manually validate required fields for parents with data
                            if (string.IsNullOrWhiteSpace(parent.FirstName))
                            {
                                yield return new ValidationResult(
                                    localizer?["ValidationRequired"] ?? "Le champ Prénom est requis.", 
                                    new[] { $"{nameof(Parents)}[{actualIndex}].{nameof(parent.FirstName)}" });
                            }
                            if (string.IsNullOrWhiteSpace(parent.LastName))
                            {
                                yield return new ValidationResult(
                                    localizer?["ValidationRequired"] ?? "Le champ Nom est requis.", 
                                    new[] { $"{nameof(Parents)}[{actualIndex}].{nameof(parent.LastName)}" });
                            }
                            if (string.IsNullOrWhiteSpace(parent.ParentType))
                            {
                                yield return new ValidationResult(
                                    localizer?["ValidationRequired"] ?? "Le champ Type de parent est requis.", 
                                    new[] { $"{nameof(Parents)}[{actualIndex}].{nameof(parent.ParentType)}" });
                            }
                            if (string.IsNullOrWhiteSpace(parent.Phone))
                            {
                                yield return new ValidationResult(
                                    localizer?["ValidationRequired"] ?? "Le champ Téléphone est requis.", 
                                    new[] { $"{nameof(Parents)}[{actualIndex}].{nameof(parent.Phone)}" });
                            }
                            
                            // Validate other attributes (StringLength, RegularExpression, etc.)
                            var results = new List<ValidationResult>();
                            var context = new ValidationContext(parent, serviceProvider: validationContext, items: null);
                            Validator.TryValidateObject(parent, context, results, validateAllProperties: true);
                            
                            foreach (var validationResult in results)
                            {
                                yield return new ValidationResult(validationResult.ErrorMessage, validationResult.MemberNames.Select(name => $"{nameof(Parents)}[{actualIndex}].{name}"));
                            }
                        }
                    }
                }
            }
            else
            {
                // Validate EmergencyContact for adult registration (age >= 18)
                if (EmergencyContact == null)
                {
                    yield return new ValidationResult(emergencyContactRequiredMessage, new[] { nameof(EmergencyContact) });
                }
                else
                {
                    var results = new List<ValidationResult>();
                    var context = new ValidationContext(EmergencyContact, serviceProvider: validationContext, items: null);
                    if (!Validator.TryValidateObject(EmergencyContact, context, results, validateAllProperties: true))
                    {
                        foreach (var validationResult in results)
                        {
                            // Prefix the member name to the error message
                            yield return new ValidationResult(validationResult.ErrorMessage, validationResult.MemberNames.Select(name => $"{nameof(EmergencyContact)}.{name}"));
                        }
                    }
                }
            }
        }

        // Helper method to check if a parent has at least some data filled
        private bool IsParentPartiallyFilled(ParentViewModel parent)
        {
            if (parent == null) return false;
            
            return !string.IsNullOrWhiteSpace(parent.FirstName) ||
                   !string.IsNullOrWhiteSpace(parent.LastName) ||
                   !string.IsNullOrWhiteSpace(parent.ParentType) ||
                   !string.IsNullOrWhiteSpace(parent.Phone) ||
                   !string.IsNullOrWhiteSpace(parent.Email);
        }
    }
}