# 🚨 UNRESOLVED ISSUES - ParaHockey E2E Tests

**Created**: January 4, 2025  
**Status**: NEEDS INVESTIGATION - ALL TESTS STILL FAILING

## ❌ CRITICAL ISSUE: Test Results Not Displaying

### Problem Summary
Despite multiple attempts to fix the test result transmission and display system, the core issue persists:

1. **ALL tests are failing** (151 tests, all with ChromeDriver version mismatch)
2. **400 Bad Request error** when sending test results to API (92.29 KB payload, 151 tests)
3. **Test results page shows "No Recent Test Results"** - the API submission is completely failing
4. **User frustration**: "things keep breaking everytime you change something"

### What Was Attempted
- ✅ Fixed ASP.NET Core request size limits (Kestrel, FormOptions, JSON depth)
- ✅ Increased PowerShell timeout from 10s to 60s
- ✅ Added payload summarization for large test sets (>2MB)
- ✅ Enhanced error logging and payload size monitoring
- ✅ Completely rewrote README for simplicity

### Current Behavior
```
📊 Sending test results payload: 92.29 KB
⚠️  Could not send test results to application: Le serveur distant a retourné une erreur : (400) Demande incorrecte.
   Payload size: 92.29 KB, Tests: 151
```

### Root Cause Analysis Needed
The 400 Bad Request error suggests:
1. **Server-side validation failing** on the TestRunResultDto
2. **Model binding issues** with the JSON payload structure
3. **Controller endpoint problems** in `/api/testresults`
4. **Authentication/Authorization** issues blocking the POST request

### ChromeDriver Issue
**ALL 151 tests failing** with same error:
```
System.InvalidOperationException : session not created: This version of ChromeDriver only supports Chrome version 138
Current browser version is 137.0.7151.122
```

This indicates a fundamental WebDriver setup problem that needs addressing first.

### Next Steps Required
1. **Fix ChromeDriver version mismatch** - Update WebDriverManager or Chrome version
2. **Debug API endpoint** - Add detailed logging to TestController.SubmitTestResults
3. **Test with smaller payload** - Verify if HTTP tests (3 tests) can submit successfully
4. **Model validation** - Check if TestRunResultDto properties are causing validation errors
5. **Authentication check** - Verify if [IgnoreAntiforgeryToken] is working correctly

### Test Categories Status
Based on previous analysis, these SHOULD work once ChromeDriver is fixed:
- ✅ HTTP (no browser) - 3 tests, works independently  
- ✅ Smoke (basic browser) - 3 tests
- ⚠️ ALL other categories failing due to ChromeDriver

### User Request
User specifically requested to **"save someplace that this isn't fixed at all"** and expressed frustration that **"nothing changes prompt after prompt"**.

The fundamental issue is that the test infrastructure itself is broken (ChromeDriver + API submission), not just the result display system.

---

## 🔄 Action Items for Future Work

1. **PRIORITY 1**: Fix ChromeDriver version compatibility
2. **PRIORITY 2**: Debug TestController.SubmitTestResults API endpoint with detailed logging
3. **PRIORITY 3**: Test with simple HTTP category first (only 3 tests)
4. **PRIORITY 4**: Verify model binding and validation for TestRunResultDto
5. **PRIORITY 5**: Consider fallback approach - file-based result storage if API continues failing

**User expectation**: Working test system that actually displays results, not complex fixes that don't resolve the core problem.