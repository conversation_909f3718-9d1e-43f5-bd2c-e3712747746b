using Microsoft.AspNetCore.Mvc.Rendering;
using Microsoft.AspNetCore.Mvc.ViewFeatures;
using Microsoft.AspNetCore.Razor.TagHelpers;
using Microsoft.Extensions.Localization;
using ParaHockeyApp.ViewModels.Forms;
using ParaHockeyApp.Resources;

namespace ParaHockeyApp.TagHelpers
{
    /// <summary>
    /// Enhanced form tag helper with accessibility and validation features
    /// </summary>
    [HtmlTargetElement("enhanced-form")]
    public class EnhancedFormTagHelper : TagHelper
    {
        private readonly IStringLocalizer<SharedResourceMarker> _localizer;

        [HtmlAttributeNotBound]
        [ViewContext]
        public ViewContext ViewContext { get; set; } = null!;

        [HtmlAttributeName("model")]
        public BaseFormViewModel Model { get; set; } = null!;

        [HtmlAttributeName("action")]
        public string Action { get; set; } = string.Empty;

        [HtmlAttributeName("method")]
        public string Method { get; set; } = "post";

        [HtmlAttributeName("css-class")]
        public string CssClass { get; set; } = "enhanced-form";

        [HtmlAttributeName("show-validation-summary")]
        public bool ShowValidationSummary { get; set; } = true;

        public EnhancedFormTagHelper(IStringLocalizer<SharedResourceMarker> localizer)
        {
            _localizer = localizer;
        }

        public override void Process(TagHelperContext context, TagHelperOutput output)
        {
            output.TagName = "form";
            output.Attributes.SetAttribute("method", Method);
            output.Attributes.SetAttribute("action", Action);
            output.Attributes.SetAttribute("class", CssClass);
            output.Attributes.SetAttribute("novalidate", "novalidate"); // Use custom validation
            output.Attributes.SetAttribute("role", "form");

            // Add ARIA attributes for accessibility
            output.Attributes.SetAttribute("aria-label", _localizer["RegistrationFormTitle"]);

            var content = output.GetChildContentAsync().Result;
            var formContent = new TagBuilder("div");
            formContent.AddCssClass("form-content");

            // Add validation summary if requested
            if (ShowValidationSummary)
            {
                var validationSummary = CreateValidationSummary();
                formContent.InnerHtml.AppendHtml(validationSummary);
            }

            // Add the original content
            formContent.InnerHtml.AppendHtml(content);

            // Add hidden anti-forgery token
            var antiForgeryToken = CreateAntiForgeryToken();
            formContent.InnerHtml.AppendHtml(antiForgeryToken);

            output.Content.SetHtmlContent(formContent);
        }

        private TagBuilder CreateValidationSummary()
        {
            var summary = new TagBuilder("div");
            summary.AddCssClass("validation-summary");
            summary.AddCssClass("alert");
            summary.AddCssClass("alert-danger");
            summary.Attributes.Add("role", "alert");
            summary.Attributes.Add("aria-live", "polite");
            summary.Attributes.Add("style", "display: none;");

            var title = new TagBuilder("h4");
            title.InnerHtml.Append(_localizer["ValidationErrors"]);
            summary.InnerHtml.AppendHtml(title);

            var list = new TagBuilder("ul");
            list.AddCssClass("validation-errors");
            summary.InnerHtml.AppendHtml(list);

            return summary;
        }

        private TagBuilder CreateAntiForgeryToken()
        {
            var token = new TagBuilder("input");
            token.Attributes.Add("type", "hidden");
            token.Attributes.Add("name", "__RequestVerificationToken");
            
            // In a real implementation, this would get the actual token
            // For now, we'll let the framework handle it
            return token;
        }
    }

    /// <summary>
    /// Enhanced input tag helper with validation and accessibility features
    /// </summary>
    [HtmlTargetElement("enhanced-input")]
    public class EnhancedInputTagHelper : TagHelper
    {
        private readonly IStringLocalizer<SharedResourceMarker> _localizer;

        [HtmlAttributeNotBound]
        [ViewContext]
        public ViewContext ViewContext { get; set; } = null!;

        [HtmlAttributeName("for")]
        public string For { get; set; } = string.Empty;

        [HtmlAttributeName("model")]
        public BaseFormViewModel Model { get; set; } = null!;

        [HtmlAttributeName("label")]
        public string? Label { get; set; }

        [HtmlAttributeName("placeholder")]
        public string? Placeholder { get; set; }

        [HtmlAttributeName("help-text")]
        public string? HelpText { get; set; }

        [HtmlAttributeName("required")]
        public bool Required { get; set; }

        [HtmlAttributeName("css-class")]
        public string CssClass { get; set; } = "form-control";

        [HtmlAttributeName("container-class")]
        public string ContainerClass { get; set; } = "form-group";

        public EnhancedInputTagHelper(IStringLocalizer<SharedResourceMarker> localizer)
        {
            _localizer = localizer;
        }

        public override void Process(TagHelperContext context, TagHelperOutput output)
        {
            output.TagName = "div";
            output.Attributes.SetAttribute("class", ContainerClass);

            var fieldId = $"field-{For}";
            var errorId = $"error-{For}";
            var helpId = $"help-{For}";

            // Create label
            var label = CreateLabel(fieldId);
            output.Content.AppendHtml(label);

            // Create input
            var input = CreateInput(fieldId, errorId, helpId);
            output.Content.AppendHtml(input);

            // Create help text if provided
            if (!string.IsNullOrWhiteSpace(HelpText))
            {
                var help = CreateHelpText(helpId);
                output.Content.AppendHtml(help);
            }

            // Create error container
            var error = CreateErrorContainer(errorId);
            output.Content.AppendHtml(error);
        }

        private TagBuilder CreateLabel(string fieldId)
        {
            var label = new TagBuilder("label");
            label.Attributes.Add("for", fieldId);
            label.AddCssClass("form-label");

            var labelText = Label ?? _localizer[For];
            label.InnerHtml.Append(labelText);

            if (Required)
            {
                var required = new TagBuilder("span");
                required.AddCssClass("required");
                required.Attributes.Add("aria-label", _localizer["Required"]);
                required.InnerHtml.Append(" *");
                label.InnerHtml.AppendHtml(required);
            }

            return label;
        }

        private TagBuilder CreateInput(string fieldId, string errorId, string helpId)
        {
            var input = new TagBuilder("input");
            input.Attributes.Add("id", fieldId);
            input.Attributes.Add("name", For);
            input.AddCssClass(CssClass);

            // Get HTML5 validation attributes from model
            if (Model != null)
            {
                var html5Attrs = Model.GetHtml5ValidationAttributes(For);
                foreach (var attr in html5Attrs)
                {
                    input.Attributes.Add(attr.Key, attr.Value);
                }

                // Add client validation attributes for unobtrusive validation
                var validationAttrs = Model.GetClientValidationAttributes(For);
                foreach (var attr in validationAttrs)
                {
                    if (!input.Attributes.ContainsKey(attr.Key))
                    {
                        input.Attributes.Add(attr.Key, attr.Value.ToString());
                    }
                }
            }
            else
            {
                // Fallback if no model
                input.Attributes.Add("type", "text");
            }

            // Add placeholder (from attribute or model)
            var placeholder = Placeholder ?? Model?.GetPlaceholder(For);
            if (!string.IsNullOrWhiteSpace(placeholder))
            {
                input.Attributes.Add("placeholder", placeholder);
            }

            // Add accessibility attributes
            var describedBy = new List<string>();
            if (!string.IsNullOrWhiteSpace(helpId))
                describedBy.Add(helpId);
            if (!string.IsNullOrWhiteSpace(errorId))
                describedBy.Add(errorId);
            
            if (describedBy.Any())
            {
                input.Attributes.Add("aria-describedby", string.Join(" ", describedBy));
            }
            
            // Required attribute handling
            if (Required || input.Attributes.ContainsKey("required"))
            {
                input.Attributes["required"] = "required";
                input.Attributes["aria-required"] = "true";
            }

            // Add spellcheck attribute for text inputs
            var inputType = input.Attributes.GetValueOrDefault("type", "text");
            if (inputType == "text" || inputType == "textarea")
            {
                input.Attributes.Add("spellcheck", "true");
            }

            return input;
        }

        private TagBuilder CreateHelpText(string helpId)
        {
            var help = new TagBuilder("small");
            help.Attributes.Add("id", helpId);
            help.AddCssClass("form-text");
            help.AddCssClass("text-muted");
            help.InnerHtml.Append(HelpText!);
            return help;
        }

        private TagBuilder CreateErrorContainer(string errorId)
        {
            var error = new TagBuilder("div");
            error.Attributes.Add("id", errorId);
            error.AddCssClass("invalid-feedback");
            error.Attributes.Add("role", "alert");
            error.Attributes.Add("aria-live", "polite");
            return error;
        }
    }

    /// <summary>
    /// Enhanced select tag helper for dropdowns with validation
    /// </summary>
    [HtmlTargetElement("enhanced-select")]
    public class EnhancedSelectTagHelper : TagHelper
    {
        private readonly IStringLocalizer<SharedResourceMarker> _localizer;

        [HtmlAttributeNotBound]
        [ViewContext]
        public ViewContext ViewContext { get; set; } = null!;

        [HtmlAttributeName("for")]
        public string For { get; set; } = string.Empty;

        [HtmlAttributeName("model")]
        public BaseFormViewModel Model { get; set; } = null!;

        [HtmlAttributeName("label")]
        public string? Label { get; set; }

        [HtmlAttributeName("options")]
        public IEnumerable<SelectListItem> Options { get; set; } = Enumerable.Empty<SelectListItem>();

        [HtmlAttributeName("placeholder")]
        public string? Placeholder { get; set; }

        [HtmlAttributeName("required")]
        public bool Required { get; set; }

        [HtmlAttributeName("css-class")]
        public string CssClass { get; set; } = "form-select";

        [HtmlAttributeName("container-class")]
        public string ContainerClass { get; set; } = "form-group";

        public EnhancedSelectTagHelper(IStringLocalizer<SharedResourceMarker> localizer)
        {
            _localizer = localizer;
        }

        public override void Process(TagHelperContext context, TagHelperOutput output)
        {
            output.TagName = "div";
            output.Attributes.SetAttribute("class", ContainerClass);

            var fieldId = $"field-{For}";
            var errorId = $"error-{For}";

            // Create label
            var label = CreateLabel(fieldId);
            output.Content.AppendHtml(label);

            // Create select
            var select = CreateSelect(fieldId, errorId);
            output.Content.AppendHtml(select);

            // Create error container
            var error = CreateErrorContainer(errorId);
            output.Content.AppendHtml(error);
        }

        private TagBuilder CreateLabel(string fieldId)
        {
            var label = new TagBuilder("label");
            label.Attributes.Add("for", fieldId);
            label.AddCssClass("form-label");

            var labelText = Label ?? _localizer[For];
            label.InnerHtml.Append(labelText);

            if (Required)
            {
                var required = new TagBuilder("span");
                required.AddCssClass("required");
                required.Attributes.Add("aria-label", _localizer["Required"]);
                required.InnerHtml.Append(" *");
                label.InnerHtml.AppendHtml(required);
            }

            return label;
        }

        private TagBuilder CreateSelect(string fieldId, string errorId)
        {
            var select = new TagBuilder("select");
            select.Attributes.Add("id", fieldId);
            select.Attributes.Add("name", For);
            select.AddCssClass(CssClass);

            // Add accessibility attributes
            select.Attributes.Add("aria-describedby", errorId);
            
            if (Required)
            {
                select.Attributes.Add("required", "required");
                select.Attributes.Add("aria-required", "true");
            }

            // Add placeholder option if provided
            if (!string.IsNullOrWhiteSpace(Placeholder))
            {
                var placeholderOption = new TagBuilder("option");
                placeholderOption.Attributes.Add("value", "");
                placeholderOption.InnerHtml.Append(Placeholder);
                select.InnerHtml.AppendHtml(placeholderOption);
            }

            // Add options
            foreach (var option in Options)
            {
                var optionElement = new TagBuilder("option");
                optionElement.Attributes.Add("value", option.Value);
                
                if (option.Selected)
                {
                    optionElement.Attributes.Add("selected", "selected");
                }

                optionElement.InnerHtml.Append(option.Text);
                select.InnerHtml.AppendHtml(optionElement);
            }

            return select;
        }

        private TagBuilder CreateErrorContainer(string errorId)
        {
            var error = new TagBuilder("div");
            error.Attributes.Add("id", errorId);
            error.AddCssClass("invalid-feedback");
            error.Attributes.Add("role", "alert");
            error.Attributes.Add("aria-live", "polite");
            return error;
        }
    }
}