using System.Diagnostics;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using Microsoft.Extensions.Localization;

namespace ParaHockeyApp.Pages;

[ResponseCache(Duration = 0, Location = ResponseCacheLocation.None, NoStore = true)]
[IgnoreAntiforgeryToken]
public class ErrorModel : PageModel
{
    public string? RequestId { get; set; }

    public bool ShowRequestId => !string.IsNullOrEmpty(RequestId);

    private readonly ILogger<ErrorModel> _logger;
    private readonly IStringLocalizer<ParaHockeyApp.Resources.SharedResourceMarker> _localizer;

    public ErrorModel(ILogger<ErrorModel> logger, IStringLocalizer<ParaHockeyApp.Resources.SharedResourceMarker> localizer)
    {
        _logger = logger;
        _localizer = localizer;
    }

    public void OnGet()
    {
        RequestId = Activity.Current?.Id ?? HttpContext.TraceIdentifier;
    }
}

