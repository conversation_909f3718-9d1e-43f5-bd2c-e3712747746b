@model dynamic
@{
    bool isExisting = ViewData["IsExisting"] as bool? ?? false;
    var member = Model;
}

<div class="member-comparison-info">
    <!-- Basic Information -->
    <div class="info-section mb-3">
        <h6 class="text-muted mb-2">
            <i class="fas fa-user me-1"></i>Basic Information
        </h6>
        <div class="row">
            <div class="col-6">
                <small class="text-muted">Name:</small><br>
                <strong>@member.FirstName @member.LastName</strong>
            </div>
            <div class="col-6">
                <small class="text-muted">Date of Birth:</small><br>
                @if (member.DateOfBirth != null)
                {
                    <span>@member.DateOfBirth?.ToString("yyyy-MM-dd")</span>
                }
                else
                {
                    <span class="text-muted">Not provided</span>
                }
            </div>
        </div>
        <div class="row mt-2">
            <div class="col-12">
                <small class="text-muted">Gender:</small><br>
                <span>@(member.GenderName ?? "Not provided")</span>
            </div>
        </div>
    </div>

    <!-- Contact Information -->
    <div class="info-section mb-3">
        <h6 class="text-muted mb-2">
            <i class="fas fa-envelope me-1"></i>Contact Information
        </h6>
        <div class="row">
            <div class="col-12 mb-2">
                <small class="text-muted">Email:</small><br>
                @if (!string.IsNullOrEmpty(member.Email))
                {
                    <span>@member.Email</span>
                }
                else
                {
                    <span class="text-muted">Not provided</span>
                }
            </div>
            <div class="col-12">
                <small class="text-muted">Phone:</small><br>
                @if (!string.IsNullOrEmpty(member.Phone))
                {
                    <span>@member.Phone</span>
                    @if (!string.IsNullOrEmpty(member.PhoneTypeName))
                    {
                        <small class="text-muted">(@member.PhoneTypeName)</small>
                    }
                }
                else
                {
                    <span class="text-muted">Not provided</span>
                }
            </div>
        </div>
    </div>

    <!-- Address Information -->
    <div class="info-section mb-3">
        <h6 class="text-muted mb-2">
            <i class="fas fa-map-marker-alt me-1"></i>Address
        </h6>
        <div class="address-display">
            @if (!string.IsNullOrEmpty(member.Address))
            {
                <div>@member.Address</div>
            }
            <div>
                @if (!string.IsNullOrEmpty(member.City))
                {
                    <span>@member.City</span>
                }
                @if (!string.IsNullOrEmpty(member.ProvinceName))
                {
                    <span>@member.ProvinceName</span>
                }
                @if (!string.IsNullOrEmpty(member.PostalCode))
                {
                    <span>@member.PostalCode</span>
                }
            </div>
            @if (string.IsNullOrEmpty(member.Address) && string.IsNullOrEmpty(member.City) && string.IsNullOrEmpty(member.ProvinceName) && string.IsNullOrEmpty(member.PostalCode))
            {
                <span class="text-muted">No address provided</span>
            }
        </div>
    </div>

    <!-- Registration Information -->
    <div class="info-section mb-3">
        <h6 class="text-muted mb-2">
            <i class="fas fa-id-card me-1"></i>Registration
        </h6>
        <div class="row">
            <div class="col-12 mb-2">
                <small class="text-muted">Registration Type:</small><br>
                <span>@(member.RegistrationTypeName ?? "Not provided")</span>
            </div>
            @if (!string.IsNullOrEmpty(member.HcrNumber))
            {
                <div class="col-12 mb-2">
                    <small class="text-muted">HCR Number:</small><br>
                    <span>@member.HcrNumber</span>
                </div>
            }
            @if (!string.IsNullOrEmpty(member.HealthCardNumber))
            {
                <div class="col-12 mb-2">
                    <small class="text-muted">Health Card:</small><br>
                    <span>@member.HealthCardNumber</span>
                </div>
            }
            @if (!string.IsNullOrEmpty(member.SocialInsuranceNumber))
            {
                <div class="col-12">
                    <small class="text-muted">SIN:</small><br>
                    <span>***-***-@member.SocialInsuranceNumber.Substring(Math.Max(0, member.SocialInsuranceNumber.Length - 3))</span>
                </div>
            }
        </div>
    </div>

    @if (isExisting)
    {
        <!-- Existing Member Additional Info -->
        <div class="info-section">
            <h6 class="text-muted mb-2">
                <i class="fas fa-clock me-1"></i>Member Since
            </h6>
            <small>
                @if (member.CreatedAt != null)
                {
                    <span>Registered: @member.CreatedAt?.ToString("yyyy-MM-dd")</span>
                }
                @if (member.Id != null)
                {
                    <br><span class="text-muted">Member ID: #@member.Id</span>
                }
            </small>
        </div>
    }
    else
    {
        <!-- Temp Member Additional Info  -->
        <div class="info-section">
            <h6 class="text-muted mb-2">
                <i class="fas fa-upload me-1"></i>Import Info
            </h6>
            <small>
                <span>Row: @member.RowNumber</span><br>
                <span class="text-muted">Batch: #@member.ImportBatchId</span>
            </small>
        </div>
    }
</div>

<style>
    .member-comparison-info .info-section {
        border-bottom: 1px solid #e9ecef;
        padding-bottom: 0.75rem;
    }
    
    .member-comparison-info .info-section:last-child {
        border-bottom: none;
        padding-bottom: 0;
    }
    
    .member-comparison-info h6 {
        font-size: 0.875rem;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 0.025em;
    }
    
    .address-display div {
        line-height: 1.4;
    }
</style>