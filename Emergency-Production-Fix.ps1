# Emergency Production Fix Script
# Run this to fix Production site immediately

Write-Host "🚨 EMERGENCY PRODUCTION FIX" -ForegroundColor Red
Write-Host "============================" -ForegroundColor Red

try {
    # Copy working Test config as base
    Write-Host "`n📋 Copying working Test configuration..." -ForegroundColor Yellow
    Copy-Item "C:\inetpub\ParaHockey\Test\appsettings.Staging.json" "C:\inetpub\ParaHockey\Production\appsettings.Production.json" -Force
    
    # Load and modify config for Production
    Write-Host "⚙️ Configuring for Production environment..." -ForegroundColor Yellow
    $config = Get-Content "C:\inetpub\ParaHockey\Production\appsettings.Production.json" | ConvertFrom-Json
    
    # Set Production database connection (CRITICAL: No line breaks!)
    $config.ConnectionStrings.DefaultConnection = "Server=SIMBA\SQLEXPRESS;User Id=ParaHockeyUser;Password=***************;Database=ParaHockeyDB;Encrypt=False;TrustServerCertificate=True;"
    
    # Set Production environment settings
    $config.Environment.Name = "PRODUCTION"
    $config.Environment.Theme = "primary"
    $config.Environment.ShowBanner = $false
    $config.Environment.ShowDevelopmentTools = $false
    $config.Environment.EnableDetailedErrorLogging = $false
    $config.Environment.ErrorDetailLevel = "minimal"
    
    # Save the corrected configuration
    Write-Host "💾 Saving Production configuration..." -ForegroundColor Yellow
    $config | ConvertTo-Json -Depth 10 | Set-Content "C:\inetpub\ParaHockey\Production\appsettings.Production.json" -Force
    
    # Restart Production app pool
    Write-Host "🔄 Restarting Production app pool..." -ForegroundColor Yellow
    Import-Module WebAdministration -ErrorAction SilentlyContinue
    
    Stop-WebAppPool -Name "ParaHockey-Production" -ErrorAction SilentlyContinue
    Start-Sleep -Seconds 5
    Start-WebAppPool -Name "ParaHockey-Production" -ErrorAction SilentlyContinue
    Start-Sleep -Seconds 10
    
    # Test the site
    Write-Host "🧪 Testing Production site..." -ForegroundColor Yellow
    
    # TLS bypass for testing
    add-type @"
    using System.Net;
    using System.Security.Cryptography.X509Certificates;
    public class TrustAllCertsPolicy : ICertificatePolicy {
        public bool CheckValidationResult(ServicePoint sp, X509Certificate cert,
                                        WebRequest req, int problem) { return true; }
    }
"@
    [System.Net.ServicePointManager]::CertificatePolicy = New-Object TrustAllCertsPolicy
    [Net.ServicePointManager]::SecurityProtocol = [Net.SecurityProtocolType]::Tls12
    
    $response = Invoke-WebRequest -Uri "https://parahockey.complys.com/" -TimeoutSec 30 -UseBasicParsing
    
    if ($response.StatusCode -eq 200) {
        Write-Host "`n🎉 SUCCESS! Production is working!" -ForegroundColor Green
        Write-Host "Status Code: $($response.StatusCode)" -ForegroundColor Green
        Write-Host "You can go home now! 🏠" -ForegroundColor Cyan
    } else {
        Write-Host "`n⚠️ Site responded but with status: $($response.StatusCode)" -ForegroundColor Yellow
    }
    
} catch {
    Write-Host "`n❌ ERROR: $_" -ForegroundColor Red
    Write-Host "Checking Windows Event Log for details..." -ForegroundColor Yellow
    
    try {
        $latestError = Get-WinEvent -LogName Application -MaxEvents 1 | Where-Object { $_.LevelDisplayName -eq "Error" }
        if ($latestError) {
            Write-Host "`nLatest Error:" -ForegroundColor Red
            Write-Host $latestError.Message -ForegroundColor Gray
        }
    } catch {
        Write-Host "Could not read event log" -ForegroundColor Yellow
    }
}

Write-Host "`n🔧 Script completed" -ForegroundColor White