using Microsoft.Extensions.Localization;
using OfficeOpenXml;
using ParaHockeyApp.Models.Entities;
using ParaHockeyApp.Resources;
using System.Text;
using System.Globalization;

namespace ParaHockeyApp.Services
{
    /// <summary>
    /// Service for exporting event data in various formats
    /// </summary>
    public class EventExportService : IEventExportService
    {
        private readonly ILogger<EventExportService> _logger;
        private readonly IStringLocalizer<SharedResourceMarker> _localizer;
        
        // Eastern Time zone for Quebec - handles EST/EDT automatically
        private static readonly TimeZoneInfo EasternTimeZone = TimeZoneInfo.FindSystemTimeZoneById("Eastern Standard Time");

        public EventExportService(
            ILogger<EventExportService> logger,
            IStringLocalizer<SharedResourceMarker> localizer)
        {
            _logger = logger;
            _localizer = localizer;
        }

        /// <inheritdoc/>
        public async Task<byte[]> ExportToCsvAsync(List<Event> events, DateTime targetDate)
        {
            try
            {
                var csv = new StringBuilder();
                
                // Add header
                csv.AppendLine("Mo<PERSON>,<PERSON>,Heure,Catégorie,Aréna ou Site,Ville,Détails");

                // Add events
                foreach (var eventItem in events.OrderBy(e => e.StartDate))
                {
                    // Convert to Eastern Time for export
                    var easternStartDate = ConvertToEasternTime(eventItem.StartDate);
                    
                    var month = GetFrenchMonthName(easternStartDate.Month);
                    var date = easternStartDate.ToString("dd/MM/yyyy");
                    var time = easternStartDate.ToString("HH:mm");
                    var category = GetCategoryDisplayName(eventItem.EventCategory);
                    var location = eventItem.Location ?? "";
                    var city = ExtractCity(location);
                    var details = eventItem.Description ?? "";

                    // Escape commas and quotes in CSV
                    csv.AppendLine($"{EscapeCsv(month)},{EscapeCsv(date)},{EscapeCsv(time)},{EscapeCsv(category)},{EscapeCsv(location)},{EscapeCsv(city)},{EscapeCsv(details)}");
                }

                return Encoding.UTF8.GetBytes(csv.ToString());
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error exporting events to CSV");
                throw;
            }
        }

        /// <inheritdoc/>
        public async Task<byte[]> ExportToExcelAsync(List<Event> events, DateTime targetDate)
        {
            try
            {
                ExcelPackage.LicenseContext = LicenseContext.NonCommercial;
                
                using var package = new ExcelPackage();
                var worksheet = package.Workbook.Worksheets.Add("Événements");

                // Set headers
                var headers = new[] { "Mois", "Date", "Heure", "Catégorie", "Aréna ou Site", "Ville", "Détails" };
                for (int i = 0; i < headers.Length; i++)
                {
                    worksheet.Cells[1, i + 1].Value = headers[i];
                    worksheet.Cells[1, i + 1].Style.Font.Bold = true;
                }

                // Add data
                int row = 2;
                foreach (var eventItem in events.OrderBy(e => e.StartDate))
                {
                    // Convert to Eastern Time for export
                    var easternStartDate = ConvertToEasternTime(eventItem.StartDate);
                    
                    worksheet.Cells[row, 1].Value = GetFrenchMonthName(easternStartDate.Month);
                    worksheet.Cells[row, 2].Value = easternStartDate.ToString("dd/MM/yyyy");
                    worksheet.Cells[row, 3].Value = easternStartDate.ToString("HH:mm");
                    worksheet.Cells[row, 4].Value = GetCategoryDisplayName(eventItem.EventCategory);
                    worksheet.Cells[row, 5].Value = eventItem.Location ?? "";
                    worksheet.Cells[row, 6].Value = ExtractCity(eventItem.Location ?? "");
                    worksheet.Cells[row, 7].Value = eventItem.Description ?? "";
                    row++;
                }

                // Auto-fit columns
                worksheet.Cells.AutoFitColumns();

                // Apply table formatting
                var range = worksheet.Cells[1, 1, row - 1, headers.Length];
                var table = worksheet.Tables.Add(range, "EventsTable");
                table.ShowHeader = true;
                table.TableStyle = OfficeOpenXml.Table.TableStyles.Light1;

                return await Task.FromResult(package.GetAsByteArray());
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error exporting events to Excel");
                throw;
            }
        }

        /// <inheritdoc/>
        public string GenerateFileName(DateTime targetDate, string format)
        {
            var monthName = GetFrenchMonthName(targetDate.Month);
            var extension = format.ToLowerInvariant() == "excel" ? ".xlsx" : ".csv";
            return $"Evenements_{monthName}_{targetDate.Year}{extension}";
        }

        /// <inheritdoc/>
        public string GetMimeType(string format)
        {
            return format.ToLowerInvariant() switch
            {
                "excel" => "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
                "csv" => "text/csv",
                _ => "text/csv"
            };
        }

        private string GetFrenchMonthName(int month)
        {
            return month switch
            {
                1 => "Janvier",
                2 => "Février",
                3 => "Mars",
                4 => "Avril",
                5 => "Mai",
                6 => "Juin",
                7 => "Juillet",
                8 => "Août",
                9 => "Septembre",
                10 => "Octobre",
                11 => "Novembre",
                12 => "Décembre",
                _ => "Inconnu"
            };
        }

        private string GetCategoryDisplayName(EventCategory? category)
        {
            if (category == null) return "Non spécifié";
            
            return category.DisplayNameKey switch
            {
                "EventCategory_Practice" => "Pratique",
                "EventCategory_Training" => "Entraînement",
                "EventCategory_Game" => "Match",
                "EventCategory_Tournament" => "Tournoi",
                "EventCategory_Other" => "Autre",
                _ => category.DisplayNameKey
            };
        }

        private string ExtractCity(string location)
        {
            if (string.IsNullOrEmpty(location))
                return "";

            var parts = location.Split(',');
            return parts.Length > 1 ? parts.Last().Trim() : "";
        }

        private string EscapeCsv(string value)
        {
            if (string.IsNullOrEmpty(value))
                return "";

            if (value.Contains(',') || value.Contains('"') || value.Contains('\n'))
            {
                return $"\"{value.Replace("\"", "\"\"")}\"";
            }

            return value;
        }

        /// <inheritdoc/>
        public async Task<List<Event>> ParseCsvFileAsync(Stream stream, Dictionary<string, int> categoryMappings, List<string> errors)
        {
            var events = new List<Event>();
            var otherCategoryId = categoryMappings.GetValueOrDefault("", categoryMappings.Values.FirstOrDefault());
            _logger.LogInformation("CSV: Category mappings available: [{Categories}]", string.Join(", ", categoryMappings.Select(kvp => $"'{kvp.Key}'->{kvp.Value}")));
            _logger.LogInformation("CSV: Using default category ID: {CategoryId}", otherCategoryId);

            using (var reader = new StreamReader(stream, Encoding.UTF8))
            {
                var line = await reader.ReadLineAsync(); // Skip header
                
                // Remove BOM if present
                if (line?.StartsWith("\ufeff") == true)
                {
                    line = line.Substring(1);
                }
                _logger.LogInformation("CSV Header: {Header}", line);
                var lineNumber = 1;

                while ((line = await reader.ReadLineAsync()) != null)
                {
                    lineNumber++;

                    // Skip empty lines
                    if (string.IsNullOrWhiteSpace(line) || line.Split(',').All(string.IsNullOrWhiteSpace))
                        continue;

                    var columns = line.Split(',');
                    
                    // Handle trailing commas by ensuring we have at least 7 columns
                    if (columns.Length < 7)
                    {
                        var newColumns = new string[7];
                        Array.Copy(columns, newColumns, columns.Length);
                        for (int i = columns.Length; i < 7; i++)
                        {
                            newColumns[i] = "";
                        }
                        columns = newColumns;
                    }
                    
                    _logger.LogInformation("CSV Line {LineNumber}: [{Columns}]", lineNumber, string.Join(", ", columns.Take(7).Select((c, i) => $"Col{i+1}:'{c}'")));
                    if (columns.Length < 7) continue;

                    try
                    {
                        var eventItem = ParseEventFromColumns(columns, categoryMappings, otherCategoryId);
                        if (eventItem != null)
                        {
                            events.Add(eventItem);
                        }
                    }
                    catch (Exception ex)
                    {
                        errors.Add($"Line {lineNumber}: {ex.Message}");
                    }
                }
            }

            return events;
        }

        /// <inheritdoc/>
        public async Task<List<Event>> ParseExcelFileAsync(Stream stream, Dictionary<string, int> categoryMappings, List<string> errors)
        {
            var events = new List<Event>();
            var otherCategoryId = categoryMappings.GetValueOrDefault("", categoryMappings.Values.FirstOrDefault());
            _logger.LogInformation("Excel: Category mappings available: [{Categories}]", string.Join(", ", categoryMappings.Select(kvp => $"'{kvp.Key}'->{kvp.Value}")));
            _logger.LogInformation("Excel: Using default category ID: {CategoryId}", otherCategoryId);

            try
            {
                ExcelPackage.LicenseContext = LicenseContext.NonCommercial;
                
                using var package = new ExcelPackage(stream);
                var worksheet = package.Workbook.Worksheets.FirstOrDefault();
                
                if (worksheet == null)
                {
                    _logger.LogError("No worksheet found in Excel file");
                    errors.Add("No worksheet found in Excel file");
                    return events;
                }

                var rowCount = worksheet.Dimension?.Rows ?? 0;
                _logger.LogInformation("Excel file has {RowCount} rows to process", rowCount);
                
                // Start from row 2 to skip header
                for (int row = 2; row <= rowCount; row++)
                {
                    try
                    {
                        // Read columns (1-based indexing in Excel)
                        var columns = new string[7];
                        for (int col = 1; col <= 7; col++)
                        {
                            columns[col - 1] = worksheet.Cells[row, col].Value?.ToString()?.Trim() ?? "";
                        }
                        
                        _logger.LogInformation("Row {Row}: [{Columns}]", row, string.Join(", ", columns.Select((c, i) => $"Col{i+1}:'{c}'")));

                        // Skip empty rows
                        if (columns.All(string.IsNullOrWhiteSpace))
                        {
                            _logger.LogInformation("Skipping empty row {Row}", row);
                            continue;
                        }

                        var eventItem = ParseEventFromColumns(columns, categoryMappings, otherCategoryId);
                        if (eventItem != null)
                        {
                            _logger.LogInformation("Successfully parsed event from row {Row}: {Title}", row, eventItem.Title);
                            events.Add(eventItem);
                        }
                        else
                        {
                            _logger.LogWarning("Failed to parse event from row {Row} - returned null", row);
                        }
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "Error parsing Excel row {Row}: {Message}", row, ex.Message);
                        errors.Add($"Row {row}: {ex.Message}");
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error parsing Excel file");
                errors.Add($"Error reading Excel file: {ex.Message}");
            }

            return events;
        }

        private Event? ParseEventFromColumns(string[] columns, Dictionary<string, int> categoryMappings, int otherCategoryId)
        {
            var month = columns[0]?.Trim();
            var date = columns[1]?.Trim();
            var time = columns[2]?.Trim();
            var category = columns[3]?.Trim();
            var venue = columns[4]?.Trim();
            var city = columns[5]?.Trim();
            var details = columns[6]?.Trim();

            // Skip if essential fields are missing
            if (string.IsNullOrWhiteSpace(month) || string.IsNullOrWhiteSpace(date))
            {
                _logger.LogWarning("Skipping row - essential fields missing: month='{Month}', date='{Date}'", month, date);
                return null;
            }

            // Parse date
            var eventDate = ParseEventDate(month, date);
            if (eventDate == DateTime.MinValue)
            {
                _logger.LogWarning("Failed to parse date from month='{Month}', date='{Date}'", month, date);
                return null;
            }

            // Parse time
            var (startTime, endTime) = ParseEventTime(time);

            // Combine date and time
            var startDateTime = eventDate.Add(startTime);
            var endDateTime = eventDate.Add(endTime);
            
            // Treat imported times as Eastern Time, convert to server local time for storage
            startDateTime = ConvertFromEasternTime(startDateTime);
            endDateTime = ConvertFromEasternTime(endDateTime);

            // Map category using details column (which contains the actual event type)
            var eventType = !string.IsNullOrWhiteSpace(details) ? details : category;
            
            // Special case: If venue contains "Tentatif", override the event type to "Tentatif"
            if (!string.IsNullOrWhiteSpace(venue) && venue.ToLowerInvariant().Contains("tentatif"))
            {
                eventType = "Tentatif";
            }
            
            var categoryId = categoryMappings.GetValueOrDefault(eventType, otherCategoryId);
            _logger.LogInformation("Mapping event type '{EventType}' to ID {CategoryId} (default: {DefaultId})", eventType, categoryId, otherCategoryId);
            
            // Ensure we have a valid category ID
            if (categoryId <= 0)
            {
                _logger.LogWarning("Invalid category ID {CategoryId} for event type '{EventType}' - skipping event", categoryId, eventType);
                return null;
            }

            // Create event title
            var title = !string.IsNullOrWhiteSpace(details) ? details : "Event";
            if (!string.IsNullOrWhiteSpace(category))
            {
                title += $" - {category}";
            }

            // Create location
            var location = venue;
            if (!string.IsNullOrWhiteSpace(city))
            {
                location += string.IsNullOrWhiteSpace(location) ? city : $", {city}";
            }

            return new Event
            {
                Title = title,
                Description = category,
                StartDate = startDateTime,
                EndDate = endDateTime,
                IsAllDay = false,
                Location = location,
                EventCategoryId = categoryId,
                IsPublished = true,
                RequiresRegistration = false,
                MaxParticipants = -1,
                Priority = 3,
                IsRecurring = false,
                IsActive = true
            };
        }

        private DateTime ParseEventDate(string month, string date)
        {
            try
            {
                // Try parsing the date as dd/MM/yyyy
                if (DateTime.TryParseExact(date, "dd/MM/yyyy", CultureInfo.InvariantCulture, DateTimeStyles.None, out var parsedDate))
                {
                    return parsedDate;
                }

                // If month is provided separately, try to combine it
                var monthNumber = GetMonthNumber(month);
                if (monthNumber > 0)
                {
                    // Handle format like "Mer-27" (Wed-27)
                    var day = 0;
                    if (date.Contains('-'))
                    {
                        var parts = date.Split('-');
                        if (parts.Length >= 2 && int.TryParse(parts[1], out day))
                        {
                            // Use current year, or next year if month has passed
                            var year = DateTime.Now.Year;
                            var testDate = new DateTime(year, monthNumber, day);
                            if (testDate < DateTime.Now.AddDays(-30)) // If more than 30 days ago, assume next year
                            {
                                year++;
                            }
                            return new DateTime(year, monthNumber, day);
                        }
                    }
                    else
                    {
                        // Handle format like "27/08" or just "27"
                        var parts = date.Split('/');
                        if (parts.Length >= 1 && int.TryParse(parts[0], out day))
                        {
                            var year = DateTime.Now.Year;
                            if (parts.Length >= 3 && int.TryParse(parts[2], out var parsedYear))
                            {
                                year = parsedYear;
                            }

                            return new DateTime(year, monthNumber, day);
                        }
                    }
                }

                return DateTime.MinValue;
            }
            catch
            {
                return DateTime.MinValue;
            }
        }

        private (TimeSpan startTime, TimeSpan endTime) ParseEventTime(string time)
        {
            if (string.IsNullOrWhiteSpace(time) || time.Contains("journée"))
                return (TimeSpan.Zero, TimeSpan.FromHours(23).Add(TimeSpan.FromMinutes(59)));

            try
            {
                // Try parsing French time range format: "16h00-18h30"
                if (time.Contains('-') && time.Contains('h'))
                {
                    var rangeParts = time.Split('-', StringSplitOptions.RemoveEmptyEntries);
                    if (rangeParts.Length == 2)
                    {
                        var startTime = ParseFrenchTime(rangeParts[0].Trim());
                        var endTime = ParseFrenchTime(rangeParts[1].Trim());
                        
                        if (startTime.HasValue && endTime.HasValue)
                        {
                            return (startTime.Value, endTime.Value);
                        }
                    }
                }

                // Try parsing single French time format: "16h00"
                if (time.Contains('h'))
                {
                    var frenchTime = ParseFrenchTime(time.Trim());
                    if (frenchTime.HasValue)
                    {
                        return (frenchTime.Value, frenchTime.Value.Add(TimeSpan.FromHours(2))); // Default 2-hour duration
                    }
                }

                if (TimeSpan.TryParse(time, out var parsedTime))
                {
                    return (parsedTime, parsedTime.Add(TimeSpan.FromHours(2))); // Default 2-hour duration
                }

                // Try parsing HH:mm format
                if (time.Contains(':'))
                {
                    var parts = time.Split(':');
                    if (parts.Length >= 2 && 
                        int.TryParse(parts[0], out var hours) && 
                        int.TryParse(parts[1], out var minutes))
                    {
                        var startTime = new TimeSpan(hours, minutes, 0);
                        return (startTime, startTime.Add(TimeSpan.FromHours(2)));
                    }
                }
            }
            catch
            {
                // Fall back to default
            }

            return (TimeSpan.Zero, TimeSpan.FromHours(23).Add(TimeSpan.FromMinutes(59)));
        }

        private TimeSpan? ParseFrenchTime(string frenchTime)
        {
            if (string.IsNullOrWhiteSpace(frenchTime))
                return null;

            try
            {
                // Handle formats like "16h00", "16h30", "9h00", etc.
                var parts = frenchTime.ToLowerInvariant().Split('h');
                if (parts.Length >= 2 && int.TryParse(parts[0], out var hours))
                {
                    var minutes = 0;
                    if (!string.IsNullOrEmpty(parts[1]) && int.TryParse(parts[1], out var parsedMinutes))
                    {
                        minutes = parsedMinutes;
                    }

                    if (hours >= 0 && hours <= 23 && minutes >= 0 && minutes <= 59)
                    {
                        return new TimeSpan(hours, minutes, 0);
                    }
                }
            }
            catch
            {
                // Fall back
            }

            return null;
        }

        private int GetMonthNumber(string monthName)
        {
            if (string.IsNullOrWhiteSpace(monthName))
                return 0;

            return monthName.ToLowerInvariant() switch
            {
                "janvier" => 1,
                "février" => 2,
                "mars" => 3,
                "avril" => 4,
                "mai" => 5,
                "juin" => 6,
                "juillet" => 7,
                "août" => 8,
                "septembre" => 9,
                "octobre" => 10,
                "novembre" => 11,
                "décembre" => 12,
                _ => 0
            };
        }

        /// <summary>
        /// Converts a DateTime to Eastern Time for consistent export formatting
        /// </summary>
        private DateTime ConvertToEasternTime(DateTime dateTime)
        {
            // Assume input is in server's local time, convert to Eastern Time
            return TimeZoneInfo.ConvertTime(dateTime, EasternTimeZone);
        }

        /// <summary>
        /// Converts a DateTime from Eastern Time to server local time for storage
        /// </summary>
        private DateTime ConvertFromEasternTime(DateTime easternDateTime)
        {
            // Treat input as Eastern Time, convert to server's local timezone
            return TimeZoneInfo.ConvertTime(easternDateTime, EasternTimeZone, TimeZoneInfo.Local);
        }
    }
}