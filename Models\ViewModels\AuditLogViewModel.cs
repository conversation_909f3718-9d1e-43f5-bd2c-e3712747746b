using System.ComponentModel.DataAnnotations;

namespace ParaHockeyApp.Models.ViewModels
{
    /// <summary>
    /// Enhanced view model for displaying consolidated audit logs with rich descriptions and clickable links
    /// </summary>
    public class AuditLogViewModel
    {
        /// <summary>
        /// When the action occurred
        /// </summary>
        public DateTime Timestamp { get; set; }

        /// <summary>
        /// Source of the action (Admin, Member, System)
        /// </summary>
        public string PerformedBy { get; set; } = string.Empty;

        /// <summary>
        /// Rich, consolidated description of what changed
        /// Examples:
        /// - "New Member: <PERSON>"
        /// - "Member <PERSON> edited: Parent #1 First Name changed from 'Ginette' to 'Louis', Phone Number changed from '************' to '************'"
        /// - "EVENT: Hockey Practice (Start: 2024-01-15 19:00, End: 2024-01-15 20:30)"
        /// </summary>
        public string Description { get; set; } = string.Empty;

        /// <summary>
        /// Context-sensitive URL for the audit item
        /// Examples:
        /// - "/Admin/MemberDetails/123" for member-related changes
        /// - "/Admin/Calendar?year=2024&month=1" for event changes
        /// </summary>
        public string LinkUrl { get; set; } = string.Empty;

        /// <summary>
        /// Formatted timestamp for display
        /// </summary>
        public string TimestampFormatted => Timestamp.ToString("MM-dd HH:mm");

        /// <summary>
        /// CSS class for the source badge
        /// </summary>
        public string SourceBadgeClass => PerformedBy switch
        {
            "Admin" => "text-danger",
            "Member" => "text-primary", 
            "System" => "text-secondary",
            _ when PerformedBy.StartsWith("Admin:") => "text-danger",
            _ => "text-secondary"
        };

        /// <summary>
        /// Returns true if this is an admin action that needs special formatting
        /// </summary>
        public bool IsAdminAction => PerformedBy.StartsWith("Admin:");

        /// <summary>
        /// Returns the admin email part (after "Admin: ")
        /// </summary>
        public string AdminEmail => IsAdminAction ? PerformedBy.Substring(7) : "";

        /// <summary>
        /// Returns the display text for non-admin actions
        /// </summary>
        public string DisplayText => IsAdminAction ? AdminEmail : PerformedBy;

        /// <summary>
        /// List of audit log IDs that were consolidated into this view model
        /// Used for debugging and traceability
        /// </summary>
        public List<int> ConsolidatedAuditLogIds { get; set; } = new List<int>();
    }
}