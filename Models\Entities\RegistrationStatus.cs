namespace ParaHockeyApp.Models.Entities
{
    /// <summary>
    /// Status of event registration
    /// </summary>
    public enum RegistrationStatus
    {
        /// <summary>
        /// Registration is pending approval
        /// </summary>
        Pending = 1,

        /// <summary>
        /// Registration has been confirmed
        /// </summary>
        Confirmed = 2,

        /// <summary>
        /// Registration has been cancelled
        /// </summary>
        Cancelled = 3,

        /// <summary>
        /// Registration was on waitlist
        /// </summary>
        Waitlisted = 4,

        /// <summary>
        /// Registration was rejected
        /// </summary>
        Rejected = 5
    }
}