# ChromeDriver Fix Guide

## Issue Description
The E2E tests are failing due to ChromeDriver version mismatch. Your Chrome browser is version 137.x, but the ChromeDriver being used doesn't match this version.

## Solution Options

### Option 1: Use WebDriverManager (Recommended)
WebDriverManager should automatically download the correct ChromeDriver version. Let's ensure it's configured properly:

1. **Update the project file** to use the latest WebDriverManager:
```xml
<PackageReference Include="WebDriverManager" Version="2.17.4" />
```

2. **Modify WebDriverFactory.cs** to use WebDriverManager:
```csharp
using WebDriverManager;
using WebDriverManager.DriverConfigs.Impl;

private IWebDriver CreateChromeDriver()
{
    // Auto-download and setup correct ChromeDriver version
    new DriverManager().SetUpDriver(new ChromeConfig());
    
    var options = new ChromeOptions();
    
    if (_config.HeadlessMode)
    {
        options.AddArgument("--headless");
    }

    // Stability and performance
    options.AddArguments(
        "--no-sandbox",
        "--disable-dev-shm-usage",
        "--disable-blink-features=AutomationControlled"
    );

    var driver = new ChromeDriver(options);
    driver.Manage().Window.Size = new System.Drawing.Size(1920, 1080);
    driver.Manage().Timeouts().ImplicitWait = TimeSpan.FromSeconds(_config.ImplicitWaitSeconds);
    
    return driver;
}
```

### Option 2: Manual ChromeDriver Download
1. Check your Chrome version:
   - Open Chrome
   - Go to `chrome://version/`
   - Note the version (e.g., 137.0.6412.120)

2. Download matching ChromeDriver:
   - Go to https://googlechromelabs.github.io/chrome-for-testing/
   - Find your Chrome version
   - Download the matching ChromeDriver

3. Place ChromeDriver in project:
   - Create folder: `ParaHockey.E2E.Tests/drivers/`
   - Copy chromedriver.exe there
   - Update `.gitignore` to exclude: `drivers/`

### Option 3: Use Selenium Manager (Selenium 4.6+)
If using Selenium 4.6 or higher, it includes Selenium Manager which auto-downloads drivers:

```csharp
// No changes needed - Selenium Manager handles it automatically
var driver = new ChromeDriver(options);
```

## Implementation Steps

1. **Update NuGet packages**:
```powershell
cd ParaHockey.E2E.Tests
dotnet add package Selenium.WebDriver --version 4.27.0
dotnet add package WebDriverManager --version 2.17.4
dotnet restore
```

2. **Update WebDriverFactory.cs** with the code from Option 1 above

3. **Test the fix**:
```powershell
# Run a simple test to verify
dotnet test --filter "FullyQualifiedName~SimpleWorkingTests"
```

## Alternative: Use Edge Instead
If Chrome continues to have issues, Edge uses the same engine and is usually more stable:

```powershell
# Run tests with Edge browser
./run-tests.ps1 -Browser Edge
```

## Debugging Steps

1. **Check Chrome version**:
```powershell
# In PowerShell
(Get-Item "C:\Program Files\Google\Chrome\Application\chrome.exe").VersionInfo.ProductVersion
```

2. **Check current ChromeDriver version**:
```powershell
# If chromedriver.exe is in PATH
chromedriver --version
```

3. **Enable verbose logging**:
```csharp
// In WebDriverFactory.cs
var service = ChromeDriverService.CreateDefaultService();
service.EnableVerboseLogging = true;
service.LogPath = "chromedriver.log";
var driver = new ChromeDriver(service, options);
```

## Prevention

To prevent future ChromeDriver issues:

1. **Use WebDriverManager** - It automatically handles version matching
2. **Set up CI/CD** to use specific Chrome versions
3. **Run tests in Docker** containers with fixed Chrome versions
4. **Use headless mode** for more stability

## Quick Fix Script

Create `fix-chromedriver.ps1`:
```powershell
# Update packages and rebuild
cd ParaHockey.E2E.Tests
dotnet add package WebDriverManager --version 2.17.4
dotnet add package Selenium.WebDriver --version 4.27.0
dotnet restore
dotnet build

# Run a test to verify
Write-Host "Testing ChromeDriver..." -ForegroundColor Yellow
dotnet test --filter "FullyQualifiedName~SimpleWorkingTests" --no-build

if ($LASTEXITCODE -eq 0) {
    Write-Host "ChromeDriver is working!" -ForegroundColor Green
} else {
    Write-Host "ChromeDriver still has issues. Try Option 2 or 3." -ForegroundColor Red
}
```

Run it:
```powershell
./fix-chromedriver.ps1
```

## Contact Support
If none of these solutions work:
1. Share your Chrome version
2. Share the exact error message
3. Try running with `--verbosity detailed` for more info