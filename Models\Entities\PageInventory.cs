using System.ComponentModel.DataAnnotations;

namespace ParaHockeyApp.Models.Entities
{
    /// <summary>
    /// Represents a complete inventory of all pages in the application
    /// Used for systematic page-by-page modernization tracking
    /// </summary>
    public class PageInventory : BaseEntity
    {
        /// <summary>
        /// Version of the inventory (incremented on each generation)
        /// </summary>
        [Required]
        public int Version { get; set; }

        /// <summary>
        /// User who generated this inventory
        /// </summary>
        [Required]
        [StringLength(100)]
        public string GeneratedBy { get; set; } = string.Empty;

        /// <summary>
        /// Total number of pages discovered
        /// </summary>
        [Required]
        public int TotalPages { get; set; }

        /// <summary>
        /// Number of pages marked as high priority
        /// </summary>
        [Required]
        public int HighPriorityPages { get; set; }

        /// <summary>
        /// Number of pages marked as medium priority
        /// </summary>
        [Required]
        public int MediumPriorityPages { get; set; }

        /// <summary>
        /// Number of pages marked as low priority
        /// </summary>
        [Required]
        public int LowPriorityPages { get; set; }

        /// <summary>
        /// Navigation property to all page information in this inventory
        /// </summary>
        public virtual ICollection<PageInfo> Pages { get; set; } = new List<PageInfo>();
    }
}