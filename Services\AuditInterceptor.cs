using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Diagnostics;
using ParaHockeyApp.Models.Entities;
using System.Text.Json;

namespace ParaHockeyApp.Services
{
    /// <summary>
    /// EF Core interceptor that automatically handles audit logging for all entities.
    /// This interceptor:
    /// 1. Automatically populates audit fields on BaseEntity when entities are created/modified
    /// 2. Creates comprehensive AuditLog entries for all entity changes
    /// 3. Captures old/new values in JSON format for forensic analysis
    /// </summary>
    public class AuditInterceptor : SaveChangesInterceptor
    {
        private readonly IUserContextService _userContextService;

        public AuditInterceptor(IUserContextService userContextService)
        {
            _userContextService = userContextService;
        }

        /// <summary>
        /// Safely gets user context, handling authentication transition scenarios
        /// </summary>
        private UserContext GetUserContextSafely()
        {
            try
            {
                return _userContextService.GetCurrentUser();
            }
            catch (Exception ex)
            {
                // During authentication transitions (e.g., admin logout during member login),
                // UserContextService might encounter inconsistent state. Return a safe default.
                return new UserContext
                {
                    IsAdmin = false,
                    AdminId = null,
                    MemberId = null,
                    UserName = "System",
                    Source = ActionSource.System
                };
            }
        }

        public override InterceptionResult<int> SavingChanges(
            DbContextEventData eventData,
            InterceptionResult<int> result)
        {
            ProcessAuditChanges(eventData.Context!);
            return base.SavingChanges(eventData, result);
        }

        public override ValueTask<InterceptionResult<int>> SavingChangesAsync(
            DbContextEventData eventData,
            InterceptionResult<int> result,
            CancellationToken cancellationToken = default)
        {
            ProcessAuditChanges(eventData.Context!);
            return base.SavingChangesAsync(eventData, result, cancellationToken);
        }

        private void ProcessAuditChanges(DbContext context)
        {
            // Handle potential authentication transition scenarios (e.g., member login while admin is signed out)
            var userContext = GetUserContextSafely();
            var ipAddress = _userContextService.GetClientIPAddress();
            var timestamp = DateTime.UtcNow;

            var auditEntries = new List<AuditLog>();

            foreach (var entry in context.ChangeTracker.Entries())
            {
                // Handle BaseEntity audit fields
                if (entry.Entity is BaseEntity baseEntity)
                {
                    ProcessBaseEntityAudit(entry, baseEntity, userContext, timestamp);
                }

                // Create AuditLog entries for all entity changes
                var auditLog = CreateAuditLogEntry(entry, userContext, ipAddress, timestamp);
                if (auditLog != null)
                {
                    auditEntries.Add(auditLog);
                }
            }

            // Add audit log entries to context
            foreach (var auditLog in auditEntries)
            {
                context.Set<AuditLog>().Add(auditLog);
            }
        }

        private void ProcessBaseEntityAudit(Microsoft.EntityFrameworkCore.ChangeTracking.EntityEntry entry, BaseEntity baseEntity, UserContext userContext, DateTime timestamp)
        {
            switch (entry.State)
            {
                case EntityState.Added:
                    baseEntity.DateCreated = timestamp;
                    baseEntity.CreatedBySource = userContext.Source;

                    if (userContext.IsAdmin && userContext.AdminId.HasValue)
                    {
                        baseEntity.CreatedByAdminId = userContext.AdminId;
                        baseEntity.CreatedByMemberId = null;
                    }
                    else if (userContext.MemberId.HasValue)
                    {
                        baseEntity.CreatedByMemberId = userContext.MemberId;
                        baseEntity.CreatedByAdminId = null;
                    }
                    break;

                case EntityState.Modified:
                    baseEntity.DateModified = timestamp;
                    baseEntity.ModifiedBySource = userContext.Source;

                    if (userContext.IsAdmin && userContext.AdminId.HasValue)
                    {
                        baseEntity.ModifiedByAdminId = userContext.AdminId;
                        baseEntity.ModifiedByMemberId = null;
                    }
                    else if (userContext.MemberId.HasValue)
                    {
                        baseEntity.ModifiedByMemberId = userContext.MemberId;
                        baseEntity.ModifiedByAdminId = null;
                    }

                    // Prevent modification of creation audit fields
                    entry.Property(nameof(BaseEntity.DateCreated)).IsModified = false;
                    entry.Property(nameof(BaseEntity.CreatedByMemberId)).IsModified = false;
                    entry.Property(nameof(BaseEntity.CreatedByAdminId)).IsModified = false;
                    entry.Property(nameof(BaseEntity.CreatedBySource)).IsModified = false;
                    break;
            }
        }

        private AuditLog? CreateAuditLogEntry(Microsoft.EntityFrameworkCore.ChangeTracking.EntityEntry entry, UserContext userContext, string? ipAddress, DateTime timestamp)
        {
            // Skip audit log entries to prevent infinite recursion
            if (entry.Entity is AuditLog || entry.Entity is MemberLog)
                return null;

            var entityType = entry.Entity.GetType();
            var entityName = entityType.Name;

            // Get entity ID (assuming all entities have an Id property)
            var entityId = GetEntityId(entry);
            if (entityId == 0)
                return null; // Skip if we can't get ID

            string action;
            string? oldValues = null;
            string? newValues = null;
            string description;

            switch (entry.State)
            {
                case EntityState.Added:
                    action = "Create";
                    newValues = SerializeEntity(entry, isNewValues: true);
                    description = $"Created new {entityName}";
                    break;

                case EntityState.Modified:
                    action = "Update";
                    oldValues = SerializeEntity(entry, isNewValues: false);
                    newValues = SerializeEntity(entry, isNewValues: true);
                    description = CreateChangeDescription(entry);
                    break;

                case EntityState.Deleted:
                    action = "Delete";
                    oldValues = SerializeEntity(entry, isNewValues: false);
                    description = $"Deleted {entityName}";
                    break;

                default:
                    return null; // Don't audit unchanged entities
            }

            return new AuditLog
            {
                EntityType = entityName,
                EntityId = entityId,
                Action = action,
                Timestamp = timestamp,
                PerformedByMemberId = userContext.MemberId,
                PerformedByAdminId = userContext.AdminId,
                PerformedBySource = userContext.Source,
                PerformerName = userContext.UserName,
                IPAddress = ipAddress,
                OldValues = oldValues,
                NewValues = newValues,
                Description = description
            };
        }

        private int GetEntityId(Microsoft.EntityFrameworkCore.ChangeTracking.EntityEntry entry)
        {
            // Handle MemberLog which uses NoLog as primary key
            if (entry.Entity is MemberLog)
            {
                var noLogProperty = entry.Property("NoLog");
                if (noLogProperty?.CurrentValue is int noLog)
                    return noLog;
                return 0;
            }
            
            // Handle standard entities with Id property
            try
            {
                var idProperty = entry.Property("Id");
                if (idProperty?.CurrentValue is int id)
                    return id;
            }
            catch (InvalidOperationException)
            {
                // Property doesn't exist, return 0
            }
            
            return 0;
        }

        private string SerializeEntity(Microsoft.EntityFrameworkCore.ChangeTracking.EntityEntry entry, bool isNewValues)
        {
            var values = new Dictionary<string, object?>();

            foreach (var property in entry.Properties)
            {
                var value = isNewValues ? property.CurrentValue : property.OriginalValue;
                values[property.Metadata.Name] = value;
            }

            return JsonSerializer.Serialize(values, new JsonSerializerOptions
            {
                WriteIndented = false,
                DefaultIgnoreCondition = System.Text.Json.Serialization.JsonIgnoreCondition.WhenWritingNull
            });
        }

        private string CreateChangeDescription(Microsoft.EntityFrameworkCore.ChangeTracking.EntityEntry entry)
        {
            var changes = new List<string>();

            foreach (var property in entry.Properties)
            {
                if (property.IsModified)
                {
                    var propertyName = property.Metadata.Name;
                    var oldValue = property.OriginalValue?.ToString() ?? "null";
                    var newValue = property.CurrentValue?.ToString() ?? "null";

                    // Skip audit fields in description to avoid noise
                    if (propertyName.StartsWith("CreatedBy") || propertyName.StartsWith("ModifiedBy") || 
                        propertyName == "DateModified" || propertyName == "DateCreated")
                        continue;

                    if (oldValue != newValue)
                    {
                        changes.Add($"{propertyName}: '{oldValue}' → '{newValue}'");
                    }
                }
            }

            if (changes.Any())
            {
                return string.Join(", ", changes);
            }

            return $"Updated {entry.Entity.GetType().Name}";
        }
    }
}