# Admin Event Time Offset Fix

## User Story
As an admin user working from any time-zone, I want an event’s start and end times to stay exactly as I entered them after saving, so that the calendar displays consistent local times for all admins and prevents accidental time shifts.

## Functional Requirements
1. The event modal must display start and end times in the admin’s local time-zone when editing an existing event.
2. Saving an event must send times in UTC to the server (current behaviour) while preserving the intended local time.
3. If the admin clicks **Save** without changing any field values, no server request shall be made and no audit log entry written.
4. The change-detection logic must compare all editable fields, including checkboxes and selects; whitespace differences in multi-line text areas should be ignored.
5. All new notification text (e.g., "No changes detected.") must use localisation keys: `@SharedLocalizer["NoChangesDetected"]`.

## Non-Functional Requirements
1. Follow ASP.NET Core MVC architecture and project coding standards.
2. Implement client-side logic in vanilla JS (reuse existing script file) to avoid new dependencies.
3. Performance: modal pre-fill conversion must complete within 50 ms on a mid-range laptop.
4. Security: only authenticated admins can trigger update calls; existing controller guards stay intact.
5. Accessibility: no regression in keyboard navigation or screen-reader support.
6. Testing: add automated E2E tests covering time conversion and change detection.

## Acceptance Criteria
| # | Given | When | Then |
|---|-------|------|------|
| AC1 | I create an event for 09:00–11:00 | I reload the calendar | The event still shows 09:00–11:00 in my local time |
| AC2 | An admin in a different time-zone opens the same event | — | They see the correct local equivalent time |
| AC3 | I open the event modal and press **Save** without edits | — | A toast "No changes detected" appears and no network request is sent |
| AC4 | I edit only the description | I click **Save** | Only the description changes; start/end times remain untouched |
| AC5 | Site language is FR | Toasts and validation messages appear in French (localised) | 