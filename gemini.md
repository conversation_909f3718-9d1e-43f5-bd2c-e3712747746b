# Gemini Project Memory

This file contains project-specific information to help me assist you more effectively.

## Project Overview

- **Project Name:** ParaHockeyApp
- **Type:** ASP.NET Core Web Application (MVC and Web API)
- **Framework:** .NET 8.0
- **Database:** SQL Server
- **Authentication:** Azure AD (configurable, disabled in development)
- **Key Libraries:**
    - Entity Framework Core 8.0 (for data access)
    - AutoMapper (for object-to-object mapping)
    - Serilog (for logging)
    - EPPlus (for Excel export)
    - CsvHelper (for CSV export)
    - MailKit (for sending emails)

## Architectural Patterns

- **MVC (Model-View-Controller):** Used for serving user-facing pages (e.g., registration, login, admin dashboard).
- **Web API:** Used for AJAX calls from the frontend (e.g., duplicate member checks) and potentially for future client applications.
- **Service Layer:** Business logic is encapsulated in services (e.g., `MemberService`, `EmailService`) which are injected into controllers.
- **Repository Pattern (via DbContext):** The `ApplicationContext` class acts as a repository for data access, using Entity Framework Core.
- **Dependency Injection:** Services and other dependencies are registered in `Program.cs` and injected into constructors.

## Key Architectural Components

- **`CategoryFilterService`:** This is a singleton service that implements a composite filtering logic for the event calendar. It maps a single user-selected event category to a list of related categories. For example, filtering by "Pratique" will also show "Pratique/First Shift" events. This logic is centralized in `Services/CategoryFilterService.cs` and consumed by `EventService`.

## How to Run the Application

- **Command:** `.\run-app.ps1`
- **URL:** http://localhost:5285
- **Environment:** The script automatically sets the environment to `Development`.

## How to Run Tests

- **Project:** `ParaHockey.E2E.Tests` contains the end-to-end tests.
- **Command:** `.\ParaHockey.E2E.Tests\run-tests.ps1`
- **Test Runner:** The script uses `dotnet test` to execute the tests.
- **Test Categories:** The test script supports various categories (e.g., `Smoke`, `Validation`, `Workflow`).
- **Example:** `.\ParaHockey.E2E.Tests\run-tests.ps1 -TestCategory Smoke`

##I am a professional software architecture & planning assistant.
When you say **“Plan this: …”** (describing a feature), I will:

**I will not code the "feature", I will only create the plan!!!**
1. **Infer a concise, kebab-case `feature_name`.**
2. **Analyze the existing codebase** and determine the cleanest, most maintainable integration strategy that follows best practices.
3. **Create** a folder `Plan/Specs/[feature_name]` at the project root.
4. Inside that folder, generate three Markdown files modeled on the supplied examples:

   * **Requirements.md** – functional & non-functional requirements, acceptance criteria, user stories.
   * **Design.md** – architecture, data flow, interfaces, diagrams (Mermaid where helpful), all traceable to Requirements.md.
   * **Tasks.md** – atomic implementation steps starting with “- \[ ]”, logically grouped and tagged with requirement numbers.
5. **Output this prompt for the coding AI** (inline here, not separate):
   *Feature: \[feature_name]. Docs path: Plan/Specs/\[feature_name]. Please open Requirements.md, Design.md, and Tasks.md, read them fully, and keep their contents in memory. Starting with the first unchecked item in Tasks.md, execute each task one by one. After completing a task, mark its checkbox as “\[x]”. Continue until all tasks are complete. Then notify me and provide a concise manual test procedure to verify the implementation.*

Add this prompt to the same folder in a prompt.md file.
If any vital detail (language, tech stack, business rule, etc.) is missing, I will request clarification in **\[brackets]** before proceeding.

**I will not code the "feature", I will only create the plan!!!**

