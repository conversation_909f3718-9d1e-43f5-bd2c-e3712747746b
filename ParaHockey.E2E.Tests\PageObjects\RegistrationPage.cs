using OpenQA.Selenium;
using OpenQA.Selenium.Support.UI;
using ParaHockey.E2E.Tests.Infrastructure;

namespace ParaHockey.E2E.Tests.PageObjects
{
    public class RegistrationPage
    {
        private readonly IWebDriver _driver;
        private readonly WebDriverWait _wait;

        public RegistrationPage(IWebDriver driver, WebDriverWait wait)
        {
            _driver = driver;
            _wait = wait;
        }

        // Basic Information Elements
        private IWebElement FirstNameField => _driver.FindElement(By.Name("FirstName"));
        private IWebElement LastNameField => _driver.FindElement(By.Name("LastName"));
        private IWebElement DateOfBirthField => _driver.FindElement(By.Name("DateOfBirth"));
        private IWebElement DatePickerIcon => _driver.FindElement(By.CssSelector(".datepicker-icon"));

        // Gender Radio Buttons
        private IWebElement MaleGenderRadio => _driver.FindElement(By.Id("gender-1"));
        private IWebElement FemaleGenderRadio => _driver.FindElement(By.Id("gender-2"));
        private IWebElement OtherGenderRadio => _driver.FindElement(By.Id("gender-3"));

        // Address Fields
        private IWebElement AddressField => _driver.FindElement(By.Name("Address"));
        private IWebElement CityField => _driver.FindElement(By.Name("City"));
        private IWebElement ProvinceDropdown => _driver.FindElement(By.Name("ProvinceId"));
        private IWebElement PostalCodeField => _driver.FindElement(By.Name("PostalCode"));

        // Contact Information
        private IWebElement PhoneField => _driver.FindElement(By.Name("Phone"));
        private IWebElement MobilePhoneTypeRadio => _driver.FindElement(By.Id("phone-1"));
        private IWebElement OtherPhoneTypeRadio => _driver.FindElement(By.Id("phone-2"));
        private IWebElement EmailField => _driver.FindElement(By.Name("Email"));

        // Registration Type Radio Buttons
        private IWebElement JuniorRegistrationRadio => _driver.FindElement(By.Id("reg-1"));
        private IWebElement DevelopmentRegistrationRadio => _driver.FindElement(By.Id("reg-2"));
        private IWebElement EliteRegistrationRadio => _driver.FindElement(By.Id("reg-3"));
        private IWebElement CoachRegistrationRadio => _driver.FindElement(By.Id("reg-4"));
        private IWebElement VolunteerRegistrationRadio => _driver.FindElement(By.Id("reg-5"));

        // Parent Fields Section (Dynamic)
        private IWebElement ParentFieldsSection => _driver.FindElement(By.Id("parent-fields-section"));
        private IWebElement ParentFirstNameField => _driver.FindElement(By.Id("Parents_0__FirstName"));
        private IWebElement ParentLastNameField => _driver.FindElement(By.Id("Parents_0__LastName"));
        private IWebElement ParentEmailField => _driver.FindElement(By.Id("Parents_0__Email"));
        private IWebElement ParentPhoneField => _driver.FindElement(By.Id("Parents_0__Phone"));

        // Emergency Contact Section (Dynamic)
        private IWebElement EmergencyContactSection => _driver.FindElement(By.Id("emergency-contact-section"));
        private IWebElement EmergencyContactFirstNameField => _driver.FindElement(By.Id("EmergencyContact_FirstName"));
        private IWebElement EmergencyContactLastNameField => _driver.FindElement(By.Id("EmergencyContact_LastName"));
        private IWebElement EmergencyContactEmailField => _driver.FindElement(By.Id("EmergencyContact_Email"));
        private IWebElement EmergencyContactPhoneField => _driver.FindElement(By.Id("EmergencyContact_Phone"));

        // Form Actions
        private IWebElement SubmitButton => _driver.FindElement(By.CssSelector("button[type='submit']"));
        private IWebElement ResetButton => _driver.FindElement(By.CssSelector("button[type='reset']"));

        // Language Switching
        private IWebElement LanguageDropdown => _driver.FindElement(By.Id("languageDropdown"));
        private IWebElement FrenchLanguageLink => _driver.FindElement(By.PartialLinkText("Français"));
        private IWebElement EnglishLanguageLink => _driver.FindElement(By.PartialLinkText("English"));

        // Validation Elements
        private IList<IWebElement> ValidationErrors => _driver.FindElements(By.CssSelector(".text-danger"));
        private IWebElement ValidationSummary => _driver.FindElement(By.CssSelector("[asp-validation-summary]"));

        // Actions - Basic Information
        public void FillBasicInformation(string firstName, string lastName, string dateOfBirth, string gender)
        {
            ClearAndType(FirstNameField, firstName);
            ClearAndType(LastNameField, lastName);
            ClearAndType(DateOfBirthField, dateOfBirth);
            SelectGender(gender);
        }

        public void SelectGender(string gender)
        {
            switch (gender.ToLower())
            {
                case "male":
                case "homme":
                    MaleGenderRadio.Click();
                    break;
                case "female":
                case "femme":
                    FemaleGenderRadio.Click();
                    break;
                case "other":
                case "autre":
                    OtherGenderRadio.Click();
                    break;
            }
        }

        public void FillAddress(string address, string city, string province, string postalCode)
        {
            ClearAndType(AddressField, address);
            ClearAndType(CityField, city);
            SelectProvince(province);
            ClearAndType(PostalCodeField, postalCode);
        }

        public void SelectProvince(string province)
        {
            var select = new SelectElement(ProvinceDropdown);
            select.SelectByValue(province);
        }

        public void FillContactInformation(string phone, string phoneType, string email)
        {
            ClearAndType(PhoneField, phone);
            SelectPhoneType(phoneType);
            ClearAndType(EmailField, email);
        }

        public void SelectPhoneType(string phoneType)
        {
            switch (phoneType.ToLower())
            {
                case "mobile":
                case "cellulaire":
                    MobilePhoneTypeRadio.Click();
                    break;
                case "other":
                case "autre":
                    OtherPhoneTypeRadio.Click();
                    break;
            }
        }

        public void SelectRegistrationType(string registrationType)
        {
            switch (registrationType.ToLower())
            {
                case "junior":
                    JuniorRegistrationRadio.Click();
                    break;
                case "development":
                case "développement":
                    DevelopmentRegistrationRadio.Click();
                    break;
                case "elite":
                case "élite":
                    EliteRegistrationRadio.Click();
                    break;
                case "coach":
                case "entraîneur":
                    CoachRegistrationRadio.Click();
                    break;
                case "volunteer":
                case "bénévole":
                    VolunteerRegistrationRadio.Click();
                    break;
            }

            // Wait for dynamic sections to update
            Thread.Sleep(1000);
        }

        public void FillParentInformation(string firstName, string lastName, string email, string phone)
        {
            _wait.Until(driver => ParentFieldsSection.Displayed);
            ClearAndType(ParentFirstNameField, firstName);
            ClearAndType(ParentLastNameField, lastName);
            ClearAndType(ParentEmailField, email);
            ClearAndType(ParentPhoneField, phone);
        }

        public void FillEmergencyContactInformation(string firstName, string lastName, string email, string phone)
        {
            _wait.Until(driver => EmergencyContactSection.Displayed);
            ClearAndType(EmergencyContactFirstNameField, firstName);
            ClearAndType(EmergencyContactLastNameField, lastName);
            ClearAndType(EmergencyContactEmailField, email);
            ClearAndType(EmergencyContactPhoneField, phone);
        }

        public void SubmitForm()
        {
            SubmitButton.Click();
        }

        public void ResetForm()
        {
            ResetButton.Click();
        }

        public void SwitchToFrench()
        {
            LanguageDropdown.Click();
            _wait.Until(driver => FrenchLanguageLink.Displayed);
            FrenchLanguageLink.Click();
            _wait.Until(driver => ((IJavaScriptExecutor)driver).ExecuteScript("return document.readyState").Equals("complete"));
        }

        public void SwitchToEnglish()
        {
            LanguageDropdown.Click();
            _wait.Until(driver => EnglishLanguageLink.Displayed);
            EnglishLanguageLink.Click();
            _wait.Until(driver => ((IJavaScriptExecutor)driver).ExecuteScript("return document.readyState").Equals("complete"));
        }

        public void ClickDatePicker()
        {
            DatePickerIcon.Click();
        }

        // Verifications
        public bool IsParentSectionVisible() => IsElementVisible(ParentFieldsSection);
        public bool IsEmergencyContactSectionVisible() => IsElementVisible(EmergencyContactSection);
        public bool HasValidationErrors() => ValidationErrors.Count > 0;
        public int ValidationErrorCount => ValidationErrors.Count;
        
        public bool IsFieldValid(IWebElement field)
        {
            return field.GetAttribute("class").Contains("is-valid");
        }

        public bool IsFieldInvalid(IWebElement field)
        {
            return field.GetAttribute("class").Contains("is-invalid");
        }

        public bool IsPhoneMasked()
        {
            var phoneValue = PhoneField.GetAttribute("value");
            return phoneValue.Contains("(") && phoneValue.Contains(")") && phoneValue.Contains("-");
        }

        public bool IsPostalCodeMasked()
        {
            var postalValue = PostalCodeField.GetAttribute("value");
            return postalValue.Length <= 7 && postalValue.Contains(" ");
        }

        public string GetFirstNameValue() => FirstNameField.GetAttribute("value");
        public string GetLastNameValue() => LastNameField.GetAttribute("value");
        public string GetEmailValue() => EmailField.GetAttribute("value");
        public string GetPhoneValue() => PhoneField.GetAttribute("value");
        public string GetPostalCodeValue() => PostalCodeField.GetAttribute("value");

        // Private helper methods
        private void ClearAndType(IWebElement element, string text)
        {
            element.Clear();
            element.SendKeys(text);
        }

        private bool IsElementVisible(IWebElement element)
        {
            try
            {
                return element.Displayed;
            }
            catch (NoSuchElementException)
            {
                return false;
            }
        }
    }
}