# Environment Configuration Comparison

## Database Connections
- **Development**: `Server=DANIEL-COMPLYS\\SQLEXPRESS;Database=ParaHockeyDB_DEV;Trusted_Connection=True;Encrypt=False;`
- **Staging/Test**: `Server=SIMBASQLEXPRESS;User Id=ParaHockeyUser;Password=***************;Database=ParaHockeyDB_TEST;Encrypt=False;TrustServerCertificate=True;`
- **Production**: `Server=SIMBA\\SQLEXPRESS;User Id=ParaHockeyUser;Password=***************;Database=ParaHockeyDB;Encrypt=False;`

## Environment Settings
| Setting | Development | Staging/Test | Production |
|---------|-------------|--------------|------------|
| Name | DEVELOPMENT | TEST | PRODUCTION |
| Theme | info | danger | primary |
| ShowBanner | false | true | false |
| UseAuthentication | true | true | true |
| BannerText | "" | "Parahockey TEST Site" | "" |
| **ShowDevelopmentTools** | false | **true** | false |
| EnableDetailedErrorLogging | true | true | false |
| EnvironmentIndicatorColor | info | danger | primary |
| ShowUserFriendlyErrors | false | true | true |
| ErrorDetailLevel | detailed | detailed | minimal |

## Logging Settings
- **Development**: Default=Information
- **Staging/Test**: Default=Information
- **Production**: Default=Warning

## Other Settings
- **DetailedErrors**: Not in Development, true in both Staging and Production
- **AzureAd**: Only configured in Development (base appsettings.json)
- **Email**: Same configuration across all environments

## Key Findings
1. **ShowDevelopmentTools** is `true` in Staging/Test but `false` in both Development and Production
2. This setting controls whether test buttons appear on the registration form
3. The Production configuration correctly has `ShowDevelopmentTools=false`