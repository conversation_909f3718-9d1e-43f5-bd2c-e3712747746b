# Production HTTP 500.30 Diagnostic Script
# Run this on the SIMBA server to diagnose the startup failure

Write-Host "🚨 DIAGNOSING PRODUCTION HTTP 500.30 ERROR" -ForegroundColor Red
Write-Host "==========================================" -ForegroundColor Red

$prodPath = "C:\inetpub\ParaHockey\Production"

Write-Host "`n1. Checking Production deployment directory..." -ForegroundColor Yellow
if (Test-Path $prodPath) {
    Write-Host "✅ Production directory exists: $prodPath" -ForegroundColor Green
    
    # Check key files
    $keyFiles = @("ParaHockeyApp.dll", "web.config", "appsettings.json", "appsettings.Production.json")
    foreach ($file in $keyFiles) {
        $filePath = "$prodPath\$file"
        if (Test-Path $filePath) {
            Write-Host "  ✓ $file exists" -ForegroundColor Green
        } else {
            Write-Host "  ❌ $file MISSING" -ForegroundColor Red
        }
    }
} else {
    Write-Host "❌ Production directory does not exist!" -ForegroundColor Red
    Write-Host "Expected path: $prodPath" -ForegroundColor Yellow
}

Write-Host "`n2. Checking web.config ASPNETCORE_ENVIRONMENT..." -ForegroundColor Yellow
$webConfigPath = "$prodPath\web.config"
if (Test-Path $webConfigPath) {
    $webConfigContent = Get-Content $webConfigPath -Raw
    if ($webConfigContent -match 'ASPNETCORE_ENVIRONMENT.*value="([^"]*)"') {
        $envValue = $matches[1]
        Write-Host "  Environment set to: $envValue" -ForegroundColor Cyan
        if ($envValue -eq "Production") {
            Write-Host "  ✅ Correct environment" -ForegroundColor Green
        } else {
            Write-Host "  ⚠️ Wrong environment - should be 'Production'" -ForegroundColor Yellow
        }
    } else {
        Write-Host "  ❌ No ASPNETCORE_ENVIRONMENT found in web.config" -ForegroundColor Red
    }
} else {
    Write-Host "  ❌ web.config not found" -ForegroundColor Red
}

Write-Host "`n3. Checking appsettings.Production.json..." -ForegroundColor Yellow
$prodConfigPath = "$prodPath\appsettings.Production.json"
if (Test-Path $prodConfigPath) {
    Write-Host "  ✅ appsettings.Production.json exists" -ForegroundColor Green
    try {
        $prodConfig = Get-Content $prodConfigPath -Raw | ConvertFrom-Json
        Write-Host "  Environment Name: $($prodConfig.Environment.Name)" -ForegroundColor Cyan
        Write-Host "  Database: $($prodConfig.ConnectionStrings.DefaultConnection.Substring(0,50))..." -ForegroundColor Cyan
    } catch {
        Write-Host "  ❌ Invalid JSON in appsettings.Production.json: $_" -ForegroundColor Red
    }
} else {
    Write-Host "  ❌ appsettings.Production.json missing" -ForegroundColor Red
}

Write-Host "`n4. Checking IIS configuration..." -ForegroundColor Yellow
Import-Module WebAdministration -ErrorAction SilentlyContinue
try {
    $website = Get-Website -Name "ParaHockey-Production" -ErrorAction SilentlyContinue
    if ($website) {
        Write-Host "  ✅ Website exists: $($website.Name)" -ForegroundColor Green
        Write-Host "  State: $($website.State)" -ForegroundColor Cyan
        Write-Host "  Physical Path: $($website.PhysicalPath)" -ForegroundColor Cyan
        
        $appPool = Get-IISAppPool -Name "ParaHockey-Production" -ErrorAction SilentlyContinue
        if ($appPool) {
            Write-Host "  ✅ App Pool exists: $($appPool.Name)" -ForegroundColor Green
            Write-Host "  App Pool State: $($appPool.State)" -ForegroundColor Cyan
        } else {
            Write-Host "  ❌ App Pool 'ParaHockey-Production' not found" -ForegroundColor Red
        }
    } else {
        Write-Host "  ❌ Website 'ParaHockey-Production' not found" -ForegroundColor Red
    }
} catch {
    Write-Host "  ⚠️ Cannot check IIS: $_" -ForegroundColor Yellow
}

Write-Host "`n5. Checking recent logs..." -ForegroundColor Yellow
$logPath = "$prodPath\logs"
if (Test-Path $logPath) {
    Write-Host "  ✅ Logs directory exists" -ForegroundColor Green
    $recentLogs = Get-ChildItem $logPath -Filter "*.log" | Sort-Object LastWriteTime -Descending | Select-Object -First 3
    if ($recentLogs) {
        Write-Host "  Recent log files:" -ForegroundColor Cyan
        foreach ($log in $recentLogs) {
            Write-Host "    - $($log.Name) ($(Get-Date $log.LastWriteTime -Format 'yyyy-MM-dd HH:mm'))" -ForegroundColor Gray
        }
        
        Write-Host "`n  Last 10 lines of most recent log:" -ForegroundColor Cyan
        $latestLog = $recentLogs[0]
        $lastLines = Get-Content $latestLog.FullName -Tail 10
        foreach ($line in $lastLines) {
            Write-Host "    $line" -ForegroundColor Gray
        }
    } else {
        Write-Host "  ⚠️ No log files found" -ForegroundColor Yellow
    }
} else {
    Write-Host "  ⚠️ Logs directory does not exist" -ForegroundColor Yellow
}

Write-Host "`n6. Testing database connection..." -ForegroundColor Yellow
try {
    $result = sqlcmd -S "SIMBA\SQLEXPRESS" -d "ParaHockeyDB" -U "ParaHockeyUser" -P "ParaHockey2025!" -Q "SELECT 1" -h -1
    if ($LASTEXITCODE -eq 0) {
        Write-Host "  ✅ Production database connection successful" -ForegroundColor Green
    } else {
        Write-Host "  ❌ Production database connection failed" -ForegroundColor Red
    }
} catch {
    Write-Host "  ❌ Database connection error: $_" -ForegroundColor Red
}

Write-Host "`n7. Checking Windows Event Log..." -ForegroundColor Yellow
try {
    $events = Get-WinEvent -LogName Application -MaxEvents 10 | Where-Object { $_.LevelDisplayName -eq "Error" -and $_.TimeCreated -gt (Get-Date).AddHours(-1) }
    if ($events) {
        Write-Host "  Recent application errors:" -ForegroundColor Red
        foreach ($event in $events) {
            Write-Host "    $($event.TimeCreated): $($event.Message.Substring(0, [Math]::Min(100, $event.Message.Length)))..." -ForegroundColor Gray
        }
    } else {
        Write-Host "  ✅ No recent application errors" -ForegroundColor Green
    }
} catch {
    Write-Host "  ⚠️ Cannot read event log: $_" -ForegroundColor Yellow
}

Write-Host "`n📋 DIAGNOSIS COMPLETE" -ForegroundColor Green
Write-Host "Check the results above for issues that need to be fixed." -ForegroundColor White