# Unified Event Details Modal – **Implementation Plan**  
*Pages impacted*: `/Home/PublicCalendar`, **member-facing** calendar pages (`/MemberPortal/Calendar`, `/Members/CalendarReadOnly`, etc.) &mdash; **admin calendars stay unchanged**  
*Updated: 2025-07-22 – Draft*

---

## 🎯 1 Objectives
1. Re-use the **visitor event-details modal** (blue header) for all non-admin calendars—including the small calendar on `/Events/Subscribe`.
2. Clicking an event in any “Événements à venir” list should open the same modal as a calendar click (admin pages keep their admin modal; others use the shared modal).
3. Eliminate the older, less polished member modal to keep UX consistent and reduce maintenance.
4. Ensure the modal supports both anonymous visitors and authenticated members (extra Register / Unregister actions when logged in).
5. Preserve localisation, accessibility, and responsive behaviour across all pages.

## 🧩 2 Architectural Options
| # | Approach | Pros | Cons |
|---|----------|------|------|
| **A** | **Shared Partial + Shared JS**  
  • Extract modal markup to `Views/Shared/_EventDetailsModal.cshtml`  
  • Move JS helpers to `wwwroot/js/event-details-modal.js`; import on needed pages | • Single source of truth  
• Easy updates across site  
• Works in MVC views and Razor Pages | • Requires editing multiple views to include partial + script |

**Recommendation**: Option **A** – simplest to implement, fully localisable, minimal overhead.

## 📋 3 TODO List (Option A)

1. **Extract Modal Markup**
   1. Copy the visitor modal HTML (header, body layout, close button) to `Views/Shared/_EventDetailsModal.cshtml`.
   2. Replace hard-coded strings with localisation calls (`@SharedLocalizer["EventDetails"]`, etc.).

2. **Consolidate JavaScript**
   1. Create `wwwroot/js/event-details-modal.js` exporting:
      ```js
      window.showEventDetailsModal = function (eventId, isAuthenticated) { /* fetch + render */ };
      ```
   2. Move existing AJAX logic from `PublicCalendar.cshtml` into this file.
   3. Detect login state via `const isAuthenticated = @Json.Serialize(User.Identity.IsAuthenticated);` emitted by Razor.

3. **Refactor Public Calendar Page**
   1. Remove inline modal markup; `@await Html.PartialAsync("_EventDetailsModal")` after calendar.
   2. Replace `eventClick` handler with `showEventDetailsModal(info.event.id, isAuthenticated)`.

4. **Update Member Calendar Pages** (exclude Admin)
   1. Pages: `Views/Members/CalendarReadOnly.cshtml`, `Views/Members/Calendar.cshtml`, any member dashboard calendar widgets.
   2. Remove old modal + JS.
   3. Include shared partial and reference `event-details-modal.js` (add to `@section Scripts`).
   4. Adjust their `eventClick` to call `showEventDetailsModal`.
   5. **Do not touch** admin-only pages under `Views/Admin/*`; they keep the existing detailed modal.
   6. Add `data-event-id` attributes to each item in the **Événements à venir** lists and bind a click handler that calls `showEventDetailsModal(eventId, isAuthenticated)` (or `showAdminEventDetailsModal` on admin pages).

5. **Registration / Unregistration Buttons**
   1. In JS, if `data.requiresRegistration` is true show **Register/Unregister** buttons.
   2. Register button → `POST /Events/Join/{id}`; Unregister → `/Events/Leave/{id}`.
   3. If not authenticated, redirect to login (or reuse existing `showLoginRequiredModal`).

6. **Localization**
   • Ensure keys `EventDetails`, `Date`, `Time`, `Location`, `Description`, `Register`, `Unregister`, `SpotsAvailable`, `AlreadyFull` exist in both RESX files.

7. **Accessibility & Style**
   • Ensure modal gets `role="dialog"`, `aria-labelledby` attributes.
   • Add `max-height: 90vh; overflow-y: auto;` on `.modal-body` for mobile friendliness.

8. **Cleanup**
   • Delete obsolete member-specific modal markup and unused JS functions after migration.

9. **Documentation**
   • Update README or developer wiki to note new shared partial usage.

10. **Testing**
   • Unit: controller returns correct JSON for event details.
   • Integration: GET calendar pages still render 200.
   • E2E (visitor): click event → modal shows; Register button triggers login prompt.
   • E2E (member): click event → modal shows; Register joins event and updates available spots.
   • Responsive: modal scrolls on 375 px width without horizontal overflow.

## 🧪 **Test Now**

### 👀 Visual Consistency
- Public and member calendars show **identical** blue-header modal ✅
- Modal closes via X or Close button ✅

### 🔒 Registration Workflow
- Visitor → Register → Login modal appears ✅
- Member already registered → Unregister button visible ✅
- Event full → Register button disabled ❌

### 🌏 Localization
- Switch to English → all modal labels translated ✅
- Switch back to French → labels in French ✅

---

*End of plan – ready for implementation phase.* 