# Admin Upcoming Events Modal Fix

## User Story
As an admin user, I want clicking an event in the "Événements à venir" (Upcoming Events) sidebar to open the same admin‐level event modal that is triggered when I click an event on the main calendar, so that I can view and manage event details consistently from either location.

## Functional Requirements

1. The Upcoming Events sidebar must list the next N events (currently handled in `AdminController` and `Calendar.cshtml`).
2. Clicking a sidebar event must open the **Admin Event Details Modal** (not the public/member modal).
3. The modal must pre-populate with the selected event’s data (title, date, time, location, registration, etc.) identical to behaviour when clicking the event directly on the calendar grid.
4. All existing admin actions available in the modal (Edit, Publish/Unpublish, Delete, Export, etc.) must remain functional.
5. Keyboard navigation and screen-reader accessibility must be preserved after change.
6. Localisation keys (`@SharedLocalizer["Key"]`) must cover any new or relocated text.
7. No regression: clicks on events in the calendar grid continue to open the admin modal.

## Non-Functional Requirements

1. The fix must follow ASP.NET Core MVC architecture and coding standards outlined in project guidelines.
2. JavaScript must reuse existing modal/FullCalendar logic where possible—avoid duplication.
3. Performance: modal must open within 300 ms on typical desktop.
4. Security: ensure only authenticated users with proper roles can trigger admin modal.
5. Testing: automated unit/integration tests to cover click interaction and modal rendering.

## Acceptance Criteria

| # | Given | When | Then |
|---|-------|------|------|
| AC1 | I am on the admin calendar page | I click an event in the Upcoming Events sidebar | The **Admin Event Details Modal** opens populated with that event’s information |
| AC2 | I am on the admin calendar page | I click an event in the main calendar grid | Modal behaviour remains unchanged |
| AC3 | I am an unauthorised visitor | I attempt to open upcoming event modal via direct URL | I receive an access-denied response |
| AC4 | I use keyboard navigation | I focus a sidebar event and press **Enter** | The modal opens and focus moves to the modal header |
| AC5 | The site language is switched to English or French | I open the modal | All text appears in the selected language | 