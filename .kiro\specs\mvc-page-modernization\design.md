# Design Document

## Overview

This design outlines the systematic modernization of the ParaHockey ASP.NET Core MVC application to meet 2025 web standards. The application currently consists of 6 main controllers (Admin, Events, Home, Language, Members, Base) with comprehensive localization support (French/English) and a well-established design system. The modernization will be executed through a page-by-page audit and upgrade process, ensuring security, accessibility, performance, and mobile-first responsive design while maintaining production stability.

## Architecture

### Current Application Structure

**Controllers & Pages Identified:**
- **HomeController**: Index, Privacy, PublicCalendar, Logout
- **AdminController**: Index (Dashboard), Members, MemberDetails, SystemInfo, AdminUsers, SetupMasterAdmin
- **MembersController**: Register, Login, Dashboard, EditProfile, CalendarReadOnly
- **EventsController**: Subscribe, Calendar management
- **LanguageController**: Culture switching
- **API Controllers**: Various endpoints for AJAX operations

**Current Technology Stack:**
- ASP.NET Core MVC with Razor Pages
- Entity Framework Core with SQL Server
- Bootstrap 5 for UI framework
- jQuery and jQ<PERSON>y UI for client-side functionality
- Comprehensive localization with .resx files
- Azure AD authentication (conditional)
- Custom design system with CSS variables

### Modernization Architecture

```mermaid
graph TB
    A[Page Inventory & Planning] --> B[Page-by-Page Audit Cycle]
    B --> C[Security Hardening]
    B --> D[Mobile-First Responsive Design]
    B --> E[Accessibility Compliance]
    B --> F[Performance Optimization]
    B --> G[Localization Enhancement]
    B --> H[Code Quality Improvement]
    
    C --> I[DEV Environment Testing]
    D --> I
    E --> I
    F --> I
    G --> I
    H --> I
    
    I --> J[PR Review & Approval]
    J --> K[Main Branch Merge]
    K --> L[Test Environment]
    L --> M[Production Deployment]
```

## Components and Interfaces

### 1. Page Audit Framework

**PageAuditService Interface:**
```csharp
public interface IPageAuditService
{
    Task<PageInventory> GenerateInventoryAsync();
    Task<PageAuditResult> AuditPageAsync(string pageName);
    Task<ValidationResult> ValidateSecurityAsync(string pageName);
    Task<AccessibilityResult> ValidateAccessibilityAsync(string pageName);
    Task<PerformanceResult> ValidatePerformanceAsync(string pageName);
    Task<LocalizationResult> ValidateLocalizationAsync(string pageName);
}
```

**Page Audit Models:**
```csharp
public class PageAuditResult
{
    public string PageName { get; set; }
    public List<string> Routes { get; set; }
    public List<string> Files { get; set; }
    public ComplexityLevel Complexity { get; set; }
    public List<AuditFinding> Findings { get; set; }
    public SecurityAuditResult Security { get; set; }
    public AccessibilityAuditResult Accessibility { get; set; }
    public PerformanceAuditResult Performance { get; set; }
    public LocalizationAuditResult Localization { get; set; }
}

public class AuditFinding
{
    public string Issue { get; set; }
    public Severity Severity { get; set; }
    public string Rationale { get; set; }
    public string FixPlan { get; set; }
    public string Category { get; set; }
}
```

### 2. Security Enhancement Framework

**Security Audit Components:**
- Anti-forgery token validation checker
- Authentication/Authorization analyzer
- Model binding security scanner
- Output encoding validator
- Cookie security configuration checker
- CSP header analyzer

**Implementation Pattern:**
```csharp
[HttpPost]
[ValidateAntiForgeryToken]
public async Task<IActionResult> SecureAction(SecureViewModel model)
{
    if (!ModelState.IsValid)
    {
        return View(model);
    }
    
    // Server-side validation as source of truth
    var validationResult = await _validationService.ValidateAsync(model);
    if (!validationResult.IsValid)
    {
        foreach (var error in validationResult.Errors)
        {
            ModelState.AddModelError(error.PropertyName, _localizer[error.ErrorKey]);
        }
        return View(model);
    }
    
    // Process with proper authorization
    var result = await _service.ProcessAsync(model);
    return RedirectToAction("Success");
}
```

### 3. Mobile-First Responsive Design System

**Design Token Structure:**
```css
:root {
  /* Spacing Scale */
  --ph-space-xs: 0.25rem;
  --ph-space-sm: 0.5rem;
  --ph-space-md: 1rem;
  --ph-space-lg: 1.5rem;
  --ph-space-xl: 2rem;
  
  /* Typography Scale */
  --ph-text-xs: 0.75rem;
  --ph-text-sm: 0.875rem;
  --ph-text-base: 1rem;
  --ph-text-lg: 1.125rem;
  --ph-text-xl: 1.25rem;
  
  /* Breakpoints */
  --ph-breakpoint-sm: 576px;
  --ph-breakpoint-md: 768px;
  --ph-breakpoint-lg: 992px;
  --ph-breakpoint-xl: 1200px;
}
```

**Component Architecture:**
- Atomic design principles (atoms, molecules, organisms)
- Mobile-first media queries
- Touch-friendly tap targets (minimum 44px)
- Dark mode and high contrast support
- Consistent spacing and typography scales

### 4. Accessibility Compliance Framework

**WCAG 2.2 AA Implementation:**
```html
<!-- Semantic Form Structure -->
<form asp-action="Register" asp-antiforgery="true" role="form" aria-labelledby="registration-title">
    <h1 id="registration-title">@Localizer["RegistrationFormTitle"]</h1>
    
    <div asp-validation-summary="All" class="alert alert-danger" role="alert" aria-live="polite"></div>
    
    <div class="form-group">
        <label asp-for="FirstName" class="form-label">@Localizer["FirstName"]</label>
        <input asp-for="FirstName" class="form-control" aria-describedby="firstName-help firstName-error" />
        <div id="firstName-help" class="form-text">@Localizer["FirstNameHelp"]</div>
        <span asp-validation-for="FirstName" id="firstName-error" class="text-danger" role="alert"></span>
    </div>
</form>
```

**Accessibility Testing Integration:**
- Automated axe-core testing in CI/CD
- Keyboard navigation validation
- Screen reader compatibility testing
- Color contrast verification
- Focus management validation

### 5. Performance Optimization Framework

**Core Web Vitals Optimization:**
- Bundle splitting and lazy loading
- Image optimization and responsive images
- Critical CSS inlining
- JavaScript deferring and async loading
- Database query optimization
- Caching strategies

**Implementation Example:**
```html
<!-- Critical CSS inline -->
<style>
  /* Critical above-the-fold styles */
</style>

<!-- Non-critical CSS deferred -->
<link rel="preload" href="~/css/non-critical.css" as="style" onload="this.onload=null;this.rel='stylesheet'">

<!-- Optimized images -->
<img src="~/images/hero-mobile.webp" 
     srcset="~/images/hero-mobile.webp 576w, ~/images/hero-tablet.webp 768w, ~/images/hero-desktop.webp 1200w"
     sizes="(max-width: 576px) 100vw, (max-width: 768px) 50vw, 33vw"
     alt="@Localizer["HeroImageAlt"]"
     loading="lazy" />
```

### 6. Enhanced Localization Framework

**Localization Key Management:**
```csharp
public static class LocalizationKeys
{
    public static class Forms
    {
        public static class Member
        {
            public const string FirstNameLabel = "Form.Member.FirstName.Label";
            public const string FirstNamePlaceholder = "Form.Member.FirstName.Placeholder";
            public const string FirstNameValidation = "Form.Member.FirstName.Validation";
        }
    }
    
    public static class Validation
    {
        public const string Required = "Validation.Required";
        public const string EmailFormat = "Validation.Email.Format";
        public const string PhoneFormat = "Validation.Phone.Format";
    }
}
```

**Culture-Aware Formatting:**
```csharp
public class CultureAwareFormattingService
{
    public string FormatDate(DateTime date, string culture)
    {
        var cultureInfo = new CultureInfo(culture);
        return date.ToString("d", cultureInfo);
    }
    
    public string FormatPhone(string phone, string culture)
    {
        // Format phone numbers according to culture
        return culture switch
        {
            "fr-CA" => FormatCanadianPhone(phone),
            "en-CA" => FormatCanadianPhone(phone),
            _ => phone
        };
    }
}
```

## Data Models

### Page Audit Data Models

```csharp
public class PageInventory
{
    public List<PageInfo> Pages { get; set; }
    public DateTime GeneratedAt { get; set; }
    public string GeneratedBy { get; set; }
}

public class PageInfo
{
    public string Name { get; set; }
    public string Controller { get; set; }
    public string Action { get; set; }
    public List<string> Routes { get; set; }
    public List<string> ViewFiles { get; set; }
    public List<string> JavaScriptFiles { get; set; }
    public List<string> StylesheetFiles { get; set; }
    public ComplexityLevel Complexity { get; set; }
    public List<string> Dependencies { get; set; }
    public int Priority { get; set; }
}

public enum ComplexityLevel
{
    Low = 1,    // Simple display pages
    Medium = 2, // Forms with basic validation
    High = 3,   // Complex forms with multiple sections
    Critical = 4 // Admin pages with sensitive operations
}

public enum Severity
{
    Low = 1,
    Medium = 2,
    High = 3,
    Critical = 4
}
```

### Security Audit Models

```csharp
public class SecurityAuditResult
{
    public bool HasAntiForgeryTokens { get; set; }
    public bool HasProperAuthorization { get; set; }
    public bool UsesViewModels { get; set; }
    public bool HasServerSideValidation { get; set; }
    public bool HasProperOutputEncoding { get; set; }
    public bool HasSecureCookies { get; set; }
    public bool HasCSPHeaders { get; set; }
    public List<SecurityFinding> Findings { get; set; }
}

public class SecurityFinding
{
    public string Issue { get; set; }
    public SecurityRisk Risk { get; set; }
    public string Recommendation { get; set; }
    public string CodeLocation { get; set; }
}

public enum SecurityRisk
{
    Low,
    Medium,
    High,
    Critical
}
```

### Form Enhancement Models

```csharp
public class ModernFormViewModel
{
    [Required(ErrorMessageResourceName = "ValidationRequired", ErrorMessageResourceType = typeof(SharedResourceMarker))]
    [StringLength(50, MinimumLength = 2, ErrorMessageResourceName = "ValidationStringLength", ErrorMessageResourceType = typeof(SharedResourceMarker))]
    [Display(Name = "FirstName", ResourceType = typeof(SharedResourceMarker))]
    public string FirstName { get; set; }

    [Required(ErrorMessageResourceName = "ValidationRequired", ErrorMessageResourceType = typeof(SharedResourceMarker))]
    [EmailAddress(ErrorMessageResourceName = "ValidationEmailFormat", ErrorMessageResourceType = typeof(SharedResourceMarker))]
    [Display(Name = "Email", ResourceType = typeof(SharedResourceMarker))]
    public string Email { get; set; }

    [Required(ErrorMessageResourceName = "ValidationRequired", ErrorMessageResourceType = typeof(SharedResourceMarker))]
    [RegularExpression(@"^\d{3}-\d{3}-\d{4}$", ErrorMessageResourceName = "ValidationPhoneFormat", ErrorMessageResourceType = typeof(SharedResourceMarker))]
    [Display(Name = "Phone", ResourceType = typeof(SharedResourceMarker))]
    public string Phone { get; set; }
}
```

## Error Handling

### Centralized Error Handling

```csharp
public class GlobalErrorHandlerMiddleware
{
    private readonly RequestDelegate _next;
    private readonly ILogger<GlobalErrorHandlerMiddleware> _logger;
    private readonly IStringLocalizer<SharedResourceMarker> _localizer;

    public async Task InvokeAsync(HttpContext context)
    {
        try
        {
            await _next(context);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Unhandled exception occurred");
            await HandleExceptionAsync(context, ex);
        }
    }

    private async Task HandleExceptionAsync(HttpContext context, Exception exception)
    {
        context.Response.StatusCode = 500;
        context.Response.ContentType = "application/json";

        var response = new ErrorResponse
        {
            Message = _localizer["GenericErrorMessage"],
            Details = context.RequestServices.GetService<IWebHostEnvironment>()?.IsDevelopment() == true 
                ? exception.Message 
                : null
        };

        await context.Response.WriteAsync(JsonSerializer.Serialize(response));
    }
}
```

### Form Validation Error Handling

```csharp
public class ValidationErrorHandler
{
    public static void HandleValidationErrors(ModelStateDictionary modelState, IStringLocalizer localizer)
    {
        foreach (var error in modelState.Values.SelectMany(v => v.Errors))
        {
            // Log validation errors for analysis
            // Ensure error messages are localized
            // Provide user-friendly error summaries
        }
    }
}
```

## Testing Strategy

### Automated Testing Framework

**Unit Testing:**
```csharp
[Test]
public async Task Register_ValidModel_ReturnsSuccessView()
{
    // Arrange
    var model = new MemberRegistrationViewModel { /* valid data */ };
    var controller = new MembersController(/* dependencies */);

    // Act
    var result = await controller.Register(model);

    // Assert
    Assert.IsInstanceOf<ViewResult>(result);
    Assert.IsTrue(controller.ModelState.IsValid);
}
```

**Integration Testing:**
```csharp
[Test]
public async Task RegisterMember_EndToEnd_CreatesValidMember()
{
    // Test complete registration flow
    // Verify database persistence
    // Validate email sending
    // Check audit logging
}
```

**Accessibility Testing:**
```javascript
// Automated accessibility testing with axe-core
describe('Member Registration Page', () => {
    it('should have no accessibility violations', async () => {
        await page.goto('/Members/Register');
        const results = await new AxePuppeteer(page).analyze();
        expect(results.violations).toHaveLength(0);
    });
});
```

**Performance Testing:**
```javascript
// Lighthouse CI integration
describe('Performance Tests', () => {
    it('should meet Core Web Vitals thresholds', async () => {
        const result = await lighthouse(url, options);
        expect(result.lhr.audits['largest-contentful-paint'].score).toBeGreaterThan(0.9);
        expect(result.lhr.audits['cumulative-layout-shift'].score).toBeGreaterThan(0.9);
    });
});
```

### Manual Testing Procedures

**Cross-Browser Testing:**
- Chrome, Firefox, Safari, Edge
- Mobile browsers (iOS Safari, Chrome Mobile)
- Screen reader testing (NVDA, JAWS, VoiceOver)

**Localization Testing:**
- French and English content verification
- Date/time formatting validation
- Currency and number formatting
- Right-to-left layout testing (future consideration)

**Security Testing:**
- OWASP ZAP automated scanning
- Manual penetration testing
- Authentication bypass attempts
- CSRF protection validation

## Implementation Phases

### Phase 1: Foundation & Planning (Week 1)
1. Generate complete page inventory
2. Create page review plan with priorities
3. Set up automated testing infrastructure
4. Establish branching and deployment procedures

### Phase 2: Core Pages Modernization (Weeks 2-4)
1. **Home/Index** - Landing page optimization
2. **Members/Register** - Registration form enhancement
3. **Members/Login** - Authentication flow improvement
4. **Admin/Index** - Dashboard modernization

### Phase 3: Administrative Pages (Weeks 5-6)
1. **Admin/Members** - Member management interface
2. **Admin/MemberDetails** - Detailed member view
3. **Admin/SystemInfo** - System information display

### Phase 4: Secondary Pages (Weeks 7-8)
1. **Events** pages - Event management
2. **Members/Dashboard** - Member portal
3. **Members/EditProfile** - Profile management

### Phase 5: Final Polish & Optimization (Week 9)
1. Performance optimization across all pages
2. Final accessibility audit and fixes
3. Security hardening review
4. Documentation completion

## Risk Mitigation

### Production Safety Measures
- Feature branch isolation for each page
- Mandatory DEV environment validation
- Automated rollback procedures
- Database backup requirements for schema changes
- Gradual rollout with feature flags

### Quality Assurance
- Automated testing gate before merge
- Manual testing checklist completion
- Accessibility audit verification
- Performance benchmark validation
- Security scan clearance

### Monitoring and Alerting
- Application performance monitoring
- Error tracking and alerting
- User experience metrics
- Security incident detection
- Accessibility compliance monitoring

This design provides a comprehensive framework for systematically modernizing the ParaHockey MVC application while maintaining production stability and delivering a superior user experience across all devices and languages.