using CsvHelper;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Localization;
using Microsoft.Extensions.Logging;
using OfficeOpenXml;
using OfficeOpenXml.Style;
using ParaHockeyApp.DTOs;
using ParaHockeyApp.Models;
using ParaHockeyApp.Models.Entities;
using ParaHockeyApp.Resources;
using System.Globalization;
using System.Text;

namespace ParaHockeyApp.Services
{
    /// <summary>
    /// Implementation of member export functionality with CSV and Excel support
    /// Provides intelligent file naming and comprehensive filter options
    /// </summary>
    public class MemberExportService : IMemberExportService
    {
        private readonly ApplicationContext _context;
        private readonly IMemberSearchService _memberSearchService;
        private readonly ILogger<MemberExportService> _logger;
        private readonly IStringLocalizer<SharedResourceMarker> _localizer;

        public MemberExportService(
            ApplicationContext context,
            IMemberSearchService memberSearchService,
            ILogger<MemberExportService> logger,
            IStringLocalizer<SharedResourceMarker> localizer)
        {
            _context = context;
            _memberSearchService = memberSearchService;
            _logger = logger;
            _localizer = localizer;
        }

        /// <inheritdoc/>
        public async Task<byte[]> ExportToCsvAsync(MemberExportRequest request)
        {
            try
            {
                _logger.LogInformation("Starting CSV export with format: {Format}", request.Format);

                // Get all matching members without pagination
                var searchRequest = CreateSearchRequestForExport(request);
                var searchResult = await _memberSearchService.SearchMembersAsync(searchRequest);

                using var memoryStream = new MemoryStream();
                using var writer = new StreamWriter(memoryStream, Encoding.UTF8);
                using var csv = new CsvWriter(writer, CultureInfo.InvariantCulture);

                // Configure CSV settings
                csv.Context.Configuration.HasHeaderRecord = request.IncludeHeaders;

                // Write headers if requested
                if (request.IncludeHeaders)
                {
                    WriteCSVHeaders(csv, request.SelectedFields);
                }

                // Write data rows
                foreach (var member in searchResult.Members)
                {
                    WriteCSVRow(csv, member, request.SelectedFields);
                }

                await writer.FlushAsync();
                return memoryStream.ToArray();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error exporting members to CSV");
                throw;
            }
        }

        /// <inheritdoc/>
        public async Task<byte[]> ExportToExcelAsync(MemberExportRequest request)
        {
            try
            {
                _logger.LogInformation("Starting Excel export with format: {Format}", request.Format);

                // Get all matching members without pagination
                var searchRequest = CreateSearchRequestForExport(request);
                var searchResult = await _memberSearchService.SearchMembersAsync(searchRequest);

                using var package = new ExcelPackage();
                var worksheet = package.Workbook.Worksheets.Add("Members");

                var currentRow = 1;

                // Add headers if requested
                if (request.IncludeHeaders)
                {
                    WriteExcelHeaders(worksheet, currentRow, request.SelectedFields);
                    FormatHeaderRow(worksheet, currentRow, GetSelectedFieldCount(request.SelectedFields));
                    currentRow++;
                }

                // Add data rows
                foreach (var member in searchResult.Members)
                {
                    WriteExcelRow(worksheet, currentRow, member, request.SelectedFields);
                    currentRow++;
                }

                // Auto-fit columns
                worksheet.Cells[worksheet.Dimension.Address].AutoFitColumns();

                // Apply alternating row colors for better readability
                ApplyAlternatingRowColors(worksheet, request.IncludeHeaders ? 2 : 1, currentRow - 1, GetSelectedFieldCount(request.SelectedFields));

                return package.GetAsByteArray();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error exporting members to Excel");
                throw;
            }
        }

        /// <inheritdoc/>
        public string GenerateFileName(MemberExportRequest request)
        {
            var parts = new List<string>();

            // Add search term if present
            if (!string.IsNullOrWhiteSpace(request.SearchTerm))
            {
                parts.Add($"Search_{SanitizeFileName(request.SearchTerm)}");
            }

            // Add registration type filter
            if (request.RegistrationTypeIds?.Any() == true)
            {
                if (request.RegistrationTypeIds.Count == 1)
                {
                    parts.Add($"Type_{request.RegistrationTypeIds.First()}");
                }
                else
                {
                    parts.Add($"Types_{string.Join("-", request.RegistrationTypeIds)}");
                }
            }

            // Add province filter
            if (!string.IsNullOrWhiteSpace(request.Province))
            {
                parts.Add($"Province_{SanitizeFileName(request.Province)}");
            }

            // Add city filter
            if (!string.IsNullOrWhiteSpace(request.City))
            {
                parts.Add($"City_{SanitizeFileName(request.City)}");
            }

            // Add status filter
            if (request.IsActive.HasValue)
            {
                parts.Add($"Status_{(request.IsActive.Value ? "Active" : "Inactive")}");
            }

            // Add age range if specified
            if (request.AgeFrom.HasValue || request.AgeTo.HasValue)
            {
                var ageRange = $"Age_{request.AgeFrom ?? 0}-{request.AgeTo ?? 120}";
                parts.Add(ageRange);
            }

            // Create base filename
            var baseName = parts.Any() ? string.Join("_", parts) : "All_Members";
            
            // Add date
            var date = DateTime.Now.ToString("yyyy_MMMM_dd", CultureInfo.InvariantCulture);
            
            // Get extension
            var extension = request.GetFileExtension().TrimStart('.');

            return $"{baseName}_{date}.{extension}";
        }

        /// <inheritdoc/>
        public async Task<string> GenerateFileNameAsync(MemberExportRequest request)
        {
            var parts = new List<string>();

            // Add search term if present
            if (!string.IsNullOrWhiteSpace(request.SearchTerm))
            {
                parts.Add($"Search_{SanitizeFileName(request.SearchTerm)}");
            }

            // Add registration type filter with actual names
            if (request.RegistrationTypeIds?.Any() == true)
            {
                var registrationTypes = await _context.RegistrationTypes
                    .Where(rt => request.RegistrationTypeIds.Contains(rt.Id))
                    .ToListAsync();

                if (registrationTypes.Count == 1)
                {
                    var localizedName = _localizer[registrationTypes.First().DisplayNameKey].Value;
                    parts.Add($"Type_{SanitizeFileName(localizedName)}");
                }
                else
                {
                    var names = registrationTypes.Select(rt => SanitizeFileName(_localizer[rt.DisplayNameKey].Value));
                    parts.Add($"Types_{string.Join("-", names)}");
                }
            }

            // Add province filter with actual name
            if (!string.IsNullOrWhiteSpace(request.Province))
            {
                var province = await _context.Provinces
                    .FirstOrDefaultAsync(p => p.Code == request.Province);
                
                if (province != null)
                {
                    var localizedName = _localizer[province.DisplayNameKey].Value;
                    parts.Add($"Province_{SanitizeFileName(localizedName)}");
                }
                else
                {
                    parts.Add($"Province_{SanitizeFileName(request.Province)}");
                }
            }

            // Add city filter
            if (!string.IsNullOrWhiteSpace(request.City))
            {
                parts.Add($"City_{SanitizeFileName(request.City)}");
            }

            // Add status filter with localized names
            if (request.IsActive.HasValue)
            {
                var statusText = request.IsActive.Value ? _localizer["Active"].Value : _localizer["Inactive"].Value;
                parts.Add($"Status_{SanitizeFileName(statusText)}");
            }

            // Add age range if specified
            if (request.AgeFrom.HasValue || request.AgeTo.HasValue)
            {
                var ageRange = $"Age_{request.AgeFrom ?? 0}-{request.AgeTo ?? 120}";
                parts.Add(ageRange);
            }

            // Create base filename
            var baseName = parts.Any() ? string.Join("_", parts) : "All_Members";
            
            // Add date
            var date = DateTime.Now.ToString("yyyy_MMMM_dd", CultureInfo.InvariantCulture);
            
            // Get extension
            var extension = request.GetFileExtension().TrimStart('.');

            return $"{baseName}_{date}.{extension}";
        }

        /// <inheritdoc/>
        public async Task<FilterOptionsResponse> GetFilterOptionsAsync(string filterType, string? searchTerm = null)
        {
            try
            {
                return filterType.ToLower() switch
                {
                    "registrationtype" => await GetRegistrationTypeOptionsAsync(),
                    "province" => await GetProvinceOptionsAsync(),
                    "city" => await GetCityOptionsAsync(searchTerm),
                    "status" => GetStatusOptions(),
                    _ => FilterOptionsResponse.CreateError(filterType, "Unknown filter type")
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting filter options for type: {FilterType}", filterType);
                return FilterOptionsResponse.CreateError(filterType, "Error retrieving filter options");
            }
        }

        #region Private Helper Methods

        private MemberSearchRequest CreateSearchRequestForExport(MemberExportRequest request)
        {
            return new MemberSearchRequest
            {
                SearchTerm = request.SearchTerm,
                RegistrationTypeIds = request.RegistrationTypeIds,
                IsActive = request.IsActive,
                DateOfBirthFrom = request.DateOfBirthFrom,
                DateOfBirthTo = request.DateOfBirthTo,
                AgeFrom = request.AgeFrom,
                AgeTo = request.AgeTo,
                City = request.City,
                Province = request.Province,
                PostalCode = request.PostalCode,
                Page = 1,
                PageSize = 50000, // Large number to get all results
                SortBy = request.SortBy,
                SortDescending = request.SortDescending
            };
        }

        private void WriteCSVHeaders(CsvWriter csv, List<string>? selectedFields)
        {
            var fields = GetFieldsToExport(selectedFields);
            foreach (var field in fields)
            {
                csv.WriteField(GetFieldDisplayName(field.Key));
            }
            csv.NextRecord();
        }

        private void WriteCSVRow(CsvWriter csv, MemberSearchResultItem member, List<string>? selectedFields)
        {
            var fields = GetFieldsToExport(selectedFields);
            foreach (var field in fields)
            {
                csv.WriteField(GetFieldValue(member, field.Key));
            }
            csv.NextRecord();
        }

        private void WriteExcelHeaders(ExcelWorksheet worksheet, int row, List<string>? selectedFields)
        {
            var fields = GetFieldsToExport(selectedFields);
            var col = 1;
            foreach (var field in fields)
            {
                worksheet.Cells[row, col].Value = GetFieldDisplayName(field.Key);
                col++;
            }
        }

        private void WriteExcelRow(ExcelWorksheet worksheet, int row, MemberSearchResultItem member, List<string>? selectedFields)
        {
            var fields = GetFieldsToExport(selectedFields);
            var col = 1;
            foreach (var field in fields)
            {
                var value = GetFieldValue(member, field.Key);
                worksheet.Cells[row, col].Value = value;
                col++;
            }
        }

        private void FormatHeaderRow(ExcelWorksheet worksheet, int row, int columnCount)
        {
            var headerRange = worksheet.Cells[row, 1, row, columnCount];
            headerRange.Style.Font.Bold = true;
            headerRange.Style.Fill.PatternType = ExcelFillStyle.Solid;
            headerRange.Style.Fill.BackgroundColor.SetColor(System.Drawing.Color.LightGray);
            headerRange.Style.Border.BorderAround(ExcelBorderStyle.Thin);
        }

        private void ApplyAlternatingRowColors(ExcelWorksheet worksheet, int startRow, int endRow, int columnCount)
        {
            for (int row = startRow; row <= endRow; row++)
            {
                if (row % 2 == 0)
                {
                    var range = worksheet.Cells[row, 1, row, columnCount];
                    range.Style.Fill.PatternType = ExcelFillStyle.Solid;
                    range.Style.Fill.BackgroundColor.SetColor(System.Drawing.Color.FromArgb(245, 245, 245));
                }
            }
        }

        private Dictionary<string, string> GetFieldsToExport(List<string>? selectedFields)
        {
            var allFields = new Dictionary<string, string>
            {
                { "Id", "Member ID" },
                { "FirstName", "First Name" },
                { "LastName", "Last Name" },
                { "Email", "Email Address" },
                { "Phone", "Phone Number" },
                { "DateOfBirth", "Date of Birth" },
                { "Age", "Age" },
                { "Address", "Address" },
                { "City", "City" },
                { "Province", "Province" },
                { "PostalCode", "Postal Code" },
                { "RegistrationTypeName", "Registration Type" },
                { "StatusText", "Status" },
                { "DateCreated", "Registration Date" }
            };

            if (selectedFields?.Any() == true)
            {
                return allFields.Where(f => selectedFields.Contains(f.Key)).ToDictionary(f => f.Key, f => f.Value);
            }

            return allFields;
        }

        private string GetFieldDisplayName(string fieldName)
        {
            return GetFieldsToExport(null)[fieldName];
        }

        private object GetFieldValue(MemberSearchResultItem member, string fieldName)
        {
            return fieldName switch
            {
                "Id" => member.Id,
                "FirstName" => member.FirstName,
                "LastName" => member.LastName,
                "Email" => member.Email,
                "Phone" => member.Phone,
                "DateOfBirth" => member.DateOfBirth.ToString("yyyy-MM-dd"),
                "Age" => member.Age,
                "Address" => member.Address,
                "City" => member.City,
                "Province" => member.Province,
                "PostalCode" => member.PostalCode,
                "RegistrationTypeName" => member.RegistrationTypeName,
                "StatusText" => member.StatusText,
                "DateCreated" => member.DateCreated.ToString("yyyy-MM-dd HH:mm"),
                _ => string.Empty
            };
        }

        private int GetSelectedFieldCount(List<string>? selectedFields)
        {
            return selectedFields?.Count ?? GetFieldsToExport(null).Count;
        }

        private string SanitizeFileName(string fileName)
        {
            var invalidChars = Path.GetInvalidFileNameChars();
            var sanitized = new string(fileName.Where(c => !invalidChars.Contains(c)).ToArray());
            return sanitized.Replace(" ", "_").Replace(".", "_");
        }

        private async Task<FilterOptionsResponse> GetRegistrationTypeOptionsAsync()
        {
            var registrationTypes = await _context.RegistrationTypes
                .OrderBy(rt => rt.DisplayNameKey)
                .ToListAsync();

            var options = registrationTypes.Select(rt => new FilterOption(
                rt.Id.ToString(),
                _localizer[rt.DisplayNameKey].Value,
                null // Could add count if needed
            )).ToList();

            return new FilterOptionsResponse("registrationtype", options);
        }

        private async Task<FilterOptionsResponse> GetProvinceOptionsAsync()
        {
            var provinces = await _context.Provinces
                .OrderBy(p => p.DisplayNameKey)
                .ToListAsync();

            var options = provinces.Select(p => new FilterOption(
                p.Code,
                _localizer[p.DisplayNameKey].Value,
                null
            )).ToList();

            return new FilterOptionsResponse("province", options);
        }

        private async Task<FilterOptionsResponse> GetCityOptionsAsync(string? searchTerm = null)
        {
            var query = _context.Members.AsQueryable();

            if (!string.IsNullOrWhiteSpace(searchTerm))
            {
                query = query.Where(m => m.City.ToLower().StartsWith(searchTerm.ToLower()));
            }

            var cities = await query
                .GroupBy(m => m.City)
                .Select(g => new { City = g.Key, Count = g.Count() })
                .OrderBy(x => x.City)
                .Take(50) // Limit for performance
                .ToListAsync();

            var options = cities.Select(c => new FilterOption(
                c.City,
                c.City,
                c.Count
            )).ToList();

            return new FilterOptionsResponse("city", options);
        }

        private FilterOptionsResponse GetStatusOptions()
        {
            var options = new List<FilterOption>
            {
                new FilterOption("true", _localizer["Active"].Value, null),
                new FilterOption("false", _localizer["Inactive"].Value, null)
            };

            return new FilterOptionsResponse("status", options);
        }

        #endregion
    }
}