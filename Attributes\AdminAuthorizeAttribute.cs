using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Filters;
using ParaHockeyApp.Models.Entities;
using ParaHockeyApp.Services;

namespace ParaHockeyApp.Attributes
{
    /// <summary>
    /// Custom authorization attribute that ensures only active admin users can access the action
    /// </summary>
    public class AdminAuthorizeAttribute : Attribute, IAuthorizationFilter
    {
        private readonly AdminType _minimumAdminType;

        /// <summary>
        /// Initialize with minimum admin type required (defaults to Normal)
        /// </summary>
        public AdminAuthorizeAttribute(AdminType minimumAdminType = AdminType.Normal)
        {
            _minimumAdminType = minimumAdminType;
        }

        public void OnAuthorization(AuthorizationFilterContext context)
        {
            // First check if user is authenticated
            if (!context.HttpContext.User.Identity?.IsAuthenticated ?? true)
            {
                context.Result = new RedirectToActionResult("Login", "Account", null);
                return;
            }

            // Get user context service
            var userContextService = context.HttpContext.RequestServices.GetService<IUserContextService>();
            if (userContextService == null)
            {
                context.Result = new ForbidResult();
                return;
            }

            // Check if current user is an admin
            var currentUser = userContextService.GetCurrentUser();
            if (currentUser == null)
            {
                context.Result = new RedirectToActionResult("Login", "Account", null);
                return;
            }

            // For now, we'll use a simple check - in a real implementation, you'd check admin status
            // This assumes your IUserContextService can identify admin users
            // You may need to modify this based on your actual user context implementation
            
            // If user context service has an admin check method, use it
            if (userContextService is IAdminUserContextService adminService)
            {
                var adminUser = adminService.GetCurrentAdminUser();
                if (adminUser == null || !adminUser.CanLogin || (int)adminUser.AdminType < (int)_minimumAdminType)
                {
                    context.Result = new ForbidResult();
                    return;
                }
            }
            else
            {
                // Fallback: assume any authenticated user is admin for now
                // This should be replaced with proper admin role checking
                var userName = currentUser.UserName?.ToLower();
                if (string.IsNullOrEmpty(userName) || !IsAdminUser(userName))
                {
                    context.Result = new ForbidResult();
                    return;
                }
            }
        }

        /// <summary>
        /// Simple admin check - replace with proper database lookup
        /// </summary>
        private static bool IsAdminUser(string userName)
        {
            // Temporary implementation - replace with actual admin user lookup
            // This is just a placeholder until proper admin authentication is implemented
            return userName.Contains("admin") || userName.Contains("administrator");
        }
    }

    /// <summary>
    /// Interface extension for admin user context
    /// </summary>
    public interface IAdminUserContextService : IUserContextService
    {
        AdminUser? GetCurrentAdminUser();
        Task<bool> IsCurrentUserAdminAsync();
    }
}