using System;

namespace ParaHockeyApp.Models.ViewModels
{
    /// <summary>
    /// View model for displaying member details in read-only format
    /// </summary>
    public class MemberDetailsViewModel
    {
        public int Id { get; set; }
        public string FirstName { get; set; } = string.Empty;
        public string LastName { get; set; } = string.Empty;
        public string FullName => $"{FirstName} {LastName}";
        public string Email { get; set; } = string.Empty;
        public string Phone { get; set; } = string.Empty;
        public DateTime DateOfBirth { get; set; }
        public int Age => DateTime.Now.Year - DateOfBirth.Year - (DateTime.Now.DayOfYear < DateOfBirth.DayOfYear ? 1 : 0);
        public string Address { get; set; } = string.Empty;
        public string City { get; set; } = string.Empty;
        public string Province { get; set; } = string.Empty;
        public string PostalCode { get; set; } = string.Empty;
        public string RegistrationTypeName { get; set; } = string.Empty;
        public DateTime DateCreated { get; set; }
        public string Status => IsActive ? "Active" : "Inactive";
        public bool IsActive { get; set; }
        public string? HQc_Id { get; set; }
        
        // Additional properties for display
        public string FormattedDateOfBirth => DateOfBirth.ToString("yyyy-MM-dd");
        public string FormattedRegistrationDate => DateCreated.ToString("yyyy-MM-dd");
        public string FormattedPhone => !string.IsNullOrEmpty(Phone) ? Phone : "Not provided";
        public string FormattedAddress => !string.IsNullOrEmpty(Address) ? $"{Address}, {City}, {Province} {PostalCode}" : "Not provided";
    }
}