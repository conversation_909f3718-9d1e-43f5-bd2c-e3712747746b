using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using ParaHockeyApp.Models.Entities;
using ParaHockeyApp.Models.Configuration;

namespace ParaHockeyApp.Models.ViewModels
{
    public class MemberSearchViewModel
    {
        [Required]
        public string LastName { get; set; } = string.Empty;

        [Required]
        [DataType(DataType.Date)]
        public DateTime DateOfBirth { get; set; }

        [Required]
        [EmailAddress]
        public string Email { get; set; } = string.Empty;

        public List<Member> SearchResults { get; set; } = new List<Member>();

        public EnvironmentSettings EnvironmentSettings { get; set; } = new EnvironmentSettings();
    }
}
