﻿// Auto-fill registration form
document.getElementsByName('FirstName')[0].value = '';
document.getElementsByName('LastName')[0].value = '';
document.getElementsByName('Email')[0].value = '';
document.getElementsByName('Phone')[0].value = '';
document.getElementsByName('DateOfBirth')[0].value = '';
document.getElementsByName('Address')[0].value = '';
document.getElementsByName('City')[0].value = '';
document.getElementsByName('PostalCode')[0].value = '';

// Select radio buttons
document.getElementById('reg-').checked = true;
document.getElementById('gender-').checked = true;
document.getElementById('phone-').checked = true;

// Select province dropdown
document.getElementsByName('ProvinceId')[0].value = '';
// Emergency contact
document.getElementsByName('EmergencyContact.FirstName')[0].value = '';
document.getElementsByName('EmergencyContact.LastName')[0].value = '';
document.getElementsByName('EmergencyContact.Phone')[0].value = '';
document.getElementsByName('EmergencyContact.RelationToUser')[0].value = 'Friend';
document.getElementsByName('EmergencyContact.Email')[0].value = '';
alert('Form filled! Review and click Register to submit.');
