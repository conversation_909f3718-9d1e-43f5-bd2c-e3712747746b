using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using ParaHockeyApp.DTOs;
using ParaHockeyApp.Models;
using ParaHockeyApp.Models.Entities;
using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Text;
using System.Text.Json;
using System.Threading.Tasks;

namespace ParaHockeyApp.Services
{
    /// <summary>
    /// Implementation of member search functionality with advanced filtering and search capabilities
    /// </summary>
    public class MemberSearchService : IMemberSearchService
    {
        private readonly ApplicationContext _context;
        private readonly ILogger<MemberSearchService> _logger;

        public MemberSearchService(ApplicationContext context, ILogger<MemberSearchService> logger)
        {
            _context = context;
            _logger = logger;
        }

        /// <inheritdoc/>
        public async Task<MemberSearchResult> SearchMembersAsync(MemberSearchRequest request)
        {
            try
            {
                // Validate request
                if (!request.IsValid())
                {
                    throw new ArgumentException("Invalid search request parameters");
                }

                // Build base query with includes
                var query = _context.Members
                    .Include(m => m.RegistrationType)
                    .Include(m => m.Province)
                    .AsQueryable();

                // Track matched fields for each result
                var matchedFieldsDict = new Dictionary<int, List<SearchMatchInfo>>();

                // Apply text search across multiple fields
                if (!string.IsNullOrWhiteSpace(request.SearchTerm))
                {
                    var normalizedSearchTerm = request.SearchTerm.Trim().ToLower();
                    var normalizedPhone = NormalizePhoneNumber(request.SearchTerm);

                    // Check if search term could be a date-related search
                    var isNumericSearch = int.TryParse(normalizedSearchTerm, out var numericValue);
                    var isDateSearch = DateTime.TryParse(normalizedSearchTerm, out var dateValue);

                    query = query.Where(m =>
                        m.FirstName.ToLower().Contains(normalizedSearchTerm) ||
                        m.LastName.ToLower().Contains(normalizedSearchTerm) ||
                        m.Email.ToLower().Contains(normalizedSearchTerm) ||
                        (!string.IsNullOrEmpty(normalizedPhone) && m.Phone.Replace(" ", "").Replace("-", "").Replace("(", "").Replace(")", "").Contains(normalizedPhone)) ||
                        m.Address.ToLower().Contains(normalizedSearchTerm) ||
                        m.City.ToLower().Contains(normalizedSearchTerm) ||
                        m.PostalCode.Replace(" ", "").ToLower().Contains(normalizedSearchTerm.Replace(" ", "")) ||
                        (isNumericSearch && (
                            m.DateOfBirth.Year == numericValue || // Year search (e.g., 1990)
                            m.DateOfBirth.Month == numericValue || // Month search (e.g., 12)
                            m.DateOfBirth.Day == numericValue // Day search (e.g., 25)
                        )) ||
                        (isDateSearch && m.DateOfBirth.Date == dateValue.Date) // Full date search
                    );
                }

                // Apply registration type filter
                if (request.RegistrationTypeIds?.Any() == true)
                {
                    query = query.Where(m => request.RegistrationTypeIds.Contains(m.RegistrationTypeId));
                }

                // Apply active/inactive filter
                if (request.IsActive.HasValue)
                {
                    query = query.Where(m => m.IsActive == request.IsActive.Value);
                }

                // Apply date of birth range filter
                if (request.DateOfBirthFrom.HasValue)
                {
                    query = query.Where(m => m.DateOfBirth >= request.DateOfBirthFrom.Value);
                }
                if (request.DateOfBirthTo.HasValue)
                {
                    query = query.Where(m => m.DateOfBirth <= request.DateOfBirthTo.Value);
                }

                // Apply age range filter
                if (request.AgeFrom.HasValue || request.AgeTo.HasValue)
                {
                    var today = DateTime.Today;
                    
                    if (request.AgeFrom.HasValue)
                    {
                        var maxBirthDate = today.AddYears(-request.AgeFrom.Value);
                        query = query.Where(m => m.DateOfBirth <= maxBirthDate);
                    }
                    
                    if (request.AgeTo.HasValue)
                    {
                        var minBirthDate = today.AddYears(-request.AgeTo.Value - 1);
                        query = query.Where(m => m.DateOfBirth > minBirthDate);
                    }
                }

                // Apply location filters
                if (!string.IsNullOrWhiteSpace(request.City))
                {
                    query = query.Where(m => m.City.ToLower().Contains(request.City.ToLower()));
                }

                if (!string.IsNullOrWhiteSpace(request.Province))
                {
                    query = query.Where(m => m.Province.Code.ToLower().Contains(request.Province.ToLower()) || 
                                             m.Province.DisplayNameKey.ToLower().Contains(request.Province.ToLower()));
                }

                if (!string.IsNullOrWhiteSpace(request.PostalCode))
                {
                    var normalizedPostal = request.PostalCode.Replace(" ", "").ToLower();
                    query = query.Where(m => m.PostalCode.Replace(" ", "").ToLower().Contains(normalizedPostal));
                }

                // Get total count before pagination
                var totalCount = await query.CountAsync();
                
                // Handle empty table gracefully
                if (totalCount == 0)
                {
                    return new MemberSearchResult
                    {
                        Members = new List<MemberSearchResultItem>(),
                        TotalCount = 0,
                        CurrentPage = 1,
                        PageSize = request.PageSize,
                        SearchCriteria = request
                    };
                }

                // Apply sorting
                query = ApplySorting(query, request.SortBy, request.SortDescending);

                // Apply pagination
                var members = await query
                    .Skip((request.Page - 1) * request.PageSize)
                    .Take(request.PageSize)
                    .ToListAsync();

                // Convert to result items with match information
                var resultItems = new List<MemberSearchResultItem>();
                foreach (var member in members)
                {
                    var item = MapToSearchResultItem(member);
                    
                    // Identify matched fields if search term was provided
                    if (!string.IsNullOrWhiteSpace(request.SearchTerm))
                    {
                        item.MatchedFields = IdentifyMatchedFields(member, request.SearchTerm);
                    }
                    
                    resultItems.Add(item);
                }

                return new MemberSearchResult
                {
                    Members = resultItems,
                    TotalCount = totalCount,
                    CurrentPage = request.Page,
                    PageSize = request.PageSize,
                    SearchCriteria = request
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error performing member search");
                throw;
            }
        }

        /// <inheritdoc/>
        public async Task<List<RegistrationType>> GetRegistrationTypesAsync()
        {
            return await _context.RegistrationTypes
                .OrderBy(rt => rt.DisplayNameKey)
                .ToListAsync();
        }

        /// <inheritdoc/>
        public async Task<byte[]> ExportMembersAsync(MemberSearchRequest request)
        {
            // Get all results (no pagination for export)
            var allMembersRequest = new MemberSearchRequest
            {
                SearchTerm = request.SearchTerm,
                RegistrationTypeIds = request.RegistrationTypeIds,
                IsActive = request.IsActive,
                DateOfBirthFrom = request.DateOfBirthFrom,
                DateOfBirthTo = request.DateOfBirthTo,
                AgeFrom = request.AgeFrom,
                AgeTo = request.AgeTo,
                City = request.City,
                Province = request.Province,
                PostalCode = request.PostalCode,
                Page = 1,
                PageSize = 10000, // Maximum export size
                SortBy = request.SortBy,
                SortDescending = request.SortDescending
            };

            var result = await SearchMembersAsync(allMembersRequest);

            // Build CSV content
            var csv = new StringBuilder();
            csv.AppendLine("ID,First Name,Last Name,Email,Phone,Date of Birth,Age,Address,City,Province,Postal Code,Registration Type,Status,Date Created");

            foreach (var member in result.Members)
            {
                csv.AppendLine($"{member.Id}," +
                    $"\"{member.FirstName}\"," +
                    $"\"{member.LastName}\"," +
                    $"\"{member.Email}\"," +
                    $"\"{member.Phone}\"," +
                    $"{member.DateOfBirth:yyyy-MM-dd}," +
                    $"{member.Age}," +
                    $"\"{member.Address}\"," +
                    $"\"{member.City}\"," +
                    $"\"{member.Province}\"," +
                    $"\"{member.PostalCode}\"," +
                    $"\"{member.RegistrationTypeName}\"," +
                    $"{member.StatusText}," +
                    $"{member.DateCreated:yyyy-MM-dd HH:mm}");
            }

            return Encoding.UTF8.GetBytes(csv.ToString());
        }

        /// <inheritdoc/>
        public async Task<SavedSearch> SaveSearchAsync(MemberSearchRequest request, string searchName, string userId)
        {
            var savedSearch = new SavedSearch
            {
                Name = searchName,
                UserId = userId,
                SearchCriteriaJson = JsonSerializer.Serialize(request),
                LastUsed = DateTime.UtcNow,
                UsageCount = 0
            };

            _context.Add(savedSearch);
            await _context.SaveChangesAsync();

            return savedSearch;
        }

        /// <inheritdoc/>
        public async Task<List<SavedSearch>> GetSavedSearchesAsync(string userId)
        {
            return await _context.Set<SavedSearch>()
                .Where(s => s.UserId == userId && s.IsActive)
                .OrderByDescending(s => s.LastUsed)
                .ToListAsync();
        }

        /// <inheritdoc/>
        public async Task<MemberSearchRequest?> LoadSavedSearchAsync(int searchId, string userId)
        {
            var savedSearch = await _context.Set<SavedSearch>()
                .FirstOrDefaultAsync(s => s.Id == searchId && s.UserId == userId && s.IsActive);

            if (savedSearch == null)
                return null;

            return JsonSerializer.Deserialize<MemberSearchRequest>(savedSearch.SearchCriteriaJson);
        }

        /// <inheritdoc/>
        public async Task<bool> DeleteSavedSearchAsync(int searchId, string userId)
        {
            var savedSearch = await _context.Set<SavedSearch>()
                .FirstOrDefaultAsync(s => s.Id == searchId && s.UserId == userId);

            if (savedSearch == null)
                return false;

            savedSearch.IsActive = false;
            await _context.SaveChangesAsync();
            return true;
        }

        /// <inheritdoc/>
        public async Task UpdateSavedSearchLastUsedAsync(int searchId, string userId)
        {
            var savedSearch = await _context.Set<SavedSearch>()
                .FirstOrDefaultAsync(s => s.Id == searchId && s.UserId == userId && s.IsActive);

            if (savedSearch != null)
            {
                savedSearch.LastUsed = DateTime.UtcNow;
                savedSearch.UsageCount++;
                await _context.SaveChangesAsync();
            }
        }

        /// <inheritdoc/>
        public string NormalizePhoneNumber(string phoneNumber)
        {
            if (string.IsNullOrWhiteSpace(phoneNumber))
                return string.Empty;

            // Remove all non-digit characters
            return new string(phoneNumber.Where(char.IsDigit).ToArray());
        }

        /// <inheritdoc/>
        public async Task<List<string>> GetDistinctCitiesAsync(string? searchTerm = null)
        {
            try
            {
                var query = _context.Members
                    .Where(m => m.IsActive && !string.IsNullOrEmpty(m.City))
                    .Select(m => m.City)
                    .Distinct();

                // Apply search term filtering if provided
                if (!string.IsNullOrWhiteSpace(searchTerm))
                {
                    var normalizedSearchTerm = searchTerm.Trim().ToLower();
                    query = query.Where(city => city.ToLower().StartsWith(normalizedSearchTerm));
                }

                var cities = await query
                    .OrderBy(city => city)
                    .ToListAsync();

                return cities;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving distinct cities with search term: {SearchTerm}", searchTerm);
                throw;
            }
        }

        /// <inheritdoc/>
        public async Task<List<string>> GetDistinctProvincesAsync()
        {
            try
            {
                var provinces = await _context.Members
                    .Where(m => m.IsActive)
                    .Include(m => m.Province)
                    .Select(m => m.Province.DisplayNameKey)
                    .Distinct()
                    .OrderBy(province => province)
                    .ToListAsync();

                return provinces;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving distinct provinces");
                throw;
            }
        }

        /// <inheritdoc/>
        public async Task<Dictionary<string, int>> GetRegistrationTypeCountsAsync()
        {
            try
            {
                var registrationTypeCounts = await _context.Members
                    .Where(m => m.IsActive)
                    .Include(m => m.RegistrationType)
                    .GroupBy(m => m.RegistrationType.DisplayNameKey)
                    .Select(g => new { RegistrationType = g.Key, Count = g.Count() })
                    .OrderBy(x => x.RegistrationType)
                    .ToDictionaryAsync(x => x.RegistrationType, x => x.Count);

                return registrationTypeCounts;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving registration type counts");
                throw;
            }
        }

        /// <summary>
        /// Applies sorting to the member query
        /// </summary>
        private IQueryable<Member> ApplySorting(IQueryable<Member> query, string? sortBy, bool descending)
        {
            if (string.IsNullOrWhiteSpace(sortBy))
            {
                // Default sort by last name, then first name
                return query.OrderBy(m => m.LastName).ThenBy(m => m.FirstName);
            }

            return sortBy.ToLower() switch
            {
                "firstname" => descending ? query.OrderByDescending(m => m.FirstName) : query.OrderBy(m => m.FirstName),
                "lastname" => descending ? query.OrderByDescending(m => m.LastName) : query.OrderBy(m => m.LastName),
                "email" => descending ? query.OrderByDescending(m => m.Email) : query.OrderBy(m => m.Email),
                "dateofbirth" => descending ? query.OrderByDescending(m => m.DateOfBirth) : query.OrderBy(m => m.DateOfBirth),
                "city" => descending ? query.OrderByDescending(m => m.City) : query.OrderBy(m => m.City),
                "datecreated" => descending ? query.OrderByDescending(m => m.DateCreated) : query.OrderBy(m => m.DateCreated),
                "registrationtype" => descending ? query.OrderByDescending(m => m.RegistrationType.DisplayNameKey) : query.OrderBy(m => m.RegistrationType.DisplayNameKey),
                _ => query.OrderBy(m => m.LastName).ThenBy(m => m.FirstName)
            };
        }

        /// <summary>
        /// Maps a Member entity to a MemberSearchResultItem DTO
        /// </summary>
        private MemberSearchResultItem MapToSearchResultItem(Member member)
        {
            return new MemberSearchResultItem
            {
                Id = member.Id,
                FirstName = member.FirstName,
                LastName = member.LastName,
                Email = member.Email,
                Phone = member.Phone,
                DateOfBirth = member.DateOfBirth,
                Address = member.Address,
                City = member.City,
                Province = member.Province?.Code ?? string.Empty,
                PostalCode = member.PostalCode,
                RegistrationTypeId = member.RegistrationTypeId,
                RegistrationTypeName = member.RegistrationType?.DisplayNameKey ?? string.Empty,
                IsActive = member.IsActive,
                DateCreated = member.DateCreated,
                DateModified = member.DateModified
            };
        }

        /// <summary>
        /// Identifies which fields matched the search term
        /// </summary>
        private List<SearchMatchInfo> IdentifyMatchedFields(Member member, string searchTerm)
        {
            var matches = new List<SearchMatchInfo>();
            var normalizedSearchTerm = searchTerm.Trim().ToLower();
            var normalizedPhone = NormalizePhoneNumber(searchTerm);

            // Check first name
            if (member.FirstName.ToLower().Contains(normalizedSearchTerm))
            {
                matches.Add(CreateMatchInfo("FirstName", "First Name", member.FirstName, searchTerm, SearchMatchType.CaseInsensitive));
            }

            // Check last name
            if (member.LastName.ToLower().Contains(normalizedSearchTerm))
            {
                matches.Add(CreateMatchInfo("LastName", "Last Name", member.LastName, searchTerm, SearchMatchType.CaseInsensitive));
            }

            // Check email
            if (member.Email.ToLower().Contains(normalizedSearchTerm))
            {
                matches.Add(CreateMatchInfo("Email", "Email", member.Email, searchTerm, SearchMatchType.CaseInsensitive));
            }

            // Check phone (only if search term contains digits)
            var memberPhoneNormalized = NormalizePhoneNumber(member.Phone);
            if (!string.IsNullOrEmpty(normalizedPhone) && memberPhoneNormalized.Contains(normalizedPhone))
            {
                matches.Add(CreateMatchInfo("Phone", "Phone", member.Phone, searchTerm, SearchMatchType.NormalizedPhone));
            }

            // Check address
            if (member.Address.ToLower().Contains(normalizedSearchTerm))
            {
                matches.Add(CreateMatchInfo("Address", "Address", member.Address, searchTerm, SearchMatchType.CaseInsensitive));
            }

            // Check city
            if (member.City.ToLower().Contains(normalizedSearchTerm))
            {
                matches.Add(CreateMatchInfo("City", "City", member.City, searchTerm, SearchMatchType.CaseInsensitive));
            }

            // Check postal code
            var normalizedPostal = member.PostalCode.Replace(" ", "").ToLower();
            if (normalizedPostal.Contains(normalizedSearchTerm.Replace(" ", "")))
            {
                matches.Add(CreateMatchInfo("PostalCode", "Postal Code", member.PostalCode, searchTerm, SearchMatchType.CaseInsensitive));
            }

            // Check date of birth (in various formats)
            var isNumericSearch = int.TryParse(normalizedSearchTerm, out var numericValue);
            var isDateSearch = DateTime.TryParse(normalizedSearchTerm, out var dateValue);
            
            if ((isNumericSearch && (
                    member.DateOfBirth.Year == numericValue || 
                    member.DateOfBirth.Month == numericValue || 
                    member.DateOfBirth.Day == numericValue
                )) ||
                (isDateSearch && member.DateOfBirth.Date == dateValue.Date))
            {
                var dobFormatted = member.DateOfBirth.ToString("yyyy-MM-dd");
                matches.Add(CreateMatchInfo("DateOfBirth", "Date of Birth", dobFormatted, searchTerm, SearchMatchType.Partial));
            }

            return matches;
        }

        /// <summary>
        /// Creates a SearchMatchInfo object
        /// </summary>
        private SearchMatchInfo CreateMatchInfo(string fieldName, string displayName, string value, string searchTerm, SearchMatchType matchType)
        {
            var normalizedValue = value.ToLower();
            var normalizedSearch = searchTerm.ToLower();
            var position = normalizedValue.IndexOf(normalizedSearch);

            return new SearchMatchInfo
            {
                FieldName = fieldName,
                FieldDisplayName = displayName,
                MatchedValue = value,
                SearchTerm = searchTerm,
                MatchType = matchType,
                MatchStartPosition = position >= 0 ? position : 0,
                MatchLength = searchTerm.Length
            };
        }
    }
}