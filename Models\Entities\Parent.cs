using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace ParaHockeyApp.Models.Entities
{
    /// <summary>
    /// Parent/guardian information for junior members.
    /// </summary>
    public class Parent : BaseEntity
    {
        // Basic Information
        [Required]
        [StringLength(50)]
        public string FirstName { get; set; } = string.Empty;

        [Required]
        [StringLength(50)]
        public string LastName { get; set; } = string.Empty;

        // Relationship (e.g., <PERSON>, Father, Guardian)
        [Required]
        [StringLength(50)]
        public string ParentType { get; set; } = string.Empty;

        // Contact Information
        [Required]
        [StringLength(20)]
        public string Phone { get; set; } = string.Empty;

        [StringLength(254)]
        [EmailAddress]
        public string? Email { get; set; }

        // Foreign Key to Member
        [Required]
        public int MemberId { get; set; }
        public virtual Member Member { get; set; } = null!;

        // Helper property not mapped to DB
        [NotMapped]
        public string FullName => $"{FirstName} {LastName}";

        public override bool EstValide =>
            !string.IsNullOrWhiteSpace(FirstName) &&
            !string.IsNullOrWhiteSpace(LastName) &&
            !string.IsNullOrWhiteSpace(ParentType) &&
            !string.IsNullOrWhiteSpace(Phone) &&
            IsActive;
    }
}