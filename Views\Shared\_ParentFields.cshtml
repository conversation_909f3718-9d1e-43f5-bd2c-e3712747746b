@model ParaHockeyApp.Models.ViewModels.MemberRegistrationViewModel
@using Microsoft.AspNetCore.Mvc.Localization
@inject IHtmlLocalizer<ParaHockeyApp.Resources.SharedResourceMarker> SharedLocalizer
@{
    // Ensure Parents list has at least 2 items for the form
    while (Model.Parents.Count < 2)
    {
        Model.Parents.Add(new ParentViewModel());
    }
}

<div id="parent-fields-section" class="parent-fields-container" style="display: none;">
    <div class="section-header mb-4 mt-4">
        <h4 class="text-primary border-bottom pb-2">
            <i class="fas fa-users"></i> @SharedLocalizer["ParentInformation"]
        </h4>
    </div>

    <!-- First Parent/Guardian -->
    <div class="card mb-4">
        <div class="card-header bg-light">
            <h6 class="card-title mb-0">@SharedLocalizer["ParentGuardianDetails"]</h6>
        </div>
        <div class="card-body">
            <div class="row mb-3">
                <div class="col-md-6">
                    <label for="Parents_0__FirstName" class="form-label required">@SharedLocalizer["FirstName"]</label>
                    <input asp-for="Parents[0].FirstName" type="text" class="form-control parent-field"
                        placeholder="@SharedLocalizer["ThirdPersonFirstNamePlaceholder"]" autocomplete="given-name" />
                    <span class="text-danger small parent-validation" data-valmsg-for="Parents[0].FirstName"
                        data-valmsg-replace="true"></span>
                </div>
                <div class="col-md-6">
                    <label for="Parents_0__LastName" class="form-label required">@SharedLocalizer["LastName"]</label>
                    <input asp-for="Parents[0].LastName" type="text" class="form-control parent-field"
                        placeholder="@SharedLocalizer["ThirdPersonLastNamePlaceholder"]" autocomplete="family-name" />
                    <span class="text-danger small parent-validation" data-valmsg-for="Parents[0].LastName"
                        data-valmsg-replace="true"></span>
                </div>
            </div>

            <div class="row mb-3">
                <div class="col-md-6">
                    <label for="Parents_0__ParentType"
                        class="form-label required">@SharedLocalizer["ParentType"]</label>
                    <select asp-for="Parents[0].ParentType" class="form-select parent-field">
                        <option value="">@SharedLocalizer["SelectParentType"]</option>
                        <option value="Mother">@SharedLocalizer["Mother"]</option>
                        <option value="Father">@SharedLocalizer["Father"]</option>
                        <option value="Guardian">@SharedLocalizer["Guardian"]</option>
                        <option value="Other">@SharedLocalizer["Other"]</option>
                    </select>
                    <span class="text-danger small parent-validation" data-valmsg-for="Parents[0].ParentType"
                        data-valmsg-replace="true"></span>
                </div>
                <div class="col-md-6">
                    <label for="Parents_0__Phone" class="form-label required">@SharedLocalizer["PhoneNumber"]</label>
                    <input asp-for="Parents[0].Phone" type="tel" class="form-control parent-field parent-phone"
                        maxlength="14" placeholder="(*************" autocomplete="tel" />
                    <span class="text-danger small parent-validation" data-valmsg-for="Parents[0].Phone"
                        data-valmsg-replace="true"></span>
                </div>
            </div>

            <div class="row mb-3">
                <div class="col-md-6">
                    <label for="Parents_0__Email" class="form-label required">@SharedLocalizer["Email"]</label>
                    <input asp-for="Parents[0].Email" type="email" class="form-control parent-field parent-email"
                        placeholder="@SharedLocalizer["ThirdPersonEmailPlaceholder"]" autocomplete="email" />
                    <span class="text-danger small parent-validation" data-valmsg-for="Parents[0].Email"
                        data-valmsg-replace="true"></span>
                </div>
            </div>
        </div>
    </div>

    <!-- Second Parent/Guardian (only shown for Junior registration) -->
    <div id="second-parent-card" class="card mb-4" style="display: none;">
        <div class="card-header bg-light">
            <h6 class="card-title mb-0">@SharedLocalizer["SecondParentGuardianDetails"]</h6>
        </div>
        <div class="card-body">
            <div class="row mb-3">
                <div class="col-md-6">
                    <label for="Parents_1__FirstName" class="form-label required">@SharedLocalizer["FirstName"]</label>
                    <input asp-for="Parents[1].FirstName" type="text"
                        class="form-control parent-field second-parent-field"
                        placeholder="@SharedLocalizer["ThirdPersonFirstNamePlaceholder"]" autocomplete="given-name" />
                    <span class="text-danger small parent-validation" data-valmsg-for="Parents[1].FirstName"
                        data-valmsg-replace="true"></span>
                </div>
                <div class="col-md-6">
                    <label for="Parents_1__LastName" class="form-label required">@SharedLocalizer["LastName"]</label>
                    <input asp-for="Parents[1].LastName" type="text"
                        class="form-control parent-field second-parent-field"
                        placeholder="@SharedLocalizer["ThirdPersonLastNamePlaceholder"]" autocomplete="family-name" />
                    <span class="text-danger small parent-validation" data-valmsg-for="Parents[1].LastName"
                        data-valmsg-replace="true"></span>
                </div>
            </div>

            <div class="row mb-3">
                <div class="col-md-6">
                    <label for="Parents_1__ParentType"
                        class="form-label required">@SharedLocalizer["ParentType"]</label>
                    <select asp-for="Parents[1].ParentType" class="form-select parent-field second-parent-field">
                        <option value="">@SharedLocalizer["SelectParentType"]</option>
                        <option value="Mother">@SharedLocalizer["Mother"]</option>
                        <option value="Father">@SharedLocalizer["Father"]</option>
                        <option value="Guardian">@SharedLocalizer["Guardian"]</option>
                        <option value="Other">@SharedLocalizer["Other"]</option>
                    </select>
                    <span class="text-danger small parent-validation" data-valmsg-for="Parents[1].ParentType"
                        data-valmsg-replace="true"></span>
                </div>
                <div class="col-md-6">
                    <label for="Parents_1__Phone" class="form-label required">@SharedLocalizer["PhoneNumber"]</label>
                    <input asp-for="Parents[1].Phone" type="tel"
                        class="form-control parent-field parent-phone second-parent-field" maxlength="14"
                        placeholder="(*************" autocomplete="tel" />
                    <span class="text-danger small parent-validation" data-valmsg-for="Parents[1].Phone"
                        data-valmsg-replace="true"></span>
                </div>
            </div>

            <div class="row mb-3">
                <div class="col-md-6">
                    <label for="Parents_1__Email" class="form-label required">@SharedLocalizer["Email"]</label>
                    <input asp-for="Parents[1].Email" type="email"
                        class="form-control parent-field parent-email second-parent-field"
                        placeholder="@SharedLocalizer["ThirdPersonEmailPlaceholder"]" autocomplete="email" />
                    <span class="text-danger small parent-validation" data-valmsg-for="Parents[1].Email"
                        data-valmsg-replace="true"></span>
                </div>
            </div>
        </div>
    </div>
</div>
