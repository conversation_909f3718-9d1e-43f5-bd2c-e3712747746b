using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Localization;
using ParaHockeyApp.Models;
using ParaHockeyApp.Resources;
using System.Text.RegularExpressions;

namespace ParaHockeyApp.Controllers.Api
{
    [Route("api/members")]
    public class MembersApiController : BaseApiController
    {
        private readonly ApplicationContext _context;

        public MembersApiController(
            ApplicationContext context,
            ILogger<MembersApiController> logger,
            IStringLocalizer<SharedResourceMarker> localizer) 
            : base(logger, localizer)
        {
            _context = context;
        }

        [HttpGet("next-test")]
        public async Task<IActionResult> GetNextTestName([FromQuery] string type = "Coach")
        {
            try
            {
                // Validate input
                if (string.IsNullOrWhiteSpace(type))
                {
                    return ValidationError("ApiValidationTypeRequired");
                }

                // Normalize type for prefix
                string prefix = type switch
                {
                    "Coach" => "TestCoach_",
                    "Junior" => "TestJunior_",
                    "Volunteer" => "TestVolunteer_",
                    "Development" => "TestDevelopment_",
                    "Elite" => "TestElite_",
                    _ => "TestCoach_"
                };

                // Query all first names starting with the prefix (case-insensitive, SQL LIKE)
                var testNames = await _context.Members
                    .AsNoTracking()
                    .Where(m => EF.Functions.Like(m.FirstName, prefix + "%"))
                    .Select(m => m.FirstName)
                    .ToListAsync();

                int maxNum = 0;
                var regex = new Regex($@"^{Regex.Escape(prefix)}(\\d{{2}})$", RegexOptions.IgnoreCase);
                
                foreach (var name in testNames)
                {
                    var match = regex.Match(name);
                    if (match.Success && int.TryParse(match.Groups[1].Value, out int num))
                    {
                        if (num > maxNum) maxNum = num;
                    }
                }

                int nextNum = maxNum + 1;
                string nextName = $"{prefix}{nextNum:00}";

                // Double-check: If somehow this name exists, increment until unique
                while (testNames.Any(n => string.Equals(n, nextName, StringComparison.OrdinalIgnoreCase)))
                {
                    nextNum++;
                    nextName = $"{prefix}{nextNum:00}";
                }

                return SuccessResponse(new { firstName = nextName });
            }
            catch (Exception ex)
            {
                return HandleApiError(ex, nameof(GetNextTestName));
            }
        }
    }
}