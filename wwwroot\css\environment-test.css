/* TEST Environment Styling - Red Theme with Warnings */
:root {
    --primary-color: #dc3545; /* Red */
    --secondary-color: #6c757d;
    --background-color: #fff5f5; /* Light red background */
    --border-color: #dc3545;
    --danger-color: #dc3545;
}

.environment-banner {
    background: linear-gradient(90deg, #dc3545, #c82333);
    color: white;
    text-align: center;
    padding: 15px;
    font-weight: bold;
    position: sticky;
    top: 0;
    z-index: 1000;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    border-bottom: 3px solid #a71e2a;
}

/* Banner text is now dynamic from the layout file */
.environment-banner {
    font-size: 16px;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.7; }
    100% { opacity: 1; }
}

.navbar-brand::after {
    content: " [TEST]";
    color: #dc3545;
    font-weight: bold;
    background-color: rgba(220, 53, 69, 0.1);
    padding: 2px 6px;
    border-radius: 4px;
    margin-left: 8px;
}

/* Override Bootstrap primary color for test environment */
.btn-primary {
    background-color: #dc3545;
    border-color: #dc3545;
}

.btn-primary:hover {
    background-color: #c82333;
    border-color: #a71e2a;
}

.bg-primary {
    background-color: #dc3545 !important;
}

/* Test environment alert styling */
.alert-test {
    background-color: #f8d7da;
    border-color: #f5c2c7;
    color: #842029;
} 