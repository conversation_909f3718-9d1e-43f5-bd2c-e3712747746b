using ParaHockeyApp.DTOs;

namespace ParaHockeyApp.Services
{
    /// <summary>
    /// Service for detecting duplicate member registrations
    /// </summary>
    public interface IDuplicateMemberService
    {
        /// <summary>
        /// Checks for duplicate members based on email and lastName/dateOfBirth combination
        /// </summary>
        /// <param name="email">Email address to check</param>
        /// <param name="lastName">Last name to check for partial matches</param>
        /// <param name="dateOfBirth">Date of birth to check for partial matches</param>
        /// <returns>DuplicateCheckResult indicating if a duplicate was found</returns>
        Task<DuplicateCheckResult> CheckForDuplicatesAsync(string email, string lastName, DateTime? dateOfBirth);
        
        /// <summary>
        /// Masks an email address for privacy (e.g., "<EMAIL>" becomes "jo***@example.com")
        /// </summary>
        /// <param name="email">Email address to mask</param>
        /// <returns>Masked email address</returns>
        string MaskEmail(string email);
    }
}