using System;
using ParaHockeyApp.Models.Entities;

namespace ParaHockeyApp.Models.ViewModels
{
    /// <summary>
    /// View model for displaying event registration information in member dashboard
    /// </summary>
    public class EventRegistrationViewModel
    {
        /// <summary>
        /// Registration ID
        /// </summary>
        public int RegistrationId { get; set; }

        /// <summary>
        /// Event ID
        /// </summary>
        public int EventId { get; set; }

        /// <summary>
        /// Event title
        /// </summary>
        public string EventTitle { get; set; } = string.Empty;

        /// <summary>
        /// Event description
        /// </summary>
        public string? EventDescription { get; set; }

        /// <summary>
        /// Event start date and time
        /// </summary>
        public DateTime EventStartDate { get; set; }

        /// <summary>
        /// Event end date and time
        /// </summary>
        public DateTime? EventEndDate { get; set; }

        /// <summary>
        /// Event location
        /// </summary>
        public string? EventLocation { get; set; }

        /// <summary>
        /// Whether the event is all day
        /// </summary>
        public bool IsAllDay { get; set; }

        /// <summary>
        /// Event category display name (localized)
        /// </summary>
        public string CategoryName { get; set; } = string.Empty;

        /// <summary>
        /// Event category color (hex)
        /// </summary>
        public string CategoryColor { get; set; } = "#6f42c1";

        /// <summary>
        /// Registration status
        /// </summary>
        public RegistrationStatus Status { get; set; }

        /// <summary>
        /// Registration status display text
        /// </summary>
        public string StatusDisplay { get; set; } = string.Empty;

        /// <summary>
        /// Date when registration was submitted
        /// </summary>
        public DateTime RegistrationDate { get; set; }

        /// <summary>
        /// Number of guests registered
        /// </summary>
        public int GuestCount { get; set; }

        /// <summary>
        /// Member notes from registration
        /// </summary>
        public string? Notes { get; set; }

        /// <summary>
        /// Whether this registration can be cancelled
        /// </summary>
        public bool CanBeCancelled { get; set; }

        /// <summary>
        /// Event date range display (formatted)
        /// </summary>
        public string DateRangeDisplay { get; set; } = string.Empty;

        /// <summary>
        /// Whether member attended (for past events)
        /// </summary>
        public bool? Attended { get; set; }

        /// <summary>
        /// Bootstrap badge class for status display
        /// </summary>
        public string StatusBadgeClass => Status switch
        {
            RegistrationStatus.Pending => "badge-warning",
            RegistrationStatus.Confirmed => "badge-success",
            RegistrationStatus.Cancelled => "badge-secondary",
            RegistrationStatus.Waitlisted => "badge-info",
            RegistrationStatus.Rejected => "badge-danger",
            _ => "badge-secondary"
        };

        /// <summary>
        /// Whether the event is in the past
        /// </summary>
        public bool IsPastEvent => EventStartDate < DateTime.Now;

        /// <summary>
        /// Whether the event is upcoming
        /// </summary>
        public bool IsUpcomingEvent => EventStartDate >= DateTime.Now;

        /// <summary>
        /// Formatted registration date
        /// </summary>
        public string FormattedRegistrationDate => RegistrationDate.ToString("yyyy-MM-dd");

        /// <summary>
        /// Event location display with fallback
        /// </summary>
        public string EventLocationDisplay => !string.IsNullOrEmpty(EventLocation) ? EventLocation : "Location TBD";
    }
}