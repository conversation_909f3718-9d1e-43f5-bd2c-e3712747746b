using Microsoft.EntityFrameworkCore;
using System.Text.Json;

namespace ParaHockeyApp.Services
{
    /// <summary>
    /// Service for handling and translating errors into user-friendly messages
    /// </summary>
    public class ErrorHandlingService : IErrorHandlingService
    {
        private readonly ILogger<ErrorHandlingService> _logger;
        private readonly IInputSanitizationService _sanitizationService;

        public ErrorHandlingService(
            ILogger<ErrorHandlingService> logger,
            IInputSanitizationService sanitizationService)
        {
            _logger = logger;
            _sanitizationService = sanitizationService;
        }

        /// <summary>
        /// Handles an exception and returns a user-friendly error response
        /// </summary>
        public ErrorResponse HandleException(Exception exception, string context = "")
        {
            var correlationId = Guid.NewGuid().ToString("N")[..8];
            
            LogError(exception, context, new Dictionary<string, object>
            {
                ["CorrelationId"] = correlationId
            });

            var response = new ErrorResponse
            {
                CorrelationId = correlationId,
                OccurredAt = DateTime.UtcNow
            };

            switch (exception)
            {
                case FileNotFoundException fileEx:
                    response.Title = "File Not Found";
                    response.Message = "The requested file could not be found.";
                    response.Type = ErrorType.FileProcessing;
                    response.Severity = ErrorSeverity.High;
                    response.Details.Add("Please ensure the file exists and try again.");
                    response.RecoveryActions = GetRecoveryActions(ErrorType.FileProcessing);
                    break;

                case UnauthorizedAccessException authEx:
                    response.Title = "Access Denied";
                    response.Message = "You don't have permission to perform this action.";
                    response.Type = ErrorType.Authorization;
                    response.Severity = ErrorSeverity.High;
                    response.Details.Add("Please contact an administrator if you believe this is incorrect.");
                    response.RecoveryActions = GetRecoveryActions(ErrorType.Authorization);
                    break;

                case DbUpdateException dbEx:
                    response.Title = "Database Error";
                    response.Message = "An error occurred while saving data.";
                    response.Type = ErrorType.DatabaseError;
                    response.Severity = ErrorSeverity.High;
                    response.IsRetryable = true;
                    response.RetryAfterSeconds = 5;
                    
                    if (dbEx.InnerException?.Message.Contains("UNIQUE constraint") == true)
                    {
                        response.Message = "This record already exists in the system.";
                        response.Details.Add("A record with the same information has already been imported.");
                    }
                    else if (dbEx.InnerException?.Message.Contains("timeout") == true)
                    {
                        response.Message = "The operation took too long to complete.";
                        response.Details.Add("The server may be busy. Please try again in a few moments.");
                    }
                    
                    response.RecoveryActions = GetRecoveryActions(ErrorType.DatabaseError);
                    break;

                case OperationCanceledException cancelEx:
                    response.Title = "Operation Cancelled";
                    response.Message = "The import operation was cancelled.";
                    response.Type = ErrorType.System;
                    response.Severity = ErrorSeverity.Medium;
                    response.Details.Add("You can start a new import if needed.");
                    response.RecoveryActions = new List<RecoveryAction>
                    {
                        new RecoveryAction
                        {
                            Title = "Start New Import",
                            Description = "Begin a new import operation",
                            ActionType = "retry",
                            IsPrimary = true
                        }
                    };
                    break;

                case ArgumentException argEx:
                    response.Title = "Invalid Input";
                    response.Message = "The provided data is not valid.";
                    response.Type = ErrorType.DataValidation;
                    response.Severity = ErrorSeverity.High;
                    response.Details.Add(argEx.Message);
                    response.RecoveryActions = GetRecoveryActions(ErrorType.DataValidation);
                    break;

                case InvalidOperationException invalidEx:
                    response.Title = "Invalid Operation";
                    response.Message = "This operation cannot be performed at this time.";
                    response.Type = ErrorType.Business;
                    response.Severity = ErrorSeverity.High;
                    response.Details.Add(invalidEx.Message);
                    response.RecoveryActions = GetRecoveryActions(ErrorType.Business);
                    break;

                case OutOfMemoryException memEx:
                    response.Title = "File Too Large";
                    response.Message = "The file is too large to process with standard import.";
                    response.Type = ErrorType.Performance;
                    response.Severity = ErrorSeverity.High;
                    response.Details.Add("Consider using streaming import for large files.");
                    response.RecoveryActions = new List<RecoveryAction>
                    {
                        new RecoveryAction
                        {
                            Title = "Use Streaming Import",
                            Description = "Process large files using streaming mode to avoid memory issues",
                            ActionType = "fix",
                            ActionUrl = "/Import/StreamingUpload",
                            IsPrimary = true
                        },
                        new RecoveryAction
                        {
                            Title = "Split File",
                            Description = "Divide the file into smaller chunks and import separately",
                            ActionType = "fix"
                        }
                    };
                    break;

                case TimeoutException timeoutEx:
                    response.Title = "Operation Timed Out";
                    response.Message = "The operation took too long to complete.";
                    response.Type = ErrorType.Performance;
                    response.Severity = ErrorSeverity.Medium;
                    response.IsRetryable = true;
                    response.RetryAfterSeconds = 30;
                    response.Details.Add("This may be due to server load or a large file size.");
                    response.RecoveryActions = GetRecoveryActions(ErrorType.Performance);
                    break;

                default:
                    response.Title = "An Error Occurred";
                    response.Message = "An unexpected error occurred while processing your request.";
                    response.Type = ErrorType.System;
                    response.Severity = ErrorSeverity.High;
                    response.Details.Add("Please try again or contact support if the problem persists.");
                    response.RecoveryActions = GetRecoveryActions(ErrorType.System);
                    break;
            }

            // Add technical details for debugging (sanitized)
            if (!string.IsNullOrEmpty(exception.Message))
            {
                response.TechnicalDetails = _sanitizationService.SanitizeText(exception.Message);
            }

            return response;
        }

        /// <summary>
        /// Handles multiple exceptions and consolidates them into a single response
        /// </summary>
        public ErrorResponse HandleMultipleExceptions(List<Exception> exceptions, string context = "")
        {
            if (!exceptions.Any())
            {
                return new ErrorResponse
                {
                    Title = "No Errors",
                    Message = "Operation completed successfully.",
                    Type = ErrorType.System,
                    Severity = ErrorSeverity.Low
                };
            }

            if (exceptions.Count == 1)
            {
                return HandleException(exceptions[0], context);
            }

            var response = new ErrorResponse
            {
                Title = "Multiple Errors Occurred",
                Message = $"{exceptions.Count} errors were encountered during processing.",
                Type = ErrorType.System,
                Severity = ErrorSeverity.High,
                CorrelationId = Guid.NewGuid().ToString("N")[..8]
            };

            // Group exceptions by type
            var groupedExceptions = exceptions.GroupBy(ex => ex.GetType()).ToList();
            
            foreach (var group in groupedExceptions)
            {
                var exampleException = group.First();
                var count = group.Count();
                var singleResponse = HandleException(exampleException, context);
                
                if (count == 1)
                {
                    response.Details.Add(singleResponse.Message);
                }
                else
                {
                    response.Details.Add($"{singleResponse.Message} (occurred {count} times)");
                }
            }

            // Determine overall severity
            var maxSeverity = exceptions.Select(ex => HandleException(ex, context).Severity).Max();
            response.Severity = maxSeverity;

            // Add generic recovery actions
            response.RecoveryActions = GetRecoveryActions(ErrorType.System);

            return response;
        }

        /// <summary>
        /// Validates import data and returns validation errors
        /// </summary>
        public ValidationErrorResponse? ValidateImportData(Dictionary<string, object?> data)
        {
            var response = new ValidationErrorResponse();

            // Validate required fields
            var requiredFields = new[] { "FirstName", "LastName" };
            foreach (var field in requiredFields)
            {
                if (!data.ContainsKey(field) || string.IsNullOrWhiteSpace(data[field]?.ToString()))
                {
                    if (!response.FieldErrors.ContainsKey(field))
                        response.FieldErrors[field] = new List<string>();
                    
                    response.FieldErrors[field].Add($"{field} is required.");
                }
            }

            // Validate email format
            if (data.ContainsKey("Email") && data["Email"] != null)
            {
                var email = data["Email"].ToString();
                var sanitizedEmail = _sanitizationService.SanitizeEmail(email);
                if (sanitizedEmail == null && !string.IsNullOrWhiteSpace(email))
                {
                    if (!response.FieldErrors.ContainsKey("Email"))
                        response.FieldErrors["Email"] = new List<string>();
                    
                    response.FieldErrors["Email"].Add("Invalid email format.");
                }
            }

            // Validate date of birth
            if (data.ContainsKey("DateOfBirth") && data["DateOfBirth"] != null)
            {
                var dobString = data["DateOfBirth"].ToString();
                if (!DateTime.TryParse(dobString, out var dob))
                {
                    if (!response.FieldErrors.ContainsKey("DateOfBirth"))
                        response.FieldErrors["DateOfBirth"] = new List<string>();
                    
                    response.FieldErrors["DateOfBirth"].Add("Invalid date format.");
                }
                else if (dob > DateTime.Now)
                {
                    if (!response.FieldErrors.ContainsKey("DateOfBirth"))
                        response.FieldErrors["DateOfBirth"] = new List<string>();
                    
                    response.FieldErrors["DateOfBirth"].Add("Date of birth cannot be in the future.");
                }
                else if (dob < DateTime.Now.AddYears(-150))
                {
                    if (!response.FieldErrors.ContainsKey("DateOfBirth"))
                        response.FieldErrors["DateOfBirth"] = new List<string>();
                    
                    response.FieldErrors["DateOfBirth"].Add("Date of birth is not realistic.");
                }
            }

            // Set message based on error count
            if (response.HasErrors)
            {
                response.Message = response.ErrorCount == 1 
                    ? "1 validation error was found." 
                    : $"{response.ErrorCount} validation errors were found.";
                
                return response;
            }

            return null; // No validation errors
        }

        /// <summary>
        /// Gets recovery suggestions for common error scenarios
        /// </summary>
        public List<RecoveryAction> GetRecoveryActions(ErrorType errorType)
        {
            return errorType switch
            {
                ErrorType.FileProcessing => new List<RecoveryAction>
                {
                    new RecoveryAction
                    {
                        Title = "Check File Format",
                        Description = "Ensure your file is in Excel (.xlsx) format",
                        ActionType = "fix",
                        IsPrimary = true
                    },
                    new RecoveryAction
                    {
                        Title = "Try Again",
                        Description = "Upload the file again",
                        ActionType = "retry"
                    }
                },

                ErrorType.DataValidation => new List<RecoveryAction>
                {
                    new RecoveryAction
                    {
                        Title = "Fix Data Issues",
                        Description = "Correct the highlighted data problems and try again",
                        ActionType = "fix",
                        IsPrimary = true
                    },
                    new RecoveryAction
                    {
                        Title = "Download Template",
                        Description = "Use the provided template to ensure correct format",
                        ActionType = "fix",
                        ActionUrl = "/Import/DownloadTemplate"
                    }
                },

                ErrorType.DatabaseError => new List<RecoveryAction>
                {
                    new RecoveryAction
                    {
                        Title = "Try Again",
                        Description = "Retry the operation",
                        ActionType = "retry",
                        IsPrimary = true
                    },
                    new RecoveryAction
                    {
                        Title = "Check for Duplicates",
                        Description = "Verify that this data hasn't already been imported",
                        ActionType = "fix"
                    }
                },

                ErrorType.Authorization => new List<RecoveryAction>
                {
                    new RecoveryAction
                    {
                        Title = "Contact Administrator",
                        Description = "Request access to import functionality",
                        ActionType = "contact",
                        IsPrimary = true
                    },
                    new RecoveryAction
                    {
                        Title = "Login Again",
                        Description = "Your session may have expired",
                        ActionType = "retry",
                        ActionUrl = "/Account/Login"
                    }
                },

                ErrorType.Performance => new List<RecoveryAction>
                {
                    new RecoveryAction
                    {
                        Title = "Use Streaming Import",
                        Description = "Try the streaming import for large files",
                        ActionType = "fix",
                        ActionUrl = "/Import/StreamingUpload",
                        IsPrimary = true
                    },
                    new RecoveryAction
                    {
                        Title = "Reduce File Size",
                        Description = "Split your file into smaller chunks",
                        ActionType = "fix"
                    }
                },

                _ => new List<RecoveryAction>
                {
                    new RecoveryAction
                    {
                        Title = "Try Again",
                        Description = "Retry the operation",
                        ActionType = "retry",
                        IsPrimary = true
                    },
                    new RecoveryAction
                    {
                        Title = "Contact Support",
                        Description = "Get help with this issue",
                        ActionType = "contact"
                    }
                }
            };
        }

        /// <summary>
        /// Logs error with appropriate level based on severity
        /// </summary>
        public void LogError(Exception exception, string context, Dictionary<string, object>? additionalData = null)
        {
            var logData = new Dictionary<string, object>
            {
                ["Context"] = context,
                ["ExceptionType"] = exception.GetType().Name,
                ["Message"] = exception.Message
            };

            if (additionalData != null)
            {
                foreach (var kvp in additionalData)
                {
                    logData[kvp.Key] = kvp.Value;
                }
            }

            // Determine log level based on exception type
            switch (exception)
            {
                case ArgumentException:
                case InvalidOperationException:
                    _logger.LogWarning(exception, "Validation/Business error in {Context}: {Message} {@LogData}", 
                        context, exception.Message, logData);
                    break;

                case UnauthorizedAccessException:
                    _logger.LogWarning(exception, "Authorization error in {Context}: {Message} {@LogData}", 
                        context, exception.Message, logData);
                    break;

                case OperationCanceledException:
                    _logger.LogInformation("Operation cancelled in {Context} {@LogData}", context, logData);
                    break;

                case TimeoutException:
                    _logger.LogWarning(exception, "Timeout in {Context}: {Message} {@LogData}", 
                        context, exception.Message, logData);
                    break;

                case OutOfMemoryException:
                case DbUpdateException:
                    _logger.LogError(exception, "Critical error in {Context}: {Message} {@LogData}", 
                        context, exception.Message, logData);
                    break;

                default:
                    _logger.LogError(exception, "Unhandled error in {Context}: {Message} {@LogData}", 
                        context, exception.Message, logData);
                    break;
            }
        }
    }
}