# ParaHockey Audit Logging System - Hybrid Approach Implementation Guide

## Current State (As of January 14, 2025)

### What Exists Now:
1. **BaseEntity**: Only has `DateCreated` and `IsActive` fields
2. **MemberLog Table**: Audit logs ONLY for Member entity changes
3. **Manual Logging**: Requires explicit service calls (easy to forget)
4. **Self-Registration Problem**: When members register themselves, EditorId = MemberId (shows "created by himself")
5. **Limited Coverage**: No audit trails for Parents, Emergency Contacts, or AdminUsers

### The Problem We're Solving:
- Admin actions are not distinguished from member self-service actions
- Can't tell if "Member updated by Member #123" means self-update or admin action
- No tracking for admin user management, parent/contact changes
- No automatic audit capture

## Hybrid Approach Design (To Be Implemented)

### 1. Enhanced BaseEntity (All entities will inherit these)
```csharp
public abstract class BaseEntity
{
    // Existing fields (keep these)
    public DateTime DateCreated { get; set; }
    public bool IsActive { get; set; }
    
    // NEW audit fields to add
    public DateTime? DateModified { get; set; }
    public int? CreatedByMemberId { get; set; }      // When member creates
    public int? CreatedByAdminId { get; set; }       // When admin creates
    public int? ModifiedByMemberId { get; set; }     // When member modifies
    public int? ModifiedByAdminId { get; set; }      // When admin modifies
    public ActionSource CreatedBySource { get; set; } // Enum: SelfService/AdminPanel/System
    public ActionSource? ModifiedBySource { get; set; }
}

public enum ActionSource
{
    System = 0,        // Automated system actions
    SelfService = 1,   // Member doing their own actions
    AdminPanel = 2     // Admin performing actions
}
```

### 2. New Universal AuditLog Table (Replaces MemberLog)
```csharp
public class AuditLog
{
    public int Id { get; set; }
    public string EntityType { get; set; }           // "Member", "AdminUser", "Parent", etc.
    public int EntityId { get; set; }                // ID of the affected record
    public string Action { get; set; }               // "Create", "Update", "Delete"
    public DateTime Timestamp { get; set; }
    public int? PerformedByMemberId { get; set; }    // If member performed action
    public int? PerformedByAdminId { get; set; }     // If admin performed action
    public ActionSource PerformedBySource { get; set; }
    public string PerformerName { get; set; }        // For display purposes
    public string IPAddress { get; set; }            // Security tracking
    public string OldValues { get; set; }            // JSON of previous values
    public string NewValues { get; set; }            // JSON of new values
    public string Description { get; set; }          // Human-readable summary
}
```

### 3. Implementation Strategy

#### Phase 1: Database Changes
1. Add new fields to BaseEntity
2. Create AuditLog table
3. Migrate existing MemberLog data to new AuditLog table
4. Create foreign key relationships

#### Phase 2: Automatic Audit Capture
1. Implement EF Core interceptor for SaveChangesAsync
2. Automatically populate audit fields based on current user context
3. Automatically create AuditLog entries for all entity changes

#### Phase 3: Service Layer Updates
1. Update authentication to track current user (admin vs member)
2. Pass user context through all service methods
3. Remove manual audit logging calls (now automatic)

#### Phase 4: Admin UI Updates
1. Add audit history view to member details
2. Add admin activity log page
3. Add "Last modified by" display to all admin pages

## Key Implementation Details

### User Context Tracking
```csharp
// In HttpContext or service base class
public class UserContext
{
    public bool IsAdmin { get; set; }
    public int? AdminId { get; set; }
    public int? MemberId { get; set; }
    public string UserName { get; set; }
    public ActionSource Source { get; set; }
}
```

### Automatic Audit in EF Interceptor
```csharp
// Pseudocode for SaveChangesAsync interceptor
foreach (var entry in ChangeTracker.Entries<BaseEntity>())
{
    if (entry.State == EntityState.Added)
    {
        entry.Entity.DateCreated = DateTime.UtcNow;
        if (currentUser.IsAdmin)
        {
            entry.Entity.CreatedByAdminId = currentUser.AdminId;
            entry.Entity.CreatedBySource = ActionSource.AdminPanel;
        }
        else
        {
            entry.Entity.CreatedByMemberId = currentUser.MemberId;
            entry.Entity.CreatedBySource = ActionSource.SelfService;
        }
        // Create AuditLog entry
    }
    else if (entry.State == EntityState.Modified)
    {
        entry.Entity.DateModified = DateTime.UtcNow;
        // Similar logic for Modified
        // Create AuditLog entry with old/new values
    }
}
```

## Example Scenarios After Implementation

### Scenario 1: Member Self-Registration
- Member.CreatedByMemberId = 123
- Member.CreatedByAdminId = null
- Member.CreatedBySource = SelfService
- AuditLog: "Member John Doe self-registered from IP *************"

### Scenario 2: Admin Creates Member
- Member.CreatedByMemberId = null
- Member.CreatedByAdminId = 456
- Member.CreatedBySource = AdminPanel
- AuditLog: "Admin Jane Smith created member John Doe"

### Scenario 3: Admin Updates Member
- Member.ModifiedByAdminId = 456
- Member.ModifiedBySource = AdminPanel
- AuditLog: "Admin Jane Smith updated phone number from (514)555-1234 to (514)555-5678"

### Scenario 4: Viewing Audit Trail
```sql
-- Quick check: Who last modified this member?
SELECT ModifiedByAdminId, ModifiedByMemberId, DateModified FROM Members WHERE Id = 123

-- Full history: Everything that happened to member 123
SELECT * FROM AuditLog WHERE EntityType = 'Member' AND EntityId = 123 ORDER BY Timestamp DESC

-- Admin activity: What has admin 456 done today?
SELECT * FROM AuditLog WHERE PerformedByAdminId = 456 AND DATE(Timestamp) = CURDATE()
```

## Benefits of This Approach

1. **Dual Storage**: Quick lookups on entity + complete history in AuditLog
2. **Automatic**: No more forgetting to log actions
3. **Comprehensive**: ALL entities get audit trails, not just Members
4. **Clear Attribution**: Always know if action was admin or self-service
5. **Forensic Detail**: JSON old/new values for investigating issues
6. **Performance**: Don't need joins for "who last modified this"

## Next Steps When Implementation Begins

1. Create migration for BaseEntity changes
2. Create AuditLog entity and migration
3. Implement CurrentUserService to track who's logged in
4. Create EF Core SaveChangesInterceptor
5. Update all admin pages to show "Last modified by" info
6. Create admin audit history viewer
7. Migrate existing MemberLog data to new structure
8. Remove old manual audit logging code

## Important Notes

- This is a DESIGN DOCUMENT - no code has been implemented yet
- The system currently uses MemberLog table which will be replaced
- All entities will automatically get audit capabilities through BaseEntity inheritance
- The hybrid approach provides both performance (quick lookups) and compliance (full history)