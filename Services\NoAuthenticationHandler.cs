using Microsoft.AspNetCore.Authentication;
using Microsoft.Extensions.Options;
using System.Security.Claims;
using System.Text.Encodings.Web;

namespace ParaHockeyApp.Services
{
    public class NoAuthenticationSchemeOptions : AuthenticationSchemeOptions { }

    public class NoAuthenticationHandler : AuthenticationHandler<NoAuthenticationSchemeOptions>
    {
        public NoAuthenticationHandler(IOptionsMonitor<NoAuthenticationSchemeOptions> options,
            ILoggerFactory logger,
            UrlEncoder encoder)
            : base(options, logger, encoder)
        {
        }

        protected override Task<AuthenticateResult> HandleAuthenticateAsync()
        {
            // Create a fake authenticated user for development
            var claims = new List<Claim>
            {
                new Claim(ClaimTypes.Name, "<EMAIL>"),
                new Claim(ClaimTypes.NameIdentifier, "dev-user-id"),
                new Claim(ClaimTypes.Email, "<EMAIL>")
            };

            var identity = new ClaimsIdentity(claims, "NoAuth");
            var principal = new ClaimsPrincipal(identity);
            var ticket = new AuthenticationTicket(principal, "NoAuth");

            return Task.FromResult(AuthenticateResult.Success(ticket));
        }
    }
}