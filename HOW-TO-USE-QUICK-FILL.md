# 🏒 How to Use Quick Registration Fill

## 🚀 Quick Start

1. **Run the script:**
   ```powershell
   .\quick-fill-simple.ps1
   ```

2. **Choose member type (1-5):**
   - `1` = Junior (8-17 years, includes parent info)
   - `2` = Development (16-25 years)
   - `3` = Elite (18-35 years)
   - `4` = Coach (25-55 years)
   - `5` = Volunteer (18-70 years)

3. **Choose how many members** to generate (default: 1)

4. **The script will:**
   - ✅ Open Chrome browser automatically
   - ✅ Navigate to registration page
   - ✅ Fill ALL form fields with realistic data
   - ✅ Ask if you want to submit each registration

## 🎯 What Gets Filled Automatically

### Basic Information
- **Name**: `Marie1 Tremblay1`, `Pierre2 Gagnon2` (incremental numbers)
- **Email**: `<EMAIL>` (realistic emails)
- **Phone**: `(*************` (formatted Canadian numbers)
- **Address**: Real Quebec addresses with proper postal codes
- **Birth Date**: Age-appropriate dates (<PERSON> gets 8-17 years old, etc.)

### Registration Details
- **Gender**: Random selection (Male/Female/Other)
- **Province**: Mostly Quebec (70%), others (30%)
- **Phone Type**: Mostly Mobile (80%), Other (20%)
- **Registration Type**: Automatically set based on your choice

### Type-Specific Information
- **Junior**: Automatically fills parent/guardian information
- **Adults**: Automatically fills emergency contact information

## 🖱️ During Execution

### Options When Form is Filled
- **`y`** = Submit this registration and continue to next
- **`n`** = Don't submit, just continue to next member  
- **`s`** = Skip all remaining members and exit

### What Happens After Submission
- ✅ Registration is submitted to database
- 🔄 Browser automatically returns to registration page
- ⏭️ Next member data is generated and filled

## 📊 Features

### Smart Data Generation
- **Incremental names** prevent duplicates
- **Age-appropriate birth dates** based on member type
- **Realistic Canadian data** (Quebec names, postal codes, phone numbers)
- **Persistent counter** tracks numbers across multiple runs

### Error Handling
- 🔄 **Auto-refresh** if page fails to load
- ⚠️ **Detailed error messages** for debugging
- 🛡️ **Fallback handling** continues even if some fields fail

### Browser Management
- 🌐 **Auto-opens Chrome** (installs Selenium if needed)
- ⏸️ **Keeps browser open** for manual review
- 🔄 **Smart navigation** between forms

## 🛠️ Troubleshooting

### If Script Fails to Start
1. **Install Selenium module:**
   ```powershell
   Install-Module -Name Selenium -Force
   ```

2. **Make sure app is running:**
   ```
   http://localhost:5285/Members/Register
   ```

### If Form Fields Don't Fill
- The script will show detailed error messages
- Browser stays open so you can manually complete
- Each field is tried individually with error handling

### If Chrome Driver Issues
- Script automatically installs/updates Chrome driver
- Uses WebDriverManager for compatibility

## 💡 Pro Tips

### Testing Different Types
```powershell
# Quick Junior test
.\quick-fill-simple.ps1
# Choose: 1, 1

# Generate 3 coaches
.\quick-fill-simple.ps1  
# Choose: 4, 3
```

### Advanced Usage
```powershell
# Direct command (bypass menu)
.\auto-fill-browser.ps1 -MemberType "Junior" -Count 1
```

### Batch Testing
- Generate multiple members of different types
- Mix and match to test various scenarios
- Use `s` to skip if you generate too many

## 📝 Generated Files

- **`registration-counter.txt`** - Tracks incremental numbers
- **`autofill-*.js`** - JavaScript files (backup method)

The script automatically handles incrementing names across runs, so `Marie1` becomes `Marie2` the next time you run it.

---

**Ready to test? Run `.\quick-fill-simple.ps1` and start filling registrations in seconds!** 🚀