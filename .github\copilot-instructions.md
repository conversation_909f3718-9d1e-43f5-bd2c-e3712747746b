# ParaHockey Application - AI Coding Agent Instructions

## Project Overview

ParaHockey is an ASP.NET Core 8.0 MVC member registration application with a sophisticated **4-calendar event management system**. Built for multi-environment deployment with conditional authentication and comprehensive localization.

## Critical Architecture Patterns

### 1. Environment-Conditional Configuration

This application's core pattern is **environment-specific behavior without code changes**:

```csharp
// ✅ CORRECT: Configuration-driven features
if (_environmentSettings.ShowDevelopmentTools) {
    // Show auto-fill buttons
}

// ❌ NEVER: Hard-coded environment checks
if (Environment.IsProduction()) {
    // Different business logic - FORBIDDEN
}
```

**Key Files:**

-   `Models/Configuration/EnvironmentSettings.cs` - Central environment configuration
-   `appsettings.{Environment}.json` - Environment-specific overrides
-   `Services/EnvironmentConfigurationService.cs` - Validation service

**Environment Differences (Configuration Only):**

-   Development: NoAuth, info theme, dev tools enabled
-   Test/Staging: Azure AD, danger theme with banner, dev tools enabled
-   Production: Azure AD, primary theme, no dev tools, user-friendly errors

### 2. Four-Calendar System (Critical)

When working with "calendars", understand there are **four distinct implementations**:

1. **Admin Calendar** (`/Admin/Calendar`) - Full CRUD, all events
2. **Subscribe Calendar** (`/Events/Subscribe`) - Member registration, published events only
3. **Public Calendar** (`/Home/PublicCalendar`) - Read-only, anonymous access
4. **Members Calendar** (`/Members/CalendarReadOnly`) - Read-only, authenticated members

**Key Pattern:** Changes to "all calendars" means updating all four implementations with role-appropriate features.

### 3. Event Registration System

Complete workflow exists for member event subscriptions:

```csharp
// Registration flow: EventsController.Join()
var userContext = _userContextService.GetCurrentUser();
if (userContext.IsAdmin) {
    // Admins cannot register - show appropriate message
}
```

**Database:** Uses existing `Events` and `EventRegistrations` tables - no additional tables needed.

### 4. Authentication Patterns

Conditional authentication based on environment:

```csharp
// Program.cs pattern
if (envSettings?.UseAuthentication == true) {
    // Azure AD for Test/Production
} else {
    // NoAuth for Development
}
```

### 5. Planning

```I am a professional software architecture & planning assistant.
When you say **“Plan this: …”** (describing a feature), I will:

**I will not code the "feature", I will only create the plan!!!**
1. **Infer a concise, kebab-case `feature_name`.**
2. **Analyze the existing codebase** and determine the cleanest, most maintainable integration strategy that follows best practices.
3. **Create** a folder `Plan/Specs/[feature_name]` at the project root.
4. Inside that folder, generate three Markdown files modeled on the supplied examples:

   * **Requirements.md** – functional & non-functional requirements, acceptance criteria, user stories.
   * **Design.md** – architecture, data flow, interfaces, diagrams (Mermaid where helpful), all traceable to Requirements.md.
   * **Tasks.md** – atomic implementation steps starting with “- \[ ]”, logically grouped and tagged with requirement numbers.
5. **Output this prompt for the coding AI** (inline here, not separate). First, explain what the feature is at the start of the prompt, then contine the prompt with this:
   *Feature: \[feature_name]. Docs path: Plan/Specs/\[feature_name]. Please open Requirements.md, Design.md, and Tasks.md, read them fully, and keep their contents in memory. Starting with the first unchecked item in Tasks.md, execute each task one by one. After completing a task, mark its checkbox as “\[x]”. Continue until all tasks are complete. Then notify me and provide a concise manual test procedure to verify the implementation.*

Add this prompt to the same folder in a prompt.md file.
If any vital detail (language, tech stack, business rule, etc.) is missing, I will request clarification in **\[brackets]** before proceeding.

**I will not code the "feature", I will only create the plan!!!**
```

**User Context Service:** Use `IUserContextService` to distinguish admin/member/visitor contexts throughout the application.

## Mandatory Localization

**ALL user-facing text MUST be localized:**

```csharp
// Views
@SharedLocalizer["MyText"]

// Controllers
_localizer["ErrorMessage"]

// DTOs/Models
[Required(ErrorMessageResourceName = "RequiredField")]
```

**Resource Files:** `Resources/SharedResource.resx` and `SharedResource.en-CA.resx`

## Service Layer Architecture

Business logic resides in services, not controllers:

-   `IEventService` - Event management and registration logic
-   `IUserContextService` - Current user context (admin/member/visitor)
-   `IAuditLogService` - Automatic audit trails via interceptor
-   `IEnvironmentConfigurationService` - Environment validation

**Pattern:** Controllers are thin, delegate to services, return appropriate ActionResults.

## Database & Entity Framework

-   **Single Database:** SQL Server for all environments (ParaHockeyDB, ParaHockeyDB_TEST, ParaHockeyDB)
-   **Audit Interceptor:** Automatic audit logging for all entities via `AuditInterceptor`
-   **Migration Pattern:** EF Core migrations with environment-specific data seeding

## Testing & Validation

Always provide step-by-step test instructions:

```markdown
## 🧪 **Test Now**

1. **As Visitor**: Click event → should see login prompt ✅
2. **As Member**: Click register → should register immediately ✅
3. **As Admin**: Click register → should show "Admins cannot register" ❌
```

## Common Pitfalls

1. **Layout Issues:** Ensure `Views/_ViewStart.cshtml` points to correct MVC layout
2. **Environment Logic:** Never put environment checks in business logic - use configuration
3. **Calendar Scope:** "All calendars" means all four implementations
4. **Localization:** No hardcoded strings in user-facing components
5. **Authentication:** Use `UserContextService` not direct `User.Identity` checks

## Development Workflow

-   **Build:** `dotnet build ParaHockey.sln`
-   **Run:** `dotnet run` or F5 in Visual Studio
-   **Database:** Migrations auto-apply in development
-   **Deployment:** Azure DevOps pipeline on main branch

## Key Integration Points

-   **FullCalendar.js:** Used across all four calendar implementations
-   **Bootstrap:** Environment-specific themes via CSS classes
-   **jQuery:** Preferred for client-side interactions (already loaded)
-   **AutoMapper:** DTO/Entity mapping throughout services
-   **EPPlus:** Excel import/export functionality

This application prioritizes consistency, configuration-driven behavior, and comprehensive localization while maintaining role-based access across multiple calendar interfaces.
