using ParaHockeyApp.DTOs;

namespace ParaHockeyApp.Services
{
    /// <summary>
    /// Service interface for member data export functionality
    /// Provides CSV and Excel export capabilities with intelligent file naming
    /// </summary>
    public interface IMemberExportService
    {
        /// <summary>
        /// Exports member data to CSV format based on search criteria
        /// </summary>
        /// <param name="request">Export request containing search criteria and export options</param>
        /// <returns>CSV file content as byte array</returns>
        Task<byte[]> ExportToCsvAsync(MemberExportRequest request);

        /// <summary>
        /// Exports member data to Excel format based on search criteria
        /// </summary>
        /// <param name="request">Export request containing search criteria and export options</param>
        /// <returns>Excel file content as byte array</returns>
        Task<byte[]> ExportToExcelAsync(MemberExportRequest request);

        /// <summary>
        /// Generates an intelligent filename based on applied filters and search terms
        /// </summary>
        /// <param name="request">Export request containing search criteria</param>
        /// <returns>Generated filename with appropriate extension</returns>
        string GenerateFileName(MemberExportRequest request);

        /// <summary>
        /// Generates an intelligent filename based on applied filters and search terms with localized names
        /// </summary>
        /// <param name="request">Export request containing search criteria</param>
        /// <returns>Generated filename with appropriate extension and localized names</returns>
        Task<string> GenerateFileNameAsync(MemberExportRequest request);

        /// <summary>
        /// Retrieves filter options for dynamic dropdown population
        /// </summary>
        /// <param name="filterType">Type of filter (e.g., "city", "province", "registrationType")</param>
        /// <param name="searchTerm">Optional search term to filter the options</param>
        /// <returns>Filter options response with available values</returns>
        Task<FilterOptionsResponse> GetFilterOptionsAsync(string filterType, string? searchTerm = null);
    }
}