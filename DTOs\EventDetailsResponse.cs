namespace ParaHockeyApp.DTOs
{
    /// <summary>
    /// User registration state for smart UI rendering
    /// </summary>
    public enum UserRegistrationState
    {
        Visitor,           // Show login prompt
        Member,            // Show register/unregister
        Admin,             // Show admin message
        MemberRegistered,  // Show unregister option
        MemberBlocked      // Show appropriate message
    }

    /// <summary>
    /// Enhanced event details response with user context
    /// </summary>
    public class EventDetailsResponse
    {
        public int Id { get; set; }
        public string Title { get; set; } = string.Empty;
        public DateTime StartDate { get; set; }
        public DateTime EndDate { get; set; }
        public string? Location { get; set; }
        public string? Description { get; set; }
        public bool RequiresRegistration { get; set; }
        public int MaxParticipants { get; set; }
        public int CurrentRegistrations { get; set; }
        public int AvailableSpots { get; set; }
        public bool IsFull { get; set; }
        public bool IsRegistrationOpen { get; set; }
        public DateTime? RegistrationDeadline { get; set; }
        public bool IsUserRegistered { get; set; }
        public UserRegistrationState UserState { get; set; }
        public string? ContactPerson { get; set; }
        public string? ContactEmail { get; set; }
        public string? ContactPhone { get; set; }
        public string? CategoryName { get; set; }
        public string? CategoryColor { get; set; }
        public bool IsAllDay { get; set; }
        public string DateRangeDisplay { get; set; } = string.Empty;
    }
}