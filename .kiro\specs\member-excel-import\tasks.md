# Implementation Plan

## 🎯 **Current Progress & Status**

### **Branch:** `feature/member-excel-import`

### **CSV Files Analysis** ✅ COMPLETED

Located in `Resources/documents/`:

1. **Rapport Parahockey 1 - Membres.csv** - Main member data with HCR linking number
2. **Rapport Parahockey 2 - contact-urgence.csv** - Emergency contacts linked by HCR number
3. **Rapport Parahockey 3 - contact-parent.csv** - Parent data linked by HCR number

**Key Mappings:**

-   `Numéro HCR` → Links all 3 files (primary identifier)
-   `Identité de genre`: "Garçon/Homme" = Male, "Fille/Femme" = Female
-   Address: Combine `Numéro d'unité` + `Numéro de rue` + `Rue`
-   Province: `QC` = Quebec, etc.

### **📁 Files Already Created:**

-   `Models/Entities/TempMemberStatus.cs` ✅
-   `Models/Entities/MemberImportBatch.cs` ✅
-   `Models/Entities/TempMember.cs` ✅
-   `Services/INormalizationService.cs` ✅
-   `Services/NormalizationService.cs` ✅
-   `Migrations/20250801122344_AddMemberImportSystem.cs` ✅

### **🔄 NEXT STEP:** Register `NormalizationService` in Program.cs line ~196

---

-   [x] 1. Create database entities and migrations for import system

    -   Create TempMember entity class with all required properties and relationships
    -   Create MemberImportBatch entity class with statistics tracking
    -   Generate and implement Entity Framework migration for new tables
    -   Add DbSet properties to ApplicationContext for new entities
    -   Create database indexes for performance optimization
    -   _Requirements: 2.3, 2.4, 2.5_

-   [x] 2. Implement core normalization service

    -   Create INormalizationService interface with email, phone, and name normalization methods
    -   Implement NormalizationService with email lowercase conversion, phone number cleaning, and postal code standardization
    -   Add unit tests for all normalization functions with various input scenarios
    -   _Requirements: 1.3, 10.4_

-   [x] 3. Create duplicate detection service

    -   Create IDuplicateDetectionService interface with member matching methods
    -   Implement DuplicateDetectionService with email-first, then name+DOB matching logic
    -   Add method to find existing members using normalized comparison values
    -   Write unit tests for duplicate detection with various matching scenarios
    -   _Requirements: 3.1, 3.2, 3.3, 3.4, 3.5_

-   [x] 4. Build Excel parsing and validation service

    -   Create IMemberImportService interface with file processing methods
    -   Implement MemberImportService using ClosedXML library for Excel parsing
    -   Add file validation for format, size, and row limits using appsettings configuration
    -   Implement row-by-row validation with error collection in JSON format
    -   Create batch processing logic to handle large files efficiently
    -   Write unit tests for file parsing with sample Excel files
    -   _Requirements: 1.1, 1.2, 1.3, 1.4, 1.5, 2.1, 2.2, 2.3, 10.1, 10.2_

-   [x] 5. Implement temp member management service

    -   Create ITempMemberService interface with CRUD operations for temp members
    -   Implement TempMemberService with methods for creating, updating, and deleting temp records
    -   Add bulk creation functionality for converting temp members to actual members
    -   Implement status update methods for tracking temp member processing states
    -   Write unit tests for all temp member operations
    -   _Requirements: 2.4, 6.5, 7.3, 7.4_

-   [x] 6. Create import batch summary functionality

    -   Add GetBatchSummaryAsync method to MemberImportService
    -   Create ImportBatchSummary view model with all required statistics
    -   Implement queue retrieval methods for different temp member statuses
    -   Add batch statistics calculation and updating logic
    -   Write unit tests for batch summary calculations
    -   _Requirements: 6.1, 6.2, 6.3, 6.4_

-   [x] 7. Build duplicate resolution merge system

    -   Add PreviewMergeAsync method to DuplicateDetectionService for field comparison
    -   Create DuplicateResolutionViewModel and FieldComparison classes
    -   Implement ApplyMergeAsync method with field-by-field merge logic
    -   Add transaction handling for merge operations with rollback capability
    -   Create unit tests for merge preview and application scenarios
    -   _Requirements: 4.1, 4.2, 4.3, 4.4, 5.1, 5.2, 5.3, 5.4, 5.5_

-   [x] 8. Create import upload controller and views

    -   Create ImportController with GET and POST actions for file upload
    -   Build upload view with file selection, validation, and progress indication
    -   Implement batch summary view displaying import statistics and queue links
    -   Add error handling and user feedback for upload operations
    -   Create responsive UI components that work on mobile and desktop
    -   _Requirements: 1.1, 1.4, 1.5, 6.1_

-   [x] 9. Build temp member queue management controllers

    -   Create TempMembersController with actions for all queue types (NeedsFix, ReadyToCreate, Duplicates)
    -   Implement queue listing views with filtering and pagination
    -   Add individual and bulk action buttons for temp member processing
    -   Create responsive grid layouts for queue management
    -   Write integration tests for queue operations
    -   _Requirements: 6.2, 6.3, 6.4, 6.5_

-   [x] 10. Implement member creation from temp data

    -   Add Create action to TempMembersController for fixing invalid temp records
    -   Modify existing Members/Create view to support temp member pre-filling
    -   Implement form validation with inline error display for temp member data
    -   Add success/cancel handling that updates temp member status appropriately
    -   Create integration tests for member creation from temp data workflow
    -   _Requirements: 7.1, 7.2, 7.3, 7.4, 7.5_

-   [x] 11. Build side-by-side duplicate resolution interface

    -   Create Resolve action and view in TempMembersController for duplicate comparison
    -   Implement side-by-side layout with temp member on left, existing member on right
    -   Add field comparison logic with identical value detection and button hiding
    -   Create JavaScript functionality for field selection and finalize button enabling
    -   Build responsive design that works on tablets and mobile devices
    -   _Requirements: 4.1, 4.2, 4.3, 4.4_

-   [x] 12. Create merge confirmation modal system

    -   Add ConfirmMerge action to TempMembersController for merge preview
    -   Build confirmation modal displaying field changes in old → new format
    -   Implement Accept and Edit button functionality with proper form handling
    -   Add JavaScript for modal display and form submission
    -   Create success messaging and navigation after merge completion
    -   _Requirements: 5.1, 5.2, 5.3, 5.4, 5.5_

-   [x] 13. Integrate audit logging for import operations

    -   Add audit logging calls to MemberImportService for batch creation
    -   Implement audit logging in TempMemberService for member creation from temp data
    -   Add detailed field-level audit logging in DuplicateDetectionService for merges
    -   Ensure all import operations are tracked with proper user attribution
    -   Write tests to verify audit log entries are created correctly
    -   _Requirements: 9.1, 9.2, 9.3, 9.4_

-   [x] 14. Implement security and authorization

    -   Add [Authorize] attributes to all import controllers with admin role requirements
    -   Implement CSRF protection on all import forms
    -   Add input sanitization for all text fields in temp member processing
    -   Create security tests to verify admin-only access to import functionality
    -   _Requirements: 9.4, 9.5_

-   [x] 15. Add multi-file relationship matching support

    -   Extend TempMember entity with ParentData and EmergencyContactData JSON fields
    -   Create relationship matching logic in MemberImportService for parent/contact files
    -   Add UI support for specifying file types during upload (member, parent, contact)
    -   Implement manual relationship review interface for unmatched records
    -   Write integration tests for multi-file import scenarios
    -   _Requirements: 8.1, 8.2, 8.3, 8.4, 8.5_

-   [x] 16. Implement performance optimizations and error handling

    -   Add streaming file processing for large Excel files to avoid memory issues
    -   Implement progress tracking and cancellation support for long-running imports
    -   Add concurrency handling to prevent multiple admins editing same temp records
    -   Create comprehensive error handling with user-friendly messages
    -   Add database transaction management with proper rollback on failures
    -   Write performance tests with maximum file sizes and row limits
    -   _Requirements: 10.1, 10.2, 10.3, 10.4, 10.5_

-   [x] 17. Create comprehensive integration tests

    -   Write end-to-end test for complete import workflow from upload to member creation
    -   Create test for validation error handling and NeedsFix queue processing
    -   Implement test for duplicate detection and merge resolution workflow
    -   Add test for bulk member creation from ReadyToCreate queue
    -   Create test for audit logging verification across all import operations
    -   Write test for multi-file import with relationship matching
    -   _Requirements: All requirements validation_

-   [x] 18. Add configuration and localization support
    -   Add import configuration settings to appsettings.json (file size limits, row limits)
    -   Create localized resource strings for all import UI text and error messages
    -   Implement multilingual support for import forms and validation messages
    -   Add configuration validation and default value handling
    -   Write tests for configuration loading and localization
    -   _Requirements: 1.2, 10.2_
