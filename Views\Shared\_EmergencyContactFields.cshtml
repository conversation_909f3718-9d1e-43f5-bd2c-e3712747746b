@model ParaHockeyApp.Models.ViewModels.MemberRegistrationViewModel
@using Microsoft.AspNetCore.Mvc.Localization
@inject IHtmlLocalizer<ParaHockeyApp.Resources.SharedResourceMarker> SharedLocalizer

<div id="emergency-contact-section" class="emergency-contact-container" style="display: none;">
    <div class="section-header mb-4 mt-4">
        <h4 class="text-danger border-bottom pb-2">
            <i class="fas fa-exclamation-triangle"></i> @SharedLocalizer["EmergencyContactInformation"]
        </h4>
    </div>

    <div class="card mb-4">
        <div class="card-header bg-light">
            <h6 class="card-title mb-0">@SharedLocalizer["EmergencyContactDetails"]</h6>
        </div>
        <div class="card-body">
            <div class="row mb-3">
                <div class="col-md-6">
                    <label for="EmergencyContact_FirstName"
                        class="form-label required">@SharedLocalizer["FirstName"]</label>
                    <input asp-for="EmergencyContact.FirstName" type="text" class="form-control emergency-field"
                        placeholder="@SharedLocalizer["ThirdPersonFirstNamePlaceholder"]" autocomplete="given-name" />
                    <span class="text-danger small emergency-validation" data-valmsg-for="EmergencyContact.FirstName"
                        data-valmsg-replace="true"></span>
                </div>
                <div class="col-md-6">
                    <label for="EmergencyContact_LastName"
                        class="form-label required">@SharedLocalizer["LastName"]</label>
                    <input asp-for="EmergencyContact.LastName" type="text" class="form-control emergency-field"
                        placeholder="@SharedLocalizer["ThirdPersonLastNamePlaceholder"]" autocomplete="family-name" />
                    <span class="text-danger small emergency-validation" data-valmsg-for="EmergencyContact.LastName"
                        data-valmsg-replace="true"></span>
                </div>
            </div>

            <div class="row mb-3">
                <div class="col-md-6">
                    <label for="EmergencyContact_RelationToUser"
                        class="form-label required">@SharedLocalizer["RelationToUser"]</label>
                    <select asp-for="EmergencyContact.RelationToUser" class="form-select emergency-field">
                        <option value="">@SharedLocalizer["SelectRelation"]</option>
                        <option value="Spouse">@SharedLocalizer["Spouse"]</option>
                        <option value="Parent">@SharedLocalizer["Parent"]</option>
                        <option value="Sibling">@SharedLocalizer["Sibling"]</option>
                        <option value="Friend">@SharedLocalizer["Friend"]</option>
                        <option value="Other">@SharedLocalizer["OtherRelation"]</option>
                    </select>
                    <span class="text-danger small emergency-validation"
                        data-valmsg-for="EmergencyContact.RelationToUser" data-valmsg-replace="true"></span>
                </div>
                <div class="col-md-6">
                    <label for="EmergencyContact_Phone"
                        class="form-label required">@SharedLocalizer["PhoneNumber"]</label>
                    <input asp-for="EmergencyContact.Phone" type="tel"
                        class="form-control emergency-field emergency-phone" maxlength="14" placeholder="(*************"
                        autocomplete="tel" />
                    <span class="text-danger small emergency-validation" data-valmsg-for="EmergencyContact.Phone"
                        data-valmsg-replace="true"></span>
                </div>
            </div>

            <div class="row mb-3">
                <div class="col-md-6">
                    <label for="EmergencyContact_Email" class="form-label required">@SharedLocalizer["Email"]</label>
                    <input asp-for="EmergencyContact.Email" type="email"
                        class="form-control emergency-field emergency-email"
                        placeholder="@SharedLocalizer["ThirdPersonEmailPlaceholder"]" autocomplete="email" />
                    <span class="text-danger small emergency-validation" data-valmsg-for="EmergencyContact.Email"
                        data-valmsg-replace="true"></span>
                </div>
            </div>
        </div>
    </div>
</div>
