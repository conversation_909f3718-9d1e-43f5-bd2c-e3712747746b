using AutoMapper;
using ParaHockeyApp.DTOs;
using ParaHockeyApp.Models.Entities;
using ParaHockeyApp.Models.ViewModels;

namespace ParaHockeyApp.Models
{
    /// <summary>
    /// AutoMapper profile for entity to DTO mappings
    /// </summary>
    public class MappingProfile : Profile
    {
        public MappingProfile()
        {

            // Member mappings
            CreateMap<MemberRegistrationViewModel, Member>();
            CreateMap<Member, MemberRegistrationViewModel>();
            CreateMap<Member, MemberResponseDto>()
                .ForMember(dest => dest.NomComplet, opt => opt.MapFrom(src => src.FullName))
                .ForMember(dest => dest.Age, opt => opt.MapFrom(src => src.Age))
                .ForMember(dest => dest.TypeInscription, opt => opt.MapFrom(src => src.RegistrationType.DisplayNameKey));

            // Parent & EmergencyContact mappings
            CreateMap<ParentViewModel, Parent>();
            CreateMap<EmergencyContactViewModel, EmergencyContact>();

            // Future Para Hockey entity mappings will be added here
            // Example: CreateMap<Game, GameResponseDto>();
            // Example: CreateMap<CreateGameDto, Game>();
        }
    }
}