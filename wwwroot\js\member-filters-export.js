/**
 * Member Filtering and Export Functionality
 * Handles dynamic filter loading, city search, and export modal functionality
 */

$(document).ready(function () {
    initializeMemberFilters();
    initializeExportModal();
    initializeActiveFilters();
});

// Global variables for managing state
let citySearchTimeout;
let currentFilters = {};

/**
 * Initialize all filter-related functionality
 */
function initializeMemberFilters() {
    // Filter type dropdown change handler
    $("#filterType").on("change", function () {
        const filterType = $(this).val();
        handleFilterTypeChange(filterType);
    });

    // City search with debounced AJAX calls
    $("#citySearch").on("input", function () {
        const searchTerm = $(this).val();
        handleCitySearch(searchTerm);
    });

    // Apply filter button click
    $("#applyFilter").on("click", function () {
        applyCurrentFilter();
    });

    // Clear filters button click
    $("#clearFilters").on("click", function () {
        clearAllFilters();
    });

    // Filter value dropdown change (for non-city filters)
    $("#filterValue").on("change", function () {
        const filterType = $("#filterType").val();
        const filterValue = $(this).val();

        if (filterType && filterValue) {
            $("#applyFilter").show();
        } else {
            $("#applyFilter").hide();
        }
    });
}

/**
 * Handle filter type dropdown change
 */
function handleFilterTypeChange(filterType) {
    // Reset and hide all filter controls
    resetFilterControls();

    if (!filterType) {
        return;
    }

    // Show apply button
    $("#applyFilter").show();

    // Handle city search separately
    if (filterType === "city") {
        $("#citySearch").show().focus();
        loadCityOptions();
    } else {
        // For other filter types, show the dropdown and load options
        $("#filterValue")
            .show()
            .empty()
            .append('<option value="">Loading...</option>');
        loadFilterOptions(filterType);
    }
}

/**
 * Handle city search with debouncing
 */
function handleCitySearch(searchTerm) {
    clearTimeout(citySearchTimeout);

    // Only search if at least 2 characters entered
    if (searchTerm.length >= 2) {
        citySearchTimeout = setTimeout(function () {
            loadCityOptions(searchTerm);
        }, 300); // 300ms debounce
    } else if (searchTerm.length === 0) {
        // Load all cities when search is cleared
        loadCityOptions();
    }
}

/**
 * Reset all filter controls to initial state
 */
function resetFilterControls() {
    $("#filterValue").hide().empty();
    $("#citySearch").hide().val("");
    $("#applyFilter").hide();
    $(".city-results-dropdown").remove();
}

/**
 * Load filter options from server
 */
function loadFilterOptions(filterType, searchTerm = "") {
    $.ajax({
        url: "/Admin/GetFilterOptions",
        method: "GET",
        data: {
            filterType: filterType,
            searchTerm: searchTerm,
        },
        success: function (response) {
            if (response.success && response.data) {
                populateFilterDropdown(response.data.options);
            } else {
                console.error(
                    "Error loading filter options:",
                    response.error || "Unknown error"
                );
                showFilterError("Error loading filter options");
            }
        },
        error: function (xhr, status, error) {
            console.error("AJAX error loading filter options:", error);
            showFilterError("Failed to load filter options");
        },
    });
}

/**
 * Load city options with optional search term
 */
function loadCityOptions(searchTerm = "") {
    $.ajax({
        url: "/Admin/GetFilterOptions",
        method: "GET",
        data: {
            filterType: "city",
            searchTerm: searchTerm,
        },
        success: function (response) {
            if (response.success && response.data) {
                showCityDropdown(response.data.options);
            } else {
                console.error(
                    "Error loading city options:",
                    response.error || "Unknown error"
                );
                showCityDropdown([]);
            }
        },
        error: function (xhr, status, error) {
            console.error("AJAX error loading city options:", error);
            showCityDropdown([]);
        },
    });
}

/**
 * Populate the filter value dropdown
 */
function populateFilterDropdown(options) {
    const $select = $("#filterValue");
    $select.empty();

    if (!options || options.length === 0) {
        $select.append('<option value="">No options available</option>');
        return;
    }

    // Add default option
    $select.append('<option value="">Select an option...</option>');

    // Add filter options
    options.forEach(function (option) {
        let displayText = option.display;
        if (option.count !== undefined && option.count !== null) {
            displayText += ` (${option.count})`;
        }
        $select.append(
            `<option value="${option.value}">${displayText}</option>`
        );
    });
}

/**
 * Show city search dropdown with results
 */
function showCityDropdown(cities) {
    const $citySearch = $("#citySearch");
    let $dropdown = $(".city-results-dropdown");

    // Create dropdown if it doesn't exist
    if ($dropdown.length === 0) {
        $dropdown = $(
            '<div class="city-results-dropdown dropdown-menu"></div>'
        );
        $citySearch.parent().append($dropdown);
    }

    $dropdown.empty();

    if (!cities || cities.length === 0) {
        $dropdown.append(
            '<div class="dropdown-item text-muted">No cities found</div>'
        );
    } else {
        cities.forEach(function (city) {
            let displayText = city.display;
            if (city.count !== undefined && city.count !== null) {
                displayText += ` (${city.count})`;
            }
            $dropdown.append(
                `<a href="#" class="dropdown-item city-option" data-value="${city.value}">${displayText}</a>`
            );
        });
    }

    $dropdown.addClass("show");

    // Handle city selection
    $(".city-option").on("click", function (e) {
        e.preventDefault();
        const cityValue = $(this).data("value");
        $citySearch.val(cityValue);
        $dropdown.removeClass("show");
        $("#applyFilter").show();
    });

    // Hide dropdown when clicking outside
    $(document).on("click.cityDropdown", function (e) {
        if (
            !$(e.target).closest(".city-results-dropdown, #citySearch").length
        ) {
            $dropdown.removeClass("show");
            $(document).off("click.cityDropdown");
        }
    });
}

/**
 * Apply the currently selected filter
 */
function applyCurrentFilter() {
    const filterType = $("#filterType").val();
    let filterValue;

    if (filterType === "city") {
        filterValue = $("#citySearch").val().trim();
    } else {
        filterValue = $("#filterValue").val();
    }

    if (!filterType || !filterValue) {
        return;
    }

    // Store current filter
    currentFilters[filterType] = filterValue;

    // Apply filter by updating URL and reloading
    applyFilterToUrl(filterType, filterValue);
}

/**
 * Apply filter by updating URL parameters and reloading page
 */
function applyFilterToUrl(filterType, filterValue) {
    const currentUrl = new URL(window.location.href);
    const params = new URLSearchParams(currentUrl.search);

    // Map filter types to request parameter names
    const paramMapping = {
        registrationtype: "RegistrationTypeIds",
        province: "Province",
        city: "City",
        status: "IsActive",
    };

    const paramName = paramMapping[filterType];
    if (paramName) {
        params.set(paramName, filterValue);

        // Reset to first page when applying filters
        params.set("page", "1");

        currentUrl.search = params.toString();
        window.location.href = currentUrl.toString();
    }
}

/**
 * Clear all active filters
 */
function clearAllFilters() {
    const currentUrl = new URL(window.location.href);
    const params = new URLSearchParams(currentUrl.search);

    // Remove all filter parameters
    params.delete("RegistrationTypeIds");
    params.delete("Province");
    params.delete("City");
    params.delete("IsActive");

    // Reset to first page
    params.set("page", "1");

    currentUrl.search = params.toString();
    window.location.href = currentUrl.toString();
}

/**
 * Show filter error message
 */
function showFilterError(message) {
    const $select = $("#filterValue");
    $select.empty().append(`<option value="">${message}</option>`);
}

/**
 * Initialize active filters display from URL parameters
 */
function initializeActiveFilters() {
    const params = new URLSearchParams(window.location.search);
    let hasActiveFilters = false;

    // Clear existing badges
    $("#filterBadges").empty();

    // Check for each filter type and add badges
    if (params.has("RegistrationTypeIds")) {
        addFilterBadge("registrationtype", params.get("RegistrationTypeIds"));
        hasActiveFilters = true;
    }

    if (params.has("Province")) {
        addFilterBadge("province", params.get("Province"));
        hasActiveFilters = true;
    }

    if (params.has("City")) {
        addFilterBadge("city", params.get("City"));
        hasActiveFilters = true;
    }

    if (params.has("IsActive")) {
        addFilterBadge("status", params.get("IsActive"));
        hasActiveFilters = true;
    }

    // Show active filters section and clear button if there are active filters
    if (hasActiveFilters) {
        $("#activeFilters").show();
        $("#clearFilters").show();
    } else {
        $("#activeFilters").hide();
        $("#clearFilters").hide();
    }
}

/**
 * Add a filter badge to the active filters display
 */
function addFilterBadge(filterType, filterValue) {
    const filterTypeDisplay = {
        registrationtype: "Registration Type",
        province: "Province",
        city: "City",
        status: "Status",
    };

    // For status, convert boolean to readable text
    let displayValue = filterValue;
    if (filterType === "status") {
        displayValue = filterValue === "true" ? "Active" : "Inactive";
    }

    const badge = `
        <span class="badge bg-primary me-1 mb-1">
            ${filterTypeDisplay[filterType]}: ${displayValue}
            <button type="button" class="btn-close btn-close-white ms-1" 
                    onclick="removeFilter('${filterType}')" 
                    style="font-size: 0.5rem;" aria-label="Remove filter"></button>
        </span>
    `;

    $("#filterBadges").append(badge);
}

/**
 * Remove a specific filter
 */
function removeFilter(filterType) {
    const currentUrl = new URL(window.location.href);
    const params = new URLSearchParams(currentUrl.search);

    // Map filter types to request parameter names
    const paramMapping = {
        registrationtype: "RegistrationTypeIds",
        province: "Province",
        city: "City",
        status: "IsActive",
    };

    const paramName = paramMapping[filterType];
    if (paramName && params.has(paramName)) {
        params.delete(paramName);

        // Reset to first page
        params.set("page", "1");

        currentUrl.search = params.toString();
        window.location.href = currentUrl.toString();
    }
}

/**
 * Initialize export modal functionality
 */
function initializeExportModal() {
    // Export format selection
    $(".export-format").on("change", function () {
        const format = $(this).val();
        $("#exportFormat").val(format);

        // Update modal display based on selection
        updateExportModalDisplay(format);
    });

    // Export button click
    $("#exportButton").on("click", function () {
        performExport();
    });

    // Initialize modal when it's shown
    $("#exportModal").on("show.bs.modal", function () {
        prepareExportModal();
    });
}

/**
 * Prepare the export modal with current search/filter state
 */
function prepareExportModal() {
    const params = new URLSearchParams(window.location.search);

    // Set hidden form fields based on current URL parameters
    $("#exportSearchTerm").val(params.get("search") || "");
    $("#exportRegistrationTypeIds").val(
        params.get("RegistrationTypeIds") || ""
    );
    $("#exportProvince").val(params.get("Province") || "");
    $("#exportCity").val(params.get("City") || "");
    $("#exportIsActive").val(params.get("IsActive") || "");

    // Set default format to Excel
    $("#formatExcel").prop("checked", true);
    $("#exportFormat").val("Excel");

    updateExportModalDisplay("Excel");
}

/**
 * Update export modal display based on selected format
 */
function updateExportModalDisplay(format) {
    const formatText = format === "Excel" ? "Excel (.xlsx)" : "CSV (.csv)";
    $("#selectedFormatDisplay").text(formatText);
}

/**
 * Perform the export operation
 */
function performExport() {
    const form = $("#exportForm");
    const formData = form.serialize();

    // Show loading state
    const $exportButton = $("#exportButton");
    const originalText = $exportButton.html();
    $exportButton
        .html('<i class="fas fa-spinner fa-spin"></i> Exporting...')
        .prop("disabled", true);

    // Create a temporary form for file download
    const downloadForm = $("<form>", {
        method: "GET",
        action: "/Admin/ExportMembersAdvanced",
    });

    // Add all form fields to download form
    form.find("input").each(function () {
        const input = $(this);
        if (input.val()) {
            downloadForm.append(
                $("<input>", {
                    type: "hidden",
                    name: input.attr("name"),
                    value: input.val(),
                })
            );
        }
    });

    // Append to body and submit
    $("body").append(downloadForm);
    downloadForm.submit();
    downloadForm.remove();

    // Reset button state after a short delay
    setTimeout(function () {
        $exportButton.html(originalText).prop("disabled", false);
        $("#exportModal").modal("hide");
    }, 1000);
}

// Global functions for removing filters (called from HTML)
window.removeFilter = removeFilter;
