namespace ParaHockey.E2E.Tests.Infrastructure
{
    public class TestData
    {
        public string ValidPostalCode { get; set; } = "H3B 2Y5";
        public string InvalidPostalCode { get; set; } = "12345";
        public string ValidPhone { get; set; } = "(*************";
        public string InvalidPhone { get; set; } = "invalid";
        public string ValidEmail { get; set; } = "<EMAIL>";
        public string InvalidEmail { get; set; } = "invalid-email";
        public string JuniorBirthDate { get; set; } = "2010-05-15";
        public string MinorBirthDate { get; set; } = "2010-05-15";
        public string AdultBirthDate { get; set; } = "1990-03-20";
        public PersonTestData ValidJunior { get; set; } = new();
        public PersonTestData ValidAdult { get; set; } = new();
        public string TestEmailPrefix { get; set; } = "test";
        public string TestEmailDomain { get; set; } = "parahockey.test";
    }

    public class PersonTestData
    {
        public string FirstName { get; set; } = string.Empty;
        public string LastName { get; set; } = string.Empty;
        public string DateOfBirth { get; set; } = string.Empty;
        public string Gender { get; set; } = string.Empty;
        public string Address { get; set; } = string.Empty;
        public string City { get; set; } = string.Empty;
        public string Province { get; set; } = string.Empty;
        public string PostalCode { get; set; } = string.Empty;
        public string Phone { get; set; } = string.Empty;
        public string PhoneType { get; set; } = string.Empty;
        public string Email { get; set; } = string.Empty;
        public string RegistrationType { get; set; } = string.Empty;
        public ParentTestData Parent1 { get; set; } = new();
        public ParentTestData Parent2 { get; set; } = new();
        public EmergencyContactTestData EmergencyContact { get; set; } = new();
    }

    public class ParentTestData
    {
        public string FirstName { get; set; } = string.Empty;
        public string LastName { get; set; } = string.Empty;
        public string Phone { get; set; } = string.Empty;
        public string PhoneType { get; set; } = string.Empty;
        public string Email { get; set; } = string.Empty;
    }

    public class EmergencyContactTestData
    {
        public string FirstName { get; set; } = string.Empty;
        public string LastName { get; set; } = string.Empty;
        public string Phone { get; set; } = string.Empty;
        public string PhoneType { get; set; } = string.Empty;
        public string Email { get; set; } = string.Empty;
        public string Relationship { get; set; } = string.Empty;
    }

    public class InvalidTestData
    {
        public string TooLongString { get; set; } = new string('A', 500);
        public string InvalidEmail { get; set; } = "invalid-email";
        public string InvalidPostalCode { get; set; } = "INVALID";
        public string InvalidPhone { get; set; } = "123";
        public string InvalidDate { get; set; } = "invalid-date";
        public string EmptyString { get; set; } = "";
        public string SpecialCharacters { get; set; } = "<script>alert('test')</script>";
    }
}