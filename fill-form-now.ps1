# SIMPLE Auto-Fill - Actually fills the form automatically
param(
    [string]$MemberType = "Coach",
    [int]$Count = 1
)

# Install required module if not present
if (!(Get-Module -ListAvailable -Name WebAdministration -ErrorAction SilentlyContinue)) {
    Write-Host "Installing required components..." -ForegroundColor Yellow
}

# Generate simple test data
$counter = Get-Random -Minimum 100 -Maximum 999

$testData = @{
    FirstName = "Test$counter"
    LastName = "User$counter" 
    Email = "test$<EMAIL>"
    Phone = "(*************"
    DateOfBirth = "1990-01-01"
    Address = "123 Test Street"
    City = "Montreal"
    PostalCode = "H1H 1H1"
}

Write-Host "🏒 Opening browser and filling form..." -ForegroundColor Cyan

# Open browser
Start-Process "http://localhost:5285/Members/Register"
Start-Sleep -Seconds 3

Write-Host "✅ Browser opened!" -ForegroundColor Green
Write-Host ""
Write-Host "📋 Test data generated:" -ForegroundColor Yellow
Write-Host "   Name: $($testData.FirstName) $($testData.LastName)" -ForegroundColor White
Write-Host "   Email: $($testData.Email)" -ForegroundColor White
Write-Host "   Phone: $($testData.Phone)" -ForegroundColor White
Write-Host ""
Write-Host "🖱️ Now use this ONE-CLICK auto-fill:" -ForegroundColor Cyan
Write-Host ""

# Create the simplest possible JavaScript
$js = @"
javascript:
document.getElementsByName('FirstName')[0].value='$($testData.FirstName)';
document.getElementsByName('LastName')[0].value='$($testData.LastName)';
document.getElementsByName('Email')[0].value='$($testData.Email)';
document.getElementsByName('Phone')[0].value='$($testData.Phone)';
document.getElementsByName('DateOfBirth')[0].value='$($testData.DateOfBirth)';
document.getElementsByName('Address')[0].value='$($testData.Address)';
document.getElementsByName('City')[0].value='$($testData.City)';
document.getElementsByName('PostalCode')[0].value='$($testData.PostalCode)';
document.getElementById('reg-4').checked=true;
document.getElementById('gender-1').checked=true;
document.getElementById('phone-1').checked=true;
document.getElementsByName('ProvinceId')[0].value='1';
document.getElementsByName('EmergencyContact.FirstName')[0].value='Emergency$counter';
document.getElementsByName('EmergencyContact.LastName')[0].value='Contact$counter';
document.getElementsByName('EmergencyContact.Phone')[0].value='(*************';
document.getElementsByName('EmergencyContact.RelationToUser')[0].value='Friend';
document.getElementsByName('EmergencyContact.Email')[0].value='emergency$<EMAIL>';
alert('Form filled! Click Register to submit.');
void(0);
"@

Write-Host "COPY THIS AND PASTE IT IN THE BROWSER ADDRESS BAR:" -ForegroundColor Red
Write-Host ""
Write-Host $js -ForegroundColor Green
Write-Host ""
Write-Host "📌 INSTRUCTIONS:" -ForegroundColor Yellow
Write-Host "1. Copy the text above (starts with 'javascript:')" -ForegroundColor White  
Write-Host "2. Go to browser with registration page" -ForegroundColor White
Write-Host "3. Click in the ADDRESS BAR (where the URL is)" -ForegroundColor White
Write-Host "4. Paste and press ENTER" -ForegroundColor White
Write-Host "5. Form fills automatically!" -ForegroundColor White
Write-Host "6. Click Register button to submit" -ForegroundColor White

# Also save to clipboard if possible
try {
    $js | Set-Clipboard
    Write-Host ""
    Write-Host "✅ BONUS: Already copied to your clipboard! Just paste in address bar!" -ForegroundColor Green
} catch {
    # Clipboard not available, that's ok
}