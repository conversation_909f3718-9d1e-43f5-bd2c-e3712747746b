/**
 * Enhanced Form Validation System
 * Provides unified client-side validation with accessibility features
 */

class EnhancedFormValidator {
    constructor(formSelector, options = {}) {
        this.form = document.querySelector(formSelector);
        this.options = {
            validateOnBlur: true,
            validateOnInput: true,
            showValidationSummary: true,
            culture: 'fr-CA',
            ...options
        };
        
        this.validators = new Map();
        this.errors = new Map();
        
        if (this.form) {
            this.init();
        }
    }

    init() {
        this.setupEventListeners();
        this.registerValidators();
        this.setupAccessibility();
        this.setupAutoFormatting();
    }

    setupEventListeners() {
        // Form submission
        this.form.addEventListener('submit', (e) => {
            if (!this.validateForm()) {
                e.preventDefault();
                this.focusFirstError();
            }
        });

        // Field validation on blur/input
        const fields = this.form.querySelectorAll('input, select, textarea');
        fields.forEach(field => {
            if (this.options.validateOnBlur) {
                field.addEventListener('blur', () => this.validateField(field));
            }
            
            if (this.options.validateOnInput) {
                field.addEventListener('input', () => {
                    // Debounce input validation
                    clearTimeout(field.validationTimeout);
                    field.validationTimeout = setTimeout(() => {
                        this.validateField(field);
                    }, 300);
                });
            }
        });
    }

    registerValidators() {
        // Required field validator
        this.validators.set('required', (field, value) => {
            if (!value || value.trim() === '') {
                return this.getLocalizedMessage('ValidationRequired', field.name);
            }
            return null;
        });

        // Email validator
        this.validators.set('email', (field, value) => {
            if (value && !this.isValidEmail(value)) {
                return this.getLocalizedMessage('ValidationEmailSpecific');
            }
            return null;
        });

        // Phone validator
        this.validators.set('phone', (field, value) => {
            if (value && !this.isValidPhone(value)) {
                return this.getLocalizedMessage('ValidationPhoneSpecific');
            }
            return null;
        });

        // Postal code validator
        this.validators.set('postalcode', (field, value) => {
            if (value && !this.isValidPostalCode(value)) {
                return this.getLocalizedMessage('ValidationPostalCodeSpecific');
            }
            return null;
        });

        // Date validator
        this.validators.set('date', (field, value) => {
            if (value && !this.isValidDate(value)) {
                return this.getLocalizedMessage('ValidationDateSpecific');
            }
            return null;
        });

        // Birth date validator
        this.validators.set('birthdate', (field, value) => {
            if (value) {
                const date = new Date(value);
                const today = new Date();
                
                if (date > today) {
                    return this.getLocalizedMessage('ValidationDateFuture');
                }
                
                const minDate = new Date();
                minDate.setFullYear(today.getFullYear() - 120);
                
                if (date < minDate) {
                    return this.getLocalizedMessage('ValidationDateTooOld');
                }
            }
            return null;
        });

        // String length validator
        this.validators.set('length', (field, value, min, max) => {
            if (value) {
                if (min && value.length < min) {
                    return this.getLocalizedMessage('ValidationNameTooShort');
                }
                if (max && value.length > max) {
                    return this.getLocalizedMessage('ValidationNameTooLong');
                }
            }
            return null;
        });
    }

    validateForm() {
        const fields = this.form.querySelectorAll('input, select, textarea');
        let isValid = true;
        this.errors.clear();

        fields.forEach(field => {
            if (!this.validateField(field)) {
                isValid = false;
            }
        });

        this.updateValidationSummary();
        return isValid;
    }

    validateField(field) {
        const value = field.value;
        const fieldName = field.name;
        let errors = [];

        // Get validation rules from data attributes
        const rules = this.getValidationRules(field);

        // Run validators
        for (const [rule, params] of rules) {
            if (this.validators.has(rule)) {
                const error = this.validators.get(rule)(field, value, ...params);
                if (error) {
                    errors.push(error);
                }
            }
        }

        // Update field state
        this.updateFieldState(field, errors);
        
        if (errors.length > 0) {
            this.errors.set(fieldName, errors);
            return false;
        } else {
            this.errors.delete(fieldName);
            return true;
        }
    }

    getValidationRules(field) {
        const rules = new Map();

        // Required
        if (field.hasAttribute('required') || field.dataset.valRequired) {
            rules.set('required', []);
        }

        // Email
        if (field.type === 'email' || field.dataset.valEmail) {
            rules.set('email', []);
        }

        // Phone
        if (field.type === 'tel' || field.dataset.valPhone) {
            rules.set('phone', []);
        }

        // Postal code (based on name or pattern)
        if (field.name.toLowerCase().includes('postal') || 
            field.pattern === '^[ABCEGHJ-NPRSTVXY]\\d[ABCEGHJ-NPRSTV-Z][ -]?\\d[ABCEGHJ-NPRSTV-Z]\\d$') {
            rules.set('postalcode', []);
        }

        // Date
        if (field.type === 'date' || field.dataset.valDate) {
            rules.set('date', []);
        }

        // Birth date
        if (field.name.toLowerCase().includes('birth') || field.name.toLowerCase().includes('dateofbirth')) {
            rules.set('birthdate', []);
        }

        // Length
        const minLength = field.dataset.valLengthMin || field.minLength;
        const maxLength = field.dataset.valLengthMax || field.maxLength;
        if (minLength || maxLength) {
            rules.set('length', [parseInt(minLength) || 0, parseInt(maxLength) || 0]);
        }

        return rules;
    }

    updateFieldState(field, errors) {
        const errorContainer = document.getElementById(`error-${field.name}`);
        const hasErrors = errors.length > 0;

        // Update field classes
        field.classList.toggle('is-invalid', hasErrors);
        field.classList.toggle('is-valid', !hasErrors && field.value.trim() !== '');

        // Update ARIA attributes
        field.setAttribute('aria-invalid', hasErrors.toString());

        // Update error container
        if (errorContainer) {
            if (hasErrors) {
                errorContainer.textContent = errors[0]; // Show first error
                errorContainer.style.display = 'block';
            } else {
                errorContainer.textContent = '';
                errorContainer.style.display = 'none';
            }
        }
    }

    updateValidationSummary() {
        if (!this.options.showValidationSummary) return;

        const summary = this.form.querySelector('.validation-summary');
        if (!summary) return;

        const errorList = summary.querySelector('.validation-errors');
        if (!errorList) return;

        // Clear existing errors
        errorList.innerHTML = '';

        if (this.errors.size > 0) {
            // Show summary
            summary.style.display = 'block';
            summary.setAttribute('aria-hidden', 'false');

            // Add errors to list
            for (const [field, fieldErrors] of this.errors) {
                fieldErrors.forEach(error => {
                    const li = document.createElement('li');
                    li.textContent = error;
                    errorList.appendChild(li);
                });
            }

            // Announce errors to screen readers
            this.announceErrors();
        } else {
            // Hide summary
            summary.style.display = 'none';
            summary.setAttribute('aria-hidden', 'true');
        }
    }

    focusFirstError() {
        const firstErrorField = this.form.querySelector('.is-invalid');
        if (firstErrorField) {
            firstErrorField.focus();
            firstErrorField.scrollIntoView({ behavior: 'smooth', block: 'center' });
        }
    }

    announceErrors() {
        const errorCount = Array.from(this.errors.values()).flat().length;
        const message = this.getLocalizedMessage('ValidationErrorsFound', errorCount);
        
        // Create or update live region for screen readers
        let liveRegion = document.getElementById('validation-live-region');
        if (!liveRegion) {
            liveRegion = document.createElement('div');
            liveRegion.id = 'validation-live-region';
            liveRegion.setAttribute('aria-live', 'polite');
            liveRegion.setAttribute('aria-atomic', 'true');
            liveRegion.style.position = 'absolute';
            liveRegion.style.left = '-10000px';
            liveRegion.style.width = '1px';
            liveRegion.style.height = '1px';
            liveRegion.style.overflow = 'hidden';
            document.body.appendChild(liveRegion);
        }
        
        liveRegion.textContent = message;
    }

    setupAccessibility() {
        // Add form role if not present
        if (!this.form.hasAttribute('role')) {
            this.form.setAttribute('role', 'form');
        }

        // Ensure all form fields have proper labels
        const fields = this.form.querySelectorAll('input, select, textarea');
        fields.forEach(field => {
            if (!field.hasAttribute('aria-label') && !field.hasAttribute('aria-labelledby')) {
                const label = this.form.querySelector(`label[for="${field.id}"]`);
                if (label) {
                    field.setAttribute('aria-labelledby', label.id || `label-${field.id}`);
                    if (!label.id) {
                        label.id = `label-${field.id}`;
                    }
                }
            }
        });
    }

    // Validation helper methods
    isValidEmail(email) {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return emailRegex.test(email);
    }

    isValidPhone(phone) {
        const phoneRegex = /^[\+]?[1-9][\d]{0,15}$/;
        const digits = phone.replace(/\D/g, '');
        return digits.length >= 10 && digits.length <= 15;
    }

    isValidPostalCode(postalCode) {
        const canadianPostalRegex = /^[ABCEGHJ-NPRSTVXY]\d[ABCEGHJ-NPRSTV-Z][ -]?\d[ABCEGHJ-NPRSTV-Z]\d$/i;
        return canadianPostalRegex.test(postalCode);
    }

    isValidDate(dateString) {
        const date = new Date(dateString);
        return date instanceof Date && !isNaN(date);
    }

    getLocalizedMessage(key, ...params) {
        // Get culture from document or default to French
        const culture = document.documentElement.lang || 'fr-CA';
        
        // French messages
        const frenchMessages = {
            'ValidationRequired': `Le champ ${params[0] || 'field'} est requis.`,
            'ValidationEmailSpecific': 'Veuillez entrer une adresse courriel valide (ex: <EMAIL>)',
            'ValidationPhoneSpecific': 'Veuillez entrer un numéro de téléphone à 10 chiffres',
            'ValidationPostalCodeSpecific': 'Veuillez entrer un code postal canadien valide (ex: H1H 1H1)',
            'ValidationDateSpecific': 'Veuillez entrer une date valide au format AAAA-MM-JJ',
            'ValidationDateFuture': 'La date de naissance ne peut pas être dans le futur',
            'ValidationDateTooOld': 'Veuillez entrer une date de naissance valide',
            'ValidationNameTooShort': 'Le nom doit contenir au moins 2 caractères',
            'ValidationNameTooLong': 'Le nom ne peut pas dépasser 50 caractères',
            'ValidationErrorsFound': `${params[0]} erreur(s) de validation trouvée(s). Veuillez les corriger avant de soumettre.`
        };

        // English messages
        const englishMessages = {
            'ValidationRequired': `The ${params[0] || 'field'} field is required.`,
            'ValidationEmailSpecific': 'Please enter a valid email address (e.g., <EMAIL>)',
            'ValidationPhoneSpecific': 'Please enter a 10-digit phone number',
            'ValidationPostalCodeSpecific': 'Please enter a valid Canadian postal code (e.g., A1A 1A1)',
            'ValidationDateSpecific': 'Please enter a valid date in YYYY-MM-DD format',
            'ValidationDateFuture': 'Date of birth cannot be in the future',
            'ValidationDateTooOld': 'Please enter a valid birth date',
            'ValidationNameTooShort': 'Name must be at least 2 characters long',
            'ValidationNameTooLong': 'Name cannot exceed 50 characters',
            'ValidationErrorsFound': `${params[0]} validation error(s) found. Please correct them before submitting.`
        };

        const messages = culture.startsWith('fr') ? frenchMessages : englishMessages;
        return messages[key] || `Validation error: ${key}`;
    }

    // Public methods for external use
    addCustomValidator(name, validatorFunction) {
        this.validators.set(name, validatorFunction);
    }

    removeValidator(name) {
        this.validators.delete(name);
    }

    clearErrors() {
        this.errors.clear();
        const fields = this.form.querySelectorAll('.is-invalid, .is-valid');
        fields.forEach(field => {
            field.classList.remove('is-invalid', 'is-valid');
            field.setAttribute('aria-invalid', 'false');
        });
        this.updateValidationSummary();
    }

    getErrors() {
        return new Map(this.errors);
    }
}

    // Culture-aware formatting methods
    formatPhoneNumber(value) {
        if (!value) return '';
        
        // Remove all non-digits
        const digits = value.replace(/\D/g, '');
        
        // Format as (XXX) XXX-XXXX for 10 digits
        if (digits.length === 10) {
            return `(${digits.slice(0, 3)}) ${digits.slice(3, 6)}-${digits.slice(6)}`;
        }
        
        // Format as +1 (XXX) XXX-XXXX for 11 digits starting with 1
        if (digits.length === 11 && digits.startsWith('1')) {
            return `+1 (${digits.slice(1, 4)}) ${digits.slice(4, 7)}-${digits.slice(7)}`;
        }
        
        return value;
    }

    formatPostalCode(value) {
        if (!value) return '';
        
        // Remove spaces and convert to uppercase
        const cleaned = value.replace(/\s/g, '').toUpperCase();
        
        // Format as A1A 1A1 for 6 characters
        if (cleaned.length === 6) {
            return `${cleaned.slice(0, 3)} ${cleaned.slice(3)}`;
        }
        
        return value;
    }

    formatDate(value, culture = 'fr-CA') {
        if (!value) return '';
        
        try {
            const date = new Date(value);
            if (isNaN(date.getTime())) return value;
            
            // Return in ISO format for input[type="date"]
            return date.toISOString().split('T')[0];
        } catch {
            return value;
        }
    }

    // Auto-format inputs on blur
    setupAutoFormatting() {
        const phoneFields = this.form.querySelectorAll('input[type="tel"], input[name*="phone" i]');
        phoneFields.forEach(field => {
            field.addEventListener('blur', (e) => {
                e.target.value = this.formatPhoneNumber(e.target.value);
            });
        });

        const postalFields = this.form.querySelectorAll('input[name*="postal" i]');
        postalFields.forEach(field => {
            field.addEventListener('blur', (e) => {
                e.target.value = this.formatPostalCode(e.target.value);
            });
        });
    }
}

// Auto-initialize enhanced forms
document.addEventListener('DOMContentLoaded', function() {
    const enhancedForms = document.querySelectorAll('.enhanced-form');
    enhancedForms.forEach(form => {
        const validator = new EnhancedFormValidator(`#${form.id}`, {
            culture: document.documentElement.lang || 'fr-CA'
        });
        validator.setupAutoFormatting();
    });
});

// Export for module use
if (typeof module !== 'undefined' && module.exports) {
    module.exports = EnhancedFormValidator;
}