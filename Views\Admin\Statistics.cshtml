@{
    ViewData["Title"] = Localizer["Statistics"];
}

<div class="container-fluid mt-4">
    <div class="row">
        <div class="col">
            <h2 class="text-primary">
                <i class="fas fa-chart-bar"></i> @SharedLocalizer["Statistics"]
            </h2>
            <p class="text-muted">@SharedLocalizer["ViewPlayerTeamStats"]</p>
        </div>
    </div>

    <div class="row mb-4">
        <div class="col">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-info-circle"></i> @SharedLocalizer["ComingSoon"]
                    </h5>
                </div>
                <div class="card-body">
                    <p class="lead">@ViewBag.Message</p>
                    
                    <h6>@SharedLocalizer["PlannedFeatures"]</h6>
                    <ul class="list-group list-group-flush">
                        @foreach (var feature in ViewBag.Features)
                        {
                            <li class="list-group-item">
                                <i class="fas fa-check text-success"></i> @feature
                            </li>
                        }
                    </ul>
                    
                    <div class="mt-3">
                        <a href="@Url.Action("Index", "Admin")" class="btn btn-primary">
                            <i class="fas fa-arrow-left"></i> @SharedLocalizer["BackToDashboard"]
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>