<?xml version="1.0" encoding="utf-8"?>
<root>
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <!-- English -->
  <data name="Home" xml:space="preserve">
    <value>Home</value>
  </data>
  <data name="Privacy" xml:space="preserve">
    <value>Privacy</value>
  </data>
  <data name="TestBackbone" xml:space="preserve">
    <value>Test Backbone</value>
  </data>
  <data name="Login" xml:space="preserve">
    <value>Sign In</value>
  </data>
  <data name="Logout" xml:space="preserve">
    <value>Sign Out</value>
  </data>
  <data name="Hello" xml:space="preserve">
    <value>Hello</value>
  </data>
  <data name="DevelopmentMode" xml:space="preserve">
    <value>Development Mode</value>
  </data>
  <data name="Language" xml:space="preserve">
    <value>Language</value>
  </data>
  <data name="French" xml:space="preserve">
    <value>French</value>
  </data>
  <data name="English" xml:space="preserve">
    <value>English</value>
  </data>
  <data name="Administration" xml:space="preserve">
    <value>Administration</value>
  </data>
  <data name="Environment" xml:space="preserve">
    <value>Environment</value>
  </data>
  <data name="Copyright" xml:space="preserve">
    <value>© {0} - Parahockey</value>
  </data>

  <!-- Lookup Table Translations -->
  <!-- Gender Options -->
  <data name="Gender_Male" xml:space="preserve">
    <value>Male</value>
  </data>
  <data name="Gender_Female" xml:space="preserve">
    <value>Female</value>
  </data>
  <data name="Gender_Other" xml:space="preserve">
    <value>Other</value>
  </data>

  <!-- Phone Type Options -->
  <data name="PhoneType_Mobile" xml:space="preserve">
    <value>Mobile</value>
  </data>
  <data name="PhoneType_Other" xml:space="preserve">
    <value>Other</value>
  </data>

  <!-- Province Options -->
  <data name="Province_AB" xml:space="preserve">
    <value>Alberta</value>
  </data>
  <data name="Province_BC" xml:space="preserve">
    <value>British Columbia</value>
  </data>
  <data name="Province_MB" xml:space="preserve">
    <value>Manitoba</value>
  </data>
  <data name="Province_NB" xml:space="preserve">
    <value>New Brunswick</value>
  </data>
  <data name="Province_NL" xml:space="preserve">
    <value>Newfoundland and Labrador</value>
  </data>
  <data name="Province_NS" xml:space="preserve">
    <value>Nova Scotia</value>
  </data>
  <data name="Province_NT" xml:space="preserve">
    <value>Northwest Territories</value>
  </data>
  <data name="Province_NU" xml:space="preserve">
    <value>Nunavut</value>
  </data>
  <data name="Province_ON" xml:space="preserve">
    <value>Ontario</value>
  </data>
  <data name="Province_PE" xml:space="preserve">
    <value>Prince Edward Island</value>
  </data>
  <data name="Province_QC" xml:space="preserve">
    <value>Quebec</value>
  </data>
  <data name="Province_SK" xml:space="preserve">
    <value>Saskatchewan</value>
  </data>
  <data name="Province_YT" xml:space="preserve">
    <value>Yukon</value>
  </data>

  <!-- Registration Type Options -->
  <data name="RegType_Junior" xml:space="preserve">
    <value>Junior</value>
  </data>
  <data name="RegType_Development" xml:space="preserve">
    <value>Development</value>
  </data>
  <data name="RegType_Elite" xml:space="preserve">
    <value>Elite</value>
  </data>
  <data name="RegType_Coach" xml:space="preserve">
    <value>Coach</value>
  </data>
  <data name="RegType_Volunteer" xml:space="preserve">
    <value>Volunteer</value>
  </data>

  <!-- Form Labels -->
  <data name="ContactInformation" xml:space="preserve">
    <value>Contact Information</value>
  </data>
  <data name="PhoneNumber" xml:space="preserve">
    <value>Phone Number</value>
  </data>

  <!-- Home Page -->
  <data name="HomePageTitle" xml:space="preserve">
    <value>Parahockey - Home</value>
  </data>
  <data name="WelcomeTitle" xml:space="preserve">
    <value>Welcome to Parahockey</value>
  </data>
  <data name="WelcomeSubtitle" xml:space="preserve">
    <value>Join our community of passionate adapted hockey enthusiasts. Whether you're a player, coach, or volunteer, your place is here.</value>
  </data>
  <data name="RegisterNow" xml:space="preserve">
    <value>Register Now</value>
  </data>
  <data name="AdminPanel" xml:space="preserve">
    <value>Admin Panel</value>
  </data>
  <data name="AdminLogin" xml:space="preserve">
    <value>Admin Login</value>
  </data>
  <data name="CommunityTitle" xml:space="preserve">
    <value>Our Community</value>
  </data>
  <data name="ActivePlayers" xml:space="preserve">
    <value>Active Players</value>
  </data>
  <data name="Teams" xml:space="preserve">
    <value>Teams</value>
  </data>
  <data name="Coaches" xml:space="preserve">
    <value>Coaches</value>
  </data>
  <data name="Volunteers" xml:space="preserve">
    <value>Volunteers</value>
  </data>
  <data name="RegistrationTypesTitle" xml:space="preserve">
    <value>Available Registration Types</value>
  </data>
  <data name="RegistrationTypesSubtitle" xml:space="preserve">
    <value>Find your place on our team</value>
  </data>
  <data name="Junior" xml:space="preserve">
    <value>Junior</value>
  </data>
  <data name="JuniorDesc" xml:space="preserve">
    <value>Program specially designed for young players starting their journey in adapted hockey.</value>
  </data>
  <data name="Development" xml:space="preserve">
    <value>Development</value>
  </data>
  <data name="DevelopmentDesc" xml:space="preserve">
    <value>For those who wish to learn and progress in a supportive and structured environment.</value>
  </data>
  <data name="Elite" xml:space="preserve">
    <value>Elite</value>
  </data>
  <data name="EliteDesc" xml:space="preserve">
    <value>Competitive level for experienced players aiming for sports excellence.</value>
  </data>
  <data name="Coach" xml:space="preserve">
    <value>Coach</value>
  </data>
  <data name="CoachDesc" xml:space="preserve">
    <value>Join our coaching staff and share your passion for adapted hockey.</value>
  </data>
  <data name="Volunteer" xml:space="preserve">
    <value>Volunteer</value>
  </data>
  <data name="VolunteerDesc" xml:space="preserve">
    <value>Contribute to the success of our organization by offering your time and skills.</value>
  </data>
  <data name="FamilyFriends" xml:space="preserve">
    <value>Family &amp; Friends</value>
  </data>
  <data name="FamilyFriendsDesc" xml:space="preserve">
    <value>Support our players and discover the unique spirit of adapted hockey.</value>
  </data>
  <data name="CtaTitle" xml:space="preserve">
    <value>Ready to join the adventure?</value>
  </data>
  <data name="CtaSubtitle" xml:space="preserve">
    <value>Registration only takes a few minutes. Start your journey with Parahockey today.</value>
  </data>
  <data name="CtaButton" xml:space="preserve">
    <value>Start My Registration</value>
  </data>
  <!-- Registration Page -->
  <data name="RegistrationPageTitle" xml:space="preserve">
    <value>Registration - Parahockey</value>
  </data>
  <data name="EditProfilePageTitle" xml:space="preserve">
    <value>Edit Profile - Parahockey</value>
  </data>
  <data name="EditProfileTitle" xml:space="preserve">
    <value>Edit Profile</value>
  </data>
  <data name="UpdateProfileButton" xml:space="preserve">
    <value>Update Profile</value>
  </data>
  <data name="RegistrationFormTitle" xml:space="preserve">
    <value>Registration Form</value>
  </data>
  <data name="AlreadyRegistered" xml:space="preserve">
    <value>Already registered?</value>
  </data>
  <data name="ClickHereLink" xml:space="preserve">
    <value>Click here</value>
  </data>
  <data name="LoginLink" xml:space="preserve">
    <value>Login</value>
  </data>
  <data name="ValidationErrors" xml:space="preserve">
    <value>Please correct the following errors:</value>
  </data>
  <data name="BasicInformation" xml:space="preserve">
    <value>Basic Information</value>
  </data>
  <data name="FirstName" xml:space="preserve">
    <value>First Name</value>
  </data>
  <data name="FirstNamePlaceholder" xml:space="preserve">
    <value>Your first name</value>
  </data>
  <data name="LastName" xml:space="preserve">
    <value>Last Name</value>
  </data>
  <data name="LastNamePlaceholder" xml:space="preserve">
    <value>Your last name</value>
  </data>
  <data name="ThirdPersonFirstNamePlaceholder" xml:space="preserve">
    <value>Contact's first name</value>
  </data>
  <data name="ThirdPersonLastNamePlaceholder" xml:space="preserve">
    <value>Contact's last name</value>
  </data>
  <data name="ThirdPersonEmailPlaceholder" xml:space="preserve">
    <value>Contact's email address</value>
  </data>
  <data name="DateOfBirth" xml:space="preserve">
    <value>Date of Birth</value>
  </data>
  <data name="DateFormat" xml:space="preserve">
    <value>Format: YYYY-MM-DD</value>
  </data>
  <data name="Gender" xml:space="preserve">
    <value>Gender</value>
  </data>
  <data name="Male" xml:space="preserve">
    <value>Male</value>
  </data>
  <data name="Femme" xml:space="preserve">
    <value>Female</value>
  </data>
  <data name="OtherRelation" xml:space="preserve">
    <value>Other</value>
  </data>
  <data name="Address" xml:space="preserve">
    <value>Address</value>
  </data>
  <data name="StreetAddress" xml:space="preserve">
    <value>Street Address</value>
  </data>
  <data name="StreetAddressPlaceholder" xml:space="preserve">
    <value>123 Rink Avenue</value>
  </data>
  <data name="City" xml:space="preserve">
    <value>City</value>
  </data>
  <data name="CityPlaceholder" xml:space="preserve">
    <value>Montreal</value>
  </data>
  <data name="Province" xml:space="preserve">
    <value>Province</value>
  </data>
  <data name="SelectProvince" xml:space="preserve">
    <value>Select a province</value>
  </data>
  <data name="PostalCode" xml:space="preserve">
    <value>Postal Code</value>
  </data>
  <data name="PostalCodeFormat" xml:space="preserve">
    <value>Format: A1A 1A1</value>
  </data>
  <data name="Contact" xml:space="preserve">
    <value>Contact</value>
  </data>
  <data name="Phone" xml:space="preserve">
    <value>Phone</value>
  </data>
  <data name="PhonePlaceholder" xml:space="preserve">
    <value>(*************</value>
  </data>
  <data name="PhoneType" xml:space="preserve">
    <value>Type</value>
  </data>
  <data name="Mobile" xml:space="preserve">
    <value>Mobile</value>
  </data>
  <data name="Email" xml:space="preserve">
    <value>Email</value>
  </data>
  <data name="EmailPlaceholder" xml:space="preserve">
    <value><EMAIL></value>
  </data>
  <data name="RegistrationType" xml:space="preserve">
    <value>Registration Type</value>
  </data>
  <data name="JuniorSubtext" xml:space="preserve">
    <value>For young players</value>
  </data>
  <data name="DevelopmentSubtext" xml:space="preserve">
    <value>To learn and progress</value>
  </data>
  <data name="EliteSubtext" xml:space="preserve">
    <value>For experienced players</value>
  </data>
  <data name="CoachSubtext" xml:space="preserve">
    <value>To manage the teams</value>
  </data>
  <data name="VolunteerSubtext" xml:space="preserve">
    <value>To help the organization</value>
  </data>
  <data name="RegisterButton" xml:space="preserve">
    <value>Register</value>
  </data>
  <data name="ClearFormButton" xml:space="preserve">
    <value>Clear Form</value>
  </data>
  <!-- Validation Messages -->
  <data name="ValidationRequired" xml:space="preserve">
    <value>The {0} field is required.</value>
  </data>
  <data name="ValidationStringLength" xml:space="preserve">
    <value>The {0} field must be a string with a maximum length of {1}.</value>
  </data>
  <data name="ValidationPostalCode" xml:space="preserve">
    <value>Invalid postal code format (e.g., A1A 1A1).</value>
  </data>
  <data name="ValidationPhone" xml:space="preserve">
    <value>Invalid phone number format.</value>
  </data>
  <data name="ValidationEmail" xml:space="preserve">
    <value>Invalid email address format.</value>
  </data>
  <data name="ValidationEmailFormat" xml:space="preserve">
    <value>Invalid email format. Example: <EMAIL></value>
  </data>

  <!-- Modern Validation Messages -->
  <data name="ValidationEmailSpecific" xml:space="preserve">
    <value>Please enter a valid email address (e.g., <EMAIL>)</value>
  </data>
  <data name="ValidationPhoneSpecific" xml:space="preserve">
    <value>Please enter a 10-digit phone number</value>
  </data>
  <data name="ValidationPostalCodeSpecific" xml:space="preserve">
    <value>Please enter a valid Canadian postal code (e.g., A1A 1A1)</value>
  </data>
  <data name="ValidationDateSpecific" xml:space="preserve">
    <value>Please enter a valid date in YYYY-MM-DD format</value>
  </data>
  <data name="ValidationDateFuture" xml:space="preserve">
    <value>Date of birth cannot be in the future</value>
  </data>
  <data name="ValidationDateTooOld" xml:space="preserve">
    <value>Please enter a valid birth date</value>
  </data>
  <data name="ValidationNameTooShort" xml:space="preserve">
    <value>Name must be at least 2 characters long</value>
  </data>
  <data name="ValidationNameTooLong" xml:space="preserve">
    <value>Name cannot exceed 50 characters</value>
  </data>
  <data name="ValidationAddressTooShort" xml:space="preserve">
    <value>Address must be at least 5 characters long</value>
  </data>
  <data name="ValidationCityTooShort" xml:space="preserve">
    <value>City must be at least 2 characters long</value>
  </data>
  <data name="ValidationProvinceRequired" xml:space="preserve">
    <value>Please select a province</value>
  </data>
  <data name="ValidationGenderRequired" xml:space="preserve">
    <value>Please select a gender</value>
  </data>
  <data name="ValidationRegistrationTypeRequired" xml:space="preserve">
    <value>Please select a registration type</value>
  </data>
  <data name="ValidationPhoneTypeRequired" xml:space="preserve">
    <value>Please select a phone type</value>
  </data>
  
  <data name="ConfirmReset" xml:space="preserve">
    <value>Do you want to clear all form fields?</value>
  </data>
  <!-- Provinces -->
  <data name="ProvinceQC" xml:space="preserve">
    <value>Quebec (QC)</value>
  </data>
  <data name="ProvinceON" xml:space="preserve">
    <value>Ontario (ON)</value>
  </data>
  <data name="ProvinceBC" xml:space="preserve">
    <value>British Columbia (BC)</value>
  </data>
  <data name="ProvinceAB" xml:space="preserve">
    <value>Alberta (AB)</value>
  </data>
  <data name="ProvinceMB" xml:space="preserve">
    <value>Manitoba (MB)</value>
  </data>
  <data name="ProvinceSK" xml:space="preserve">
    <value>Saskatchewan (SK)</value>
  </data>
  <data name="ProvinceNS" xml:space="preserve">
    <value>Nova Scotia (NS)</value>
  </data>
  <data name="ProvinceNB" xml:space="preserve">
    <value>New Brunswick (NB)</value>
  </data>
  <data name="ProvinceNL" xml:space="preserve">
    <value>Newfoundland and Labrador (NL)</value>
  </data>
  <data name="ProvincePE" xml:space="preserve">
    <value>Prince Edward Island (PE)</value>
  </data>
  <data name="ProvinceYT" xml:space="preserve">
    <value>Yukon (YT)</value>
  </data>
  <data name="ProvinceNT" xml:space="preserve">
    <value>Northwest Territories (NT)</value>
  </data>
  <data name="ProvinceNU" xml:space="preserve">
    <value>Nunavut (NU)</value>
  </data>
  <data name="DateFormatHelper" xml:space="preserve">
    <value>Format: YYYY-MM-DD (e.g., 1995-03-15)</value>
  </data>
  <data name="DatePlaceholder" xml:space="preserve">
    <value>YYYY-MM-DD</value>
  </data>
  <data name="NoDataForReport" xml:space="preserve">
    <value>No data available for this report</value>
  </data>

  <!-- Parent Information -->
  <data name="ParentInformation" xml:space="preserve">
    <value>Parent/Guardian Information</value>
  </data>
  <data name="ParentGuardianDetails" xml:space="preserve">
    <value>Parent/Guardian Details</value>
  </data>
  <data name="SecondParentGuardianDetails" xml:space="preserve">
    <value>Second Parent/Guardian Details</value>
  </data>
  <data name="ParentType" xml:space="preserve">
    <value>Parent Type</value>
  </data>
  <data name="SelectParentType" xml:space="preserve">
    <value>Select Parent Type</value>
  </data>
  <data name="Mother" xml:space="preserve">
    <value>Mother</value>
  </data>
  <data name="Father" xml:space="preserve">
    <value>Father</value>
  </data>
  <data name="Guardian" xml:space="preserve">
    <value>Guardian</value>
  </data>

  <!-- Emergency Contact Information -->
  <data name="EmergencyContactInformation" xml:space="preserve">
    <value>Emergency Contact Information</value>
  </data>
  <data name="EmergencyContactDetails" xml:space="preserve">
    <value>Emergency Contact Details</value>
  </data>
  <data name="RelationToUser" xml:space="preserve">
    <value>Relationship to Member</value>
  </data>
  <data name="SelectRelation" xml:space="preserve">
    <value>Select Relationship</value>
  </data>
  <data name="Spouse" xml:space="preserve">
    <value>Spouse</value>
  </data>
  <data name="Parent" xml:space="preserve">
    <value>Parent</value>
  </data>
  <data name="Sibling" xml:space="preserve">
    <value>Sibling</value>
  </data>
  <data name="Friend" xml:space="preserve">
    <value>Friend</value>
  </data>
  <data name="ValidPostalCode" xml:space="preserve">
    <value>Please enter a valid postal code (A1A 1A1)</value>
  </data>

  <!-- Login Page -->
  <data name="LoginPageTitle" xml:space="preserve">
    <value>Member Search - Parahockey</value>
  </data>
  <data name="SearchForMember" xml:space="preserve">
    <value>Search for a Member</value>
  </data>
  <data name="SearchButton" xml:space="preserve">
    <value>Search</value>
  </data>
  <data name="SearchResults" xml:space="preserve">
    <value>Search Results</value>
  </data>
  <data name="NoResultsFound" xml:space="preserve">
    <value>No members found matching your criteria. If your account has been deactivated, please contact administration.</value>
  </data>
  <data name="WrongEmail" xml:space="preserve">
    <value>Wrong email</value>
  </data>
  <data name="SendCodeButton" xml:space="preserve">
    <value>Send Code</value>
  </data>
  <data name="EmailVerification" xml:space="preserve">
    <value>Email Verification</value>
  </data>
  <data name="VerificationCodeSent" xml:space="preserve">
    <value>A verification code has been sent to your email address.</value>
  </data>
  <data name="EnterCode" xml:space="preserve">
    <value>Enter Code</value>
  </data>
  <data name="VerifyButton" xml:space="preserve">
    <value>Verify</value>
  </data>
  
  <!-- Azure AD Admin Users -->
  <data name="AdminUsers" xml:space="preserve">
    <value>Admin Users</value>
  </data>
  <data name="AzureADInfo" xml:space="preserve">
    <value>Azure AD Information</value>
  </data>
  <data name="AzureADDescription" xml:space="preserve">
    <value>Admin access uses Azure AD authentication. Only users who are members of the configured admin group can access these features.</value>
  </data>
  
  <!-- Enhanced Form Validation Messages -->
  <data name="SelectGender" xml:space="preserve">
    <value>Select Gender</value>
  </data>
  <data name="SelectPhoneType" xml:space="preserve">
    <value>Select Phone Type</value>
  </data>
  <data name="SelectRegistrationType" xml:space="preserve">
    <value>Select Registration Type</value>
  </data>
  <data name="ParentInfoRequired" xml:space="preserve">
    <value>Parent/guardian information is required for members under 18 years of age.</value>
  </data>
  <data name="EmergencyContactOptional" xml:space="preserve">
    <value>Emergency contact information is optional but recommended.</value>
  </data>
  <data name="MemberRegistrationSuccess" xml:space="preserve">
    <value>Registration successful for {0} {1}!</value>
  </data>
  <data name="RegistrationSuccess" xml:space="preserve">
    <value>Registration Successful</value>
  </data>
  <data name="RegistrationSuccessTitle" xml:space="preserve">
    <value>Welcome to Parahockey!</value>
  </data>
  <data name="RegistrationSuccessMessage" xml:space="preserve">
    <value>Thank you {0} {1}, your registration has been completed successfully.</value>
  </data>
  <data name="NextStepEmail" xml:space="preserve">
    <value>Check your email for a welcome message and important information</value>
  </data>
  <data name="NextStepEvents" xml:space="preserve">
    <value>Browse upcoming events and register for activities</value>
  </data>
  <data name="NextStepProfile" xml:space="preserve">
    <value>Complete your profile with additional information</value>
  </data>
  <data name="ViewEvents" xml:space="preserve">
    <value>View Events</value>
  </data>
  <data name="AutoRedirectConfirm" xml:space="preserve">
    <value>Would you like to return to the home page?</value>
  </data>
  <data name="RegistrationFormSubtitle" xml:space="preserve">
    <value>Join our community of adapted hockey enthusiasts</value>
  </data>
  <data name="CurrentUserInfo" xml:space="preserve">
    <value>Current User Information</value>
  </data>
  <data name="Username" xml:space="preserve">
    <value>Username</value>
  </data>
  <data name="AdminGroupId" xml:space="preserve">
    <value>Admin Group ID</value>
  </data>
  <data name="UserGroups" xml:space="preserve">
    <value>User Groups</value>
  </data>
  <data name="AdminAccess" xml:space="preserve">
    <value>Admin Access</value>
  </data>
  <data name="NoGroups" xml:space="preserve">
    <value>No Groups</value>
  </data>
  <data name="ConfigurationSteps" xml:space="preserve">
    <value>Configuration Steps</value>
  </data>
  <data name="AzureADStep1" xml:space="preserve">
    <value>Register the application in Azure AD</value>
  </data>
  <data name="AzureADStep2" xml:space="preserve">
    <value>Configure values in appsettings.json</value>
  </data>
  <data name="AzureADStep3" xml:space="preserve">
    <value>Create an Azure AD admin group</value>
  </data>
  <data name="AzureADStep4" xml:space="preserve">
    <value>Add users to the admin group</value>
  </data>
  <data name="AzureADStep5" xml:space="preserve">
    <value>Configure the group ID in appsettings.json</value>
  </data>
  <data name="AzureADWarning" xml:space="preserve">
    <value>Warning: The Azure AD values in appsettings.json are currently placeholder values. They must be configured with your actual Azure AD values.</value>
  </data>
  <data name="BackToDashboard" xml:space="preserve">
    <value>Back to Dashboard</value>
  </data>

  <!-- Calendar & Events -->
  <data name="CalendarEvents" xml:space="preserve">
    <value>Calendar &amp; Events</value>
  </data>
  <data name="CalendarEventsDescription" xml:space="preserve">
    <value>Manage events and member registrations</value>
  </data>
  <data name="ComingSoon" xml:space="preserve">
    <value>Coming Soon</value>
  </data>
  <data name="PlannedFeatures" xml:space="preserve">
    <value>Planned Features:</value>
  </data>
  <data name="CreateManageEvents" xml:space="preserve">
    <value>Create and manage events</value>
  </data>
  <data name="MemberEventSubscriptions" xml:space="preserve">
    <value>Member event subscriptions</value>
  </data>
  <data name="SeasonBasedEventOrganization" xml:space="preserve">
    <value>Season-based event organization</value>
  </data>
  <data name="EventCapacityManagement" xml:space="preserve">
    <value>Event capacity management</value>
  </data>
  <data name="CalendarFunctionalityMessage" xml:space="preserve">
    <value>Calendar &amp; Events functionality will be implemented here.</value>
  </data>

  <!-- Teams Management -->
  <data name="TeamsManagement" xml:space="preserve">
    <value>Teams Management</value>
  </data>
  <data name="TeamsManagementDescription" xml:space="preserve">
    <value>Manage teams, players and coaches</value>
  </data>
  <data name="CreateManageTeams" xml:space="preserve">
    <value>Create and manage teams</value>
  </data>
  <data name="AssignPlayersCoaches" xml:space="preserve">
    <value>Assign players and coaches</value>
  </data>
  <data name="TeamRosterManagement" xml:space="preserve">
    <value>Team roster management</value>
  </data>
  <data name="SeasonBasedTeamOrganization" xml:space="preserve">
    <value>Season-based team organization</value>
  </data>
  <data name="TeamsManagementMessage" xml:space="preserve">
    <value>Teams Management functionality will be implemented here.</value>
  </data>

  <!-- Rankings -->
  <data name="Rankings" xml:space="preserve">
    <value>Rankings</value>
  </data>
  <data name="RankingsDescription" xml:space="preserve">
    <value>View team standings and rankings</value>
  </data>
  <data name="TeamStandingsRankings" xml:space="preserve">
    <value>Team standings and rankings</value>
  </data>
  <data name="WinLossTracking" xml:space="preserve">
    <value>Win/loss tracking</value>
  </data>
  <data name="PointsCalculation" xml:space="preserve">
    <value>Points calculation</value>
  </data>
  <data name="SeasonBasedRankings" xml:space="preserve">
    <value>Season-based rankings</value>
  </data>
  <data name="RankingsMessage" xml:space="preserve">
    <value>Rankings functionality will be implemented here.</value>
  </data>

  <!-- Statistics -->
  <data name="Statistics" xml:space="preserve">
    <value>Statistics</value>
  </data>
  <data name="StatisticsDescription" xml:space="preserve">
    <value>View player and team performance statistics</value>
  </data>
  <data name="PlayerPerformanceStatistics" xml:space="preserve">
    <value>Player performance statistics</value>
  </data>
  <data name="TeamPerformanceMetrics" xml:space="preserve">
    <value>Team performance metrics</value>
  </data>
  <data name="SeasonComparisonReports" xml:space="preserve">
    <value>Season comparison reports</value>
  </data>
  <data name="ManualStatisticsEntry" xml:space="preserve">
    <value>Manual statistics entry</value>
  </data>
  <data name="StatisticsMessage" xml:space="preserve">
    <value>Statistics functionality will be implemented here.</value>
  </data>

  <!-- Admin Management -->
  <data name="ManageAdmins" xml:space="preserve">
    <value>Manage Admins</value>
  </data>
  <data name="CurrentAdministrators" xml:space="preserve">
    <value>Current Administrators</value>
  </data>
  <data name="AddNewAdministrator" xml:space="preserve">
    <value>Add New Administrator</value>
  </data>
  <data name="YourAccount" xml:space="preserve">
    <value>Your Account</value>
  </data>
  <data name="LoggedInAs" xml:space="preserve">
    <value>Logged in as:</value>
  </data>
  <data name="Authentication" xml:space="preserve">
    <value>Authentication:</value>
  </data>
  <data name="MicrosoftAzureAD" xml:space="preserve">
    <value>Microsoft/Azure AD</value>
  </data>
  <data name="Name" xml:space="preserve">
    <value>Name</value>
  </data>
  <data name="Role" xml:space="preserve">
    <value>Role</value>
  </data>
  <data name="Added" xml:space="preserve">
    <value>Added</value>
  </data>
  <data name="MasterAdmin" xml:space="preserve">
    <value>Master Admin</value>
  </data>
  <data name="NormalAdmin" xml:space="preserve">
    <value>Normal Admin</value>
  </data>
  <data name="Disabled" xml:space="preserve">
    <value>Disabled</value>
  </data>
  <data name="CannotRemove" xml:space="preserve">
    <value>Cannot Remove</value>
  </data>
  <data name="Remove" xml:space="preserve">
    <value>Remove</value>
  </data>
  <data name="EmailAddress" xml:space="preserve">
    <value>Email Address</value>
  </data>
  <data name="FullName" xml:space="preserve">
    <value>Full Name</value>
  </data>
  <data name="AddAdmin" xml:space="preserve">
    <value>Add Admin</value>
  </data>
  <data name="NoAdministratorsFound" xml:space="preserve">
    <value>No administrators found.</value>
  </data>

  <!-- Quick Actions -->
  <data name="QuickActions" xml:space="preserve">
    <value>Quick Actions</value>
  </data>
  <data name="ViewAllMembers" xml:space="preserve">
    <value>View All Members</value>
  </data>
  <data name="AddNewMember" xml:space="preserve">
    <value>Add New Member</value>
  </data>
  <data name="SystemInformation" xml:space="preserve">
    <value>System Information</value>
  </data>
  <data name="BackToSite" xml:space="preserve">
    <value>Back to Site</value>
  </data>

  <!-- Recent Audit Activity -->
  <data name="RecentMemberAudit" xml:space="preserve">
    <value>Recent Member Audit</value>
  </data>
  <data name="RecentChanges" xml:space="preserve">
    <value>Recent Changes</value>
  </data>
  <data name="ViewAllAuditHistory" xml:space="preserve">
    <value>View All Audit History</value>
  </data>
  <data name="NoRecentActivity" xml:space="preserve">
    <value>No recent activity.</value>
  </data>
  <data name="AuditDateTime" xml:space="preserve">
    <value>Date/Time</value>
  </data>
  <data name="Action" xml:space="preserve">
    <value>Action</value>
  </data>
  <data name="PerformedBy" xml:space="preserve">
    <value>Performed By</value>
  </data>
  <data name="Description" xml:space="preserve">
    <value>Description</value>
  </data>
  <data name="Create" xml:space="preserve">
    <value>Create</value>
  </data>
  <data name="Update" xml:space="preserve">
    <value>Update</value>
  </data>
  <data name="Delete" xml:space="preserve">
    <value>Delete</value>
  </data>
  <data name="Admin" xml:space="preserve">
    <value>Admin</value>
  </data>
  <data name="Member" xml:space="preserve">
    <value>Member</value>
  </data>
  <data name="System" xml:space="preserve">
    <value>System</value>
  </data>
  <data name="RecentMemberRegistrations" xml:space="preserve">
    <value>Recent Member Registrations</value>
  </data>
  <data name="DateCreated" xml:space="preserve">
    <value>Date Created</value>
  </data>
  <data name="NoMembersRegistered" xml:space="preserve">
    <value>No members registered yet.</value>
  </data>
  <data name="EntityType" xml:space="preserve">
    <value>Entity Type</value>
  </data>
  <data name="Filters" xml:space="preserve">
    <value>Filters</value>
  </data>
  <data name="AllEntityTypes" xml:space="preserve">
    <value>All Entity Types</value>
  </data>
  <data name="AllActions" xml:space="preserve">
    <value>All Actions</value>
  </data>
  <data name="Filter" xml:space="preserve">
    <value>Filter</value>
  </data>
  <data name="Clear" xml:space="preserve">
    <value>Clear</value>
  </data>
  <data name="ShowingResults" xml:space="preserve">
    <value>Showing results {0} to {1} of {2}</value>
  </data>
  <data name="Source" xml:space="preserve">
    <value>Source</value>
  </data>
  <data name="NoDescriptionAvailable" xml:space="preserve">
    <value>No description available</value>
  </data>
  <data name="Previous" xml:space="preserve">
    <value>Previous</value>
  </data>
  <data name="Next" xml:space="preserve">
    <value>Next</value>
  </data>
  <data name="NoAuditLogsFound" xml:space="preserve">
    <value>No Audit Logs Found</value>
  </data>
  <data name="NoAuditLogsFoundDesc" xml:space="preserve">
    <value>No audit activity matches the selected filter criteria.</value>
  </data>
  <data name="ViewAllRecords" xml:space="preserve">
    <value>View All Records</value>
  </data>

  <!-- Admin Dashboard -->
  <data name="AdminDashboard" xml:space="preserve">
    <value>Admin Dashboard</value>
  </data>
  <data name="Welcome" xml:space="preserve">
    <value>Welcome</value>
  </data>
  <data name="TotalMembers" xml:space="preserve">
    <value>Total Members</value>
  </data>
  <data name="TotalParents" xml:space="preserve">
    <value>Total Parents</value>
  </data>
  <data name="EmergencyContacts" xml:space="preserve">
    <value>Emergency Contacts</value>
  </data>
  <data name="ViewAll" xml:space="preserve">
    <value>View All</value>
  </data>
  <data name="SystemInfo" xml:space="preserve">
    <value>System Info</value>
  </data>

  <!-- Access Denied -->
  <data name="AccessDenied" xml:space="preserve">
    <value>Access Denied</value>
  </data>
  <data name="NoPermissionMessage" xml:space="preserve">
    <value>You don't have permission to access this area</value>
  </data>
  <data name="OnlyAdminsMessage" xml:space="preserve">
    <value>Only administrators can access the admin panel.</value>
  </data>
  <data name="ContactAdminMessage" xml:space="preserve">
    <value>If you believe this is an error, please contact the system administrator.</value>
  </data>
  <data name="PleaseLoginMessage" xml:space="preserve">
    <value>Please log in to access the admin panel.</value>
  </data>
  <data name="ReturnToHome" xml:space="preserve">
    <value>Return to Home</value>
  </data>

  <!-- System Information -->
  <data name="EnvironmentConfiguration" xml:space="preserve">
    <value>Environment Configuration</value>
  </data>
  <data name="Theme" xml:space="preserve">
    <value>Theme</value>
  </data>
  <data name="ShowBanner" xml:space="preserve">
    <value>Show Banner</value>
  </data>
  <data name="DatabaseType" xml:space="preserve">
    <value>Database Type</value>
  </data>
  <data name="DatabaseStatus" xml:space="preserve">
    <value>Database Status</value>
  </data>
  <data name="DatabaseStatistics" xml:space="preserve">
    <value>Database Statistics</value>
  </data>
  <data name="Connected" xml:space="preserve">
    <value>Connected</value>
  </data>
  <data name="NotConnected" xml:space="preserve">
    <value>Not Connected</value>
  </data>
  <data name="Enabled" xml:space="preserve">
    <value>Enabled</value>
  </data>
  <data name="DisabledDevelopment" xml:space="preserve">
    <value>Disabled (Development)</value>
  </data>
  <data name="Yes" xml:space="preserve">
    <value>Yes</value>
  </data>
  <data name="No" xml:space="preserve">
    <value>No</value>
  </data>
  <data name="QuickActionsText" xml:space="preserve">
    <value>Quick Actions:</value>
  </data>
  <data name="AuthenticationStatus" xml:space="preserve">
    <value>Authentication Status</value>
  </data>
  <data name="UserAuthenticated" xml:space="preserve">
    <value>User Authenticated</value>
  </data>
  <data name="CurrentUser" xml:space="preserve">
    <value>Current User</value>
  </data>
  <data name="ServerTime" xml:space="preserve">
    <value>Server Time</value>
  </data>
  <data name="DevelopmentModeMessage" xml:space="preserve">
    <value>Authentication is disabled for faster development. In test/production environments, Microsoft authentication will be required to access this admin panel.</value>
  </data>
  <data name="AuthenticationRequired" xml:space="preserve">
    <value>Authentication Required</value>
  </data>
  <data name="AuthenticationError" xml:space="preserve">
    <value>You should not be able to see this page without authentication. Check your authentication configuration.</value>
  </data>
  <data name="AuthenticatedAccess" xml:space="preserve">
    <value>Authenticated Access</value>
  </data>
  <data name="AuthenticatedMessage" xml:space="preserve">
    <value>You are successfully authenticated via Microsoft account.</value>
  </data>
  <data name="BackToAdminDashboard" xml:space="preserve">
    <value>Back to Admin Dashboard</value>
  </data>

  <!-- Members Management -->
  <data name="ManageMembers" xml:space="preserve">
    <value>Manage Members</value>
  </data>
  <data name="SearchByNameOrEmail" xml:space="preserve">
    <value>Search by name or email...</value>
  </data>
  <data name="Search" xml:space="preserve">
    <value>Search</value>
  </data>
  <data name="Showing" xml:space="preserve">
    <value>Showing</value>
  </data>
  <data name="Of" xml:space="preserve">
    <value>of</value>
  </data>
  <data name="Members" xml:space="preserve">
    <value>members</value>
  </data>
  <data name="Matching" xml:space="preserve">
    <value>matching</value>
  </data>
  <data name="Page" xml:space="preserve">
    <value>Page</value>
  </data>
  <data name="RegistrationDate" xml:space="preserve">
    <value>Registration Date</value>
  </data>
  <data name="YearsOld" xml:space="preserve">
    <value>years old</value>
  </data>
  <data name="Unknown" xml:space="preserve">
    <value>Unknown</value>
  </data>
  <data name="ViewDetails" xml:space="preserve">
    <value>View Details</value>
  </data>
  <data name="Edit" xml:space="preserve">
    <value>Edit</value>
  </data>
  <data name="NoMembersFound" xml:space="preserve">
    <value>No members found matching</value>
  </data>
  <data name="TryDifferentSearch" xml:space="preserve">
    <value>Try a different search term or</value>
  </data>
  <data name="ViewAllMembersLink" xml:space="preserve">
    <value>view all members</value>
  </data>
  <data name="NoMembersRegisteredYet" xml:space="preserve">
    <value>No members registered yet</value>
  </data>
  <data name="StartByRegistering" xml:space="preserve">
    <value>Start by registering the first member.</value>
  </data>
  <data name="RegisterFirstMember" xml:space="preserve">
    <value>Register First Member</value>
  </data>
  <data name="ConfirmDelete" xml:space="preserve">
    <value>Confirm Delete</value>
  </data>
  <data name="ConfirmDeleteMessage" xml:space="preserve">
    <value>Are you sure you want to delete member</value>
  </data>
  <data name="DeleteWarning" xml:space="preserve">
    <value>This action cannot be undone and will also delete all associated parent and emergency contact information.</value>
  </data>
  <data name="Cancel" xml:space="preserve">
    <value>Cancel</value>
  </data>
  <data name="DeleteMember" xml:space="preserve">
    <value>Delete Member</value>
  </data>

  <!-- Member Details -->
  <data name="MemberDetails" xml:space="preserve">
    <value>Member Details</value>
  </data>
  <data name="EditMember" xml:space="preserve">
    <value>Edit Member</value>
  </data>
  <data name="BackToMembersList" xml:space="preserve">
    <value>Back to Members List</value>
  </data>
  <data name="PersonalInformation" xml:space="preserve">
    <value>Personal Information</value>
  </data>
  <data name="MemberID" xml:space="preserve">
    <value>Member ID</value>
  </data>
  <data name="NotSpecified" xml:space="preserve">
    <value>Not specified</value>
  </data>
  <data name="QuickStats" xml:space="preserve">
    <value>Quick Stats</value>
  </data>
  <data name="ParentsText" xml:space="preserve">
    <value>Parent(s)</value>
  </data>
  <data name="EmergencyContact" xml:space="preserve">
    <value>Emergency Contact</value>
  </data>
  <data name="AddressInformation" xml:space="preserve">
    <value>Address Information</value>
  </data>
  <data name="ParentGuardianInformation" xml:space="preserve">
    <value>Parent/Guardian Information</value>
  </data>
  <data name="Type" xml:space="preserve">
    <value>Type</value>
  </data>
  <data name="Relation" xml:space="preserve">
    <value>Relation</value>
  </data>
  <data name="AuditHistory" xml:space="preserve">
    <value>Audit History</value>
  </data>
  <data name="Created" xml:space="preserve">
    <value>Created</value>
  </data>
  <data name="By" xml:space="preserve">
    <value>by</value>
  </data>
  <data name="LastModified" xml:space="preserve">
    <value>Last Modified</value>
  </data>
  <data name="NeverModified" xml:space="preserve">
    <value>Never modified</value>
  </data>
  <data name="TotalChanges" xml:space="preserve">
    <value>Total Changes</value>
  </data>
  <data name="ChangeHistory" xml:space="preserve">
    <value>Change History</value>
  </data>
  <data name="NoAuditHistoryAvailable" xml:space="preserve">
    <value>No audit history available.</value>
  </data>
  <data name="IP" xml:space="preserve">
    <value>IP</value>
  </data>
  <data name="Active" xml:space="preserve">
    <value>Active</value>
  </data>
  <data name="Inactive" xml:space="preserve">
    <value>Inactive</value>
  </data>
  <data name="Status" xml:space="preserve">
    <value>Status</value>
  </data>

  <!-- Setup Master Admin -->
  <data name="SetupMasterAdmin" xml:space="preserve">
    <value>Setup Master Admin</value>
  </data>
  <data name="FirstTimeSetup" xml:space="preserve">
    <value>First Time Setup:</value>
  </data>
  <data name="NoAdminsExist" xml:space="preserve">
    <value>No administrators exist yet. You will become the master administrator.</value>
  </data>
  <data name="YouAreLoggedInAs" xml:space="preserve">
    <value>You are logged in as:</value>
  </data>
  <data name="Important" xml:space="preserve">
    <value>Important:</value>
  </data>
  <data name="MasterAdminCannotBeRemoved" xml:space="preserve">
    <value>As the master admin, you cannot be removed from the system. You will be able to add and remove other administrators.</value>
  </data>
  <data name="SetupMasterAdminAccount" xml:space="preserve">
    <value>Setup Master Admin Account</value>
  </data>

  <!-- Teams, Calendar, Rankings, Statistics coming soon messages -->
  <data name="ManageEventsAndSubscriptions" xml:space="preserve">
    <value>Manage events and member subscriptions</value>
  </data>
  <data name="ManageTeamsPlayersCoaches" xml:space="preserve">
    <value>Manage teams, players, and coaches</value>
  </data>
  <data name="ViewTeamStandings" xml:space="preserve">
    <value>View team standings and rankings</value>
  </data>
  <data name="ViewPlayerTeamStats" xml:space="preserve">
    <value>View player and team performance statistics</value>
  </data>

  <!-- Confirmation messages -->
  <data name="AreYouSureRemoveAdmin" xml:space="preserve">
    <value>Are you sure you want to remove this admin?</value>
  </data>

  <!-- Controller messages -->
  <data name="MemberNotFound" xml:space="preserve">
    <value>Member not found.</value>
  </data>
  <data name="MemberDeletedSuccessfully" xml:space="preserve">
    <value>Member {0} {1} has been deleted successfully.</value>
  </data>
  <data name="ErrorDeletingMember" xml:space="preserve">
    <value>An error occurred while deleting the member.</value>
  </data>
  <data name="MasterAdminCreatedSuccessfully" xml:space="preserve">
    <value>Master admin account created successfully!</value>
  </data>
  <data name="OnlyMasterAdminsCanCreate" xml:space="preserve">
    <value>Only Master Admins can create other Master Admins.</value>
  </data>
  <data name="EmailAndNameRequired" xml:space="preserve">
    <value>Email and name are required.</value>
  </data>
  <data name="AdminAlreadyExists" xml:space="preserve">
    <value>Admin already exists.</value>
  </data>
  <data name="AdminAddedSuccessfully" xml:space="preserve">
    <value>{0} {1} added successfully!</value>
  </data>
  <data name="AdminNotFound" xml:space="preserve">
    <value>Admin not found.</value>
  </data>
  <data name="CannotRemoveMasterAdmin" xml:space="preserve">
    <value>Cannot remove master admin.</value>
  </data>
  <data name="AdminRemovedSuccessfully" xml:space="preserve">
    <value>Admin {0} removed successfully!</value>
  </data>
  
  <!-- Disable/Enable Actions -->
  <data name="Disable" xml:space="preserve">
    <value>Disable</value>
  </data>
  <data name="Enable" xml:space="preserve">
    <value>Enable</value>
  </data>
  <data name="DisableMember" xml:space="preserve">
    <value>Disable Member</value>
  </data>
  <data name="EnableMember" xml:space="preserve">
    <value>Enable Member</value>
  </data>
  <data name="ConfirmDisable" xml:space="preserve">
    <value>Confirm Disable</value>
  </data>
  <data name="ConfirmEnable" xml:space="preserve">
    <value>Confirm Enable</value>
  </data>
  <data name="ConfirmDisableMessage" xml:space="preserve">
    <value>Are you sure you want to disable</value>
  </data>
  <data name="ConfirmEnableMessage" xml:space="preserve">
    <value>Are you sure you want to enable</value>
  </data>
  <data name="DisableWarning" xml:space="preserve">
    <value>The member will no longer be able to login or be displayed in active lists.</value>
  </data>
  <data name="EnableWarning" xml:space="preserve">
    <value>The member will be able to login again and be displayed in active lists.</value>
  </data>
  <data name="MemberDisabledSuccessfully" xml:space="preserve">
    <value>Member {0} {1} has been disabled successfully.</value>
  </data>
  <data name="MemberEnabledSuccessfully" xml:space="preserve">
    <value>Member {0} {1} has been enabled successfully.</value>
  </data>
  <data name="ErrorDisablingMember" xml:space="preserve">
    <value>Error disabling member.</value>
  </data>
  <data name="ErrorEnablingMember" xml:space="preserve">
    <value>Error enabling member.</value>
  </data>
  <data name="EventCalendar" xml:space="preserve">
    <value>Event Calendar</value>
  </data>
  <data name="AddEvent" xml:space="preserve">
    <value>Add Event</value>
  </data>
  <data name="AllEvents" xml:space="preserve">
    <value>All Events</value>
  </data>
  <data name="TotalEvents" xml:space="preserve">
    <value>Total Events</value>
  </data>
  <data name="PublishedEvents" xml:space="preserve">
    <value>Published Events</value>
  </data>
  <data name="UpcomingEvents" xml:space="preserve">
    <value>Upcoming Events</value>
  </data>
  <data name="NoUpcomingEvents" xml:space="preserve">
    <value>No upcoming events</value>
  </data>
  <data name="EventCategories" xml:space="preserve">
    <value>Event Categories</value>
  </data>
  <data name="EventDetails" xml:space="preserve">
    <value>Event Details</value>
  </data>
  <data name="ViewEventDetails" xml:space="preserve">
    <value>View event details</value>
  </data>
  <data name="StartDate" xml:space="preserve">
    <value>Start Date</value>
  </data>
  <data name="EndDate" xml:space="preserve">
    <value>End Date</value>
  </data>
  <data name="MaxParticipants" xml:space="preserve">
    <value>Max Participants</value>
  </data>
  <data name="MaxParticipantsHelp" xml:space="preserve">
    <value>Leave empty for no limit</value>
  </data>
  <data name="UnlimitedParticipants" xml:space="preserve">
    <value>Unlimited participants</value>
  </data>
  <data name="AllDayEvent" xml:space="preserve">
    <value>All Day Event</value>
  </data>
  <data name="RequiresRegistration" xml:space="preserve">
    <value>Requires Registration</value>
  </data>
  <data name="ContactPerson" xml:space="preserve">
    <value>Contact Person</value>
  </data>
  <data name="ContactEmail" xml:space="preserve">
    <value>Contact Email</value>
  </data>
  <data name="ContactPhone" xml:space="preserve">
    <value>Contact Phone</value>
  </data>
  <data name="OrganizerNotes" xml:space="preserve">
    <value>Organizer Notes</value>
  </data>
  <data name="PublishEvent" xml:space="preserve">
    <value>Publish Event</value>
  </data>
  <data name="ConfirmDeleteEvent" xml:space="preserve">
    <value>Are you sure you want to delete this event?</value>
  </data>
  <data name="EventCategory_Practice" xml:space="preserve">
    <value>Practice</value>
  </data>
  <data name="EventCategory_Practice_Desc" xml:space="preserve">
    <value>Training and practice sessions</value>
  </data>
  <data name="EventCategory_Game" xml:space="preserve">
    <value>Game</value>
  </data>
  <data name="EventCategory_Game_Desc" xml:space="preserve">
    <value>Games and competitions</value>
  </data>
  <data name="EventCategory_Tournament" xml:space="preserve">
    <value>Tournament</value>
  </data>
  <data name="EventCategory_Tournament_Desc" xml:space="preserve">
    <value>Tournaments and championships</value>
  </data>
  <data name="EventCategory_PratiqueFirstShift" xml:space="preserve">
    <value>Practice First Shift</value>
  </data>
  <data name="EventCategory_PratiqueFirstShift_Desc" xml:space="preserve">
    <value>Practice sessions dedicated to the First Shift program</value>
  </data>
  <data name="EventCategory_Training" xml:space="preserve">
    <value>Training</value>
  </data>
  <data name="EventCategory_Training_Desc" xml:space="preserve">
    <value>Training and workshops</value>
  </data>
  <data name="EventCategory_Meeting" xml:space="preserve">
    <value>Meeting</value>
  </data>
  <data name="EventCategory_Meeting_Desc" xml:space="preserve">
    <value>Meetings and assemblies</value>
  </data>
  <data name="EventCategory_Social" xml:space="preserve">
    <value>Social</value>
  </data>
  <data name="EventCategory_Social_Desc" xml:space="preserve">
    <value>Social events and celebrations</value>
  </data>
  <data name="EventCategory_Fundraiser" xml:space="preserve">
    <value>Fundraiser</value>
  </data>
  <data name="EventCategory_Fundraiser_Desc" xml:space="preserve">
    <value>Fundraising events</value>
  </data>
  <data name="EventCategory_Other" xml:space="preserve">
    <value>Other</value>
  </data>
  <data name="EventCategory_Other_Desc" xml:space="preserve">
    <value>Other events</value>
  </data>
  <data name="EventCategory_Tentative" xml:space="preserve">
    <value>Tentative</value>
  </data>
  <data name="EventCategory_Tentative_Desc" xml:space="preserve">
    <value>Tentative and to be confirmed events</value>
  </data>
  <data name="EventCategory_FirstShift" xml:space="preserve">
    <value>First Shift</value>
  </data>
  <data name="EventCategory_FirstShift_Desc" xml:space="preserve">
    <value>First Shift events</value>
  </data>
  <data name="EventCategory_Camp" xml:space="preserve">
    <value>Camp</value>
  </data>
  <data name="EventCategory_Camp_Desc" xml:space="preserve">
    <value>Training and development camps</value>
  </data>
  <data name="EventCategory_Serie" xml:space="preserve">
    <value>Series</value>
  </data>
  <data name="EventCategory_Serie_Desc" xml:space="preserve">
    <value>Series of games or events</value>
  </data>
  <data name="EventCategory_Tentatif" xml:space="preserve">
    <value>Tentative</value>
  </data>
  <data name="EventCategory_Tentatif_Desc" xml:space="preserve">
    <value>Tentative and to be confirmed events</value>
  </data>
  
  <!-- Public Calendar Keys -->
  <data name="ViewCalendar" xml:space="preserve">
    <value>View Calendar</value>
  </data>
  <data name="ViewPublicEventsAndSchedule" xml:space="preserve">
    <value>Browse public events and schedules.</value>
  </data>
  <data name="BackToHome" xml:space="preserve">
    <value>Back to Home</value>
  </data>
  <data name="LoginRequired" xml:space="preserve">
    <value>Login Required</value>
  </data>
  <data name="MembershipRequired" xml:space="preserve">
    <value>Membership Required</value>
  </data>
  <data name="LoginRequiredMessage" xml:space="preserve">
    <value>To view event details and register for events, you must be logged in as a member.</value>
  </data>
  <data name="MemberBenefitsText" xml:space="preserve">
    <value>Members can register for events, manage their profile, and access exclusive features.</value>
  </data>
  <data name="ErrorLoadingCalendar" xml:space="preserve">
    <value>Error loading calendar</value>
  </data>

  <!-- Import/Export Calendar -->
  <data name="ImportExport" xml:space="preserve">
    <value>Import/Export</value>
  </data>
  <data name="ImportEvents" xml:space="preserve">
    <value>Import Events</value>
  </data>
  <data name="ExportEvents" xml:space="preserve">
    <value>Export Events</value>
  </data>
  <data name="SelectFile" xml:space="preserve">
    <value>Select File</value>
  </data>
  <data name="ImportFileHelp" xml:space="preserve">
    <value>Select a CSV or Excel file containing events to import</value>
  </data>
  <data name="ImportInstructions" xml:space="preserve">
    <value>Import Instructions</value>
  </data>
  <data name="ImportSupportedFormats" xml:space="preserve">
    <value>Supported Formats</value>
  </data>
  <data name="ImportExpectedColumns" xml:space="preserve">
    <value>Expected Columns</value>
  </data>
  <data name="ExportInformation" xml:space="preserve">
    <value>Export Information</value>
  </data>
  <data name="ExportDescription" xml:space="preserve">
    <value>Export events from the selected month in your preferred format</value>
  </data>
  <data name="FileFormat" xml:space="preserve">
    <value>File Format</value>
  </data>
  <data name="Year" xml:space="preserve">
    <value>Year</value>
  </data>
  <data name="Month" xml:space="preserve">
    <value>Month</value>
  </data>
  <data name="PleaseSelectFile" xml:space="preserve">
    <value>Please select a file</value>
  </data>
  <data name="FileTooLarge" xml:space="preserve">
    <value>File is too large (max 10 MB)</value>
  </data>
  <data name="UnsupportedFileFormat" xml:space="preserve">
    <value>Unsupported file format</value>
  </data>
  <data name="EventsImportedSuccessfully" xml:space="preserve">
    <value>{0} events imported successfully</value>
  </data>
  <data name="ImportWarnings" xml:space="preserve">
    <value>with {0} warnings</value>
  </data>
  <data name="NoEventsToImport" xml:space="preserve">
    <value>No events to import</value>
  </data>
  <data name="ImportError" xml:space="preserve">
    <value>Import error</value>
  </data>
  <data name="ExportError" xml:space="preserve">
    <value>Export error</value>
  </data>
  
  <!-- Audit Log Localization -->
  <data name="NewMember" xml:space="preserve">
    <value>New Member</value>
  </data>
  <data name="Event" xml:space="preserve">
    <value>Event</value>
  </data>
  <data name="edited" xml:space="preserve">
    <value>edited</value>
  </data>
  <data name="deleted" xml:space="preserve">
    <value>deleted</value>
  </data>
  <data name="Start" xml:space="preserve">
    <value>Start</value>
  </data>
  <data name="End" xml:space="preserve">
    <value>End</value>
  </data>
  
  <!-- Duplicate Detection Messages -->
  <data name="DuplicateEmailMessage" xml:space="preserve">
    <value>This email address is already in use by an existing member.</value>
  </data>
  <data name="DuplicatePartialMessage" xml:space="preserve">
    <value>A member with this name and birth date already exists with email address {0}. Is this your correct email?</value>
  </data>
  <data name="DuplicateOkButton" xml:space="preserve">
    <value>OK</value>
  </data>
  <data name="DuplicateModifyButton" xml:space="preserve">
    <value>Modify Member</value>
  </data>
  <data name="DuplicateYesButton" xml:space="preserve">
    <value>Yes</value>
  </data>
  <data name="DuplicateNoButton" xml:space="preserve">
    <value>No</value>
  </data>
  <data name="Attention" xml:space="preserve">
    <value>Attention</value>
  </data>
  <data name="DuplicateEmailUpdateError" xml:space="preserve">
    <value>This email address is already used by another member.</value>
  </data>
  <data name="HQc_Id" xml:space="preserve">
    <value>Hockey Quebec ID</value>
  </data>
  <data name="HQc_IdPlaceholder" xml:space="preserve">
    <value>Hockey Quebec ID (optional)</value>
  </data>
  <data name="DuplicateHQcIdUpdateError" xml:space="preserve">
    <value>This Hockey Quebec ID is already used by another member.</value>
  </data>
  <data name="AdminOnly" xml:space="preserve">
    <value>Administrator only field</value>
  </data>
  <data name="AccountDeactivated" xml:space="preserve">
    <value>Your account has been deactivated. Please contact the administration.</value>
  </data>
  <data name="DeactivatedAccountLogin" xml:space="preserve">
    <value>This account is deactivated and cannot log in.</value>
  </data>

  <!-- Export and Filter UI -->
  <data name="Export" xml:space="preserve">
    <value>Export</value>
  </data>
  <data name="ExportMembers" xml:space="preserve">
    <value>Export Members</value>
  </data>
  <data name="ExportMembersDescription" xml:space="preserve">
    <value>Choose the export format for the currently displayed members.</value>
  </data>
  <data name="SelectExportFormat" xml:space="preserve">
    <value>Select export format</value>
  </data>
  <data name="ExportWillIncludeAllResults" xml:space="preserve">
    <value>Export will include all results matching current search and filter criteria:</value>
  </data>
  <data name="SelectFilter" xml:space="preserve">
    <value>Select filter</value>
  </data>
  <data name="SelectOption" xml:space="preserve">
    <value>Select option</value>
  </data>
  <data name="TypeToSearchCities" xml:space="preserve">
    <value>Type to search cities</value>
  </data>
  <data name="Apply" xml:space="preserve">
    <value>Apply</value>
  </data>
  <data name="ClearFilters" xml:space="preserve">
    <value>Clear filters</value>
  </data>
  <data name="ActiveFilters" xml:space="preserve">
    <value>Active filters</value>
  </data>
  <data name="RemoveFilter" xml:space="preserve">
    <value>Remove</value>
  </data>
  <data name="Loading" xml:space="preserve">
    <value>Loading...</value>
  </data>
  <data name="NoCitiesFound" xml:space="preserve">
    <value>No cities found</value>
  </data>
  <data name="InvalidExportParameters" xml:space="preserve">
    <value>Invalid export parameters</value>
  </data>
  <data name="ErrorExportingMembers" xml:space="preserve">
    <value>Error exporting members</value>
  </data>

  <!-- Empty State Messages -->
  <data name="EmptyState_NoMembers" xml:space="preserve">
    <value>No members found</value>
  </data>
  <data name="EmptyState_NoEvents" xml:space="preserve">
    <value>No events found</value>
  </data>
  <data name="EmptyState_NoAdminData" xml:space="preserve">
    <value>No administrative data found</value>
  </data>
  <data name="EmptyState_NoLogs" xml:space="preserve">
    <value>No logs found</value>
  </data>
  <data name="EmptyState_NoExports" xml:space="preserve">
    <value>No exports found</value>
  </data>
  <data name="EmptyState_NoSavedSearches" xml:space="preserve">
    <value>No saved searches found</value>
  </data>
  <data name="EmptyState_NoData" xml:space="preserve">
    <value>No data found</value>
  </data>

  <!-- Empty State Titles -->
  <data name="EmptyState_NoMembersTitle" xml:space="preserve">
    <value>No Members</value>
  </data>
  <data name="EmptyState_NoEventsTitle" xml:space="preserve">
    <value>No Events</value>
  </data>
  <data name="EmptyState_NoAdminDataTitle" xml:space="preserve">
    <value>No Administrative Data</value>
  </data>
  <data name="EmptyState_NoLogsTitle" xml:space="preserve">
    <value>No Logs</value>
  </data>
  <data name="EmptyState_NoExportsTitle" xml:space="preserve">
    <value>No Exports</value>
  </data>
  <data name="EmptyState_NoSavedSearchesTitle" xml:space="preserve">
    <value>No Saved Searches</value>
  </data>
  <data name="EmptyState_NoDataTitle" xml:space="preserve">
    <value>No Data</value>
  </data>

  <!-- Search Empty State Messages -->
  <data name="EmptyState_SearchNoResults" xml:space="preserve">
    <value>No results found</value>
  </data>
  <data name="EmptyState_SearchNoMembers" xml:space="preserve">
    <value>No members found for "{0}"</value>
  </data>
  <data name="EmptyState_SearchNoEvents" xml:space="preserve">
    <value>No events found for "{0}"</value>
  </data>
  <data name="EmptyState_SearchNoAdminData" xml:space="preserve">
    <value>No administrative data found for "{0}"</value>
  </data>
  <data name="EmptyState_SearchNoLogs" xml:space="preserve">
    <value>No logs found for "{0}"</value>
  </data>
  <data name="EmptyState_SearchNoExports" xml:space="preserve">
    <value>No exports found for "{0}"</value>
  </data>
  <data name="EmptyState_SearchNoSavedSearches" xml:space="preserve">
    <value>No saved searches found for "{0}"</value>
  </data>
  <data name="EmptyState_SearchNoData" xml:space="preserve">
    <value>No data found for "{0}"</value>
  </data>

  <!-- Search Suggestions -->
  <data name="EmptyState_SearchSuggestions" xml:space="preserve">
    <value>Try: {0}</value>
  </data>

  <!-- Action Messages -->
  <data name="EmptyState_AddFirstMember" xml:space="preserve">
    <value>Add First Member</value>
  </data>
  <data name="EmptyState_CreateFirstEvent" xml:space="preserve">
    <value>Create First Event</value>
  </data>
  <data name="EmptyState_ConfigureSystem" xml:space="preserve">
    <value>Configure System</value>
  </data>
  <data name="EmptyState_ViewAllLogs" xml:space="preserve">
    <value>View All Logs</value>
  </data>
  <data name="EmptyState_CreateExport" xml:space="preserve">
    <value>Create Export</value>
  </data>
  <data name="EmptyState_CreateSearch" xml:space="preserve">
    <value>Create Search</value>
  </data>
  <data name="EmptyState_GetStarted" xml:space="preserve">
    <value>Get Started</value>
  </data>
  <data name="EmptyState_SearchTryDifferent" xml:space="preserve">
    <value>Try a different search or modify your filters</value>
  </data>
  <data name="EmptyState_ErrorTitle" xml:space="preserve">
    <value>An error occurred</value>
  </data>
  <data name="EmptyState_RetryAction" xml:space="preserve">
    <value>Retry</value>
  </data>
  
  <!-- Context-specific suggestions -->
  <data name="EmptyState_members_filterSuggestion" xml:space="preserve">
    <value>Try removing some filters to see more results</value>
  </data>
  <data name="EmptyState_members_newSuggestion" xml:space="preserve">
    <value>You can add a new member using the button below</value>
  </data>
  <data name="EmptyState_events_filterSuggestion" xml:space="preserve">
    <value>Try changing the date range or removing filters</value>
  </data>
  <data name="EmptyState_events_newSuggestion" xml:space="preserve">
    <value>You can create a new event using the button below</value>
  </data>
  
  <!-- Error-specific messages -->
  <data name="EmptyState_ErrorDatabaseMembers" xml:space="preserve">
    <value>Unable to load members due to a database error</value>
  </data>
  <data name="EmptyState_ErrorNetworkMembers" xml:space="preserve">
    <value>Unable to load members due to a network issue</value>
  </data>
  <data name="EmptyState_ErrorPermissionMembers" xml:space="preserve">
    <value>You don't have permission to view these members</value>
  </data>
  <data name="EmptyState_ErrorGeneralMembers" xml:space="preserve">
    <value>An error occurred while loading members</value>
  </data>
  <data name="EmptyState_ErrorDatabaseEvents" xml:space="preserve">
    <value>Unable to load events due to a database error</value>
  </data>
  <data name="EmptyState_ErrorNetworkEvents" xml:space="preserve">
    <value>Unable to load events due to a network issue</value>
  </data>
  <data name="EmptyState_ErrorPermissionEvents" xml:space="preserve">
    <value>You don't have permission to view these events</value>
  </data>
  <data name="EmptyState_ErrorGeneralEvents" xml:space="preserve">
    <value>An error occurred while loading events</value>
  </data>
  <data name="EmptyState_ErrorDatabaseData" xml:space="preserve">
    <value>Unable to load data due to a database error</value>
  </data>
  <data name="EmptyState_ErrorNetworkData" xml:space="preserve">
    <value>Unable to load data due to a network issue</value>
  </data>
  <data name="EmptyState_ErrorPermissionData" xml:space="preserve">
    <value>You don't have permission to view this data</value>
  </data>
  <data name="EmptyState_ErrorGeneralData" xml:space="preserve">
    <value>An error occurred while loading data</value>
  </data>
  <!-- Environment Indicator Messages -->
  <data name="Environment_Development" xml:space="preserve">
    <value>Development Environment</value>
  </data>
  <data name="Environment_Test" xml:space="preserve">
    <value>Test Environment</value>
  </data>
  <data name="Environment_SqlServer" xml:space="preserve">
    <value>SQL Server Database</value>
  </data>
  <data name="Environment_DevToolsAvailable" xml:space="preserve">
    <value>Development tools available</value>
  </data>
  <!-- Development Tools Messages -->
  <data name="DevTools_Title" xml:space="preserve">
    <value>Development Tools</value>
  </data>
  <data name="DevTools_Registration" xml:space="preserve">
    <value>Registration</value>
  </data>
  <data name="DevTools_FillJunior" xml:space="preserve">
    <value>Fill Junior</value>
  </data>
  <data name="DevTools_FillAdult" xml:space="preserve">
    <value>Fill Adult</value>
  </data>
  <data name="DevTools_FillCoach" xml:space="preserve">
    <value>Fill Coach</value>
  </data>
  <data name="DevTools_Admin" xml:space="preserve">
    <value>Admin</value>
  </data>
  <data name="DevTools_GenerateTestData" xml:space="preserve">
    <value>Generate Test Data</value>
  </data>
  <data name="DevTools_ClearTestData" xml:space="preserve">
    <value>Clear Test Data</value>
  </data>
  <data name="DevTools_Debug" xml:space="preserve">
    <value>Debug</value>
  </data>
  <data name="DevTools_ShowDebugInfo" xml:space="preserve">
    <value>Show Debug Info</value>
  </data>
  <data name="DevTools_ShowEnvInfo" xml:space="preserve">
    <value>Environment Info</value>
  </data>
  <data name="DevTools_ShowUserInfo" xml:space="preserve">
    <value>User Info</value>
  </data>
  <data name="DevTools_Performance" xml:space="preserve">
    <value>Performance</value>
  </data>
  <data name="DevTools_MeasurePageLoad" xml:space="preserve">
    <value>Measure Page Load</value>
  </data>
  <data name="DevTools_ClearCache" xml:space="preserve">
    <value>Clear Cache</value>
  </data>
  <data name="DevTools_Api" xml:space="preserve">
    <value>API</value>
  </data>
  <data name="DevTools_TestApi" xml:space="preserve">
    <value>Test API</value>
  </data>
  <data name="DevTools_ApiDocs" xml:space="preserve">
    <value>API Documentation</value>
  </data>
  <data name="DevTools_Environment" xml:space="preserve">
    <value>Environment</value>
  </data>
  <data name="DevTools_Theme" xml:space="preserve">
    <value>Theme</value>
  </data>
  <data name="DevTools_Auth" xml:space="preserve">
    <value>Authentication</value>
  </data>
  <data name="DevTools_Enabled" xml:space="preserve">
    <value>Enabled</value>
  </data>
  <data name="DevTools_Disabled" xml:space="preserve">
    <value>Disabled</value>
  </data>
  <data name="DevTools_DebugInformation" xml:space="preserve">
    <value>Debug Information</value>
  </data>
  <data name="DevTools_FormFilled" xml:space="preserve">
    <value>Form filled successfully</value>
  </data>
  <data name="DevTools_ConfirmGenerateData" xml:space="preserve">
    <value>Are you sure you want to generate test data?</value>
  </data>
  <data name="DevTools_TestDataGenerated" xml:space="preserve">
    <value>Test data generated</value>
  </data>
  <data name="DevTools_ConfirmClearData" xml:space="preserve">
    <value>Are you sure you want to clear test data?</value>
  </data>
  <data name="DevTools_TestDataCleared" xml:space="preserve">
    <value>Test data cleared</value>
  </data>
  <data name="DevTools_PageInfo" xml:space="preserve">
    <value>Page Information</value>
  </data>
  <data name="DevTools_UserAgent" xml:space="preserve">
    <value>User Agent</value>
  </data>
  <data name="DevTools_Viewport" xml:space="preserve">
    <value>Viewport</value>
  </data>
  <data name="DevTools_LoadTime" xml:space="preserve">
    <value>Load Time</value>
  </data>
  <data name="DevTools_Browser" xml:space="preserve">
    <value>Browser</value>
  </data>
  <data name="DevTools_Language" xml:space="preserve">
    <value>Language</value>
  </data>
  <data name="DevTools_CookiesEnabled" xml:space="preserve">
    <value>Cookies Enabled</value>
  </data>
  <data name="DevTools_OnLine" xml:space="preserve">
    <value>Online</value>
  </data>
  <data name="DevTools_CheckConsole" xml:space="preserve">
    <value>Check console for details</value>
  </data>
  <data name="DevTools_PageLoadTime" xml:space="preserve">
    <value>Page load time</value>
  </data>
  <data name="DevTools_CacheCleared" xml:space="preserve">
    <value>Cache cleared successfully</value>
  </data>
  <data name="DevTools_CacheNotSupported" xml:space="preserve">
    <value>Cache not supported in this browser</value>
  </data>
  <data name="DevTools_TestingApi" xml:space="preserve">
    <value>Testing API...</value>
  </data>
  <!-- Layout Messages -->
  <data name="PoweredBy" xml:space="preserve">
    <value>Powered by</value>
  </data>
  <!-- Validation Messages for Custom ValidationResult -->
  <data name="ValidationParentRequired" xml:space="preserve">
    <value>At least one parent is required for Junior registration</value>
  </data>
  <data name="ValidationEmergencyContactRequired" xml:space="preserve">
    <value>Emergency contact is required</value>
  </data>
  <!-- Client-side validation messages -->
  <data name="ClientValidationEmailInvalid" xml:space="preserve">
    <value>Please enter a valid email address (e.g., <EMAIL>)</value>
  </data>
  <data name="ClientValidationPhoneInvalid" xml:space="preserve">
    <value>Please enter a 10-digit phone number</value>
  </data>
  <data name="ClientValidationPostalCodeInvalid" xml:space="preserve">
    <value>Please enter a valid Canadian postal code (e.g., A1A 1A1)</value>
  </data>
  <data name="ClientValidationDateInvalid" xml:space="preserve">
    <value>Please enter a valid date in YYYY-MM-DD format</value>
  </data>
  <data name="ClientValidationDateFuture" xml:space="preserve">
    <value>Date of birth cannot be in the future</value>
  </data>
  <data name="ClientValidationDateGeneral" xml:space="preserve">
    <value>Please enter a valid birth date</value>
  </data>
  <data name="ClientValidationNameTooShort" xml:space="preserve">
    <value>Name must be at least 2 characters long</value>
  </data>
  <data name="ClientValidationAddressTooShort" xml:space="preserve">
    <value>Address must be at least 5 characters long</value>
  </data>
  <data name="ClientValidationCityTooShort" xml:space="preserve">
    <value>City must be at least 2 characters long</value>
  </data>
  <data name="ClientValidationRequired" xml:space="preserve">
    <value>This field is required</value>
  </data>
  <!-- jQuery validation default messages -->
  <data name="JQueryValidationRequired" xml:space="preserve">
    <value>This field is required.</value>
  </data>
  <data name="JQueryValidationEmail" xml:space="preserve">
    <value>Please enter a valid email address.</value>
  </data>
  <data name="JQueryValidationNumber" xml:space="preserve">
    <value>Please enter a valid number.</value>
  </data>
  <data name="JQueryValidationDigits" xml:space="preserve">
    <value>Please enter only digits.</value>
  </data>
  <data name="JQueryValidationMinLength" xml:space="preserve">
    <value>Please enter at least {0} characters.</value>
  </data>
  <data name="JQueryValidationMaxLength" xml:space="preserve">
    <value>Please enter no more than {0} characters.</value>
  </data>
  <data name="JQueryValidationRangeLength" xml:space="preserve">
    <value>Please enter a value between {0} and {1} characters long.</value>
  </data>
  <data name="JQueryValidationMin" xml:space="preserve">
    <value>Please enter a value greater than or equal to {0}.</value>
  </data>
  <data name="JQueryValidationMax" xml:space="preserve">
    <value>Please enter a value less than or equal to {0}.</value>
  </data>
  <data name="JQueryValidationRange" xml:space="preserve">
    <value>Please enter a value between {0} and {1}.</value>
  </data>
  <!-- API Error Messages -->
  <data name="ApiAccessDenied" xml:space="preserve">
    <value>Access denied</value>
  </data>
  <data name="ApiFilterTypeRequired" xml:space="preserve">
    <value>Filter type is required</value>
  </data>
  <data name="ApiErrorRetrievingFilterOptions" xml:space="preserve">
    <value>Error retrieving filter options</value>
  </data>
  <data name="ApiInvalidRequestData" xml:space="preserve">
    <value>Invalid request data</value>
  </data>
  <data name="ApiUserNotFound" xml:space="preserve">
    <value>User not found</value>
  </data>
  <data name="ApiCodeSentEmail" xml:space="preserve">
    <value>Code sent by email</value>
  </data>
  <data name="ApiEmailSendingError" xml:space="preserve">
    <value>Email sending error</value>
  </data>
  <data name="ApiValidationBad" xml:space="preserve">
    <value>Validation failed</value>
  </data>
  <data name="ApiValidationGood" xml:space="preserve">
    <value>Validation successful</value>
  </data>
  <data name="ApiUnauthorizedAccess" xml:space="preserve">
    <value>Unauthorized access</value>
  </data>
  <data name="ApiSessionExpired" xml:space="preserve">
    <value>Session expired</value>
  </data>
  <data name="ApiFailedToLoadEvents" xml:space="preserve">
    <value>Failed to load events</value>
  </data>
  <data name="ApiValidationFailed" xml:space="preserve">
    <value>Validation failed</value>
  </data>
  <data name="ApiEventDataRequired" xml:space="preserve">
    <value>Event data is required</value>
  </data>
  <data name="ApiEventTitleRequired" xml:space="preserve">
    <value>Event title is required</value>
  </data>
  <data name="ApiEventCategoryRequired" xml:space="preserve">
    <value>Event category is required</value>
  </data>
  <!-- JavaScript Error Messages -->
  <data name="JsVerificationCodeMessage" xml:space="preserve">
    <value>Verification code: {0}\n\nEnter this code in the verification field below.</value>
  </data>
  <data name="JsErrorSendingCode" xml:space="preserve">
    <value>Error sending verification code. Check console for details.</value>
  </data>
  <data name="JsFormResetConfirm" xml:space="preserve">
    <value>Do you want to clear all form fields?</value>
  </data>
  <!-- System Error Page Messages -->
  <data name="SystemErrorTitle" xml:space="preserve">
    <value>Error</value>
  </data>
  <data name="SystemErrorMessage" xml:space="preserve">
    <value>An error occurred while processing your request.</value>
  </data>
  <data name="SystemErrorRequestId" xml:space="preserve">
    <value>Request ID:</value>
  </data>
  <data name="SystemErrorDevelopmentMode" xml:space="preserve">
    <value>Development Mode</value>
  </data>
  <data name="SystemErrorDevelopmentInfo" xml:space="preserve">
    <value>Swapping to the <strong>Development</strong> environment displays detailed information about the error that occurred.</value>
  </data>
  <data name="SystemErrorDevelopmentWarning" xml:space="preserve">
    <value><strong>The Development environment shouldn't be enabled for deployed applications.</strong> It can result in displaying sensitive information from exceptions to end users. For local debugging, enable the <strong>Development</strong> environment by setting the <strong>ASPNETCORE_ENVIRONMENT</strong> environment variable to <strong>Development</strong> and restarting the app.</value>
  </data>
  <data name="ApiValidationTypeRequired" xml:space="preserve">
    <value>Type is required.</value>
  </data>
  <data name="NoUpcomingEventsMessage" xml:space="preserve">
    <value>There are no upcoming events at the moment. Check the main calendar to see all events.</value>
  </data>
  <data name="NoRecentEventsMessage" xml:space="preserve">
    <value>No recent events are available at the moment.</value>
  </data>
  <data name="ExploreAllEvents" xml:space="preserve">
    <value>Explore All Events</value>
  </data>
  <data name="SetupFirstAdmin" xml:space="preserve">
    <value>Set Up First Administrator</value>
  </data>
  <data name="Error_ValidationFailed" xml:space="preserve">
    <value>Data validation error</value>
  </data>
  <data name="Error_Unauthorized" xml:space="preserve">
    <value>Unauthorized access</value>
  </data>
  <data name="Error_ResourceNotFound" xml:space="preserve">
    <value>Resource not found</value>
  </data>
  <data name="Error_RequestTimeout" xml:space="preserve">
    <value>Request timeout exceeded</value>
  </data>
  <data name="Error_InvalidOperation" xml:space="preserve">
    <value>Invalid operation</value>
  </data>
  <data name="Error_OperationNotSupported" xml:space="preserve">
    <value>Operation not supported</value>
  </data>
  <data name="Error_DatabaseError" xml:space="preserve">
    <value>Database error</value>
  </data>
  <data name="Error_UnexpectedError" xml:space="preserve">
    <value>An unexpected error occurred</value>
  </data>
  <data name="PageNotFoundTitle" xml:space="preserve">
    <value>Page Not Found</value>
  </data>
  <data name="PageNotFoundHeading" xml:space="preserve">
    <value>Oops! Page Not Found</value>
  </data>
  <data name="PageNotFoundMessage" xml:space="preserve">
    <value>The page you are looking for does not exist or has been moved.</value>
  </data>
  <data name="SystemErrorHeading" xml:space="preserve">
    <value>System Error</value>
  </data>
  <data name="SuggestedLinks" xml:space="preserve">
    <value>Suggested Links</value>
  </data>
  <data name="HomePage" xml:space="preserve">
    <value>Home Page</value>
  </data>
  <data name="GoBack" xml:space="preserve">
    <value>Go Back</value>
  </data>
  <data name="MemberRegistration" xml:space="preserve">
    <value>Member Registration</value>
  </data>
  <data name="MemberLogin" xml:space="preserve">
    <value>Member Login</value>
  </data>
  
  <!-- Event Subscription Page -->
  <data name="SubscribeToEvents" xml:space="preserve">
    <value>Subscribe to Events</value>
  </data>
  <data name="BrowseAndSubscribeToUpcomingEvents" xml:space="preserve">
    <value>Browse and subscribe to upcoming events</value>
  </data>
  <data name="Subscribe" xml:space="preserve">
    <value>Subscribe</value>
  </data>
  <data name="Registered" xml:space="preserve">
    <value>Registered</value>
  </data>
  <data name="Full" xml:space="preserve">
    <value>Full</value>
  </data>
  <data name="SpotsAvailable" xml:space="preserve">
    <value>spots available</value>
  </data>
  <data name="RegistrationDeadline" xml:space="preserve">
    <value>Registration Deadline</value>
  </data>
  <data name="NoRegistrationRequired" xml:space="preserve">
    <value>No Registration Required</value>
  </data>
  <data name="RegistrationClosed" xml:space="preserve">
    <value>Registration Closed</value>
  </data>
  <data name="CheckBackLaterForNewEvents" xml:space="preserve">
    <value>Check back later for new events</value>
  </data>
  <data name="EventSubscriptionSuccess" xml:space="preserve">
    <value>You have been successfully registered for the event!</value>
  </data>
  <data name="EventSubscriptionError" xml:space="preserve">
    <value>An error occurred while registering for the event.</value>
  </data>
  <data name="AlreadyRegisteredForEvent" xml:space="preserve">
    <value>You are already registered for this event.</value>
  </data>
  <data name="RegisterForUpcomingEvents" xml:space="preserve">
    <value>Register for upcoming events</value>
  </data>
  <data name="ErrorLoadingEvents" xml:space="preserve">
    <value>Error loading events</value>
  </data>
  <data name="MemberAccountRequired" xml:space="preserve">
    <value>A member account is required to register for events</value>
  </data>
  
  <!-- Event Details Modal -->
  <data name="Date" xml:space="preserve">
    <value>Date</value>
  </data>
  <data name="Time" xml:space="preserve">
    <value>Time</value>
  </data>
  <data name="RegisterForEvent" xml:space="preserve">
    <value>Register for Event</value>
  </data>
  <data name="AlreadyFull" xml:space="preserve">
    <value>This event is already full</value>
  </data>
  <data name="AllDay" xml:space="preserve">
    <value>All Day</value>
  </data>
  <data name="LocationTBA" xml:space="preserve">
    <value>Location TBA</value>
  </data>
  <data name="ErrorLoadingEventDetails" xml:space="preserve">
    <value>Error loading event details</value>
  </data>
  <data name="NoChangesDetected" xml:space="preserve">
    <value>No changes detected.</value>
  </data>
  <data name="Close" xml:space="preserve">
    <value>Close</value>
  </data>
  <data name="Location" xml:space="preserve">
    <value>Location</value>
  </data>
  <data name="YouAreRegistered" xml:space="preserve">
    <value>You are registered for this event</value>
  </data>
  <data name="Unregister" xml:space="preserve">
    <value>Unregister</value>
  </data>
  <data name="EventUnregistrationSuccess" xml:space="preserve">
    <value>You have been successfully unregistered from the event</value>
  </data>
  <data name="EventUnregistrationError" xml:space="preserve">
    <value>An error occurred while unregistering from the event</value>
  </data>
  <data name="MemberBenefitsTitle" xml:space="preserve">
    <value>Membership Benefits:</value>
  </data>
  <data name="MemberBenefitEventRegistration" xml:space="preserve">
    <value>Register for events and competitions</value>
  </data>
  <data name="MemberBenefitPriorityAccess" xml:space="preserve">
    <value>Priority access to popular events</value>
  </data>
  <data name="MemberBenefitUpdates" xml:space="preserve">
    <value>Club updates and news</value>
  </data>
  <data name="MemberBenefitCommunity" xml:space="preserve">
    <value>Access to the parahockey community</value>
  </data>
  <data name="MembershipIsFree" xml:space="preserve">
    <value>Membership is free and takes just a few minutes!</value>
  </data>
  <data name="EventRegistrationAfterSignup" xml:space="preserve">
    <value>After your registration, you will be automatically redirected to register for the selected event.</value>
  </data>
  <data name="Available" xml:space="preserve">
    <value>Available</value>
  </data>
  <data name="EventFull" xml:space="preserve">
    <value>Full</value>
  </data>
  <data name="Registering" xml:space="preserve">
    <value>Registering</value>
  </data>
  <data name="Unregistering" xml:space="preserve">
    <value>Unregistering</value>
  </data>
  <data name="RegistrationSuccess" xml:space="preserve">
    <value>Registration successful!</value>
  </data>
  <data name="RegistrationError" xml:space="preserve">
    <value>Registration error</value>
  </data>
  <data name="UnregisterConfirm" xml:space="preserve">
    <value>Are you sure you want to unregister from this event?</value>
  </data>
  <data name="ViewDetails" xml:space="preserve">
    <value>View Details</value>
  </data>
  <data name="ManageRegistration" xml:space="preserve">
    <value>Manage Registration</value>
  </data>
  <data name="JuniorMembershipNotAvailable18Plus" xml:space="preserve">
    <value>Junior membership is not available for persons 18 years and older</value>
  </data>

  <!-- Page Audit System -->
  <data name="InventoryGeneratedSuccessfully" xml:space="preserve">
    <value>Page inventory generated successfully</value>
  </data>
  <data name="ErrorGeneratingInventory" xml:space="preserve">
    <value>Error generating page inventory</value>
  </data>
  <data name="PageNameRequired" xml:space="preserve">
    <value>Page name is required</value>
  </data>
  <data name="PageAuditCompleted" xml:space="preserve">
    <value>Page audit completed</value>
  </data>
  <data name="ErrorAuditingPage" xml:space="preserve">
    <value>Error auditing page</value>
  </data>
  <data name="NoInventoryFound" xml:space="preserve">
    <value>No inventory found. Please generate an inventory first.</value>
  </data>
  <data name="ErrorRetrievingInventory" xml:space="preserve">
    <value>Error retrieving inventory</value>
  </data>
  <data name="ErrorRetrievingAuditHistory" xml:space="preserve">
    <value>Error retrieving audit history</value>
  </data>
  <data name="InvalidFindingId" xml:space="preserve">
    <value>Invalid finding ID</value>
  </data>
  <data name="ResolutionNotesRequired" xml:space="preserve">
    <value>Resolution notes are required</value>
  </data>
  <data name="FindingResolvedSuccessfully" xml:space="preserve">
    <value>Finding resolved successfully</value>
  </data>
  <data name="ErrorResolvingFinding" xml:space="preserve">
    <value>Error resolving finding</value>
  </data>
  <data name="PageAuditSystem" xml:space="preserve">
    <value>Page Audit System</value>
  </data>
  <data name="GenerateInventory" xml:space="preserve">
    <value>Generate Inventory</value>
  </data>
  <data name="AuditPage" xml:space="preserve">
    <value>Audit Page</value>
  </data>
  <data name="PageInventory" xml:space="preserve">
    <value>Page Inventory</value>
  </data>
  <data name="AuditResults" xml:space="preserve">
    <value>Audit Results</value>
  </data>
  <data name="SecurityScore" xml:space="preserve">
    <value>Security Score</value>
  </data>
  <data name="AccessibilityScore" xml:space="preserve">
    <value>Accessibility Score</value>
  </data>
  <data name="PerformanceScore" xml:space="preserve">
    <value>Performance Score</value>
  </data>
  <data name="LocalizationScore" xml:space="preserve">
    <value>Localization Score</value>
  </data>
  <data name="OverallScore" xml:space="preserve">
    <value>Overall Score</value>
  </data>
  <data name="CriticalIssues" xml:space="preserve">
    <value>Critical Issues</value>
  </data>
  <data name="HighIssues" xml:space="preserve">
    <value>High Issues</value>
  </data>
  <data name="MediumIssues" xml:space="preserve">
    <value>Medium Issues</value>
  </data>
  <data name="LowIssues" xml:space="preserve">
    <value>Low Issues</value>
  </data>
  <data name="TotalIssues" xml:space="preserve">
    <value>Total Issues</value>
  </data>
  <data name="PageComplexity" xml:space="preserve">
    <value>Page Complexity</value>
  </data>
  <data name="Priority" xml:space="preserve">
    <value>Priority</value>
  </data>
  <data name="IsModernized" xml:space="preserve">
    <value>Modernized</value>
  </data>
  <data name="LastAuditScore" xml:space="preserve">
    <value>Last Audit Score</value>
  </data>
  <data name="AuditHistory" xml:space="preserve">
    <value>Audit History</value>
  </data>
  <data name="ResolveFinding" xml:space="preserve">
    <value>Resolve Finding</value>
  </data>
  <data name="ResolutionNotes" xml:space="preserve">
    <value>Resolution Notes</value>
  </data>
  
  <!-- Page Audit System -->
  <data name="PageReviewPlanGenerated" xml:space="preserve">
    <value>Page review plan generated successfully</value>
  </data>
  <data name="InitialAuditReportsGenerated" xml:space="preserve">
    <value>Initial audit reports generated successfully</value>
  </data>
  <data name="ErrorGeneratingReviewPlan" xml:space="preserve">
    <value>Error generating review plan</value>
  </data>
  <data name="ErrorGeneratingAuditReports" xml:space="preserve">
    <value>Error generating audit reports</value>
  </data>

  <!-- Error Handling System -->
  <data name="Error_ValidationFailed" xml:space="preserve">
    <value>Validation failed</value>
  </data>
  <data name="Error_Unauthorized" xml:space="preserve">
    <value>Unauthorized access</value>
  </data>
  <data name="Error_ResourceNotFound" xml:space="preserve">
    <value>Resource not found</value>
  </data>
  <data name="Error_RequestTimeout" xml:space="preserve">
    <value>Request timeout</value>
  </data>
  <data name="Error_InvalidOperation" xml:space="preserve">
    <value>Invalid operation</value>
  </data>
  <data name="Error_OperationNotSupported" xml:space="preserve">
    <value>Operation not supported</value>
  </data>
  <data name="Error_DatabaseError" xml:space="preserve">
    <value>Database error</value>
  </data>
  <data name="Error_UnexpectedError" xml:space="preserve">
    <value>An unexpected error occurred</value>
  </data>

  <!-- Validation Error Messages -->
  <data name="ValidationError_Generic" xml:space="preserve">
    <value>Validation error</value>
  </data>
  <data name="ValidationError_Required" xml:space="preserve">
    <value>This field is required</value>
  </data>
  <data name="ValidationError_EmailFormat" xml:space="preserve">
    <value>Invalid email format</value>
  </data>
  <data name="ValidationError_PhoneFormat" xml:space="preserve">
    <value>Invalid phone format</value>
  </data>
  <data name="ValidationError_PostalCodeFormat" xml:space="preserve">
    <value>Invalid postal code format</value>
  </data>
  <data name="ValidationError_StringLength" xml:space="preserve">
    <value>Invalid text length</value>
  </data>
  <data name="ValidationError_Range" xml:space="preserve">
    <value>Value is out of allowed range</value>
  </data>
  <data name="ValidationError_DateFormat" xml:space="preserve">
    <value>Invalid date format</value>
  </data>

  <!-- Validation Summary Messages -->
  <data name="ValidationSummary_SingleError" xml:space="preserve">
    <value>Please correct the error below</value>
  </data>
  <data name="ValidationSummary_MultipleErrors" xml:space="preserve">
    <value>Please correct the {0} errors below</value>
  </data>

  <!-- Error Page Messages -->
  <data name="SystemErrorTitle" xml:space="preserve">
    <value>System Error</value>
  </data>
  <data name="SystemErrorHeading" xml:space="preserve">
    <value>An unexpected error occurred</value>
  </data>
  <data name="SystemErrorRequestId" xml:space="preserve">
    <value>Request ID</value>
  </data>
  <data name="PageNotFoundTitle" xml:space="preserve">
    <value>Page Not Found</value>
  </data>
  <data name="PageNotFoundHeading" xml:space="preserve">
    <value>This page doesn't exist</value>
  </data>
  <data name="PageNotFoundMessage" xml:space="preserve">
    <value>The page you're looking for could not be found. It may have been moved, deleted, or you may have typed the wrong address.</value>
  </data>
  <data name="RequestedUrl" xml:space="preserve">
    <value>Requested URL</value>
  </data>
  <data name="ReturnToHome" xml:space="preserve">
    <value>Return to Home</value>
  </data>
  <data name="GoBack" xml:space="preserve">
    <value>Go Back</value>
  </data>
  <data name="ViewCalendar" xml:space="preserve">
    <value>View Calendar</value>
  </data>
  <data name="SuggestedLinks" xml:space="preserve">
    <value>Suggested Links</value>
  </data>
  <data name="HomePage" xml:space="preserve">
    <value>Home Page</value>
  </data>
  <data name="Calendar" xml:space="preserve">
    <value>Calendar</value>
  </data>
  <data name="MemberRegistration" xml:space="preserve">
    <value>Member Registration</value>
  </data>
  <data name="MemberLogin" xml:space="preserve">
    <value>Member Login</value>
  </data>
  <data name="NeedHelp" xml:space="preserve">
    <value>Need help?</value>
  </data>
  <data name="AutoRedirectConfirm" xml:space="preserve">
    <value>Would you like to be redirected to the home page?</value>
  </data>
  
  <!-- Home Page Accessibility Keys -->
  <data name="SkipToMainContent" xml:space="preserve">
    <value>Skip to main content</value>
  </data>
  <data name="MainContentArea" xml:space="preserve">
    <value>Main content area</value>
  </data>
  <data name="ParahockeyLogoAlt" xml:space="preserve">
    <value>Parahockey Quebec Logo - Sledge Hockey Association</value>
  </data>
  <data name="PrimaryActions" xml:space="preserve">
    <value>Primary actions</value>
  </data>
  <data name="RegisterButtonDesc" xml:space="preserve">
    <value>Start your membership registration</value>
  </data>
  <data name="ViewCalendarDesc" xml:space="preserve">
    <value>View the public events calendar</value>
  </data>
  <data name="SubscribeEventsDesc" xml:space="preserve">
    <value>Subscribe to events and activities</value>
  </data>
  <data name="CommunityStatsLabel" xml:space="preserve">
    <value>Community statistics</value>
  </data>
  <data name="PlayersCount" xml:space="preserve">
    <value>Over 150 active players</value>
  </data>
  <data name="TeamsCount" xml:space="preserve">
    <value>12 teams</value>
  </data>
  <data name="CoachesCount" xml:space="preserve">
    <value>25 coaches</value>
  </data>
  <data name="VolunteersCount" xml:space="preserve">
    <value>Over 50 volunteers</value>
  </data>
  <data name="RegistrationTypesList" xml:space="preserve">
    <value>Available registration types</value>
  </data>
  <data name="CtaButtonDesc" xml:space="preserve">
    <value>Start your registration now</value>
  </data>
  
  <!-- Home Page Content Keys (if missing) -->
  <data name="HomePageTitle" xml:space="preserve">
    <value>Parahockey Quebec - Home Page</value>
  </data>
  <data name="WelcomeTitle" xml:space="preserve">
    <value>Welcome to Parahockey Quebec</value>
  </data>
  <data name="WelcomeSubtitle" xml:space="preserve">
    <value>Join our passionate sledge hockey community</value>
  </data>
  <data name="RegisterNow" xml:space="preserve">
    <value>Register Now</value>
  </data>
  <data name="ViewCalendar" xml:space="preserve">
    <value>View Calendar</value>
  </data>
  <data name="SubscribeToEvents" xml:space="preserve">
    <value>Subscribe to Events</value>
  </data>
  <data name="CommunityTitle" xml:space="preserve">
    <value>Our Community</value>
  </data>
  <data name="ActivePlayers" xml:space="preserve">
    <value>Active Players</value>
  </data>
  <data name="Teams" xml:space="preserve">
    <value>Teams</value>
  </data>
  <data name="Coaches" xml:space="preserve">
    <value>Coaches</value>
  </data>
  <data name="Volunteers" xml:space="preserve">
    <value>Volunteers</value>
  </data>
  <data name="RegistrationTypesTitle" xml:space="preserve">
    <value>Registration Types</value>
  </data>
  <data name="RegistrationTypesSubtitle" xml:space="preserve">
    <value>Choose the registration type that suits you</value>
  </data>
  <data name="Junior" xml:space="preserve">
    <value>Junior</value>
  </data>
  <data name="JuniorDesc" xml:space="preserve">
    <value>For young players aged 6 to 17 years</value>
  </data>
  <data name="Development" xml:space="preserve">
    <value>Development</value>
  </data>
  <data name="DevelopmentDesc" xml:space="preserve">
    <value>Training program for new players</value>
  </data>
  <data name="Elite" xml:space="preserve">
    <value>Elite</value>
  </data>
  <data name="EliteDesc" xml:space="preserve">
    <value>For experienced and competitive players</value>
  </data>
  <data name="Coach" xml:space="preserve">
    <value>Coach</value>
  </data>
  <data name="CoachDesc" xml:space="preserve">
    <value>Join our team of certified coaches</value>
  </data>
  <data name="Volunteer" xml:space="preserve">
    <value>Volunteer</value>
  </data>
  <data name="VolunteerDesc" xml:space="preserve">
    <value>Help organize events and activities</value>
  </data>
  <data name="FamilyFriends" xml:space="preserve">
    <value>Family &amp; Friends</value>
  </data>
  <data name="FamilyFriendsDesc" xml:space="preserve">
    <value>Support our players and participate in events</value>
  </data>
  <data name="CtaTitle" xml:space="preserve">
    <value>Ready to Join Our Team?</value>
  </data>
  <data name="CtaSubtitle" xml:space="preserve">
    <value>Register today and discover the passion of sledge hockey</value>
  </data>
  <data name="CtaButton" xml:space="preserve">
    <value>Start My Registration</value>
  </data>
  
  <!-- Members Registration Page Accessibility Keys -->
  <data name="SkipToRegistrationForm" xml:space="preserve">
    <value>Skip to registration form</value>
  </data>
  <data name="RegistrationMainContent" xml:space="preserve">
    <value>Registration main content</value>
  </data>
  <data name="LoginLinkDescription" xml:space="preserve">
    <value>Navigate to login page for existing members</value>
  </data>
  <data name="RegistrationFormDescription" xml:space="preserve">
    <value>Complete registration form to become a member of Parahockey Quebec</value>
  </data>
  <data name="FirstNameHelp" xml:space="preserve">
    <value>Your first name as it appears on official documents</value>
  </data>
  <data name="LastNameHelp" xml:space="preserve">
    <value>Your last name as it appears on official documents</value>
  </data>
  <data name="DateOfBirthHelp" xml:space="preserve">
    <value>Required format: YYYY-MM-DD (ex: 1990-12-25)</value>
  </data>
  <data name="GenderFieldsetLabel" xml:space="preserve">
    <value>Gender selection</value>
  </data>
  <data name="AddressFieldsetLabel" xml:space="preserve">
    <value>Address information</value>
  </data>
  <data name="ContactFieldsetLabel" xml:space="preserve">
    <value>Contact information</value>
  </data>
  <data name="RegistrationTypeFieldsetLabel" xml:space="preserve">
    <value>Registration type</value>
  </data>
  <data name="FormSubmissionStatus" xml:space="preserve">
    <value>Processing your registration...</value>
  </data>
  <data name="FormValidationError" xml:space="preserve">
    <value>Validation error detected in form</value>
  </data>

  <!-- Login Page Accessibility -->
  <data name="SkipToLoginForm" xml:space="preserve">
    <value>Skip to login form</value>
  </data>
  <data name="LoginMainContent" xml:space="preserve">
    <value>Login page main content</value>
  </data>
  <data name="LoginFormDescription" xml:space="preserve">
    <value>Member search and login form</value>
  </data>
  <data name="LastNameHelp" xml:space="preserve">
    <value>Enter your last name as registered</value>
  </data>
  <data name="EmailHelp" xml:space="preserve">
    <value>Enter the email address associated with your account</value>
  </data>
  <data name="OpenDatePicker" xml:space="preserve">
    <value>Open date picker</value>
  </data>
  <data name="SearchButtonHelp" xml:space="preserve">
    <value>Search for your member profile in the database</value>
  </data>
  <data name="SearchResultsTableDescription" xml:space="preserve">
    <value>Search results for matching members</value>
  </data>
  <data name="Actions" xml:space="preserve">
    <value>Actions</value>
  </data>
  <data name="SendCodeButtonHelp" xml:space="preserve">
    <value>Send verification code by email</value>
  </data>
  <data name="VerificationCodeEntry" xml:space="preserve">
    <value>Verification code entry</value>
  </data>
  <data name="VerificationCode" xml:space="preserve">
    <value>Verification Code</value>
  </data>
  <data name="VerificationCodeHelp" xml:space="preserve">
    <value>Enter the 6-digit code received by email</value>
  </data>
  <data name="VerifyButtonHelp" xml:space="preserve">
    <value>Verify code and login</value>
  </data>
  <data name="CloseAlert" xml:space="preserve">
    <value>Close alert</value>
  </data>
  <data name="InfoMessage" xml:space="preserve">
    <value>Information message</value>
  </data>
  <data name="SendingCode" xml:space="preserve">
    <value>Sending...</value>
  </data>
  <data name="VerifyingCode" xml:space="preserve">
    <value>Verifying...</value>
  </data>
  <data name="VerificationSuccessful" xml:space="preserve">
    <value>Verification successful!</value>
  </data>
  <data name="VerificationFailed" xml:space="preserve">
    <value>Invalid verification code</value>
  </data>
  <data name="VerificationError" xml:space="preserve">
    <value>Error during verification</value>
  </data>
  <data name="LogoutSuccessful" xml:space="preserve">
    <value>Successfully logged out. See you soon!</value>
  </data>
  <data name="LogoutCompleted" xml:space="preserve">
    <value>Logout completed</value>
  </data>

  <!-- Admin Dashboard Accessibility -->
  <data name="SkipToMainContent" xml:space="preserve">
    <value>Skip to main content</value>
  </data>
  <data name="SkipToStatistics" xml:space="preserve">
    <value>Skip to statistics</value>
  </data>
  <data name="SkipToQuickActions" xml:space="preserve">
    <value>Skip to quick actions</value>
  </data>
  <data name="AdminDashboardMainContent" xml:space="preserve">
    <value>Admin dashboard main content</value>
  </data>
  <data name="AdminDashboard" xml:space="preserve">
    <value>Admin Dashboard</value>
  </data>
  <data name="AdminWelcomeMessage" xml:space="preserve">
    <value>Welcome, {0} | Environment: {1}</value>
  </data>
  <data name="SystemStatistics" xml:space="preserve">
    <value>System Statistics</value>
  </data>
  <data name="TotalMembers" xml:space="preserve">
    <value>Total Members</value>
  </data>
  <data name="TotalParents" xml:space="preserve">
    <value>Total Parents</value>
  </data>
  <data name="EmergencyContacts" xml:space="preserve">
    <value>Emergency Contacts</value>
  </data>
  <data name="CurrentEnvironment" xml:space="preserve">
    <value>Current Environment</value>
  </data>
  <data name="ViewAllMembers" xml:space="preserve">
    <value>View All Members</value>
  </data>
  <data name="ViewSystemInfo" xml:space="preserve">
    <value>System Info</value>
  </data>
  <data name="QuickActions" xml:space="preserve">
    <value>Quick Actions</value>
  </data>
  <data name="QuickActionsDescription" xml:space="preserve">
    <value>Quick access to main administration system functions</value>
  </data>
  <data name="AdminQuickActions" xml:space="preserve">
    <value>Administration quick actions</value>
  </data>
  <data name="ViewAllMembersDescription" xml:space="preserve">
    <value>View and manage all registered members</value>
  </data>
  <data name="AddNewMember" xml:space="preserve">
    <value>Add New Member</value>
  </data>
  <data name="AddNewMemberDescription" xml:space="preserve">
    <value>Register a new member in the system</value>
  </data>
  <data name="ManageCalendar" xml:space="preserve">
    <value>Manage Calendar</value>
  </data>
  <data name="ManageCalendarDescription" xml:space="preserve">
    <value>Create and modify calendar events</value>
  </data>
  <data name="ManageAdmins" xml:space="preserve">
    <value>Manage Administrators</value>
  </data>
  <data name="ManageAdminsDescription" xml:space="preserve">
    <value>Administer admin accounts and permissions</value>
  </data>
  <data name="PageAuditSystem" xml:space="preserve">
    <value>Page Audit System</value>
  </data>
  <data name="PageAuditDescription" xml:space="preserve">
    <value>Analyze page performance and accessibility</value>
  </data>
  <data name="SystemInformation" xml:space="preserve">
    <value>System Information</value>
  </data>
  <data name="SystemInfoDescription" xml:space="preserve">
    <value>View system status and configuration</value>
  </data>
  <data name="TeamsManagement" xml:space="preserve">
    <value>Teams Management</value>
  </data>
  <data name="TeamsDescription" xml:space="preserve">
    <value>Organize and manage hockey teams</value>
  </data>
  <data name="ViewStatistics" xml:space="preserve">
    <value>View Statistics</value>
  </data>
  <data name="StatisticsDescription" xml:space="preserve">
    <value>View system reports and analytics</value>
  </data>
  <data name="BackToSite" xml:space="preserve">
    <value>Back to Site</value>
  </data>
  <data name="BackToSiteDescription" xml:space="preserve">
    <value>Return to the public homepage</value>
  </data>
  <data name="RecentMemberAudit" xml:space="preserve">
    <value>Recent Member Audit</value>
  </data>
  <data name="RecentAuditDescription" xml:space="preserve">
    <value>Recent activities and system modifications</value>
  </data>
  <data name="AuditTableDescription" xml:space="preserve">
    <value>Table of recent audit activities</value>
  </data>
  <data name="AuditDateTime" xml:space="preserve">
    <value>Date &amp; Time</value>
  </data>
  <data name="Description" xml:space="preserve">
    <value>Description</value>
  </data>
  <data name="PerformedBy" xml:space="preserve">
    <value>Performed By</value>
  </data>
  <data name="Actions" xml:space="preserve">
    <value>Actions</value>
  </data>
  <data name="AdminAction" xml:space="preserve">
    <value>Administrator action</value>
  </data>
  <data name="Admin" xml:space="preserve">
    <value>Admin</value>
  </data>
  <data name="SystemAction" xml:space="preserve">
    <value>System action</value>
  </data>
  <data name="ViewAuditDetails" xml:space="preserve">
    <value>View audit details for {0}</value>
  </data>
  <data name="ViewDetails" xml:space="preserve">
    <value>View Details</value>
  </data>
  <data name="ViewAllAuditHistory" xml:space="preserve">
    <value>View All Audit History</value>
  </data>
  <data name="NoRecentActivity" xml:space="preserve">
    <value>No Recent Activity</value>
  </data>
  <data name="NoAuditActivityDescription" xml:space="preserve">
    <value>No audit activity has been recorded recently</value>
  </data>
  <data name="RecentMemberRegistrations" xml:space="preserve">
    <value>Recent Member Registrations</value>
  </data>
  <data name="RecentMembersDescription" xml:space="preserve">
    <value>New members recently registered in the system</value>
  </data>
  <data name="MembersTableDescription" xml:space="preserve">
    <value>Table of recently registered members</value>
  </data>
  <data name="MemberID" xml:space="preserve">
    <value>Member ID</value>
  </data>
  <data name="FullName" xml:space="preserve">
    <value>Full Name</value>
  </data>
  <data name="RegistrationDate" xml:space="preserve">
    <value>Registration Date</value>
  </data>
  <data name="SendEmailTo" xml:space="preserve">
    <value>Send email to {0}</value>
  </data>
  <data name="ViewMemberDetails" xml:space="preserve">
    <value>View details for {0}</value>
  </data>
  <data name="NoMembersRegistered" xml:space="preserve">
    <value>No Members Registered</value>
  </data>
  <data name="NoMembersDescription" xml:space="preserve">
    <value>No members have been registered in the system yet</value>
  </data>
  <data name="RegisterFirstMember" xml:space="preserve">
    <value>Register First Member</value>
  </data>
  <data name="DashboardLoaded" xml:space="preserve">
    <value>Dashboard loaded successfully</value>
  </data>
  
  <!-- Admin Members Page Accessibility -->
  <data name="SkipToSearchFilters" xml:space="preserve">
    <value>Skip to search and filters</value>
  </data>
  <data name="SkipToMemberActions" xml:space="preserve">
    <value>Skip to member actions</value>
  </data>
  <data name="SkipToMembersTable" xml:space="preserve">
    <value>Skip to members table</value>
  </data>
  <data name="MemberManagementPage" xml:space="preserve">
    <value>Member management page</value>
  </data>
  <data name="SearchAndFilters" xml:space="preserve">
    <value>Search and Filters</value>
  </data>
  <data name="SearchHelpText" xml:space="preserve">
    <value>Search by first name, last name, or email address</value>
  </data>
  <data name="AdvancedFilters" xml:space="preserve">
    <value>Advanced Filters</value>
  </data>
  <data name="SelectFilterType" xml:space="preserve">
    <value>Select filter type...</value>
  </data>
  <data name="SelectFilterTypeHelp" xml:space="preserve">
    <value>Choose the type of filter to apply</value>
  </data>
  <data name="SelectFilterValueHelp" xml:space="preserve">
    <value>Select the value to filter by</value>
  </data>
  <data name="FilterValue" xml:space="preserve">
    <value>Filter Value</value>
  </data>
  <data name="TypeToSearchCities" xml:space="preserve">
    <value>Type to search cities...</value>
  </data>
  <data name="CitySearchHelp" xml:space="preserve">
    <value>Type at least 2 characters to search for cities</value>
  </data>
  <data name="ApplyFilter" xml:space="preserve">
    <value>Apply Filter</value>
  </data>
  <data name="ApplyFilterHelp" xml:space="preserve">
    <value>Apply the selected filter to the member list</value>
  </data>
  <data name="ClearFilters" xml:space="preserve">
    <value>Clear Filters</value>
  </data>
  <data name="ClearFiltersHelp" xml:space="preserve">
    <value>Remove all active filters</value>
  </data>
  <data name="ActiveFilters" xml:space="preserve">
    <value>Active Filters</value>
  </data>
  <data name="MemberActions" xml:space="preserve">
    <value>Member Actions</value>
  </data>
  <data name="AddNewMemberHelp" xml:space="preserve">
    <value>Register a new member in the system</value>
  </data>
  <data name="SearchResultsSummary" xml:space="preserve">
    <value>Search results summary</value>
  </data>
  <data name="MembersTable" xml:space="preserve">
    <value>Members Table</value>
  </data>
  <data name="MembersTableDescription" xml:space="preserve">
    <value>Table showing all registered members with their details and available actions</value>
  </data>
  <data name="NoCitiesFound" xml:space="preserve">
    <value>No cities found</value>
  </data>
  <data name="NoOptionsAvailable" xml:space="preserve">
    <value>No options available</value>
  </data>
  <data name="SelectOption" xml:space="preserve">
    <value>Select an option</value>
  </data>
  <data name="Loading" xml:space="preserve">
    <value>Loading</value>
  </data>
  
  <!-- Admin Member Details Page Accessibility -->
  <data name="SkipToMemberInfo" xml:space="preserve">
    <value>Skip to member information</value>
  </data>
  <data name="SkipToContactInfo" xml:space="preserve">
    <value>Skip to contact information</value>
  </data>
  <data name="SkipToAuditHistory" xml:space="preserve">
    <value>Skip to audit history</value>
  </data>
  <data name="MemberDetailsPage" xml:space="preserve">
    <value>Member details page</value>
  </data>
  <data name="MemberDetailsPageLoaded" xml:space="preserve">
    <value>Member details page loaded successfully</value>
  </data>
  <data name="MemberActionButtons" xml:space="preserve">
    <value>Member action buttons</value>
  </data>
  <data name="Exporting" xml:space="preserve">
    <value>Exporting...</value>
  </data>
  
  <!-- Events Subscribe Page Accessibility -->
  <data name="SkipToUpcomingEvents" xml:space="preserve">
    <value>Skip to upcoming events</value>
  </data>
  <data name="SkipToEventCalendar" xml:space="preserve">
    <value>Skip to event calendar</value>
  </data>
  <data name="EventSubscriptionPage" xml:space="preserve">
    <value>Event subscription page</value>
  </data>
  <data name="EventSubscriptionPageLoaded" xml:space="preserve">
    <value>Event subscription page loaded successfully</value>
  </data>
  <data name="BackToHomePage" xml:space="preserve">
    <value>Return to home page</value>
  </data>
  <data name="CloseAlert" xml:space="preserve">
    <value>Close alert</value>
  </data>
  <data name="MustBeLoggedInToRegister" xml:space="preserve">
    <value>You must be logged in to register for events</value>
  </data>
  <data name="LoadingEventDetails" xml:space="preserve">
    <value>Loading event details...</value>
  </data>
</root>