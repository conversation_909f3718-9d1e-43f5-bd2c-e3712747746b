{
    "Logging": {
        "LogLevel": {
            "Default": "Information",
            "Microsoft.AspNetCore": "Warning"
        }
    },
    "AllowedHosts": "*",
    "ConnectionStrings": {
        "DefaultConnection": "Server=Daniel-Complys\\SQLEXPRESS;Database=ParaHockeyDB_DEV;Trusted_Connection=true;MultipleActiveResultSets=true;Encrypt=False;TrustServerCertificate=True;"
    },
    "Environment": {
        "Name": "DEVELOPMENT",
        "Theme": "info",
        "ShowBanner": false,
        "UseAuthentication": true,
        "BannerText": "",
        "ShowDevelopmentTools": false,
        "EnableDetailedErrorLogging": true,
        "EnvironmentIndicatorColor": "info",
        "ShowUserFriendlyErrors": false,
        "ErrorDetailLevel": "detailed"
    },
    "AzureAd": {
        "Instance": "https://login.microsoftonline.com/",
        "Domain": "common",
        "TenantId": "daf47e42-871b-4818-bb22-814e32266933",
        "ClientId": "dd2beea2-bc7d-4832-89a5-42dd9b7393b2",
        "CallbackPath": "/signin-oidc",
        "AdminGroupId": "admin-group-id"
    },
    "Email": {
        // SMTP2GO configuration (commented out until domain verification)
        // "SmtpHost": "mail.smtp2go.com",
        // "SmtpPort": "2525",
        // "Username": "parahockey.com",
        // "Password": "twU4QePp19iy9Hdd",
        // "FromEmail": "<EMAIL>",

        // Microsoft Office365 configuration (working)
        "SmtpHost": "smtp.office365.com",
        "SmtpPort": "587",
        "Username": "<EMAIL>",
        "Password": "L@535539113654on",
        "FromEmail": "<EMAIL>",
        "FromName": "Parahockey Verification"
    },
    "Registration": {
        "AgeOfMajority": 18
    }
}
