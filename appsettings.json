{"Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning"}}, "AllowedHosts": "*", "ConnectionStrings": {"DefaultConnection": "Server=Daniel-Complys\\SQLEXPRESS;Database=ParaHockeyDB_DEV;Trusted_Connection=true;MultipleActiveResultSets=true;Encrypt=False;TrustServerCertificate=True;"}, "Environment": {"Name": "DEVELOPMENT", "Theme": "info", "ShowBanner": false, "UseAuthentication": true, "BannerText": "", "ShowDevelopmentTools": false, "EnableDetailedErrorLogging": true, "EnvironmentIndicatorColor": "info", "ShowUserFriendlyErrors": false, "ErrorDetailLevel": "detailed"}, "AzureAd": {"Instance": "https://login.microsoftonline.com/", "Domain": "common", "TenantId": "daf47e42-871b-4818-bb22-814e32266933", "ClientId": "dd2beea2-bc7d-4832-89a5-42dd9b7393b2", "CallbackPath": "/signin-oidc", "AdminGroupId": "admin-group-id"}, "Email": {"SmtpHost": "smtp.office365.com", "SmtpPort": "587", "Username": "<EMAIL>", "Password": "L@535539113654on", "FromEmail": "<EMAIL>", "FromName": "Parahockey Verification"}, "Registration": {"AgeOfMajority": 18}, "Import": {"MaxFileSizeMB": 50, "MaxRecordsPerImport": 10000, "BatchSize": 100, "TimeoutMinutes": 30, "SupportedFileExtensions": [".xlsx", ".xls"], "EnableDuplicateDetection": true, "DuplicateThreshold": 0.8, "EnableAuditLogging": true, "TempFileDirectory": "C:\\Temp\\ParaHockey\\Imports", "TempFileRetentionHours": 24, "AutoCleanupTempFiles": true, "DefaultCulture": "en", "SupportedCultures": ["en", "fr"], "EmailNotifications": {"Enabled": false, "OnlyOnFailure": true, "NotificationEmails": "", "SuccessTemplate": "Import completed successfully. {0} records processed.", "FailureTemplate": "Import failed with error: {0}"}, "Performance": {"Enabled": true, "LogDetailedTimings": false, "MemoryThresholdMB": 1000, "ProcessingTimeThresholdMinutes": 10}}, "MemberImport": {"MaxFileSizeBytes": 10485760, "MaxRowCount": 10000, "BatchSize": 500, "ValidateHeaders": true, "RequiredHeaders": ["FirstName", "LastName"], "OptionalHeaders": ["Email", "Phone", "DateOfBirth", "Address", "City", "PostalCode", "Gender", "Province", "PhoneType", "RegistrationType"]}}