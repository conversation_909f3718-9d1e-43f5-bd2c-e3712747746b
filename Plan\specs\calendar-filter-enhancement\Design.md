# Design – Calendar Filter Enhancement

## Current Flow
1. UI dropdown passes a **single** `categoryId` to the endpoint `GET /Admin/GetCalendarEvents` (also used by Public & Member controllers).
2. `EventService.GetCalendarEventsAsync(start, end, publishedOnly, categoryId)` filters with:
```csharp
if (categoryId.HasValue)
    query = query.Where(e => e.EventCategoryId == categoryId.Value);
```

## Problem Statement
Some events are stored under a hybrid category **Pratique First Shift**. Users, however, expect this hybrid to appear under either primary category (**First Shift** or **Pratique**) without having to pick a separate filter.

## Approach 1 – Server-Side Category Map (RECOMMENDED)
1. Introduce `ICategoryFilterService` (or static helper) that returns **List<int> GetFilterCategoryIds(int selectedCategoryId)**.
2. Populate an **in-memory map**:
   * FirstShift → {FirstShiftId, PratiqueFirstShiftId}
   * Pratique → {PratiqueId, PratiqueFirstShiftId}
   * default → {selectedCategoryId}
3. Refactor `EventService.GetCalendarEventsAsync`:
```csharp
var categories = _categoryFilter.GetFilterCategoryIds(categoryId.Value);
query = query.Where(e => categories.Contains(e.EventCategoryId));
```
4. No front-end changes – UI still sends one `categoryId`.

### Pros
+ Minimal changes to API surface & JS.
+ Centralised rule set; easy to extend.
+ Fast to implement; caching keeps it performant.

### Cons
- Additional lookup on each request (mitigated by caching).
- API still pretends to accept a single category even though logic is composite.

## Approach 2 – Multi-Id Query Parameter
1. Change endpoint to accept `categoryIds=1,7` (comma-separated).
2. JS `filterEvents()` builds the list when the user clicks First Shift or Pratique.
3. Server splits into list and filters via `Contains`.

### Pros
+ Contract is explicit – endpoint openly supports multi-category filtering.
+ Generic; can support future multi-select UI.

### Cons
- Requires touching every calendar script plus Mobile agenda logic.
- Larger regression risk; more test surface.

## Decision
Proceed with **Approach 1** for fastest safe delivery and least disruption. Leave notes in code pointing to Approach 2 if future multi-select is desired.

## Data Flow (Approach 1)
```mermaid
graph TD
UI["User selects categoryId"] --> CTRL["AdminController.GetCalendarEvents"]
CTRL --> SRV["EventService.GetCalendarEventsAsync"]
SRV --> MAP["CategoryFilterService"]
MAP --> SRV
SRV --> DB[(Events)]
DB --> SRV
SRV --> CTRL
CTRL --> UI
```

## Implementation Notes
* Cache the mapping using `IMemoryCache` or a static dictionary (small size).
* Ensure seeding/migration sets `IsActive` on **Pratique First Shift** category.
* Unit test `CategoryFilterService` for each rule.
* Integration test `GetCalendarEvents` for both filters.
* E2E test clicking dropdown on Admin calendar. 