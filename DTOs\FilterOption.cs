namespace ParaHockeyApp.DTOs
{
    /// <summary>
    /// Represents a single filter option with display text, value, and optional count
    /// Used for populating dynamic filter dropdowns
    /// </summary>
    public class FilterOption
    {
        /// <summary>
        /// The actual value to be used in filtering (e.g., ID, code, or exact text)
        /// </summary>
        public string Value { get; set; } = string.Empty;

        /// <summary>
        /// The display text shown to the user in the dropdown
        /// </summary>
        public string Display { get; set; } = string.Empty;

        /// <summary>
        /// Optional count of items matching this filter option
        /// Useful for showing how many results each filter option will return
        /// </summary>
        public int? Count { get; set; }

        /// <summary>
        /// Creates a new FilterOption with the specified value and display text
        /// </summary>
        /// <param name="value">The filter value</param>
        /// <param name="display">The display text</param>
        /// <param name="count">Optional count of matching items</param>
        public FilterOption(string value, string display, int? count = null)
        {
            Value = value;
            Display = display;
            Count = count;
        }

        /// <summary>
        /// Parameterless constructor for serialization
        /// </summary>
        public FilterOption() { }
    }
}