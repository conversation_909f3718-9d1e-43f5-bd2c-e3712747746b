using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Localization;
using Microsoft.Extensions.Logging;
using ParaHockeyApp.Services;
using ParaHockey.Models.ViewModels;
using System.Threading.Tasks;
using System.Linq;
using ParaHockeyApp.Models.Entities;
using ParaHockeyApp.Resources;
using ParaHockeyApp.DTOs;

namespace ParaHockey.Controllers
{
    [AllowAnonymous]
    public class EventsController : Controller
    {
        private readonly IEventService _eventService;
        private readonly IUserContextService _userContextService;
        private readonly IStringLocalizer<SharedResourceMarker> _localizer;
        private readonly ILogger<EventsController> _logger;

        public EventsController(
            IEventService eventService,
            IUserContextService userContextService,
            IStringLocalizer<SharedResourceMarker> localizer,
            ILogger<EventsController> logger)
        {
            _eventService = eventService;
            _userContextService = userContextService;
            _localizer = localizer;
            _logger = logger;
        }

        [HttpGet]
        [Route("Events/Subscribe")]
        [Route("MemberPortal/Subscribe")]
        public async Task<IActionResult> Subscribe()
        {
            var upcomingEvents = await _eventService.GetSubscribableEventsAsync();
            var eventCategories = await _eventService.GetAllEventCategoriesSortedAsync();
            
            var eventViewModels = upcomingEvents.Select(e => new EventViewModel
            {
                Id = e.Id,
                Title = e.Title,
                Description = e.Description,
                StartDate = e.StartDate,
                EndDate = e.EndDate,
                Location = e.Location,
                IsAllDay = e.IsAllDay,
                RequiresRegistration = e.RequiresRegistration,
                MaxParticipants = e.MaxParticipants,
                RegistrationDeadline = e.RegistrationDeadline,
                IsPublished = e.IsPublished,
                CurrentRegistrations = e.CurrentRegistrations,
                AvailableSpots = e.AvailableSpots,
                IsFull = e.IsFull,
                IsRegistrationOpen = e.IsRegistrationOpen,
                DateRangeDisplay = e.DateRangeDisplay,
                CategoryName = e.EventCategory?.DisplayNameKey != null ? _localizer[e.EventCategory.DisplayNameKey] : null,
                CategoryColor = e.EventCategory?.Color,
                ContactPerson = e.ContactPerson,
                ContactEmail = e.ContactEmail,
                ContactPhone = e.ContactPhone,
                IsUserRegistered = false // Will be set if user is logged in
            }).ToList(); // CRITICAL: Materialize the enumerable so we can modify IsUserRegistered

            // Check if user is registered for events (if logged in)
            var userContext = _userContextService.GetCurrentUser();
            _logger.LogInformation("🔍 EventsController.Subscribe - UserContext.MemberId: {MemberId}", userContext.MemberId?.ToString() ?? "NULL");
            _logger.LogInformation("🔍 EventsController.Subscribe - UserContext.IsAdmin: {IsAdmin}", userContext.IsAdmin);
            
            if (userContext.MemberId.HasValue)
            {
                _logger.LogInformation("🔍 Member detected - checking registrations for MemberId: {MemberId}", userContext.MemberId.Value);
                var userRegistrations = await _eventService.GetMemberRegistrationsAsync(userContext.MemberId.Value);
                var registeredEventIds = userRegistrations.Select(r => r.EventId).ToHashSet();
                
                _logger.LogInformation("🔍 Found {Count} registered events: [{EventIds}]", 
                    registeredEventIds.Count, string.Join(", ", registeredEventIds));
                
                foreach (var eventVm in eventViewModels)
                {
                    bool wasRegistered = eventVm.IsUserRegistered;
                    eventVm.IsUserRegistered = registeredEventIds.Contains(eventVm.Id);
                    
                    if (eventVm.IsUserRegistered)
                    {
                        _logger.LogInformation("🔍 Event {EventId} '{EventTitle}' - SETTING IsUserRegistered = TRUE (was {WasRegistered})", eventVm.Id, eventVm.Title, wasRegistered);
                    }
                    else
                    {
                        _logger.LogInformation("🔍 Event {EventId} '{EventTitle}' - IsUserRegistered = FALSE", eventVm.Id, eventVm.Title);
                    }
                }
            }
            else
            {
                _logger.LogInformation("🔍 No MemberId detected - user is not a logged-in member");
            }

            // Categories are already sorted from the service method
            var sortedCategories = eventCategories
                .Select(c => new CategoryFilterViewModel
                {
                    Id = c.Id,
                    Name = _localizer[c.DisplayNameKey],
                    Color = c.Color,
                    DisplayNameKey = c.DisplayNameKey
                })
                .ToList();

            var viewModel = new EventSubscribeViewModel
            {
                UpcomingEvents = eventViewModels,
                CalendarOptions = new CalendarOptionsViewModel
                {
                    ShowFilters = true,
                    IsReadOnly = true,
                    IsMiniView = true,
                    Categories = sortedCategories
                }
            };

            // Set authentication state for JavaScript
            ViewBag.HasMemberSession = userContext.MemberId.HasValue;

            return View(viewModel);
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        [Route("Events/Join/{eventId}")]
        public async Task<IActionResult> Join(int eventId)
        {
            if (!User.Identity.IsAuthenticated)
            {
                // Store the return URL
                TempData["ReturnUrl"] = Url.Action("Subscribe", "Events");
                return RedirectToAction("Login", "Members");
            }

            var userContext = _userContextService.GetCurrentUser();
            if (!userContext.MemberId.HasValue)
            {
                TempData["ErrorMessage"] = _localizer["MemberAccountRequired"];
                return RedirectToAction("Subscribe");
            }
            
            var memberId = userContext.MemberId.Value;
            
            // Check if already registered
            var existingRegistration = await _eventService.GetEventRegistrationAsync(eventId, memberId);
            if (existingRegistration != null)
            {
                TempData["ErrorMessage"] = _localizer["AlreadyRegisteredForEvent"];
                return RedirectToAction("Subscribe");
            }

            // Register for the event
            try
            {
                var registration = await _eventService.RegisterMemberForEventAsync(eventId, memberId);
                TempData["SuccessMessage"] = _localizer["EventSubscriptionSuccess"];
            }
            catch (Exception ex)
            {
                TempData["ErrorMessage"] = _localizer["EventSubscriptionError"];
            }

            return RedirectToAction("Subscribe");
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        [Route("Events/Leave/{eventId}")]
        public async Task<IActionResult> Leave(int eventId)
        {
            if (!User.Identity.IsAuthenticated)
            {
                return RedirectToAction("Login", "Members");
            }

            var userContext = _userContextService.GetCurrentUser();
            if (!userContext.MemberId.HasValue)
            {
                TempData["ErrorMessage"] = _localizer["MemberAccountRequired"];
                return RedirectToAction("Subscribe");
            }
            
            var memberId = userContext.MemberId.Value;
            
            try
            {
                var result = await _eventService.CancelEventRegistrationAsync(eventId, memberId);
                if (result)
                {
                    TempData["SuccessMessage"] = _localizer["EventUnregistrationSuccess"];
                }
                else
                {
                    TempData["ErrorMessage"] = _localizer["EventUnregistrationError"];
                }
            }
            catch (Exception ex)
            {
                TempData["ErrorMessage"] = _localizer["EventUnregistrationError"];
                _logger.LogError(ex, "Error unregistering member {MemberId} from event {EventId}", memberId, eventId);
            }

            return RedirectToAction("Subscribe");
        }

        [HttpPost]
        [Route("Events/JoinAjax")]
        public async Task<IActionResult> JoinAjax([FromBody] EventRegistrationRequest request)
        {
            // DEBUG: Log authentication state for event registration
            _logger.LogInformation("🔍 JoinAjax - User.Identity.IsAuthenticated: {IsAuthenticated}", User.Identity.IsAuthenticated);
            _logger.LogInformation("🔍 JoinAjax - EventId: {EventId}", request.EventId);
            
            var userContext = _userContextService.GetCurrentUser();
            
            // DEBUG: Log user context for event registration
            _logger.LogInformation("🔍 JoinAjax - UserContext.IsSystem: {IsSystem}", userContext.IsSystem);
            _logger.LogInformation("🔍 JoinAjax - UserContext.MemberId: {MemberId}", userContext.MemberId?.ToString() ?? "NULL");
            
            // Check authentication - either ASP.NET Core Identity OR valid member session
            if ((!User.Identity.IsAuthenticated) && userContext.IsSystem)
            {
                _logger.LogInformation("🔍 JoinAjax - Authentication failed: No Identity auth and no member session");
                return Json(new EventRegistrationResponse
                {
                    Success = false,
                    Error = _localizer["AuthenticationRequired"]
                });
            }
            
            // Admin protection
            if (userContext.IsAdmin)
            {
                return Json(new EventRegistrationResponse
                {
                    Success = false,
                    Error = _localizer["AdminCannotRegister"]
                });
            }
                
            if (!userContext.MemberId.HasValue)
            {
                return Json(new EventRegistrationResponse
                {
                    Success = false,
                    Error = _localizer["MemberAccountRequired"]
                });
            }

            try 
            {
                var registration = await _eventService.RegisterMemberForEventAsync(
                    request.EventId, userContext.MemberId.Value, request.Notes, request.GuestCount);
                    
                return Json(new EventRegistrationResponse
                { 
                    Success = true, 
                    Message = _localizer["EventSubscriptionSuccess"],
                    Registration = new EventRegistrationInfo
                    {
                        Id = registration.Id,
                        Status = registration.Status.ToString(),
                        RegistrationDate = registration.RegistrationDate,
                        GuestCount = registration.GuestCount,
                        Notes = registration.MemberNotes
                    }
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error registering member {MemberId} for event {EventId}", 
                    userContext.MemberId.Value, request.EventId);
                return Json(new EventRegistrationResponse
                {
                    Success = false,
                    Error = _localizer["EventSubscriptionError"]
                });
            }
        }

        [HttpPost]
        [Route("Events/LeaveAjax")]
        public async Task<IActionResult> LeaveAjax([FromBody] EventUnregistrationRequest request)
        {
            var userContext = _userContextService.GetCurrentUser();
            
            // Check authentication - either ASP.NET Core Identity OR valid member session
            if ((!User.Identity.IsAuthenticated) && userContext.IsSystem)
            {
                return Json(new EventRegistrationResponse
                {
                    Success = false,
                    Error = _localizer["AuthenticationRequired"]
                });
            }
            
            // Admin protection
            if (userContext.IsAdmin)
            {
                return Json(new EventRegistrationResponse
                {
                    Success = false,
                    Error = _localizer["AdminCannotRegister"]
                });
            }
                
            if (!userContext.MemberId.HasValue)
            {
                return Json(new EventRegistrationResponse
                {
                    Success = false,
                    Error = _localizer["MemberAccountRequired"]
                });
            }

            try 
            {
                var result = await _eventService.CancelEventRegistrationAsync(request.EventId, userContext.MemberId.Value);
                
                if (result)
                {
                    return Json(new EventRegistrationResponse
                    { 
                        Success = true, 
                        Message = _localizer["EventUnregistrationSuccess"]
                    });
                }
                else
                {
                    return Json(new EventRegistrationResponse
                    {
                        Success = false,
                        Error = _localizer["EventUnregistrationError"]
                    });
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error unregistering member {MemberId} from event {EventId}", 
                    userContext.MemberId.Value, request.EventId);
                return Json(new EventRegistrationResponse
                {
                    Success = false,
                    Error = _localizer["EventUnregistrationError"]
                });
            }
        }

        [HttpPost]
        [Route("Events/UnsubscribeByRegistrationId")]
        public async Task<IActionResult> UnsubscribeByRegistrationId([FromBody] EventUnsubscribeByRegistrationRequest request)
        {
            var userContext = _userContextService.GetCurrentUser();
            
            // Check authentication - either ASP.NET Core Identity OR valid member session
            if ((!User.Identity.IsAuthenticated) && userContext.IsSystem)
            {
                return Json(new EventRegistrationResponse
                {
                    Success = false,
                    Error = _localizer["AuthenticationRequired"]
                });
            }
            
            // Admin protection
            if (userContext.IsAdmin)
            {
                return Json(new EventRegistrationResponse
                {
                    Success = false,
                    Error = _localizer["AdminCannotRegister"]
                });
            }
                
            if (!userContext.MemberId.HasValue)
            {
                return Json(new EventRegistrationResponse
                {
                    Success = false,
                    Error = _localizer["MemberAccountRequired"]
                });
            }

            try 
            {
                // Get the registration to ensure it belongs to the current member
                var registration = await _eventService.GetEventRegistrationByIdAsync(request.RegistrationId);
                if (registration == null)
                {
                    return Json(new EventRegistrationResponse
                    {
                        Success = false,
                        Error = "RegistrationNotFound"
                    });
                }

                // Verify the registration belongs to the current member
                if (registration.MemberId != userContext.MemberId.Value)
                {
                    return Json(new EventRegistrationResponse
                    {
                        Success = false,
                        Error = "UnauthorizedAccess"
                    });
                }

                // Cancel the registration
                var result = await _eventService.CancelEventRegistrationAsync(registration.EventId, userContext.MemberId.Value);
                
                if (result)
                {
                    return Json(new EventRegistrationResponse
                    { 
                        Success = true, 
                        Message = _localizer["EventUnregistrationSuccess"]
                    });
                }
                else
                {
                    return Json(new EventRegistrationResponse
                    {
                        Success = false,
                        Error = _localizer["EventUnregistrationError"]
                    });
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error unregistering member {MemberId} from registration {RegistrationId}", 
                    userContext.MemberId.Value, request.RegistrationId);
                return Json(new EventRegistrationResponse
                {
                    Success = false,
                    Error = _localizer["EventUnregistrationError"]
                });
            }
        }
    }
}