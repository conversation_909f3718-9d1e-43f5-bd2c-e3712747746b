﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace ParaHockeyApp.Migrations
{
    /// <inheritdoc />
    public partial class UpdateMemberLogStructure : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "IsActive",
                table: "MemberLogs");

            migrationBuilder.RenameColumn(
                name: "LogType",
                table: "MemberLogs",
                newName: "EditorNum");

            migrationBuilder.RenameColumn(
                name: "Id",
                table: "MemberLogs",
                newName: "NoLog");

            migrationBuilder.AddColumn<string>(
                name: "EditorName",
                table: "MemberLogs",
                type: "nvarchar(101)",
                maxLength: 101,
                nullable: false,
                defaultValue: "");

            migrationBuilder.CreateIndex(
                name: "IX_MemberLogs_EditorNum",
                table: "MemberLogs",
                column: "EditorNum");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropIndex(
                name: "IX_MemberLogs_EditorNum",
                table: "MemberLogs");

            migrationBuilder.DropColumn(
                name: "EditorName",
                table: "MemberLogs");

            migrationBuilder.RenameColumn(
                name: "EditorNum",
                table: "MemberLogs",
                newName: "LogType");

            migrationBuilder.RenameColumn(
                name: "NoLog",
                table: "MemberLogs",
                newName: "Id");

            migrationBuilder.AddColumn<bool>(
                name: "IsActive",
                table: "MemberLogs",
                type: "bit",
                nullable: false,
                defaultValue: false);
        }
    }
}
