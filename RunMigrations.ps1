# PowerShell script to run Entity Framework migrations on SIMBA server
# Run this script ON THE SIMBA SERVER

Write-Host "=== ParaHockey Database Migration Script ===" -ForegroundColor Green

# Check if databases exist
Write-Host "`nChecking if databases exist..." -ForegroundColor Yellow

try {
    # Test database connection
    $testConnection = "Server=SIMBA\SQLEXPRESS;Database=ParaHockeyDB_TEST;Integrated Security=true;TrustServerCertificate=true;Encrypt=false;"
    $prodConnection = "Server=SIMBA\SQLEXPRESS;Database=ParaHockeyDB;Integrated Security=true;TrustServerCertificate=true;Encrypt=false;"
    
    Write-Host "Test DB Connection String: $testConnection" -ForegroundColor Cyan
    Write-Host "Prod DB Connection String: $prodConnection" -ForegroundColor Cyan

    # Check if .NET EF tools are installed
    Write-Host "`nChecking .NET EF tools..." -ForegroundColor Yellow
    dotnet tool list -g | findstr "dotnet-ef"
    
    if ($LASTEXITCODE -ne 0) {
        Write-Host "Installing .NET EF tools..." -ForegroundColor Yellow
        dotnet tool install --global dotnet-ef
    }

    # Run migrations for Test environment
    Write-Host "`n=== Running Test Database Migration ===" -ForegroundColor Green
    Set-Location "C:\inetpub\wwwroot\ParaHockey-Test"
    Write-Host "Current directory: $(Get-Location)" -ForegroundColor Cyan
    
    # List files to verify we're in the right place
    Write-Host "Files in directory:" -ForegroundColor Cyan
    Get-ChildItem -Name "*.dll" | Select-Object -First 5
    
    dotnet ef database update --connection $testConnection --verbose
    
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✅ Test database migration completed successfully!" -ForegroundColor Green
    }
    else {
        Write-Host "❌ Test database migration failed!" -ForegroundColor Red
    }

    # Run migrations for Production environment
    Write-Host "`n=== Running Production Database Migration ===" -ForegroundColor Green
    Set-Location "C:\inetpub\wwwroot\ParaHockey-Production"
    Write-Host "Current directory: $(Get-Location)" -ForegroundColor Cyan
    
    dotnet ef database update --connection $prodConnection --verbose
    
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✅ Production database migration completed successfully!" -ForegroundColor Green
    }
    else {
        Write-Host "❌ Production database migration failed!" -ForegroundColor Red
    }

    Write-Host "`n=== Migration Complete ===" -ForegroundColor Green
    Write-Host "Next steps:" -ForegroundColor Yellow
    Write-Host "1. Test the websites: http://localhost:8080 (Test) and http://localhost:8081 (Production)" -ForegroundColor White
    Write-Host "2. Check if tables were created in both databases" -ForegroundColor White
    Write-Host "3. Add test data if needed" -ForegroundColor White

}
catch {
    Write-Host "❌ Error occurred: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host "`nPress any key to continue..." -ForegroundColor Yellow
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown") 