# Design: Event Creation & Visibility Defaults

## 1. Architecture

The implementation will be split between the frontend (client-side JavaScript for the admin UI) and the backend (`EventService` for core business logic and data access).

### 1.1. Frontend (Admin Calendar UI)
- **File:** `Views/Admin/Calendar.cshtml` (or its associated JavaScript file).
- **Logic:**
    - An event listener will be attached to the "Category" dropdown in the new/edit event modal.
    - When the category changes, a JavaScript function will apply the default states to the `Requires Registration` and `Publish Event` checkboxes based on the selected category's name or ID.
    - This provides immediate visual feedback to the admin user as specified in **NFR-2.1**.

### 1.2. Backend (Service Layer)
- **File:** `Services/EventService.cs`
- **Logic:**
    - A new private method, `ApplyCategoryDefaults(Event eventEntity)`, will be created. This method will contain the core logic for setting `IsPublished` and `RequiresRegistration` based on the `EventCategoryId`.
    - This method will be called from:
        - `CreateEventAsync(Event eventEntity)`: To apply defaults when a new event is created through the UI.
        - The event import method (e.g., `ImportEventsFromCsvAsync`): To apply the same defaults during bulk import, ensuring consistency as per **NFR-2.2**.
- **File:** `Models/Entities/Event.cs`
- **Logic:**
    - The existing `IsRegistrationOpen` property will be used. We must verify its logic correctly checks the registration deadline and event capacity.

### 1.3. Backend (Controllers)
- **Files:** `Controllers/HomeController.cs`, `Controllers/MembersController.cs`, `Controllers/EventsController.cs`
- **Logic:**
    - The `GetCalendarEventsJsonAsync` method in `EventService` already supports a `publishedOnly` flag. We will ensure this is set to `true` for all calls from non-admin controllers.
    - The `EventsController.Subscribe` action will have its data retrieval logic updated to filter for events where `IsRegistrationOpen` is `true`.

## 2. Data Flow

1.  **Admin UI (New Event):**
    - Admin opens the "New Event" modal.
    - Admin selects a category from the dropdown.
    - JavaScript event listener fires -> `applyCategoryDefaults.js` function reads the category.
    - The function sets the `checked` property of the two checkboxes.
    - Admin saves the event. The form data (including the final state of the checkboxes) is POSTed to the server.
    - `AdminController` calls `EventService.CreateEventAsync`.

2.  **Backend (Event Creation):**
    - `EventService.CreateEventAsync` receives the new event entity.
    - It calls the internal `ApplyCategoryDefaults` method to ensure server-side enforcement of the rules.
    - The event is saved to the database.

3.  **Backend (Event Import):**
    - An admin uploads a CSV/Excel file.
    - The import logic in `EventService` processes each row.
    - For each new event created from a row, the `ApplyCategoryDefaults` method is called before saving it to the database.

4.  **Frontend (Subscribe Page):**
    - User navigates to `/Events/Subscribe`.
    - `EventsController.Subscribe` action calls a method in `EventService` (e.g., `GetSubscribableEventsAsync`).
    - This service method queries the database for events WHERE `IsPublished == true` AND `RegistrationDeadline > DateTime.Now` AND `CurrentRegistrations < MaxParticipants`.
    - The filtered list of events is returned to the view.

## 3. Diagram (Sequence)

```mermaid
sequenceDiagram
    participant Admin
    participant AdminUI as Admin Calendar UI (JS)
    participant AdminController
    participant EventService
    participant Database

    Admin->>AdminUI: Select "Tentatif" Category
    activate AdminUI
    AdminUI->>AdminUI: applyDefaults(): Uncheck checkboxes
    deactivate AdminUI
    Admin->>AdminUI: Click Save
    AdminUI->>AdminController: POST /Admin/CreateEvent
    activate AdminController
    AdminController->>EventService: CreateEventAsync(event)
    activate EventService
    EventService->>EventService: ApplyCategoryDefaults(event)
    EventService->>Database: Save Event
    deactivate EventService
    deactivate AdminController
```
