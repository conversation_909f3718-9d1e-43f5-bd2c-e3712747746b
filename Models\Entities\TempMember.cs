using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace ParaHockeyApp.Models.Entities
{
    /// <summary>
    /// Temporary staging table for imported member data before validation and creation
    /// </summary>
    public class TempMember : BaseEntity
    {
        /// <summary>
        /// Unique identifier for this temp member record
        /// </summary>
        public Guid TempMemberId { get; set; } = Guid.NewGuid();

        /// <summary>
        /// Reference to the import batch this record belongs to (BaseEntity.Id)
        /// </summary>
        [Required]
        public int ImportBatchId { get; set; }

        /// <summary>
        /// HCR number from the CSV file that links all related records
        /// </summary>
        [StringLength(50)]
        public string? HcrNumber { get; set; }

        // === MEMBER FIELDS (matching Members table structure) ===

        /// <summary>
        /// Member's first name
        /// </summary>
        [Required]
        [StringLength(50)]
        public string FirstName { get; set; } = string.Empty;

        /// <summary>
        /// Member's last name
        /// </summary>
        [Required]
        [StringLength(50)]
        public string LastName { get; set; } = string.Empty;

        /// <summary>
        /// Date of birth
        /// </summary>
        public DateTime? DateOfBirth { get; set; }

        /// <summary>
        /// Email address
        /// </summary>
        [StringLength(254)]
        public string? Email { get; set; }

        /// <summary>
        /// Phone number
        /// </summary>
        [StringLength(20)]
        public string? Phone { get; set; }

        /// <summary>
        /// Street address
        /// </summary>
        [StringLength(200)]
        public string? Address { get; set; }

        /// <summary>
        /// City
        /// </summary>
        [StringLength(100)]
        public string? City { get; set; }

        /// <summary>
        /// Postal code
        /// </summary>
        [StringLength(10)]
        public string? PostalCode { get; set; }

        // === LOOKUP FIELD VALUES (stored as text during import) ===

        /// <summary>
        /// Gender as text from CSV (e.g., "Garçon/Homme", "Fille/Femme")
        /// </summary>
        [StringLength(50)]
        public string? GenderText { get; set; }

        /// <summary>
        /// Province as text from CSV (e.g., "QC", "ON")
        /// </summary>
        [StringLength(50)]
        public string? ProvinceText { get; set; }

        /// <summary>
        /// Phone type as text (will default to "Mobile")
        /// </summary>
        [StringLength(50)]
        public string? PhoneTypeText { get; set; }

        /// <summary>
        /// Registration type as text (will be determined by age-based logic)
        /// </summary>
        [StringLength(50)]
        public string? RegistrationTypeText { get; set; }

        // === RESOLVED LOOKUP IDs (set during validation) ===

        /// <summary>
        /// Resolved gender ID
        /// </summary>
        public int? GenderId { get; set; }

        /// <summary>
        /// Resolved province ID
        /// </summary>
        public int? ProvinceId { get; set; }

        /// <summary>
        /// Resolved phone type ID
        /// </summary>
        public int? PhoneTypeId { get; set; }

        /// <summary>
        /// Resolved registration type ID
        /// </summary>
        public int? RegistrationTypeId { get; set; }

        // === DUPLICATE DETECTION ===

        /// <summary>
        /// If this is a duplicate, the ID of the existing member
        /// </summary>
        public int? ExistingMemberId { get; set; }

        // === PROCESSING STATUS ===

        /// <summary>
        /// Current processing status
        /// </summary>
        [Required]
        public TempMemberStatus Status { get; set; } = TempMemberStatus.Imported;

        /// <summary>
        /// Validation errors in JSON format
        /// </summary>
        public string? ValidationErrorsJson { get; set; }

        /// <summary>
        /// Original raw data from CSV in JSON format for traceability
        /// </summary>
        [Required]
        public string RawSourceJson { get; set; } = string.Empty;

        // === RELATIONSHIP DATA ===

        /// <summary>
        /// Parent information in JSON format (for junior members)
        /// </summary>
        public string? ParentData { get; set; }

        /// <summary>
        /// Emergency contact information in JSON format
        /// </summary>
        public string? EmergencyContactData { get; set; }

        // === NAVIGATION PROPERTIES ===

        /// <summary>
        /// Navigation property to the import batch
        /// </summary>
        public virtual MemberImportBatch ImportBatch { get; set; } = null!;

        /// <summary>
        /// Navigation property to existing member (if duplicate)
        /// </summary>
        public virtual Member? ExistingMember { get; set; }

        /// <summary>
        /// Navigation property to resolved gender
        /// </summary>
        public virtual Gender? Gender { get; set; }

        /// <summary>
        /// Navigation property to resolved province
        /// </summary>
        public virtual Province? Province { get; set; }

        /// <summary>
        /// Navigation property to resolved phone type
        /// </summary>
        public virtual PhoneType? PhoneType { get; set; }

        /// <summary>
        /// Navigation property to resolved registration type
        /// </summary>
        public virtual RegistrationType? RegistrationType { get; set; }

        // === HELPER PROPERTIES ===

        /// <summary>
        /// Full name for display
        /// </summary>
        [NotMapped]
        public string FullName => $"{FirstName} {LastName}";

        /// <summary>
        /// Whether this record has validation errors
        /// </summary>
        [NotMapped]
        public bool HasValidationErrors => !string.IsNullOrEmpty(ValidationErrorsJson);

        /// <summary>
        /// Age calculated from date of birth (if available)
        /// </summary>
        [NotMapped]
        public int? Age => DateOfBirth?.Year != null 
            ? DateTime.Now.Year - DateOfBirth.Value.Year - (DateTime.Now.DayOfYear < DateOfBirth.Value.DayOfYear ? 1 : 0)
            : null;
    }
}