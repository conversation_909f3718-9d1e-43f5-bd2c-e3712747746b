﻿// Auto-fill registration form for Jean<PERSON> Roy7 (Coach)
document.getElementById('FirstName').value = 'Jean7';
document.getElementById('LastName').value = 'Roy7';
document.getElementById('Email').value = '<EMAIL>';
document.getElementById('Phone').value = '(*************';
document.getElementById('DateOfBirth').value = '1976-07-04';
document.getElementById('Address').value = '123 rue Principale';
document.getElementById('City').value = 'Sherbrooke';
document.getElementById('PostalCode').value = 'M9W 9I6';
document.getElementById('RegistrationTypeId').value = '4';
document.getElementById('GenderId').value = '1';
document.getElementById('ProvinceId').value = '1';
document.getElementById('PhoneTypeId').value = '1';
// Emergency contact for non-Junior
document.getElementById('EmergencyContact_FirstName').value = 'Nathalie_Emergency7';
document.getElementById('EmergencyContact_LastName').value = 'Roy7';
document.getElementById('EmergencyContact_Email').value = '<EMAIL>';
document.getElementById('EmergencyContact_Phone').value = '(*************';
