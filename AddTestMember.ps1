# Add test member to Production
Write-Host "Adding test member to Production..." -ForegroundColor Yellow

$prodConn = "Server=SIMBA\SQLEXPRESS;Database=ParaHockeyDB;User Id=ParaHockeyUser;Password=***************;TrustServerCertificate=True;"

$conn = New-Object System.Data.SqlClient.SqlConnection($prodConn)
$conn.Open()

$cmd = $conn.CreateCommand()
$cmd.CommandText = "SELECT COUNT(*) FROM Members"
$currentCount = $cmd.ExecuteScalar()

Write-Host "Current members: $currentCount"

if ($currentCount -eq 0) {
    Write-Host "Creating test member..."
    
    $cmd.CommandText = "INSERT INTO Members (FirstName, LastName, Email, Phone, DateOfBirth, Address, City, PostalCode, ProvinceId, RegistrationTypeId, IsActive, DateCreated, DateModified, CreatedBy, ModifiedBy) VALUES ('Test', 'User', '<EMAIL>', '(*************', '1990-01-01', '123 Test St', 'Montreal', 'H1A 1A1', (SELECT Id FROM Provinces WHERE Code = 'QC'), (SELECT TOP 1 Id FROM RegistrationTypes), 1, GETUTCDATE(), GETUTCDATE(), 'SYSTEM', 'SYSTEM')"
    $cmd.ExecuteNonQuery()
    
    $cmd.CommandText = "SELECT COUNT(*) FROM Members"
    $newCount = $cmd.ExecuteScalar()
    Write-Host "New count: $newCount" -ForegroundColor Green
} else {
    Write-Host "Members already exist" -ForegroundColor Green
}

$conn.Close()
Write-Host "Done!"