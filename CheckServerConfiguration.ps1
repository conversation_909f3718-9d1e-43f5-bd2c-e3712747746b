# ParaHockey Server Configuration Check Script
# Run this on the SIMBA server to diagnose deployment issues

Write-Host "🏒 ParaHockey Server Configuration Check" -ForegroundColor Green
Write-Host "==========================================" -ForegroundColor Green

# Check .NET Core Runtime Installation
Write-Host "`n📋 Checking .NET Runtime Installation..." -ForegroundColor Yellow
try {
    $dotnetVersions = dotnet --list-runtimes
    Write-Host "✅ .NET Runtimes found:" -ForegroundColor Green
    $dotnetVersions | ForEach-Object { Write-Host "   $_" }
    
    # Check specifically for ASP.NET Core 8.0
    $aspnetCore8 = $dotnetVersions | Where-Object { $_ -like "*Microsoft.AspNetCore.App 8.*" }
    if ($aspnetCore8) {
        Write-Host "✅ ASP.NET Core 8.0 Runtime: FOUND" -ForegroundColor Green
    }
    else {
        Write-Host "❌ ASP.NET Core 8.0 Runtime: NOT FOUND" -ForegroundColor Red
        Write-Host "   Download: https://dotnet.microsoft.com/download/dotnet/8.0" -ForegroundColor Red
        Write-Host "   Install: ASP.NET Core Runtime 8.0.x - Windows Hosting Bundle" -ForegroundColor Red
    }
}
catch {
    Write-Host "❌ .NET CLI not found or not working" -ForegroundColor Red
}

# Check IIS Application Pools
Write-Host "`n🏊 Checking IIS Application Pools..." -ForegroundColor Yellow
try {
    Import-Module WebAdministration -ErrorAction Stop
    
    $pools = @("ParaHockey-Test", "ParaHockey-Production")
    foreach ($poolName in $pools) {
        $pool = Get-IISAppPool -Name $poolName -ErrorAction SilentlyContinue
        if ($pool) {
            Write-Host "✅ Application Pool '$poolName': EXISTS" -ForegroundColor Green
            Write-Host "   State: $($pool.State)" -ForegroundColor Cyan
            Write-Host "   .NET Version: $($pool.ManagedRuntimeVersion)" -ForegroundColor Cyan
            
            if ($pool.ManagedRuntimeVersion -eq "") {
                Write-Host "   ✅ Correctly set to 'No Managed Code'" -ForegroundColor Green
            }
            else {
                Write-Host "   ❌ Should be 'No Managed Code' for ASP.NET Core!" -ForegroundColor Red
            }
        }
        else {
            Write-Host "❌ Application Pool '$poolName': NOT FOUND" -ForegroundColor Red
        }
    }
}
catch {
    Write-Host "❌ Cannot access IIS WebAdministration module" -ForegroundColor Red
}

# Check Website Folders
Write-Host "`n📁 Checking Website Folders..." -ForegroundColor Yellow
$folders = @(
    "C:\inetpub\ParaHockey\Test",
    "C:\inetpub\ParaHockey\Production"
)

foreach ($folder in $folders) {
    if (Test-Path $folder) {
        Write-Host "✅ Folder exists: $folder" -ForegroundColor Green
        
        # Check for key files
        $dllFile = Join-Path $folder "ParaHockeyApp.dll"
        $webConfig = Join-Path $folder "web.config"
        
        if (Test-Path $dllFile) {
            Write-Host "   ✅ ParaHockeyApp.dll found" -ForegroundColor Green
        }
        else {
            Write-Host "   ❌ ParaHockeyApp.dll missing!" -ForegroundColor Red
        }
        
        if (Test-Path $webConfig) {
            Write-Host "   ✅ web.config found" -ForegroundColor Green
        }
        else {
            Write-Host "   ❌ web.config missing!" -ForegroundColor Red
        }
        
        # Count files
        $fileCount = (Get-ChildItem $folder -Recurse -File | Measure-Object).Count
        Write-Host "   📊 Total files: $fileCount" -ForegroundColor Cyan
    }
    else {
        Write-Host "❌ Folder missing: $folder" -ForegroundColor Red
    }
}

# Check IIS Sites Configuration
Write-Host "`n🌐 Checking IIS Sites..." -ForegroundColor Yellow
try {
    $sites = @("ParaHockey-Test", "ParaHockey-Production")
    foreach ($siteName in $sites) {
        $site = Get-IISSite -Name $siteName -ErrorAction SilentlyContinue
        if ($site) {
            Write-Host "✅ IIS Site '$siteName': EXISTS" -ForegroundColor Green
            Write-Host "   State: $($site.State)" -ForegroundColor Cyan
            Write-Host "   Physical Path: $($site.Applications[0].VirtualDirectories[0].PhysicalPath)" -ForegroundColor Cyan
            
            # Check bindings
            foreach ($binding in $site.Bindings) {
                Write-Host "   Binding: $($binding.Protocol)://$($binding.BindingInformation)" -ForegroundColor Cyan
            }
        }
        else {
            Write-Host "❌ IIS Site '$siteName': NOT FOUND" -ForegroundColor Red
        }
    }
}
catch {
    Write-Host "❌ Cannot access IIS Sites information" -ForegroundColor Red
}

Write-Host "`n🏁 Configuration Check Complete!" -ForegroundColor Green
Write-Host "If you see any ❌ errors above, those need to be fixed first." -ForegroundColor Yellow 