using Microsoft.Extensions.Caching.Memory;
using ParaHockeyApp.Models.Entities;
using System.Text.RegularExpressions;
using System.Text.Json;

namespace ParaHockeyApp.Services;

/// <summary>
/// Implementation of performance optimization service for bundle management,
/// image optimization, critical CSS extraction, and caching strategies.
/// </summary>
public class PerformanceOptimizationService : IPerformanceOptimizationService
{
    private readonly IWebHostEnvironment _environment;
    private readonly IMemoryCache _cache;
    private readonly ILogger<PerformanceOptimizationService> _logger;
    private readonly IConfiguration _configuration;

    public PerformanceOptimizationService(
        IWebHostEnvironment environment,
        IMemoryCache cache,
        ILogger<PerformanceOptimizationService> logger,
        IConfiguration configuration)
    {
        _environment = environment;
        _cache = cache;
        _logger = logger;
        _configuration = configuration;
    }

    /// <summary>
    /// Generates optimized bundle configuration for JavaScript and CSS assets
    /// </summary>
    public async Task<BundleConfiguration> GenerateBundleConfigurationAsync()
    {
        const string cacheKey = "bundle_configuration";
        
        if (_cache.TryGetValue(cacheKey, out BundleConfiguration? cachedConfig))
        {
            return cachedConfig!;
        }

        var config = new BundleConfiguration
        {
            GeneratedAt = DateTime.UtcNow,
            Environment = _environment.EnvironmentName
        };

        // CSS Bundle Configuration
        config.CssBundles = new List<BundleGroup>
        {
            new BundleGroup
            {
                Name = "critical",
                LoadStrategy = BundleLoadStrategy.Critical,
                Priority = 1,
                Files = new List<string>
                {
                    "~/css/shared/variables.css",
                    "~/css/site.css",
                    "~/css/forced-colors.css"
                }
            },
            new BundleGroup
            {
                Name = "enhanced-forms",
                LoadStrategy = BundleLoadStrategy.Deferred,
                Priority = 2,
                Files = new List<string>
                {
                    "~/css/enhanced-forms.css"
                }
            },
            new BundleGroup
            {
                Name = "theme",
                LoadStrategy = BundleLoadStrategy.Deferred,
                Priority = 3,
                Files = new List<string>
                {
                    "~/css/dark-overrides.css",
                    "~/css/parahockey-design-system.css"
                }
            }
        };

        // JavaScript Bundle Configuration
        config.JavaScriptBundles = new List<BundleGroup>
        {
            new BundleGroup
            {
                Name = "core",
                LoadStrategy = BundleLoadStrategy.Critical,
                Priority = 1,
                Files = new List<string>
                {
                    "~/lib/jquery/dist/jquery.min.js",
                    "~/lib/bootstrap/dist/js/bootstrap.bundle.min.js"
                }
            },
            new BundleGroup
            {
                Name = "forms",
                LoadStrategy = BundleLoadStrategy.LazyLoad,
                Priority = 2,
                Dependencies = new List<string> { "core" },
                Files = new List<string>
                {
                    "~/js/enhanced-form-validation.js",
                    "~/lib/jquery-validation/dist/jquery.validate.min.js",
                    "~/lib/jquery-validation-unobtrusive/jquery.validate.unobtrusive.min.js"
                }
            },
            new BundleGroup
            {
                Name = "ui-components",
                LoadStrategy = BundleLoadStrategy.LazyLoad,
                Priority = 3,
                Dependencies = new List<string> { "core" },
                Files = new List<string>
                {
                    "~/js/datepicker-fr.js",
                    "~/js/member-filters-export.js",
                    "~/js/event-details-modal.js"
                }
            },
            new BundleGroup
            {
                Name = "site",
                LoadStrategy = BundleLoadStrategy.Deferred,
                Priority = 4,
                Dependencies = new List<string> { "core" },
                Files = new List<string>
                {
                    "~/js/site.js",
                    "~/js/theme-listener.js"
                }
            }
        };

        // Calculate estimated sizes
        await CalculateBundleSizesAsync(config);

        // Cache for 1 hour
        _cache.Set(cacheKey, config, TimeSpan.FromHours(1));

        _logger.LogInformation("Generated bundle configuration with {CssBundles} CSS bundles and {JsBundles} JS bundles",
            config.CssBundles.Count, config.JavaScriptBundles.Count);

        return config;
    }

    /// <summary>
    /// Extracts critical CSS for above-the-fold content
    /// </summary>
    public async Task<CriticalCssResult> ExtractCriticalCssAsync(string pageName, string url)
    {
        var cacheKey = $"critical_css_{pageName}";
        
        if (_cache.TryGetValue(cacheKey, out CriticalCssResult? cachedResult))
        {
            return cachedResult!;
        }

        var result = new CriticalCssResult
        {
            PageName = pageName,
            ExtractedAt = DateTime.UtcNow
        };

        try
        {
            // Read all CSS files
            var allCss = await ReadAllCssFilesAsync();
            
            // Define critical selectors based on page type
            var criticalSelectors = GetCriticalSelectorsForPage(pageName);
            result.CriticalSelectors = criticalSelectors;

            // Extract critical CSS
            result.CriticalCss = ExtractCriticalSelectorsFromCss(allCss, criticalSelectors);
            result.NonCriticalCss = RemoveCriticalSelectorsFromCss(allCss, criticalSelectors);

            result.CriticalCssSize = System.Text.Encoding.UTF8.GetByteCount(result.CriticalCss);
            result.NonCriticalCssSize = System.Text.Encoding.UTF8.GetByteCount(result.NonCriticalCss);

            // Cache for 2 hours
            _cache.Set(cacheKey, result, TimeSpan.FromHours(2));

            _logger.LogInformation("Extracted critical CSS for {PageName}: {CriticalSize} bytes critical, {NonCriticalSize} bytes non-critical",
                pageName, result.CriticalCssSize, result.NonCriticalCssSize);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to extract critical CSS for page {PageName}", pageName);
            
            // Return empty result on error
            result.CriticalCss = "";
            result.NonCriticalCss = "";
        }

        return result;
    }

    /// <summary>
    /// Optimizes images with responsive generation and WebP support
    /// </summary>
    public async Task<ImageOptimizationResult> OptimizeImageAsync(string imagePath, ImageOptimizationOptions options)
    {
        var result = new ImageOptimizationResult
        {
            OriginalPath = imagePath,
            OptimizedAt = DateTime.UtcNow
        };

        try
        {
            var fullPath = Path.Combine(_environment.WebRootPath, imagePath.TrimStart('~', '/'));
            
            if (!File.Exists(fullPath))
            {
                _logger.LogWarning("Image file not found: {ImagePath}", fullPath);
                return result;
            }

            var fileInfo = new FileInfo(fullPath);
            result.OriginalSize = fileInfo.Length;

            // For now, simulate image optimization since we don't have image processing libraries
            // In a real implementation, you would use libraries like ImageSharp or SkiaSharp
            
            foreach (var width in options.ResponsiveWidths)
            {
                // Simulate WebP variant
                if (options.GenerateWebP)
                {
                    var webpVariant = new OptimizedImageVariant
                    {
                        Path = GenerateOptimizedPath(imagePath, width, "webp", options.OutputDirectory),
                        Format = "webp",
                        Width = width,
                        Height = CalculateProportionalHeight(width, 1200, 800), // Assume 1200x800 original
                        Size = (long)(result.OriginalSize * 0.7), // WebP typically 30% smaller
                        MediaQuery = $"(max-width: {width}px)"
                    };
                    result.Variants.Add(webpVariant);
                }

                // Simulate JPEG variant
                var jpegVariant = new OptimizedImageVariant
                {
                    Path = GenerateOptimizedPath(imagePath, width, "jpg", options.OutputDirectory),
                    Format = "jpeg",
                    Width = width,
                    Height = CalculateProportionalHeight(width, 1200, 800),
                    Size = (long)(result.OriginalSize * 0.85), // JPEG typically 15% smaller with optimization
                    MediaQuery = $"(max-width: {width}px)"
                };
                result.Variants.Add(jpegVariant);
            }

            result.TotalOptimizedSize = result.Variants.Sum(v => v.Size);
            result.CompressionRatio = result.OriginalSize > 0 
                ? (double)result.TotalOptimizedSize / result.OriginalSize 
                : 1.0;

            _logger.LogInformation("Optimized image {ImagePath}: {OriginalSize} -> {OptimizedSize} bytes ({CompressionRatio:P1} compression)",
                imagePath, result.OriginalSize, result.TotalOptimizedSize, 1 - result.CompressionRatio);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to optimize image {ImagePath}", imagePath);
        }

        return result;
    }

    /// <summary>
    /// Analyzes database queries for N+1 problems and optimization opportunities
    /// </summary>
    public async Task<QueryOptimizationResult> AnalyzeDatabaseQueriesAsync()
    {
        var result = new QueryOptimizationResult
        {
            AnalyzedAt = DateTime.UtcNow
        };

        try
        {
            // Analyze common N+1 query patterns in the codebase
            var codeFiles = await GetCSharpFilesAsync();
            
            foreach (var file in codeFiles)
            {
                var content = await File.ReadAllTextAsync(file);
                AnalyzeFileForQueryIssues(file, content, result);
            }

            // Generate recommendations based on found issues
            GenerateQueryOptimizationRecommendations(result);

            result.TotalQueriesAnalyzed = result.Issues.Count;

            _logger.LogInformation("Analyzed {FileCount} files and found {IssueCount} potential query optimization issues",
                codeFiles.Count, result.Issues.Count);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to analyze database queries");
        }

        return result;
    }

    /// <summary>
    /// Generates appropriate cache headers for different resource types
    /// </summary>
    public CacheHeaderConfiguration GetCacheHeaders(string resourceType, string resourcePath)
    {
        var config = new CacheHeaderConfiguration();

        switch (resourceType.ToLowerInvariant())
        {
            case "css":
            case "js":
                config.MaxAge = TimeSpan.FromDays(30);
                config.CacheControl = "public, max-age=2592000, immutable";
                config.IsImmutable = true;
                config.VaryHeaders.Add("Accept-Encoding");
                break;

            case "image":
            case "font":
                config.MaxAge = TimeSpan.FromDays(365);
                config.CacheControl = "public, max-age=31536000, immutable";
                config.IsImmutable = true;
                config.VaryHeaders.Add("Accept");
                break;

            case "html":
                config.MaxAge = TimeSpan.FromMinutes(5);
                config.CacheControl = "public, max-age=300, must-revalidate";
                config.VaryHeaders.AddRange(new[] { "Accept-Language", "Accept-Encoding" });
                break;

            case "api":
                config.MaxAge = TimeSpan.FromMinutes(1);
                config.CacheControl = "public, max-age=60, must-revalidate";
                config.VaryHeaders.Add("Accept");
                break;

            default:
                config.MaxAge = TimeSpan.FromHours(1);
                config.CacheControl = "public, max-age=3600";
                break;
        }

        // Generate ETag based on file path and last modified time
        config.ETag = GenerateETag(resourcePath);

        return config;
    }

    /// <summary>
    /// Validates Core Web Vitals for a given page (simulation)
    /// </summary>
    public async Task<CoreWebVitalsResult> ValidateCoreWebVitalsAsync(string pageName, string url)
    {
        // This is a simulation - in a real implementation, you would use tools like:
        // - Lighthouse CI
        // - WebPageTest API
        // - Chrome DevTools Protocol
        
        var result = new CoreWebVitalsResult
        {
            PageName = pageName,
            Url = url,
            MeasuredAt = DateTime.UtcNow
        };

        // Simulate realistic Core Web Vitals measurements
        result.LargestContentfulPaint = new WebVitalMetric
        {
            Name = "Largest Contentful Paint",
            Value = 1.8, // seconds
            Unit = "s",
            Rating = WebVitalRating.Good,
            Threshold = 2.5
        };

        result.FirstInputDelay = new WebVitalMetric
        {
            Name = "First Input Delay",
            Value = 85, // milliseconds
            Unit = "ms",
            Rating = WebVitalRating.Good,
            Threshold = 100
        };

        result.CumulativeLayoutShift = new WebVitalMetric
        {
            Name = "Cumulative Layout Shift",
            Value = 0.08,
            Unit = "",
            Rating = WebVitalRating.Good,
            Threshold = 0.1
        };

        result.FirstContentfulPaint = new WebVitalMetric
        {
            Name = "First Contentful Paint",
            Value = 1.2,
            Unit = "s",
            Rating = WebVitalRating.Good,
            Threshold = 1.8
        };

        result.TimeToInteractive = new WebVitalMetric
        {
            Name = "Time to Interactive",
            Value = 2.1,
            Unit = "s",
            Rating = WebVitalRating.Good,
            Threshold = 3.8
        };

        // Calculate overall performance score
        result.PerformanceScore = CalculatePerformanceScore(result);

        // Generate recommendations
        result.Recommendations = GeneratePerformanceRecommendations(result);

        _logger.LogInformation("Validated Core Web Vitals for {PageName}: Performance Score {Score}",
            pageName, result.PerformanceScore);

        return result;
    }

    #region Private Helper Methods

    private async Task CalculateBundleSizesAsync(BundleConfiguration config)
    {
        foreach (var bundle in config.CssBundles.Concat(config.JavaScriptBundles))
        {
            long totalSize = 0;
            foreach (var file in bundle.Files)
            {
                var filePath = Path.Combine(_environment.WebRootPath, file.TrimStart('~', '/'));
                if (File.Exists(filePath))
                {
                    var fileInfo = new FileInfo(filePath);
                    totalSize += fileInfo.Length;
                }
            }
            bundle.EstimatedSize = totalSize;
        }
    }

    private async Task<string> ReadAllCssFilesAsync()
    {
        var cssContent = new List<string>();
        var cssDirectory = Path.Combine(_environment.WebRootPath, "css");
        
        if (Directory.Exists(cssDirectory))
        {
            var cssFiles = Directory.GetFiles(cssDirectory, "*.css", SearchOption.AllDirectories);
            foreach (var file in cssFiles)
            {
                cssContent.Add(await File.ReadAllTextAsync(file));
            }
        }

        return string.Join("\n", cssContent);
    }

    private List<string> GetCriticalSelectorsForPage(string pageName)
    {
        // Define critical selectors based on page type
        var commonCritical = new List<string>
        {
            "body", "html", ".container", ".navbar", ".btn", ".form-control",
            "h1", "h2", "h3", ".alert", ".card", ".row", ".col"
        };

        return pageName.ToLowerInvariant() switch
        {
            "home" => commonCritical.Concat(new[] { ".hero", ".jumbotron", ".landing" }).ToList(),
            "register" => commonCritical.Concat(new[] { ".form-group", ".form-label", ".validation-summary" }).ToList(),
            "login" => commonCritical.Concat(new[] { ".login-form", ".form-signin" }).ToList(),
            _ => commonCritical
        };
    }

    private string ExtractCriticalSelectorsFromCss(string css, List<string> criticalSelectors)
    {
        var criticalCss = new List<string>();
        
        foreach (var selector in criticalSelectors)
        {
            var pattern = $@"{Regex.Escape(selector)}\s*\{{[^}}]*\}}";
            var matches = Regex.Matches(css, pattern, RegexOptions.IgnoreCase | RegexOptions.Multiline);
            
            foreach (Match match in matches)
            {
                criticalCss.Add(match.Value);
            }
        }

        return string.Join("\n", criticalCss.Distinct());
    }

    private string RemoveCriticalSelectorsFromCss(string css, List<string> criticalSelectors)
    {
        var nonCriticalCss = css;
        
        foreach (var selector in criticalSelectors)
        {
            var pattern = $@"{Regex.Escape(selector)}\s*\{{[^}}]*\}}";
            nonCriticalCss = Regex.Replace(nonCriticalCss, pattern, "", RegexOptions.IgnoreCase | RegexOptions.Multiline);
        }

        return nonCriticalCss;
    }

    private string GenerateOptimizedPath(string originalPath, int width, string format, string outputDirectory)
    {
        var directory = Path.GetDirectoryName(originalPath) ?? "";
        var filename = Path.GetFileNameWithoutExtension(originalPath);
        var extension = format.StartsWith('.') ? format : $".{format}";
        
        return Path.Combine(directory, outputDirectory, $"{filename}-{width}w{extension}").Replace('\\', '/');
    }

    private int CalculateProportionalHeight(int newWidth, int originalWidth, int originalHeight)
    {
        return (int)Math.Round((double)newWidth * originalHeight / originalWidth);
    }

    private async Task<List<string>> GetCSharpFilesAsync()
    {
        var files = new List<string>();
        var directories = new[] { "Controllers", "Services", "Models" };
        
        foreach (var dir in directories)
        {
            var fullPath = Path.Combine(_environment.ContentRootPath, dir);
            if (Directory.Exists(fullPath))
            {
                files.AddRange(Directory.GetFiles(fullPath, "*.cs", SearchOption.AllDirectories));
            }
        }

        return files;
    }

    private void AnalyzeFileForQueryIssues(string filePath, string content, QueryOptimizationResult result)
    {
        var fileName = Path.GetFileName(filePath);

        // Check for potential N+1 queries
        if (Regex.IsMatch(content, @"foreach\s*\([^)]*\)\s*\{[^}]*\.(Where|First|Single|Any)\(", RegexOptions.IgnoreCase))
        {
            result.Issues.Add(new QueryIssue
            {
                QueryType = "N+1 Query",
                Location = fileName,
                Severity = QueryIssueSeverity.High,
                Description = "Potential N+1 query detected in foreach loop",
                Example = "foreach loop with LINQ queries inside",
                EstimatedImpact = 8
            });
        }

        // Check for missing Include statements
        if (Regex.IsMatch(content, @"\.Where\([^)]*\)\s*\.Select\([^)]*=>[^)]*\.[A-Za-z]+", RegexOptions.IgnoreCase))
        {
            result.Issues.Add(new QueryIssue
            {
                QueryType = "Missing Include",
                Location = fileName,
                Severity = QueryIssueSeverity.Medium,
                Description = "Potential lazy loading issue - consider using Include()",
                Example = "Navigation property access without Include",
                EstimatedImpact = 6
            });
        }

        // Check for inefficient queries
        if (Regex.IsMatch(content, @"\.ToList\(\)\s*\.Where\(", RegexOptions.IgnoreCase))
        {
            result.Issues.Add(new QueryIssue
            {
                QueryType = "Inefficient Query",
                Location = fileName,
                Severity = QueryIssueSeverity.Medium,
                Description = "ToList() called before Where() - filtering should be done in database",
                Example = ".ToList().Where() pattern",
                EstimatedImpact = 5
            });
        }
    }

    private void GenerateQueryOptimizationRecommendations(QueryOptimizationResult result)
    {
        if (result.Issues.Any(i => i.QueryType == "N+1 Query"))
        {
            result.Recommendations.Add(new QueryRecommendation
            {
                Title = "Use Include() to prevent N+1 queries",
                Description = "Replace foreach loops with database queries using Include() for related data",
                CodeExample = "context.Members.Include(m => m.Events).Where(m => m.IsActive)",
                Priority = QueryOptimizationPriority.High,
                AffectedQueries = result.Issues.Where(i => i.QueryType == "N+1 Query").Select(i => i.Location).ToList()
            });
        }

        if (result.Issues.Any(i => i.QueryType == "Inefficient Query"))
        {
            result.Recommendations.Add(new QueryRecommendation
            {
                Title = "Move filtering to database level",
                Description = "Apply Where() clauses before ToList() to filter data in the database",
                CodeExample = "context.Members.Where(m => m.IsActive).ToList()",
                Priority = QueryOptimizationPriority.Medium,
                AffectedQueries = result.Issues.Where(i => i.QueryType == "Inefficient Query").Select(i => i.Location).ToList()
            });
        }
    }

    private string GenerateETag(string resourcePath)
    {
        var fullPath = Path.Combine(_environment.WebRootPath, resourcePath.TrimStart('~', '/'));
        if (File.Exists(fullPath))
        {
            var lastWrite = File.GetLastWriteTimeUtc(fullPath);
            var fileSize = new FileInfo(fullPath).Length;
            return $"\"{lastWrite.Ticks:x}-{fileSize:x}\"";
        }
        return $"\"{DateTime.UtcNow.Ticks:x}\"";
    }

    private int CalculatePerformanceScore(CoreWebVitalsResult result)
    {
        var scores = new List<int>();

        // LCP Score (0-100)
        scores.Add(result.LargestContentfulPaint.Value <= 2.5 ? 100 : 
                  result.LargestContentfulPaint.Value <= 4.0 ? 75 : 50);

        // FID Score (0-100)
        scores.Add(result.FirstInputDelay.Value <= 100 ? 100 : 
                  result.FirstInputDelay.Value <= 300 ? 75 : 50);

        // CLS Score (0-100)
        scores.Add(result.CumulativeLayoutShift.Value <= 0.1 ? 100 : 
                  result.CumulativeLayoutShift.Value <= 0.25 ? 75 : 50);

        return (int)scores.Average();
    }

    private List<PerformanceRecommendation> GeneratePerformanceRecommendations(CoreWebVitalsResult result)
    {
        var recommendations = new List<PerformanceRecommendation>();

        if (result.LargestContentfulPaint.Rating != WebVitalRating.Good)
        {
            recommendations.Add(new PerformanceRecommendation
            {
                Title = "Optimize Largest Contentful Paint",
                Description = "Reduce server response times and optimize critical resources",
                Category = "Loading",
                Impact = 9,
                Implementation = "Implement critical CSS inlining and optimize images"
            });
        }

        if (result.FirstInputDelay.Rating != WebVitalRating.Good)
        {
            recommendations.Add(new PerformanceRecommendation
            {
                Title = "Reduce First Input Delay",
                Description = "Minimize JavaScript execution time and use code splitting",
                Category = "Interactivity",
                Impact = 8,
                Implementation = "Defer non-critical JavaScript and use lazy loading"
            });
        }

        if (result.CumulativeLayoutShift.Rating != WebVitalRating.Good)
        {
            recommendations.Add(new PerformanceRecommendation
            {
                Title = "Minimize Cumulative Layout Shift",
                Description = "Reserve space for images and avoid inserting content above existing content",
                Category = "Visual Stability",
                Impact = 7,
                Implementation = "Add width/height attributes to images and use CSS aspect-ratio"
            });
        }

        return recommendations;
    }

    #endregion
}