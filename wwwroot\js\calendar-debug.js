// Debug script to check calendar initialization
console.log('Calendar debug script loaded');

document.addEventListener('DOMContentLoaded', function() {
    console.log('DOM loaded - checking calendar elements');
    
    const miniCalendar = document.getElementById('miniCalendar');
    console.log('Mini calendar element:', miniCalendar);
    console.log('Mini calendar visible:', miniCalendar ? (miniCalendar.offsetWidth > 0 && miniCalendar.offsetHeight > 0) : false);
    
    console.log('FullCalendar available:', typeof FullCalendar !== 'undefined');
    console.log('Bootstrap available:', typeof bootstrap !== 'undefined');
    console.log('showEventDetailsModal available:', typeof showEventDetailsModal === 'function');
    
    // Check if there are any errors in existing script
    try {
        if (miniCalendar) {
            const testCalendar = new FullCalendar.Calendar(miniCalendar, {
                initialView: 'dayGridMonth',
                events: []
            });
            console.log('Test calendar created successfully');
        }
    } catch (error) {
        console.error('FullCalendar initialization error:', error);
    }
    
    console.log('Calendar debug complete');
});