using System.ComponentModel.DataAnnotations;

namespace ParaHockeyApp.DTOs
{
    /// <summary>
    /// DTO for AJAX event registration requests
    /// </summary>
    public class EventRegistrationRequest
    {
        [Required]
        public int EventId { get; set; }

        [StringLength(500, ErrorMessageResourceName = "ValidationStringLength", ErrorMessageResourceType = typeof(Resources.SharedResource))]
        public string? Notes { get; set; }

        [Range(0, 10, ErrorMessageResourceName = "ValidationRange", ErrorMessageResourceType = typeof(Resources.SharedResource))]
        public int GuestCount { get; set; } = 0;
    }
}