# Implementation Tasks

- [x] **1 – Add semantic tokens** ✅ Done  
      • Extend `wwwroot/css/shared/variables.css` with the new `--ph-*` tokens.

- [x] **2 – Dark-mode overrides** ✅ Done  
      • In the same file, add an `@media (prefers-color-scheme: dark)` block.

- [x] **3 – High-contrast stylesheet** ✅ Done  
      • Create `wwwroot/css/forced-colors.css` per design.

- [x] **4 – Refactor existing CSS** ✅ Done  
      • Replace hard-coded colours with tokens across `css/`.  
      • Update Razor inline styles.

- [x] **5 – Layout integration** ✅ Done  
      • Update `Views/Shared/_Layout.cshtml` with meta tag, stylesheet links, script.

- [x] **6 – Theme listener JS** ✅ Done  
      • Add `wwwroot/js/theme-listener.js` for runtime theme changes.

- [x] **7 – Accessibility tests** ✅ Done  
      • Extend `ParaHockey.E2E.Tests` to run axe scans in light, dark, and forced-colours modes.  
      • Ensure tests fail on any new contrast errors.

- [x] **8 – CI pipeline step** ✅ Done  
      • Update `azure-pipelines.yml` to call the accessibility test job.

- [x] **9 – Documentation** ✅ Done  
      • Add README section on theming and colour tokens. 