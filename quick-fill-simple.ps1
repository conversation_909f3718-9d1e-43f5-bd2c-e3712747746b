# Quick Registration Fill - ParaHockey
# Browser automation for registration testing

Write-Host "🏒 ParaHockey Quick Registration Fill" -ForegroundColor Cyan
Write-Host "=====================================" -ForegroundColor Cyan
Write-Host ""
Write-Host "Select member type:" -ForegroundColor Yellow
Write-Host "1. Junior (8-17 years, needs parent info)" -ForegroundColor White
Write-Host "2. Development (16-25 years)" -ForegroundColor White  
Write-Host "3. Elite (18-35 years)" -ForegroundColor White
Write-Host "4. Coach (25-55 years)" -ForegroundColor White
Write-Host "5. Volunteer (18-70 years)" -ForegroundColor White
Write-Host ""

$choice = Read-Host "Enter choice (1-5)"

$memberTypes = @{
    "1" = "Junior"
    "2" = "Development" 
    "3" = "Elite"
    "4" = "Coach"
    "5" = "Volunteer"
}

if ($memberTypes.ContainsKey($choice)) {
    $memberType = $memberTypes[$choice]
    
    Write-Host ""
    $count = Read-Host "How many members to generate? (default: 1)"
    if ([string]::IsNullOrWhiteSpace($count)) { $count = 1 }
    
    Write-Host ""
    Write-Host "🌐 Opening browser and creating auto-fill..." -ForegroundColor Green
    
    # Run the ACTUALLY WORKING approach
    & .\fill-form-now.ps1 -MemberType $memberType -Count ([int]$count)
} else {
    Write-Host "❌ Invalid choice. Please run again and select 1-5." -ForegroundColor Red
}