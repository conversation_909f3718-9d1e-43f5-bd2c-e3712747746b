﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace ParaHockeyApp.Migrations
{
    /// <inheritdoc />
    public partial class ReplaceAdminTypeWithLookupTable : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            // Defensive: Rename column only if source exists and target doesn't exist
            migrationBuilder.Sql(@"
                IF EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('AdminUsers') AND name = 'IsMasterAdmin')
                AND NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('AdminUsers') AND name = 'IsActive')
                BEGIN
                    EXEC sp_rename 'AdminUsers.IsMasterAdmin', 'IsActive', 'COLUMN'
                END");

            // Defensive: Add AdminType column only if it doesn't exist
            migrationBuilder.Sql(@"
                IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('AdminUsers') AND name = 'AdminType')
                BEGIN
                    ALTER TABLE AdminUsers ADD AdminType int NOT NULL DEFAULT 0
                END");

            // Defensive: Add AdminTypeId column only if it doesn't exist
            migrationBuilder.Sql(@"
                IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('AdminUsers') AND name = 'AdminTypeId')
                BEGIN
                    ALTER TABLE AdminUsers ADD AdminTypeId int NOT NULL DEFAULT 0
                END");

            // Defensive: Create AdminTypes table only if it doesn't exist
            migrationBuilder.Sql(@"
                IF NOT EXISTS (SELECT * FROM sys.tables WHERE name = 'AdminTypes')
                BEGIN
                    CREATE TABLE AdminTypes (
                        Id int IDENTITY(1,1) PRIMARY KEY,
                        TypeCode int NOT NULL,
                        TypeName nvarchar(50) NOT NULL,
                        Description nvarchar(200) NULL,
                        SortOrder int NOT NULL,
                        DateCreated datetime2 NOT NULL,
                        IsActive bit NOT NULL
                    )
                    
                    CREATE UNIQUE INDEX IX_AdminTypes_TypeCode ON AdminTypes(TypeCode)
                END");

            // Defensive: Create index only if it doesn't exist
            migrationBuilder.Sql(@"
                IF NOT EXISTS (SELECT * FROM sys.indexes WHERE object_id = OBJECT_ID('AdminUsers') AND name = 'IX_AdminUsers_AdminTypeId')
                BEGIN
                    CREATE INDEX IX_AdminUsers_AdminTypeId ON AdminUsers(AdminTypeId)
                END");

            // Defensive: Seed AdminTypes data only if table is empty
            migrationBuilder.Sql(@"
                IF NOT EXISTS (SELECT 1 FROM AdminTypes)
                BEGIN
                    INSERT INTO AdminTypes (TypeCode, TypeName, Description, SortOrder, IsActive, DateCreated)
                    VALUES 
                    (0, 'Disabled', 'Disabled admin account - no access to admin functions', 1, 1, GETUTCDATE()),
                    (3, 'Normal Admin', 'Standard admin with access to most administrative functions', 2, 1, GETUTCDATE()),
                    (9, 'Master Admin', 'Full system administrator with complete access to all functions', 3, 1, GETUTCDATE())
                END");

            // Defensive: Update AdminTypeId only if AdminType column exists
            migrationBuilder.Sql(@"
                IF EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('AdminUsers') AND name = 'AdminType')
                AND EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('AdminUsers') AND name = 'AdminTypeId')
                BEGIN
                    UPDATE AdminUsers 
                    SET AdminTypeId = CASE 
                        WHEN AdminType = 0 THEN 1  -- Disabled -> AdminTypes.Id=1
                        WHEN AdminType = 3 THEN 2  -- Normal -> AdminTypes.Id=2  
                        WHEN AdminType = 9 THEN 3  -- Master -> AdminTypes.Id=3
                        ELSE 2 -- Default to Normal Admin
                    END
                END");

            // Defensive: Add foreign key only if it doesn't exist
            migrationBuilder.Sql(@"
                IF NOT EXISTS (SELECT * FROM sys.foreign_keys WHERE name = 'FK_AdminUsers_AdminTypes_AdminTypeId')
                AND EXISTS (SELECT * FROM sys.tables WHERE name = 'AdminTypes')
                AND EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('AdminUsers') AND name = 'AdminTypeId')
                BEGIN
                    ALTER TABLE AdminUsers 
                    ADD CONSTRAINT FK_AdminUsers_AdminTypes_AdminTypeId 
                    FOREIGN KEY (AdminTypeId) REFERENCES AdminTypes(Id)
                END");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_AdminUsers_AdminTypes_AdminTypeId",
                table: "AdminUsers");

            migrationBuilder.DropTable(
                name: "AdminTypes");

            migrationBuilder.DropIndex(
                name: "IX_AdminUsers_AdminTypeId",
                table: "AdminUsers");

            migrationBuilder.DropColumn(
                name: "AdminType",
                table: "AdminUsers");

            migrationBuilder.DropColumn(
                name: "AdminTypeId",
                table: "AdminUsers");

            migrationBuilder.RenameColumn(
                name: "IsActive",
                table: "AdminUsers",
                newName: "IsMasterAdmin");
        }
    }
}
