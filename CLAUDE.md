# CLAUDE.md

This file provides guidance to Claude <PERSON> (claude.ai/code) when working with code in this repository.

## MCP Tools Usage

**IMPORTANT**: Always use the Context7 MCP server (available at https://mcp.context7.com/mcp) for enhanced capabilities when working on this project. The Context7 MCP tools provide additional functionality beyond the standard Claude Code tools.

## Project Architecture

This is a **ParaHockey member registration web application** built with **ASP.NET Core 8.0 MVC** using a hybrid MVC + API approach. The application supports multi-environment deployment with conditional authentication and theming.

### Key Technologies
- **Framework**: ASP.NET Core 8.0 MVC
- **Database**: Entity Framework Core with SQL Server (all environments)
- **Authentication**: Azure AD (disabled in development, enabled in test/production)
- **Localization**: French (default) and English Canadian
- **Deployment**: Azure DevOps pipeline with automatic test deployment and manual production approval
- **Styling**: Bootstrap with environment-specific themes

### Project Structure
```
Controllers/            # MVC Controllers + API Controllers
├── Api/               # REST API endpoints
├── AdminController.cs  # Full admin panel with calendar management
├── HomeController.cs   # Public pages including public calendar
├── MembersController.cs # Member portal with member calendar
├── EventsController.cs # Event subscription system
└── LanguageController.cs

Models/
├── Entities/          # Entity Framework entities
├── Configuration/     # Environment settings
├── ViewModels/        # View models for MVC
└── ApplicationContext.cs

Views/                 # MVC Razor views
├── Admin/             # Admin panel views including calendar
├── Home/              # Public views including PublicCalendar
├── Members/           # Member portal including CalendarReadOnly
├── Events/            # Event subscription including Subscribe page
├── Shared/            # Shared layouts and partials
└── _ViewStart.cshtml  # CRITICAL: Points to MVC layout

DTOs/                  # Data Transfer Objects for API
Services/              # Business logic services
Resources/             # Localization resource files
wwwroot/              # Static files, CSS, JS, themes
```

## Calendar System Architecture

**CRITICAL NOTE**: When the user asks to do something on "all calendars", this means ALL FOUR calendar implementations:

### 1. Admin Calendar (`/Admin/Calendar`)
- **Controller**: `AdminController.Calendar()`  
- **View**: `Views/Admin/Calendar.cshtml`
- **Access**: Admin users only
- **Features**: Full CRUD operations, import/export, event management
- **Capabilities**: Create, edit, delete, publish/unpublish events
- **Data**: All events (published and unpublished)

### 2. Subscribe/Events Calendar (`/Events/Subscribe`)
- **Controller**: `EventsController.Subscribe()`
- **View**: `Views/Events/Subscribe.cshtml`
- **Access**: Public (enhanced for logged-in members)
- **Features**: Event subscription and registration
- **Capabilities**: View published events, register/unregister
- **Data**: Published events only

### 3. Public/Visitors Calendar (`/Home/PublicCalendar`)
- **Controller**: `HomeController.PublicCalendar()`
- **View**: `Views/Home/PublicCalendar.cshtml`
- **Access**: Anonymous users
- **Features**: Read-only public calendar
- **Capabilities**: View published events only
- **Data**: Published events with public details

### 4. Members Calendar (`/Members/CalendarReadOnly`)
- **Controller**: `MembersController.CalendarReadOnly()`
- **View**: `Views/Members/CalendarReadOnly.cshtml`
- **Access**: Authenticated members only
- **Features**: Member-specific read-only calendar
- **Capabilities**: View published events, see registration status
- **Data**: Published events with member context

## Event Categories and Auto-Categorization System

### Current Event Categories (Production Ready)
All categories are fully localized and sorted alphabetically with "Other" always last:

1. **Camp** - `EventCategory_Camp` (ID: 11, Color: #20c997, Icon: fas fa-campground)
2. **Formation** - `EventCategory_Training` (ID: 4, Color: #17a2b8, Icon: fas fa-dumbbell)
3. **Game** - `EventCategory_Game` (ID: 2, Color: #dc3545, Icon: fas fa-hockey-puck)
4. **Match** - `EventCategory_Game` (localized name for games)
5. **Meeting** - `EventCategory_Meeting` (ID: 5, Color: #6c757d, Icon: fas fa-users)
6. **Practice** - `EventCategory_Practice` (ID: 1, Color: #28a745, Icon: fas fa-skating)
7. **Série** - `EventCategory_Serie` (ID: 12, Color: #6610f2, Icon: fas fa-stream)
8. **Tentative** - `EventCategory_Tentative` (ID: 9, Color: #ffa500, Icon: fas fa-question-circle)
9. **Tournament** - `EventCategory_Tournament` (ID: 3, Color: #ffc107, Icon: fas fa-trophy)
10. **Other** - `EventCategory_Other` (ID: 8, Color: #6f42c1, Icon: fas fa-calendar-alt) - Always displayed last

### Auto-Categorization Features
Implemented in `EventService.DetermineCategoryIdAsync()`:
- **"tentatif"** (case-insensitive) → Automatically assigns to Tentative category
- **"défi sportif"** (case-insensitive) → Automatically assigns to Tournament category
- **Default** → Assigns to Other category
- **Applied to**: New event creation and CSV/Excel imports
- **Migration**: Existing events were automatically recategorized

### Category Management
- **Service Method**: `EventService.GetAllEventCategoriesSortedAsync()` - Returns categories sorted alphabetically with "Other" at end
- **Localization**: All category names use `@SharedLocalizer[category.DisplayNameKey]`
- **Filtering**: All four calendars use identical category filtering with consistent sorting

## Environment Configuration

The application uses environment-specific configuration:

- **Development**: SQL Server database, NoAuth authentication (dev mode), info theme
- **Test/Staging**: SQL Server, Azure AD authentication, red theme with banner
- **Production**: SQL Server, Azure AD authentication, clean blue theme

Environment detection uses `EnvironmentSettings` class and `ASPNETCORE_ENVIRONMENT` variable.

## Common Development Commands

### Build and Run
```bash
# Build the project
dotnet build

# Run locally (development mode)
dotnet run

# Run with specific environment
dotnet run --environment Staging
```

### Database Operations
```bash
# Add new migration
dotnet ef migrations add MigrationName

# Update database
dotnet ef database update

# Update with specific connection string
dotnet ef database update --connection "connection_string_here"

# Generate SQL script
dotnet ef migrations script
```

### Testing
```bash
# Run all tests
dotnet test

# Run specific test project
dotnet test ParaHockey.E2E.Tests/

# Run accessibility tests only
dotnet test ParaHockey.E2E.Tests/ --filter "Category=Accessibility"
```

### **CRITICAL BUILD VERIFICATION**
**MANDATORY**: Always verify the build works before declaring code ready for user testing:

1. **Run dotnet build** to check for compilation errors
2. **Fix any build errors** before proceeding  
3. **Test the application** by running it briefly
4. **Stop the application** and confirm it works
5. **Only then** inform the user that it's ready for testing

This verification step is absolutely essential and must never be skipped!

### Deployment Scripts
- `RunMigrations.ps1` - Runs EF migrations on SIMBA server
- `CheckDeployment.ps1` - Verifies deployment status
- `CheckServerConfiguration.ps1` - Checks server configuration

## Critical Development Guidelines

### Environment Consistency (ABSOLUTE REQUIREMENT)
- **ALL ENVIRONMENTS MUST WORK EXACTLY THE SAME** - Development, Test/Staging, AND Production
- **NEVER** add environment-specific code that changes functionality between environments
- If something works in Development, it MUST work identically in Test and Production
- If JavaScript expects a certain response format, ALL environments must return that exact format
- **NO EXCEPTIONS** - This is critical for deployment success
- Example: If Login page expects `{success: true, code: "123"}` in dev, Production MUST return the same structure

### Multilingual Support (MANDATORY)
- **ALL** user-facing text in Views must use localization: `@Localizer["MyText"]`
- **ALL** validation messages in DTOs must use localization: `ErrorMessageResourceName = "MyValidationError"`
- Resource files are in `/Resources/` folder
- French is the default language, English Canadian is secondary

### MVC Architecture Requirements
- Use MVC pattern strictly (NOT Razor Pages)
- Views use `~/Views/Shared/_Layout.cshtml`
- Navigation links must use MVC routing: `asp-controller/asp-action`
- `Views/_ViewStart.cshtml` must point to MVC layout

### Input Validation and Masking
- Canadian Postal Code: Use jQuery Mask Plugin with pattern `'L0L 0L0'`
- Phone Numbers: Use jQuery Mask Plugin with pattern `'(*************'`
- Use established jQuery plugins over custom solutions
- Always include validation spans: `<span asp-validation-for="Property" class="text-danger"></span>`

### Responsive Design Requirements (MANDATORY)
- **Multi-Device Support**: Application MUST look excellent and function perfectly on:
  - **Desktop/Laptop**: Full-featured experience with optimal use of screen space
  - **Tablets**: Touch-optimized interface with appropriate sizing for tablet screens
  - **Mobile Phones**: Mobile-first responsive design with touch-friendly controls
- **Bootstrap Grid System**: Use Bootstrap's responsive grid classes (col-xs, col-sm, col-md, col-lg, col-xl)
- **Touch-Friendly**: All interactive elements must be appropriately sized for touch input
- **Viewport Optimization**: Proper viewport meta tag and responsive breakpoints
- **Font Scaling**: Text must be readable on all device sizes
- **Form Optimization**: Forms must be easy to complete on mobile devices
- **Navigation**: Mobile-friendly navigation with collapsible menus

### Database Entities
- All entities inherit from `BaseEntity` (audit fields)
- Use Entity Framework Code First approach
- Migrations are auto-applied in non-development environments
- Follow naming conventions: PascalCase for properties

### Services Pattern
- Use interfaces for dependency injection
- Register services in `Program.cs`
- Keep controllers thin - delegate to services
- Use async/await for database operations

## Security and Authentication

### Authentication Flow
- **Development**: NoAuth authentication (bypassed for development)
- **Test/Production**: Azure AD authentication required
- **Member System**: Custom email verification with time-limited codes
- **Admin System**: Role-based access with Master/Normal admin types
- Authentication configuration is conditional based on environment in `Program.cs`

### Important Security Notes
- Never commit secrets or connection strings to repository
- Use environment-specific configuration files
- Azure AD configuration is conditional

## Current Services and Responsibilities

### Core Services (Production Ready)
- **EventService**: Event CRUD, categorization, calendar data, auto-categorization logic
- **MemberService**: Member registration and management
- **AuditLogService**: Comprehensive audit tracking
- **MemberSearchService**: Advanced search with filters and saved searches  
- **MemberExportService**: CSV/Excel export capabilities
- **EventExportService**: Event import/export functionality, CSV/Excel parsing
- **DuplicateMemberService**: Duplicate detection and prevention
- **EmailService**: Email verification and notifications
- **EmptyStateService**: UI empty state management
- **UserContextService**: Session and context management
- **EnvironmentConfigurationService**: Environment-specific settings

## Database Schema (Current Production State)

### Core Entities
- **Members**: Complete member registration data with audit fields
- **Events**: Full event management with categories and registrations
- **EventCategories**: Hierarchical event organization with localization
- **EventRegistrations**: Member event subscriptions and attendance
- **AdminUsers**: Administrative access control with role types
- **AuditLogs**: Comprehensive audit trail for all operations
- **SavedSearches**: Advanced search capabilities for admin users
- **Parents/EmergencyContacts**: Family information and relationships
- **Lookup Tables**: Provinces, registration types, phone types, admin types

### Recent Migration History
- `20250724183159_FixCampAndSerieEventCategories` - Added Camp and Serie categories with correct IDs
- `20250724181603_AddCampAndSerieEventCategories` - Initial attempt (superseded)
- `20250721205515_UpdateEventCategories` - Updated category structure
- `20250718141726_AddIndexesForFilterQueries` - Performance optimization
- `20250717211248_AddSavedSearchTable` - Advanced search features
- `20250715182533_FixEventSystemMigration` - Event system fixes
- `20250715180850_SeedEventCategories` - Initial event categories

## Deployment Pipeline

### Azure DevOps Pipeline
- **Trigger**: Push to `main` branch
- **Build Stage**: Compile, test, package
- **Test Stage**: Auto-deploy to test environment
- **Production Stage**: Manual approval required

### Environment-Specific Deployment
- Test environment: Red theme with "TEST" banner
- Production environment: Clean blue theme
- Auto-migration on startup for non-development environments

## Testing Requirements

### E2E Test Suite (Comprehensive)
The application includes a robust E2E test suite:

**Test Categories**:
- **SmokeTests**: Basic functionality verification
- **AccessibilityTests**: WCAG AA compliance with axe-core
- **LocalizationTests**: Multi-language validation
- **InputMaskingTests**: Form validation and masking
- **ResponsiveDesignTests**: Multi-device compatibility
- **EventTimeOffsetTests**: Event time handling across timezones
- **EventCategoryTests**: Category display and filtering
- **PerformanceTests**: Load time and responsiveness
- **CrossBrowserTests**: Chrome, Firefox, Safari, Edge compatibility

**Test Infrastructure**:
- **Framework**: xUnit with Selenium WebDriver
- **Browser Management**: WebDriverManager for automation
- **CI/CD Integration**: Automated testing in pipeline

### Test Commands
```bash
# Run all tests
dotnet test ParaHockey.E2E.Tests/

# Run specific category
dotnet test --filter "Category=Accessibility"
dotnet test --filter "Category=Localization"

# Run smoke tests only
./run-smoke-tests.ps1
```

When implementing features, always provide testing instructions in this format:
```
## 🧪 **Test Now**

### **Phone Number** 📞
- Type "abc" → should be blocked completely ❌
- Type "1234567890" → should format to "(*************" ✅

### **Email** 📧
- Type "test" → red border ❌
- Type "<EMAIL>" → green border ✅
```

## Daily Commit Reports

When the user requests a daily commit report, follow this specific format:

### Command Sequence
1. Get commits from specific date: `git log --all --since="YYYY-MM-DD 00:00:00" --until="YYYY-MM-DD*****:00:00" --pretty=format:"%h - %an, %ad : %s (%D)" --date=short`
2. Analyze commits by branch and functionality

### Report Format (in French)
- Write in first person singular (je/j'ai) instead of third person or developer names
- Use paragraph format with complete sentences, not bullet points
- Structure: Introduction → Main development work → Technical fixes → Documentation/Quality → Preparatory work → Conclusion
- Avoid subjective adjectives like "considérable", "important", "excellent", etc.
- Focus on what was accomplished, technical details, and business impact
- Group related commits by functionality rather than chronologically
- Use past tense and describe actions taken
- Keep professional tone while being personal (using "je")

## Key Files to Understand

- `Program.cs` - Application configuration and DI setup
- `Models/ApplicationContext.cs` - Entity Framework context
- `Models/Configuration/EnvironmentSettings.cs` - Environment-specific settings
- `Services/EventService.cs` - Event management and auto-categorization
- `Services/IEventService.cs` - Event service interface
- `azure-pipelines.yml` - CI/CD pipeline configuration
- `appsettings.json` - Base configuration
- `appsettings.Staging.json` - Test environment config
- `appsettings.Production.json` - Production environment config
- `Resources/SharedResourceMarker.resx` - French localization
- `Resources/SharedResourceMarker.en-CA.resx` - English localization

## Development Workflow

1. **Local Development**: Use SQL Server database, NoAuth authentication
2. **Feature Development**: Create feature branch, test locally
3. **Integration**: Merge to `main` branch
4. **Deployment**: Pipeline auto-deploys to test, manual approval for production
5. **Verification**: Test in all environments before marking complete

## Common Pitfalls to Avoid

- Don't mix MVC and Razor Pages routing
- Don't hardcode user-facing text (use localization)
- Don't write custom input masking (use jQuery plugins)
- Don't forget `_ViewStart.cshtml` for MVC layout
- Don't skip validation spans in forms
- Don't forget to test in both languages (French/English)
- Don't use `GetAllEventCategoriesAsync()` for UI - use `GetAllEventCategoriesSortedAsync()` instead

## Important Development Notes

### Package and Framework Considerations
- The "shadcn-ui" package is deprecated. Use the "shadcn" package instead.
- When using dynamic Next.js APIs (params, searchParams, cookies(), headers(), etc.) in Server Components or async functions, you must now use "await" before accessing its properties or the code will not work.

### Development Server Protocol
- Never run a development server automatically, it is already running. Instead ask the user to start it manually by providing the appropriate command.

## Development Assistance Guidelines

### Collaboration Approach
- Act as a professional lead developer mentoring a beginner programmer
- Provide clear, jargon-free explanations with technical terms defined when necessary
- Collaborate step-by-step using efficient, industry-standard methods
- Default language for user-facing content: English (overrides previous French default)
- Use English for all non-user-facing code elements (variable names, HTML tags, etc.)

### Code Modification Protocol
- **Preserve all existing code and features** unless explicitly requested to modify
- **Flag any risks** before making changes and wait for confirmation
- **Never make code changes without explicit approval**
- Provide at least two implementation approaches with pros/cons for each feature or bug fix

### Technical Standards
- Prioritize: performance, readability, security, and maintainability
- Flag potential security or privacy issues immediately
- Follow all project-specific standards and version constraints
- Ensure full responsiveness and multilingual readiness across all pages
- Provide quick test scenarios or unit tests for each new feature/fix

### Task Completion Protocol
- **Always provide testing instructions** when finishing a plan or completing tasks
- Include specific steps the user can follow to verify the implementation works correctly
- Test scenarios should cover both happy path and edge cases
- Format tests clearly with expected outcomes for easy validation

### Context7 MCP Usage Policy
Context7 provides external documentation and library information. Use Context7 when:
- User requests library documentation, code examples, or package updates
- User explicitly requests Context7 usage
- Information is not available in local project files
- By default, prioritize local project files and built-in tools unless Context7 is optimal or requested

## Theming and Color System

### Color-Contrast Consistency Feature

The ParaHockey application implements a comprehensive color-contrast consistency system that ensures WCAG AA compliance across all themes and accessibility modes.

#### Design System Architecture

The application uses a **token-based theming system** with CSS custom properties for consistent color management:

```css
/* Semantic Color Tokens */
--ph-bg-primary: #ffffff;        /* Primary background */
--ph-bg-secondary: #f8f9fa;      /* Secondary background */
--ph-text-primary: #212529;      /* Primary text */
--ph-text-secondary: #6c757d;    /* Secondary text */
--ph-border: #dee2e6;            /* Border color */
--ph-link: #0d6efd;              /* Link color */
--ph-link-hover: #0b5ed7;        /* Link hover color */
```

#### Supported Themes

1. **Light Mode** (default)
   - Clean, accessible color palette
   - Optimized for daytime use
   - WCAG AA contrast ratios

2. **Dark Mode** (`prefers-color-scheme: dark`)
   - Automatically activates based on system preference
   - Eye-friendly low-light interface
   - Maintains accessibility standards

3. **Forced Colors / High Contrast Mode**
   - Windows High Contrast mode support
   - Uses system colors (Canvas, CanvasText, LinkText)
   - Ensures visibility for users with visual impairments

#### Implementation Files

| File | Purpose |
|------|---------|
| `wwwroot/css/shared/variables.css` | Main design tokens and dark mode overrides |
| `wwwroot/css/forced-colors.css` | High contrast mode styles |
| `wwwroot/js/theme-listener.js` | Runtime theme detection and switching |
| `Views/Shared/_Layout.cshtml` | Theme integration and meta tags |

#### Accessibility Testing

The application includes comprehensive accessibility testing:

- **Automated Testing**: axe-core integration in E2E test suite
- **Multi-Theme Testing**: Tests run in light, dark, and forced-colors modes
- **WCAG Compliance**: Validates AA standard contrast ratios (≥4.5:1)
- **CI/CD Integration**: Pipeline fails on accessibility violations

```bash
# Run accessibility tests locally
cd ParaHockey.E2E.Tests
dotnet test --filter Category=Accessibility
```

#### Theme Utilities (JavaScript)

The `ParaHockeyTheme` global object provides theme utilities for debugging:

```javascript
// Check current theme state
console.log(window.ParaHockeyTheme.isDark());
console.log(window.ParaHockeyTheme.isForcedColors());

// Get complete theme info
console.log(window.ParaHockeyTheme.getSystemTheme());

// Manually refresh theme (for development)
window.ParaHockeyTheme.refresh();
```

#### Adding New Colors

When adding new colors to the design system:

1. **Add semantic token** to `variables.css`:
   ```css
   --ph-new-color: #hexvalue;
   ```

2. **Add dark mode override**:
   ```css
   @media (prefers-color-scheme: dark) {
     :root {
       --ph-new-color: #dark-hexvalue;
     }
   }
   ```

3. **Add forced-colors fallback** in `forced-colors.css`:
   ```css
   @media (forced-colors: active) {
     :root {
       --ph-new-color: CanvasText; /* or appropriate system color */
     }
   }
   ```

4. **Use token in CSS**:
   ```css
   .my-component {
     color: var(--ph-new-color);
   }
   ```

5. **Test accessibility**: Run accessibility tests to ensure WCAG compliance

#### Browser Support

- **Chrome**: Full support including forced-colors
- **Firefox**: Full support including forced-colors  
- **Safari**: Supports light/dark modes (forced-colors limited)
- **Edge**: Full support including Windows High Contrast

#### Troubleshooting

**Theme not switching?**
- Check browser dev tools for `prefers-color-scheme` media query
- Verify `theme-listener.js` is loaded without errors
- Ensure CSS custom properties are properly defined

**Accessibility test failures?**
- Review axe-core test output for specific violations
- Check color contrast ratios using browser dev tools
- Verify semantic tokens are used instead of hard-coded colors

**High contrast mode issues?**
- Test on Windows with High Contrast enabled
- Ensure `forced-colors.css` is properly linked
- Verify interactive elements have proper borders/outlines

## Current Application Features (Production Ready - July 24, 2025)

### Core Functionality
- ✅ **Member Registration**: Complete registration workflow with duplicate detection
- ✅ **Admin Panel**: Full user management, member oversight, system administration
- ✅ **Event Calendar System**: Four complete calendar implementations with CRUD operations
- ✅ **Event Import/Export**: CSV and Excel support with French category mapping
- ✅ **Auto-Categorization**: Intelligent event categorization based on title keywords
- ✅ **Authentication**: Environment-specific auth (NoAuth for dev, Azure AD for prod)
- ✅ **Multilingual Support**: Complete French/English localization throughout
- ✅ **Responsive Design**: Bootstrap-based responsive UI for all devices
- ✅ **Environment Themes**: Visual indicators for dev/test/production environments
- ✅ **Advanced Search**: Member search with filters and saved searches
- ✅ **Audit System**: Comprehensive audit logging for all operations
- ✅ **Email System**: Verification codes and notifications

### Event Management System
- ✅ **10 Event Categories**: Fully localized with auto-categorization
- ✅ **Four Calendar Views**: Admin, Subscribe, Public, Members - each with specific access levels
- ✅ **Event Registration**: Members can register for events with capacity management
- ✅ **Import/Export**: Support for CSV and Excel files with category mapping
- ✅ **Publishing Control**: Events can be published/unpublished for public visibility
- ✅ **Time Zone Handling**: Proper time zone management across environments

### Database Schema (Current)
- ✅ **Members**: Core member registration data with audit fields
- ✅ **Events**: Complete event management with categories and registrations
- ✅ **EventCategories**: 10 categories with localization and auto-assignment
- ✅ **EventRegistrations**: Member event subscriptions with status tracking
- ✅ **AdminUsers**: Administrative access control with Master/Normal types
- ✅ **AuditLogs**: Comprehensive audit trail for all operations
- ✅ **SavedSearches**: Advanced search capabilities for admin users
- ✅ **Lookup Tables**: Provinces, registration types, phone types, admin types

### File Cleanup Status
**Last Major Cleanup**: July 14, 2025
- Removed 52 obsolete files including test controllers, legacy SQL scripts, and development utilities
- Codebase is clean and production-ready

### Clean Codebase
The codebase has been thoroughly cleaned of:
- ✅ Test/debug controllers and views
- ✅ Obsolete SQL scripts (now using EF migrations)
- ✅ Development autofill utilities
- ✅ Outdated documentation and artifacts
- ✅ Test artifacts and screenshots
- ✅ Disabled test files

## Recent Major Updates (July 2025)

### Event Categories Enhancement
- ✅ Added Camp and Série event categories with proper localization
- ✅ Implemented auto-categorization for "tentatif" → Tentative and "défi sportif" → Tournament
- ✅ Fixed category sorting to be alphabetical with "Other" always last
- ✅ Applied consistent sorting across all four calendar implementations
- ✅ Updated all controllers to use `GetAllEventCategoriesSortedAsync()`

### Calendar System Improvements
- ✅ Standardized category filtering across Admin, Subscribe, Public, and Members calendars
- ✅ Fixed localization issues in category dropdown displays
- ✅ Ensured consistent user experience across all calendar views
- ✅ Implemented proper category color coding and icons

### Data Migration
- ✅ `20250724183159_FixCampAndSerieEventCategories` migration successfully adds new categories
- ✅ Existing events with "tentatif" and "défi sportif" automatically recategorized
- ✅ All calendar filters updated to show new categories in correct order

# important-instruction-reminders
Do what has been asked; nothing more, nothing less.
NEVER create files unless they're absolutely necessary for achieving your goal.
ALWAYS prefer editing an existing file to creating a new one.
NEVER proactively create documentation files (*.md) or README files. Only create documentation files if explicitly requested by the User.