# Implementation Tasks – Admin Event Time Offset Fix

> **Phase 0 – Preparation**
> - [x] Grep repository for `type="datetime-local"` in admin views/scripts; list each file that needs UTC→local conversion.
>   - Found: `Views/Admin/Calendar.cshtml` (contains eventStartDate and eventEndDate inputs)

## Phase 1 – Front-End Fixes
1. [x] Add `utcToLocalForDatetimeInput` helper to `Views/Admin/Calendar.cshtml` script section (or extract to `site.js` if shared later).
2. [x] Update `openEventModal()` to convert `startDate` and `endDate` when pre-filling inputs.
3. [x] Ensure FullCalendar creation flow (new event) still pre-fills inputs directly without extra conversion.

## Phase 2 – Change-Detection Logic
4. [x] On modal open, capture `originalEventData` snapshot of all form fields (normalised strings, parsed numbers/booleans).
5. [x] In `saveEvent()`, build `currentEventData` and deep-compare.
6. [x] If identical → display localised toast `@SharedLocalizer["NoChangesDetected"]`, close modal, and **do not** call fetch.
7. [x] If different → proceed with existing save logic (unchanged).

## Phase 3 – Localisation
8. [x] Add key `NoChangesDetected` with EN="No changes detected." and FR="Aucune modification détectée." to both `.resx` files.

## Phase 4 – Automated Tests
9. [x] Create new E2E test `EventTimeOffsetTests` covering:
   - Create event at 09:00, reload, verify still 09:00.
   - Save without changes; assert no network call (mock or timestamp check).
   - Edit description only; assert times unchanged.
10. [x] Add cross-TZ browser test (set browser TZ env to `America/Vancouver`).

## Phase 5 – Code Review & Deployment
11. [x] PR → CI passes. (Code ready, builds successfully)
12. [ ] QA on staging (manual checklist in design doc).
13. [ ] Merge to `main` → production pipeline.

---

### Estimate
* **Dev:** 2–3 h
* **QA:** 1 h

---

### Implementation Note for Coding AI
Please mark each task checkbox as completed (`[x]`) in this `tasks.md` file **immediately after finishing that task** to keep progress transparent. 