# 🏒 ParaHockey E2E Tests - Quick Start Guide

**Last Updated**: January 2025  
**Status**: ✅ **FULLY WORKING**

This guide shows you how to run automated tests for the ParaHockey registration system.

## 🚀 Before You Start

### ⚠️ **IMPORTANT: Your website MUST be running first!**

1. **Open PowerShell Window #1**
2. **Start the website**:
   ```powershell
   cd C:\Users\<USER>\OneDrive\Documents\Repos\2-Complys\Clients\ParaHockey
   dotnet run
   ```
3. **Wait for**: `Now listening on: http://localhost:5285`
4. **Keep this window open!**

### Now you're ready to run tests!

1. **Open PowerShell Window #2** (different window)
2. **Go to test folder**:
   ```powershell
   cd C:\Users\<USER>\OneDrive\Documents\Repos\2-Complys\Clients\ParaHockey\ParaHockey.E2E.Tests
   ```

---

## 🎯 Available Tests & Commands

### 1️⃣ **HTTP Tests** (30 seconds) ✅ FASTEST & MOST RELIABLE
Tests server without opening browser
```powershell
./run-tests.ps1 -TestCategory HTTP
```
**What it tests**: Page availability, security headers, static files

---

### 2️⃣ **Smoke Tests** (1-2 minutes) ✅ QUICK CHECK
Basic functionality with browser
```powershell
./run-tests.ps1 -TestCategory Smoke
```
**What it tests**: Homepage loads, registration page works, language switching

---

### 3️⃣ **Validation Tests** (3-4 minutes) ✅ FORM TESTING
Tests all form field validations
```powershell
./run-tests.ps1 -TestCategory Validation
```
**What it tests**: Required fields, email format, phone masks, postal codes

---

### 4️⃣ **Workflow Tests** (4-5 minutes) ✅ FULL REGISTRATION
Tests complete registration process
```powershell
./run-tests.ps1 -TestCategory Workflow
```
**What it tests**: Junior registration, adult registration, form submission

---

### 5️⃣ **Localization Tests** (2-3 minutes) ✅ LANGUAGE TESTING
Tests French/English switching
```powershell
./run-tests.ps1 -TestCategory Localization
```
**What it tests**: Language switching, translations, form data preservation

---

### 6️⃣ **Input Masking Tests** (2-3 minutes) ✅ FORMAT TESTING
Tests phone and postal code formatting
```powershell
./run-tests.ps1 -TestCategory Masking
```
**What it tests**: Phone (xxx) xxx-xxxx, postal code L0L 0L0, paste handling

---

### 7️⃣ **Performance Tests** (3-4 minutes) ✅ SPEED TESTING
Tests page load times
```powershell
./run-tests.ps1 -TestCategory Performance
```
**What it tests**: Page load speed, memory usage, response times

---

### 8️⃣ **ALL Tests** (10-15 minutes) ⚠️ COMPREHENSIVE
Runs every single test
```powershell
./run-tests.ps1
```
**What it tests**: Everything above plus cross-browser and edge cases

---

## 📊 Understanding Results

After tests run, a browser window opens automatically showing:
- ✅ **PASSED** tests in green
- ❌ **FAILED** tests in red with error details
- Test execution times
- What to do if tests fail

---

## 🔧 Common Issues & Solutions

### ❌ "Connection refused" or "This site can't be reached"
**Solution**: Your website isn't running! Go back to step 1 and run `dotnet run`

### ❌ "ChromeDriver version mismatch"
**Solution**: Your Chrome browser updated. The tests should auto-fix this, but if not:
```powershell
dotnet build
```

### ❌ Tests are slow
**Solution**: Run without browser window:
```powershell
./run-tests.ps1 -TestCategory Smoke -Headless
```

---

## 📝 Quick Reference Card

```powershell
# Quick server check (no browser, 30 seconds)
./run-tests.ps1 -TestCategory HTTP

# Daily check (with browser, 2 minutes)
./run-tests.ps1 -TestCategory Smoke

# After changing forms (3-4 minutes)
./run-tests.ps1 -TestCategory Validation

# After changing translations (2-3 minutes)
./run-tests.ps1 -TestCategory Localization

# Full test suite (10-15 minutes)
./run-tests.ps1
```

---

## 🎓 When to Run Which Test

| If you changed... | Run this test |
|------------------|---------------|
| Nothing, just daily check | `Smoke` |
| Form fields or validation | `Validation` |
| Registration process | `Workflow` |
| French/English text | `Localization` |
| Phone/postal formatting | `Masking` |
| Page performance | `Performance` |
| Major changes | `ALL` (no category) |

---

## 💡 Pro Tips

1. **Always run website first** - Most common mistake!
2. **Start with HTTP or Smoke** - Quick and catches major issues
3. **Watch the browser** - See exactly what's being tested
4. **Check results page** - Shows detailed pass/fail info

---

## 🆘 Need Help?

If tests fail:
1. Check if website is running at http://localhost:5285
2. Look at the error message in the results page
3. Screenshot saved in `Screenshots` folder
4. Try running just the failing test category

Remember: Green = Good, Red = Problem to fix!