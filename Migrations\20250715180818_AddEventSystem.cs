﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace ParaHockeyApp.Migrations
{
    /// <inheritdoc />
    public partial class AddEventSystem : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            // Skip Members and Parents - they already have audit columns from AddHybridAuditSystem migration
            
            // Create EventCategories table
            migrationBuilder.CreateTable(
                name: "EventCategories",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    DisplayNameKey = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: false),
                    DescriptionKey = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: true),
                    Color = table.Column<string>(type: "nvarchar(7)", maxLength: 7, nullable: false),
                    IconClass = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: true),
                    DisplayOrder = table.Column<int>(type: "int", nullable: false),
                    RequiresRegistration = table.Column<bool>(type: "bit", nullable: false),
                    MaxParticipants = table.Column<int>(type: "int", nullable: false),
                    DateCreated = table.Column<DateTime>(type: "datetime2", nullable: false),
                    IsActive = table.Column<bool>(type: "bit", nullable: false),
                    DateModified = table.Column<DateTime>(type: "datetime2", nullable: true),
                    CreatedByMemberId = table.Column<int>(type: "int", nullable: true),
                    CreatedByAdminId = table.Column<int>(type: "int", nullable: true),
                    ModifiedByMemberId = table.Column<int>(type: "int", nullable: true),
                    ModifiedByAdminId = table.Column<int>(type: "int", nullable: true),
                    CreatedBySource = table.Column<int>(type: "int", nullable: false),
                    ModifiedBySource = table.Column<int>(type: "int", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_EventCategories", x => x.Id);
                    table.ForeignKey(
                        name: "FK_EventCategories_AdminUsers_CreatedByAdminId",
                        column: x => x.CreatedByAdminId,
                        principalTable: "AdminUsers",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "FK_EventCategories_AdminUsers_ModifiedByAdminId",
                        column: x => x.ModifiedByAdminId,
                        principalTable: "AdminUsers",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "FK_EventCategories_Members_CreatedByMemberId",
                        column: x => x.CreatedByMemberId,
                        principalTable: "Members",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "FK_EventCategories_Members_ModifiedByMemberId",
                        column: x => x.ModifiedByMemberId,
                        principalTable: "Members",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                });

            // Create Events table
            migrationBuilder.CreateTable(
                name: "Events",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    Title = table.Column<string>(type: "nvarchar(200)", maxLength: 200, nullable: false),
                    Description = table.Column<string>(type: "nvarchar(2000)", maxLength: 2000, nullable: true),
                    StartDate = table.Column<DateTime>(type: "datetime2", nullable: false),
                    EndDate = table.Column<DateTime>(type: "datetime2", nullable: false),
                    IsAllDay = table.Column<bool>(type: "bit", nullable: false),
                    Location = table.Column<string>(type: "nvarchar(300)", maxLength: 300, nullable: true),
                    EventCategoryId = table.Column<int>(type: "int", nullable: false),
                    RequiresRegistration = table.Column<bool>(type: "bit", nullable: false),
                    MaxParticipants = table.Column<int>(type: "int", nullable: false),
                    RegistrationDeadline = table.Column<DateTime>(type: "datetime2", nullable: true),
                    IsPublished = table.Column<bool>(type: "bit", nullable: false),
                    IsRecurring = table.Column<bool>(type: "bit", nullable: false),
                    RecurrencePattern = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: true),
                    Priority = table.Column<int>(type: "int", nullable: false),
                    ContactPerson = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: true),
                    ContactEmail = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: true),
                    ContactPhone = table.Column<string>(type: "nvarchar(20)", maxLength: 20, nullable: true),
                    OrganizerNotes = table.Column<string>(type: "nvarchar(1000)", maxLength: 1000, nullable: true),
                    DateCreated = table.Column<DateTime>(type: "datetime2", nullable: false),
                    IsActive = table.Column<bool>(type: "bit", nullable: false),
                    DateModified = table.Column<DateTime>(type: "datetime2", nullable: true),
                    CreatedByMemberId = table.Column<int>(type: "int", nullable: true),
                    CreatedByAdminId = table.Column<int>(type: "int", nullable: true),
                    ModifiedByMemberId = table.Column<int>(type: "int", nullable: true),
                    ModifiedByAdminId = table.Column<int>(type: "int", nullable: true),
                    CreatedBySource = table.Column<int>(type: "int", nullable: false),
                    ModifiedBySource = table.Column<int>(type: "int", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Events", x => x.Id);
                    table.ForeignKey(
                        name: "FK_Events_AdminUsers_CreatedByAdminId",
                        column: x => x.CreatedByAdminId,
                        principalTable: "AdminUsers",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "FK_Events_AdminUsers_ModifiedByAdminId",
                        column: x => x.ModifiedByAdminId,
                        principalTable: "AdminUsers",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "FK_Events_EventCategories_EventCategoryId",
                        column: x => x.EventCategoryId,
                        principalTable: "EventCategories",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "FK_Events_Members_CreatedByMemberId",
                        column: x => x.CreatedByMemberId,
                        principalTable: "Members",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "FK_Events_Members_ModifiedByMemberId",
                        column: x => x.ModifiedByMemberId,
                        principalTable: "Members",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                });

            // Create EventRegistrations table
            migrationBuilder.CreateTable(
                name: "EventRegistrations",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    EventId = table.Column<int>(type: "int", nullable: false),
                    MemberId = table.Column<int>(type: "int", nullable: false),
                    Status = table.Column<int>(type: "int", nullable: false),
                    RegistrationDate = table.Column<DateTime>(type: "datetime2", nullable: false),
                    ConfirmationDate = table.Column<DateTime>(type: "datetime2", nullable: true),
                    CancellationDate = table.Column<DateTime>(type: "datetime2", nullable: true),
                    MemberNotes = table.Column<string>(type: "nvarchar(1000)", maxLength: 1000, nullable: true),
                    AdminNotes = table.Column<string>(type: "nvarchar(1000)", maxLength: 1000, nullable: true),
                    Attended = table.Column<bool>(type: "bit", nullable: true),
                    GuestCount = table.Column<int>(type: "int", nullable: false),
                    GuestNames = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: true),
                    EmergencyContact = table.Column<string>(type: "nvarchar(200)", maxLength: 200, nullable: true),
                    EmergencyPhone = table.Column<string>(type: "nvarchar(20)", maxLength: 20, nullable: true),
                    SpecialRequirements = table.Column<string>(type: "nvarchar(1000)", maxLength: 1000, nullable: true),
                    IsActive = table.Column<bool>(type: "bit", nullable: false),
                    DateCreated = table.Column<DateTime>(type: "datetime2", nullable: false),
                    DateModified = table.Column<DateTime>(type: "datetime2", nullable: true),
                    CreatedByMemberId = table.Column<int>(type: "int", nullable: true),
                    CreatedByAdminId = table.Column<int>(type: "int", nullable: true),
                    ModifiedByMemberId = table.Column<int>(type: "int", nullable: true),
                    ModifiedByAdminId = table.Column<int>(type: "int", nullable: true),
                    CreatedBySource = table.Column<int>(type: "int", nullable: false),
                    ModifiedBySource = table.Column<int>(type: "int", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_EventRegistrations", x => x.Id);
                    table.ForeignKey(
                        name: "FK_EventRegistrations_AdminUsers_CreatedByAdminId",
                        column: x => x.CreatedByAdminId,
                        principalTable: "AdminUsers",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "FK_EventRegistrations_AdminUsers_ModifiedByAdminId",
                        column: x => x.ModifiedByAdminId,
                        principalTable: "AdminUsers",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "FK_EventRegistrations_Events_EventId",
                        column: x => x.EventId,
                        principalTable: "Events",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_EventRegistrations_Members_CreatedByMemberId",
                        column: x => x.CreatedByMemberId,
                        principalTable: "Members",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "FK_EventRegistrations_Members_MemberId",
                        column: x => x.MemberId,
                        principalTable: "Members",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_EventRegistrations_Members_ModifiedByMemberId",
                        column: x => x.ModifiedByMemberId,
                        principalTable: "Members",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                });

            // Add audit columns to remaining tables (defensive)
            migrationBuilder.Sql(@"
                -- Add audit columns to Parents table (defensive)
                IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('Parents') AND name = 'CreatedByAdminId')
                BEGIN
                    ALTER TABLE Parents ADD CreatedByAdminId int NULL
                END
                
                IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('Parents') AND name = 'CreatedByMemberId')
                BEGIN
                    ALTER TABLE Parents ADD CreatedByMemberId int NULL
                END
                
                IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('Parents') AND name = 'CreatedBySource')
                BEGIN
                    ALTER TABLE Parents ADD CreatedBySource int NOT NULL DEFAULT 0
                END
                
                IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('Parents') AND name = 'DateModified')
                BEGIN
                    ALTER TABLE Parents ADD DateModified datetime2 NULL
                END
                
                IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('Parents') AND name = 'ModifiedByAdminId')
                BEGIN
                    ALTER TABLE Parents ADD ModifiedByAdminId int NULL
                END
                
                IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('Parents') AND name = 'ModifiedByMemberId')
                BEGIN
                    ALTER TABLE Parents ADD ModifiedByMemberId int NULL
                END
                
                IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('Parents') AND name = 'ModifiedBySource')
                BEGIN
                    ALTER TABLE Parents ADD ModifiedBySource int NULL
                END
                
                -- Add audit columns to Members table (defensive)
                IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('Members') AND name = 'CreatedByAdminId')
                BEGIN
                    ALTER TABLE Members ADD CreatedByAdminId int NULL
                END
                
                IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('Members') AND name = 'CreatedByMemberId')
                BEGIN
                    ALTER TABLE Members ADD CreatedByMemberId int NULL
                END
                
                IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('Members') AND name = 'CreatedBySource')
                BEGIN
                    ALTER TABLE Members ADD CreatedBySource int NOT NULL DEFAULT 0
                END
                
                IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('Members') AND name = 'DateModified')
                BEGIN
                    ALTER TABLE Members ADD DateModified datetime2 NULL
                END
                
                IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('Members') AND name = 'ModifiedByAdminId')
                BEGIN
                    ALTER TABLE Members ADD ModifiedByAdminId int NULL
                END
                
                IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('Members') AND name = 'ModifiedByMemberId')
                BEGIN
                    ALTER TABLE Members ADD ModifiedByMemberId int NULL
                END
                
                IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('Members') AND name = 'ModifiedBySource')
                BEGIN
                    ALTER TABLE Members ADD ModifiedBySource int NULL
                END
            ");

            // Add audit columns to remaining tables (defensive)
            migrationBuilder.Sql(@"
                -- Add audit columns to EmergencyContacts table (defensive)
                IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('EmergencyContacts') AND name = 'CreatedByAdminId')
                BEGIN
                    ALTER TABLE EmergencyContacts ADD CreatedByAdminId int NULL
                END
                
                IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('EmergencyContacts') AND name = 'CreatedByMemberId')
                BEGIN
                    ALTER TABLE EmergencyContacts ADD CreatedByMemberId int NULL
                END
                
                IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('EmergencyContacts') AND name = 'CreatedBySource')
                BEGIN
                    ALTER TABLE EmergencyContacts ADD CreatedBySource int NOT NULL DEFAULT 0
                END
                
                IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('EmergencyContacts') AND name = 'DateModified')
                BEGIN
                    ALTER TABLE EmergencyContacts ADD DateModified datetime2 NULL
                END
                
                IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('EmergencyContacts') AND name = 'ModifiedByAdminId')
                BEGIN
                    ALTER TABLE EmergencyContacts ADD ModifiedByAdminId int NULL
                END
                
                IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('EmergencyContacts') AND name = 'ModifiedByMemberId')
                BEGIN
                    ALTER TABLE EmergencyContacts ADD ModifiedByMemberId int NULL
                END
                
                IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('EmergencyContacts') AND name = 'ModifiedBySource')
                BEGIN
                    ALTER TABLE EmergencyContacts ADD ModifiedBySource int NULL
                END
                
                -- Add audit columns to AdminUsers table (defensive)
                IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('AdminUsers') AND name = 'CreatedByAdminId')
                BEGIN
                    ALTER TABLE AdminUsers ADD CreatedByAdminId int NULL
                END
                
                IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('AdminUsers') AND name = 'CreatedByMemberId')
                BEGIN
                    ALTER TABLE AdminUsers ADD CreatedByMemberId int NULL
                END
                
                IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('AdminUsers') AND name = 'CreatedBySource')
                BEGIN
                    ALTER TABLE AdminUsers ADD CreatedBySource int NOT NULL DEFAULT 0
                END
                
                IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('AdminUsers') AND name = 'DateModified')
                BEGIN
                    ALTER TABLE AdminUsers ADD DateModified datetime2 NULL
                END
                
                IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('AdminUsers') AND name = 'ModifiedByAdminId')
                BEGIN
                    ALTER TABLE AdminUsers ADD ModifiedByAdminId int NULL
                END
                
                IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('AdminUsers') AND name = 'ModifiedByMemberId')
                BEGIN
                    ALTER TABLE AdminUsers ADD ModifiedByMemberId int NULL
                END
                
                IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('AdminUsers') AND name = 'ModifiedBySource')
                BEGIN
                    ALTER TABLE AdminUsers ADD ModifiedBySource int NULL
                END
                
                -- Add audit columns to AdminTypes table (defensive)
                IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('AdminTypes') AND name = 'CreatedByAdminId')
                BEGIN
                    ALTER TABLE AdminTypes ADD CreatedByAdminId int NULL
                END
                
                IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('AdminTypes') AND name = 'CreatedByMemberId')
                BEGIN
                    ALTER TABLE AdminTypes ADD CreatedByMemberId int NULL
                END
                
                IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('AdminTypes') AND name = 'CreatedBySource')
                BEGIN
                    ALTER TABLE AdminTypes ADD CreatedBySource int NOT NULL DEFAULT 0
                END
                
                IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('AdminTypes') AND name = 'DateModified')
                BEGIN
                    ALTER TABLE AdminTypes ADD DateModified datetime2 NULL
                END
                
                IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('AdminTypes') AND name = 'ModifiedByAdminId')
                BEGIN
                    ALTER TABLE AdminTypes ADD ModifiedByAdminId int NULL
                END
                
                IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('AdminTypes') AND name = 'ModifiedByMemberId')
                BEGIN
                    ALTER TABLE AdminTypes ADD ModifiedByMemberId int NULL
                END
                
                IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('AdminTypes') AND name = 'ModifiedBySource')
                BEGIN
                    ALTER TABLE AdminTypes ADD ModifiedBySource int NULL
                END
            ");

            // Create AuditLogs table (defensive - skip if already exists from AddHybridAuditSystem)
            migrationBuilder.Sql(@"
                IF NOT EXISTS (SELECT * FROM sys.tables WHERE name = 'AuditLogs')
                BEGIN
                    CREATE TABLE AuditLogs (
                        Id int IDENTITY(1,1) PRIMARY KEY,
                        EntityType nvarchar(50) NOT NULL,
                        EntityId int NOT NULL,
                        Action nvarchar(20) NOT NULL,
                        Timestamp datetime2 NOT NULL,
                        PerformedByMemberId int NULL,
                        PerformedByAdminId int NULL,
                        PerformedBySource int NOT NULL,
                        PerformerName nvarchar(101) NOT NULL,
                        IPAddress nvarchar(45) NULL,
                        OldValues nvarchar(max) NULL,
                        NewValues nvarchar(max) NULL,
                        Description nvarchar(1000) NULL
                    )
                    
                    -- Create indexes for performance
                    CREATE INDEX IX_AuditLogs_Entity ON AuditLogs (EntityType, EntityId)
                    CREATE INDEX IX_AuditLogs_Timestamp ON AuditLogs (Timestamp)
                    CREATE INDEX IX_AuditLogs_PerformedByMember ON AuditLogs (PerformedByMemberId)
                    CREATE INDEX IX_AuditLogs_PerformedByAdmin ON AuditLogs (PerformedByAdminId)
                    
                    PRINT 'AuditLogs table created'
                END
                ELSE
                BEGIN
                    PRINT 'AuditLogs table already exists - skipping creation'
                END
                
                -- Add foreign key constraints (defensive)
                IF NOT EXISTS (SELECT * FROM sys.foreign_keys WHERE name = 'FK_AuditLogs_AdminUsers_PerformedByAdminId')
                BEGIN
                    ALTER TABLE AuditLogs 
                    ADD CONSTRAINT FK_AuditLogs_AdminUsers_PerformedByAdminId 
                    FOREIGN KEY (PerformedByAdminId) REFERENCES AdminUsers(Id) ON DELETE NO ACTION
                END
                
                IF NOT EXISTS (SELECT * FROM sys.foreign_keys WHERE name = 'FK_AuditLogs_Members_PerformedByMemberId')
                BEGIN
                    ALTER TABLE AuditLogs 
                    ADD CONSTRAINT FK_AuditLogs_Members_PerformedByMemberId 
                    FOREIGN KEY (PerformedByMemberId) REFERENCES Members(Id) ON DELETE NO ACTION
                END
            ");

            // Create Indexes
            migrationBuilder.CreateIndex(
                name: "IX_Parents_CreatedByAdminId",
                table: "Parents",
                column: "CreatedByAdminId");

            migrationBuilder.CreateIndex(
                name: "IX_Parents_CreatedByMemberId",
                table: "Parents",
                column: "CreatedByMemberId");

            migrationBuilder.CreateIndex(
                name: "IX_Parents_ModifiedByAdminId",
                table: "Parents",
                column: "ModifiedByAdminId");

            migrationBuilder.CreateIndex(
                name: "IX_Parents_ModifiedByMemberId",
                table: "Parents",
                column: "ModifiedByMemberId");

            migrationBuilder.CreateIndex(
                name: "IX_Members_CreatedByAdminId",
                table: "Members",
                column: "CreatedByAdminId");

            migrationBuilder.CreateIndex(
                name: "IX_Members_CreatedByMemberId",
                table: "Members",
                column: "CreatedByMemberId");

            migrationBuilder.CreateIndex(
                name: "IX_Members_ModifiedByAdminId",
                table: "Members",
                column: "ModifiedByAdminId");

            migrationBuilder.CreateIndex(
                name: "IX_Members_ModifiedByMemberId",
                table: "Members",
                column: "ModifiedByMemberId");

            migrationBuilder.CreateIndex(
                name: "IX_EmergencyContacts_CreatedByAdminId",
                table: "EmergencyContacts",
                column: "CreatedByAdminId");

            migrationBuilder.CreateIndex(
                name: "IX_EmergencyContacts_CreatedByMemberId",
                table: "EmergencyContacts",
                column: "CreatedByMemberId");

            migrationBuilder.CreateIndex(
                name: "IX_EmergencyContacts_ModifiedByAdminId",
                table: "EmergencyContacts",
                column: "ModifiedByAdminId");

            migrationBuilder.CreateIndex(
                name: "IX_EmergencyContacts_ModifiedByMemberId",
                table: "EmergencyContacts",
                column: "ModifiedByMemberId");

            migrationBuilder.CreateIndex(
                name: "IX_AdminUsers_CreatedByAdminId",
                table: "AdminUsers",
                column: "CreatedByAdminId");

            migrationBuilder.CreateIndex(
                name: "IX_AdminUsers_CreatedByMemberId",
                table: "AdminUsers",
                column: "CreatedByMemberId");

            migrationBuilder.CreateIndex(
                name: "IX_AdminUsers_ModifiedByAdminId",
                table: "AdminUsers",
                column: "ModifiedByAdminId");

            migrationBuilder.CreateIndex(
                name: "IX_AdminUsers_ModifiedByMemberId",
                table: "AdminUsers",
                column: "ModifiedByMemberId");

            migrationBuilder.CreateIndex(
                name: "IX_AdminTypes_CreatedByAdminId",
                table: "AdminTypes",
                column: "CreatedByAdminId");

            migrationBuilder.CreateIndex(
                name: "IX_AdminTypes_CreatedByMemberId",
                table: "AdminTypes",
                column: "CreatedByMemberId");

            migrationBuilder.CreateIndex(
                name: "IX_AdminTypes_ModifiedByAdminId",
                table: "AdminTypes",
                column: "ModifiedByAdminId");

            migrationBuilder.CreateIndex(
                name: "IX_AdminTypes_ModifiedByMemberId",
                table: "AdminTypes",
                column: "ModifiedByMemberId");


            migrationBuilder.CreateIndex(
                name: "IX_EventCategories_CreatedByAdminId",
                table: "EventCategories",
                column: "CreatedByAdminId");

            migrationBuilder.CreateIndex(
                name: "IX_EventCategories_CreatedByMemberId",
                table: "EventCategories",
                column: "CreatedByMemberId");

            migrationBuilder.CreateIndex(
                name: "IX_EventCategories_DisplayOrder",
                table: "EventCategories",
                column: "DisplayOrder");

            migrationBuilder.CreateIndex(
                name: "IX_EventCategories_ModifiedByAdminId",
                table: "EventCategories",
                column: "ModifiedByAdminId");

            migrationBuilder.CreateIndex(
                name: "IX_EventCategories_ModifiedByMemberId",
                table: "EventCategories",
                column: "ModifiedByMemberId");

            migrationBuilder.CreateIndex(
                name: "IX_EventRegistrations_CreatedByAdminId",
                table: "EventRegistrations",
                column: "CreatedByAdminId");

            migrationBuilder.CreateIndex(
                name: "IX_EventRegistrations_CreatedByMemberId",
                table: "EventRegistrations",
                column: "CreatedByMemberId");

            migrationBuilder.CreateIndex(
                name: "IX_EventRegistrations_EventId_MemberId",
                table: "EventRegistrations",
                columns: new[] { "EventId", "MemberId" },
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_EventRegistrations_MemberId",
                table: "EventRegistrations",
                column: "MemberId");

            migrationBuilder.CreateIndex(
                name: "IX_EventRegistrations_ModifiedByAdminId",
                table: "EventRegistrations",
                column: "ModifiedByAdminId");

            migrationBuilder.CreateIndex(
                name: "IX_EventRegistrations_ModifiedByMemberId",
                table: "EventRegistrations",
                column: "ModifiedByMemberId");

            migrationBuilder.CreateIndex(
                name: "IX_EventRegistrations_Status",
                table: "EventRegistrations",
                column: "Status");

            migrationBuilder.CreateIndex(
                name: "IX_Events_CreatedByAdminId",
                table: "Events",
                column: "CreatedByAdminId");

            migrationBuilder.CreateIndex(
                name: "IX_Events_CreatedByMemberId",
                table: "Events",
                column: "CreatedByMemberId");

            migrationBuilder.CreateIndex(
                name: "IX_Events_EventCategoryId",
                table: "Events",
                column: "EventCategoryId");

            migrationBuilder.CreateIndex(
                name: "IX_Events_IsPublished",
                table: "Events",
                column: "IsPublished");

            migrationBuilder.CreateIndex(
                name: "IX_Events_ModifiedByAdminId",
                table: "Events",
                column: "ModifiedByAdminId");

            migrationBuilder.CreateIndex(
                name: "IX_Events_ModifiedByMemberId",
                table: "Events",
                column: "ModifiedByMemberId");

            migrationBuilder.CreateIndex(
                name: "IX_Events_StartDate",
                table: "Events",
                column: "StartDate");

            migrationBuilder.AddForeignKey(
                name: "FK_AdminTypes_AdminUsers_CreatedByAdminId",
                table: "AdminTypes",
                column: "CreatedByAdminId",
                principalTable: "AdminUsers",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "FK_AdminTypes_AdminUsers_ModifiedByAdminId",
                table: "AdminTypes",
                column: "ModifiedByAdminId",
                principalTable: "AdminUsers",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "FK_AdminTypes_Members_CreatedByMemberId",
                table: "AdminTypes",
                column: "CreatedByMemberId",
                principalTable: "Members",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "FK_AdminTypes_Members_ModifiedByMemberId",
                table: "AdminTypes",
                column: "ModifiedByMemberId",
                principalTable: "Members",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "FK_AdminUsers_AdminUsers_CreatedByAdminId",
                table: "AdminUsers",
                column: "CreatedByAdminId",
                principalTable: "AdminUsers",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "FK_AdminUsers_AdminUsers_ModifiedByAdminId",
                table: "AdminUsers",
                column: "ModifiedByAdminId",
                principalTable: "AdminUsers",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "FK_AdminUsers_Members_CreatedByMemberId",
                table: "AdminUsers",
                column: "CreatedByMemberId",
                principalTable: "Members",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "FK_AdminUsers_Members_ModifiedByMemberId",
                table: "AdminUsers",
                column: "ModifiedByMemberId",
                principalTable: "Members",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "FK_EmergencyContacts_AdminUsers_CreatedByAdminId",
                table: "EmergencyContacts",
                column: "CreatedByAdminId",
                principalTable: "AdminUsers",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "FK_EmergencyContacts_AdminUsers_ModifiedByAdminId",
                table: "EmergencyContacts",
                column: "ModifiedByAdminId",
                principalTable: "AdminUsers",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "FK_EmergencyContacts_Members_CreatedByMemberId",
                table: "EmergencyContacts",
                column: "CreatedByMemberId",
                principalTable: "Members",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "FK_EmergencyContacts_Members_ModifiedByMemberId",
                table: "EmergencyContacts",
                column: "ModifiedByMemberId",
                principalTable: "Members",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "FK_Members_AdminUsers_CreatedByAdminId",
                table: "Members",
                column: "CreatedByAdminId",
                principalTable: "AdminUsers",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "FK_Members_AdminUsers_ModifiedByAdminId",
                table: "Members",
                column: "ModifiedByAdminId",
                principalTable: "AdminUsers",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "FK_Members_Members_CreatedByMemberId",
                table: "Members",
                column: "CreatedByMemberId",
                principalTable: "Members",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "FK_Members_Members_ModifiedByMemberId",
                table: "Members",
                column: "ModifiedByMemberId",
                principalTable: "Members",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "FK_Parents_AdminUsers_CreatedByAdminId",
                table: "Parents",
                column: "CreatedByAdminId",
                principalTable: "AdminUsers",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "FK_Parents_AdminUsers_ModifiedByAdminId",
                table: "Parents",
                column: "ModifiedByAdminId",
                principalTable: "AdminUsers",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "FK_Parents_Members_CreatedByMemberId",
                table: "Parents",
                column: "CreatedByMemberId",
                principalTable: "Members",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "FK_Parents_Members_ModifiedByMemberId",
                table: "Parents",
                column: "ModifiedByMemberId",
                principalTable: "Members",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_AdminTypes_AdminUsers_CreatedByAdminId",
                table: "AdminTypes");

            migrationBuilder.DropForeignKey(
                name: "FK_AdminTypes_AdminUsers_ModifiedByAdminId",
                table: "AdminTypes");

            migrationBuilder.DropForeignKey(
                name: "FK_AdminTypes_Members_CreatedByMemberId",
                table: "AdminTypes");

            migrationBuilder.DropForeignKey(
                name: "FK_AdminTypes_Members_ModifiedByMemberId",
                table: "AdminTypes");

            migrationBuilder.DropForeignKey(
                name: "FK_AdminUsers_AdminUsers_CreatedByAdminId",
                table: "AdminUsers");

            migrationBuilder.DropForeignKey(
                name: "FK_AdminUsers_AdminUsers_ModifiedByAdminId",
                table: "AdminUsers");

            migrationBuilder.DropForeignKey(
                name: "FK_AdminUsers_Members_CreatedByMemberId",
                table: "AdminUsers");

            migrationBuilder.DropForeignKey(
                name: "FK_AdminUsers_Members_ModifiedByMemberId",
                table: "AdminUsers");

            migrationBuilder.DropForeignKey(
                name: "FK_EmergencyContacts_AdminUsers_CreatedByAdminId",
                table: "EmergencyContacts");

            migrationBuilder.DropForeignKey(
                name: "FK_EmergencyContacts_AdminUsers_ModifiedByAdminId",
                table: "EmergencyContacts");

            migrationBuilder.DropForeignKey(
                name: "FK_EmergencyContacts_Members_CreatedByMemberId",
                table: "EmergencyContacts");

            migrationBuilder.DropForeignKey(
                name: "FK_EmergencyContacts_Members_ModifiedByMemberId",
                table: "EmergencyContacts");

            migrationBuilder.DropForeignKey(
                name: "FK_Members_AdminUsers_CreatedByAdminId",
                table: "Members");

            migrationBuilder.DropForeignKey(
                name: "FK_Members_AdminUsers_ModifiedByAdminId",
                table: "Members");

            migrationBuilder.DropForeignKey(
                name: "FK_Members_Members_CreatedByMemberId",
                table: "Members");

            migrationBuilder.DropForeignKey(
                name: "FK_Members_Members_ModifiedByMemberId",
                table: "Members");

            migrationBuilder.DropForeignKey(
                name: "FK_Parents_AdminUsers_CreatedByAdminId",
                table: "Parents");

            migrationBuilder.DropForeignKey(
                name: "FK_Parents_AdminUsers_ModifiedByAdminId",
                table: "Parents");

            migrationBuilder.DropForeignKey(
                name: "FK_Parents_Members_CreatedByMemberId",
                table: "Parents");

            migrationBuilder.DropForeignKey(
                name: "FK_Parents_Members_ModifiedByMemberId",
                table: "Parents");

            migrationBuilder.DropTable(
                name: "AuditLogs");

            migrationBuilder.DropTable(
                name: "EventRegistrations");

            migrationBuilder.DropTable(
                name: "Events");

            migrationBuilder.DropTable(
                name: "EventCategories");

            migrationBuilder.DropIndex(
                name: "IX_Parents_CreatedByAdminId",
                table: "Parents");

            migrationBuilder.DropIndex(
                name: "IX_Parents_CreatedByMemberId",
                table: "Parents");

            migrationBuilder.DropIndex(
                name: "IX_Parents_ModifiedByAdminId",
                table: "Parents");

            migrationBuilder.DropIndex(
                name: "IX_Parents_ModifiedByMemberId",
                table: "Parents");

            migrationBuilder.DropIndex(
                name: "IX_Members_CreatedByAdminId",
                table: "Members");

            migrationBuilder.DropIndex(
                name: "IX_Members_CreatedByMemberId",
                table: "Members");

            migrationBuilder.DropIndex(
                name: "IX_Members_ModifiedByAdminId",
                table: "Members");

            migrationBuilder.DropIndex(
                name: "IX_Members_ModifiedByMemberId",
                table: "Members");

            migrationBuilder.DropIndex(
                name: "IX_EmergencyContacts_CreatedByAdminId",
                table: "EmergencyContacts");

            migrationBuilder.DropIndex(
                name: "IX_EmergencyContacts_CreatedByMemberId",
                table: "EmergencyContacts");

            migrationBuilder.DropIndex(
                name: "IX_EmergencyContacts_ModifiedByAdminId",
                table: "EmergencyContacts");

            migrationBuilder.DropIndex(
                name: "IX_EmergencyContacts_ModifiedByMemberId",
                table: "EmergencyContacts");

            migrationBuilder.DropIndex(
                name: "IX_AdminUsers_CreatedByAdminId",
                table: "AdminUsers");

            migrationBuilder.DropIndex(
                name: "IX_AdminUsers_CreatedByMemberId",
                table: "AdminUsers");

            migrationBuilder.DropIndex(
                name: "IX_AdminUsers_ModifiedByAdminId",
                table: "AdminUsers");

            migrationBuilder.DropIndex(
                name: "IX_AdminUsers_ModifiedByMemberId",
                table: "AdminUsers");

            migrationBuilder.DropIndex(
                name: "IX_AdminTypes_CreatedByAdminId",
                table: "AdminTypes");

            migrationBuilder.DropIndex(
                name: "IX_AdminTypes_CreatedByMemberId",
                table: "AdminTypes");

            migrationBuilder.DropIndex(
                name: "IX_AdminTypes_ModifiedByAdminId",
                table: "AdminTypes");

            migrationBuilder.DropIndex(
                name: "IX_AdminTypes_ModifiedByMemberId",
                table: "AdminTypes");

            migrationBuilder.DropColumn(
                name: "CreatedByAdminId",
                table: "Parents");

            migrationBuilder.DropColumn(
                name: "CreatedByMemberId",
                table: "Parents");

            migrationBuilder.DropColumn(
                name: "CreatedBySource",
                table: "Parents");

            migrationBuilder.DropColumn(
                name: "DateModified",
                table: "Parents");

            migrationBuilder.DropColumn(
                name: "ModifiedByAdminId",
                table: "Parents");

            migrationBuilder.DropColumn(
                name: "ModifiedByMemberId",
                table: "Parents");

            migrationBuilder.DropColumn(
                name: "ModifiedBySource",
                table: "Parents");

            migrationBuilder.DropColumn(
                name: "CreatedByAdminId",
                table: "Members");

            migrationBuilder.DropColumn(
                name: "CreatedByMemberId",
                table: "Members");

            migrationBuilder.DropColumn(
                name: "CreatedBySource",
                table: "Members");

            migrationBuilder.DropColumn(
                name: "DateModified",
                table: "Members");

            migrationBuilder.DropColumn(
                name: "ModifiedByAdminId",
                table: "Members");

            migrationBuilder.DropColumn(
                name: "ModifiedByMemberId",
                table: "Members");

            migrationBuilder.DropColumn(
                name: "ModifiedBySource",
                table: "Members");

            migrationBuilder.DropColumn(
                name: "CreatedByAdminId",
                table: "EmergencyContacts");

            migrationBuilder.DropColumn(
                name: "CreatedByMemberId",
                table: "EmergencyContacts");

            migrationBuilder.DropColumn(
                name: "CreatedBySource",
                table: "EmergencyContacts");

            migrationBuilder.DropColumn(
                name: "DateModified",
                table: "EmergencyContacts");

            migrationBuilder.DropColumn(
                name: "ModifiedByAdminId",
                table: "EmergencyContacts");

            migrationBuilder.DropColumn(
                name: "ModifiedByMemberId",
                table: "EmergencyContacts");

            migrationBuilder.DropColumn(
                name: "ModifiedBySource",
                table: "EmergencyContacts");

            migrationBuilder.DropColumn(
                name: "CreatedByAdminId",
                table: "AdminUsers");

            migrationBuilder.DropColumn(
                name: "CreatedByMemberId",
                table: "AdminUsers");

            migrationBuilder.DropColumn(
                name: "CreatedBySource",
                table: "AdminUsers");

            migrationBuilder.DropColumn(
                name: "DateModified",
                table: "AdminUsers");

            migrationBuilder.DropColumn(
                name: "ModifiedByAdminId",
                table: "AdminUsers");

            migrationBuilder.DropColumn(
                name: "ModifiedByMemberId",
                table: "AdminUsers");

            migrationBuilder.DropColumn(
                name: "ModifiedBySource",
                table: "AdminUsers");

            migrationBuilder.DropColumn(
                name: "CreatedByAdminId",
                table: "AdminTypes");

            migrationBuilder.DropColumn(
                name: "CreatedByMemberId",
                table: "AdminTypes");

            migrationBuilder.DropColumn(
                name: "CreatedBySource",
                table: "AdminTypes");

            migrationBuilder.DropColumn(
                name: "DateModified",
                table: "AdminTypes");

            migrationBuilder.DropColumn(
                name: "ModifiedByAdminId",
                table: "AdminTypes");

            migrationBuilder.DropColumn(
                name: "ModifiedByMemberId",
                table: "AdminTypes");

            migrationBuilder.DropColumn(
                name: "ModifiedBySource",
                table: "AdminTypes");
        }
    }
}
