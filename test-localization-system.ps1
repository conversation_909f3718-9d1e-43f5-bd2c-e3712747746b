#!/usr/bin/env pwsh

# Enhanced Localization System Tests
# Tests the new localization services and functionality

Write-Host "🌐 Enhanced Localization System Tests" -ForegroundColor Cyan
Write-Host "Testing LocalizationKeys, CultureAwareFormatting, KeyDetection, and Validation Services" -ForegroundColor Cyan
Write-Host "=" * 80 -ForegroundColor Gray

$testsPassed = 0
$testsTotal = 0
$criticalFailures = @()

function Test-Result {
    param($testName, $condition, $isCritical = $false)
    $script:testsTotal++
    if ($condition) {
        Write-Host "✅ $testName" -ForegroundColor Green
        $script:testsPassed++
    } else {
        Write-Host "❌ $testName" -ForegroundColor Red
        if ($isCritical) {
            $script:criticalFailures += $testName
        }
    }
}

# Test 1: Build Verification
Write-Host "`n🔨 Build and Compilation Tests" -ForegroundColor Yellow
Write-Host "-" * 40

$buildResult = dotnet build ParaHockeyApp.csproj --no-restore --verbosity quiet
$buildSuccess = $LASTEXITCODE -eq 0
Test-Result "Application builds without errors" $buildSuccess $true

# Test 2: Service File Existence
Write-Host "`n📁 Service File Tests" -ForegroundColor Yellow
Write-Host "-" * 40

$localizationKeysExists = Test-Path "Services/LocalizationKeys.cs"
$cultureFormattingExists = Test-Path "Services/CultureAwareFormattingService.cs"
$keyDetectionExists = Test-Path "Services/LocalizationKeyDetectionService.cs"
$validationServiceExists = Test-Path "Services/LocalizationValidationService.cs"
$cultureValidationExists = Test-Path "Services/CultureSpecificValidationService.cs"

Test-Result "LocalizationKeys class exists" $localizationKeysExists $true
Test-Result "CultureAwareFormattingService exists" $cultureFormattingExists $true
Test-Result "LocalizationKeyDetectionService exists" $keyDetectionExists $true
Test-Result "LocalizationValidationService exists" $validationServiceExists $true
Test-Result "CultureSpecificValidationService exists" $cultureValidationExists $true

# Test 3: LocalizationKeys Structure
Write-Host "`nLocalizationKeys Structure Tests" -ForegroundColor Yellow
Write-Host "-" * 40

if ($localizationKeysExists) {
    $keysContent = Get-Content "Services/LocalizationKeys.cs" -Raw
    
    Test-Result "Forms class defined" ($keysContent -match "public static class Forms")
    Test-Result "Validation class defined" ($keysContent -match "public static class Validation")
    Test-Result "UI class defined" ($keysContent -match "public static class UI")
    Test-Result "Pages class defined" ($keysContent -match "public static class Pages")
    Test-Result "Errors class defined" ($keysContent -match "public static class Errors")
    Test-Result "Success class defined" ($keysContent -match "public static class Success")
    Test-Result "Accessibility class defined" ($keysContent -match "public static class Accessibility")
    Test-Result "DateTime class defined" ($keysContent -match "public static class DateTime")
    
    # Test specific key examples
    Test-Result "Member form keys defined" ($keysContent -match "FirstNameLabel.*Form\.Member\.FirstName\.Label")
    Test-Result "Validation keys defined" ($keysContent -match "Required.*Validation\.Required")
    Test-Result "UI action keys defined" ($keysContent -match "Save.*UI\.Action\.Save")
}

# Test 4: CultureAwareFormattingService Interface
Write-Host "`nCultureAwareFormattingService Tests" -ForegroundColor Yellow
Write-Host "-" * 40

if ($cultureFormattingExists) {
    $formattingContent = Get-Content "Services/CultureAwareFormattingService.cs" -Raw
    
    Test-Result "ICultureAwareFormattingService interface defined" ($formattingContent -match "interface ICultureAwareFormattingService")
    Test-Result "FormatDate methods defined" ($formattingContent -match "FormatDate.*DateTime")
    Test-Result "FormatPhoneNumber method defined" ($formattingContent -match "FormatPhoneNumber.*string")
    Test-Result "FormatPostalCode method defined" ($formattingContent -match "FormatPostalCode.*string")
    Test-Result "ValidatePhoneNumber method defined" ($formattingContent -match "IsValidPhoneNumber.*string")
    Test-Result "ValidatePostalCode method defined" ($formattingContent -match "IsValidPostalCode.*string")
    Test-Result "Canadian phone regex defined" ($formattingContent -match "CanadianPhoneRegex")
    Test-Result "Canadian postal code regex defined" ($formattingContent -match "CanadianPostalCodeRegex")
}

# Test 5: LocalizationKeyDetectionService Features
Write-Host "`nLocalizationKeyDetectionService Tests" -ForegroundColor Yellow
Write-Host "-" * 40

if ($keyDetectionExists) {
    $detectionContent = Get-Content "Services/LocalizationKeyDetectionService.cs" -Raw
    
    Test-Result "ILocalizationKeyDetectionService interface defined" ($detectionContent -match "interface ILocalizationKeyDetectionService")
    Test-Result "GetLocalizedString method defined" ($detectionContent -match "GetLocalizedString.*string")
    Test-Result "GetMissingKeysAsync method defined" ($detectionContent -match "GetMissingKeysAsync")
    Test-Result "ExportMissingKeysAsync method defined" ($detectionContent -match "ExportMissingKeysAsync")
    Test-Result "ValidateAllKeysAsync method defined" ($detectionContent -match "ValidateAllKeysAsync")
    Test-Result "MissingLocalizationKey model defined" ($detectionContent -match "class MissingLocalizationKey")
    Test-Result "LocalizationDetectionOptions defined" ($detectionContent -match "class LocalizationDetectionOptions")
    Test-Result "ConcurrentDictionary for thread safety" ($detectionContent -match "ConcurrentDictionary")
}

# Test 6: LocalizationValidationService Scanning
Write-Host "`nLocalizationValidationService Tests" -ForegroundColor Yellow
Write-Host "-" * 40

if ($validationServiceExists) {
    $validationContent = Get-Content "Services/LocalizationValidationService.cs" -Raw
    
    Test-Result "ILocalizationValidationService interface defined" ($validationContent -match "interface ILocalizationValidationService")
    Test-Result "ScanViewsAsync method defined" ($validationContent -match "ScanViewsAsync")
    Test-Result "ScanControllersAsync method defined" ($validationContent -match "ScanControllersAsync")
    Test-Result "ScanJavaScriptAsync method defined" ($validationContent -match "ScanJavaScriptAsync")
    Test-Result "PerformComprehensiveScanAsync method defined" ($validationContent -match "PerformComprehensiveScanAsync")
    Test-Result "Hard-coded text regex patterns defined" ($validationContent -match "HardCodedTextInViews.*Regex")
    Test-Result "LocalizationScanResult model defined" ($validationContent -match "class LocalizationScanResult")
    Test-Result "LocalizationIssue model defined" ($validationContent -match "class LocalizationIssue")
}

# Test 7: CultureSpecificValidationService Features
Write-Host "`nCultureSpecificValidationService Tests" -ForegroundColor Yellow
Write-Host "-" * 40

if ($cultureValidationExists) {
    $cultureValidationContent = Get-Content "Services/CultureSpecificValidationService.cs" -Raw
    
    Test-Result "ICultureSpecificValidationService interface defined" ($cultureValidationContent -match "interface ICultureSpecificValidationService")
    Test-Result "ValidateModel method defined" ($cultureValidationContent -match "ValidateModel.*T.*where T : class")
    Test-Result "ValidatePhoneNumber method defined" ($cultureValidationContent -match "ValidatePhoneNumber.*string")
    Test-Result "ValidatePostalCode method defined" ($cultureValidationContent -match "ValidatePostalCode.*string")
    Test-Result "ValidateDate method defined" ($cultureValidationContent -match "ValidateDate.*string")
    Test-Result "ValidateEmail method defined" ($cultureValidationContent -match "ValidateEmail.*string")
    Test-Result "Custom validation attributes defined" ($cultureValidationContent -match "CanadianPhoneAttribute.*ValidationAttribute")
    Test-Result "ValidationResult model defined" ($cultureValidationContent -match "class ValidationResult")
    Test-Result "ValidationError model defined" ($cultureValidationContent -match "class ValidationError")
}

# Test 8: Service Registration in Program.cs
Write-Host "`nService Registration Tests" -ForegroundColor Yellow
Write-Host "-" * 40

$programContent = Get-Content "Program.cs" -Raw
Test-Result "ICultureAwareFormattingService registered" ($programContent -match "AddScoped<ICultureAwareFormattingService, CultureAwareFormattingService>") $true
Test-Result "ILocalizationKeyDetectionService registered" ($programContent -match "AddScoped<ILocalizationKeyDetectionService, LocalizationKeyDetectionService>") $true
Test-Result "ILocalizationValidationService registered" ($programContent -match "AddScoped<ILocalizationValidationService, LocalizationValidationService>") $true
Test-Result "ICultureSpecificValidationService registered" ($programContent -match "AddScoped<ICultureSpecificValidationService, CultureSpecificValidationService>") $true
Test-Result "LocalizationDetectionOptions configured" ($programContent -match "Configure<LocalizationDetectionOptions>") $true

# Test 9: Code Quality and Best Practices
Write-Host "`nCode Quality Tests" -ForegroundColor Yellow
Write-Host "-" * 40

# Check for proper async/await patterns
$asyncPatternCorrect = $true
$serviceFiles = @(
    "Services/CultureAwareFormattingService.cs",
    "Services/LocalizationKeyDetectionService.cs", 
    "Services/LocalizationValidationService.cs",
    "Services/CultureSpecificValidationService.cs"
)

foreach ($file in $serviceFiles) {
    if (Test-Path $file) {
        $content = Get-Content $file -Raw
        # Check for proper async method signatures
        if ($content -match "async.*Task" -and $content -notmatch "await") {
            $asyncPatternCorrect = $false
        }
    }
}

Test-Result "Proper async/await patterns used" $asyncPatternCorrect
Test-Result "Interfaces defined for all services" ($cultureFormattingExists -and $keyDetectionExists -and $validationServiceExists -and $cultureValidationExists)
Test-Result "Logging implemented in services" ($formattingContent -match "ILogger" -and $detectionContent -match "ILogger")
Test-Result "Exception handling implemented" ($formattingContent -match "try.*catch" -and $detectionContent -match "try.*catch")

# Test 10: Integration Readiness
Write-Host "`nIntegration Readiness Tests" -ForegroundColor Yellow
Write-Host "-" * 40

Test-Result "All service interfaces are public" ($true) # Verified by compilation
Test-Result "Services use dependency injection" ($formattingContent -match "ILogger" -and $detectionContent -match "IStringLocalizer")
Test-Result "Services support culture parameters" ($formattingContent -match "culture.*string" -and $cultureValidationContent -match "culture.*string")
Test-Result "Thread-safe implementations where needed" ($detectionContent -match "ConcurrentDictionary")

# Test 11: Documentation and Comments
Write-Host "`nDocumentation Tests" -ForegroundColor Yellow
Write-Host "-" * 40

$documentationComplete = $true
foreach ($file in $serviceFiles) {
    if (Test-Path $file) {
        $content = Get-Content $file -Raw
        if ($content -notmatch "/// <summary>" -or $content -notmatch "/// </summary>") {
            $documentationComplete = $false
        }
    }
}

Test-Result "XML documentation comments present" $documentationComplete
Test-Result "Interface methods documented" ($cultureFormattingExists -and ($formattingContent -match "/// <summary>"))
Test-Result "Service classes documented" ($keyDetectionExists -and ($detectionContent -match "/// <summary>"))

# Summary
Write-Host "`n" + "=" * 80 -ForegroundColor Gray
Write-Host "🌐 Enhanced Localization System Test Results" -ForegroundColor Cyan

$passRate = [math]::Round(($testsPassed / $testsTotal) * 100, 1)
Write-Host "Tests Passed: $testsPassed / $testsTotal ($passRate%)" -ForegroundColor $(if ($passRate -ge 90) { "Green" } elseif ($passRate -ge 75) { "Yellow" } else { "Red" })

if ($criticalFailures.Count -gt 0) {
    Write-Host "`nCritical Failures:" -ForegroundColor Red
    foreach ($failure in $criticalFailures) {
        Write-Host "   - $failure" -ForegroundColor Red
    }
    Write-Host "`nIMPLEMENTATION INCOMPLETE - Fix critical failures" -ForegroundColor Red
    exit 1
} elseif ($passRate -ge 90) {
    Write-Host "`nENHANCED LOCALIZATION SYSTEM READY" -ForegroundColor Green
    Write-Host "Implementation Summary:" -ForegroundColor Cyan
    Write-Host "   - LocalizationKeys static class with semantic organization" -ForegroundColor White
    Write-Host "   - CultureAwareFormattingService for dates, numbers, phone formatting" -ForegroundColor White
    Write-Host "   - LocalizationKeyDetectionService with runtime logging" -ForegroundColor White
    Write-Host "   - LocalizationValidationService to ensure all UI text uses resource keys" -ForegroundColor White
    Write-Host "   - CultureSpecificValidationService with localized error messages" -ForegroundColor White
    Write-Host "   - All services registered in dependency injection" -ForegroundColor White
    Write-Host "   - Thread-safe implementations and proper error handling" -ForegroundColor White
    Write-Host "`nNext Steps:" -ForegroundColor Cyan
    Write-Host "   1. Test the services manually by running the application" -ForegroundColor White
    Write-Host "   2. Update existing views to use LocalizationKeys constants" -ForegroundColor White
    Write-Host "   3. Run localization validation scan to identify hard-coded text" -ForegroundColor White
    Write-Host "   4. Commit and push changes, then merge to main" -ForegroundColor White
    exit 0
} else {
    Write-Host "`nIMPLEMENTATION NEEDS IMPROVEMENT" -ForegroundColor Yellow
    Write-Host "Review failed tests and improve implementation quality" -ForegroundColor Yellow
    exit 0
}