# 🎯 Para Hockey Assets

This folder contains all static assets for the Para Hockey web application.

## 📁 Folder Structure

```
assets/
├── images/          📸 General images (photos, graphics, backgrounds)
├── logos/           🏒 Branding (Para Hockey logos, sponsor logos)
├── videos/          🎥 Video content (game highlights, promotional videos)
├── audio/           🔊 Audio files (sounds, notifications, music)
└── documents/       📄 Documents (PDFs, rules, forms for download)
```

## 📋 Guidelines

### **File Naming Convention**

-   Use **lowercase** with **hyphens** for spaces
-   Be **descriptive** and **specific**
-   Include **dimensions** for images when relevant

**Examples:**

```
✅ Good:
- para-hockey-logo-main-300x150.png
- game-highlight-2024-championship.mp4
- notification-goal-scored.wav
- tournament-rules-2024.pdf

❌ Avoid:
- Logo.PNG
- video1.mp4
- sound.wav
- doc.pdf
```

### **Image Guidelines**

-   **Logos**: PNG with transparent background
-   **Photos**: WEBP or JPG (optimized for web)
-   **Icons**: SVG preferred for scalability
-   **Max file size**: 2MB for images, optimize for web

### **Video Guidelines**

-   **Format**: MP4 (H.264 codec)
-   **Max file size**: 50MB
-   **Resolution**: 1920x1080 max for web
-   Consider creating thumbnail images

### **Audio Guidelines**

-   **Format**: WAV for high quality, MP3 for web
-   **Max file size**: 5MB
-   **Use cases**: UI sounds, notifications, accessibility

### **Documents Guidelines**

-   **Format**: PDF preferred
-   **Naming**: Include version and date
-   **Max file size**: 10MB

## 🌐 Usage in Code

### **In Razor Views**

```html
<!-- Images -->
<img src="~/assets/images/game-action-shot.jpg" alt="Para Hockey Game" />

<!-- Logos -->
<img src="~/assets/logos/para-hockey-logo-main.png" alt="Para Hockey" />

<!-- Documents -->
<a href="~/assets/documents/tournament-rules-2024.pdf" download
    >Download Rules</a
>
```

### **In CSS**

```css
.hero-banner {
    background-image: url("/assets/images/ice-rink-background.jpg");
}

.logo {
    background-image: url("/assets/logos/para-hockey-icon.svg");
}
```

### **In JavaScript**

```javascript
// Audio
const goalSound = new Audio("/assets/audio/goal-celebration.wav");

// Dynamic images
const playerImage = "/assets/images/players/" + playerId + ".jpg";
```

## 📊 Asset Optimization

### **Tools for Optimization**

-   **Images**: TinyPNG, ImageOptim, Squoosh
-   **Videos**: HandBrake, FFmpeg
-   **Audio**: Audacity, FFmpeg

### **Performance Tips**

-   Use **WebP** format for modern browsers
-   Implement **lazy loading** for images
-   Create **different sizes** for responsive design
-   Use **CDN** for large files in production

## 🏒 Para Hockey Specific Assets

### **Expected Assets**

-   Para Hockey federation logos
-   Team logos and emblems
-   Player photos (with consent)
-   Game action shots
-   Awards and trophies images
-   Sponsor logos
-   Arena/venue photos

### **Accessibility**

-   Always include **alt text** for images
-   Provide **transcripts** for videos
-   Include **captions** for video content
-   Use **descriptive filenames**

---

**Last Updated**: December 2024  
**Maintained By**: Para Hockey Development Team
