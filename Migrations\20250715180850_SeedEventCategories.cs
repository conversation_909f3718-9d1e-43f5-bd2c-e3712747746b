﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace ParaHockeyApp.Migrations
{
    /// <inheritdoc />
    public partial class SeedEventCategories : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            // Defensive check: Ensure EventCategories table exists before seeding
            migrationBuilder.Sql(@"
                IF EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'EventCategories')
                BEGIN
                    -- Only insert categories that don't already exist
                    IF NOT EXISTS (SELECT 1 FROM EventCategories WHERE Id = 1)
                    BEGIN
                        SET IDENTITY_INSERT EventCategories ON
                        INSERT INTO EventCategories (Id, DisplayNameKey, DescriptionKey, Color, IconClass, DisplayOrder, RequiresRegistration, MaxParticipants, DateCreated, IsActive, CreatedBySource)
                        VALUES 
                            (1, 'EventCategory_Practice', 'EventCategory_Practice_Desc', '#28a745', 'fas fa-skating', 1, 0, -1, '2025-07-15T18:08:50Z', 1, 0),
                            (2, 'EventCategory_Game', 'EventCategory_Game_Desc', '#dc3545', 'fas fa-hockey-puck', 2, 0, -1, '2025-07-15T18:08:50Z', 1, 0),
                            (3, 'EventCategory_Tournament', 'EventCategory_Tournament_Desc', '#ffc107', 'fas fa-trophy', 3, 1, 50, '2025-07-15T18:08:50Z', 1, 0),
                            (4, 'EventCategory_Training', 'EventCategory_Training_Desc', '#17a2b8', 'fas fa-dumbbell', 4, 1, 20, '2025-07-15T18:08:50Z', 1, 0),
                            (5, 'EventCategory_Meeting', 'EventCategory_Meeting_Desc', '#6c757d', 'fas fa-users', 5, 1, 30, '2025-07-15T18:08:50Z', 1, 0),
                            (6, 'EventCategory_Social', 'EventCategory_Social_Desc', '#e83e8c', 'fas fa-glass-cheers', 6, 1, 100, '2025-07-15T18:08:50Z', 1, 0),
                            (7, 'EventCategory_Fundraiser', 'EventCategory_Fundraiser_Desc', '#fd7e14', 'fas fa-donate', 7, 1, -1, '2025-07-15T18:08:50Z', 1, 0),
                            (8, 'EventCategory_Other', 'EventCategory_Other_Desc', '#6f42c1', 'fas fa-calendar-alt', 8, 0, -1, '2025-07-15T18:08:50Z', 1, 0)
                        SET IDENTITY_INSERT EventCategories OFF
                    END
                    ELSE
                    BEGIN
                        PRINT 'Event categories already exist - skipping seed operation'
                    END
                END
                ELSE
                BEGIN
                    RAISERROR('EventCategories table does not exist. Ensure AddEventSystem migration runs first.', 16, 1)
                END
            ");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            // Defensive removal of seeded event categories
            migrationBuilder.Sql(@"
                IF EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'EventCategories')
                BEGIN
                    DELETE FROM EventCategories WHERE Id IN (1, 2, 3, 4, 5, 6, 7, 8)
                    PRINT 'Removed seeded event categories'
                END
                ELSE
                BEGIN
                    PRINT 'EventCategories table does not exist - nothing to remove'
                END
            ");
        }
    }
}
