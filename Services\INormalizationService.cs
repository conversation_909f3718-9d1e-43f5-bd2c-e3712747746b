using ParaHockeyApp.Models.Entities;

namespace ParaHockeyApp.Services
{
    /// <summary>
    /// Service for normalizing and cleaning imported member data
    /// </summary>
    public interface INormalizationService
    {
        /// <summary>
        /// Normalizes email address (lowercase, trim)
        /// </summary>
        /// <param name="email">Raw email address</param>
        /// <returns>Normalized email or null if invalid</returns>
        string? NormalizeEmail(string? email);

        /// <summary>
        /// Normalizes phone number (strip non-digits, format)
        /// </summary>
        /// <param name="phone">Raw phone number</param>
        /// <returns>Normalized phone number or null if invalid</returns>
        string? NormalizePhone(string? phone);

        /// <summary>
        /// Normalizes postal code (uppercase, remove spaces, add space if Canadian)
        /// </summary>
        /// <param name="postalCode">Raw postal code</param>
        /// <returns>Normalized postal code or null if invalid</returns>
        string? NormalizePostalCode(string? postalCode);

        /// <summary>
        /// Normalizes name (trim, proper case)
        /// </summary>
        /// <param name="name">Raw name</param>
        /// <returns>Normalized name or null if empty</returns>
        string? NormalizeName(string? name);

        /// <summary>
        /// Normalizes address components and combines them
        /// </summary>
        /// <param name="unitNumber">Unit number</param>
        /// <param name="streetNumber">Street number</param>
        /// <param name="streetName">Street name</param>
        /// <returns>Complete normalized address</returns>
        string? NormalizeAddress(string? unitNumber, string? streetNumber, string? streetName);

        /// <summary>
        /// Normalizes gender text to match database values
        /// </summary>
        /// <param name="genderText">Raw gender text from CSV</param>
        /// <returns>Normalized gender identifier or null if not recognized</returns>
        string? NormalizeGender(string? genderText);

        /// <summary>
        /// Normalizes province code
        /// </summary>
        /// <param name="provinceText">Raw province text</param>
        /// <returns>Normalized province code or null if not recognized</returns>
        string? NormalizeProvince(string? provinceText);

        /// <summary>
        /// Applies all applicable normalizations to a TempMember
        /// </summary>
        /// <param name="tempMember">TempMember to normalize</param>
        /// <returns>The same TempMember with normalized fields</returns>
        TempMember NormalizeTempMember(TempMember tempMember);
    }
}