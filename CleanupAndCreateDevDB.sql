-- Clean up old databases and create ParaHockeyDB_DEV
-- Run this script in SQL Server Management Studio connected to .\SQLEXPRESS

USE master;
GO

-- Drop ParaHockeyDB_TEST if it exists
IF DB_ID('ParaHockeyDB_TEST') IS NOT NULL
BEGIN
    PRINT 'Dropping ParaHockeyDB_TEST database...';
    ALTER DATABASE ParaHockeyDB_TEST SET SINGLE_USER WITH ROLLBACK IMMEDIATE;
    DROP DATABASE ParaHockeyDB_TEST;
    PRINT 'ParaHockeyDB_TEST dropped successfully.';
END
ELSE
BEGIN
    PRINT 'ParaHockeyDB_TEST does not exist.';
END
GO

-- Drop ParaHockeyDB if it exists
IF DB_ID('ParaHockeyDB') IS NOT NULL
BEGIN
    PRINT 'Dropping ParaHockeyDB database...';
    ALTER DATABASE ParaHockeyDB SET SINGLE_USER WITH ROLLBACK IMMEDIATE;
    DROP DATABASE ParaHockeyDB;
    PRINT 'ParaHockeyDB dropped successfully.';
END
ELSE
BEGIN
    PRINT 'ParaHockeyDB does not exist.';
END
GO

-- Create ParaHockeyDB_DEV
IF DB_ID('ParaHockeyDB_DEV') IS NULL
BEGIN
    PRINT 'Creating ParaHockeyDB_DEV database...';
    CREATE DATABASE ParaHockeyDB_DEV;
    PRINT 'ParaHockeyDB_DEV created successfully.';
END
ELSE
BEGIN
    PRINT 'ParaHockeyDB_DEV already exists.';
END
GO

PRINT 'Database cleanup and creation completed.';
PRINT 'Next step: Run "dotnet ef database update" to create tables and seed data.'; 