using ParaHockeyApp.Models.Entities;
using ParaHockeyApp.DTOs;

namespace ParaHockeyApp.Services
{
    public interface IEventService
    {
        // Event management
        Task<List<Event>> GetAllEventsAsync();
        Task<List<Event>> GetPublishedEventsAsync();
        Task<List<Event>> GetEventsBetweenDatesAsync(DateTime startDate, DateTime endDate);
        Task<Event?> GetEventByIdAsync(int id);
        Task<List<Event>> GetEventsByCategoryAsync(int categoryId);
        Task<Event> CreateEventAsync(Event eventEntity);
        Task<Event> UpdateEventAsync(Event eventEntity);
        Task<bool> DeleteEventAsync(int id);
        Task<bool> PublishEventAsync(int id);
        Task<bool> UnpublishEventAsync(int id);

        // Event categories
        Task<List<EventCategory>> GetAllEventCategoriesAsync();
        Task<List<EventCategory>> GetAllEventCategoriesSortedAsync();
        Task<EventCategory?> GetEventCategoryByIdAsync(int id);
        Task<EventCategory> CreateEventCategoryAsync(EventCategory category);
        Task<EventCategory> UpdateEventCategoryAsync(EventCategory category);
        Task<bool> DeleteEventCategoryAsync(int id);

        // Event registrations
        Task<List<EventRegistration>> GetEventRegistrationsAsync(int eventId);
        Task<List<EventRegistration>> GetMemberRegistrationsAsync(int memberId);
        Task<EventRegistration?> GetEventRegistrationAsync(int eventId, int memberId);
        Task<EventRegistration?> GetEventRegistrationByIdAsync(int registrationId);
        Task<EventRegistration> RegisterMemberForEventAsync(int eventId, int memberId, string? memberNotes = null, int guestCount = 0);
        Task<bool> CancelEventRegistrationAsync(int eventId, int memberId);
        Task<bool> ConfirmEventRegistrationAsync(int registrationId);
        Task<bool> RejectEventRegistrationAsync(int registrationId, string? adminNotes = null);
        Task<bool> MarkAttendanceAsync(int registrationId, bool attended, AdminContext? adminContext = null);
        Task<bool> UpdateRegistrationStatusAsync(int registrationId, RegistrationStatus newStatus, string? adminNotes, AdminContext adminContext);
        Task<bool> BulkUpdateRegistrationStatusAsync(List<int> registrationIds, RegistrationStatus newStatus, AdminContext adminContext);
        Task<bool> AddAdminNoteAsync(int registrationId, string adminNote, AdminContext adminContext);
        Task<List<EventRegistration>> GetEventRegistrationsOptimizedAsync(int eventId);
        Task<Dictionary<RegistrationStatus, int>> GetRegistrationCountsByStatusAsync(int eventId);
        Task<Dictionary<RegistrationStatus, List<EventRegistration>>> GetRegistrationsGroupedByStatusAsync(int eventId);

        // Statistics and reporting
        Task<int> GetEventCountAsync(bool publishedOnly = false);
        Task<int> GetRegistrationCountAsync(int eventId);
        Task<int> GetMemberEventCountAsync(int memberId);
        Task<List<Event>> GetUpcomingEventsAsync(int count = 10);
        Task<List<Event>> GetRecentEventsAsync(int count = 10);
        Task<bool> IsEventFullAsync(int eventId);
        Task<bool> CanMemberRegisterAsync(int eventId, int memberId);
        Task<List<Event>> GetSubscribableEventsAsync();

        // Calendar specific methods
        Task<List<Event>> GetCalendarEventsAsync(DateTime startDate, DateTime endDate, bool publishedOnly = true, int? categoryId = null);
        Task<List<object>> GetCalendarEventsJsonAsync(DateTime startDate, DateTime endDate, bool publishedOnly = true, int? categoryId = null);
        Task<List<object>> GetSubscribableCalendarEventsJsonAsync(DateTime startDate, DateTime endDate, int? categoryId = null);
        Task<List<Event>> GetEventsForDateRangeAsync(DateTime startDate, DateTime endDate);

        // Enhanced methods for event subscription system
        Task<EventDetailsResponse?> GetEventDetailsWithUserContextAsync(int eventId, UserContext userContext);
        Task<bool> CanUserRegisterAsync(int eventId, UserContext userContext);
    }
}