namespace ParaHockeyApp.Models.Entities;

/// <summary>
/// Configuration for JavaScript and CSS bundle optimization
/// </summary>
public class BundleConfiguration
{
    public List<BundleGroup> CssBundles { get; set; } = new();
    public List<BundleGroup> JavaScriptBundles { get; set; } = new();
    public DateTime GeneratedAt { get; set; }
    public string Environment { get; set; } = string.Empty;
}

/// <summary>
/// Represents a group of assets that should be bundled together
/// </summary>
public class BundleGroup
{
    public string Name { get; set; } = string.Empty;
    public List<string> Files { get; set; } = new();
    public BundleLoadStrategy LoadStrategy { get; set; }
    public int Priority { get; set; }
    public List<string> Dependencies { get; set; } = new();
    public bool IsMinified { get; set; }
    public long EstimatedSize { get; set; }
}

/// <summary>
/// Loading strategy for bundles
/// </summary>
public enum BundleLoadStrategy
{
    Critical,    // Load immediately (above-the-fold)
    Deferred,    // Load after page load
    LazyLoad,    // Load on demand
    Preload      // Preload for next navigation
}

/// <summary>
/// Result of critical CSS extraction
/// </summary>
public class CriticalCssResult
{
    public string PageName { get; set; } = string.Empty;
    public string CriticalCss { get; set; } = string.Empty;
    public string NonCriticalCss { get; set; } = string.Empty;
    public List<string> CriticalSelectors { get; set; } = new();
    public int CriticalCssSize { get; set; }
    public int NonCriticalCssSize { get; set; }
    public DateTime ExtractedAt { get; set; }
}

/// <summary>
/// Options for image optimization
/// </summary>
public class ImageOptimizationOptions
{
    public List<int> ResponsiveWidths { get; set; } = new() { 576, 768, 992, 1200 };
    public bool GenerateWebP { get; set; } = true;
    public bool GenerateAvif { get; set; } = false;
    public int Quality { get; set; } = 85;
    public bool PreserveOriginal { get; set; } = true;
    public string OutputDirectory { get; set; } = "optimized";
}

/// <summary>
/// Result of image optimization process
/// </summary>
public class ImageOptimizationResult
{
    public string OriginalPath { get; set; } = string.Empty;
    public List<OptimizedImageVariant> Variants { get; set; } = new();
    public long OriginalSize { get; set; }
    public long TotalOptimizedSize { get; set; }
    public double CompressionRatio { get; set; }
    public DateTime OptimizedAt { get; set; }
}

/// <summary>
/// Individual optimized image variant
/// </summary>
public class OptimizedImageVariant
{
    public string Path { get; set; } = string.Empty;
    public string Format { get; set; } = string.Empty;
    public int Width { get; set; }
    public int Height { get; set; }
    public long Size { get; set; }
    public string MediaQuery { get; set; } = string.Empty;
}

/// <summary>
/// Result of database query optimization analysis
/// </summary>
public class QueryOptimizationResult
{
    public List<QueryIssue> Issues { get; set; } = new();
    public List<QueryRecommendation> Recommendations { get; set; } = new();
    public int TotalQueriesAnalyzed { get; set; }
    public DateTime AnalyzedAt { get; set; }
}

/// <summary>
/// Database query performance issue
/// </summary>
public class QueryIssue
{
    public string QueryType { get; set; } = string.Empty;
    public string Location { get; set; } = string.Empty;
    public QueryIssueSeverity Severity { get; set; }
    public string Description { get; set; } = string.Empty;
    public string Example { get; set; } = string.Empty;
    public int EstimatedImpact { get; set; }
}

/// <summary>
/// Recommendation for query optimization
/// </summary>
public class QueryRecommendation
{
    public string Title { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public string CodeExample { get; set; } = string.Empty;
    public QueryOptimizationPriority Priority { get; set; }
    public List<string> AffectedQueries { get; set; } = new();
}

/// <summary>
/// Severity levels for query issues
/// </summary>
public enum QueryIssueSeverity
{
    Low = 1,
    Medium = 2,
    High = 3,
    Critical = 4
}

/// <summary>
/// Priority levels for optimization recommendations
/// </summary>
public enum QueryOptimizationPriority
{
    Low = 1,
    Medium = 2,
    High = 3,
    Critical = 4
}

/// <summary>
/// Cache header configuration for different resource types
/// </summary>
public class CacheHeaderConfiguration
{
    public string CacheControl { get; set; } = string.Empty;
    public string ETag { get; set; } = string.Empty;
    public DateTime? LastModified { get; set; }
    public TimeSpan MaxAge { get; set; }
    public bool IsImmutable { get; set; }
    public List<string> VaryHeaders { get; set; } = new();
}

/// <summary>
/// Core Web Vitals measurement result
/// </summary>
public class CoreWebVitalsResult
{
    public string PageName { get; set; } = string.Empty;
    public string Url { get; set; } = string.Empty;
    public WebVitalMetric LargestContentfulPaint { get; set; } = new();
    public WebVitalMetric FirstInputDelay { get; set; } = new();
    public WebVitalMetric CumulativeLayoutShift { get; set; } = new();
    public WebVitalMetric FirstContentfulPaint { get; set; } = new();
    public WebVitalMetric TimeToInteractive { get; set; } = new();
    public int PerformanceScore { get; set; }
    public DateTime MeasuredAt { get; set; }
    public List<PerformanceRecommendation> Recommendations { get; set; } = new();
}

/// <summary>
/// Individual Web Vital metric
/// </summary>
public class WebVitalMetric
{
    public string Name { get; set; } = string.Empty;
    public double Value { get; set; }
    public string Unit { get; set; } = string.Empty;
    public WebVitalRating Rating { get; set; }
    public double Threshold { get; set; }
}

/// <summary>
/// Web Vital performance rating
/// </summary>
public enum WebVitalRating
{
    Good,
    NeedsImprovement,
    Poor
}

/// <summary>
/// Performance optimization recommendation
/// </summary>
public class PerformanceRecommendation
{
    public string Title { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public string Category { get; set; } = string.Empty;
    public int Impact { get; set; }
    public string Implementation { get; set; } = string.Empty;
}