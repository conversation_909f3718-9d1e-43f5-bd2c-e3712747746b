# Test script to verify the inventory generation fix
Write-Host "🔧 Testing Page Inventory Generation Fix" -ForegroundColor Green
Write-Host "=========================================" -ForegroundColor Green

# First, let's check if the application is running
Write-Host "`n1. Checking if application is running..." -ForegroundColor Cyan
try {
    $response = Invoke-WebRequest -Uri "http://localhost:5285" -UseBasicParsing -TimeoutSec 5
    Write-Host "   ✅ Application is running on port 5285" -ForegroundColor Green
} catch {
    Write-Host "   ❌ Application not running. Starting it..." -ForegroundColor Yellow
    Write-Host "   Please run: dotnet run --project ParaHockeyApp.csproj" -ForegroundColor White
    exit 1
}

# Test accessing the PageAudit page
Write-Host "`n2. Testing PageAudit page access..." -ForegroundColor Cyan
try {
    $response = Invoke-WebRequest -Uri "http://localhost:5285/Admin/PageAudit" -UseBasicParsing -TimeoutSec 10
    if ($response.StatusCode -eq 200) {
        Write-Host "   ✅ PageAudit page accessible" -ForegroundColor Green
    } else {
        Write-Host "   ⚠️  PageAudit page returned status: $($response.StatusCode)" -ForegroundColor Yellow
    }
} catch {
    Write-Host "   ❌ Error accessing PageAudit page: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host "`n🧪 Manual Testing Instructions:" -ForegroundColor Yellow
Write-Host "================================" -ForegroundColor Yellow
Write-Host "1. Open browser and go to: http://localhost:5285/Admin/PageAudit" -ForegroundColor White
Write-Host "2. Click the 'Generate Inventory' button" -ForegroundColor White
Write-Host "3. Verify that the inventory generates without duplicate key errors" -ForegroundColor White
Write-Host "4. Check that pages are listed in the table below" -ForegroundColor White

Write-Host "`n✅ Test script complete. Please perform manual testing." -ForegroundColor Green