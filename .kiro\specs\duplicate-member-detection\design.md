# Design Document

## Overview

The duplicate member detection feature will be implemented as a server-side validation mechanism that integrates seamlessly with the existing member registration flow. The system will perform duplicate checks after client-side validation passes but before the member is saved to the database. This approach ensures data integrity while providing a smooth user experience with localized feedback and appropriate resolution options.

## Architecture

### High-Level Flow

```mermaid
sequenceDiagram
    participant User
    participant Browser
    participant Controller
    participant DuplicateService
    participant Database
    participant EmailService

    User->>Browser: Submit registration form
    Browser->>Controller: POST /Members/Register
    Controller->>Controller: Validate model state
    alt Model validation fails
        Controller->>Browser: Return form with validation errors
    else Model validation passes
        Controller->>DuplicateService: CheckForDuplicates(email, lastName, dateOfBirth)
        DuplicateService->>Database: Query Members table
        Database->>DuplicateService: Return potential duplicates
        alt Exact email match found
            DuplicateService->>Controller: Return ExactEmailMatch result
            Controller->>Browser: Return JSON with duplicate email message
        else Partial match found (lastName + DOB)
            DuplicateService->>Controller: Return PartialMatch result with masked email
            Controller->>Browser: Return JSON with partial match message
        else No duplicates found
            Controller->>Controller: Proceed with normal registration
            Controller->>Database: Save new member
            Controller->>EmailService: Send welcome email (if applicable)
            Controller->>Browser: Redirect to success page
        end
    end
```

### Integration Points

The duplicate detection will integrate with:

-   **MembersController**: Add duplicate checking before member creation
-   **Existing validation flow**: Work alongside current ModelState validation
-   **Localization system**: Use SharedLocalizer for all user-facing messages
-   **Admin functionality**: Apply same logic for admin-created members
-   **Client-side JavaScript**: Handle AJAX responses and user interactions

## Components and Interfaces

### 1. Duplicate Detection Service

```csharp
public interface IDuplicateMemberService
{
    Task<DuplicateCheckResult> CheckForDuplicatesAsync(string email, string lastName, DateTime dateOfBirth);
    string MaskEmail(string email);
}

public class DuplicateMemberService : IDuplicateMemberService
{
    private readonly ApplicationContext _context;

    public async Task<DuplicateCheckResult> CheckForDuplicatesAsync(string email, string lastName, DateTime dateOfBirth)
    {
        // Check for exact email match first
        var exactEmailMatch = await _context.Members
            .FirstOrDefaultAsync(m => m.Email.ToLower() == email.ToLower());

        if (exactEmailMatch != null)
        {
            return new DuplicateCheckResult
            {
                Type = DuplicateType.ExactEmailMatch,
                ExistingMember = exactEmailMatch
            };
        }

        // Check for partial match (lastName + dateOfBirth)
        var partialMatch = await _context.Members
            .FirstOrDefaultAsync(m => m.LastName.ToLower() == lastName.ToLower()
                                   && m.DateOfBirth.Date == dateOfBirth.Date);

        if (partialMatch != null)
        {
            return new DuplicateCheckResult
            {
                Type = DuplicateType.PartialMatch,
                ExistingMember = partialMatch,
                MaskedEmail = MaskEmail(partialMatch.Email)
            };
        }

        return new DuplicateCheckResult { Type = DuplicateType.NoDuplicate };
    }

    public string MaskEmail(string email)
    {
        // Implementation follows security best practices
        // Show first 2 chars + *** + domain
        // Example: jo***@example.com
    }
}
```

### 2. Data Transfer Objects

```csharp
public class DuplicateCheckResult
{
    public DuplicateType Type { get; set; }
    public Member? ExistingMember { get; set; }
    public string? MaskedEmail { get; set; }
}

public enum DuplicateType
{
    NoDuplicate,
    ExactEmailMatch,
    PartialMatch
}

public class DuplicateResponseDto
{
    public bool IsDuplicate { get; set; }
    public string DuplicateType { get; set; } = string.Empty;
    public string Message { get; set; } = string.Empty;
    public string? MaskedEmail { get; set; }
    public string? RedirectUrl { get; set; }
}
```

### 3. Controller Integration

The MembersController will be modified to:

-   Inject IDuplicateMemberService
-   Add duplicate checking logic in Register and UpdateMember actions
-   Handle AJAX responses for duplicate detection
-   Provide different redirect URLs for regular users vs admins

### 4. Client-Side JavaScript Integration

```javascript
// Add to existing registration form JavaScript
function handleDuplicateResponse(response) {
    if (response.isDuplicate) {
        showDuplicateModal(response);
    } else {
        // Continue with normal form submission
        submitRegistrationForm();
    }
}

function showDuplicateModal(duplicateInfo) {
    // Create and display modal with localized messages
    // Handle OK/Modify and Yes/No button clicks
    // Manage form clearing and redirects
}
```

## Data Models

### Database Schema Impact

No database schema changes are required. The feature uses existing Member table columns:

-   `Email` (for exact matching)
-   `LastName` (for partial matching)
-   `DateOfBirth` (for partial matching)

### Existing Indexes

The feature will leverage existing database indexes:

-   Email field (likely indexed for login functionality)
-   Composite index on LastName + DateOfBirth may be beneficial for performance

## Error Handling

### Validation Integration

The duplicate detection integrates with the existing validation pipeline:

1. **Client-side validation** runs first (existing JavaScript validation)
2. **Server-side ModelState validation** runs second (existing ASP.NET validation)
3. **Duplicate detection** runs third (new functionality)
4. **Database transaction** runs last (existing save logic)

### Error Scenarios

-   **Database connection issues**: Log error, allow registration to proceed with warning
-   **Service unavailable**: Graceful degradation, log incident
-   **Invalid input data**: Handled by existing validation layers
-   **Concurrent registration**: Database constraints will prevent actual duplicates

### Logging Strategy

```csharp
// Log duplicate detection events for monitoring
_logger.LogInformation("Duplicate check performed for email: {Email}, Result: {Result}",
    email.Substring(0, 2) + "***", result.Type);

// Log when duplicates are found
_logger.LogWarning("Duplicate member detected: Type={Type}, ExistingMemberId={MemberId}",
    result.Type, result.ExistingMember?.Id);
```

## Testing Strategy

### Unit Tests

1. **DuplicateMemberService Tests**

    - Test exact email matching (case insensitive)
    - Test partial matching with lastName + dateOfBirth
    - Test email masking functionality
    - Test no duplicate scenarios

2. **Controller Integration Tests**
    - Test duplicate detection in registration flow
    - Test admin vs regular user redirect logic
    - Test AJAX response formatting

### Integration Tests

1. **End-to-End Registration Flow**

    - Test complete registration with no duplicates
    - Test registration attempt with exact email match
    - Test registration attempt with partial match
    - Test user interactions with duplicate dialogs

2. **Localization Tests**
    - Verify all messages display in French and English
    - Test message formatting with dynamic content

### Performance Tests

1. **Database Query Performance**

    - Measure duplicate check query execution time
    - Test with large member datasets
    - Validate index usage

2. **Concurrent Registration Tests**
    - Test multiple simultaneous registrations
    - Verify race condition handling

## Security Considerations

### Email Masking

The email masking follows security best practices:

-   Show minimal information (first 2 characters + domain)
-   Never expose full email addresses to unauthorized users
-   Consistent masking algorithm across the application

### Data Privacy

-   Duplicate detection only queries necessary fields
-   No sensitive information is logged in plain text
-   Audit trail maintained for duplicate detection events

### Input Validation

-   All inputs are validated before duplicate checking
-   SQL injection prevention through parameterized queries
-   XSS prevention in client-side message display

## Localization Implementation

### Resource Keys

New localization keys to be added to SharedResourceMarker.resx:

```xml
<!-- Duplicate Detection Messages -->
<data name="DuplicateEmailMessage" xml:space="preserve">
    <value>Cette adresse courriel est déjà utilisée par un membre existant.</value>
</data>
<data name="DuplicatePartialMessage" xml:space="preserve">
    <value>Un membre avec ce nom et cette date de naissance existe déjà avec l'adresse courriel {0}. Est-ce votre adresse courriel correcte?</value>
</data>
<data name="DuplicateOkButton" xml:space="preserve">
    <value>OK</value>
</data>
<data name="DuplicateModifyButton" xml:space="preserve">
    <value>Modifier le membre</value>
</data>
<data name="DuplicateYesButton" xml:space="preserve">
    <value>Oui</value>
</data>
<data name="DuplicateNoButton" xml:space="preserve">
    <value>Non</value>
</data>
```

### English Translations

Corresponding English translations in SharedResourceMarker.en-CA.resx:

```xml
<data name="DuplicateEmailMessage" xml:space="preserve">
    <value>This email address is already in use by an existing member.</value>
</data>
<data name="DuplicatePartialMessage" xml:space="preserve">
    <value>A member with this name and birth date already exists with email address {0}. Is this your correct email?</value>
</data>
<!-- Button labels remain the same or have English equivalents -->
```

## Performance Considerations

### Database Optimization

-   Leverage existing indexes on email field
-   Consider composite index on (LastName, DateOfBirth) if performance testing indicates need
-   Use efficient query patterns with FirstOrDefaultAsync

### Caching Strategy

-   No caching implemented initially (duplicate checks should be real-time)
-   Consider caching for frequently accessed lookup data if needed
-   Monitor performance and implement caching if duplicate checks become bottleneck

### Scalability

-   Duplicate detection adds minimal overhead to registration process
-   Database queries are simple and efficient
-   Service can be easily moved to separate microservice if needed

## Deployment Considerations

### Feature Flags

Consider implementing feature flag for duplicate detection:

-   Allow gradual rollout
-   Easy rollback if issues discovered
-   A/B testing capabilities

### Monitoring

-   Track duplicate detection rates
-   Monitor performance impact on registration flow
-   Alert on unusual duplicate patterns

### Rollback Plan

-   Feature can be disabled via configuration
-   No database migrations required
-   Existing registration flow remains unchanged if feature disabled
