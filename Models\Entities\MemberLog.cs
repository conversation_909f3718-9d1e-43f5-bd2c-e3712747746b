using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace ParaHockeyApp.Models.Entities
{
    /// <summary>
    /// Audit log table that tracks all changes made to member records.
    /// This provides a complete history of who changed what and when.
    /// </summary>
    public class MemberLog
    {
        /// <summary>
        /// Primary key showing the number of the log entry
        /// </summary>
        [Key]
        public int NoLog { get; set; }

        /// <summary>
        /// Foreign key to the Member that was created or modified
        /// </summary>
        [Required]
        public int MemberId { get; set; }

        /// <summary>
        /// Navigation property to the associated Member
        /// </summary>
        public virtual Member Member { get; set; } = null!;

        /// <summary>
        /// Date and time when the change occurred (UTC)
        /// </summary>
        [Required]
        public DateTime LogDate { get; set; } = DateTime.UtcNow;

        /// <summary>
        /// Detailed description of what was changed.
        /// For new registration: "New member registration"
        /// For modifications: "FirstName: 'John' → 'Jonathan', Email: '<EMAIL>' → '<EMAIL>'"
        /// </summary>
        [Required]
        [StringLength(1000)]
        public string Description { get; set; } = string.Empty;

        /// <summary>
        /// Member or Admin ID of the person who edited the member.
        /// For new registration: same as MemberId
        /// For updates: the ID of the person making the change
        /// </summary>
        [Required]
        public int EditorId { get; set; }

        /// <summary>
        /// Full name (First + Last) of the editor
        /// Populated by querying Members table using EditorId
        /// </summary>
        [Required]
        [StringLength(101)] // 50 + 1 + 50 for "FirstName LastName"
        public string EditorName { get; set; } = string.Empty;
    }
}