# Design – Add Camp & Série Event Categories

## 1. Overview
This change extends both the **data model** and **business logic** so that:
1. New event categories *Camp* and *Série* exist and are fully localised.
2. Title-driven auto-classification moves *Tentatif* events to their own category and reroutes *Défi Sportif* events to *Tournoi* (Tournament).
3. All calendar endpoints and views surface the new categories without extra code changes – they already join `Event -> EventCategory`.

## 2. Data Model Changes
| Table | Change | Notes |
|-------|--------|-------|
| EventCategories | Seed rows for Id=9 (Camp) and Id=10 (Série) | Uses colour `#20c997` (teal) and `#6610f2` (indigo) with `fas fa-campground`, `fas fa-stream` icons. |
| EventCategories | Optionally seed Id=11 (Tentatif) if not present | Uses neutral grey `#adb5bd` + `fas fa-question-circle` icon. |

A new **EF Core migration** `AddCampAndSerieEventCategories` seeds these rows using the same pattern as *SeedEventCategories*.

## 3. Localization
Add keys in `Resources/SharedResource.resx` & `SharedResource.en-CA.resx`:
* `EventCategory_Camp`, `EventCategory_Camp_Desc`
* `EventCategory_Serie`, `EventCategory_Serie_Desc`
* (Optional) `EventCategory_Tentatif`, `EventCategory_Tentatif_Desc`

French values: *Camp*, *Série*, *Tentatif*  
English values: *Camp*, *Series*, *Tentative*

## 4. Business Logic Changes
### 4.1 Auto-classification helper
Add method `DetermineCategoryId(string title)` to **EventService**:
```csharp
private int DetermineCategoryId(string title)
{
    title = title.ToLowerInvariant();
    if (title.Contains("tentatif")) return _context.EventCategories.First(c=>c.DisplayNameKey=="EventCategory_Tentatif").Id;
    if (title.Contains("défi sportif")) return _context.EventCategories.First(c=>c.DisplayNameKey=="EventCategory_Tournament").Id; // Tournoi
    return _context.EventCategories.First(c=>c.DisplayNameKey=="EventCategory_Other").Id;
}
```
Invoke this helper from both `CreateEventAsync` and any **import** routine when `EventCategoryId` is not explicitly set.

### 4.2 Existing Data Migration
A lightweight `UPDATE` SQL block inside the migration will:
```sql
UPDATE E SET EventCategoryId = (SELECT Id FROM EventCategories WHERE DisplayNameKey='EventCategory_Tentatif')
WHERE Title LIKE '%Tentatif%' AND EventCategoryId = (SELECT Id FROM EventCategories WHERE DisplayNameKey='EventCategory_Other');

UPDATE E SET EventCategoryId = (SELECT Id FROM EventCategories WHERE DisplayNameKey='EventCategory_Tournament')
WHERE Title LIKE '%Défi Sportif%' AND EventCategoryId = (SELECT Id FROM EventCategories WHERE DisplayNameKey='EventCategory_Other');
```

## 5. UI Integration
No Razor changes are required; the dropdown `asp-items="@Model.EventCategorySelectList"` is populated from `GetAllEventCategoriesAsync()`. After seeding, Camp and Série automatically appear.

Bootstrap colour chips are already generated via `EventCategory.Color`.

## 6. Alternatives Considered
| Option | Pros | Cons |
|--------|------|------|
| **A. Simple seed + string-contains logic (chosen)** | Fast to implement, no new tables, keeps logic in service layer | String matching can be brittle; new keywords require code change |
| **B. Configurable mapping table (e.g., `EventImportMappings`)** | Non-developers can edit mappings; no code change for new patterns | Requires new table, CRUD UI, more complex migration |

## 7. Impacted Components
* `Migrations/*` – new migration file
* `Resources/*` – resx updates
* `Services/EventService.cs` – helper + call-site
* E2E tests – `LocalizationTests`, `InputMaskingTests` unaffected; new `EventCategoryTests` added.

## 8. Diagram
```mermaid
graph TD
    A[Event Creation / Import] --> B{Determine Category}
    B --> |Tentatif title| C[Tentatif]
    B --> |Défi Sportif title| D[Tournoi]
    B --> |User-selected| E[Selected Category]
    C --> F[Event Saved]
    D --> F
    E --> F
``` 