using Microsoft.Extensions.Localization;
using System.ComponentModel.DataAnnotations;

namespace ParaHockeyApp.Attributes
{
    /// <summary>
    /// Required attribute with localized error messages
    /// </summary>
    public class LocalizedRequiredAttribute : RequiredAttribute
    {
        public LocalizedRequiredAttribute()
        {
            ErrorMessage = "ValidationRequired";
        }

        public override string FormatErrorMessage(string name)
        {
            // This will be handled by the FormValidationService for proper localization
            return ErrorMessage ?? $"The {name} field is required.";
        }
    }

    /// <summary>
    /// String length attribute with localized error messages
    /// </summary>
    public class LocalizedStringLengthAttribute : StringLengthAttribute
    {
        public LocalizedStringLengthAttribute(int maximumLength) : base(maximumLength)
        {
            ErrorMessage = "ValidationStringLength";
        }

        public LocalizedStringLengthAttribute(int maximumLength, int minimumLength) : base(maximumLength)
        {
            MinimumLength = minimumLength;
            ErrorMessage = "ValidationStringLength";
        }

        public override string FormatErrorMessage(string name)
        {
            // This will be handled by the FormValidationService for proper localization
            return ErrorMessage ?? $"The {name} field must be a string with a maximum length of {MaximumLength}.";
        }
    }

    /// <summary>
    /// Email address attribute with localized error messages
    /// </summary>
    public class LocalizedEmailAddressAttribute : ValidationAttribute
    {
        public LocalizedEmailAddressAttribute()
        {
            ErrorMessage = "ValidationEmailSpecific";
        }

        public override bool IsValid(object? value)
        {
            if (value == null) return true; // Let Required handle null values
            
            if (value is string email)
            {
                var emailAttribute = new EmailAddressAttribute();
                return emailAttribute.IsValid(email);
            }

            return false;
        }

        public override string FormatErrorMessage(string name)
        {
            return ErrorMessage ?? "Please enter a valid email address.";
        }
    }

    /// <summary>
    /// Phone attribute with localized error messages
    /// </summary>
    public class LocalizedPhoneAttribute : ValidationAttribute
    {
        public LocalizedPhoneAttribute()
        {
            ErrorMessage = "ValidationPhoneSpecific";
        }

        public override bool IsValid(object? value)
        {
            if (value == null) return true; // Let Required handle null values
            
            if (value is string phone)
            {
                var phoneAttribute = new PhoneAttribute();
                return phoneAttribute.IsValid(phone);
            }

            return false;
        }

        public override string FormatErrorMessage(string name)
        {
            return ErrorMessage ?? "Please enter a valid phone number.";
        }
    }

    /// <summary>
    /// Canadian postal code validation attribute
    /// </summary>
    public class CanadianPostalCodeAttribute : RegularExpressionAttribute
    {
        private const string PostalCodePattern = @"^[ABCEGHJ-NPRSTVXY]\d[ABCEGHJ-NPRSTV-Z][ -]?\d[ABCEGHJ-NPRSTV-Z]\d$";

        public CanadianPostalCodeAttribute() : base(PostalCodePattern)
        {
            ErrorMessage = "ValidationPostalCodeSpecific";
        }

        public override string FormatErrorMessage(string name)
        {
            return ErrorMessage ?? "Please enter a valid Canadian postal code (e.g., A1A 1A1).";
        }
    }

    /// <summary>
    /// Date validation attribute with localized error messages
    /// </summary>
    public class LocalizedDateAttribute : DataTypeAttribute
    {
        public LocalizedDateAttribute() : base(DataType.Date)
        {
            ErrorMessage = "ValidationDateSpecific";
        }

        public override bool IsValid(object? value)
        {
            if (value == null) return true; // Let Required handle null values
            
            if (value is DateTime dateTime)
            {
                return dateTime != default(DateTime);
            }

            if (value is string stringValue)
            {
                return DateTime.TryParse(stringValue, out _);
            }

            return false;
        }

        public override string FormatErrorMessage(string name)
        {
            return ErrorMessage ?? "Please enter a valid date.";
        }
    }

    /// <summary>
    /// Birth date validation attribute (cannot be in future, reasonable age limits)
    /// </summary>
    public class BirthDateAttribute : ValidationAttribute
    {
        public BirthDateAttribute()
        {
            ErrorMessage = "ValidationDateFuture";
        }

        public override bool IsValid(object? value)
        {
            if (value == null) return true; // Let Required handle null values

            if (value is DateTime birthDate)
            {
                var today = DateTime.Today;
                var minDate = today.AddYears(-120); // Reasonable maximum age
                
                return birthDate >= minDate && birthDate <= today;
            }

            if (value is string stringValue && DateTime.TryParse(stringValue, out var parsedDate))
            {
                var today = DateTime.Today;
                var minDate = today.AddYears(-120);
                
                return parsedDate >= minDate && parsedDate <= today;
            }

            return false;
        }

        public override string FormatErrorMessage(string name)
        {
            return ErrorMessage ?? "Date of birth cannot be in the future.";
        }
    }

    /// <summary>
    /// Name validation attribute (minimum 2 characters, maximum 50)
    /// </summary>
    public class PersonNameAttribute : StringLengthAttribute
    {
        public PersonNameAttribute() : base(50)
        {
            MinimumLength = 2;
            ErrorMessage = "ValidationNameTooShort";
        }

        public override string FormatErrorMessage(string name)
        {
            if (MinimumLength > 0)
            {
                return ErrorMessage ?? $"Name must be at least {MinimumLength} characters long.";
            }
            return $"Name cannot exceed {MaximumLength} characters.";
        }
    }

    /// <summary>
    /// Address validation attribute
    /// </summary>
    public class AddressAttribute : StringLengthAttribute
    {
        public AddressAttribute() : base(200)
        {
            MinimumLength = 5;
            ErrorMessage = "ValidationAddressTooShort";
        }

        public override string FormatErrorMessage(string name)
        {
            return ErrorMessage ?? $"Address must be at least {MinimumLength} characters long.";
        }
    }

    /// <summary>
    /// City name validation attribute
    /// </summary>
    public class CityNameAttribute : StringLengthAttribute
    {
        public CityNameAttribute() : base(100)
        {
            MinimumLength = 2;
            ErrorMessage = "ValidationCityTooShort";
        }

        public override string FormatErrorMessage(string name)
        {
            return ErrorMessage ?? $"City must be at least {MinimumLength} characters long.";
        }
    }

    /// <summary>
    /// Comprehensive validation attribute that combines multiple validation rules
    /// </summary>
    public class ComprehensiveValidationAttribute : ValidationAttribute
    {
        public bool IsRequired { get; set; }
        public int MinLength { get; set; }
        public int MaxLength { get; set; }
        public string? Pattern { get; set; }
        public string? InputType { get; set; }
        public string? InputMode { get; set; }
        public string? Autocomplete { get; set; }

        public ComprehensiveValidationAttribute()
        {
        }

        public override bool IsValid(object? value)
        {
            if (value == null || string.IsNullOrWhiteSpace(value.ToString()))
            {
                return !IsRequired;
            }

            var stringValue = value.ToString()!;

            // Check length constraints
            if (MinLength > 0 && stringValue.Length < MinLength)
                return false;

            if (MaxLength > 0 && stringValue.Length > MaxLength)
                return false;

            // Check pattern if provided
            if (!string.IsNullOrEmpty(Pattern))
            {
                var regex = new System.Text.RegularExpressions.Regex(Pattern);
                if (!regex.IsMatch(stringValue))
                    return false;
            }

            return true;
        }

        public override string FormatErrorMessage(string name)
        {
            if (IsRequired && string.IsNullOrEmpty(ErrorMessage))
                return $"The {name} field is required.";

            return ErrorMessage ?? $"The {name} field is invalid.";
        }
    }

    /// <summary>
    /// Enhanced Canadian postal code validation with client-side support
    /// </summary>
    public class EnhancedCanadianPostalCodeAttribute : ComprehensiveValidationAttribute
    {
        private const string PostalCodePattern = @"^[ABCEGHJ-NPRSTVXY]\d[ABCEGHJ-NPRSTV-Z][ -]?\d[ABCEGHJ-NPRSTV-Z]\d$";

        public EnhancedCanadianPostalCodeAttribute()
        {
            Pattern = PostalCodePattern;
            MaxLength = 7; // A1A 1A1 with space
            MinLength = 6; // A1A1A1 without space
            InputType = "text";
            InputMode = "text";
            Autocomplete = "postal-code";
            ErrorMessage = "ValidationPostalCodeSpecific";
        }

        public override bool IsValid(object? value)
        {
            if (value == null) return !IsRequired;

            if (value is string postalCode)
            {
                // Allow both formats: A1A1A1 and A1A 1A1
                var cleaned = postalCode.Replace(" ", "").Replace("-", "").ToUpperInvariant();
                
                if (cleaned.Length != 6) return false;

                var regex = new System.Text.RegularExpressions.Regex(@"^[ABCEGHJ-NPRSTVXY]\d[ABCEGHJ-NPRSTV-Z]\d[ABCEGHJ-NPRSTV-Z]\d$");
                return regex.IsMatch(cleaned);
            }

            return false;
        }
    }
}