/**
 * Critical CSS Manager - Handles critical CSS inlining and non-critical CSS loading
 */
class CriticalCssManager {
    constructor() {
        this.criticalCssLoaded = false;
        this.nonCriticalCssLoaded = false;
        this.loadedStylesheets = new Set();
        this.pendingStylesheets = new Map();
        
        this.initializeCriticalCss();
    }

    /**
     * Initialize critical CSS management
     */
    initializeCriticalCss() {
        // Mark critical CSS as loaded if it's already inlined
        const inlineCriticalCss = document.querySelector('style[data-critical="true"]');
        if (inlineCriticalCss) {
            this.criticalCssLoaded = true;
            console.log('Critical CSS already inlined');
        }

        // Load non-critical CSS after page load
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => {
                this.loadNonCriticalCss();
            });
        } else {
            this.loadNonCriticalCss();
        }
    }

    /**
     * Inline critical CSS for a specific page
     */
    async inlineCriticalCss(pageName) {
        if (this.criticalCssLoaded) {
            return;
        }

        try {
            // In a real implementation, this would fetch from an API
            const criticalCss = await this.fetchCriticalCss(pageName);
            
            if (criticalCss) {
                this.injectCriticalCss(criticalCss);
                this.criticalCssLoaded = true;
                console.log(`Critical CSS inlined for page: ${pageName}`);
            }
        } catch (error) {
            console.error('Failed to inline critical CSS:', error);
        }
    }

    /**
     * Inject critical CSS into the document head
     */
    injectCriticalCss(css) {
        const style = document.createElement('style');
        style.setAttribute('data-critical', 'true');
        style.textContent = css;
        
        // Insert before any existing stylesheets
        const firstLink = document.querySelector('link[rel="stylesheet"]');
        if (firstLink) {
            document.head.insertBefore(style, firstLink);
        } else {
            document.head.appendChild(style);
        }
    }

    /**
     * Load non-critical CSS asynchronously
     */
    loadNonCriticalCss() {
        if (this.nonCriticalCssLoaded) {
            return;
        }

        // Find preloaded stylesheets and convert them to regular stylesheets
        const preloadedStyles = document.querySelectorAll('link[rel="preload"][as="style"]');
        preloadedStyles.forEach(link => {
            this.convertPreloadToStylesheet(link);
        });

        // Load deferred stylesheets
        const deferredStyles = document.querySelectorAll('link[data-defer="true"]');
        deferredStyles.forEach(link => {
            this.loadDeferredStylesheet(link);
        });

        this.nonCriticalCssLoaded = true;
        console.log('Non-critical CSS loading initiated');
    }

    /**
     * Convert preload link to stylesheet
     */
    convertPreloadToStylesheet(preloadLink) {
        if (preloadLink.rel === 'stylesheet') {
            return; // Already converted
        }

        preloadLink.onload = function() {
            this.onload = null;
            this.rel = 'stylesheet';
        };

        // Fallback for browsers that don't support onload on link elements
        setTimeout(() => {
            if (preloadLink.rel !== 'stylesheet') {
                preloadLink.rel = 'stylesheet';
            }
        }, 100);
    }

    /**
     * Load a deferred stylesheet
     */
    loadDeferredStylesheet(deferredLink) {
        const href = deferredLink.getAttribute('data-href') || deferredLink.href;
        
        if (this.loadedStylesheets.has(href)) {
            return;
        }

        const link = document.createElement('link');
        link.rel = 'stylesheet';
        link.href = href;
        link.media = deferredLink.media || 'all';
        
        link.onload = () => {
            this.loadedStylesheets.add(href);
            console.log(`Deferred stylesheet loaded: ${href}`);
        };

        link.onerror = () => {
            console.error(`Failed to load deferred stylesheet: ${href}`);
        };

        document.head.appendChild(link);
    }

    /**
     * Load stylesheet with priority and callback
     */
    loadStylesheet(href, options = {}) {
        const {
            priority = 'normal',
            media = 'all',
            onLoad = null,
            onError = null
        } = options;

        if (this.loadedStylesheets.has(href)) {
            if (onLoad) onLoad();
            return Promise.resolve();
        }

        if (this.pendingStylesheets.has(href)) {
            return this.pendingStylesheets.get(href);
        }

        const loadPromise = new Promise((resolve, reject) => {
            const link = document.createElement('link');
            link.rel = priority === 'high' ? 'preload' : 'stylesheet';
            link.href = href;
            link.media = media;
            
            if (priority === 'high') {
                link.as = 'style';
                link.onload = function() {
                    this.onload = null;
                    this.rel = 'stylesheet';
                    resolve();
                    if (onLoad) onLoad();
                };
            } else {
                link.onload = () => {
                    resolve();
                    if (onLoad) onLoad();
                };
            }

            link.onerror = () => {
                reject(new Error(`Failed to load stylesheet: ${href}`));
                if (onError) onError();
            };

            document.head.appendChild(link);
        });

        this.pendingStylesheets.set(href, loadPromise);
        
        loadPromise.finally(() => {
            this.loadedStylesheets.add(href);
            this.pendingStylesheets.delete(href);
        });

        return loadPromise;
    }

    /**
     * Fetch critical CSS for a specific page (simulation)
     */
    async fetchCriticalCss(pageName) {
        // Simulate API call
        await new Promise(resolve => setTimeout(resolve, 50));

        // Return page-specific critical CSS
        const criticalCssMap = {
            'home': `
                body { margin: 0; font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif; }
                .container { max-width: 1200px; margin: 0 auto; padding: 0 15px; }
                .navbar { background: #fff; border-bottom: 1px solid #dee2e6; padding: 0.5rem 0; }
                .btn { display: inline-block; padding: 0.375rem 0.75rem; border: 1px solid transparent; border-radius: 0.25rem; }
                .btn-primary { background-color: #007bff; border-color: #007bff; color: #fff; }
                h1, h2, h3 { margin-top: 0; margin-bottom: 0.5rem; font-weight: 500; line-height: 1.2; }
                .hero { padding: 4rem 0; text-align: center; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; }
            `,
            'register': `
                body { margin: 0; font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif; }
                .container { max-width: 1200px; margin: 0 auto; padding: 0 15px; }
                .form-group { margin-bottom: 1rem; }
                .form-label { display: block; margin-bottom: 0.5rem; font-weight: 500; }
                .form-control { display: block; width: 100%; padding: 0.375rem 0.75rem; border: 1px solid #ced4da; border-radius: 0.25rem; }
                .btn { display: inline-block; padding: 0.375rem 0.75rem; border: 1px solid transparent; border-radius: 0.25rem; }
                .btn-primary { background-color: #007bff; border-color: #007bff; color: #fff; }
                .alert { padding: 0.75rem 1.25rem; margin-bottom: 1rem; border: 1px solid transparent; border-radius: 0.25rem; }
                .alert-danger { color: #721c24; background-color: #f8d7da; border-color: #f5c6cb; }
            `,
            'login': `
                body { margin: 0; font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif; }
                .container { max-width: 1200px; margin: 0 auto; padding: 0 15px; }
                .form-group { margin-bottom: 1rem; }
                .form-label { display: block; margin-bottom: 0.5rem; font-weight: 500; }
                .form-control { display: block; width: 100%; padding: 0.375rem 0.75rem; border: 1px solid #ced4da; border-radius: 0.25rem; }
                .btn { display: inline-block; padding: 0.375rem 0.75rem; border: 1px solid transparent; border-radius: 0.25rem; }
                .btn-primary { background-color: #007bff; border-color: #007bff; color: #fff; }
                .login-form { max-width: 400px; margin: 2rem auto; padding: 2rem; border: 1px solid #dee2e6; border-radius: 0.5rem; }
            `
        };

        return criticalCssMap[pageName.toLowerCase()] || criticalCssMap['home'];
    }

    /**
     * Extract critical CSS from existing stylesheets
     */
    extractCriticalCss(selectors) {
        const criticalRules = [];
        
        // Get all stylesheets
        Array.from(document.styleSheets).forEach(stylesheet => {
            try {
                Array.from(stylesheet.cssRules || []).forEach(rule => {
                    if (rule.type === CSSRule.STYLE_RULE) {
                        const selectorText = rule.selectorText;
                        
                        // Check if this rule matches any critical selectors
                        const isCritical = selectors.some(selector => 
                            selectorText.includes(selector) || 
                            this.matchesSelector(selectorText, selector)
                        );
                        
                        if (isCritical) {
                            criticalRules.push(rule.cssText);
                        }
                    }
                });
            } catch (e) {
                // Cross-origin stylesheets may throw errors
                console.warn('Cannot access stylesheet rules:', e);
            }
        });

        return criticalRules.join('\n');
    }

    /**
     * Check if a CSS selector matches a critical selector pattern
     */
    matchesSelector(selectorText, pattern) {
        // Simple pattern matching - in a real implementation, this would be more sophisticated
        const regex = new RegExp(pattern.replace(/\./g, '\\.').replace(/\*/g, '.*'), 'i');
        return regex.test(selectorText);
    }

    /**
     * Get CSS loading performance metrics
     */
    getCssMetrics() {
        const cssEntries = performance.getEntriesByType('resource')
            .filter(entry => entry.name.endsWith('.css'));

        const metrics = {
            totalCssFiles: cssEntries.length,
            totalLoadTime: cssEntries.reduce((sum, entry) => sum + entry.duration, 0),
            criticalCssInlined: this.criticalCssLoaded,
            nonCriticalCssLoaded: this.nonCriticalCssLoaded,
            loadedStylesheets: this.loadedStylesheets.size,
            files: cssEntries.map(entry => ({
                name: entry.name,
                loadTime: entry.duration,
                size: entry.transferSize || 0,
                renderBlocking: entry.renderBlockingStatus === 'blocking'
            }))
        };

        return metrics;
    }

    /**
     * Optimize CSS loading for a specific page
     */
    optimizeForPage(pageName) {
        // Inline critical CSS
        this.inlineCriticalCss(pageName);

        // Preload important stylesheets
        const importantStyles = this.getImportantStylesForPage(pageName);
        importantStyles.forEach(href => {
            this.preloadStylesheet(href);
        });

        // Defer less important stylesheets
        const deferredStyles = this.getDeferredStylesForPage(pageName);
        deferredStyles.forEach(href => {
            this.deferStylesheet(href);
        });
    }

    /**
     * Preload a stylesheet
     */
    preloadStylesheet(href) {
        const existing = document.querySelector(`link[href="${href}"]`);
        if (existing) return;

        const link = document.createElement('link');
        link.rel = 'preload';
        link.as = 'style';
        link.href = href;
        link.onload = function() {
            this.onload = null;
            this.rel = 'stylesheet';
        };

        document.head.appendChild(link);
    }

    /**
     * Defer a stylesheet
     */
    deferStylesheet(href) {
        const link = document.createElement('link');
        link.rel = 'preload';
        link.as = 'style';
        link.href = href;
        link.setAttribute('data-defer', 'true');

        // Load after a delay
        setTimeout(() => {
            link.rel = 'stylesheet';
        }, 100);

        document.head.appendChild(link);
    }

    /**
     * Get important stylesheets for a page
     */
    getImportantStylesForPage(pageName) {
        const pageStyles = {
            'home': ['/css/site.css'],
            'register': ['/css/site.css', '/css/enhanced-forms.css'],
            'login': ['/css/site.css', '/css/enhanced-forms.css'],
            'admin': ['/css/site.css', '/css/parahockey-design-system.css']
        };

        return pageStyles[pageName.toLowerCase()] || ['/css/site.css'];
    }

    /**
     * Get deferred stylesheets for a page
     */
    getDeferredStylesForPage(pageName) {
        return [
            '/css/dark-overrides.css',
            '/css/environment-test.css',
            '/css/environment-prod.css'
        ];
    }
}

// Initialize critical CSS manager
const criticalCssManager = new CriticalCssManager();

// Global access
window.criticalCssManager = criticalCssManager;

// Utility functions
window.inlineCriticalCss = (pageName) => criticalCssManager.inlineCriticalCss(pageName);
window.loadStylesheet = (href, options) => criticalCssManager.loadStylesheet(href, options);

// Export for module systems
if (typeof module !== 'undefined' && module.exports) {
    module.exports = CriticalCssManager;
}