@model TempMember
@{
    ViewData["Title"] = "Temp Member Details";
}

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <!-- Header -->
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h2 class="mb-1">Temp Member Details</h2>
                    <p class="text-muted mb-0">
                        Status: <span class="badge bg-@GetStatusBadgeColor(Model.Status)">@Model.Status</span>
                    </p>
                </div>
                <div>
                    <a href="@Url.Action("Queue", new { batchId = Model.ImportBatchId, status = Model.Status })" 
                       class="btn btn-outline-primary me-2">
                        <i class="fas fa-arrow-left me-1"></i>Back to Queue
                    </a>
                    
                    @if (Model.Status == TempMemberStatus.NeedsFix)
                    {
                        <a href="@Url.Action("Edit", new { id = Model.TempMemberId })" class="btn btn-primary">
                            <i class="fas fa-edit me-1"></i>Edit
                        </a>
                    }
                    else if (Model.Status == TempMemberStatus.ReadyToCreate)
                    {
                        <form method="post" action="@Url.Action("CreateMember")" class="d-inline">
                            @Html.AntiForgeryToken()
                            <input type="hidden" name="id" value="@Model.TempMemberId" />
                            <button type="submit" class="btn btn-success" 
                                    onclick="return confirm('Create member from this temp record?')">
                                <i class="fas fa-plus-circle me-1"></i>Create Member
                            </button>
                        </form>
                    }
                    else if (Model.Status == TempMemberStatus.Duplicate)
                    {
                        <a href="@Url.Action("ResolveDuplicate", new { id = Model.TempMemberId })" class="btn btn-warning">
                            <i class="fas fa-compress-arrows-alt me-1"></i>Resolve Duplicate
                        </a>
                    }
                </div>
            </div>

            <div class="row">
                <!-- Basic Information -->
                <div class="col-lg-6">
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-user me-2"></i>Basic Information
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="row mb-3">
                                <div class="col-sm-4"><strong>First Name:</strong></div>
                                <div class="col-sm-8">@(Model.FirstName ?? "Not provided")</div>
                            </div>
                            <div class="row mb-3">
                                <div class="col-sm-4"><strong>Last Name:</strong></div>
                                <div class="col-sm-8">@(Model.LastName ?? "Not provided")</div>
                            </div>
                            <div class="row mb-3">
                                <div class="col-sm-4"><strong>Date of Birth:</strong></div>
                                <div class="col-sm-8">
                                    @if (Model.DateOfBirth.HasValue)
                                    {
                                        <span>@Model.DateOfBirth.Value.ToString("yyyy-MM-dd")</span>
                                    }
                                    else
                                    {
                                        <span class="text-muted">Not provided</span>
                                    }
                                </div>
                            </div>
                            <div class="row mb-3">
                                <div class="col-sm-4"><strong>Gender:</strong></div>
                                <div class="col-sm-8">@(Model.GenderText ?? "Not provided")</div>
                            </div>
                            <div class="row mb-3">
                                <div class="col-sm-4"><strong>Email:</strong></div>
                                <div class="col-sm-8">
                                    @if (!string.IsNullOrEmpty(Model.Email))
                                    {
                                        <a href="mailto:@Model.Email">@Model.Email</a>
                                    }
                                    else
                                    {
                                        <span class="text-muted">Not provided</span>
                                    }
                                </div>
                            </div>
                            <div class="row mb-3">
                                <div class="col-sm-4"><strong>Phone Number:</strong></div>
                                <div class="col-sm-8">
                                    @if (!string.IsNullOrEmpty(Model.Phone))
                                    {
                                        <a href="tel:@Model.Phone">@Model.Phone</a>
                                    }
                                    else
                                    {
                                        <span class="text-muted">Not provided</span>
                                    }
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Address Information -->
                <div class="col-lg-6">
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-map-marker-alt me-2"></i>Address Information
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="row mb-3">
                                <div class="col-sm-4"><strong>Street Address:</strong></div>
                                <div class="col-sm-8">@(Model.Address ?? "Not provided")</div>
                            </div>
                            <div class="row mb-3">
                                <div class="col-sm-4"><strong>City:</strong></div>
                                <div class="col-sm-8">@(Model.City ?? "Not provided")</div>
                            </div>
                            <div class="row mb-3">
                                <div class="col-sm-4"><strong>Province:</strong></div>
                                <div class="col-sm-8">@(Model.ProvinceText ?? "Not provided")</div>
                            </div>
                            <div class="row mb-3">
                                <div class="col-sm-4"><strong>Postal Code:</strong></div>
                                <div class="col-sm-8">@(Model.PostalCode ?? "Not provided")</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Registration Information -->
            <div class="row">
                <div class="col-lg-6">
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-id-card me-2"></i>Registration Information
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="row mb-3">
                                <div class="col-sm-4"><strong>Registration Type:</strong></div>
                                <div class="col-sm-8">@(Model.RegistrationTypeText ?? "Not provided")</div>
                            </div>
                            <div class="row mb-3">
                                <div class="col-sm-4"><strong>HCR Number:</strong></div>
                                <div class="col-sm-8">@(Model.HcrNumber ?? "Not provided")</div>
                            </div>
                            <div class="row mb-3">
                                <div class="col-sm-4"><strong>Health Card:</strong></div>
                                <div class="col-sm-8"><span class="text-muted">Not available</span></div>
                            </div>
                            <div class="row mb-3">
                                <div class="col-sm-4"><strong>SIN:</strong></div>
                                <div class="col-sm-8">
                                    <span class="text-muted">Not available</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Import Information -->
                <div class="col-lg-6">
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-upload me-2"></i>Import Information
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="row mb-3">
                                <div class="col-sm-4"><strong>Batch ID:</strong></div>
                                <div class="col-sm-8">
                                    <a href="@Url.Action("BatchSummary", "Import", new { id = Model.ImportBatchId })">
                                        #@Model.ImportBatchId
                                    </a>
                                </div>
                            </div>
                            <div class="row mb-3">
                                <div class="col-sm-4"><strong>Row Number:</strong></div>
                                <div class="col-sm-8">Not available</div>
                            </div>
                            <div class="row mb-3">
                                <div class="col-sm-4"><strong>Status:</strong></div>
                                <div class="col-sm-8">
                                    <span class="badge bg-@GetStatusBadgeColor(Model.Status)">@Model.Status</span>
                                </div>
                            </div>
                            <div class="row mb-3">
                                <div class="col-sm-4"><strong>Created:</strong></div>
                                <div class="col-sm-8">@Model.DateCreated.ToString("yyyy-MM-dd HH:mm:ss")</div>
                            </div>
                            <div class="row mb-3">
                                <div class="col-sm-4"><strong>Updated:</strong></div>
                                <div class="col-sm-8">@Model.DateModified?.ToString("yyyy-MM-dd HH:mm:ss") ?? "Never"</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Validation Errors -->
            @if (!string.IsNullOrEmpty(Model.ValidationErrorsJson))
            {
                <div class="row">
                    <div class="col-12">
                        <div class="card mb-4">
                            <div class="card-header bg-danger text-white">
                                <h5 class="card-title mb-0">
                                    <i class="fas fa-exclamation-triangle me-2"></i>Validation Errors
                                </h5>
                            </div>
                            <div class="card-body">
                                <div class="alert alert-danger mb-0">
                                    <p class="mb-2">The following validation errors must be fixed before this temp member can be processed:</p>
                                    <div class="validation-errors">
                                        @{
                                            try
                                            {
                                                var errors = System.Text.Json.JsonSerializer.Deserialize<List<dynamic>>(Model.ValidationErrorsJson);
                                                if (errors != null)
                                                {
                                                    <ul class="mb-0">
                                                        @foreach (var error in errors)
                                                        {
                                                            <li>@error.ToString()</li>
                                                        }
                                                    </ul>
                                                }
                                            }
                                            catch
                                            {
                                                <pre class="mb-0">@Model.ValidationErrorsJson</pre>
                                            }
                                        }
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            }

            <!-- Raw Data -->
            <div class="row">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-database me-2"></i>Raw Import Data
                            </h5>
                        </div>
                        <div class="card-body">
                            @if (!string.IsNullOrEmpty(Model.RawSourceJson))
                            {
                                <div class="accordion" id="rawDataAccordion">
                                    <div class="accordion-item">
                                        <h2 class="accordion-header" id="rawDataHeading">
                                            <button class="accordion-button collapsed" type="button" 
                                                    data-bs-toggle="collapse" data-bs-target="#rawDataCollapse" 
                                                    aria-expanded="false" aria-controls="rawDataCollapse">
                                                Show Raw Excel Data
                                            </button>
                                        </h2>
                                        <div id="rawDataCollapse" class="accordion-collapse collapse" 
                                             aria-labelledby="rawDataHeading" data-bs-parent="#rawDataAccordion">
                                            <div class="accordion-body">
                                                <pre class="bg-light p-3 rounded"><code>@Model.RawSourceJson</code></pre>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            }
                            else
                            {
                                <p class="text-muted mb-0">No raw data available.</p>
                            }
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@functions {
    private string GetStatusBadgeColor(TempMemberStatus status)
    {
        return status switch
        {
            TempMemberStatus.Imported => "info",
            TempMemberStatus.ReadyToCreate => "success",
            TempMemberStatus.NeedsFix => "warning",
            TempMemberStatus.Duplicate => "primary",
            TempMemberStatus.Created => "success",
            TempMemberStatus.Merged => "info",
            TempMemberStatus.Rejected => "danger",
            _ => "secondary"
        };
    }
}