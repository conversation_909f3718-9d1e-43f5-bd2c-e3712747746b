/* ParaHockey Design System - Utility Classes */

/* ===== SPACING UTILITIES ===== */
/* Margin utilities */
.ph-m-0 { margin: 0 !important; }
.ph-m-1 { margin: var(--ph-spacing-xs) !important; }
.ph-m-2 { margin: var(--ph-spacing-sm) !important; }
.ph-m-3 { margin: var(--ph-spacing-md) !important; }
.ph-m-4 { margin: var(--ph-spacing-base) !important; }
.ph-m-5 { margin: var(--ph-spacing-lg) !important; }
.ph-m-6 { margin: var(--ph-spacing-xl) !important; }

.ph-mt-0 { margin-top: 0 !important; }
.ph-mt-1 { margin-top: var(--ph-spacing-xs) !important; }
.ph-mt-2 { margin-top: var(--ph-spacing-sm) !important; }
.ph-mt-3 { margin-top: var(--ph-spacing-md) !important; }
.ph-mt-4 { margin-top: var(--ph-spacing-base) !important; }
.ph-mt-5 { margin-top: var(--ph-spacing-lg) !important; }
.ph-mt-6 { margin-top: var(--ph-spacing-xl) !important; }

.ph-mb-0 { margin-bottom: 0 !important; }
.ph-mb-1 { margin-bottom: var(--ph-spacing-xs) !important; }
.ph-mb-2 { margin-bottom: var(--ph-spacing-sm) !important; }
.ph-mb-3 { margin-bottom: var(--ph-spacing-md) !important; }
.ph-mb-4 { margin-bottom: var(--ph-spacing-base) !important; }
.ph-mb-5 { margin-bottom: var(--ph-spacing-lg) !important; }
.ph-mb-6 { margin-bottom: var(--ph-spacing-xl) !important; }

.ph-ml-0 { margin-left: 0 !important; }
.ph-ml-1 { margin-left: var(--ph-spacing-xs) !important; }
.ph-ml-2 { margin-left: var(--ph-spacing-sm) !important; }
.ph-ml-3 { margin-left: var(--ph-spacing-md) !important; }
.ph-ml-4 { margin-left: var(--ph-spacing-base) !important; }
.ph-ml-5 { margin-left: var(--ph-spacing-lg) !important; }
.ph-ml-6 { margin-left: var(--ph-spacing-xl) !important; }

.ph-mr-0 { margin-right: 0 !important; }
.ph-mr-1 { margin-right: var(--ph-spacing-xs) !important; }
.ph-mr-2 { margin-right: var(--ph-spacing-sm) !important; }
.ph-mr-3 { margin-right: var(--ph-spacing-md) !important; }
.ph-mr-4 { margin-right: var(--ph-spacing-base) !important; }
.ph-mr-5 { margin-right: var(--ph-spacing-lg) !important; }
.ph-mr-6 { margin-right: var(--ph-spacing-xl) !important; }

.ph-mx-0 { margin-left: 0 !important; margin-right: 0 !important; }
.ph-mx-1 { margin-left: var(--ph-spacing-xs) !important; margin-right: var(--ph-spacing-xs) !important; }
.ph-mx-2 { margin-left: var(--ph-spacing-sm) !important; margin-right: var(--ph-spacing-sm) !important; }
.ph-mx-3 { margin-left: var(--ph-spacing-md) !important; margin-right: var(--ph-spacing-md) !important; }
.ph-mx-4 { margin-left: var(--ph-spacing-base) !important; margin-right: var(--ph-spacing-base) !important; }
.ph-mx-5 { margin-left: var(--ph-spacing-lg) !important; margin-right: var(--ph-spacing-lg) !important; }
.ph-mx-6 { margin-left: var(--ph-spacing-xl) !important; margin-right: var(--ph-spacing-xl) !important; }

.ph-my-0 { margin-top: 0 !important; margin-bottom: 0 !important; }
.ph-my-1 { margin-top: var(--ph-spacing-xs) !important; margin-bottom: var(--ph-spacing-xs) !important; }
.ph-my-2 { margin-top: var(--ph-spacing-sm) !important; margin-bottom: var(--ph-spacing-sm) !important; }
.ph-my-3 { margin-top: var(--ph-spacing-md) !important; margin-bottom: var(--ph-spacing-md) !important; }
.ph-my-4 { margin-top: var(--ph-spacing-base) !important; margin-bottom: var(--ph-spacing-base) !important; }
.ph-my-5 { margin-top: var(--ph-spacing-lg) !important; margin-bottom: var(--ph-spacing-lg) !important; }
.ph-my-6 { margin-top: var(--ph-spacing-xl) !important; margin-bottom: var(--ph-spacing-xl) !important; }

/* Padding utilities */
.ph-p-0 { padding: 0 !important; }
.ph-p-1 { padding: var(--ph-spacing-xs) !important; }
.ph-p-2 { padding: var(--ph-spacing-sm) !important; }
.ph-p-3 { padding: var(--ph-spacing-md) !important; }
.ph-p-4 { padding: var(--ph-spacing-base) !important; }
.ph-p-5 { padding: var(--ph-spacing-lg) !important; }
.ph-p-6 { padding: var(--ph-spacing-xl) !important; }

.ph-pt-0 { padding-top: 0 !important; }
.ph-pt-1 { padding-top: var(--ph-spacing-xs) !important; }
.ph-pt-2 { padding-top: var(--ph-spacing-sm) !important; }
.ph-pt-3 { padding-top: var(--ph-spacing-md) !important; }
.ph-pt-4 { padding-top: var(--ph-spacing-base) !important; }
.ph-pt-5 { padding-top: var(--ph-spacing-lg) !important; }
.ph-pt-6 { padding-top: var(--ph-spacing-xl) !important; }

.ph-pb-0 { padding-bottom: 0 !important; }
.ph-pb-1 { padding-bottom: var(--ph-spacing-xs) !important; }
.ph-pb-2 { padding-bottom: var(--ph-spacing-sm) !important; }
.ph-pb-3 { padding-bottom: var(--ph-spacing-md) !important; }
.ph-pb-4 { padding-bottom: var(--ph-spacing-base) !important; }
.ph-pb-5 { padding-bottom: var(--ph-spacing-lg) !important; }
.ph-pb-6 { padding-bottom: var(--ph-spacing-xl) !important; }

.ph-pl-0 { padding-left: 0 !important; }
.ph-pl-1 { padding-left: var(--ph-spacing-xs) !important; }
.ph-pl-2 { padding-left: var(--ph-spacing-sm) !important; }
.ph-pl-3 { padding-left: var(--ph-spacing-md) !important; }
.ph-pl-4 { padding-left: var(--ph-spacing-base) !important; }
.ph-pl-5 { padding-left: var(--ph-spacing-lg) !important; }
.ph-pl-6 { padding-left: var(--ph-spacing-xl) !important; }

.ph-pr-0 { padding-right: 0 !important; }
.ph-pr-1 { padding-right: var(--ph-spacing-xs) !important; }
.ph-pr-2 { padding-right: var(--ph-spacing-sm) !important; }
.ph-pr-3 { padding-right: var(--ph-spacing-md) !important; }
.ph-pr-4 { padding-right: var(--ph-spacing-base) !important; }
.ph-pr-5 { padding-right: var(--ph-spacing-lg) !important; }
.ph-pr-6 { padding-right: var(--ph-spacing-xl) !important; }

.ph-px-0 { padding-left: 0 !important; padding-right: 0 !important; }
.ph-px-1 { padding-left: var(--ph-spacing-xs) !important; padding-right: var(--ph-spacing-xs) !important; }
.ph-px-2 { padding-left: var(--ph-spacing-sm) !important; padding-right: var(--ph-spacing-sm) !important; }
.ph-px-3 { padding-left: var(--ph-spacing-md) !important; padding-right: var(--ph-spacing-md) !important; }
.ph-px-4 { padding-left: var(--ph-spacing-base) !important; padding-right: var(--ph-spacing-base) !important; }
.ph-px-5 { padding-left: var(--ph-spacing-lg) !important; padding-right: var(--ph-spacing-lg) !important; }
.ph-px-6 { padding-left: var(--ph-spacing-xl) !important; padding-right: var(--ph-spacing-xl) !important; }

.ph-py-0 { padding-top: 0 !important; padding-bottom: 0 !important; }
.ph-py-1 { padding-top: var(--ph-spacing-xs) !important; padding-bottom: var(--ph-spacing-xs) !important; }
.ph-py-2 { padding-top: var(--ph-spacing-sm) !important; padding-bottom: var(--ph-spacing-sm) !important; }
.ph-py-3 { padding-top: var(--ph-spacing-md) !important; padding-bottom: var(--ph-spacing-md) !important; }
.ph-py-4 { padding-top: var(--ph-spacing-base) !important; padding-bottom: var(--ph-spacing-base) !important; }
.ph-py-5 { padding-top: var(--ph-spacing-lg) !important; padding-bottom: var(--ph-spacing-lg) !important; }
.ph-py-6 { padding-top: var(--ph-spacing-xl) !important; padding-bottom: var(--ph-spacing-xl) !important; }

/* ===== DISPLAY UTILITIES ===== */
.ph-d-none { display: none !important; }
.ph-d-inline { display: inline !important; }
.ph-d-inline-block { display: inline-block !important; }
.ph-d-block { display: block !important; }
.ph-d-grid { display: grid !important; }
.ph-d-table { display: table !important; }
.ph-d-table-row { display: table-row !important; }
.ph-d-table-cell { display: table-cell !important; }
.ph-d-flex { display: flex !important; }
.ph-d-inline-flex { display: inline-flex !important; }

/* ===== FLEXBOX UTILITIES ===== */
.ph-flex-row { flex-direction: row !important; }
.ph-flex-column { flex-direction: column !important; }
.ph-flex-row-reverse { flex-direction: row-reverse !important; }
.ph-flex-column-reverse { flex-direction: column-reverse !important; }

.ph-flex-wrap { flex-wrap: wrap !important; }
.ph-flex-nowrap { flex-wrap: nowrap !important; }
.ph-flex-wrap-reverse { flex-wrap: wrap-reverse !important; }

.ph-flex-fill { flex: 1 1 auto !important; }
.ph-flex-grow-0 { flex-grow: 0 !important; }
.ph-flex-grow-1 { flex-grow: 1 !important; }
.ph-flex-shrink-0 { flex-shrink: 0 !important; }
.ph-flex-shrink-1 { flex-shrink: 1 !important; }

.ph-justify-content-start { justify-content: flex-start !important; }
.ph-justify-content-end { justify-content: flex-end !important; }
.ph-justify-content-center { justify-content: center !important; }
.ph-justify-content-between { justify-content: space-between !important; }
.ph-justify-content-around { justify-content: space-around !important; }
.ph-justify-content-evenly { justify-content: space-evenly !important; }

.ph-align-items-start { align-items: flex-start !important; }
.ph-align-items-end { align-items: flex-end !important; }
.ph-align-items-center { align-items: center !important; }
.ph-align-items-baseline { align-items: baseline !important; }
.ph-align-items-stretch { align-items: stretch !important; }

.ph-align-content-start { align-content: flex-start !important; }
.ph-align-content-end { align-content: flex-end !important; }
.ph-align-content-center { align-content: center !important; }
.ph-align-content-between { align-content: space-between !important; }
.ph-align-content-around { align-content: space-around !important; }
.ph-align-content-stretch { align-content: stretch !important; }

.ph-align-self-auto { align-self: auto !important; }
.ph-align-self-start { align-self: flex-start !important; }
.ph-align-self-end { align-self: flex-end !important; }
.ph-align-self-center { align-self: center !important; }
.ph-align-self-baseline { align-self: baseline !important; }
.ph-align-self-stretch { align-self: stretch !important; }

/* ===== POSITION UTILITIES ===== */
.ph-position-static { position: static !important; }
.ph-position-relative { position: relative !important; }
.ph-position-absolute { position: absolute !important; }
.ph-position-fixed { position: fixed !important; }
.ph-position-sticky { position: sticky !important; }

.ph-top-0 { top: 0 !important; }
.ph-top-50 { top: 50% !important; }
.ph-top-100 { top: 100% !important; }
.ph-bottom-0 { bottom: 0 !important; }
.ph-bottom-50 { bottom: 50% !important; }
.ph-bottom-100 { bottom: 100% !important; }
.ph-start-0 { left: 0 !important; }
.ph-start-50 { left: 50% !important; }
.ph-start-100 { left: 100% !important; }
.ph-end-0 { right: 0 !important; }
.ph-end-50 { right: 50% !important; }
.ph-end-100 { right: 100% !important; }

/* ===== SIZING UTILITIES ===== */
.ph-w-25 { width: 25% !important; }
.ph-w-50 { width: 50% !important; }
.ph-w-75 { width: 75% !important; }
.ph-w-100 { width: 100% !important; }
.ph-w-auto { width: auto !important; }

.ph-mw-100 { max-width: 100% !important; }

.ph-vw-100 { width: 100vw !important; }
.ph-min-vw-100 { min-width: 100vw !important; }

.ph-h-25 { height: 25% !important; }
.ph-h-50 { height: 50% !important; }
.ph-h-75 { height: 75% !important; }
.ph-h-100 { height: 100% !important; }
.ph-h-auto { height: auto !important; }

.ph-mh-100 { max-height: 100% !important; }

.ph-vh-100 { height: 100vh !important; }
.ph-min-vh-100 { min-height: 100vh !important; }

/* ===== BORDER UTILITIES ===== */
.ph-border { border: 1px solid var(--ph-gray-300) !important; }
.ph-border-0 { border: 0 !important; }
.ph-border-top { border-top: 1px solid var(--ph-gray-300) !important; }
.ph-border-top-0 { border-top: 0 !important; }
.ph-border-end { border-right: 1px solid var(--ph-gray-300) !important; }
.ph-border-end-0 { border-right: 0 !important; }
.ph-border-bottom { border-bottom: 1px solid var(--ph-gray-300) !important; }
.ph-border-bottom-0 { border-bottom: 0 !important; }
.ph-border-start { border-left: 1px solid var(--ph-gray-300) !important; }
.ph-border-start-0 { border-left: 0 !important; }

.ph-border-primary { border-color: var(--ph-primary) !important; }
.ph-border-secondary { border-color: var(--ph-secondary) !important; }
.ph-border-success { border-color: var(--ph-success) !important; }
.ph-border-danger { border-color: var(--ph-danger) !important; }
.ph-border-warning { border-color: var(--ph-warning) !important; }
.ph-border-info { border-color: var(--ph-info) !important; }
.ph-border-light { border-color: var(--ph-light) !important; }
.ph-border-dark { border-color: var(--ph-dark) !important; }
.ph-border-white { border-color: var(--ph-white) !important; }

.ph-rounded { border-radius: var(--ph-radius-md) !important; }
.ph-rounded-0 { border-radius: 0 !important; }
.ph-rounded-1 { border-radius: var(--ph-radius-sm) !important; }
.ph-rounded-2 { border-radius: var(--ph-radius-base) !important; }
.ph-rounded-3 { border-radius: var(--ph-radius-lg) !important; }
.ph-rounded-circle { border-radius: 50% !important; }
.ph-rounded-pill { border-radius: var(--ph-radius-full) !important; }

.ph-rounded-top { border-top-left-radius: var(--ph-radius-md) !important; border-top-right-radius: var(--ph-radius-md) !important; }
.ph-rounded-end { border-top-right-radius: var(--ph-radius-md) !important; border-bottom-right-radius: var(--ph-radius-md) !important; }
.ph-rounded-bottom { border-bottom-right-radius: var(--ph-radius-md) !important; border-bottom-left-radius: var(--ph-radius-md) !important; }
.ph-rounded-start { border-bottom-left-radius: var(--ph-radius-md) !important; border-top-left-radius: var(--ph-radius-md) !important; }

/* ===== BACKGROUND UTILITIES ===== */
.ph-bg-primary { background-color: var(--ph-primary) !important; }
.ph-bg-secondary { background-color: var(--ph-secondary) !important; }
.ph-bg-success { background-color: var(--ph-success) !important; }
.ph-bg-danger { background-color: var(--ph-danger) !important; }
.ph-bg-warning { background-color: var(--ph-warning) !important; }
.ph-bg-info { background-color: var(--ph-info) !important; }
.ph-bg-light { background-color: var(--ph-light) !important; }
.ph-bg-dark { background-color: var(--ph-dark) !important; }
.ph-bg-white { background-color: var(--ph-white) !important; }
.ph-bg-transparent { background-color: transparent !important; }

/* ===== OPACITY UTILITIES ===== */
.ph-opacity-0 { opacity: 0 !important; }
.ph-opacity-25 { opacity: 0.25 !important; }
.ph-opacity-50 { opacity: 0.5 !important; }
.ph-opacity-75 { opacity: 0.75 !important; }
.ph-opacity-100 { opacity: 1 !important; }

/* ===== OVERFLOW UTILITIES ===== */
.ph-overflow-auto { overflow: auto !important; }
.ph-overflow-hidden { overflow: hidden !important; }
.ph-overflow-visible { overflow: visible !important; }
.ph-overflow-scroll { overflow: scroll !important; }

/* ===== VISIBILITY UTILITIES ===== */
.ph-visible { visibility: visible !important; }
.ph-invisible { visibility: hidden !important; }

/* ===== Z-INDEX UTILITIES ===== */
.ph-z-n1 { z-index: -1 !important; }
.ph-z-0 { z-index: 0 !important; }
.ph-z-1 { z-index: 1 !important; }
.ph-z-2 { z-index: 2 !important; }
.ph-z-3 { z-index: 3 !important; }

/* ===== RESPONSIVE UTILITIES ===== */
@media (min-width: 576px) {
    .ph-d-sm-none { display: none !important; }
    .ph-d-sm-inline { display: inline !important; }
    .ph-d-sm-inline-block { display: inline-block !important; }
    .ph-d-sm-block { display: block !important; }
    .ph-d-sm-grid { display: grid !important; }
    .ph-d-sm-flex { display: flex !important; }
    .ph-d-sm-inline-flex { display: inline-flex !important; }
}

@media (min-width: 768px) {
    .ph-d-md-none { display: none !important; }
    .ph-d-md-inline { display: inline !important; }
    .ph-d-md-inline-block { display: inline-block !important; }
    .ph-d-md-block { display: block !important; }
    .ph-d-md-grid { display: grid !important; }
    .ph-d-md-flex { display: flex !important; }
    .ph-d-md-inline-flex { display: inline-flex !important; }
}

@media (min-width: 992px) {
    .ph-d-lg-none { display: none !important; }
    .ph-d-lg-inline { display: inline !important; }
    .ph-d-lg-inline-block { display: inline-block !important; }
    .ph-d-lg-block { display: block !important; }
    .ph-d-lg-grid { display: grid !important; }
    .ph-d-lg-flex { display: flex !important; }
    .ph-d-lg-inline-flex { display: inline-flex !important; }
}

@media (min-width: 1200px) {
    .ph-d-xl-none { display: none !important; }
    .ph-d-xl-inline { display: inline !important; }
    .ph-d-xl-inline-block { display: inline-block !important; }
    .ph-d-xl-block { display: block !important; }
    .ph-d-xl-grid { display: grid !important; }
    .ph-d-xl-flex { display: flex !important; }
    .ph-d-xl-inline-flex { display: inline-flex !important; }
}

/* ===== ANIMATION UTILITIES ===== */
.ph-fade-in {
    animation: phFadeIn 0.3s ease-in;
}

.ph-fade-out {
    animation: phFadeOut 0.3s ease-out;
}

.ph-slide-in-up {
    animation: phSlideInUp 0.3s ease-out;
}

.ph-slide-in-down {
    animation: phSlideInDown 0.3s ease-out;
}

.ph-scale-in {
    animation: phScaleIn 0.2s ease-out;
}

@keyframes phFadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes phFadeOut {
    from { opacity: 1; }
    to { opacity: 0; }
}

@keyframes phSlideInUp {
    from { 
        opacity: 0; 
        transform: translateY(1rem); 
    }
    to { 
        opacity: 1; 
        transform: translateY(0); 
    }
}

@keyframes phSlideInDown {
    from { 
        opacity: 0; 
        transform: translateY(-1rem); 
    }
    to { 
        opacity: 1; 
        transform: translateY(0); 
    }
}

@keyframes phScaleIn {
    from { 
        opacity: 0; 
        transform: scale(0.9); 
    }
    to { 
        opacity: 1; 
        transform: scale(1); 
    }
}

/* ===== INTERACTION UTILITIES ===== */
.ph-user-select-all { user-select: all !important; }
.ph-user-select-auto { user-select: auto !important; }
.ph-user-select-none { user-select: none !important; }

.ph-pe-none { pointer-events: none !important; }
.ph-pe-auto { pointer-events: auto !important; }

.ph-cursor-pointer { cursor: pointer !important; }
.ph-cursor-default { cursor: default !important; }
.ph-cursor-not-allowed { cursor: not-allowed !important; }

/* ===== ACCESSIBILITY UTILITIES ===== */
.ph-sr-only {
    position: absolute !important;
    width: 1px !important;
    height: 1px !important;
    padding: 0 !important;
    margin: -1px !important;
    overflow: hidden !important;
    clip: rect(0, 0, 0, 0) !important;
    white-space: nowrap !important;
    border: 0 !important;
}

.ph-sr-only-focusable:focus {
    position: static !important;
    width: auto !important;
    height: auto !important;
    padding: inherit !important;
    margin: inherit !important;
    overflow: visible !important;
    clip: auto !important;
    white-space: normal !important;
}

/* ===== PRINT UTILITIES ===== */
@media print {
    .ph-d-print-none { display: none !important; }
    .ph-d-print-inline { display: inline !important; }
    .ph-d-print-inline-block { display: inline-block !important; }
    .ph-d-print-block { display: block !important; }
    .ph-d-print-grid { display: grid !important; }
    .ph-d-print-flex { display: flex !important; }
    .ph-d-print-inline-flex { display: inline-flex !important; }
}

/* ===== ACCESSIBILITY UTILITIES ===== */
/* Skip links for keyboard navigation */
.skip-link {
    position: absolute;
    top: -40px;
    left: 6px;
    z-index: 999999;
    padding: 8px 16px;
    background-color: var(--ph-primary);
    color: var(--ph-white);
    text-decoration: none;
    border-radius: 4px;
    font-weight: 600;
    font-size: 14px;
    line-height: 1.4;
    transition: top 0.3s ease;
}

.skip-link:focus {
    top: 6px;
    outline: 2px solid var(--ph-focus);
    outline-offset: 2px;
}

/* Visually hidden but available to screen readers */
.visually-hidden {
    position: absolute !important;
    width: 1px !important;
    height: 1px !important;
    padding: 0 !important;
    margin: -1px !important;
    overflow: hidden !important;
    clip: rect(0, 0, 0, 0) !important;
    white-space: nowrap !important;
    border: 0 !important;
}

/* Only visually hidden when not focused (for skip links) */
.visually-hidden-focusable:not(:focus):not(:focus-within) {
    position: absolute !important;
    width: 1px !important;
    height: 1px !important;
    padding: 0 !important;
    margin: -1px !important;
    overflow: hidden !important;
    clip: rect(0, 0, 0, 0) !important;
    white-space: nowrap !important;
    border: 0 !important;
}

/* High contrast mode support */
@media (forced-colors: active) {
    .skip-link {
        background-color: ButtonText;
        color: ButtonFace;
        border: 1px solid ButtonText;
    }
    
    .skip-link:focus {
        outline: 2px solid Highlight;
        outline-offset: 2px;
    }
}

/* Ensure minimum touch target size (44px) */
.ph-btn,
button,
[role="button"],
input[type="button"],
input[type="submit"],
input[type="reset"] {
    min-height: 44px;
    min-width: 44px;
}

/* Focus styles for better keyboard navigation */
.ph-btn:focus,
button:focus,
[role="button"]:focus,
input:focus,
textarea:focus,
select:focus,
a:focus {
    outline: 2px solid var(--ph-focus, #0066cc);
    outline-offset: 2px;
}

/* Remove focus outline for mouse users but keep for keyboard users */
.ph-btn:focus:not(:focus-visible),
button:focus:not(:focus-visible),
[role="button"]:focus:not(:focus-visible),
input:focus:not(:focus-visible),
textarea:focus:not(:focus-visible),
select:focus:not(:focus-visible),
a:focus:not(:focus-visible) {
    outline: none;
}