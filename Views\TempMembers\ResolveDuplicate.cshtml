@model DuplicateResolutionViewModel
@{
    ViewData["Title"] = "Resolve Duplicate Member";
}

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <!-- Header -->
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h2 class="mb-1">Resolve Duplicate Member</h2>
                    <p class="text-muted mb-0">Choose which values to keep when merging these records</p>
                </div>
                <div>
                    <a href="@Url.Action("Queue", new { batchId = Model.TempMember.ImportBatchId, status = TempMemberStatus.Duplicate })" 
                       class="btn btn-outline-primary">
                        <i class="fas fa-arrow-left me-1"></i>Back to Duplicates Queue
                    </a>
                </div>
            </div>

            <!-- Duplicate Information Alert -->
            <div class="alert alert-info mb-4">
                <h5 class="alert-heading">
                    <i class="fas fa-info-circle me-2"></i>Duplicate Detected
                </h5>
                <p class="mb-2">
                    We found an existing member that appears to match the imported record. 
                    Please review the information below and choose which values to keep.
                </p>
                <hr>
                <p class="mb-0">
                    <strong>Match Reason:</strong> 
                    @if (!string.IsNullOrEmpty(Model.ExistingMember.Email) && Model.TempMember.Email == Model.ExistingMember.Email)
                    {
                        <span class="badge bg-primary">Email Match</span>
                    }
                    @if (Model.ExistingMember.FirstName == Model.TempMember.FirstName && 
                         Model.ExistingMember.LastName == Model.TempMember.LastName && 
                         Model.ExistingMember.DateOfBirth == Model.TempMember.DateOfBirth)
                    {
                        <span class="badge bg-secondary">Name + DOB Match</span>
                    }
                </p>
            </div>

            <!-- Comparison Form -->
            <form method="post" action="@Url.Action("ResolveDuplicate", new { id = Model.TempMember.TempMemberId })">
                @Html.AntiForgeryToken()

                <!-- Side-by-Side Comparison -->
                <div class="row">
                    <div class="col-lg-6">
                        <div class="card h-100">
                            <div class="card-header bg-info text-white">
                                <h5 class="card-title mb-0">
                                    <i class="fas fa-upload me-2"></i>Imported Record (New)
                                </h5>
                            </div>
                            <div class="card-body">
                                <div class="member-info">
                                    @await Html.PartialAsync("_MemberComparisonInfo", Model.TempMember, new ViewDataDictionary(ViewData) { { "IsExisting", false } })
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-lg-6">
                        <div class="card h-100">
                            <div class="card-header bg-success text-white">
                                <h5 class="card-title mb-0">
                                    <i class="fas fa-user me-2"></i>Existing Member (Current)
                                </h5>
                            </div>
                            <div class="card-body">
                                <div class="member-info">
                                    @await Html.PartialAsync("_MemberComparisonInfo", Model.ExistingMember, new ViewDataDictionary(ViewData) { { "IsExisting", true } })
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Field Selection -->
                <div class="card mt-4">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-exchange-alt me-2"></i>Choose Values to Keep
                        </h5>
                    </div>
                    <div class="card-body">
                        <p class="text-muted mb-4">
                            For each field below, choose whether to keep the existing value or update it with the imported value.
                            Only fields with differences are shown.
                        </p>

                        <div class="row">
                            @foreach (var kvp in Model.FieldComparisons)
                            {
                                var fieldName = kvp.Key;
                                var comparison = kvp.Value;
                                
                                <div class="col-md-6 mb-4">
                                    <div class="field-comparison">
                                        <label class="form-label fw-bold">@comparison.DisplayName</label>
                                        
                                        @if (comparison.AreIdentical)
                                        {
                                            <div class="alert alert-success py-2">
                                                <i class="fas fa-check-circle me-2"></i>
                                                Values are identical: <strong>@(comparison.ExistingValue ?? "Not set")</strong>
                                            </div>
                                        }
                                        else
                                        {
                                            <div class="row">
                                                <div class="col-6">
                                                    <div class="form-check">
                                                        <input class="form-check-input" type="radio" 
                                                               name="fieldChoices[@fieldName]" 
                                                               value="existing" 
                                                               id="@(fieldName)_existing"
                                                               checked="@(comparison.SelectedValue == "existing")">
                                                        <label class="form-check-label" for="@(fieldName)_existing">
                                                            <strong>Keep Current:</strong><br>
                                                            <span class="text-muted">@(comparison.ExistingValue ?? "Not set")</span>
                                                        </label>
                                                    </div>
                                                </div>
                                                <div class="col-6">
                                                    <div class="form-check">
                                                        <input class="form-check-input" type="radio" 
                                                               name="fieldChoices[@fieldName]" 
                                                               value="new" 
                                                               id="@(fieldName)_new"
                                                               checked="@(comparison.SelectedValue == "new")">
                                                        <label class="form-check-label" for="@(fieldName)_new">
                                                            <strong>Use Imported:</strong><br>
                                                            <span class="text-muted">@(comparison.TempValue ?? "Not set")</span>
                                                        </label>
                                                    </div>
                                                </div>
                                            </div>
                                        }
                                    </div>
                                </div>
                            }
                        </div>

                        @if (!Model.FieldComparisons.Values.Any(c => !c.AreIdentical))
                        {
                            <div class="alert alert-success">
                                <h5 class="alert-heading">
                                    <i class="fas fa-check-circle me-2"></i>Perfect Match Found
                                </h5>
                                <p class="mb-0">
                                    All fields in the imported record match the existing member exactly. 
                                    You can safely merge these records without any changes.
                                </p>
                            </div>
                        }
                    </div>
                </div>

                <!-- Action Buttons -->
                <div class="card mt-4">
                    <div class="card-body">
                        <div class="d-flex justify-content-between">
                            <div>
                                <a href="@Url.Action("Queue", new { batchId = Model.TempMember.ImportBatchId, status = TempMemberStatus.Duplicate })" 
                                   class="btn btn-outline-secondary">
                                    <i class="fas fa-times me-1"></i>Cancel
                                </a>
                                
                                <form method="post" action="@Url.Action("Reject", new { id = Model.TempMember.TempMemberId })" class="d-inline ms-2">
                                    @Html.AntiForgeryToken()
                                    <button type="submit" class="btn btn-outline-danger" 
                                            onclick="return confirm('Reject this imported record? The existing member will remain unchanged.')">
                                        <i class="fas fa-trash me-1"></i>Reject Import
                                    </button>
                                </form>
                            </div>
                            <div>
                                <button type="button" class="btn btn-info me-2" id="previewBtn">
                                    <i class="fas fa-eye me-1"></i>Preview Changes
                                </button>
                                
                                <button type="button" class="btn btn-success" id="confirmMergeBtn">
                                    <i class="fas fa-compress-arrows-alt me-1"></i>Confirm & Merge
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Enhanced Confirmation Modal -->
<div class="modal fade" id="confirmationModal" tabindex="-1" aria-labelledby="confirmationModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="confirmationModalLabel">
                    <i class="fas fa-exclamation-triangle text-warning me-2"></i>Confirm Member Merge
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="alert alert-info mb-4">
                    <h6 class="alert-heading">
                        <i class="fas fa-info-circle me-2"></i>Review Changes Before Merging
                    </h6>
                    <p class="mb-0">
                        Please review the changes below. Only fields that will be updated are shown. 
                        This action cannot be undone.
                    </p>
                </div>

                <!-- Loading spinner -->
                <div id="confirmationLoading" class="text-center py-4">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Loading preview...</span>
                    </div>
                    <p class="mt-2 text-muted">Generating merge preview...</p>
                </div>

                <!-- Confirmation content -->
                <div id="confirmationContent" style="display: none;">
                    <div class="row">
                        <div class="col-12">
                            <h6 class="text-muted mb-3">
                                <i class="fas fa-list me-2"></i>Field Changes Summary
                            </h6>
                            <div id="changesTable">
                                <!-- Changes will be populated here -->
                            </div>
                        </div>
                    </div>
                    
                    <div id="noChangesAlert" class="alert alert-success mt-3" style="display: none;">
                        <h6 class="alert-heading">
                            <i class="fas fa-check-circle me-2"></i>No Changes Required
                        </h6>
                        <p class="mb-0">
                            All selected values match the existing member data. 
                            The records will be merged without any field updates.
                        </p>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-outline-secondary me-2" data-bs-dismiss="modal">
                    <i class="fas fa-times me-1"></i>Cancel
                </button>
                <button type="button" class="btn btn-warning me-2" id="editChoicesBtn">
                    <i class="fas fa-edit me-1"></i>Edit Selections
                </button>
                <button type="button" class="btn btn-success" id="finalMergeBtn">
                    <i class="fas fa-check me-1"></i>Accept & Merge
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Quick Preview Modal (for Preview Changes button) -->
<div class="modal fade" id="previewModal" tabindex="-1" aria-labelledby="previewModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="previewModalLabel">
                    <i class="fas fa-eye me-2"></i>Quick Preview
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p class="text-muted">Current field selections for merge:</p>
                <div id="previewContent">
                    <!-- Preview content will be loaded here -->
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script>
        $(document).ready(function() {
            // Handle quick preview button
            $('#previewBtn').click(function() {
                // Generate simple preview content
                let previewHtml = '<div class="list-group">';
                
                // Loop through selected radio buttons to show preview
                $('input[type="radio"]:checked').each(function() {
                    const name = this.name.match(/fieldChoices\[(.*?)\]/);
                    if (name) {
                        const fieldName = name[1];
                        const choice = this.value;
                        const fieldComparison = $(this).closest('.field-comparison');
                        const fieldLabel = fieldComparison.find('label.fw-bold').text();
                        const selectedLabel = $(this).next('label').find('.text-muted').text();
                        
                        previewHtml += '<div class="list-group-item d-flex justify-content-between align-items-center">';
                        previewHtml += '<div>';
                        previewHtml += '<strong>' + fieldLabel + ':</strong> ' + selectedLabel;
                        previewHtml += '</div>';
                        if (choice === 'new') {
                            previewHtml += '<span class="badge bg-success">Will Update</span>';
                        } else {
                            previewHtml += '<span class="badge bg-secondary">Keep Current</span>';
                        }
                        previewHtml += '</div>';
                    }
                });
                
                previewHtml += '</div>';
                
                // Show in quick preview modal
                $('#previewContent').html(previewHtml);
                $('#previewModal').modal('show');
            });

            // Handle confirm merge button - shows detailed confirmation modal
            $('#confirmMergeBtn').click(function() {
                // Validate that at least some choices are made
                const checkedInputs = $('input[type="radio"]:checked');
                if (checkedInputs.length === 0) {
                    alert('Please make selections for the fields you want to merge.');
                    return;
                }

                // Show confirmation modal with loading state
                $('#confirmationModal').modal('show');
                $('#confirmationLoading').show();
                $('#confirmationContent').hide();

                // Collect field choices
                const fieldChoices = {};
                checkedInputs.each(function() {
                    const name = this.name.match(/fieldChoices\[(.*?)\]/);
                    if (name) {
                        fieldChoices[name[1]] = this.value;
                    }
                });

                // Call ConfirmMerge action to get detailed preview
                $.ajax({
                    url: '@Url.Action("ConfirmMerge", new { id = Model.TempMember.TempMemberId })',
                    type: 'POST',
                    data: {
                        __RequestVerificationToken: $('input[name="__RequestVerificationToken"]').val(),
                        ...Object.fromEntries(Object.entries(fieldChoices).map(([key, value]) => [`fieldChoices[${key}]`, value]))
                    },
                    success: function(response) {
                        if (response.success) {
                            displayConfirmationContent(response.mergePreview);
                        } else {
                            $('#confirmationLoading').html('<div class="alert alert-danger">Error: ' + response.error + '</div>');
                        }
                    },
                    error: function() {
                        $('#confirmationLoading').html('<div class="alert alert-danger">Failed to load merge preview. Please try again.</div>');
                    }
                });
            });

            // Display confirmation content with old → new format
            function displayConfirmationContent(mergePreview) {
                $('#confirmationLoading').hide();
                
                let hasChanges = false;
                let tableHtml = '<div class="table-responsive"><table class="table table-bordered table-sm">';
                tableHtml += '<thead class="table-light">';
                tableHtml += '<tr><th>Field</th><th>Current Value</th><th></th><th>New Value</th><th>Status</th></tr>';
                tableHtml += '</thead><tbody>';

                mergePreview.forEach(function(item) {
                    const statusClass = item.WillChange ? 'table-warning' : 'table-light';
                    const statusBadge = item.WillChange ? '<span class="badge bg-warning">Will Update</span>' : '<span class="badge bg-secondary">No Change</span>';
                    const arrow = item.WillChange ? '<i class="fas fa-arrow-right text-primary"></i>' : '<i class="fas fa-equals text-muted"></i>';
                    
                    if (item.WillChange) hasChanges = true;
                    
                    tableHtml += `<tr class="${statusClass}">`;
                    tableHtml += `<td><strong>${item.DisplayName}</strong></td>`;
                    tableHtml += `<td><code>${item.OldValue}</code></td>`;
                    tableHtml += `<td class="text-center">${arrow}</td>`;
                    tableHtml += `<td><code>${item.NewValue}</code></td>`;
                    tableHtml += `<td>${statusBadge}</td>`;
                    tableHtml += '</tr>';
                });

                tableHtml += '</tbody></table></div>';
                
                $('#changesTable').html(tableHtml);
                
                if (!hasChanges) {
                    $('#noChangesAlert').show();
                }
                
                $('#confirmationContent').show();
            }

            // Handle edit choices button
            $('#editChoicesBtn').click(function() {
                $('#confirmationModal').modal('hide');
            });

            // Handle final merge button
            $('#finalMergeBtn').click(function() {
                // Show loading state
                $(this).prop('disabled', true).html('<span class="spinner-border spinner-border-sm me-1"></span>Merging...');
                
                // Submit the form
                $('form').submit();
            });

            // Highlight differences
            $('.field-comparison').each(function() {
                const $this = $(this);
                const hasRadios = $this.find('input[type="radio"]').length > 0;
                
                if (hasRadios) {
                    $this.addClass('border border-warning bg-light p-3 rounded');
                }
            });

            // Auto-select recommended choices
            $('.form-check-input[checked]').closest('.form-check').addClass('bg-primary bg-opacity-10 border border-primary rounded p-2');

            // Update styling when radio buttons change
            $('input[type="radio"]').change(function() {
                const $fieldComparison = $(this).closest('.field-comparison');
                $fieldComparison.find('.form-check').removeClass('bg-primary bg-opacity-10 border border-primary rounded p-2');
                $(this).closest('.form-check').addClass('bg-primary bg-opacity-10 border border-primary rounded p-2');
            });
        });
    </script>
}