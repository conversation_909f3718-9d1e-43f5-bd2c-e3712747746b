# Test script for Page Audit Framework
# This script demonstrates the basic functionality of the Page Audit Service

Write-Host "🏒 Para Hockey - Page Audit Framework Test" -ForegroundColor Green
Write-Host "=========================================" -ForegroundColor Green

Write-Host "`n📋 Testing Page Audit Framework Components..." -ForegroundColor Yellow

# Test 1: Build the project
Write-Host "`n1. Building the project..." -ForegroundColor Cyan
$buildResult = dotnet build ParaHockeyApp.csproj --verbosity quiet
if ($LASTEXITCODE -eq 0) {
    Write-Host "   ✅ Build successful" -ForegroundColor Green
} else {
    Write-Host "   ❌ Build failed" -ForegroundColor Red
    exit 1
}

# Test 2: Check if migration was created
Write-Host "`n2. Checking migration files..." -ForegroundColor Cyan
$migrationFiles = Get-ChildItem -Path "Migrations" -Filter "*AddPageAuditSystem*"
if ($migrationFiles -and $migrationFiles.Count -gt 0) {
    Write-Host "   ✅ Page Audit migration created: $($migrationFiles[0].Name)" -ForegroundColor Green
} else {
    Write-Host "   ❌ Page Audit migration not found" -ForegroundColor Red
}

# Test 3: Check if service files exist
Write-Host "`n3. Checking service files..." -ForegroundColor Cyan
$serviceFiles = @(
    "Services/IPageAuditService.cs",
    "Services/PageAuditService.cs"
)

foreach ($file in $serviceFiles) {
    if (Test-Path $file) {
        Write-Host "   ✅ $file exists" -ForegroundColor Green
    } else {
        Write-Host "   ❌ $file missing" -ForegroundColor Red
    }
}

# Test 4: Check if model files exist
Write-Host "`n4. Checking model files..." -ForegroundColor Cyan
$modelFiles = @(
    "Models/Entities/PageInventory.cs",
    "Models/Entities/PageInfo.cs",
    "Models/Entities/PageAuditResult.cs",
    "Models/Entities/AuditFinding.cs"
)

foreach ($file in $modelFiles) {
    if (Test-Path $file) {
        Write-Host "   ✅ $file exists" -ForegroundColor Green
    } else {
        Write-Host "   ❌ $file missing" -ForegroundColor Red
    }
}

# Test 5: Check if documentation exists
Write-Host "`n5. Checking documentation..." -ForegroundColor Cyan
$docFiles = @(
    "docs/BRANCHING_STRATEGY.md"
)

foreach ($file in $docFiles) {
    if (Test-Path $file) {
        Write-Host "   ✅ $file exists" -ForegroundColor Green
    } else {
        Write-Host "   ❌ $file missing" -ForegroundColor Red
    }
}

# Test 6: Check if service is registered in Program.cs
Write-Host "`n6. Checking service registration..." -ForegroundColor Cyan
$programContent = Get-Content "Program.cs" -Raw
if ($programContent -match "IPageAuditService.*PageAuditService") {
    Write-Host "   ✅ PageAuditService is registered in DI container" -ForegroundColor Green
} else {
    Write-Host "   ❌ PageAuditService not found in Program.cs" -ForegroundColor Red
}

# Test 7: Check if DbSets are added to ApplicationContext
Write-Host "`n7. Checking database context..." -ForegroundColor Cyan
$contextContent = Get-Content "Models/ApplicationContext.cs" -Raw
$dbSets = @("PageInventories", "PageInfos", "PageAuditResults", "AuditFindings")
$allDbSetsFound = $true

foreach ($dbSet in $dbSets) {
    if ($contextContent -match "DbSet<.*>.*$dbSet") {
        Write-Host "   ✅ $dbSet DbSet found" -ForegroundColor Green
    } else {
        Write-Host "   ❌ $dbSet DbSet missing" -ForegroundColor Red
        $allDbSetsFound = $false
    }
}

Write-Host "`n📊 Test Summary:" -ForegroundColor Yellow
Write-Host "=================" -ForegroundColor Yellow

if ($allDbSetsFound) {
    Write-Host "✅ All Page Audit Framework components are properly set up!" -ForegroundColor Green
    Write-Host "`n🚀 Next Steps:" -ForegroundColor Cyan
    Write-Host "1. Run 'dotnet ef database update' to apply the migration" -ForegroundColor White
    Write-Host "2. Start the application to test the Page Audit Service" -ForegroundColor White
    Write-Host "3. Use the service to generate page inventory and audit pages" -ForegroundColor White
    Write-Host "4. Follow the branching strategy in docs/BRANCHING_STRATEGY.md" -ForegroundColor White
} else {
    Write-Host "❌ Some components are missing. Please check the errors above." -ForegroundColor Red
}

Write-Host "`n🏒 Page Audit Framework Test Complete!" -ForegroundColor Green