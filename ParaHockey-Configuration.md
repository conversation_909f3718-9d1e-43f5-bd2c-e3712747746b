# ParaHockey - Configuration Reference

## 🔐 **Database Credentials**

### **ParaHockey SQL Server User**

-   **Username:** `ParaHockeyUser`
-   **Password:** `***************`
-   **Access:** ParaHockeyDB, ParaHockeyDB_TEST (db_owner)

## 🗄️ **Database Information**

### **SQL Server Instance**

-   **Server:** `SIMBA\SQLEXPRESS`
-   **Local Connection:** `localhost\SQLEXPRESS`

### **ParaHockey Databases**

-   **Production:** `ParaHockeyDB`
-   **Test:** `ParaHockeyDB_TEST`
-   **Development:** `ParaHockeyLocal.db` (SQLite)

## 🌐 **Website Configuration**

### **Environment Settings**

#### **Development (Local)**

-   **Database:** SQLite (`ParaHockeyLocal.db`)
-   **Environment:** `DEVELOPMENT`
-   **Config File:** `appsettings.Development.json`
-   **Banner:** None

#### **Test Environment**

-   **Database:** `ParaHockeyDB_TEST` (SQL Server)
-   **Environment:** `Staging`
-   **Config File:** `appsettings.json` + `appsettings.Staging.json`
-   **Banner:** Red "Para Hockey TEST Site"
-   **IIS Environment Variable:** `ASPNETCORE_ENVIRONMENT = Staging`

#### **Production Environment**

-   **Database:** `ParaHockeyDB` (SQL Server)
-   **Environment:** `Production`
-   **Config File:** `appsettings.json` + `appsettings.Production.json`
-   **Banner:** None
-   **IIS Environment Variable:** None (uses default)

## 🔗 **Connection Strings**

### **Test Environment**

```json
"ConnectionStrings": {
    "DefaultConnection": "Server=localhost\\SQLEXPRESS;User Id=ParaHockeyUser;Password=***************;Database=ParaHockeyDB_TEST;Encrypt=False;"
}
```

### **Production Environment**

```json
"ConnectionStrings": {
    "DefaultConnection": "Server=localhost\\SQLEXPRESS;User Id=ParaHockeyUser;Password=***************;Database=ParaHockeyDB;Encrypt=False;"
}
```

### **Development Environment**

```json
"ConnectionStrings": {
    "DefaultConnection": "Data Source=ParaHockeyLocal.db"
}
```

## 🖥️ **SIMBA Server Setup**

### **IIS Websites**

-   **ParaHockey-Test:** Uses Staging environment
-   **ParaHockey-Production:** Uses Production environment (default)

### **Application Pools**

-   **ParaHockey-Test:** Recycle when changes made
-   **ParaHockey-Production:** Recycle when changes made

## 📁 **File Locations**

### **Server Paths**

-   **Test:** `C:\inetpub\wwwroot\ParaHockey-Test\`
-   **Production:** `C:\inetpub\wwwroot\ParaHockey-Production\`

### **Configuration Files**

-   **appsettings.json** (base configuration)
-   **appsettings.Development.json** (local development)
-   **appsettings.Staging.json** (test environment - may not be needed if using base)
-   **appsettings.Production.json** (production environment - may not be needed if using base)

## 🚀 **Deployment Process**

### **Azure DevOps Pipeline**

-   **Triggers:** Push to `main` branch
-   **Target:** SIMBA server
-   **Issue:** Currently doesn't clean destination folders (leaves old files)
-   **Fix Needed:** Add clean step to pipeline

### **Git Workflow**

1. Develop on feature branches
2. Merge to `main` branch
3. Pipeline automatically deploys to SIMBA server
4. Test environment and Production environment updated

## 🎨 **Visual Indicators**

### **Test Environment Banner**

-   **Color:** Red
-   **Text:** "🧪 Para Hockey TEST Site 🧪"
-   **CSS:** `environment-test.css`
-   **Condition:** `ShowBanner: true` + `ASPNETCORE_ENVIRONMENT = Staging`

### **Environment Themes**

-   **Development:** Blue (info)
-   **Test:** Red (danger)
-   **Production:** Green (primary)

## 📊 **Sample Data**

### **Test Database**

-   **15 test entries** with French names and emails
-   **Environment field:** "TEST"

### **Production Database**

-   **3 minimal entries** for system verification
-   **Environment field:** "PRODUCTION"

## 🔧 **Troubleshooting**

### **Common Issues**

1. **SQLite fallback:** Check connection string and database permissions
2. **No red banner:** Verify IIS environment variable is set
3. **Wrong environment:** Check which config file is being used
4. **Connection failed:** Verify ParaHockeyUser has database access

### **SQL Server Connection Requirements**

-   **Named Pipes or Shared Memory** (TCP/IP not required)
-   **SQL Server authentication** (not Windows authentication)
-   **User mapping** to correct databases
-   **db_owner permissions** for full access

## 📋 **Verification Checklist**

### **Test Environment Should Show:**

-   ✅ Red banner at top
-   ✅ "Environment: TEST" at bottom
-   ✅ "SQL Server Database"
-   ✅ 15 test entries

### **Production Environment Should Show:**

-   ✅ No banner
-   ✅ "Environment: PRODUCTION" at bottom
-   ✅ "SQL Server Database"
-   ✅ 3 production entries

### **Development Environment Should Show:**

-   ✅ Blue development banner
-   ✅ "Environment: DEVELOPMENT" at bottom
-   ✅ "SQLite Database"
-   ✅ No entries (empty)

---

**Last Updated:** June 25, 2025
**Configuration Status:** In Progress
