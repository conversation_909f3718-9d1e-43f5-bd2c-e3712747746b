using ParaHockeyApp.Models.ViewModels;
using ParaHockeyApp.Models.Entities;
using System.Threading.Tasks;

namespace ParaHockeyApp.Services
{
    /// <summary>
    /// Service interface for managing members.
    /// Following the architectural pattern established in the project
    /// </summary>
    public interface IMemberService
    {
        Task RegisterMemberAsync(MemberRegistrationViewModel viewModel);
        Task RegisterMemberAsync(MemberRegistrationViewModel viewModel, Member member);
        Task UpdateMemberAsync(Member updatedMember, int editorId);
        // --- Parent CRUD ---
        Task<Parent> AddParentAsync(Parent parent);
        Task<Parent?> GetParentAsync(int id);
        Task UpdateParentAsync(Parent parent);
        Task DeleteParentAsync(int id);

        // --- EmergencyContact CRUD ---
        Task<EmergencyContact> AddEmergencyContactAsync(EmergencyContact contact);
        Task<EmergencyContact?> GetEmergencyContactAsync(int id);
        Task UpdateEmergencyContactAsync(EmergencyContact contact);
        Task DeleteEmergencyContactAsync(int id);
    }
}