using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Localization;
using System.Globalization;
using System.Text.Json;

namespace ParaHockeyApp.Services
{
    /// <summary>
    /// Service for handling localization of import-related text and messages
    /// </summary>
    public interface IImportLocalizationService
    {
        /// <summary>
        /// Gets a localized string for the specified key
        /// </summary>
        /// <param name="key">The localization key (e.g., "Import.Title")</param>
        /// <param name="args">Optional format arguments</param>
        /// <returns>Localized string</returns>
        string GetString(string key, params object[] args);

        /// <summary>
        /// Gets a localized error message for the specified error type
        /// </summary>
        /// <param name="errorType">The type of error</param>
        /// <param name="args">Optional format arguments</param>
        /// <returns>Localized error message</returns>
        string GetErrorMessage(string errorType, params object[] args);

        /// <summary>
        /// Gets the current culture
        /// </summary>
        CultureInfo CurrentCulture { get; }

        /// <summary>
        /// Sets the culture for the current operation
        /// </summary>
        /// <param name="culture">The culture to use</param>
        void SetCulture(string culture);
    }

    /// <summary>
    /// Implementation of import localization service using JSON resource files
    /// </summary>
    public class ImportLocalizationService : IImportLocalizationService
    {
        private readonly IConfiguration _configuration;
        private readonly string _resourcePath;
        private Dictionary<string, Dictionary<string, object>> _resources;
        private CultureInfo _currentCulture;

        public ImportLocalizationService(IConfiguration configuration)
        {
            _configuration = configuration;
            _resourcePath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Resources");
            _currentCulture = CultureInfo.CurrentCulture;
            LoadResources();
        }

        public CultureInfo CurrentCulture => _currentCulture;

        public void SetCulture(string culture)
        {
            try
            {
                _currentCulture = new CultureInfo(culture);
                CultureInfo.CurrentCulture = _currentCulture;
                CultureInfo.CurrentUICulture = _currentCulture;
            }
            catch (CultureNotFoundException)
            {
                // Fall back to default culture if specified culture is not supported
                _currentCulture = new CultureInfo("en");
            }
        }

        public string GetString(string key, params object[] args)
        {
            var value = GetLocalizedValue(key);

            if (args != null && args.Length > 0)
            {
                try
                {
                    return string.Format(value, args);
                }
                catch (FormatException)
                {
                    // Return the unformatted string if formatting fails
                    return value;
                }
            }

            return value;
        }

        public string GetErrorMessage(string errorType, params object[] args)
        {
            var key = $"Errors.{errorType}";
            return GetString(key, args);
        }

        private string GetLocalizedValue(string key)
        {
            // Try to get value for current culture
            var cultureName = _currentCulture.Name;
            if (_resources.ContainsKey(cultureName) && TryGetNestedValue(_resources[cultureName], key, out var value))
            {
                return value.ToString();
            }

            // Try to get value for parent culture (e.g., "en" for "en-US")
            if (cultureName.Contains("-"))
            {
                var parentCulture = cultureName.Split('-')[0];
                if (_resources.ContainsKey(parentCulture) && TryGetNestedValue(_resources[parentCulture], key, out value))
                {
                    return value.ToString();
                }
            }

            // Fall back to English
            if (_resources.ContainsKey("en") && TryGetNestedValue(_resources["en"], key, out value))
            {
                return value.ToString();
            }

            // Return the key itself if no localization is found
            return key;
        }

        private bool TryGetNestedValue(Dictionary<string, object> dictionary, string key, out object value)
        {
            value = null;
            var keys = key.Split('.');

            object current = dictionary;
            foreach (var k in keys)
            {
                if (current is Dictionary<string, object> dict && dict.ContainsKey(k))
                {
                    current = dict[k];
                }
                else
                {
                    return false;
                }
            }

            value = current;
            return true;
        }

        private void LoadResources()
        {
            _resources = new Dictionary<string, Dictionary<string, object>>();

            try
            {
                var resourceFiles = Directory.GetFiles(_resourcePath, "ImportResources.*.json");

                foreach (var file in resourceFiles)
                {
                    var fileName = Path.GetFileNameWithoutExtension(file);
                    var culturePart = fileName.Split('.').LastOrDefault();

                    if (!string.IsNullOrEmpty(culturePart))
                    {
                        var content = File.ReadAllText(file);
                        var resources = JsonSerializer.Deserialize<Dictionary<string, object>>(content);

                        if (resources != null)
                        {
                            _resources[culturePart] = ConvertJsonElements(resources);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                // Log error but don't throw - application should still work with default strings
                System.Diagnostics.Debug.WriteLine($"Error loading localization resources: {ex.Message}");
            }

            // Ensure we have at least an empty English resource set
            if (!_resources.ContainsKey("en"))
            {
                _resources["en"] = new Dictionary<string, object>();
            }
        }

        private Dictionary<string, object> ConvertJsonElements(Dictionary<string, object> source)
        {
            var result = new Dictionary<string, object>();

            foreach (var kvp in source)
            {
                if (kvp.Value is JsonElement element)
                {
                    result[kvp.Key] = ConvertJsonElement(element);
                }
                else
                {
                    result[kvp.Key] = kvp.Value;
                }
            }

            return result;
        }

        private object ConvertJsonElement(JsonElement element)
        {
            switch (element.ValueKind)
            {
                case JsonValueKind.String:
                    return element.GetString();
                case JsonValueKind.Object:
                    var dict = new Dictionary<string, object>();
                    foreach (var property in element.EnumerateObject())
                    {
                        dict[property.Name] = ConvertJsonElement(property.Value);
                    }
                    return dict;
                case JsonValueKind.Array:
                    return element.EnumerateArray().Select(ConvertJsonElement).ToArray();
                case JsonValueKind.True:
                    return true;
                case JsonValueKind.False:
                    return false;
                case JsonValueKind.Number:
                    return element.GetDecimal();
                case JsonValueKind.Null:
                    return null;
                default:
                    return element.ToString();
            }
        }
    }
}
