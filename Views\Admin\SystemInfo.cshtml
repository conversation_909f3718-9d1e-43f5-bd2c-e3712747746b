@model dynamic
@{
    ViewData["Title"] = Localizer["SystemInformation"];
}

<div class="container-fluid mt-4">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col">
            <h1 class="h2 text-primary">
                <i class="fas fa-server"></i> @SharedLocalizer["SystemInformation"]
            </h1>
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="@Url.Action("Index", "Admin")">@SharedLocalizer["AdminDashboard"]</a></li>
                    <li class="breadcrumb-item active" aria-current="page">@SharedLocalizer["SystemInfo"]</li>
                </ol>
            </nav>
        </div>
    </div>

    <!-- Environment Information -->
    <div class="row mb-4">
        <div class="col-md-6">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-cog"></i> @SharedLocalizer["EnvironmentConfiguration"]
                    </h5>
                </div>
                <div class="card-body">
                    <table class="table table-borderless">
                        <tr>
                            <td><strong>@SharedLocalizer["Environment"]:</strong></td>
                            <td>
                                <span class="badge bg-@(Model.Environment == "DEVELOPMENT" ? "info" : Model.Environment == "TEST" ? "warning" : "success")">
                                    @Model.Environment
                                </span>
                            </td>
                        </tr>
                        <tr>
                            <td><strong>@SharedLocalizer["Theme"]:</strong></td>
                            <td>@Model.Theme</td>
                        </tr>
                        <tr>
                            <td><strong>@SharedLocalizer["ShowBanner"]:</strong></td>
                            <td>
                                @if (Model.ShowBanner)
                                {
                                    <span class="badge bg-warning">@SharedLocalizer["Yes"]</span>
                                }
                                else
                                {
                                    <span class="badge bg-success">@SharedLocalizer["No"]</span>
                                }
                            </td>
                        </tr>
                        <tr>
                            <td><strong>@SharedLocalizer["Authentication"]:</strong></td>
                            <td>
                                @if (Model.UseAuthentication)
                                {
                                    <span class="badge bg-success">
                                        <i class="fas fa-shield-alt"></i> @SharedLocalizer["Enabled"]
                                    </span>
                                }
                                else
                                {
                                    <span class="badge bg-warning">
                                        <i class="fas fa-shield-alt"></i> @SharedLocalizer["DisabledDevelopment"]
                                    </span>
                                }
                            </td>
                        </tr>
                        <tr>
                            <td><strong>@SharedLocalizer["DatabaseType"]:</strong></td>
                            <td>
                                <span class="badge bg-info">
                                    <i class="fas fa-database"></i> @Model.DatabaseType
                                </span>
                            </td>
                        </tr>
                        <tr>
                            <td><strong>@SharedLocalizer["DatabaseStatus"]:</strong></td>
                            <td>
                                @if (Model.DatabaseCreated)
                                {
                                    <span class="badge bg-success">
                                        <i class="fas fa-check"></i> @SharedLocalizer["Connected"]
                                    </span>
                                }
                                else
                                {
                                    <span class="badge bg-danger">
                                        <i class="fas fa-times"></i> @SharedLocalizer["NotConnected"]
                                    </span>
                                }
                            </td>
                        </tr>
                    </table>
                </div>
            </div>
        </div>

        <div class="col-md-6">
            <div class="card">
                <div class="card-header bg-success text-white">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-chart-bar"></i> @SharedLocalizer["DatabaseStatistics"]
                    </h5>
                </div>
                <div class="card-body">
                    <table class="table table-borderless">
                        <tr>
                            <td><strong>@SharedLocalizer["TotalMembers"]:</strong></td>
                            <td><span class="badge bg-primary fs-6">@Model.TotalMembers</span></td>
                        </tr>
                        <tr>
                            <td><strong>@SharedLocalizer["TotalParents"]:</strong></td>
                            <td><span class="badge bg-success fs-6">@Model.TotalParents</span></td>
                        </tr>
                        <tr>
                            <td><strong>@SharedLocalizer["EmergencyContacts"]:</strong></td>
                            <td><span class="badge bg-warning fs-6">@Model.TotalEmergencyContacts</span></td>
                        </tr>
                    </table>

                    <div class="mt-3">
                        <h6>@SharedLocalizer["QuickActionsText"]</h6>
                        <div class="d-grid gap-2">
                            <a href="@Url.Action("Members", "Admin")" class="btn btn-outline-primary btn-sm">
                                <i class="fas fa-list"></i> @SharedLocalizer["ViewAllMembers"]
                            </a>
                            <a href="@Url.Action("Register", "Members")" class="btn btn-outline-success btn-sm">
                                <i class="fas fa-plus"></i> @SharedLocalizer["AddNewMember"]
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Authentication Information -->
    <div class="row mb-4">
        <div class="col">
            <div class="card">
                <div class="card-header bg-info text-white">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-user-shield"></i> @SharedLocalizer["AuthenticationStatus"]
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <table class="table table-borderless">
                                <tr>
                                    <td><strong>@SharedLocalizer["UserAuthenticated"]:</strong></td>
                                    <td>
                                        @if (Model.UserAuthenticated)
                                        {
                                            <span class="badge bg-success">
                                                <i class="fas fa-check"></i> @SharedLocalizer["Yes"]
                                            </span>
                                        }
                                        else
                                        {
                                            <span class="badge bg-warning">
                                                <i class="fas fa-exclamation-triangle"></i> @SharedLocalizer["No"] (@SharedLocalizer["DevelopmentMode"])
                                            </span>
                                        }
                                    </td>
                                </tr>
                                <tr>
                                    <td><strong>@SharedLocalizer["CurrentUser"]:</strong></td>
                                    <td>@Model.UserName</td>
                                </tr>
                                <tr>
                                    <td><strong>@SharedLocalizer["ServerTime"]:</strong></td>
                                    <td>@((DateTime)Model.ServerTime).ToString("yyyy-MM-dd HH:mm:ss")</td>
                                </tr>
                            </table>
                        </div>
                        <div class="col-md-6">
                            @if (!Model.UseAuthentication)
                            {
                                <div class="alert alert-warning">
                                    <h6><i class="fas fa-info-circle"></i> @SharedLocalizer["DevelopmentMode"]</h6>
                                    <p class="mb-0">@SharedLocalizer["DevelopmentModeMessage"]</p>
                                </div>
                            }
                            else if (!Model.UserAuthenticated)
                            {
                                <div class="alert alert-danger">
                                    <h6><i class="fas fa-exclamation-triangle"></i> @SharedLocalizer["AuthenticationRequired"]</h6>
                                    <p class="mb-0">@SharedLocalizer["AuthenticationError"]</p>
                                </div>
                            }
                            else
                            {
                                <div class="alert alert-success">
                                    <h6><i class="fas fa-shield-alt"></i> @SharedLocalizer["AuthenticatedAccess"]</h6>
                                    <p class="mb-0">@SharedLocalizer["AuthenticatedMessage"]</p>
                                </div>
                            }
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Back to Dashboard -->
    <div class="row">
        <div class="col text-center">
            <a href="@Url.Action("Index", "Admin")" class="btn btn-primary">
                <i class="fas fa-arrow-left"></i> @SharedLocalizer["BackToAdminDashboard"]
            </a>
        </div>
    </div>
</div>