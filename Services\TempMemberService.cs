using Microsoft.EntityFrameworkCore;
using ParaHockeyApp.Models;
using ParaHockeyApp.Models.Entities;
using System.Text.Json;

namespace ParaHockeyApp.Services
{
    /// <summary>
    /// Service for managing temporary member records during import process
    /// </summary>
    public class TempMemberService : ITempMemberService
    {
        private readonly ApplicationContext _context;
        private readonly INormalizationService _normalizationService;
        private readonly IAuditLogService _auditLogService;
        private readonly IMemberService _memberService;

        public TempMemberService(
            ApplicationContext context,
            INormalizationService normalizationService,
            IAuditLogService auditLogService,
            IMemberService memberService)
        {
            _context = context;
            _normalizationService = normalizationService;
            _auditLogService = auditLogService;
            _memberService = memberService;
        }

        /// <summary>
        /// Gets a temp member by ID with related data
        /// </summary>
        public async Task<TempMember?> GetByIdAsync(Guid tempMemberId)
        {
            return await _context.TempMembers
                .Include(tm => tm.ImportBatch)
                .Include(tm => tm.ExistingMember)
                .Include(tm => tm.Gender)
                .Include(tm => tm.Province)
                .Include(tm => tm.PhoneType)
                .Include(tm => tm.RegistrationType)
                .FirstOrDefaultAsync(tm => tm.TempMemberId == tempMemberId);
        }

        /// <summary>
        /// Gets temp members by import batch and status with pagination
        /// </summary>
        public async Task<PagedResult<TempMember>> GetByBatchAsync(int importBatchId, TempMemberStatus? status = null, int pageNumber = 1, int pageSize = 50)
        {
            var query = _context.TempMembers
                .Include(tm => tm.ExistingMember)
                .Include(tm => tm.Gender)
                .Include(tm => tm.Province)
                .Where(tm => tm.ImportBatchId == importBatchId);

            if (status.HasValue)
            {
                query = query.Where(tm => tm.Status == status.Value);
            }

            var totalCount = await query.CountAsync();

            var items = await query
                .OrderBy(tm => tm.DateCreated)
                .Skip((pageNumber - 1) * pageSize)
                .Take(pageSize)
                .ToListAsync();

            return new PagedResult<TempMember>
            {
                Items = items,
                TotalCount = totalCount,
                PageNumber = pageNumber,
                PageSize = pageSize
            };
        }

        /// <summary>
        /// Creates a new member from temp member data
        /// </summary>
        public async Task<Member> CreateMemberFromTempAsync(Guid tempMemberId, string createdBy)
        {
            using var transaction = await _context.Database.BeginTransactionAsync();

            try
            {
                // Get temp member with all necessary data
                var tempMember = await _context.TempMembers
                    .Include(tm => tm.ImportBatch)
                    .FirstOrDefaultAsync(tm => tm.TempMemberId == tempMemberId);

                if (tempMember == null)
                    throw new ArgumentException($"Temp member {tempMemberId} not found");

                if (tempMember.Status != TempMemberStatus.ReadyToCreate && tempMember.Status != TempMemberStatus.NeedsFix)
                    throw new InvalidOperationException($"Temp member {tempMemberId} is not in a valid state for member creation (current status: {tempMember.Status})");

                // Resolve lookup values if not already resolved
                if (!tempMember.GenderId.HasValue || !tempMember.ProvinceId.HasValue || 
                    !tempMember.PhoneTypeId.HasValue || !tempMember.RegistrationTypeId.HasValue)
                {
                    tempMember = await ResolveLookupValuesAsync(tempMemberId);
                }

                // Validate temp member data
                var validationErrors = await ValidateTempMemberAsync(tempMember);
                if (validationErrors.Any())
                {
                    throw new InvalidOperationException($"Temp member has validation errors: {string.Join(", ", validationErrors.Select(e => e.ErrorMessage))}");
                }

                // Create new member from temp data
                var member = new Member
                {
                    FirstName = tempMember.FirstName,
                    LastName = tempMember.LastName,
                    DateOfBirth = tempMember.DateOfBirth ?? DateTime.Today.AddYears(-18), // Default if missing
                    Email = tempMember.Email ?? "",
                    Phone = tempMember.Phone ?? "",
                    Address = tempMember.Address ?? "",
                    City = tempMember.City ?? "",
                    PostalCode = tempMember.PostalCode ?? "",
                    GenderId = tempMember.GenderId!.Value,
                    ProvinceId = tempMember.ProvinceId!.Value,
                    PhoneTypeId = tempMember.PhoneTypeId!.Value,
                    RegistrationTypeId = tempMember.RegistrationTypeId!.Value,
                    DateCreated = DateTime.UtcNow,
                    CreatedBySource = ActionSource.AdminPanel,
                    IsActive = true
                };

                // Save member
                _context.Members.Add(member);
                await _context.SaveChangesAsync();

                // Update temp member status
                tempMember.Status = TempMemberStatus.Created;
                tempMember.DateModified = DateTime.UtcNow;
                tempMember.ModifiedBySource = ActionSource.AdminPanel;

                await _context.SaveChangesAsync();

                // Log the creation
                await _auditLogService.LogActionAsync(
                    $"Created member from temp import: {member.FullName} (Temp ID: {tempMemberId})",
                    member,
                    ActionSource.AdminPanel,
                    createdBy,
                    null);

                await transaction.CommitAsync();
                return member;
            }
            catch
            {
                await transaction.RollbackAsync();
                throw;
            }
        }

        /// <summary>
        /// Bulk creates members from multiple temp member records
        /// </summary>
        public async Task<List<Member>> BulkCreateMembersFromTempAsync(List<Guid> tempMemberIds, string createdBy)
        {
            if (!tempMemberIds.Any())
                return new List<Member>();

            using var transaction = await _context.Database.BeginTransactionAsync();

            try
            {
                var createdMembers = new List<Member>();
                var errors = new List<string>();

                // Process in batches to avoid memory issues
                const int batchSize = 100;
                for (int i = 0; i < tempMemberIds.Count; i += batchSize)
                {
                    var batch = tempMemberIds.Skip(i).Take(batchSize);
                    
                    foreach (var tempMemberId in batch)
                    {
                        try
                        {
                            var member = await CreateSingleMemberFromTempAsync(tempMemberId, createdBy);
                            createdMembers.Add(member);
                        }
                        catch (Exception ex)
                        {
                            errors.Add($"Failed to create member from temp {tempMemberId}: {ex.Message}");
                        }
                    }
                }

                if (errors.Any())
                {
                    throw new InvalidOperationException($"Bulk creation completed with errors: {string.Join("; ", errors)}");
                }

                // Log bulk operation
                await _auditLogService.LogActionAsync(
                    $"Bulk created {createdMembers.Count} members from temp import",
                    null,
                    ActionSource.AdminPanel,
                    createdBy,
                    null);

                await transaction.CommitAsync();
                return createdMembers;
            }
            catch
            {
                await transaction.RollbackAsync();
                throw;
            }
        }

        /// <summary>
        /// Updates a temp member's status
        /// </summary>
        public async Task<TempMember> UpdateStatusAsync(Guid tempMemberId, TempMemberStatus status, string updatedBy)
        {
            var tempMember = await GetByIdAsync(tempMemberId);
            if (tempMember == null)
                throw new ArgumentException($"Temp member {tempMemberId} not found");

            var oldStatus = tempMember.Status;
            tempMember.Status = status;
            tempMember.DateModified = DateTime.UtcNow;
            tempMember.ModifiedBySource = ActionSource.AdminPanel;

            await _context.SaveChangesAsync();

            // Log status change
            await _auditLogService.LogActionAsync(
                $"Updated temp member status from {oldStatus} to {status} for {tempMember.FullName}",
                null,
                ActionSource.AdminPanel,
                updatedBy,
                null);

            return tempMember;
        }

        /// <summary>
        /// Updates temp member data (for fixing validation errors)
        /// </summary>
        public async Task<TempMember> UpdateTempMemberDataAsync(Guid tempMemberId, TempMemberUpdateData updatedData, string updatedBy)
        {
            var tempMember = await GetByIdAsync(tempMemberId);
            if (tempMember == null)
                throw new ArgumentException($"Temp member {tempMemberId} not found");

            var changes = new List<string>();

            // Update fields if provided
            if (!string.IsNullOrEmpty(updatedData.FirstName) && updatedData.FirstName != tempMember.FirstName)
            {
                changes.Add($"FirstName: '{tempMember.FirstName}' → '{updatedData.FirstName}'");
                tempMember.FirstName = updatedData.FirstName;
            }

            if (!string.IsNullOrEmpty(updatedData.LastName) && updatedData.LastName != tempMember.LastName)
            {
                changes.Add($"LastName: '{tempMember.LastName}' → '{updatedData.LastName}'");
                tempMember.LastName = updatedData.LastName;
            }

            if (updatedData.DateOfBirth.HasValue && updatedData.DateOfBirth != tempMember.DateOfBirth)
            {
                changes.Add($"DateOfBirth: '{tempMember.DateOfBirth?.ToString("yyyy-MM-dd")}' → '{updatedData.DateOfBirth?.ToString("yyyy-MM-dd")}'");
                tempMember.DateOfBirth = updatedData.DateOfBirth;
            }

            if (!string.IsNullOrEmpty(updatedData.Email) && updatedData.Email != tempMember.Email)
            {
                changes.Add($"Email: '{tempMember.Email}' → '{updatedData.Email}'");
                tempMember.Email = updatedData.Email;
            }

            if (!string.IsNullOrEmpty(updatedData.Phone) && updatedData.Phone != tempMember.Phone)
            {
                changes.Add($"Phone: '{tempMember.Phone}' → '{updatedData.Phone}'");
                tempMember.Phone = updatedData.Phone;
            }

            if (!string.IsNullOrEmpty(updatedData.Address) && updatedData.Address != tempMember.Address)
            {
                changes.Add($"Address: '{tempMember.Address}' → '{updatedData.Address}'");
                tempMember.Address = updatedData.Address;
            }

            if (!string.IsNullOrEmpty(updatedData.City) && updatedData.City != tempMember.City)
            {
                changes.Add($"City: '{tempMember.City}' → '{updatedData.City}'");
                tempMember.City = updatedData.City;
            }

            if (!string.IsNullOrEmpty(updatedData.PostalCode) && updatedData.PostalCode != tempMember.PostalCode)
            {
                changes.Add($"PostalCode: '{tempMember.PostalCode}' → '{updatedData.PostalCode}'");
                tempMember.PostalCode = updatedData.PostalCode;
            }

            // Update lookup text fields
            if (!string.IsNullOrEmpty(updatedData.GenderText) && updatedData.GenderText != tempMember.GenderText)
            {
                changes.Add($"GenderText: '{tempMember.GenderText}' → '{updatedData.GenderText}'");
                tempMember.GenderText = updatedData.GenderText;
                tempMember.GenderId = null; // Clear resolved ID to force re-resolution
            }

            if (!string.IsNullOrEmpty(updatedData.ProvinceText) && updatedData.ProvinceText != tempMember.ProvinceText)
            {
                changes.Add($"ProvinceText: '{tempMember.ProvinceText}' → '{updatedData.ProvinceText}'");
                tempMember.ProvinceText = updatedData.ProvinceText;
                tempMember.ProvinceId = null; // Clear resolved ID to force re-resolution
            }

            if (!string.IsNullOrEmpty(updatedData.PhoneTypeText) && updatedData.PhoneTypeText != tempMember.PhoneTypeText)
            {
                changes.Add($"PhoneTypeText: '{tempMember.PhoneTypeText}' → '{updatedData.PhoneTypeText}'");
                tempMember.PhoneTypeText = updatedData.PhoneTypeText;
                tempMember.PhoneTypeId = null; // Clear resolved ID to force re-resolution
            }

            if (!string.IsNullOrEmpty(updatedData.RegistrationTypeText) && updatedData.RegistrationTypeText != tempMember.RegistrationTypeText)
            {
                changes.Add($"RegistrationTypeText: '{tempMember.RegistrationTypeText}' → '{updatedData.RegistrationTypeText}'");
                tempMember.RegistrationTypeText = updatedData.RegistrationTypeText;
                tempMember.RegistrationTypeId = null; // Clear resolved ID to force re-resolution
            }

            // Apply normalization to updated data
            tempMember = _normalizationService.NormalizeTempMember(tempMember);

            // Clear validation errors to force re-validation
            tempMember.ValidationErrorsJson = null;

            tempMember.DateModified = DateTime.UtcNow;
            tempMember.ModifiedBySource = ActionSource.AdminPanel;

            await _context.SaveChangesAsync();

            // Log the changes
            if (changes.Any())
            {
                await _auditLogService.LogActionAsync(
                    $"Updated temp member {tempMember.FullName}: {string.Join(", ", changes)}",
                    null,
                    ActionSource.AdminPanel,
                    updatedBy,
                    null);
            }

            return tempMember;
        }

        /// <summary>
        /// Deletes a temp member record
        /// </summary>
        public async Task<bool> DeleteAsync(Guid tempMemberId, string deletedBy)
        {
            var tempMember = await GetByIdAsync(tempMemberId);
            if (tempMember == null)
                return false;

            // Only allow deletion of rejected or certain statuses
            if (tempMember.Status == TempMemberStatus.Created || tempMember.Status == TempMemberStatus.Merged)
            {
                throw new InvalidOperationException($"Cannot delete temp member {tempMemberId} with status {tempMember.Status}");
            }

            // Log before deletion
            await _auditLogService.LogActionAsync(
                $"Deleted temp member {tempMember.FullName} (Status: {tempMember.Status})",
                null,
                ActionSource.AdminPanel,
                deletedBy,
                null);

            _context.TempMembers.Remove(tempMember);
            await _context.SaveChangesAsync();

            return true;
        }

        /// <summary>
        /// Validates temp member data and returns validation errors
        /// </summary>
        public async Task<List<TempMemberValidationError>> ValidateTempMemberAsync(TempMember tempMember)
        {
            var errors = new List<TempMemberValidationError>();

            // Required fields
            if (string.IsNullOrWhiteSpace(tempMember.FirstName))
                errors.Add(new TempMemberValidationError { FieldName = "FirstName", ErrorMessage = "First Name is required", CurrentValue = tempMember.FirstName });

            if (string.IsNullOrWhiteSpace(tempMember.LastName))
                errors.Add(new TempMemberValidationError { FieldName = "LastName", ErrorMessage = "Last Name is required", CurrentValue = tempMember.LastName });

            // Minimum identification requirement: Email OR (Date of Birth + Phone)
            var hasEmail = !string.IsNullOrWhiteSpace(tempMember.Email);
            var hasDobAndPhone = tempMember.DateOfBirth.HasValue && !string.IsNullOrWhiteSpace(tempMember.Phone);

            if (!hasEmail && !hasDobAndPhone)
            {
                errors.Add(new TempMemberValidationError { FieldName = "Identification", ErrorMessage = "Either Email OR (Date of Birth + Phone) is required", CurrentValue = null });
            }

            // Email format validation
            if (!string.IsNullOrWhiteSpace(tempMember.Email))
            {
                var emailValidator = new System.ComponentModel.DataAnnotations.EmailAddressAttribute();
                if (!emailValidator.IsValid(tempMember.Email))
                {
                    errors.Add(new TempMemberValidationError { FieldName = "Email", ErrorMessage = "Email format is invalid", CurrentValue = tempMember.Email });
                }
            }

            // Date of birth validation
            if (tempMember.DateOfBirth.HasValue)
            {
                if (tempMember.DateOfBirth.Value > DateTime.Today)
                {
                    errors.Add(new TempMemberValidationError { FieldName = "DateOfBirth", ErrorMessage = "Date of Birth cannot be in the future", CurrentValue = tempMember.DateOfBirth?.ToString("yyyy-MM-dd") });
                }
                if (tempMember.DateOfBirth.Value < DateTime.Today.AddYears(-120))
                {
                    errors.Add(new TempMemberValidationError { FieldName = "DateOfBirth", ErrorMessage = "Date of Birth is too far in the past", CurrentValue = tempMember.DateOfBirth?.ToString("yyyy-MM-dd") });
                }
            }

            // Lookup validation - ensure IDs are resolved
            if (!tempMember.GenderId.HasValue && !string.IsNullOrWhiteSpace(tempMember.GenderText))
            {
                errors.Add(new TempMemberValidationError { FieldName = "Gender", ErrorMessage = "Gender value could not be resolved", CurrentValue = tempMember.GenderText });
            }

            if (!tempMember.ProvinceId.HasValue && !string.IsNullOrWhiteSpace(tempMember.ProvinceText))
            {
                errors.Add(new TempMemberValidationError { FieldName = "Province", ErrorMessage = "Province value could not be resolved", CurrentValue = tempMember.ProvinceText });
            }

            if (!tempMember.PhoneTypeId.HasValue && !string.IsNullOrWhiteSpace(tempMember.PhoneTypeText))
            {
                errors.Add(new TempMemberValidationError { FieldName = "PhoneType", ErrorMessage = "Phone type value could not be resolved", CurrentValue = tempMember.PhoneTypeText });
            }

            if (!tempMember.RegistrationTypeId.HasValue && !string.IsNullOrWhiteSpace(tempMember.RegistrationTypeText))
            {
                errors.Add(new TempMemberValidationError { FieldName = "RegistrationType", ErrorMessage = "Registration type value could not be resolved", CurrentValue = tempMember.RegistrationTypeText });
            }

            return errors;
        }

        /// <summary>
        /// Resolves lookup values for temp member (gender, province, etc.)
        /// </summary>
        public async Task<TempMember> ResolveLookupValuesAsync(Guid tempMemberId)
        {
            var tempMember = await GetByIdAsync(tempMemberId);
            if (tempMember == null)
                throw new ArgumentException($"Temp member {tempMemberId} not found");

            // Resolve Gender
            if (!tempMember.GenderId.HasValue && !string.IsNullOrWhiteSpace(tempMember.GenderText))
            {
                var gender = await _context.Genders
                    .FirstOrDefaultAsync(g => g.DisplayNameKey.ToLower().Contains(tempMember.GenderText.ToLower()) ||
                                             tempMember.GenderText.ToLower().Contains(g.DisplayNameKey.ToLower()));
                
                if (gender != null)
                {
                    tempMember.GenderId = gender.Id;
                }
                else
                {
                    // Try mapping common variations
                    var genderText = tempMember.GenderText.ToLower();
                    if (genderText.Contains("garçon") || genderText.Contains("homme") || genderText.Contains("male"))
                    {
                        var maleGender = await _context.Genders.FirstOrDefaultAsync(g => g.DisplayNameKey.ToLower().Contains("male") || g.DisplayNameKey.ToLower().Contains("homme"));
                        if (maleGender != null) tempMember.GenderId = maleGender.Id;
                    }
                    else if (genderText.Contains("fille") || genderText.Contains("femme") || genderText.Contains("female"))
                    {
                        var femaleGender = await _context.Genders.FirstOrDefaultAsync(g => g.DisplayNameKey.ToLower().Contains("female") || g.DisplayNameKey.ToLower().Contains("femme"));
                        if (femaleGender != null) tempMember.GenderId = femaleGender.Id;
                    }
                }
            }

            // Resolve Province
            if (!tempMember.ProvinceId.HasValue && !string.IsNullOrWhiteSpace(tempMember.ProvinceText))
            {
                var province = await _context.Provinces
                    .FirstOrDefaultAsync(p => p.Code.ToLower() == tempMember.ProvinceText.ToLower() ||
                                             p.DisplayNameKey.ToLower().Contains(tempMember.ProvinceText.ToLower()));
                
                if (province != null)
                {
                    tempMember.ProvinceId = province.Id;
                }
            }

            // Resolve PhoneType - default to Mobile if not specified
            if (!tempMember.PhoneTypeId.HasValue)
            {
                var phoneType = await _context.PhoneTypes
                    .FirstOrDefaultAsync(pt => pt.DisplayNameKey.ToLower().Contains("mobile") || pt.DisplayNameKey.ToLower().Contains("cell"));
                
                if (phoneType != null)
                {
                    tempMember.PhoneTypeId = phoneType.Id;
                }
            }

            // Resolve RegistrationType based on age if date of birth is available
            if (!tempMember.RegistrationTypeId.HasValue && tempMember.DateOfBirth.HasValue)
            {
                var age = DateTime.Now.Year - tempMember.DateOfBirth.Value.Year;
                if (DateTime.Now.DayOfYear < tempMember.DateOfBirth.Value.DayOfYear) age--;

                // This would need to be customized based on actual registration type logic
                var registrationType = await _context.RegistrationTypes
                    .FirstOrDefaultAsync(rt => rt.DisplayNameKey.ToLower().Contains(age < 18 ? "junior" : "adult"));
                
                if (registrationType != null)
                {
                    tempMember.RegistrationTypeId = registrationType.Id;
                }
            }

            await _context.SaveChangesAsync();
            return tempMember;
        }

        /// <summary>
        /// Helper method for creating a single member from temp data (used in bulk operations)
        /// </summary>
        private async Task<Member> CreateSingleMemberFromTempAsync(Guid tempMemberId, string createdBy)
        {
            // Get temp member
            var tempMember = await _context.TempMembers
                .FirstOrDefaultAsync(tm => tm.TempMemberId == tempMemberId);

            if (tempMember == null)
                throw new ArgumentException($"Temp member {tempMemberId} not found");

            // Resolve lookup values if needed
            if (!tempMember.GenderId.HasValue || !tempMember.ProvinceId.HasValue || 
                !tempMember.PhoneTypeId.HasValue || !tempMember.RegistrationTypeId.HasValue)
            {
                await ResolveLookupValuesInternalAsync(tempMember);
            }

            // Create member
            var member = new Member
            {
                FirstName = tempMember.FirstName,
                LastName = tempMember.LastName,
                DateOfBirth = tempMember.DateOfBirth ?? DateTime.Today.AddYears(-18),
                Email = tempMember.Email ?? "",
                Phone = tempMember.Phone ?? "",
                Address = tempMember.Address ?? "",
                City = tempMember.City ?? "",
                PostalCode = tempMember.PostalCode ?? "",
                GenderId = tempMember.GenderId!.Value,
                ProvinceId = tempMember.ProvinceId!.Value,
                PhoneTypeId = tempMember.PhoneTypeId!.Value,
                RegistrationTypeId = tempMember.RegistrationTypeId!.Value,
                DateCreated = DateTime.UtcNow,
                CreatedBySource = ActionSource.AdminPanel,
                IsActive = true
            };

            _context.Members.Add(member);

            // Update temp member status
            tempMember.Status = TempMemberStatus.Created;
            tempMember.DateModified = DateTime.UtcNow;
            tempMember.ModifiedBySource = ActionSource.AdminPanel;

            await _context.SaveChangesAsync();
            return member;
        }

        /// <summary>
        /// Internal helper for resolving lookup values without saving changes
        /// </summary>
        private async Task ResolveLookupValuesInternalAsync(TempMember tempMember)
        {
            // Same logic as ResolveLookupValuesAsync but without SaveChanges
            // (Implementation details same as above but without the final SaveChanges call)
            
            // Resolve Gender
            if (!tempMember.GenderId.HasValue && !string.IsNullOrWhiteSpace(tempMember.GenderText))
            {
                var gender = await _context.Genders
                    .FirstOrDefaultAsync(g => g.DisplayNameKey.ToLower().Contains(tempMember.GenderText.ToLower()));
                
                if (gender == null)
                {
                    var genderText = tempMember.GenderText.ToLower();
                    if (genderText.Contains("garçon") || genderText.Contains("homme") || genderText.Contains("male"))
                    {
                        gender = await _context.Genders.FirstOrDefaultAsync(g => g.DisplayNameKey.ToLower().Contains("male"));
                    }
                    else if (genderText.Contains("fille") || genderText.Contains("femme") || genderText.Contains("female"))
                    {
                        gender = await _context.Genders.FirstOrDefaultAsync(g => g.DisplayNameKey.ToLower().Contains("female"));
                    }
                }
                
                if (gender != null) tempMember.GenderId = gender.Id;
            }

            // Resolve Province
            if (!tempMember.ProvinceId.HasValue && !string.IsNullOrWhiteSpace(tempMember.ProvinceText))
            {
                var province = await _context.Provinces
                    .FirstOrDefaultAsync(p => p.Code.ToLower() == tempMember.ProvinceText.ToLower());
                if (province != null) tempMember.ProvinceId = province.Id;
            }

            // Resolve PhoneType - default to Mobile
            if (!tempMember.PhoneTypeId.HasValue)
            {
                var phoneType = await _context.PhoneTypes
                    .FirstOrDefaultAsync(pt => pt.DisplayNameKey.ToLower().Contains("mobile"));
                if (phoneType != null) tempMember.PhoneTypeId = phoneType.Id;
            }

            // Resolve RegistrationType based on age
            if (!tempMember.RegistrationTypeId.HasValue && tempMember.DateOfBirth.HasValue)
            {
                var age = DateTime.Now.Year - tempMember.DateOfBirth.Value.Year;
                if (DateTime.Now.DayOfYear < tempMember.DateOfBirth.Value.DayOfYear) age--;

                var registrationType = await _context.RegistrationTypes
                    .FirstOrDefaultAsync();
                if (registrationType != null) tempMember.RegistrationTypeId = registrationType.Id;
            }
        }
    }
}