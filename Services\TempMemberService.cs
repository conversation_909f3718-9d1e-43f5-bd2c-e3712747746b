using Microsoft.EntityFrameworkCore;
using ParaHockeyApp.Models;
using ParaHockeyApp.Models.Entities;
using System.Text.Json;

namespace ParaHockeyApp.Services
{
    /// <summary>
    /// Service for managing temporary member records during import process
    /// </summary>
    public class TempMemberService : ITempMemberService
    {
        private readonly ApplicationContext _context;
        private readonly INormalizationService _normalizationService;
        private readonly IDuplicateDetectionService _duplicateDetectionService;
        private readonly IAuditLogService _auditLogService;

        public TempMemberService(
            ApplicationContext context,
            INormalizationService normalizationService,
            IDuplicateDetectionService duplicateDetectionService,
            IAuditLogService auditLogService)
        {
            _context = context;
            _normalizationService = normalizationService;
            _duplicateDetectionService = duplicateDetectionService;
            _auditLogService = auditLogService;
        }

        /// <summary>
        /// Gets temp member by ID with related data
        /// </summary>
        public async Task<TempMember?> GetByIdAsync(Guid tempMemberId)
        {
            return await _context.TempMembers
                .Include(tm => tm.ImportBatch)
                .Include(tm => tm.ExistingMember)
                .Include(tm => tm.Gender)
                .Include(tm => tm.Province)
                .Include(tm => tm.PhoneType)
                .Include(tm => tm.RegistrationType)
                .FirstOrDefaultAsync(tm => tm.TempMemberId == tempMemberId);
        }

        /// <summary>
        /// Creates a new member from temp member data
        /// </summary>
        public async Task<Member> CreateFromTempAsync(Guid tempMemberId, string createdBy)
        {
            using var transaction = await _context.Database.BeginTransactionAsync();

            try
            {
                var tempMember = await GetByIdAsync(tempMemberId);
                if (tempMember == null)
                    throw new ArgumentException("Temp member not found", nameof(tempMemberId));

                if (tempMember.Status != TempMemberStatus.ReadyToCreate && tempMember.Status != TempMemberStatus.NeedsFix)
                    throw new InvalidOperationException($"Cannot create member from temp record with status: {tempMember.Status}");

                // Resolve lookup fields if not already resolved
                await ResolveLookupFieldsAsync(tempMember);

                // Validate temp member data
                var validationErrors = await ValidateAsync(tempMember);
                if (validationErrors.Any())
                    throw new InvalidOperationException($"Validation failed: {string.Join(", ", validationErrors)}");

                // Create new member
                var member = new Member
                {
                    FirstName = tempMember.FirstName,
                    LastName = tempMember.LastName,
                    DateOfBirth = tempMember.DateOfBirth ?? throw new InvalidOperationException("DateOfBirth is required"),
                    Email = tempMember.Email ?? throw new InvalidOperationException("Email is required"),
                    Phone = tempMember.Phone ?? "",
                    Address = tempMember.Address ?? "",
                    City = tempMember.City ?? "",
                    PostalCode = tempMember.PostalCode ?? "",
                    GenderId = tempMember.GenderId ?? throw new InvalidOperationException("GenderId is required"),
                    ProvinceId = tempMember.ProvinceId ?? throw new InvalidOperationException("ProvinceId is required"),
                    PhoneTypeId = tempMember.PhoneTypeId ?? throw new InvalidOperationException("PhoneTypeId is required"),
                    RegistrationTypeId = tempMember.RegistrationTypeId ?? throw new InvalidOperationException("RegistrationTypeId is required")
                };

                _context.Members.Add(member);
                await _context.SaveChangesAsync();

                // Update temp member status
                tempMember.Status = TempMemberStatus.Created;
                await _context.SaveChangesAsync();

                // Log the creation
                await _auditLogService.LogActionAsync(
                    $"Member created from import data (TempMember: {tempMemberId})",
                    member,
                    ActionSource.AdminPanel,
                    createdBy,
                    null);

                await transaction.CommitAsync();
                return member;
            }
            catch
            {
                await transaction.RollbackAsync();
                throw;
            }
        }

        /// <summary>
        /// Bulk creates members from multiple temp member records
        /// </summary>
        public async Task<List<Member>> BulkCreateFromTempAsync(List<Guid> tempMemberIds, string createdBy)
        {
            var createdMembers = new List<Member>();

            using var transaction = await _context.Database.BeginTransactionAsync();

            try
            {
                foreach (var tempMemberId in tempMemberIds)
                {
                    var member = await CreateFromTempAsync(tempMemberId, createdBy);
                    createdMembers.Add(member);
                }

                await transaction.CommitAsync();
                return createdMembers;
            }
            catch
            {
                await transaction.RollbackAsync();
                throw;
            }
        }

        /// <summary>
        /// Updates temp member status
        /// </summary>
        public async Task UpdateStatusAsync(Guid tempMemberId, TempMemberStatus status)
        {
            var tempMember = await _context.TempMembers
                .FirstOrDefaultAsync(tm => tm.TempMemberId == tempMemberId);

            if (tempMember != null)
            {
                tempMember.Status = status;
                await _context.SaveChangesAsync();
            }
        }

        /// <summary>
        /// Updates temp member data (for fixing validation errors)
        /// </summary>
        public async Task UpdateAsync(TempMember tempMember)
        {
            if (tempMember == null)
                throw new ArgumentNullException(nameof(tempMember));

            // Normalize data
            _normalizationService.NormalizeTempMember(tempMember);

            // Resolve lookup fields
            await ResolveLookupFieldsAsync(tempMember);

            // Reprocess for duplicates if data changed
            await ReprocessForDuplicatesAsync(tempMember.TempMemberId);

            _context.TempMembers.Update(tempMember);
            await _context.SaveChangesAsync();
        }

        /// <summary>
        /// Deletes temp member record
        /// </summary>
        public async Task DeleteAsync(Guid tempMemberId)
        {
            var tempMember = await _context.TempMembers
                .FirstOrDefaultAsync(tm => tm.TempMemberId == tempMemberId);

            if (tempMember != null)
            {
                _context.TempMembers.Remove(tempMember);
                await _context.SaveChangesAsync();
            }
        }

        /// <summary>
        /// Resolves lookup field IDs from text values
        /// </summary>
        public async Task ResolveLookupFieldsAsync(TempMember tempMember)
        {
            // Resolve Gender
            if (!string.IsNullOrEmpty(tempMember.GenderText) && tempMember.GenderId == null)
            {
                var gender = await _context.Genders
                    .FirstOrDefaultAsync(g => g.DisplayNameKey.Contains("Male") && tempMember.GenderText == "Male" ||
                                             g.DisplayNameKey.Contains("Female") && tempMember.GenderText == "Female" ||
                                             g.DisplayNameKey.Contains("Other") && tempMember.GenderText == "Other");
                tempMember.GenderId = gender?.Id;
            }

            // Resolve Province
            if (!string.IsNullOrEmpty(tempMember.ProvinceText) && tempMember.ProvinceId == null)
            {
                var province = await _context.Provinces
                    .FirstOrDefaultAsync(p => p.Code == tempMember.ProvinceText);
                tempMember.ProvinceId = province?.Id;
            }

            // Resolve PhoneType (default to Mobile if phone exists)
            if (!string.IsNullOrEmpty(tempMember.Phone) && tempMember.PhoneTypeId == null)
            {
                var phoneType = await _context.PhoneTypes
                    .FirstOrDefaultAsync(pt => pt.DisplayNameKey.Contains("Mobile"));
                tempMember.PhoneTypeId = phoneType?.Id;
            }

            // Resolve RegistrationType based on age
            if (tempMember.DateOfBirth.HasValue && tempMember.RegistrationTypeId == null)
            {
                tempMember.RegistrationTypeId = await DetermineRegistrationTypeAsync(tempMember.DateOfBirth.Value);
            }
        }

        /// <summary>
        /// Determines registration type based on age and business rules
        /// </summary>
        public async Task<int> DetermineRegistrationTypeAsync(DateTime dateOfBirth)
        {
            var age = DateTime.Now.Year - dateOfBirth.Year;
            if (DateTime.Now.DayOfYear < dateOfBirth.DayOfYear)
                age--;

            // Age-based logic (similar to existing member registration)
            if (age < 18)
            {
                // Junior registration
                var juniorType = await _context.RegistrationTypes
                    .FirstOrDefaultAsync(rt => rt.DisplayNameKey.Contains("Junior"));
                return juniorType?.Id ?? 1; // Default to ID 1 if not found
            }
            else
            {
                // Adult registration (Development is default for adults)
                var developmentType = await _context.RegistrationTypes
                    .FirstOrDefaultAsync(rt => rt.DisplayNameKey.Contains("Development"));
                return developmentType?.Id ?? 2; // Default to ID 2 if not found
            }
        }

        /// <summary>
        /// Validates temp member data and returns validation errors
        /// </summary>
        public async Task<List<string>> ValidateAsync(TempMember tempMember)
        {
            var errors = new List<string>();

            // Required fields
            if (string.IsNullOrEmpty(tempMember.FirstName))
                errors.Add("FirstName is required");

            if (string.IsNullOrEmpty(tempMember.LastName))
                errors.Add("LastName is required");

            if (string.IsNullOrEmpty(tempMember.Email))
                errors.Add("Email is required");

            if (!tempMember.DateOfBirth.HasValue)
                errors.Add("DateOfBirth is required");

            // Lookup field validation
            if (tempMember.GenderId == null)
                errors.Add("Gender must be resolved");

            if (tempMember.ProvinceId == null)
                errors.Add("Province must be resolved");

            if (tempMember.PhoneTypeId == null)
                errors.Add("PhoneType must be resolved");

            if (tempMember.RegistrationTypeId == null)
                errors.Add("RegistrationType must be resolved");

            // Email format validation
            if (!string.IsNullOrEmpty(tempMember.Email))
            {
                var normalizedEmail = _normalizationService.NormalizeEmail(tempMember.Email);
                if (string.IsNullOrEmpty(normalizedEmail))
                    errors.Add("Email format is invalid");
            }

            return errors;
        }

        /// <summary>
        /// Gets count of temp members by status for a batch
        /// </summary>
        public async Task<Dictionary<TempMemberStatus, int>> GetStatusCountsAsync(int batchId)
        {
            var counts = await _context.TempMembers
                .Where(tm => tm.ImportBatchId == batchId)
                .GroupBy(tm => tm.Status)
                .Select(g => new { Status = g.Key, Count = g.Count() })
                .ToListAsync();

            var result = new Dictionary<TempMemberStatus, int>();
            foreach (var statusValue in Enum.GetValues<TempMemberStatus>())
            {
                result[statusValue] = counts.FirstOrDefault(c => c.Status == statusValue)?.Count ?? 0;
            }

            return result;
        }

        /// <summary>
        /// Reprocesses temp member for duplicate detection after data changes
        /// </summary>
        public async Task ReprocessForDuplicatesAsync(Guid tempMemberId)
        {
            var tempMember = await GetByIdAsync(tempMemberId);
            if (tempMember == null)
                return;

            // Clear existing duplicate info
            tempMember.ExistingMemberId = null;

            // Re-check for duplicates
            var existingMember = await _duplicateDetectionService.FindExistingMemberAsync(tempMember);
            if (existingMember != null)
            {
                tempMember.Status = TempMemberStatus.Duplicate;
                tempMember.ExistingMemberId = existingMember.Id;
            }
            else
            {
                // Validate to determine if ready to create or needs fix
                var validationErrors = await ValidateAsync(tempMember);
                tempMember.Status = validationErrors.Any() ? TempMemberStatus.NeedsFix : TempMemberStatus.ReadyToCreate;
            }

            await _context.SaveChangesAsync();
        }
    }
}