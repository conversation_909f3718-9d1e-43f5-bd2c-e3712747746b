using ParaHockeyApp.Models.ViewModels;

namespace ParaHockeyApp.Services
{
    /// <summary>
    /// Service interface for managing empty state messages across the application.
    /// Provides localized empty state messages for different entity types and search scenarios.
    /// </summary>
    public interface IEmptyStateService
    {
        /// <summary>
        /// Gets a simple empty state message for the specified entity type.
        /// </summary>
        /// <param name="entityType">The type of entity (e.g., "members", "events", "admin")</param>
        /// <param name="searchTerm">Optional search term that was used</param>
        /// <returns>Localized empty state message</returns>
        string GetEmptyMessage(string entityType, string searchTerm = null);

        /// <summary>
        /// Gets a complete empty state view model with title, message, icon, and optional action.
        /// </summary>
        /// <param name="entityType">The type of entity (e.g., "members", "events", "admin")</param>
        /// <param name="searchTerm">Optional search term that was used</param>
        /// <param name="actionUrl">Optional URL for an action button</param>
        /// <param name="actionText">Optional text for the action button</param>
        /// <returns>Complete empty state view model</returns>
        EmptyStateViewModel GetEmptyState(string entityType, string searchTerm = null, string actionUrl = null, string actionText = null);

        /// <summary>
        /// Gets an empty state for search-specific scenarios with helpful suggestions.
        /// </summary>
        /// <param name="entityType">The type of entity that was searched</param>
        /// <param name="searchTerm">The search term that returned no results</param>
        /// <param name="suggestions">Optional list of search suggestions</param>
        /// <returns>Search-specific empty state view model</returns>
        EmptyStateViewModel GetSearchEmptyState(string entityType, string searchTerm, string[] suggestions = null);
        
        /// <summary>
        /// Gets an empty state with actionable suggestions based on the context.
        /// </summary>
        /// <param name="entityType">The type of entity</param>
        /// <param name="context">Additional context information to customize suggestions</param>
        /// <param name="actionUrl">Optional URL for primary action</param>
        /// <returns>Empty state view model with actionable suggestions</returns>
        EmptyStateViewModel GetActionableEmptyState(string entityType, string context = null, string actionUrl = null);
        
        /// <summary>
        /// Gets an empty state for error scenarios with appropriate messaging.
        /// </summary>
        /// <param name="entityType">The type of entity that was being accessed</param>
        /// <param name="errorType">Type of error (e.g., "database", "network", "permission")</param>
        /// <param name="isRetryable">Whether the operation can be retried</param>
        /// <returns>Error-specific empty state view model</returns>
        EmptyStateViewModel GetErrorEmptyState(string entityType, string errorType, bool isRetryable = false);
    }
}