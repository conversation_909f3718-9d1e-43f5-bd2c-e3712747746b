using ParaHockeyApp.Models.Entities;

namespace ParaHockeyApp.Services
{
    /// <summary>
    /// Service interface for comprehensive security auditing and enhancement
    /// Provides automated scanning and validation of security best practices
    /// </summary>
    public interface ISecurityAuditService
    {
        /// <summary>
        /// Performs a comprehensive security audit of a specific page
        /// </summary>
        /// <param name="pageName">Name of the page to audit (e.g., "Home/Index")</param>
        /// <returns>Detailed security audit results</returns>
        Task<SecurityAuditResult> AuditPageSecurityAsync(string pageName);

        /// <summary>
        /// Scans all forms in the application for anti-forgery token usage
        /// </summary>
        /// <returns>List of forms and their CSRF protection status</returns>
        Task<List<AntiForgeryAuditResult>> ScanAntiForgeryTokensAsync();

        /// <summary>
        /// Analyzes all controller actions for proper authorization attributes
        /// </summary>
        /// <returns>List of actions and their authorization status</returns>
        Task<List<AuthorizationAuditResult>> AnalyzeAuthorizationAsync();
        
        /// <summary>
        /// Scans CSRF protection across all controller actions
        /// </summary>
        /// <returns>List of actions and their CSRF protection status</returns>
        Task<List<CsrfProtectionAuditResult>> ScanCsrfProtectionAsync();

        /// <summary>
        /// Scans for potential over-posting vulnerabilities in model binding
        /// </summary>
        /// <returns>List of potential over-posting vulnerabilities</returns>
        Task<List<OverPostingAuditResult>> ScanOverPostingVulnerabilitiesAsync();

        /// <summary>
        /// Validates proper HTML encoding in Razor views
        /// </summary>
        /// <returns>List of potential XSS vulnerabilities</returns>
        Task<List<OutputEncodingAuditResult>> ValidateOutputEncodingAsync();

        /// <summary>
        /// Checks cookie security configuration
        /// </summary>
        /// <returns>Cookie security configuration audit results</returns>
        Task<CookieSecurityAuditResult> CheckCookieSecurityAsync();

        /// <summary>
        /// Generates a comprehensive security report for all pages
        /// </summary>
        /// <returns>Overall security assessment</returns>
        Task<SecuritySummaryReport> GenerateSecurityReportAsync();

        /// <summary>
        /// Automatically fixes common security issues where possible
        /// </summary>
        /// <param name="pageName">Page to fix, or null for all pages</param>
        /// <param name="fixTypes">Types of fixes to apply</param>
        /// <returns>Results of the automated fixes</returns>
        Task<SecurityFixResult> AutoFixSecurityIssuesAsync(string? pageName = null, SecurityFixType fixTypes = SecurityFixType.All);
    }

    /// <summary>
    /// Comprehensive security audit result for a page
    /// </summary>
    public class SecurityAuditResult
    {
        public string PageName { get; set; } = string.Empty;
        public bool HasAntiForgeryTokens { get; set; }
        public bool HasProperAuthorization { get; set; }
        public bool UsesViewModels { get; set; }
        public bool HasServerSideValidation { get; set; }
        public bool HasProperOutputEncoding { get; set; }
        public bool HasSecureCookies { get; set; }
        public bool HasCSPHeaders { get; set; }
        public int SecurityScore { get; set; }
        public List<SecurityIssue> Issues { get; set; } = new();
        public List<SecurityRecommendation> Recommendations { get; set; } = new();
        public DateTime AuditDate { get; set; } = DateTime.UtcNow;
    }

    /// <summary>
    /// Anti-forgery token audit result
    /// </summary>
    public class AntiForgeryAuditResult
    {
        public string FormLocation { get; set; } = string.Empty;
        public string ViewPath { get; set; } = string.Empty;
        public string ActionMethod { get; set; } = string.Empty;
        public bool HasAntiForgeryToken { get; set; }
        public bool HasValidateAntiForgeryAttribute { get; set; }
        public SecurityRiskLevel RiskLevel { get; set; }
        public string Recommendation { get; set; } = string.Empty;
    }

    /// <summary>
    /// Authorization audit result
    /// </summary>
    public class AuthorizationAuditResult
    {
        public string ControllerName { get; set; } = string.Empty;
        public string ActionName { get; set; } = string.Empty;
        public string FullActionPath { get; set; } = string.Empty;
        public bool HasAuthorizeAttribute { get; set; }
        public bool HasAllowAnonymousAttribute { get; set; }
        public string[] RequiredRoles { get; set; } = Array.Empty<string>();
        public string[] RequiredPolicies { get; set; } = Array.Empty<string>();
        public SecurityRiskLevel RiskLevel { get; set; }
        public string Recommendation { get; set; } = string.Empty;
        public bool IsPublicEndpoint { get; set; }
    }
    
    /// <summary>
    /// CSRF protection audit result
    /// </summary>
    public class CsrfProtectionAuditResult
    {
        public string ControllerName { get; set; } = string.Empty;
        public string ActionName { get; set; } = string.Empty;
        public bool IsProtected { get; set; }
        public SecurityRiskLevel RiskLevel { get; set; }
        public List<string> ProtectionMethods { get; set; } = new();
        public string Recommendation { get; set; } = string.Empty;
    }

    /// <summary>
    /// Over-posting vulnerability audit result
    /// </summary>
    public class OverPostingAuditResult
    {
        public string ControllerName { get; set; } = string.Empty;
        public string ActionName { get; set; } = string.Empty;
        public string ParameterName { get; set; } = string.Empty;
        public string ParameterType { get; set; } = string.Empty;
        public bool UsesBindAttribute { get; set; }
        public bool UsesViewModel { get; set; }
        public SecurityRiskLevel RiskLevel { get; set; }
        public string Vulnerability { get; set; } = string.Empty;
        public string Recommendation { get; set; } = string.Empty;
    }

    /// <summary>
    /// Output encoding audit result
    /// </summary>
    public class OutputEncodingAuditResult
    {
        public string ViewPath { get; set; } = string.Empty;
        public int LineNumber { get; set; }
        public string CodeSnippet { get; set; } = string.Empty;
        public bool UsesRawOutput { get; set; }
        public bool HasProperEncoding { get; set; }
        public SecurityRiskLevel RiskLevel { get; set; }
        public string Vulnerability { get; set; } = string.Empty;
        public string Recommendation { get; set; } = string.Empty;
    }

    /// <summary>
    /// Cookie security audit result
    /// </summary>
    public class CookieSecurityAuditResult
    {
        public bool HasHttpOnlyFlag { get; set; }
        public bool HasSecureFlag { get; set; }
        public string SameSitePolicy { get; set; } = string.Empty;
        public bool HasProperExpiration { get; set; }
        public bool UsesSecureDefaults { get; set; }
        public int SecurityScore { get; set; }
        public List<CookieSecurityIssue> Issues { get; set; } = new();
        public List<string> Recommendations { get; set; } = new();
    }

    /// <summary>
    /// Security summary report
    /// </summary>
    public class SecuritySummaryReport
    {
        public int TotalPagesAudited { get; set; }
        public int SecurePagesCount { get; set; }
        public int VulnerablePagesCount { get; set; }
        public double AverageSecurityScore { get; set; }
        public int CriticalIssuesCount { get; set; }
        public int HighRiskIssuesCount { get; set; }
        public int MediumRiskIssuesCount { get; set; }
        public int LowRiskIssuesCount { get; set; }
        public List<string> TopVulnerabilities { get; set; } = new();
        public List<string> RecommendedActions { get; set; } = new();
        public DateTime GeneratedAt { get; set; } = DateTime.UtcNow;
    }

    /// <summary>
    /// Security fix result
    /// </summary>
    public class SecurityFixResult
    {
        public int IssuesFixed { get; set; }
        public int IssuesRemaining { get; set; }
        public List<string> FixesApplied { get; set; } = new();
        public List<string> ManualFixesRequired { get; set; } = new();
        public bool Success { get; set; }
        public string Message { get; set; } = string.Empty;
    }

    /// <summary>
    /// Individual security issue
    /// </summary>
    public class SecurityIssue
    {
        public string Title { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public SecurityRiskLevel RiskLevel { get; set; }
        public string Location { get; set; } = string.Empty;
        public string Impact { get; set; } = string.Empty;
        public bool CanAutoFix { get; set; }
    }

    /// <summary>
    /// Security recommendation
    /// </summary>
    public class SecurityRecommendation
    {
        public string Title { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public string Implementation { get; set; } = string.Empty;
        public int Priority { get; set; }
        public string[] References { get; set; } = Array.Empty<string>();
    }

    /// <summary>
    /// Cookie security issue
    /// </summary>
    public class CookieSecurityIssue
    {
        public string CookieName { get; set; } = string.Empty;
        public string Issue { get; set; } = string.Empty;
        public SecurityRiskLevel RiskLevel { get; set; }
        public string Recommendation { get; set; } = string.Empty;
    }

    /// <summary>
    /// Security risk levels
    /// </summary>
    public enum SecurityRiskLevel
    {
        Low = 1,
        Medium = 2,
        High = 3,
        Critical = 4
    }

    /// <summary>
    /// Types of security fixes that can be automatically applied
    /// </summary>
    [Flags]
    public enum SecurityFixType
    {
        None = 0,
        AntiForgeryTokens = 1,
        Authorization = 2,
        OutputEncoding = 4,
        CookieSettings = 8,
        All = AntiForgeryTokens | Authorization | OutputEncoding | CookieSettings
    }
}