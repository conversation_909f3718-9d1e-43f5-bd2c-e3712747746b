# CRITICAL TASK WORKFLOW - NEVER FORGET THIS

## MANDATORY WORKFLOW FOR ALL TASKS

**THIS IS THE CORRECT WORKFLOW - FOLLOW IT EXACTLY:**

1. **Complete task** - Implement the requested functionality
2. **Commit and push** - Commit changes and push to current branch
3. **Show what to test** - Provide clear testing instructions
4. **Suggest merge to main if tests pass** - Recommend merging after successful tests
5. **WAIT for you to merge** - DO NOT proceed until user confirms merge
6. **WAIT for you to ask for next task** - DO NOT automatically start next task
   - User will create new branch for next task
   - User will tell me which task to work on next

## CRITICAL RULES

- **NEVER** automatically proceed to the next task
- **NEVER** assume the user wants to continue immediately
- **ALWAYS** wait for explicit confirmation before moving forward
- **ALWAYS** let the user control the workflow timing
- **ALWAYS** wait for the user to create new branches
- **ALWAYS** wait for the user to specify the next task

## WHAT NOT TO DO

- ❌ Don't say "Now let's move to the next task"
- ❌ Don't automatically start implementing the next task
- ❌ Don't assume the workflow should continue
- ❌ Don't create branches or manage git workflow

## WHAT TO DO

- ✅ Complete the current task fully
- ✅ Commit and push changes
- ✅ Provide testing instructions
- ✅ Suggest merge if tests pass
- ✅ STOP and WAIT for user direction
- ✅ Let user control the pace and timing

**REMEMBER: The user controls the workflow timing and branch management!**