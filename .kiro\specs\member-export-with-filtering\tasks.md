# Implementation Plan

-   [x] 1. Create export DTOs and enums

    -   Create MemberExportRequest DTO extending MemberSearchRequest with export-specific properties
    -   Create ExportFormat enum with CSV and Excel options
    -   Create FilterOptionsResponse and FilterOption DTOs for dynamic filter data
    -   _Requirements: 1.2, 3.2, 3.3_

-   [x] 2. Implement MemberExportService interface and service

    -   Create IMemberExportService interface with export and filter methods
    -   Implement MemberExportService with CSV export functionality using CsvHelper library
    -   Implement Excel export functionality using EPPlus library
    -   Create intelligent filename generation based on applied filters and search terms
    -   _Requirements: 1.1, 1.2, 2.1, 2.2, 2.3, 2.4_

-   [x] 3. Enhance MemberSearchService with filter option methods

    -   Add GetDistinctCitiesAsync method with optional search term filtering
    -   Add GetDistinctProvincesAsync method for province filter options
    -   Add GetRegistrationTypeCountsAsync method for registration type filter with counts
    -   Implement efficient database queries with proper indexing considerations
    -   _Requirements: 3.2, 3.3, 3.6, 3.7_

-   [x] 4. Add export controller actions to AdminController

    -   Create ExportMembersAdvanced action that handles both CSV and Excel export requests
    -   Create GetFilterOptions AJAX endpoint for dynamic filter option loading
    -   Implement proper error handling and validation for export requests
    -   Add audit logging for export activities
    -   _Requirements: 1.1, 1.5, 3.1, 4.4_

-   [x] 5. Create export UI components in Members.cshtml

    -   Add export button with Bootstrap modal for format selection
    -   Create filter dropdown system with primary and secondary dropdowns
    -   Implement dynamic filter option loading with AJAX calls
    -   Add city search functionality with type-ahead capability
    -   _Requirements: 1.1, 3.1, 3.2, 3.3, 3.6, 3.7_

-   [-] 6. Implement JavaScript functionality for filtering and export

    -   Create filter dropdown change handlers for dynamic option loading
    -   Implement city search with debounced AJAX calls
    -   Add export modal functionality with format selection
    -   Create filter application logic that integrates with existing search
    -   _Requirements: 3.4, 3.5, 5.1, 5.2, 5.3_

-   [ ] 7. Add required NuGet packages and dependencies

    -   Add CsvHelper package for CSV export functionality
    -   Add EPPlus package for Excel export functionality
    -   Update project dependencies and ensure compatibility
    -   _Requirements: 4.1, 4.2, 4.3_

-   [ ] 8. Register services in Program.cs

    -   Register IMemberExportService and MemberExportService in DI container
    -   Configure service lifetimes appropriately
    -   Ensure proper dependency injection setup
    -   _Requirements: 1.1, 4.4_

-   [ ] 9. Add localization keys for new UI elements

    -   Add export-related localization keys to resource files
    -   Add filter-related localization keys for dropdown labels
    -   Add error message keys for export validation
    -   Ensure French and English translations are complete
    -   _Requirements: 1.1, 3.1, 4.4_

-   [ ] 10. Create unit tests for export functionality

    -   Write unit tests for MemberExportService CSV export methods
    -   Write unit tests for MemberExportService Excel export methods
    -   Write unit tests for filename generation with various filter combinations
    -   Write unit tests for filter option retrieval methods
    -   _Requirements: 1.1, 1.2, 2.1, 2.2, 2.3, 3.2, 3.3_

-   [ ] 11. Integrate export functionality with existing member search

    -   Ensure export respects current search results and applied filters
    -   Test export with various search and filter combinations
    -   Verify pagination doesn't affect export (exports all matching results)
    -   Test filename generation reflects current search/filter state
    -   _Requirements: 1.3, 1.4, 2.2, 2.3, 2.4, 5.1, 5.4, 5.5_

-   [ ] 12. Add error handling and validation
    -   Implement comprehensive error handling for export operations
    -   Add validation for export request parameters
    -   Create user-friendly error messages for export failures
    -   Add logging for export operations and errors
    -   _Requirements: 4.4, 1.5_
