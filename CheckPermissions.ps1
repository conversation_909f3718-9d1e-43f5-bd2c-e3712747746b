# Check ParaHockeyUser permissions in Production
Write-Host "=== Checking ParaHockeyUser Permissions ===" -ForegroundColor Cyan

try {
    $connStr = "Server=SIMBA\SQLEXPRESS;Database=ParaHockeyDB;User Id=ParaHockeyUser;Password=***************;TrustServerCertificate=True;"
    $conn = New-Object System.Data.SqlClient.SqlConnection($connStr)
    $conn.Open()
    
    Write-Host "Connected to Production DB" -ForegroundColor Green
    
    # Check role membership
    $cmd = $conn.CreateCommand()
    $cmd.CommandText = @"
SELECT 
    dp.name AS UserName,
    dp2.name AS RoleName
FROM sys.database_role_members rm
JOIN sys.database_principals dp ON rm.member_principal_id = dp.principal_id
JOIN sys.database_principals dp2 ON rm.role_principal_id = dp2.principal_id
WHERE dp.name = 'ParaHockeyUser'
ORDER BY dp2.name
"@
    
    Write-Host "`nRole Membership:" -ForegroundColor Yellow
    $reader = $cmd.ExecuteReader()
    $hasRoles = $false
    while ($reader.Read()) {
        $hasRoles = $true
        Write-Host "  $($reader['UserName']) -> $($reader['RoleName'])"
    }
    if (-not $hasRoles) {
        Write-Host "  No roles assigned!" -ForegroundColor Red
    }
    $reader.Close()
    
    # Check if user can read Members table
    Write-Host "`nTesting table access:" -ForegroundColor Yellow
    try {
        $cmd.CommandText = "SELECT COUNT(*) FROM Members"
        $count = $cmd.ExecuteScalar()
        Write-Host "  Members table: $count records" -ForegroundColor Green
    } catch {
        Write-Host "  Members table: ACCESS DENIED - $_" -ForegroundColor Red
    }
    
    # Check if user can read Events table  
    try {
        $cmd.CommandText = "SELECT COUNT(*) FROM Events"
        $count = $cmd.ExecuteScalar()
        Write-Host "  Events table: $count records" -ForegroundColor Green
    } catch {
        Write-Host "  Events table: ACCESS DENIED - $_" -ForegroundColor Red
    }
    
    $conn.Close()
}
catch {
    Write-Host "Connection failed: $_" -ForegroundColor Red
}