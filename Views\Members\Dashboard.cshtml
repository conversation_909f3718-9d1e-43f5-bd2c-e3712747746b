@model ParaHockeyApp.Models.ViewModels.MemberDashboardViewModel
@using Microsoft.AspNetCore.Mvc.Localization
@inject IHtmlLocalizer<ParaHockeyApp.Resources.SharedResourceMarker> SharedLocalizer

@{
    ViewData["Title"] = $"My Dashboard - {Model.Member.FullName}";
}

<!-- CSRF Token for AJAX requests -->
@Html.AntiForgeryToken()

<!-- Skip navigation links for accessibility -->
<a href="#main-content" class="skip-link visually-hidden-focusable">@SharedLocalizer["SkipToMainContent"]</a>
<a href="#quick-actions" class="skip-link visually-hidden-focusable">@SharedLocalizer["SkipToQuickActions"]</a>
<a href="#personal-info" class="skip-link visually-hidden-focusable">@SharedLocalizer["SkipToPersonalInfo"]</a>
<a href="#event-subscriptions" class="skip-link visually-hidden-focusable">@SharedLocalizer["SkipToEventSubscriptions"]</a>

<main id="main-content" role="main" aria-label="@SharedLocalizer["MemberDashboard"]">
<div class="container-fluid mt-4">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col">
            <h1 class="h2 text-primary">
                <i class="fas fa-tachometer-alt"></i> @SharedLocalizer["MyDashboard"]
            </h1>
            <p class="text-muted">@SharedLocalizer["Welcome"], @Model.Member.FullName!</p>
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item">@SharedLocalizer["MemberPortal"]</li>
                    <li class="breadcrumb-item active" aria-current="page">@SharedLocalizer["Dashboard"]</li>
                </ol>
            </nav>
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="row mb-4" id="quick-actions">
        <div class="col">
            <div class="card">
                <div class="card-header bg-info text-white">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-rocket"></i> @SharedLocalizer["QuickActions"]
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3 col-sm-6 mb-3">
                            <a href="@Url.Action("Subscribe", "Events")" class="btn btn-success btn-lg w-100 h-100 d-flex flex-column justify-content-center align-items-center">
                                <i class="fas fa-calendar-plus fa-2x mb-2"></i>
                                <span>@SharedLocalizer["SubscribeToEvents"]</span>
                            </a>
                        </div>
                        <div class="col-md-3 col-sm-6 mb-3">
                            <a href="@Url.Action("CalendarReadOnly", "Members")" class="btn btn-info btn-lg w-100 h-100 d-flex flex-column justify-content-center align-items-center">
                                <i class="fas fa-calendar fa-2x mb-2"></i>
                                <span>@SharedLocalizer["ViewCalendar"]</span>
                            </a>
                        </div>
                        <div class="col-md-3 col-sm-6 mb-3">
                            <a href="@Url.Action("Edit", "Members", new { memberId = Model.Member.Id })" class="btn btn-primary btn-lg w-100 h-100 d-flex flex-column justify-content-center align-items-center">
                                <i class="fas fa-edit fa-2x mb-2"></i>
                                <span>@SharedLocalizer["EditProfile"]</span>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Main Content -->
        <div class="col-lg-8">
            <!-- Personal Information Card -->
            <div class="card mb-4" id="personal-info">
                <div class="card-header bg-primary text-white">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-user"></i> @SharedLocalizer["PersonalInformation"]
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <table class="table table-borderless">
                                <tr>
                                    <td><strong>@SharedLocalizer["MemberID"]:</strong></td>
                                    <td>
                                        @Model.Member.Id
                                        @if (!string.IsNullOrEmpty(Model.Member.HQc_Id))
                                        {
                                            <br><small class="text-muted">HQc ID: @Model.Member.HQc_Id</small>
                                        }
                                    </td>
                                </tr>
                                <tr>
                                    <td><strong>@SharedLocalizer["FirstName"]:</strong></td>
                                    <td>@Model.Member.FirstName</td>
                                </tr>
                                <tr>
                                    <td><strong>@SharedLocalizer["LastName"]:</strong></td>
                                    <td>@Model.Member.LastName</td>
                                </tr>
                                <tr>
                                    <td><strong>@SharedLocalizer["DateOfBirth"]:</strong></td>
                                    <td>
                                        @Model.Member.FormattedDateOfBirth
                                        <span class="text-muted">(@Model.Member.Age @SharedLocalizer["YearsOld"])</span>
                                    </td>
                                </tr>
                            </table>
                        </div>
                        <div class="col-md-6">
                            <table class="table table-borderless">
                                <tr>
                                    <td><strong>@SharedLocalizer["Email"]:</strong></td>
                                    <td>@Model.Member.Email</td>
                                </tr>
                                <tr>
                                    <td><strong>@SharedLocalizer["Phone"]:</strong></td>
                                    <td>@Model.Member.FormattedPhone</td>
                                </tr>
                                <tr>
                                    <td><strong>@SharedLocalizer["RegistrationType"]:</strong></td>
                                    <td>
                                        @if (!string.IsNullOrEmpty(Model.Member.RegistrationTypeName))
                                        {
                                            <span class="badge bg-info">@Model.Member.RegistrationTypeName</span>
                                        }
                                        else
                                        {
                                            <span class="badge bg-secondary">@SharedLocalizer["Unknown"]</span>
                                        }
                                    </td>
                                </tr>
                                <tr>
                                    <td><strong>@SharedLocalizer["Status"]:</strong></td>
                                    <td>
                                        @if (Model.Member.IsActive)
                                        {
                                            <span class="badge bg-success">@SharedLocalizer["Active"]</span>
                                        }
                                        else
                                        {
                                            <span class="badge bg-secondary">@SharedLocalizer["Inactive"]</span>
                                        }
                                    </td>
                                </tr>
                            </table>
                        </div>
                    </div>
                    <div class="mt-3">
                        <a href="@Url.Action("Edit", "Members", new { memberId = Model.Member.Id })" class="btn btn-primary">
                            <i class="fas fa-edit"></i> @SharedLocalizer["EditMyProfile"]
                        </a>
                    </div>
                </div>
            </div>

            <!-- Address Information Card -->
            <div class="card mb-4">
                <div class="card-header bg-secondary text-white">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-map-marker-alt"></i> @SharedLocalizer["AddressInformation"]
                    </h5>
                </div>
                <div class="card-body">
                    <table class="table table-borderless">
                        <tr>
                            <td><strong>@SharedLocalizer["Address"]:</strong></td>
                            <td>@Model.Member.Address</td>
                        </tr>
                        <tr>
                            <td><strong>@SharedLocalizer["City"]:</strong></td>
                            <td>@Model.Member.City</td>
                        </tr>
                        <tr>
                            <td><strong>@SharedLocalizer["Province"]:</strong></td>
                            <td>@Model.Member.Province</td>
                        </tr>
                        <tr>
                            <td><strong>@SharedLocalizer["PostalCode"]:</strong></td>
                            <td>@Model.Member.PostalCode</td>
                        </tr>
                        <tr>
                            <td><strong>@SharedLocalizer["FullAddress"]:</strong></td>
                            <td>@Model.Member.FormattedAddress</td>
                        </tr>
                    </table>
                </div>
            </div>

            <!-- My Event Subscriptions -->
            <div class="card mb-4" id="event-subscriptions">
                <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-calendar-check"></i> @SharedLocalizer["MyEventSubscriptions"]
                    </h5>
                    <span class="badge bg-light text-dark">@Model.UpcomingEventsCount @SharedLocalizer["Upcoming"]</span>
                </div>
                <div class="card-body">
                    @if (Model.UpcomingEventsCount > 0)
                    {
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead class="table-light">
                                    <tr>
                                        <th>@SharedLocalizer["Event"]</th>
                                        <th>@SharedLocalizer["Date"]</th>
                                        <th>@SharedLocalizer["Location"]</th>
                                        <th>@SharedLocalizer["Status"]</th>
                                        <th>@SharedLocalizer["Actions"]</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach (var registration in Model.UpcomingEventRegistrations)
                                    {
                                        <tr>
                                            <td>
                                                <div class="d-flex align-items-center">
                                                    <span class="badge me-2" style="background-color: @registration.CategoryColor;">
                                                        @registration.CategoryName
                                                    </span>
                                                    <div>
                                                        <strong>@registration.EventTitle</strong>
                                                        @if (!string.IsNullOrEmpty(registration.EventDescription))
                                                        {
                                                            <br><small class="text-muted">@registration.EventDescription</small>
                                                        }
                                                    </div>
                                                </div>
                                            </td>
                                            <td>
                                                <div>@registration.DateRangeDisplay</div>
                                                @if (registration.GuestCount > 0)
                                                {
                                                    <small class="text-muted">+@registration.GuestCount @SharedLocalizer["Guests"]</small>
                                                }
                                            </td>
                                            <td>
                                                <span class="text-muted">@registration.EventLocationDisplay</span>
                                            </td>
                                            <td>
                                                <span class="badge @registration.StatusBadgeClass">@registration.StatusDisplay</span>
                                                <br><small class="text-muted">@SharedLocalizer["Registered"]: @registration.FormattedRegistrationDate</small>
                                            </td>
                                            <td>
                                                @if (registration.CanBeCancelled)
                                                {
                                                    <button type="button" class="btn btn-outline-danger btn-sm" 
                                                            onclick="cancelRegistration(@registration.RegistrationId, '@Html.Raw(registration.EventTitle.Replace("'", "\\'"))')"
                                                            title="@SharedLocalizer["Unsubscribe"]">
                                                        <i class="fas fa-times"></i> @SharedLocalizer["Unsubscribe"]
                                                    </button>
                                                }
                                                else
                                                {
                                                    <span class="text-muted small">@SharedLocalizer["CannotCancel"]</span>
                                                }
                                            </td>
                                        </tr>
                                    }
                                </tbody>
                            </table>
                        </div>
                    }
                    else
                    {
                        <div class="text-center py-5">
                            <i class="fas fa-calendar-times fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">@SharedLocalizer["NoUpcomingEventRegistrations"]</h5>
                            <p class="text-muted">@SharedLocalizer["NoUpcomingEventRegistrationsText"]</p>
                            <a href="@Url.Action("Subscribe", "Events")" class="btn btn-primary">
                                <i class="fas fa-calendar-plus"></i> @SharedLocalizer["SubscribeToEvents"]
                            </a>
                        </div>
                    }
                </div>
            </div>

            <!-- Recent Events -->
            @if (Model.PastEventsCount > 0)
            {
                <div class="card mb-4">
                    <div class="card-header bg-secondary text-white">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-history"></i> @SharedLocalizer["RecentEvents"]
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead class="table-light">
                                    <tr>
                                        <th>@SharedLocalizer["Event"]</th>
                                        <th>@SharedLocalizer["Date"]</th>
                                        <th>@SharedLocalizer["Status"]</th>
                                        <th>@SharedLocalizer["Attended"]</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach (var registration in Model.PastEventRegistrations)
                                    {
                                        <tr>
                                            <td>
                                                <div class="d-flex align-items-center">
                                                    <span class="badge me-2" style="background-color: @registration.CategoryColor;">
                                                        @registration.CategoryName
                                                    </span>
                                                    <div>
                                                        <strong>@registration.EventTitle</strong>
                                                    </div>
                                                </div>
                                            </td>
                                            <td>@registration.DateRangeDisplay</td>
                                            <td>
                                                <span class="badge @registration.StatusBadgeClass">@registration.StatusDisplay</span>
                                            </td>
                                            <td>
                                                @if (registration.Attended.HasValue)
                                                {
                                                    if (registration.Attended.Value)
                                                    {
                                                        <span class="text-success"><i class="fas fa-check"></i> @SharedLocalizer["Yes"]</span>
                                                    }
                                                    else
                                                    {
                                                        <span class="text-danger"><i class="fas fa-times"></i> @SharedLocalizer["No"]</span>
                                                    }
                                                }
                                                else
                                                {
                                                    <span class="text-muted">@SharedLocalizer["Unknown"]</span>
                                                }
                                            </td>
                                        </tr>
                                    }
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            }
        </div>

        <!-- Sidebar -->
        <div class="col-lg-4">
            <!-- Member Summary Card -->
            <div class="card mb-4">
                <div class="card-header bg-info text-white">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-user"></i> @SharedLocalizer["MemberSummary"]
                    </h5>
                </div>
                <div class="card-body">
                    <div class="text-center mb-3">
                        <i class="fas fa-user-circle fa-4x text-muted"></i>
                        <h5 class="mt-2">@Model.Member.FullName</h5>
                        <p class="text-muted">@Model.Member.Email</p>
                    </div>
                    
                    <div class="row text-center">
                        <div class="col-6">
                            <h4 class="text-primary">@Model.UpcomingEventsCount</h4>
                            <small class="text-muted">@SharedLocalizer["UpcomingEvents"]</small>
                        </div>
                        <div class="col-6">
                            <h4 class="text-secondary">@Model.PastEventsCount</h4>
                            <small class="text-muted">@SharedLocalizer["RecentEvents"]</small>
                        </div>
                    </div>

                    <hr>

                    <div class="mb-2">
                        <small class="text-muted">@SharedLocalizer["MemberID"]:</small>
                        <div>@Model.Member.Id</div>
                    </div>
                    
                    @if (!string.IsNullOrEmpty(Model.Member.HQc_Id))
                    {
                        <div class="mb-2">
                            <small class="text-muted">HQc ID:</small>
                            <div>@Model.Member.HQc_Id</div>
                        </div>
                    }
                    
                    <div class="mb-2">
                        <small class="text-muted">@SharedLocalizer["RegistrationType"]:</small>
                        <div>
                            <span class="badge bg-info">@Model.Member.RegistrationTypeName</span>
                        </div>
                    </div>
                    
                    <div class="mb-2">
                        <small class="text-muted">@SharedLocalizer["Status"]:</small>
                        <div>
                            @if (Model.Member.IsActive)
                            {
                                <span class="badge bg-success">@SharedLocalizer["Active"]</span>
                            }
                            else
                            {
                                <span class="badge bg-secondary">@SharedLocalizer["Inactive"]</span>
                            }
                        </div>
                    </div>
                    
                    <div class="mb-2">
                        <small class="text-muted">@SharedLocalizer["RegistrationDate"]:</small>
                        <div>@Model.Member.FormattedRegistrationDate</div>
                    </div>
                    
                    <div class="mb-2">
                        <small class="text-muted">@SharedLocalizer["MemberSince"]:</small>
                        <div>@((DateTime.Now - Model.Member.DateCreated).Days) @SharedLocalizer["DaysAgo"]</div>
                    </div>
                </div>
            </div>

            <!-- Help Card -->
            <div class="card">
                <div class="card-header bg-light">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-question-circle"></i> @SharedLocalizer["NeedHelp"]
                    </h5>
                </div>
                <div class="card-body">
                    <p class="small">@SharedLocalizer["MemberDashboardHelpText"]</p>
                    <div class="d-grid gap-2">
                        <a href="@Url.Action("Subscribe", "Events")" class="btn btn-outline-success btn-sm">
                            <i class="fas fa-calendar-plus"></i> @SharedLocalizer["SubscribeToEvents"]
                        </a>
                        <a href="mailto:<EMAIL>" class="btn btn-outline-primary btn-sm">
                            <i class="fas fa-envelope"></i> @SharedLocalizer["ContactSupport"]
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Unsubscribe Confirmation Modal -->
<div class="modal fade" id="unsubscribeModal" tabindex="-1" aria-labelledby="unsubscribeModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="unsubscribeModalLabel">
                    <i class="fas fa-exclamation-triangle text-warning"></i> @SharedLocalizer["ConfirmUnsubscribe"]
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p>@SharedLocalizer["UnsubscribeConfirmText"]</p>
                <p><strong id="eventTitleToCancel"></strong></p>
                <div class="alert alert-warning">
                    <i class="fas fa-info-circle"></i> @SharedLocalizer["UnsubscribeWarning"]
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                    <i class="fas fa-times"></i> @SharedLocalizer["Cancel"]
                </button>
                <button type="button" class="btn btn-danger" id="confirmUnsubscribeBtn">
                    <i class="fas fa-check"></i> @SharedLocalizer["YesUnsubscribe"]
                </button>
            </div>
        </div>
    </div>
</div>
</main>

<!-- Custom Styles -->
<style>
    /* Mobile-first responsive design */
    .btn-lg {
        min-height: 100px;
        padding: 1rem;
        font-size: 1rem;
    }
    
    .card-title {
        font-weight: 600;
    }
    
    .table td {
        vertical-align: middle;
    }
    
    .badge {
        font-size: 0.85em;
    }
    
    .fa-user-circle {
        color: #6c757d;
    }
    
    /* Skip links for accessibility */
    .skip-link {
        position: absolute;
        top: -40px;
        left: 6px;
        background: #000;
        color: #fff;
        padding: 8px;
        text-decoration: none;
        z-index: 1000;
        border-radius: 4px;
    }
    
    .skip-link:focus {
        top: 6px;
    }
    
    /* Tablet optimizations */
    @@media (max-width: 991px) {
        .col-lg-8, .col-lg-4 {
            margin-bottom: 1rem;
        }
        
        /* Stack sidebar below main content on tablets */
        .col-lg-4 {
            order: 2;
        }
        
        .col-lg-8 {
            order: 1;
        }
    }
    
    @@media (max-width: 768px) {
        /* Touch-friendly buttons */
        .btn {
            min-width: 44px;
            min-height: 44px;
            padding: 0.75rem 1rem;
        }
        
        .btn-sm {
            min-width: 36px;
            min-height: 36px;
            padding: 0.5rem 0.75rem;
        }
        
        .btn-lg {
            min-height: 80px;
            font-size: 0.95rem;
        }
        
        .btn-lg .fa-2x {
            font-size: 1.5em !important;
        }
        
        /* Responsive typography */
        h1, .h1 {
            font-size: 1.75rem;
        }
        
        h2, .h2 {
            font-size: 1.5rem;
        }
        
        h5, .h5 {
            font-size: 1.1rem;
        }
        
        /* Table responsiveness */
        .table-responsive {
            border: none;
        }
        
        .table th, .table td {
            padding: 0.5rem;
            font-size: 0.9rem;
        }
        
        /* Quick actions responsive grid */
        .col-md-3 {
            margin-bottom: 1rem;
        }
    }
    
    /* Mobile phone optimizations */
    @@media (max-width: 576px) {
        .container-fluid {
            padding-left: 1rem;
            padding-right: 1rem;
        }
        
        /* Compact header */
        .breadcrumb {
            font-size: 0.875rem;
            flex-wrap: wrap;
        }
        
        /* Stack quick action buttons */
        .col-md-3 {
            flex: 0 0 100%;
            max-width: 100%;
        }
        
        .btn-lg {
            min-height: 60px;
            font-size: 0.9rem;
        }
        
        .btn-lg .fa-2x {
            font-size: 1.25em !important;
        }
        
        /* Compact cards */
        .card-body {
            padding: 1rem;
        }
        
        .card-header {
            padding: 0.75rem 1rem;
        }
        
        /* Responsive tables - stack on mobile */
        .table-responsive table,
        .table-responsive thead,
        .table-responsive tbody,
        .table-responsive th,
        .table-responsive td,
        .table-responsive tr {
            display: block;
        }
        
        .table-responsive thead tr {
            position: absolute;
            top: -9999px;
            left: -9999px;
        }
        
        .table-responsive tr {
            border: 1px solid #ccc;
            margin-bottom: 0.5rem;
            padding: 0.5rem;
            border-radius: 0.375rem;
        }
        
        .table-responsive td {
            border: none;
            position: relative;
            padding-left: 50% !important;
            padding-top: 0.5rem;
            padding-bottom: 0.5rem;
        }
        
        .table-responsive td:before {
            content: attr(data-label) ": ";
            position: absolute;
            left: 6px;
            width: 45%;
            font-weight: bold;
            text-align: left;
        }
        
        /* Member summary optimization */
        .text-center h4 {
            font-size: 1.5rem;
        }
        
        /* Badge improvements */
        .badge {
            font-size: 0.8rem;
            white-space: normal;
            line-height: 1.2;
        }
    }
    
    /* Focus improvements for accessibility */
    .btn:focus,
    .btn:focus-visible {
        outline: 2px solid #0d6efd;
        outline-offset: 2px;
        box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
    }
    
    .card:focus-within {
        box-shadow: 0 0 0 2px rgba(13, 110, 253, 0.25);
    }
    
    /* Loading states */
    .btn.loading {
        pointer-events: none;
        opacity: 0.6;
    }
</style>

<!-- JavaScript for Unsubscribe Functionality -->
<script>
    let currentRegistrationId = null;

    function cancelRegistration(registrationId, eventTitle) {
        currentRegistrationId = registrationId;
        document.getElementById('eventTitleToCancel').textContent = eventTitle;
        
        // Show the modal
        const modal = new bootstrap.Modal(document.getElementById('unsubscribeModal'));
        modal.show();
    }

    document.getElementById('confirmUnsubscribeBtn').addEventListener('click', function() {
        if (currentRegistrationId) {
            unsubscribeFromEvent(currentRegistrationId);
        }
    });

    async function unsubscribeFromEvent(registrationId) {
        const confirmBtn = document.getElementById('confirmUnsubscribeBtn');
        const originalText = confirmBtn.innerHTML;
        
        // Show loading state
        confirmBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> @SharedLocalizer["Processing"]...';
        confirmBtn.disabled = true;

        try {
            const response = await fetch('/Events/UnsubscribeByRegistrationId', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'RequestVerificationToken': document.querySelector('input[name="__RequestVerificationToken"]')?.value || ''
                },
                body: JSON.stringify({
                    registrationId: registrationId
                })
            });

            const result = await response.json();

            if (result.success) {
                // Close modal
                bootstrap.Modal.getInstance(document.getElementById('unsubscribeModal')).hide();
                
                // Show success message
                showAlert('success', '@SharedLocalizer["UnsubscribeSuccess"]');
                
                // Reload page to refresh data
                setTimeout(() => {
                    window.location.reload();
                }, 1500);
            } else {
                showAlert('danger', result.error || '@SharedLocalizer["UnsubscribeError"]');
            }
        } catch (error) {
            console.error('Error unsubscribing:', error);
            showAlert('danger', '@SharedLocalizer["UnsubscribeError"]');
        } finally {
            // Reset button
            confirmBtn.innerHTML = originalText;
            confirmBtn.disabled = false;
        }
    }

    function showAlert(type, message) {
        const alertHtml = `
            <div class="alert alert-${type} alert-dismissible fade show" role="alert">
                <i class="fas fa-${type === 'success' ? 'check-circle' : 'exclamation-circle'}"></i> ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
        `;
        
        const alertContainer = document.createElement('div');
        alertContainer.innerHTML = alertHtml;
        document.querySelector('.container-fluid').insertBefore(alertContainer.firstElementChild, document.querySelector('.container-fluid').firstElementChild);
    }
</script>