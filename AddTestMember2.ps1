# Add test member to Production with correct columns
Write-Host "Adding test member to Production..." -ForegroundColor Yellow

$prodConn = "Server=SIMBA\SQLEXPRESS;Database=ParaHockeyDB;User Id=ParaHockeyUser;Password=***************;TrustServerCertificate=True;"

$conn = New-Object System.Data.SqlClient.SqlConnection($prodConn)
$conn.Open()

# Get required lookup IDs
$cmd = $conn.CreateCommand()
$cmd.CommandText = "SELECT Id FROM Provinces WHERE Code = 'QC'"
$provinceId = $cmd.ExecuteScalar()

$cmd.CommandText = "SELECT TOP 1 Id FROM RegistrationTypes ORDER BY Id"
$regTypeId = $cmd.ExecuteScalar()

$cmd.CommandText = "SELECT TOP 1 Id FROM Genders ORDER BY Id"
$genderId = $cmd.ExecuteScalar()

$cmd.CommandText = "SELECT TOP 1 Id FROM PhoneTypes ORDER BY Id"
$phoneTypeId = $cmd.ExecuteScalar()

Write-Host "Province ID: $provinceId"
Write-Host "Registration Type ID: $regTypeId"
Write-Host "Gender ID: $genderId"
Write-Host "Phone Type ID: $phoneTypeId"

# Insert test member
$cmd.CommandText = @"
INSERT INTO Members (
    FirstName, LastName, DateOfBirth, Address, City, PostalCode, Phone, Email,
    GenderId, ProvinceId, PhoneTypeId, RegistrationTypeId,
    DateCreated, IsActive, CreatedBySource
) VALUES (
    'Test', 'User', '1990-01-01', '123 Test St', 'Montreal', 'H1A 1A1', '(*************', '<EMAIL>',
    $genderId, $provinceId, $phoneTypeId, $regTypeId,
    GETUTCDATE(), 1, 0
)
"@

$cmd.ExecuteNonQuery()

$cmd.CommandText = "SELECT COUNT(*) FROM Members"
$count = $cmd.ExecuteScalar()
Write-Host "New member count: $count" -ForegroundColor Green

$conn.Close()
Write-Host "Test member created!" -ForegroundColor Green