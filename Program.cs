using Microsoft.AspNetCore.Identity;
using Microsoft.EntityFrameworkCore;
using Microsoft.Identity.Web;
using Microsoft.Identity.Web.UI;
using Microsoft.AspNetCore.Authentication.OpenIdConnect;
using ParaHockeyApp.Models;
using ParaHockeyApp.Models.Configuration;
using ParaHockeyApp.Models.Entities;
using ParaHockeyApp.Services;
using ParaHockeyApp.Middleware;
using Microsoft.AspNetCore.Localization;
using System.Globalization;

using Microsoft.Extensions.Options;
using OfficeOpenXml;

// Set EPPlus license for the entire application (EPPlus 6.x approach)
// This is the correct approach for EPPlus 6.2.10
// Note: This property is marked as obsolete but still required for EPPlus 6.x
OfficeOpenXml.ExcelPackage.LicenseContext = OfficeOpenXml.LicenseContext.NonCommercial;

var builder = WebApplication.CreateBuilder(args);

// Environment variables work the same way in all environments
// No additional configuration needed - ASP.NET Core loads them automatically

// Configure Kestrel to handle large request bodies for test results
builder.Services.Configure<Microsoft.AspNetCore.Server.Kestrel.Core.KestrelServerOptions>(options =>
{
    // Increase maximum request body size to 50MB for large test result payloads
    options.Limits.MaxRequestBodySize = 50 * 1024 * 1024; // 50MB (default is 30MB)
});

// Configure Environment Settings
builder.Services.Configure<EnvironmentSettings>(
    builder.Configuration.GetSection("Environment"));

// Configure Registration Settings (bind to Environment to support AgeOfMajority)
var registrationConfig = builder.Configuration.GetSection("Registration");
if (registrationConfig.Exists())
{
    var ageOfMajority = registrationConfig.GetValue<int>("AgeOfMajority", 18);
    builder.Services.Configure<EnvironmentSettings>(settings =>
    {
        settings.AgeOfMajority = ageOfMajority;
    });
}

// Configure Member Import Settings
builder.Services.Configure<MemberImportConfig>(
    builder.Configuration.GetSection("MemberImport"));

// Get environment settings for conditional setup
var envSettings = builder.Configuration.GetSection("Environment").Get<EnvironmentSettings>();
if (envSettings != null)
{
    // Override with Registration settings if they exist
    var regAgeOfMajority = builder.Configuration.GetValue<int?>("Registration:AgeOfMajority");
    if (regAgeOfMajority.HasValue)
    {
        envSettings.AgeOfMajority = regAgeOfMajority.Value;
    }
}

// Unified Database Configuration - All environments use SQL Server
var connectionString = builder.Configuration.GetConnectionString("DefaultConnection");
builder.Services.AddDbContext<ApplicationContext>((serviceProvider, options) =>
    options.UseSqlServer(connectionString)
    .AddInterceptors(serviceProvider.GetRequiredService<AuditInterceptor>()));

Console.WriteLine($"🗄️ {envSettings?.Name ?? "DEVELOPMENT"}: Using SQL Server database");

// Add services
builder.Services.AddRazorPages();

// Add Controllers with Views
var mvcBuilder = builder.Services.AddControllersWithViews();

// Add Session Services (for member authentication)
builder.Services.AddDistributedMemoryCache();
builder.Services.AddSession(options =>
{
    options.IdleTimeout = TimeSpan.FromMinutes(30); // 30 minute timeout to match MemberSession
    options.Cookie.HttpOnly = true; // Security: cookie not accessible via JavaScript
    options.Cookie.IsEssential = true; // Required for GDPR compliance
    options.Cookie.SameSite = SameSiteMode.Lax; // Security: prevent CSRF attacks
    options.Cookie.SecurePolicy = CookieSecurePolicy.SameAsRequest; // Use HTTPS in production
    options.Cookie.Name = ".ParaHockey.Session"; // Custom cookie name
    // Note: Not setting persistent cookies - sessions expire when browser closes
});

// Add Identity Core with SignInManager (needed by MembersController)
builder.Services.AddIdentityCore<AppUser>()
    .AddRoles<IdentityRole>()
    .AddSignInManager()
    .AddDefaultTokenProviders()
    .AddEntityFrameworkStores<ApplicationContext>();

// Add Azure AD Authentication conditionally based on environment settings
if (envSettings?.UseAuthentication == true)
{
    builder.Services.AddAuthentication(OpenIdConnectDefaults.AuthenticationScheme)
        .AddMicrosoftIdentityWebApp(builder.Configuration.GetSection("AzureAd"));
    
    // Configure OpenIdConnect events separately
    builder.Services.Configure<OpenIdConnectOptions>(OpenIdConnectDefaults.AuthenticationScheme, options =>
    {
        // Clear member sessions when admin successfully authenticates
        options.Events.OnTokenValidated = async context =>
        {
            var logger = context.HttpContext.RequestServices.GetRequiredService<ILogger<Program>>();
            if (context.HttpContext.Session.GetString("MemberSessionData") != null)
            {
                logger.LogInformation("Admin authenticated - clearing existing member session");
                context.HttpContext.Session.Remove("MemberSessionData");
            }
        };
    });

    // Add Azure AD UI - needs to be chained to MVC
    mvcBuilder.AddMicrosoftIdentityUI();

    Console.WriteLine($"🔐 {envSettings?.Name}: Azure AD authentication enabled");
}
else
{
    // Add a no-op authentication scheme for development without Azure AD
    builder.Services.AddAuthentication("NoAuth")
        .AddScheme<NoAuthenticationSchemeOptions, NoAuthenticationHandler>("NoAuth", null);

    Console.WriteLine($"🔐 {envSettings?.Name}: Azure AD authentication disabled (NoAuth mode)");
}

// Add localization services for views and data annotations
mvcBuilder.AddViewLocalization(Microsoft.AspNetCore.Mvc.Razor.LanguageViewLocationExpanderFormat.Suffix)
    .AddDataAnnotationsLocalization(options =>
    {
        options.DataAnnotationLocalizerProvider = (type, factory) =>
            factory.Create(typeof(ParaHockeyApp.Resources.SharedResourceMarker));
    });

// Add API Controllers
builder.Services.AddControllers();

// Configure request size limits for large test result submissions
builder.Services.Configure<Microsoft.AspNetCore.Http.Features.FormOptions>(options =>
{
    // Increase limits for large test result payloads (130+ tests with error messages)
    options.ValueLengthLimit = int.MaxValue; // Default is 4MB
    options.MultipartBodyLengthLimit = int.MaxValue; // Default is 128MB  
    options.MultipartHeadersLengthLimit = int.MaxValue;
});

// Configure JSON options to handle large payloads and serialize enums as strings
builder.Services.Configure<Microsoft.AspNetCore.Mvc.JsonOptions>(options =>
{
    options.JsonSerializerOptions.MaxDepth = 64; // Default is 32
                                                 // Allow large JSON payloads for test results

    // Serialize enums as strings instead of numbers (fixes UserRegistrationState serialization)
    options.JsonSerializerOptions.Converters.Add(new System.Text.Json.Serialization.JsonStringEnumConverter());
});

// Add Swagger for Development only
if (builder.Environment.IsDevelopment())
{
    builder.Services.AddEndpointsApiExplorer();
    builder.Services.AddSwaggerGen(c =>
    {
        c.SwaggerDoc("v1", new Microsoft.OpenApi.Models.OpenApiInfo
        {
            Title = "Parahockey API",
            Version = "v1",
            Description = "API REST pour l'application Parahockey"
        });
    });
}

// Add AutoMapper (for future DTOs)
builder.Services.AddAutoMapper(typeof(Program));

// Configure Localization
builder.Services.AddLocalization();

// Configure supported cultures
var supportedCultures = new[]
{
    new CultureInfo("fr-CA"), // Canadian French (default)
    new CultureInfo("en-CA")  // Canadian English
};

builder.Services.Configure<RequestLocalizationOptions>(options =>
{
    options.DefaultRequestCulture = new RequestCulture("fr-CA"); // French as default
    options.SupportedCultures = supportedCultures;
    options.SupportedUICultures = supportedCultures;

    // Configure culture providers (order matters)
    options.RequestCultureProviders = new List<IRequestCultureProvider>
    {
        new CookieRequestCultureProvider(), // First: Check cookie
        new AcceptLanguageHeaderRequestCultureProvider(), // Second: Check browser language
        new QueryStringRequestCultureProvider() // Third: Check query string
    };
});

// Register application services
builder.Services.AddScoped<IMemberService, MemberService>();
builder.Services.AddScoped<IMemberLogService, MemberLogService>();
builder.Services.AddScoped<IEmailService, EmailService>();
builder.Services.AddScoped<IUserContextService, UserContextService>();
builder.Services.AddScoped<IAuditLogService, AuditLogService>();
builder.Services.AddScoped<IAuditLogProcessingService, AuditLogProcessingService>();
builder.Services.AddScoped<IEventService, EventService>();
builder.Services.AddScoped<IDuplicateMemberService, DuplicateMemberService>();
builder.Services.AddScoped<INormalizationService, NormalizationService>();
builder.Services.AddScoped<IDuplicateDetectionService, DuplicateDetectionService>();
builder.Services.AddScoped<IMemberImportService, MemberImportService>();
builder.Services.AddScoped<ITempMemberService, TempMemberService>();
builder.Services.AddScoped<IImportBatchService, ImportBatchService>();
builder.Services.AddScoped<IDuplicateResolutionCoordinator, DuplicateResolutionCoordinator>();
builder.Services.AddScoped<IMemberSearchService, MemberSearchService>();
// Duplicate registrations removed - INormalizationService, IDuplicateDetectionService, IMemberImportService, ITempMemberService already registered above
builder.Services.AddScoped<IMemberExportService, MemberExportService>();
builder.Services.AddScoped<IEventExportService, EventExportService>();
builder.Services.AddScoped<IEnvironmentConfigurationService, EnvironmentConfigurationService>();
builder.Services.AddScoped<IEmptyStateService, EmptyStateService>();
builder.Services.AddScoped<IRegistrationFlowService, RegistrationFlowService>();
builder.Services.AddScoped<IPageAuditService, PageAuditService>();
builder.Services.AddScoped<ISecurityAuditService, SecurityAuditService>();
builder.Services.AddScoped<IAccessibilityAuditService, AccessibilityAuditService>();
builder.Services.AddScoped<IPerformanceOptimizationService, PerformanceOptimizationService>();
builder.Services.AddScoped<IInputSanitizationService, InputSanitizationService>();
builder.Services.AddScoped<IStreamingImportService, StreamingImportService>();
builder.Services.AddScoped<IConcurrencyService, ConcurrencyService>();
builder.Services.AddScoped<IConcurrencyLockService, ConcurrencyLockService>();
builder.Services.AddScoped<IErrorHandlingService, ErrorHandlingService>();
builder.Services.AddScoped<ITransactionService, TransactionService>();
builder.Services.AddScoped<AuditInterceptor>();

// Register Enhanced Localization System services
builder.Services.AddScoped<ICultureAwareFormattingService, CultureAwareFormattingService>();
builder.Services.AddScoped<ILocalizationKeyDetectionService, LocalizationKeyDetectionService>();
builder.Services.AddScoped<ILocalizationValidationService, LocalizationValidationService>();

// Register Error Handling System services are built-in to GlobalErrorHandlerMiddleware
builder.Services.AddScoped<ICultureSpecificValidationService, CultureSpecificValidationService>();

// Modern Form Validation Framework components are available through:
// - BaseFormViewModel for HTML5 validation attributes
// - Enhanced Tag Helpers for accessibility features  
// - JavaScript validation with bilingual support
// - Localized validation attributes

// Configure localization detection options
builder.Services.Configure<LocalizationDetectionOptions>(options =>
{
    options.EnableMissingKeyDetection = true;
    options.LogMissingKeys = true;
    options.EnableFallbackText = true;
    options.ShowFallbackIndicator = builder.Environment.IsDevelopment(); // Show indicators in dev only
});

// Register category filter service (singleton for caching)
builder.Services.AddSingleton<ICategoryFilterService, CategoryFilterService>();

// Add HttpContextAccessor for user context service
builder.Services.AddHttpContextAccessor();


// Add Health Checks
builder.Services.AddHealthChecks()
    .AddDbContextCheck<ApplicationContext>();

// Build the application
var app = builder.Build();

// Auto-migrate database for all environments (including Development)
Console.WriteLine($"🗄️ {envSettings?.Name}: Ensuring database is up to date...");

try
{
    using (var scope = app.Services.CreateScope())
    {
        var context = scope.ServiceProvider.GetRequiredService<ApplicationContext>();

        // Log connection string (without password)
        var connStr = builder.Configuration.GetConnectionString("DefaultConnection") ?? "No connection string found";
        var safeConnStr = System.Text.RegularExpressions.Regex.Replace(connStr, @"Password=[^;]+", "Password=***");
        Console.WriteLine($"📊 Using connection: {safeConnStr}");

        // Apply migrations (safe to call even if already applied)
        Console.WriteLine($"🔄 Applying database migrations...");
        context.Database.Migrate();
        Console.WriteLine($"✅ {envSettings?.Name}: Database migrations applied successfully");

        // Seed Master Admin (<EMAIL>) with AdminType = 9
        Console.WriteLine($"🌱 Seeding master admin...");
        await context.SeedMasterAdminAsync();
        Console.WriteLine($"✅ {envSettings?.Name}: Master Admin seeding completed");

        // Validate environment configuration
        Console.WriteLine($"🔍 Validating environment configuration...");
        try
        {
            var envConfigService = scope.ServiceProvider.GetRequiredService<IEnvironmentConfigurationService>();
            var validationResult = await envConfigService.ValidateConfigurationAsync();

            if (validationResult.IsValid)
            {
                Console.WriteLine($"✅ {envSettings?.Name}: Environment configuration validation passed");
            }
            else
            {
                Console.WriteLine($"⚠️ {envSettings?.Name}: Environment configuration validation failed:");
                foreach (var error in validationResult.Errors)
                {
                    Console.WriteLine($"   ❌ {error}");
                }
            }

            if (validationResult.Warnings.Count > 0)
            {
                Console.WriteLine($"⚠️ {envSettings?.Name}: Environment configuration warnings:");
                foreach (var warning in validationResult.Warnings)
                {
                    Console.WriteLine($"   ⚠️ {warning}");
                }
            }

            // In production, fail fast if there are critical configuration errors
            if (!validationResult.IsValid && envSettings?.IsProduction == true)
            {
                throw new InvalidOperationException($"Critical environment configuration errors detected in Production: {string.Join("; ", validationResult.Errors)}");
            }
        }
        catch (Exception configEx)
        {
            Console.WriteLine($"❌ Environment configuration validation failed: {configEx.Message}");
            // Don't fail startup for configuration validation errors in non-production
            if (envSettings?.IsProduction == true)
            {
                throw;
            }
        }
    }
}
catch (Exception ex)
{
    Console.WriteLine($"❌ DATABASE INITIALIZATION FAILED: {ex.Message}");
    Console.WriteLine($"❌ Stack Trace: {ex.StackTrace}");
    if (ex.InnerException != null)
    {
        Console.WriteLine($"❌ Inner Exception: {ex.InnerException.Message}");
        Console.WriteLine($"❌ Inner Stack Trace: {ex.InnerException.StackTrace}");
    }

    // In production, we might want to continue despite migration failures
    // but for now, let's make it obvious what's wrong
    throw;
}

// Environment-specific pipeline configuration
if (app.Environment.IsDevelopment())
{
    app.UseDeveloperExceptionPage();
    app.UseSwagger();
    app.UseSwaggerUI(c =>
    {
        c.SwaggerEndpoint("/swagger/v1/swagger.json", "Para Hockey API v1");
        c.RoutePrefix = "api/docs"; // Swagger UI at /api/docs
    });

    Console.WriteLine("📖 Swagger UI available at: /api/docs");
}
else
{
    app.UseExceptionHandler("/Error");
    app.UseHsts();
}

app.UseHttpsRedirection();

// Add global error handler middleware (must be early in the pipeline) - TEMPORARILY DISABLED FOR DEBUGGING
// app.UseMiddleware<GlobalErrorHandlerMiddleware>();

// Add performance cache middleware before static files
app.UseMiddleware<PerformanceCacheMiddleware>();

app.UseStaticFiles();

// Add request logging middleware for debugging
app.Use(async (context, next) =>
{
    Console.WriteLine($"🌐 REQUEST: {context.Request.Method} {context.Request.Path}");
    if (context.Request.Method == "POST")
    {
        Console.WriteLine($"🔴 POST REQUEST DETECTED: {context.Request.Path}");
        Console.WriteLine($"🔴 Content-Type: {context.Request.ContentType}");
        Console.WriteLine($"🔴 Has Form: {context.Request.HasFormContentType}");
    }
    await next();
    Console.WriteLine($"🌐 RESPONSE: {context.Response.StatusCode}");
});

app.UseRouting();

// Configure localization middleware (must be before MVC)
var localizationOptions = app.Services.GetRequiredService<IOptions<RequestLocalizationOptions>>();
app.UseRequestLocalization(localizationOptions.Value);

// Add Session middleware (must be after UseRouting and before UseAuthentication)
app.UseSession();

// Authentication and Authorization middleware (always active for Identity)
app.UseAuthentication();
app.UseAuthorization();

// Map Controllers (both MVC and API)
app.MapControllerRoute(
    name: "default",
    pattern: "{controller=Home}/{action=Index}/{id?}");

// Map API Controllers
app.MapControllers();

// Map Razor Pages
app.MapRazorPages();

// Map Health Checks
app.MapHealthChecks("/health");

// Display startup information
Console.WriteLine("🏒 Para Hockey Application Starting...");
Console.WriteLine($"🌍 Environment: {envSettings?.Name ?? "Unknown"}");
Console.WriteLine($"🎨 Theme: {envSettings?.Theme}");
Console.WriteLine($"🚨 Show Banner: {envSettings?.ShowBanner}");
Console.WriteLine($"🔐 Authentication: {(envSettings?.UseAuthentication == true ? "Enabled" : "Disabled")}");

app.Run();
