Feature: dob-driven-parent-emergency-form. Docs path: Plan/Specs/dob-driven-parent-emergency-form. Please open Requirements.md, Design.md, and Tasks.md, read them fully, and keep their contents in memory. Starting with the first unchecked item in Tasks.md, execute each task one by one. After completing a task, mark its checkbox as “[x]”. Continue until all tasks are complete. Then notify me and provide a concise manual test procedure to verify the implementation.

This feature changes the member registration process so that after entering the date of birth (DOB):

-   The Parent Form is shown immediately if the person is under 18, skipping membership type selection.
-   The Emergency Contact Form is shown immediately if the person is 18 or older, skipping membership type selection.
-   After completing the appropriate form, the membership type selection is shown.
-   If the person is 18 or older, the "Junior" membership type is greyed out and non-clickable, with a localized tooltip explaining why.
-   All age logic is in the service layer and uses a configurable age of majority.
-   All new UI and validation text is localized and accessible.
