@model ParaHockeyApp.Models.ViewModels.MemberRegistrationViewModel
@using Microsoft.AspNetCore.Mvc.Localization
@using System.Text.Json
@inject IHtmlLocalizer<ParaHockeyApp.Resources.SharedResourceMarker> SharedLocalizer

@{
    var isEditMode = ViewBag.IsEditMode == true;
    ViewData["Title"] = isEditMode ? SharedLocalizer["EditProfilePageTitle"] : SharedLocalizer["RegistrationPageTitle"];
}

<!-- Skip navigation links for accessibility -->
<a href="#registration-form" class="skip-link visually-hidden-focusable">@SharedLocalizer["SkipToRegistrationForm"]</a>

<main id="main-content" role="main" aria-label="@SharedLocalizer["RegistrationMainContent"]">
    <div class="container mt-4">
        <div class="row justify-content-center">
            <div class="col-lg-8 col-md-10">
                <div class="card shadow">
                    <header class="card-header bg-primary text-white text-center py-4">
                        <div class="mb-3">
                            <img src="~/assets/logos/parahockey-01.jpg" 
                                 alt="@SharedLocalizer["ParahockeyLogoAlt"]" 
                                 class="logo-img mb-3"
                                 style="max-height: 80px;"
                                 loading="lazy"
                                 decoding="async"
                                 width="200"
                                 height="80">
                        </div>
                        <h1 class="mb-0">@(isEditMode ? SharedLocalizer["EditProfileTitle"] : SharedLocalizer["RegistrationFormTitle"])</h1>
                        <p class="mb-0">Parahockey</p>
                    </header>

                    <div class="card-body p-4">
                        <!-- ARIA live region for dynamic error announcements -->
                        <div id="form-status" aria-live="polite" aria-atomic="true" class="visually-hidden"></div>
                        <div id="form-errors" aria-live="assertive" aria-atomic="true" class="visually-hidden"></div>
                        
                        @if (!isEditMode)
                        {
                            <div class="text-center mb-4">
                                <p class="text-muted">
                                    @SharedLocalizer["AlreadyRegistered"]
                                    <a href="@Url.Action("Login", "Members")" 
                                       class="text-decoration-none fw-bold"
                                       aria-describedby="login-help">
                                        @SharedLocalizer["ClickHereLink"]</a>
                                </p>
                                <span id="login-help" class="visually-hidden">@SharedLocalizer["LoginLinkDescription"]</span>
                            </div>
                        }

                        @if (TempData["ServerValidationErrors"] != null)
                        {
                            <div class="alert alert-danger" role="alert" aria-labelledby="debug-errors-title">
                                <h4 id="debug-errors-title">Server-Side Validation Errors (Debug):</h4>
                                <pre>@TempData["ServerValidationErrors"]</pre>
                            </div>
                        }

                        @if (!ViewData.ModelState.IsValid)
                        {
                            <div class="alert alert-danger" role="alert" aria-labelledby="validation-errors-title">
                                <h6 id="validation-errors-title">
                                    <i class="fas fa-exclamation-triangle" aria-hidden="true"></i> 
                                    @SharedLocalizer["ValidationErrors"]
                                </h6>
                                <div asp-validation-summary="ModelOnly" class="mb-0"></div>
                            </div>
                        }

                    @if (!isEditMode)
                    {
                        @if (!Model.EnvironmentSettings.IsProduction)
                        {
                            <div class="text-end mb-3">
                                <button type="button" class="btn btn-info me-1" id="juniorTestBtn">Junior test</button>
                                <button type="button" class="btn btn-secondary me-1" id="developmentTestBtn">Development test</button>
                                <button type="button" class="btn btn-primary me-1" id="eliteTestBtn">Elite test</button>
                                <button type="button" class="btn btn-warning me-1" id="coachTestBtn">Coach test</button>
                                <button type="button" class="btn btn-success" id="volunteerTestBtn">Volunteer test</button>
                            </div>
                        }
                    }

                        <form asp-controller="Members" 
                              asp-action="@(isEditMode ? "UpdateMember" : "Register")" 
                              method="post" 
                              id="registration-form"
                              aria-labelledby="form-title"
                              aria-describedby="form-description"
                              novalidate>
                            @Html.AntiForgeryToken()
                            
                            @if (isEditMode)
                            {
                                <input type="hidden" name="memberId" value="@ViewBag.MemberId" />
                            }

                            @* Hidden field for temp member ID when creating from imported data *@
                            @if (ViewBag.TempMemberId != null)
                            {
                                <input type="hidden" name="tempMemberId" value="@ViewBag.TempMemberId" />
                            }

                            <p id="form-description" class="visually-hidden">
                                @SharedLocalizer["RegistrationFormDescription"]
                            </p>

                            <!-- Basic Information Fieldset -->
                            <fieldset class="mb-4">
                                <legend class="section-header">
                                    <h2 class="text-primary border-bottom pb-2 h4">
                                        <i class="fas fa-user" aria-hidden="true"></i> 
                                        @SharedLocalizer["BasicInformation"]
                                    </h2>
                                </legend>

                                <div class="row mb-3">
                                    <div class="col-md-6">
                                        <label asp-for="FirstName" 
                                               class="ph-form-label required">
                                            @SharedLocalizer["FirstName"]
                                        </label>
                                        <input asp-for="FirstName" 
                                               name="FirstName" 
                                               type="text"
                                               class="ph-form-control" 
                                               required
                                               aria-required="true"
                                               aria-describedby="FirstName-error FirstName-help"
                                               placeholder="@SharedLocalizer["FirstNamePlaceholder"]" 
                                               autocomplete="given-name"
                                               inputmode="text"
                                               minlength="2"
                                               maxlength="50" />
                                        <small id="FirstName-help" class="form-text text-muted visually-hidden">
                                            @SharedLocalizer["FirstNameHelp"]
                                        </small>
                                        <span id="FirstName-error" asp-validation-for="FirstName" 
                                              class="text-danger small" role="alert" aria-live="polite"></span>
                                    </div>
                                    <div class="col-md-6">
                                        <label asp-for="LastName" 
                                               class="ph-form-label required">
                                            @SharedLocalizer["LastName"]
                                        </label>
                                        <input asp-for="LastName" 
                                               name="LastName" 
                                               type="text"
                                               class="ph-form-control" 
                                               required
                                               aria-required="true"
                                               aria-describedby="LastName-error LastName-help"
                                               placeholder="@SharedLocalizer["LastNamePlaceholder"]" 
                                               autocomplete="family-name"
                                               inputmode="text"
                                               minlength="2"
                                               maxlength="50" />
                                        <small id="LastName-help" class="form-text text-muted visually-hidden">
                                            @SharedLocalizer["LastNameHelp"]
                                        </small>
                                        <span id="LastName-error" asp-validation-for="LastName" 
                                              class="text-danger small" role="alert" aria-live="polite"></span>
                                    </div>
                                </div>

                        <div class="row mb-3">
                            <div class="col-md-6">
                                <div class="d-flex align-items-center mb-2">
                                    <label asp-for="DateOfBirth"
                                        class="form-label required me-2">@SharedLocalizer["DateOfBirth"]</label>
                                    <i class="fas fa-calendar-alt datepicker-icon" style="cursor: pointer;"></i>
                                </div>
                                <input asp-for="DateOfBirth" name="DateOfBirth" type="text" class="form-control datepicker-input"
                                    placeholder="@SharedLocalizer["DatePlaceholder"]" required autocomplete="bday" />
                                <small class="form-text text-muted">@SharedLocalizer["DateFormatHelper"]</small>
                                <span asp-validation-for="DateOfBirth" class="text-danger small"></span>
                            </div>
                            <div class="col-md-6">
                                <label asp-for="GenderId" class="form-label required"
                                    id="genderLabel">@SharedLocalizer["Gender"]</label>
                                <div class="mt-2" role="group" aria-labelledby="genderLabel">
                                    @foreach (var gender in Model.Genders)
                                    {
                                                                                                                                                                        <div class="form-check form-check-inline">
                                                                                                                                                                            <input type="radio" asp-for="GenderId" name="GenderId" value="@gender.Value"
                                                                                                                                                                                class="form-check-input" id="<EMAIL>" required />
                                                                                                                                                                            <label class="form-check-label"
                                                                                                                                                                                for="<EMAIL>">@SharedLocalizer[gender.Text]</label>
                                                                                                                                                                        </div>
                                    }
                                </div>
                                <span asp-validation-for="GenderId" class="text-danger small"></span>
                            </div>
                        </div>

                        <div class="section-header mb-4 mt-5">
                            <h4 class="text-primary border-bottom pb-2">
                                <i class="fas fa-map-marker-alt"></i> @SharedLocalizer["Address"]
                            </h4>
                        </div>

                        <div class="mb-3">
                            <label asp-for="Address"
                                class="form-label required">@SharedLocalizer["StreetAddress"]</label>
                            <input asp-for="Address" name="Address" class="form-control" required
                                placeholder="@SharedLocalizer["StreetAddressPlaceholder"]" autocomplete="street-address" />
                            <span asp-validation-for="Address" class="text-danger small"></span>
                        </div>

                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label asp-for="City" class="form-label required">@SharedLocalizer["City"]</label>
                                <input asp-for="City" name="City" class="form-control" required
                                    placeholder="@SharedLocalizer["CityPlaceholder"]" autocomplete="address-level2" />
                                <span asp-validation-for="City" class="text-danger small"></span>
                            </div>
                            <div class="col-md-6">
                                <label asp-for="ProvinceId"
                                    class="form-label required">@SharedLocalizer["Province"]</label>
                                <select asp-for="ProvinceId" name="ProvinceId" class="form-select" required>
                                    <option value="">@SharedLocalizer["SelectProvince"]</option>
                                    @foreach (var province in Model.Provinces)
                                    {
                                                                                                                                                                        <option value="@province.Value">@SharedLocalizer[province.Text]</option>
                                    }
                                </select>
                                <span asp-validation-for="ProvinceId" class="text-danger"></span>
                            </div>
                        </div>

                        <div class="col-md-6 mb-3">
                            <label asp-for="PostalCode"
                                class="form-label required">@SharedLocalizer["PostalCode"]</label>
                            <input asp-for="PostalCode" name="PostalCode" class="form-control" id="postal-code-input" maxlength="7"
                                placeholder="A1A 1A1" style="text-transform: uppercase;" required autocomplete="postal-code" />
                            <span asp-validation-for="PostalCode" class="text-danger"></span>
                        </div>

                        <div class="section-header mb-4 mt-5">
                            <h4 class="text-primary border-bottom pb-2"><i class="fas fa-phone"></i>
                                @SharedLocalizer["ContactInformation"]</h4>
                        </div>

                        <div class="row mb-3">
                            <div class="col-md-8">
                                <label asp-for="Phone"
                                    class="form-label required">@SharedLocalizer["PhoneNumber"]</label>
                                <input asp-for="Phone" name="Phone" type="tel" class="form-control" maxlength="14"
                                    placeholder="(*************" required autocomplete="tel" />
                                <span asp-validation-for="Phone" class="text-danger"></span>
                            </div>
                            <div class="col-md-4 align-self-end">
                                @foreach (var phoneType in Model.PhoneTypes)
                                {
                                                                                                                                                                    <div class="form-check form-check-inline">
                                                                                                                                                                        <input type="radio" asp-for="PhoneTypeId" name="PhoneTypeId" value="@phoneType.Value"
                                                                                                                                                                            class="form-check-input" id="<EMAIL>" required />
                                                                                                                                                                        <label class="form-check-label"
                                                                                                                                                                            for="<EMAIL>">@SharedLocalizer[phoneType.Text]</label>
                                                                                                                                                                    </div>
                                }
                                <span asp-validation-for="PhoneTypeId" class="text-danger small"></span>
                            </div>
                        </div>

                        <div class="col-md-8 mb-3 mt-4">
                            <label asp-for="Email" class="form-label required">@SharedLocalizer["Email"]</label>
                            <input asp-for="Email" name="Email" type="email" class="form-control" required
                                placeholder="@SharedLocalizer["EmailPlaceholder"]" autocomplete="email" />
                            <span asp-validation-for="Email" class="text-danger"></span>
                        </div>

                        @* HQc_Id field - Admin only *@
                        @if (ViewBag.IsAdmin == true)
                        {
                            <div class="col-md-8 mb-3">
                                <label asp-for="HQc_Id" class="form-label">@SharedLocalizer["HQc_Id"]</label>
                                <input asp-for="HQc_Id" name="HQc_Id" type="text" class="form-control"
                                    placeholder="@SharedLocalizer["HQc_IdPlaceholder"]" autocomplete="off" />
                                <span asp-validation-for="HQc_Id" class="text-danger"></span>
                                <small class="form-text text-muted">@SharedLocalizer["AdminOnly"]</small>
                            </div>
                        }


                        <div id="membership-type-section" class="section-header mb-4 mt-5">
                            <h4 class="text-primary border-bottom pb-2">
                                <i class="fas fa-hockey-puck"></i> @SharedLocalizer["RegistrationType"]
                            </h4>
                        </div>

                        <div id="membership-type-options" class="row mb-4">
                            <div class="col-12">
                                <div class="registration-types">
                                    <div class="row" role="group">
                                        @foreach (var regType in Model.RegistrationTypes)
                                        {
                                            <div class="col-md-6">
                                                <div class="form-check mb-3">
                                                    <input type="radio" asp-for="RegistrationTypeId"
                                                        name="RegistrationTypeId" value="@regType.Value"
                                                        class="form-check-input" id="<EMAIL>" 
                                                        required />
                                                    <label class="form-check-label" for="<EMAIL>">
                                                        <strong>@SharedLocalizer[regType.Text]</strong><br>
                                                        <small class="text-muted">@SharedLocalizer[regType.Group.Name]</small>
                                                    </label>
                                                </div>
                                            </div>
                                        }
                                    </div>
                                </div>
                                @if (isEditMode)
                                {
                                    <input type="hidden" name="RegistrationTypeId" value="@Model.RegistrationTypeId" />
                                }
                                <span asp-validation-for="RegistrationTypeId" class="text-danger small"></span>
                            </div>
                        </div>

                        <!-- Dynamic Contact Fields - Shown based on DOB (MOVED AFTER Registration Type) -->
                        @await Html.PartialAsync("_ParentFields")
                        @await Html.PartialAsync("_EmergencyContactFields")

                        <div class="d-grid gap-2 mt-4">
                            <button type="submit" class="btn btn-primary btn-lg">
                                <i class="@(isEditMode ? "fas fa-save" : "fas fa-user-plus")"></i> 
                                @(isEditMode ? SharedLocalizer["UpdateProfileButton"] : SharedLocalizer["RegisterButton"])
                            </button>
                            @if (!isEditMode)
                            {
                                <button type="reset" class="btn btn-outline-secondary">
                                    <i class="fas fa-undo"></i> @SharedLocalizer["ClearFormButton"]
                                </button>
                            }
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
    .required::after {
        content: " *";
        color: #dc3545;
        font-weight: bold;
    }

    .logo-img {
        border-radius: 8px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }

    .section-header h4 {
        margin-bottom: 0.5rem;
    }

    .registration-types .form-check {
        position: relative;
        padding: 1rem;
        border: 1px solid #dee2e6;
        border-radius: 0.375rem;
        transition: all 0.15s ease-in-out;
        margin-bottom: 1rem;
        background-color: #ffffff;
    }

    .registration-types .form-check:hover {
        background-color: #f8f9fa;
        border-color: #0d6efd;
    }

    .registration-types .form-check-input:checked+.form-check-label {
        color: #0d6efd;
        font-weight: 600;
    }

    .registration-types .form-check:has(.form-check-input:checked) {
        background-color: #e7f3ff;
        border-color: #0d6efd;
        border-width: 2px;
    }

    .registration-types .form-check-label {
        cursor: pointer;
        width: 100%;
        margin: 0;
    }

    /* Ensure radio buttons work properly */
    .registration-types .form-check-input[type="radio"] {
        margin-top: 0.25rem;
    }

    /* Selected state for debugging */
    .registration-types .form-check.selected {
        background-color: #e7f3ff !important;
        border-color: #0d6efd !important;
        border-width: 2px !important;
    }

    /* Mobile responsiveness */
    @@media (max-width: 768px) {
        .card {
            margin-left: -0.5rem;
            margin-right: -0.5rem;
        }
    }
</style>

@section Scripts {
    @{
        await Html.RenderPartialAsync("_ValidationScriptsPartial");
    }
    <script src="~/js/jquery.mask.min.js"></script>
    <script>
        $(document).ready(function () {
            console.log('🟢 Registration form loaded');

            // Initialize basic field masks
            $('#postal-code-input').mask('L0L 0L0', {
                'translation': {
                    'L': { pattern: /[A-Za-z]/ },
                    '0': { pattern: /[0-9]/ }
                }
            }).on('input', function () {
                this.value = this.value.toUpperCase();
            });

            $('input[type="tel"]').mask('(*************');
            $('.datepicker-input').mask('0000-00-00');

            // Main DOB logic function
            function handleDOBChange() {
                console.log('🟢 DOB change detected');
                var dobValue = $('.datepicker-input').val();
                console.log('DOB value:', dobValue);
                
                // Auto-format 8 digits to YYYY-MM-DD
                if (/^\d{8}$/.test(dobValue)) {
                    dobValue = dobValue.substring(0,4) + '-' + dobValue.substring(4,6) + '-' + dobValue.substring(6,8);
                    $('.datepicker-input').val(dobValue);
                    console.log('Formatted DOB:', dobValue);
                }
                
                if (dobValue && dobValue.length === 10) {
                    // Calculate age
                    var dateOfBirth = new Date(dobValue);
                    var today = new Date();
                    var age = today.getFullYear() - dateOfBirth.getFullYear();
                    var monthDiff = today.getMonth() - dateOfBirth.getMonth();
                    if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < dateOfBirth.getDate())) {
                        age--;
                    }
                    console.log('Age calculated:', age);
                    
                    // Show appropriate form based on age
                    var ageOfMajority = @Model.EnvironmentSettings.AgeOfMajority;
                    if (age < ageOfMajority) {
                        console.log('Showing Parent form');
                        $('#parent-fields-section').show();
                        $('#second-parent-card').show(); // Also show second parent
                        $('#emergency-contact-section').hide();
                        // Enable Junior option
                        $('input[name="RegistrationTypeId"][value="1"]').prop('disabled', false);
                    } else {
                        console.log('Showing Emergency Contact form');
                        $('#emergency-contact-section').show();
                        $('#parent-fields-section').hide();
                        $('#second-parent-card').hide(); // Hide second parent
                        // Disable Junior option
                        var juniorOption = $('input[name="RegistrationTypeId"][value="1"]');
                        juniorOption.prop('disabled', true).prop('checked', false);
                        console.log('Junior option disabled for 18+');
                    }
                } else {
                    console.log('DOB incomplete, hiding forms');
                    $('#parent-fields-section').hide();
                    $('#second-parent-card').hide();
                    $('#emergency-contact-section').hide();
                }
            }

            // Bind DOB events
            $('.datepicker-input').on('input blur change keydown', function(e) {
                if (e.type === 'keydown' && e.key !== 'Enter') return;
                handleDOBChange();
            });

            // Handle registration type selection to show forms if DOB is entered
            $('input[name="RegistrationTypeId"]').on('change', function() {
                console.log('🟢 Registration type changed:', $(this).val());
                // If DOB is already entered, refresh the form display
                var dobValue = $('.datepicker-input').val();
                if (dobValue && dobValue.length === 10) {
                    handleDOBChange();
                }
            });

            // Initialize on page load
            handleDOBChange();

            // Test button handlers - using database-based incremental naming
            $('#coachTestBtn').on('click', function() {
                console.log('🟢 Coach test button clicked');
                $.get('/api/members/next-test?type=Coach')
                    .done(function(data) {
                        console.log('🟢 Coach test data received:', data);
                        fillForm('Coach', data);
                    })
                    .fail(function(xhr, status, error) {
                        console.error('❌ Coach test API error:', error);
                        alert('Failed to load Coach test data: ' + error);
                    });
            });
            
            $('#juniorTestBtn').on('click', function() {
                console.log('🟢 Junior test button clicked');
                $.get('/api/members/next-test?type=Junior')
                    .done(function(data) {
                        console.log('🟢 Junior test data received:', data);
                        fillForm('Junior', data);
                    })
                    .fail(function(xhr, status, error) {
                        console.error('❌ Junior test API error:', error);
                        alert('Failed to load Junior test data: ' + error);
                    });
            });
            
            $('#volunteerTestBtn').on('click', function() {
                console.log('🟢 Volunteer test button clicked');
                $.get('/api/members/next-test?type=Volunteer')
                    .done(function(data) {
                        console.log('🟢 Volunteer test data received:', data);
                        fillForm('Volunteer', data);
                    })
                    .fail(function(xhr, status, error) {
                        console.error('❌ Volunteer test API error:', error);
                        alert('Failed to load Volunteer test data: ' + error);
                    });
            });
            
            $('#developmentTestBtn').on('click', function() {
                console.log('🟢 Development test button clicked');
                $.get('/api/members/next-test?type=Development')
                    .done(function(data) {
                        console.log('🟢 Development test data received:', data);
                        fillForm('Development', data);
                    })
                    .fail(function(xhr, status, error) {
                        console.error('❌ Development test API error:', error);
                        alert('Failed to load Development test data: ' + error);
                    });
            });
            
            $('#eliteTestBtn').on('click', function() {
                console.log('🟢 Elite test button clicked');
                $.get('/api/members/next-test?type=Elite')
                    .done(function(data) {
                        console.log('🟢 Elite test data received:', data);
                        fillForm('Elite', data);
                    })
                    .fail(function(xhr, status, error) {
                        console.error('❌ Elite test API error:', error);
                        alert('Failed to load Elite test data: ' + error);
                    });
            });

            // Function to fill form with test data using database-based incremental naming
            function fillForm(type, data) {
                console.log('🟢 fillForm called with type:', type, 'data:', data);
                
                // If API response is wrapped ( { success:true, data:{...} } ), unwrap it
                if (data && data.data) {
                    data = data.data;
                    console.log('🟢 Unwrapped data:', data);
                }
                if (!data || !data.firstName) {
                    console.error('❌ fillForm: invalid data received', data);
                    alert('Error: Invalid test data received from API');
                    return;
                }
                
                console.log('🟢 Using firstName:', data.firstName);
                
                var regTypeIds = {
                    'Coach': 4,
                    'Junior': 1,
                    'Volunteer': 5,
                    'Development': 2,
                    'Elite': 3
                };
                
                // Extract number from first name (e.g., TestCoach_12 -> 12)
                var numMatch = data.firstName.match(/_(\d{2})$/);
                var num = numMatch ? numMatch[1] : '01';
                
                // Clear form first
                $('#registration-form')[0].reset();
                
                // Main fields
                $("input[name='FirstName']").val(data.firstName);
                $("input[name='LastName']").val('TEST-' + type + 'Test_' + num);
                
                // Set DOB based on type - Junior gets 2015, others get 1965
                var birthDate = type === 'Junior' ? '2015-05-04' : '1965-05-04';
                $("input[name='DateOfBirth']").val(birthDate);
                
                $("input[name='Address']").val('123 Test St');
                $("input[name='City']").val('Testville');
                $("select[name='ProvinceId']").val($("select[name='ProvinceId'] option:eq(1)").val());
                $("input[name='PostalCode']").val('A1A 1A1');
                $("input[name='Phone']").val('(*************');
                $("input[name='Email']").val(data.firstName.toLowerCase() + '@@test.com');
                
                // Select first gender
                $("input[name='GenderId']").first().prop('checked', true);
                
                // Select first phone type
                $("input[name='PhoneTypeId']").first().prop('checked', true);
                
                // Select registration type
                $("input[name='RegistrationTypeId']").prop('checked', false); // Uncheck all first
                $("input[name='RegistrationTypeId'][value='" + regTypeIds[type] + "']").prop('checked', true);
                
                // Trigger DOB change to show appropriate forms
                handleDOBChange();
                
                // Wait for forms to be shown, then fill contact data
                setTimeout(function() {
                    // Fill ALL parent fields (for Junior - will show parent form)
                    $("#Parents_0__FirstName").val('ParentFirst');
                    $("#Parents_0__LastName").val('ParentLast');
                    $("#Parents_0__ParentType").val('Mother');
                    $("#Parents_0__Phone").val('(*************');
                    $("#Parents_0__Email").val('parent1@@test.com');
                    
                    $("#Parents_1__FirstName").val('Parent2First');
                    $("#Parents_1__LastName").val('Parent2Last');
                    $("#Parents_1__ParentType").val('Father');
                    $("#Parents_1__Phone").val('(*************');
                    $("#Parents_1__Email").val('parent2@@test.com');
                    
                    // Fill ALL emergency contact fields (for adults - will show emergency form)  
                    $("#EmergencyContact_FirstName").val(type + 'EC');
                    $("#EmergencyContact_LastName").val('TestEC');
                    $("#EmergencyContact_RelationToUser").val('Friend');
                    $("#EmergencyContact_Phone").val('(*************');
                    $("#EmergencyContact_Email").val(type.toLowerCase() + '.ec@@test.com');
                }, 100);
            }
        });
    </script>
}