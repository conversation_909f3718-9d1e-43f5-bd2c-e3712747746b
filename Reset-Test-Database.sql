-- Resets the TEST database by dropping and recreating it.
-- Run this on your TEST server.
USE master;
GO

IF DB_ID('ParaHockeyDB_TEST') IS NOT NULL
BEGIN
    PRINT 'Dropping ParaHockeyDB_TEST...';
    ALTER DATABASE ParaHockeyDB_TEST SET SINGLE_USER WITH ROLLBACK IMMEDIATE;
    DROP DATABASE ParaHockeyDB_TEST;
    PRINT 'ParaHockeyDB_TEST dropped.';
END
GO

PRINT 'Creating ParaHockeyDB_TEST...';
CREATE DATABASE ParaHockeyDB_TEST;
PRINT 'ParaHockeyDB_TEST created.';
GO