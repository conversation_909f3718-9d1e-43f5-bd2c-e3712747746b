{"Import": {"Title": "Importer des Membres", "SelectFile": "Sélectionner un Fichier Excel", "FileFormat": "Formats supportés : .xlsx, .xls", "Upload": "Télécharger le Fichier", "Processing": "Traitement de l'importation en cours...", "Success": "Importation terminée avec succès", "Error": "Une erreur s'est produite durant l'importation", "ValidationErrors": "Erreurs de Validation Trouvées", "DuplicatesFound": "<PERSON><PERSON><PERSON> Potentiels Détectés", "ReviewRequired": "Révision Requise Avant Traitement"}, "Validation": {"RequiredField": "Ce champ est obligatoire", "InvalidEmail": "Veu<PERSON>z entrer une adresse email valide", "InvalidDate": "Veuillez entrer une date valide", "InvalidPhone": "Veuillez entrer un numéro de téléphone valide", "InvalidPostalCode": "Veuillez entrer un code postal valide", "FutureDate": "La date ne peut pas être dans le futur", "DuplicateEmail": "Cette adresse email existe déjà", "InvalidStatus": "Veuillez sélectionner un statut valide"}, "Duplicates": {"Title": "Résolution des Doublons", "PotentialMatch": "<PERSON><PERSON>lon potentiel trouvé", "ConfidenceHigh": "Correspondance de haute confiance", "ConfidenceMedium": "Correspondance de confiance moyenne", "ConfidenceLow": "Correspondance de faible confiance", "ExactEmail": "Correspondance exacte d'email", "SimilarName": "Nom similaire et date de naissance", "Actions": {"KeepExisting": "Garder le Membre Existant", "ReplaceExisting": "Remplacer par les Nouvelles Données", "MergeFields": "Fusionner les Champs Sélectionnés", "CreateAsNew": "<PERSON><PERSON>er comme Nouveau Membre"}, "Reasons": {"MoreComplete": "L'enregistrement existant est plus complet", "MoreRecent": "Le nouvel enregistrement a des informations plus récentes", "DifferentPerson": "Ce sont des personnes différentes", "CombineBest": "Combiner les meilleures informations des deux"}}, "Processing": {"Title": "Traitement des Membres", "ValidatingData": "Validation des données importées...", "DetectingDuplicates": "Détection des doublons potentiels...", "CreatingMembers": "Création des enregistrements de membres...", "UpdatingRecords": "Mise à jour des enregistrements existants...", "GeneratingReport": "Génération du rapport d'importation...", "Complete": "Traitement terminé", "RecordsProcessed": "Enregistrements traités : {0}", "RecordsCreated": "Nouveaux membres créés : {0}", "RecordsUpdated": "Membres existants mis à jour : {0}", "ErrorsFound": "Erreurs trouvées : {0}"}, "Errors": {"FileNotFound": "Le fichier sélectionné n'a pas pu être trouvé", "InvalidFileFormat": "Le format de fichier n'est pas supporté. Veuillez utiliser des fichiers .xlsx ou .xls", "FileCorrupted": "Le fichier semble être corrompu ou ne peut pas être lu", "FileTooLarge": "Le fichier est trop volumineux. La taille maximale est de {0} Mo", "NoDataFound": "Aucune donnée n'a été trouvée dans le fichier", "DatabaseError": "Une erreur de base de données s'est produite. Veuillez réessayer plus tard", "UnexpectedError": "Une erreur inattendue s'est produite. Veuillez contacter le support si le problème persiste", "ValidationFailed": "La validation des données a échoué. Veuillez corriger les erreurs et réessayer", "DuplicateProcessingFailed": "Le traitement des doublons a échoué. Veuillez réviser et réessayer", "PermissionDenied": "Vous n'avez pas l'autorisation d'effectuer cette opération"}, "Buttons": {"Cancel": "Annuler", "Continue": "<PERSON><PERSON><PERSON>", "Retry": "<PERSON><PERSON><PERSON><PERSON>", "Download": "Télécharger", "Back": "Retour", "Next": "Suivant", "Finish": "<PERSON><PERSON><PERSON>", "Save": "<PERSON><PERSON><PERSON><PERSON>", "Delete": "<PERSON><PERSON><PERSON><PERSON>", "Edit": "Modifier", "View": "Voir"}, "Messages": {"ConfirmDelete": "Êtes-vous sûr de vouloir supprimer cet élément ?", "UnsavedChanges": "Vous avez des modifications non sauvegardées. Êtes-vous sûr de vouloir partir ?", "OperationComplete": "Opération terminée avec succès", "PleaseWait": "Veuillez patienter pendant que nous traitons votre demande...", "NoRecordsFound": "Aucun enregistrement trouvé correspondant à vos critères", "LoadingData": "Chargement des données..."}}