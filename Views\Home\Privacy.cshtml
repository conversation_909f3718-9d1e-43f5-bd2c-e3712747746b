@{
    ViewData["Title"] = "Politique de Confidentialité";
}

<div class="container">
    <div class="row">
        <div class="col-12">
            <div class="jumbotron jumbotron-fluid bg-primary text-white mb-4">
                <div class="container">
                    <h1 class="display-4">@ViewData["Title"]</h1>
                    <p class="lead">Protection de vos données personnelles</p>
                </div>
            </div>

            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-shield-alt"></i> Confidentialité et Protection des Données</h5>
                </div>
                <div class="card-body">
                    <p>Cette page détaille notre politique de confidentialité et la façon dont nous protégeons vos
                        données personnelles.</p>

                    <h6>Données collectées :</h6>
                    <ul>
                        <li>Informations de connexion (si authentification activée)</li>
                        <li>Données de test pendant la phase de développement</li>
                        <li>Logs techniques pour le débogage</li>
                    </ul>

                    <h6>Utilisation des données :</h6>
                    <ul>
                        <li>Amélioration de l'application</li>
                        <li>Support technique</li>
                        <li>Conformité réglementaire</li>
                    </ul>

                    <div class="alert alert-info mt-3">
                        <i class="fas fa-info-circle"></i>
                        <strong>Phase de développement :</strong> Cette application est actuellement en phase de test.
                        Les données saisies sont utilisées uniquement pour vérifier le bon fonctionnement de
                        l'architecture.
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>