@using Microsoft.Extensions.Options
@using Microsoft.Extensions.Localization
@inject IOptions<ParaHockeyApp.Models.Configuration.EnvironmentSettings> EnvSettings
@inject IStringLocalizer<ParaHockeyApp.Resources.SharedResourceMarker> Localizer

@{
    var envSettings = EnvSettings.Value;
    var shouldShowTools = envSettings.ShowDevelopmentTools && !envSettings.IsProduction;
    var currentPath = Context.Request.Path.ToString().ToLowerInvariant();
    var isRegistrationPage = currentPath.Contains("/members/register");
    var isAdminPage = currentPath.Contains("/admin");
    var isFormsPage = isRegistrationPage || isAdminPage;
}

@if (shouldShowTools)
{
    <div class="development-tools-container">
        <!-- Collapsible Development Tools Panel -->
        <div class="card border-info mb-3">
            <div class="card-header bg-info text-white">
                <button class="btn btn-link text-white p-0 w-100 text-start" type="button" 
                        data-bs-toggle="collapse" data-bs-target="#developmentToolsPanel" 
                        aria-expanded="false" aria-controls="developmentToolsPanel">
                    <i class="fas fa-tools me-2"></i>
                    <strong>@Localizer["DevTools_Title"]</strong>
                    <i class="fas fa-chevron-down float-end mt-1"></i>
                </button>
            </div>
            <div class="collapse" id="developmentToolsPanel">
                <div class="card-body">
                    <div class="row">
                        @if (isRegistrationPage)
                        {
                            <!-- Registration Form Tools -->
                            <div class="col-md-6 col-lg-4 mb-3">
                                <h6 class="text-info">
                                    <i class="fas fa-user-plus me-1"></i> @Localizer["DevTools_Registration"]
                                </h6>
                                <div class="btn-group-vertical d-grid gap-1">
                                    <button type="button" class="btn btn-outline-primary btn-sm" onclick="quickFillJunior()">
                                        <i class="fas fa-child me-1"></i> @Localizer["DevTools_FillJunior"]
                                    </button>
                                    <button type="button" class="btn btn-outline-success btn-sm" onclick="quickFillAdult()">
                                        <i class="fas fa-user me-1"></i> @Localizer["DevTools_FillAdult"]
                                    </button>
                                    <button type="button" class="btn btn-outline-warning btn-sm" onclick="quickFillCoach()">
                                        <i class="fas fa-whistle me-1"></i> @Localizer["DevTools_FillCoach"]
                                    </button>
                                </div>
                            </div>
                        }

                        @if (isAdminPage)
                        {
                            <!-- Admin Panel Tools -->
                            <div class="col-md-6 col-lg-4 mb-3">
                                <h6 class="text-danger">
                                    <i class="fas fa-user-shield me-1"></i> @Localizer["DevTools_Admin"]
                                </h6>
                                <div class="btn-group-vertical d-grid gap-1">
                                    <button type="button" class="btn btn-outline-info btn-sm" onclick="generateTestData()">
                                        <i class="fas fa-database me-1"></i> @Localizer["DevTools_GenerateTestData"]
                                    </button>
                                    <button type="button" class="btn btn-outline-secondary btn-sm" onclick="clearTestData()">
                                        <i class="fas fa-trash me-1"></i> @Localizer["DevTools_ClearTestData"]
                                    </button>
                                </div>
                            </div>
                        }

                        <!-- Debug Information Tools -->
                        <div class="col-md-6 col-lg-4 mb-3">
                            <h6 class="text-secondary">
                                <i class="fas fa-bug me-1"></i> @Localizer["DevTools_Debug"]
                            </h6>
                            <div class="btn-group-vertical d-grid gap-1">
                                <button type="button" class="btn btn-outline-dark btn-sm" onclick="toggleDebugInfo()">
                                    <i class="fas fa-info-circle me-1"></i> @Localizer["DevTools_ShowDebugInfo"]
                                </button>
                                <button type="button" class="btn btn-outline-dark btn-sm" onclick="showEnvironmentInfo()">
                                    <i class="fas fa-server me-1"></i> @Localizer["DevTools_ShowEnvInfo"]
                                </button>
                                <button type="button" class="btn btn-outline-dark btn-sm" onclick="showUserInfo()">
                                    <i class="fas fa-user-cog me-1"></i> @Localizer["DevTools_ShowUserInfo"]
                                </button>
                            </div>
                        </div>

                        <!-- Performance Tools -->
                        <div class="col-md-6 col-lg-4 mb-3">
                            <h6 class="text-warning">
                                <i class="fas fa-tachometer-alt me-1"></i> @Localizer["DevTools_Performance"]
                            </h6>
                            <div class="btn-group-vertical d-grid gap-1">
                                <button type="button" class="btn btn-outline-warning btn-sm" onclick="measurePageLoad()">
                                    <i class="fas fa-stopwatch me-1"></i> @Localizer["DevTools_MeasurePageLoad"]
                                </button>
                                <button type="button" class="btn btn-outline-warning btn-sm" onclick="clearCache()">
                                    <i class="fas fa-broom me-1"></i> @Localizer["DevTools_ClearCache"]
                                </button>
                            </div>
                        </div>

                        <!-- API Testing Tools -->
                        <div class="col-md-6 col-lg-4 mb-3">
                            <h6 class="text-success">
                                <i class="fas fa-plug me-1"></i> @Localizer["DevTools_Api"]
                            </h6>
                            <div class="btn-group-vertical d-grid gap-1">
                                <button type="button" class="btn btn-outline-success btn-sm" onclick="testApiEndpoints()">
                                    <i class="fas fa-vial me-1"></i> @Localizer["DevTools_TestApi"]
                                </button>
                                <a href="/api/docs" target="_blank" class="btn btn-outline-success btn-sm">
                                    <i class="fas fa-book me-1"></i> @Localizer["DevTools_ApiDocs"]
                                </a>
                            </div>
                        </div>
                    </div>

                    <!-- Environment Information Display -->
                    <div class="row mt-3">
                        <div class="col-12">
                            <div class="alert alert-light">
                                <small class="text-muted">
                                    <strong>@Localizer["DevTools_Environment"]:</strong> @envSettings.Name |
                                    <strong>@Localizer["DevTools_Theme"]:</strong> @envSettings.Theme |
                                    <strong>@Localizer["DevTools_Auth"]:</strong> @(envSettings.UseAuthentication ? Localizer["DevTools_Enabled"] : Localizer["DevTools_Disabled"])
                                </small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Debug Information Panel (Initially Hidden) -->
        <div id="debugInfoPanel" class="card border-secondary mb-3" style="display: none;">
            <div class="card-header bg-secondary text-white">
                <strong>@Localizer["DevTools_DebugInformation"]</strong>
                <button type="button" class="btn-close btn-close-white float-end" onclick="hideDebugInfo()"></button>
            </div>
            <div class="card-body">
                <div id="debugInfoContent">
                    <!-- Debug content will be populated by JavaScript -->
                </div>
            </div>
        </div>
    </div>

    <script>
        // Development Tools JavaScript Functions
        function quickFillJunior() {
            console.log('Filling junior registration form...');
            // Click the Junior Test button if it exists on the page
            const juniorBtn = document.getElementById('juniorTestBtn');
            if (juniorBtn) {
                juniorBtn.click();
                showNotification('@Html.Raw(Localizer["DevTools_FormFilled"].Value.Replace("'", "\\'"))', 'success');
            } else {
                showNotification('Junior test button not found. Make sure you are on the registration page.', 'warning');
            }
        }

        function quickFillAdult() {
            console.log('Filling adult registration form...');
            // Since there's no adult button, use Elite as substitute
            const eliteBtn = document.getElementById('eliteTestBtn');
            if (eliteBtn) {
                eliteBtn.click();
                showNotification('@Html.Raw(Localizer["DevTools_FormFilled"].Value.Replace("'", "\\'"))', 'success');
            } else {
                showNotification('Elite test button not found. Make sure you are on the registration page.', 'warning');
            }
        }

        function quickFillCoach() {
            console.log('Filling coach registration form...');
            // Click the Coach Test button if it exists on the page
            const coachBtn = document.getElementById('coachTestBtn');
            if (coachBtn) {
                coachBtn.click();
                showNotification('@Html.Raw(Localizer["DevTools_FormFilled"].Value.Replace("'", "\\'"))', 'success');
            } else {
                showNotification('Coach test button not found. Make sure you are on the registration page.', 'warning');
            }
        }

        function generateTestData() {
            if (confirm('@Html.Raw(Localizer["DevTools_ConfirmGenerateData"].Value.Replace("'", "\\'"))')) {
                console.log('Generating test data...');
                showNotification('@Html.Raw(Localizer["DevTools_TestDataGenerated"].Value.Replace("'", "\\'"))', 'info');
                // Here you would make an API call to generate test data
            }
        }

        function clearTestData() {
            if (confirm('@Html.Raw(Localizer["DevTools_ConfirmClearData"].Value.Replace("'", "\\'"))')) {
                console.log('Clearing test data...');
                showNotification('@Html.Raw(Localizer["DevTools_TestDataCleared"].Value.Replace("'", "\\'"))', 'warning');
                // Here you would make an API call to clear test data
            }
        }

        function toggleDebugInfo() {
            const panel = document.getElementById('debugInfoPanel');
            const isVisible = panel.style.display !== 'none';
            
            if (isVisible) {
                hideDebugInfo();
            } else {
                showDebugInfo();
            }
        }

        function showDebugInfo() {
            const panel = document.getElementById('debugInfoPanel');
            const content = document.getElementById('debugInfoContent');
            
            content.innerHTML = `
                <div class="row">
                    <div class="col-md-6">
                        <h6>@Html.Raw(Localizer["DevTools_PageInfo"].Value.Replace("'", "\\'"))</h6>
                        <ul class="list-unstyled small">
                            <li><strong>URL:</strong> ${window.location.href}</li>
                            <li><strong>@Html.Raw(Localizer["DevTools_UserAgent"].Value.Replace("'", "\\'")):</strong> ${navigator.userAgent}</li>
                            <li><strong>@Html.Raw(Localizer["DevTools_Viewport"].Value.Replace("'", "\\'")):</strong> ${window.innerWidth}x${window.innerHeight}</li>
                            <li><strong>@Html.Raw(Localizer["DevTools_LoadTime"].Value.Replace("'", "\\'")):</strong> ${performance.now().toFixed(2)}ms</li>
                        </ul>
                    </div>
                    <div class="col-md-6">
                        <h6>@Html.Raw(Localizer["DevTools_Browser"].Value.Replace("'", "\\'"))</h6>
                        <ul class="list-unstyled small">
                            <li><strong>@Html.Raw(Localizer["DevTools_Language"].Value.Replace("'", "\\'")):</strong> ${navigator.language}</li>
                            <li><strong>@Html.Raw(Localizer["DevTools_CookiesEnabled"].Value.Replace("'", "\\'")):</strong> ${navigator.cookieEnabled}</li>
                            <li><strong>@Html.Raw(Localizer["DevTools_OnLine"].Value.Replace("'", "\\'")):</strong> ${navigator.onLine}</li>
                        </ul>
                    </div>
                </div>
            `;
            
            panel.style.display = 'block';
        }

        function hideDebugInfo() {
            document.getElementById('debugInfoPanel').style.display = 'none';
        }

        function showEnvironmentInfo() {
            alert(`Environment: @envSettings.Name\nTheme: @envSettings.Theme\nAuthentication: @(envSettings.UseAuthentication ? "Enabled" : "Disabled")\nDev Tools: @(envSettings.ShowDevelopmentTools ? "Enabled" : "Disabled")`);
        }

        function showUserInfo() {
            console.log('Showing user information...');
            showNotification('@Html.Raw(Localizer["DevTools_CheckConsole"].Value.Replace("'", "\\'"))', 'info');
        }

        function measurePageLoad() {
            const loadTime = performance.now();
            showNotification(`@Html.Raw(Localizer["DevTools_PageLoadTime"].Value.Replace("'", "\\'")): ${loadTime.toFixed(2)}ms`, 'info');
        }

        function clearCache() {
            if ('caches' in window) {
                caches.keys().then(function(cacheNames) {
                    return Promise.all(
                        cacheNames.map(function(cacheName) {
                            return caches.delete(cacheName);
                        })
                    );
                }).then(() => {
                    showNotification('@Html.Raw(Localizer["DevTools_CacheCleared"].Value.Replace("'", "\\'"))', 'success');
                });
            } else {
                showNotification('@Html.Raw(Localizer["DevTools_CacheNotSupported"].Value.Replace("'", "\\'"))', 'warning');
            }
        }

        function testApiEndpoints() {
            console.log('Testing API endpoints...');
            showNotification('@Html.Raw(Localizer["DevTools_TestingApi"].Value.Replace("'", "\\'"))', 'info');
            // Here you would implement API testing logic
        }

        function showNotification(message, type = 'info') {
            const alertClass = `alert-${type}`;
            const notification = document.createElement('div');
            notification.className = `alert ${alertClass} alert-dismissible fade show position-fixed`;
            notification.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
            notification.innerHTML = `
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;
            
            document.body.appendChild(notification);
            
            // Auto-remove after 3 seconds
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.remove();
                }
            }, 3000);
        }
    </script>

    <style>
        .development-tools-container {
            margin-bottom: 1rem;
        }
        
        .development-tools-container .btn-group-vertical .btn {
            text-align: left;
        }
        
        .development-tools-container .card-header .btn-link:hover {
            text-decoration: none;
        }
        
        .development-tools-container .fas {
            width: 16px;
        }
        
        @@media (max-width: 768px) {
            .development-tools-container .col-md-6 {
                margin-bottom: 1rem;
            }
        }
    </style>
}