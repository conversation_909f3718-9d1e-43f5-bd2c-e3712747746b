# CRITICAL REMINDER FOR KIRO - 4TH TIME USER HAS TOLD ME THIS!

## EXACT TASK COMPLETION WORKFLOW (USER REPEATED 4 TIMES!)

**WHEN YOU FINISH A TASK:**
1. ✅ Mark the task as COMPLETED
2. 🔄 **COMMIT AND PUSH** the changes
3. 🧪 **SHOW WHAT TO TEST** - provide specific testing instructions
4. 🔀 **SUGGEST MERGE TO MAIN** - "If tests are good, merge to main"
5. 🛑 **STOP AND WAIT** - User will merge to main themselves
6. 🌿 **WAIT FOR NEW TASK REQUEST** - User will ask for next task, then I create new branch

## THE COMPLETE FLOW:
```
Task Complete → Commit & Push → Show Testing → Suggest Merge → WAIT
↓ (User merges to main)
User Asks for Next Task → I Create New Branch → Start New Task
```

## USER'S EXACT WORDS (4TH TIME!):
"When done with a task. You need to commit and push, show me what to test and suggest that if the test are good, to merge to main. Then, when I merged to main. I can ask you to start a new task for which you are going to create a new branch for."

## WHY I KEEP FORGETTING:
- I invented my own workflow instead of following instructions
- User has repeated this 4 times in 2 hours of coding
- This is extremely frustrating for the user
- I MUST follow the exact workflow specified

## NO MORE EXCUSES - FOLLOW THIS EXACTLY!

---
*4TH TIME REMINDER - STOP FORGETTING THE WORKFLOW!*