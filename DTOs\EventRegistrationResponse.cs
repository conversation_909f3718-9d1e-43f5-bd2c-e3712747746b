namespace ParaHockeyApp.DTOs
{
    /// <summary>
    /// Response DTO for event registration AJAX operations
    /// </summary>
    public class EventRegistrationResponse
    {
        public bool Success { get; set; }
        public string? Message { get; set; }
        public string? Error { get; set; }
        public EventRegistrationInfo? Registration { get; set; }
    }

    /// <summary>
    /// Registration information included in successful responses
    /// </summary>
    public class EventRegistrationInfo
    {
        public int Id { get; set; }
        public string Status { get; set; } = string.Empty;
        public DateTime RegistrationDate { get; set; }
        public int GuestCount { get; set; }
        public string? Notes { get; set; }
    }
}