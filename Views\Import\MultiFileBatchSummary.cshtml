@model ParaHockeyApp.Controllers.MultiFileImportBatchSummaryViewModel
@{
    ViewData["Title"] = "Multi-File Import Results";
}

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2>@ViewData["Title"]</h2>
                <div>
                    <a href="@Url.Action("History")" class="btn btn-outline-secondary">
                        <i class="fas fa-history"></i> Import History
                    </a>
                    <a href="@Url.Action("MultiFileUpload")" class="btn btn-primary">
                        <i class="fas fa-upload"></i> New Multi-File Import
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Import Batch Information -->
    <div class="row mb-4">
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-info-circle"></i> Import Batch Information
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <p><strong>Batch ID:</strong> @Model.BatchSummary.ImportBatchId</p>
                            <p><strong>File Name:</strong> @Model.BatchSummary.FileName</p>
                            <p><strong>Uploaded By:</strong> @Model.BatchSummary.UploadedBy</p>
                        </div>
                        <div class="col-md-6">
                            <p><strong>Upload Date:</strong> @Model.BatchSummary.UploadedAtUtc.ToString("yyyy-MM-dd HH:mm:ss") UTC</p>
                            <p><strong>Total Records:</strong> @Model.BatchSummary.TotalRows</p>
                            <p><strong>Status:</strong> <span class="badge bg-info">@Model.BatchSummary.Status</span></p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-lg-4">
            <!-- Relationship Matching Results -->
            <div class="card">
                <div class="card-header bg-success text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-link"></i> Relationship Matching
                    </h5>
                </div>
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <span>Members with Parents:</span>
                        <strong>@Model.RelationshipMatchingResult.MembersWithParents</strong>
                    </div>
                    <div class="d-flex justify-content-between">
                        <span>Members with Emergency Contacts:</span>
                        <strong>@Model.RelationshipMatchingResult.MembersWithEmergencyContacts</strong>
                    </div>
                    <hr>
                    <div class="d-flex justify-content-between">
                        <span>Unmatched Parent Records:</span>
                        <strong class="@(Model.RelationshipMatchingResult.UnmatchedParentRecords > 0 ? "text-warning" : "text-success")">
                            @Model.RelationshipMatchingResult.UnmatchedParentRecords
                        </strong>
                    </div>
                    <div class="d-flex justify-content-between">
                        <span>Unmatched Emergency Contacts:</span>
                        <strong class="@(Model.RelationshipMatchingResult.UnmatchedEmergencyContactRecords > 0 ? "text-warning" : "text-success")">
                            @Model.RelationshipMatchingResult.UnmatchedEmergencyContactRecords
                        </strong>
                    </div>
                    
                    @if (Model.RelationshipMatchingResult.HasUnmatchedRecords)
                    {
                        <div class="mt-3">
                            <a href="@Url.Action("ReviewUnmatchedRelationships", new { batchId = Model.BatchSummary.ImportBatchId })" 
                               class="btn btn-warning btn-sm">
                                <i class="fas fa-exclamation-triangle"></i> Review Unmatched
                            </a>
                        </div>
                    }
                </div>
            </div>
        </div>
    </div>

    <!-- Processing Status Cards -->
    <div class="row mb-4">
        <div class="col-md-2">
            <div class="card text-center @(Model.BatchSummary.ReadyToCreateCount > 0 ? "border-success" : "")">
                <div class="card-body">
                    <h3 class="text-success">@Model.BatchSummary.ReadyToCreateCount</h3>
                    <p class="card-text">Ready to Create</p>
                    @if (Model.BatchSummary.ReadyToCreateCount > 0)
                    {
                        <a href="@Url.Action("Queue", "TempMembers", new { batchId = Model.BatchSummary.ImportBatchId, status = "ReadyToCreate" })" 
                           class="btn btn-success btn-sm">Process</a>
                    }
                </div>
            </div>
        </div>
        
        <div class="col-md-2">
            <div class="card text-center @(Model.BatchSummary.DuplicateCount > 0 ? "border-warning" : "")">
                <div class="card-body">
                    <h3 class="text-warning">@Model.BatchSummary.DuplicateCount</h3>
                    <p class="card-text">Duplicates</p>
                    @if (Model.BatchSummary.DuplicateCount > 0)
                    {
                        <a href="@Url.Action("Queue", "TempMembers", new { batchId = Model.BatchSummary.ImportBatchId, status = "Duplicate" })" 
                           class="btn btn-warning btn-sm">Review</a>
                    }
                </div>
            </div>
        </div>
        
        <div class="col-md-2">
            <div class="card text-center @(Model.BatchSummary.NeedsFixCount > 0 ? "border-danger" : "")">
                <div class="card-body">
                    <h3 class="text-danger">@Model.BatchSummary.NeedsFixCount</h3>
                    <p class="card-text">Needs Fix</p>
                    @if (Model.BatchSummary.NeedsFixCount > 0)
                    {
                        <a href="@Url.Action("Queue", "TempMembers", new { batchId = Model.BatchSummary.ImportBatchId, status = "NeedsFix" })" 
                           class="btn btn-danger btn-sm">Fix</a>
                    }
                </div>
            </div>
        </div>
        
        <div class="col-md-2">
            <div class="card text-center @(Model.BatchSummary.CreatedCount > 0 ? "border-primary" : "")">
                <div class="card-body">
                    <h3 class="text-primary">@Model.BatchSummary.CreatedCount</h3>
                    <p class="card-text">Created</p>
                </div>
            </div>
        </div>
        
        <div class="col-md-2">
            <div class="card text-center @(Model.BatchSummary.MergedCount > 0 ? "border-info" : "")">
                <div class="card-body">
                    <h3 class="text-info">@Model.BatchSummary.MergedCount</h3>
                    <p class="card-text">Merged</p>
                </div>
            </div>
        </div>
        
        <div class="col-md-2">
            <div class="card text-center @(Model.BatchSummary.RejectedCount > 0 ? "border-secondary" : "")">
                <div class="card-body">
                    <h3 class="text-secondary">@Model.BatchSummary.RejectedCount</h3>
                    <p class="card-text">Rejected</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Queue Status Details -->
    @if (Model.QueueStatuses.Any())
    {
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-list"></i> Queue Status Details
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-striped">
                                <thead>
                                    <tr>
                                        <th>Status</th>
                                        <th>Count</th>
                                        <th>Description</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach (var status in Model.QueueStatuses)
                                    {
                                        <tr>
                                            <td>
                                                <span class="badge bg-@GetStatusBadgeClass(status.QueueType.ToString())">
                                                    @status.QueueDisplayName
                                                </span>
                                            </td>
                                            <td><strong>@status.TotalItems</strong></td>
                                            <td>@status.QueueDescription</td>
                                            <td>
                                                @if (status.TotalItems > 0)
                                                {
                                                    <a href="@Url.Action("Queue", "TempMembers", new { batchId = Model.BatchSummary.ImportBatchId, status = status.QueueType })" 
                                                       class="btn btn-outline-primary btn-sm">
                                                        <i class="fas fa-eye"></i> View Queue
                                                    </a>
                                                }
                                            </td>
                                        </tr>
                                    }
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    }

    <!-- Unmatched HCR Numbers -->
    @if (Model.RelationshipMatchingResult.UnmatchedHcrNumbers.Any())
    {
        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header bg-warning text-dark">
                        <h5 class="mb-0">
                            <i class="fas fa-exclamation-triangle"></i> Unmatched HCR Numbers
                        </h5>
                    </div>
                    <div class="card-body">
                        <p>The following HCR numbers were found in relationship files but not in the member file:</p>
                        <div class="row">
                            @foreach (var hcrNumber in Model.RelationshipMatchingResult.UnmatchedHcrNumbers.Take(20))
                            {
                                <div class="col-md-3 col-sm-6">
                                    <span class="badge bg-warning text-dark mb-1">@hcrNumber</span>
                                </div>
                            }
                            @if (Model.RelationshipMatchingResult.UnmatchedHcrNumbers.Count > 20)
                            {
                                <div class="col-12">
                                    <p class="text-muted">
                                        ... and @(Model.RelationshipMatchingResult.UnmatchedHcrNumbers.Count - 20) more.
                                    </p>
                                </div>
                            }
                        </div>
                    </div>
                </div>
            </div>
        </div>
    }
</div>

@{
    string GetStatusBadgeClass(string status)
    {
        return status switch
        {
            "ReadyToCreate" => "success",
            "Duplicate" => "warning",
            "NeedsFix" => "danger",
            "Created" => "primary",
            "Merged" => "info",
            "Rejected" => "secondary",
            _ => "light"
        };
    }

    string GetStatusDescription(string status)
    {
        return status switch
        {
            "ReadyToCreate" => "Records validated and ready to create as new members",
            "Duplicate" => "Records that match existing members and need resolution",
            "NeedsFix" => "Records with validation errors that need to be corrected",
            "Created" => "Records that have been successfully created as members",
            "Merged" => "Records that have been merged with existing members",
            "Rejected" => "Records that have been rejected and will not be processed",
            _ => "Unknown status"
        };
    }
}