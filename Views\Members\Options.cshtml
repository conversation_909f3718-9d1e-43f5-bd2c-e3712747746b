@model ParaHockeyApp.Models.ViewModels.MemberDetailsViewModel
@using Microsoft.AspNetCore.Mvc.Localization
@inject IHtmlLocalizer<ParaHockeyApp.Resources.SharedResourceMarker> SharedLocalizer

@{
    ViewData["Title"] = $"Member Dashboard - {Model.FullName}";
}

<div class="container-fluid mt-4">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col">
            <h1 class="h2 text-primary">
                <i class="fas fa-user-circle"></i> @SharedLocalizer["MemberDashboard"]
            </h1>
            <p class="text-muted">@SharedLocalizer["Welcome"], @Model.FullName!</p>
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item">@SharedLocalizer["MemberPortal"]</li>
                    <li class="breadcrumb-item active" aria-current="page">@SharedLocalizer["Dashboard"]</li>
                </ol>
            </nav>
        </div>
    </div>

    <!-- Navigation Menu -->
    <div class="row mb-4">
        <div class="col">
            <div class="card">
                <div class="card-header bg-info text-white">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-compass"></i> @SharedLocalizer["WhatWouldYouLikeToDo"]
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6 col-lg-4 mb-3">
                            <a href="@Url.Action("Edit", "Members", new { memberId = Model.Id })" class="btn btn-primary btn-lg w-100 h-100 d-flex flex-column justify-content-center align-items-center">
                                <i class="fas fa-edit fa-2x mb-2"></i>
                                <span>@SharedLocalizer["EditMyProfile"]</span>
                                <small class="text-light">@SharedLocalizer["UpdateYourInformation"]</small>
                            </a>
                        </div>
                        <div class="col-md-6 col-lg-4 mb-3">
                            <a href="@Url.Action("CalendarReadOnly", "Members")" class="btn btn-info btn-lg w-100 h-100 d-flex flex-column justify-content-center align-items-center">
                                <i class="fas fa-calendar fa-2x mb-2"></i>
                                <span>@SharedLocalizer["ViewCalendar"]</span>
                                <small class="text-light">@SharedLocalizer["SeeUpcomingEvents"]</small>
                            </a>
                        </div>
                        <div class="col-md-6 col-lg-4 mb-3">
                            <a href="@Url.Action("Subscribe", "Events")" class="btn btn-success btn-lg w-100 h-100 d-flex flex-column justify-content-center align-items-center">
                                <i class="fas fa-calendar-check fa-2x mb-2"></i>
                                <span>@SharedLocalizer["SubscribeToEvents"]</span>
                                <small class="text-light">@SharedLocalizer["RegisterForUpcomingEvents"]</small>
                            </a>
                        </div>
                        <div class="col-md-6 col-lg-4 mb-3">
                            <a href="@Url.Action("Login", "Members")" class="btn btn-outline-secondary btn-lg w-100 h-100 d-flex flex-column justify-content-center align-items-center">
                                <i class="fas fa-sign-out-alt fa-2x mb-2"></i>
                                <span>@SharedLocalizer["Logout"]</span>
                                <small>@SharedLocalizer["EndYourSession"]</small>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Member Information Display -->
    <div class="row">
        <div class="col-lg-8">
            <!-- Personal Information Card -->
            <div class="card mb-4">
                <div class="card-header bg-primary text-white">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-user"></i> @SharedLocalizer["PersonalInformation"]
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <table class="table table-borderless">
                                <tr>
                                    <td><strong>@SharedLocalizer["MemberID"]:</strong></td>
                                    <td>
                                        @Model.Id
                                        @if (!string.IsNullOrEmpty(Model.HQc_Id))
                                        {
                                            <br><small class="text-muted">HQc ID: @Model.HQc_Id</small>
                                        }
                                    </td>
                                </tr>
                                <tr>
                                    <td><strong>@SharedLocalizer["FirstName"]:</strong></td>
                                    <td>@Model.FirstName</td>
                                </tr>
                                <tr>
                                    <td><strong>@SharedLocalizer["LastName"]:</strong></td>
                                    <td>@Model.LastName</td>
                                </tr>
                                <tr>
                                    <td><strong>@SharedLocalizer["DateOfBirth"]:</strong></td>
                                    <td>
                                        @Model.FormattedDateOfBirth
                                        <span class="text-muted">(@Model.Age @SharedLocalizer["YearsOld"])</span>
                                    </td>
                                </tr>
                            </table>
                        </div>
                        <div class="col-md-6">
                            <table class="table table-borderless">
                                <tr>
                                    <td><strong>@SharedLocalizer["Email"]:</strong></td>
                                    <td>@Model.Email</td>
                                </tr>
                                <tr>
                                    <td><strong>@SharedLocalizer["Phone"]:</strong></td>
                                    <td>@Model.FormattedPhone</td>
                                </tr>
                                <tr>
                                    <td><strong>@SharedLocalizer["RegistrationType"]:</strong></td>
                                    <td>
                                        @if (!string.IsNullOrEmpty(Model.RegistrationTypeName))
                                        {
                                            <span class="badge bg-info">@Model.RegistrationTypeName</span>
                                        }
                                        else
                                        {
                                            <span class="badge bg-secondary">@SharedLocalizer["Unknown"]</span>
                                        }
                                    </td>
                                </tr>
                                <tr>
                                    <td><strong>@SharedLocalizer["Status"]:</strong></td>
                                    <td>
                                        @if (Model.IsActive)
                                        {
                                            <span class="badge bg-success">@SharedLocalizer["Active"]</span>
                                        }
                                        else
                                        {
                                            <span class="badge bg-secondary">@SharedLocalizer["Inactive"]</span>
                                        }
                                    </td>
                                </tr>
                            </table>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Address Information Card -->
            <div class="card mb-4">
                <div class="card-header bg-secondary text-white">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-map-marker-alt"></i> @SharedLocalizer["AddressInformation"]
                    </h5>
                </div>
                <div class="card-body">
                    <table class="table table-borderless">
                        <tr>
                            <td><strong>@SharedLocalizer["Address"]:</strong></td>
                            <td>@Model.Address</td>
                        </tr>
                        <tr>
                            <td><strong>@SharedLocalizer["City"]:</strong></td>
                            <td>@Model.City</td>
                        </tr>
                        <tr>
                            <td><strong>@SharedLocalizer["Province"]:</strong></td>
                            <td>@Model.Province</td>
                        </tr>
                        <tr>
                            <td><strong>@SharedLocalizer["PostalCode"]:</strong></td>
                            <td>@Model.PostalCode</td>
                        </tr>
                        <tr>
                            <td><strong>@SharedLocalizer["FullAddress"]:</strong></td>
                            <td>@Model.FormattedAddress</td>
                        </tr>
                    </table>
                </div>
            </div>
        </div>

        <!-- Sidebar with Quick Info -->
        <div class="col-lg-4">
            <div class="card mb-4">
                <div class="card-header bg-info text-white">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-info-circle"></i> @SharedLocalizer["QuickInfo"]
                    </h5>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <small class="text-muted">@SharedLocalizer["RegistrationDate"]</small>
                        <div>@Model.FormattedRegistrationDate</div>
                    </div>
                    <div class="mb-3">
                        <small class="text-muted">@SharedLocalizer["MemberSince"]</small>
                        <div>@((DateTime.Now - Model.DateCreated).Days) @SharedLocalizer["DaysAgo"]</div>
                    </div>
                    <div class="mb-3">
                        <small class="text-muted">@SharedLocalizer["AccountStatus"]</small>
                        <div>
                            @if (Model.IsActive)
                            {
                                <span class="text-success"><i class="fas fa-check-circle"></i> @SharedLocalizer["Active"]</span>
                            }
                            else
                            {
                                <span class="text-warning"><i class="fas fa-exclamation-circle"></i> @SharedLocalizer["Inactive"]</span>
                            }
                        </div>
                    </div>
                </div>
            </div>

            <!-- Help Card -->
            <div class="card">
                <div class="card-header bg-light">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-question-circle"></i> @SharedLocalizer["NeedHelp"]
                    </h5>
                </div>
                <div class="card-body">
                    <p class="small">@SharedLocalizer["MemberHelpText"]</p>
                    <div class="d-grid">
                        <a href="mailto:<EMAIL>" class="btn btn-outline-primary btn-sm">
                            <i class="fas fa-envelope"></i> @SharedLocalizer["ContactSupport"]
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Custom Styles for Member Dashboard -->
<style>
    .btn-lg {
        min-height: 120px;
    }
    
    @@media (max-width: 768px) {
        .btn-lg {
            min-height: 80px;
        }
        
        .btn-lg .fa-2x {
            font-size: 1.5em !important;
        }
    }
    
    .card-title {
        font-weight: 600;
    }
    
    .table td {
        padding: 0.5rem 0.75rem;
        vertical-align: middle;
    }
    
    .badge {
        font-size: 0.9em;
    }
</style>