# Design: Event Subscription Enhancement

## 1. Architecture Overview

### 1.1 System Components

```mermaid
graph TB
    subgraph "Frontend Layer"
        V1[Subscribe Page] --> M[Event Details Modal]
        V2[Public Calendar] --> M
        V3[Admin Calendar] --> M
        V4[Member Calendar] --> M
        M --> JS[Modal JavaScript]
    end

    subgraph "Controller Layer"
        EC[EventsController]
        HC[HomeController]
        AC[AdminController]
        MC[MembersController]
    end

    subgraph "Service Layer"
        ES[EventService]
        UCS[UserContextService]
        ES --> UCS
    end

    subgraph "Data Layer"
        DB[(Database)]
        ES --> DB
        UCS --> DB
    end

    JS --> EC
    JS --> HC
    V1 --> EC
    V2 --> HC
    V3 --> AC
    V4 --> MC
```

### 1.2 Key Design Principles

-   **Leverage Existing Architecture**: Use current EventService and UserContextService
-   **No Database Changes**: Perfect schema already exists
-   **Configuration-Driven**: Environment-specific behavior without code changes
-   **Role-Based Interface**: Different experience per user type (visitor/member/admin)
-   **AJAX-First**: Modal-based registration without page refreshes

## 2. Enhanced User Context Service Integration

### 2.1 Current UserContext Structure ✅

```csharp
public class UserContext
{
    public bool IsAdmin { get; set; }
    public int? AdminId { get; set; }
    public int? MemberId { get; set; }
    public string UserName { get; set; }
    public ActionSource Source { get; set; }
    public string? Email { get; set; }
}
```

### 2.2 Registration Flow Decision Matrix

```mermaid
flowchart TD
    Start[User clicks Register] --> Auth{Authenticated?}

    Auth -->|No| Login[Show Login Prompt]
    Login --> Store[Store Registration Intent]
    Store --> Redirect[Redirect to Login]

    Auth -->|Yes| Context[Get UserContext]
    Context --> Admin{IsAdmin?}

    Admin -->|Yes| Block[Show Admin Message]
    Block --> End1[Cannot Register]

    Admin -->|No| Member{Has MemberId?}
    Member -->|No| Error[Show Member Required]
    Error --> End2[Redirect to Register]

    Member -->|Yes| Check[Check Existing Registration]
    Check --> Exists{Already Registered?}

    Exists -->|Yes| Unregister[Show Unregister Option]
    Exists -->|No| Rules[Check Business Rules]

    Rules --> Full{Event Full?}
    Full -->|Yes| Wait[Show Full Message]

    Full -->|No| Deadline{Past Deadline?}
    Deadline -->|Yes| Closed[Show Closed Message]

    Deadline -->|No| Register[Execute Registration]
    Register --> Success[Show Success + Update UI]
```

## 3. Enhanced Modal System

### 3.1 Modal State Management

```javascript
const ModalState = {
    LOADING: "loading",
    CONTENT: "content",
    ERROR: "error",
};

const UserType = {
    VISITOR: "visitor",
    MEMBER: "member",
    ADMIN: "admin",
};
```

### 3.2 Modal Registration Flow

```mermaid
sequenceDiagram
    participant U as User
    participant M as Modal
    participant JS as JavaScript
    participant C as Controller
    participant S as Service
    participant DB as Database

    U->>M: Click Event
    M->>JS: showEventDetailsModal()
    JS->>C: GET /Home/GetPublicEventDetails
    C->>S: GetEventDetailsWithUserContext()
    S->>DB: Query Event + User Registration Status
    DB->>S: Event Data + Registration State
    S->>C: Enhanced Event DTO
    C->>JS: JSON Response
    JS->>M: Populate Modal + Smart Buttons

    alt User clicks Register
        U->>M: Click Register
        M->>JS: tryRegister()

        alt User is Visitor
            JS->>JS: showLoginPrompt()
            JS->>C: Store Registration Intent
        else User is Member
            JS->>C: POST /Events/JoinAjax
            C->>S: RegisterMemberForEventAsync()
            S->>DB: Create EventRegistration
            DB->>S: Success
            S->>C: Registration DTO
            C->>JS: JSON Success
            JS->>M: Update UI State
        else User is Admin
            JS->>M: Show Admin Message
        end
    end
```

## 4. Enhanced EventsController

### 4.1 New AJAX Endpoints

```csharp
[HttpPost]
[Route("Events/JoinAjax")]
public async Task<IActionResult> JoinAjax([FromBody] EventRegistrationRequest request)
{
    if (!User.Identity.IsAuthenticated)
        return Json(new { success = false, error = "AuthenticationRequired" });

    var userContext = _userContextService.GetCurrentUser();

    // Admin protection
    if (userContext.IsAdmin)
        return Json(new { success = false, error = "AdminCannotRegister" });

    if (!userContext.MemberId.HasValue)
        return Json(new { success = false, error = "MemberAccountRequired" });

    try
    {
        var registration = await _eventService.RegisterMemberForEventAsync(
            request.EventId, userContext.MemberId.Value, request.Notes, request.GuestCount);

        return Json(new {
            success = true,
            message = _localizer["EventSubscriptionSuccess"],
            registration = new {
                id = registration.Id,
                status = registration.Status.ToString(),
                registrationDate = registration.RegistrationDate
            }
        });
    }
    catch (Exception ex)
    {
        return Json(new { success = false, error = _localizer["EventSubscriptionError"] });
    }
}

[HttpPost]
[Route("Events/LeaveAjax")]
public async Task<IActionResult> LeaveAjax([FromBody] EventUnregistrationRequest request)
{
    // Similar pattern for unregistration
}
```

### 4.2 Enhanced Event Details Response

```csharp
public class EventDetailsResponse
{
    public int Id { get; set; }
    public string Title { get; set; }
    public DateTime StartDate { get; set; }
    public DateTime EndDate { get; set; }
    public string? Location { get; set; }
    public string? Description { get; set; }
    public bool RequiresRegistration { get; set; }
    public int MaxParticipants { get; set; }
    public int CurrentRegistrations { get; set; }
    public int AvailableSpots { get; set; }
    public bool IsFull { get; set; }
    public bool IsRegistrationOpen { get; set; }
    public DateTime? RegistrationDeadline { get; set; }
    public bool IsUserRegistered { get; set; }
    public UserRegistrationState UserState { get; set; } // New
    public string? ContactPerson { get; set; }
    public string? ContactEmail { get; set; }
    public string? ContactPhone { get; set; }
}

public enum UserRegistrationState
{
    Visitor,           // Show login prompt
    Member,            // Show register/unregister
    Admin,             // Show admin message
    MemberRegistered,  // Show unregister option
    MemberBlocked      // Show appropriate message
}
```

## 5. Enhanced JavaScript Modal

### 5.1 Registration Intent Storage

```javascript
// Store registration intent for visitors
function storeRegistrationIntent(eventId) {
    sessionStorage.setItem(
        "registrationIntent",
        JSON.stringify({
            eventId: eventId,
            timestamp: Date.now(),
            returnUrl: window.location.href,
        })
    );
}

// Retrieve and execute pending registration
function executePendingRegistration() {
    const intent = sessionStorage.getItem("registrationIntent");
    if (intent) {
        const data = JSON.parse(intent);
        if (Date.now() - data.timestamp < 30 * 60 * 1000) {
            // 30 minutes
            sessionStorage.removeItem("registrationIntent");
            return attemptEventRegistration(data.eventId);
        }
    }
    return false;
}
```

### 5.2 Smart Button Rendering

```javascript
function renderRegistrationButtons(eventData, userState) {
    const buttonContainer = document.getElementById("eventRegistrationButtons");

    switch (userState) {
        case "Visitor":
            buttonContainer.innerHTML = `
                <button class="btn btn-primary" onclick="promptLogin(${
                    eventData.id
                })">
                    <i class="fas fa-sign-in-alt"></i> ${getTranslation(
                        "LoginToRegister"
                    )}
                </button>
                <p class="text-muted mt-2">${getTranslation(
                    "MembershipRequired"
                )}</p>
            `;
            break;

        case "Member":
            if (eventData.isRegistrationOpen && !eventData.isFull) {
                buttonContainer.innerHTML = `
                    <button class="btn btn-primary" onclick="registerForEvent(${
                        eventData.id
                    })" id="registerBtn">
                        <i class="fas fa-user-plus"></i> ${getTranslation(
                            "Register"
                        )}
                    </button>
                `;
            }
            break;

        case "MemberRegistered":
            buttonContainer.innerHTML = `
                <div class="alert alert-success">
                    <i class="fas fa-check"></i> ${getTranslation(
                        "AlreadyRegistered"
                    )}
                </div>
                <button class="btn btn-outline-danger" onclick="unregisterFromEvent(${
                    eventData.id
                })">
                    <i class="fas fa-user-minus"></i> ${getTranslation(
                        "Unregister"
                    )}
                </button>
            `;
            break;

        case "Admin":
            buttonContainer.innerHTML = `
                <div class="alert alert-info">
                    <i class="fas fa-info-circle"></i> ${getTranslation(
                        "AdminManageOnly"
                    )}
                </div>
            `;
            break;
    }
}
```

## 6. Calendar Integration Strategy

### 6.1 Modal Configuration per Calendar

```javascript
const CalendarModalConfig = {
    // Admin Calendar - Management focus
    admin: {
        showRegistrationStatus: true,
        allowRegistration: false,
        showManagementActions: true,
        detailsUrl: "/Admin/GetEventDetails",
    },

    // Subscribe Calendar - Registration focus
    subscribe: {
        showRegistrationStatus: true,
        allowRegistration: true,
        showQuickActions: true,
        detailsUrl: "/Home/GetPublicEventDetails",
    },

    // Public Calendar - Visitor conversion
    public: {
        showRegistrationStatus: false,
        allowRegistration: true,
        emphasizeLogin: true,
        detailsUrl: "/Home/GetPublicEventDetails",
    },

    // Member Calendar - Read-only with status
    member: {
        showRegistrationStatus: true,
        allowRegistration: false,
        showStatusOnly: true,
        detailsUrl: "/Home/GetPublicEventDetails",
    },
};
```

### 6.2 Calendar-Specific Modal Calls

```javascript
// Subscribe Page
showEventDetailsModal(eventId, isAuthenticated, {
    config: CalendarModalConfig.subscribe,
    joinUrl: "/Events/JoinAjax",
    leaveUrl: "/Events/LeaveAjax",
});

// Public Calendar
showEventDetailsModal(eventId, isAuthenticated, {
    config: CalendarModalConfig.public,
    emphasizeConversion: true,
});

// Admin Calendar
showEventDetailsModal(eventId, isAuthenticated, {
    config: CalendarModalConfig.admin,
    showManagementPanel: true,
});
```

## 7. Service Layer Enhancements

### 7.1 Enhanced EventService Methods

```csharp
public async Task<EventDetailsResponse> GetEventDetailsWithUserContextAsync(int eventId, UserContext userContext)
{
    var eventEntity = await GetEventByIdAsync(eventId);
    if (eventEntity == null) return null;

    var response = _mapper.Map<EventDetailsResponse>(eventEntity);

    // Determine user registration state
    if (!userContext.IsAdmin && userContext.MemberId.HasValue)
    {
        var registration = await GetEventRegistrationAsync(eventId, userContext.MemberId.Value);
        response.IsUserRegistered = registration != null;
        response.UserState = registration != null
            ? UserRegistrationState.MemberRegistered
            : UserRegistrationState.Member;
    }
    else if (userContext.IsAdmin)
    {
        response.UserState = UserRegistrationState.Admin;
    }
    else
    {
        response.UserState = UserRegistrationState.Visitor;
    }

    return response;
}

public async Task<bool> CanUserRegisterAsync(int eventId, UserContext userContext)
{
    // Admin check
    if (userContext.IsAdmin) return false;

    // Member check
    if (!userContext.MemberId.HasValue) return false;

    // Existing registration check
    var existing = await GetEventRegistrationAsync(eventId, userContext.MemberId.Value);
    if (existing != null) return false;

    // Event business rules
    var eventEntity = await GetEventByIdAsync(eventId);
    return eventEntity?.IsRegistrationOpen == true && !eventEntity.IsFull;
}
```

## 8. Quick Registration Actions

### 8.1 Event List Item Enhancement

```html
<!-- Enhanced event list item with quick actions -->
<div class="list-group-item event-list-item" data-event-id="@evt.Id">
    <div class="d-flex w-100 justify-content-between align-items-start">
        <div class="flex-grow-1" onclick="showEventDetails(@evt.Id)">
            <!-- Event details content -->
        </div>
        <div class="ms-3 flex-shrink-0">
            @if (User.Identity.IsAuthenticated)
            {
                @if (ViewBag.UserContext.IsAdmin)
                {
                    <span class="badge bg-secondary">@SharedLocalizer["AdminView"]</span>
                }
                else if (evt.IsUserRegistered)
                {
                    <button class="btn btn-sm btn-outline-danger quick-unregister"
                            data-event-id="@evt.Id"
                            title="@SharedLocalizer["Unregister"]">
                        <i class="fas fa-user-minus"></i>
                    </button>
                }
                else if (evt.IsRegistrationOpen && !evt.IsFull)
                {
                    <button class="btn btn-sm btn-primary quick-register"
                            data-event-id="@evt.Id"
                            title="@SharedLocalizer["QuickRegister"]">
                        <i class="fas fa-user-plus"></i>
                    </button>
                }
            }
            else
            {
                <button class="btn btn-sm btn-outline-primary"
                        onclick="promptLogin(@evt.Id)">
                    <i class="fas fa-sign-in-alt"></i> @SharedLocalizer["Login"]
                </button>
            }
        </div>
    </div>
</div>
```

### 8.2 Quick Action JavaScript

```javascript
// Quick registration without opening modal
async function quickRegister(eventId) {
    const button = document.querySelector(
        `[data-event-id="${eventId}"].quick-register`
    );

    // Show loading state
    button.disabled = true;
    button.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';

    try {
        const response = await fetch("/Events/JoinAjax", {
            method: "POST",
            headers: { "Content-Type": "application/json" },
            body: JSON.stringify({ eventId: eventId }),
        });

        const result = await response.json();

        if (result.success) {
            // Update button to unregister state
            button.className = "btn btn-sm btn-outline-danger quick-unregister";
            button.innerHTML = '<i class="fas fa-user-minus"></i>';
            button.title = getTranslation("Unregister");

            // Show success toast
            showToast("success", result.message);
        } else {
            // Show error and revert button
            showToast("error", result.error);
            button.innerHTML = '<i class="fas fa-user-plus"></i>';
        }
    } catch (error) {
        showToast("error", "Registration failed");
        button.innerHTML = '<i class="fas fa-user-plus"></i>';
    } finally {
        button.disabled = false;
    }
}
```

## 9. Mobile Responsiveness

### 9.1 Modal Responsive Design

```css
/* Enhanced modal for mobile */
@media (max-width: 576px) {
    .modal-dialog {
        margin: 0.5rem;
        max-width: calc(100% - 1rem);
    }

    .event-registration-buttons {
        display: flex;
        flex-direction: column;
        gap: 0.5rem;
    }

    .event-registration-buttons .btn {
        width: 100%;
        justify-content: center;
    }

    .quick-register,
    .quick-unregister {
        min-width: 44px; /* Touch target size */
        min-height: 44px;
    }
}
```

## 10. Error Handling & Feedback

### 10.1 Comprehensive Error States

```javascript
const ErrorHandling = {
    AuthenticationRequired: () => promptLogin(),
    AdminCannotRegister: () =>
        showMessage("info", getTranslation("AdminManageOnly")),
    EventFull: () => showMessage("warning", getTranslation("EventFull")),
    RegistrationClosed: () =>
        showMessage("warning", getTranslation("RegistrationClosed")),
    AlreadyRegistered: () =>
        showMessage("info", getTranslation("AlreadyRegistered")),
    NetworkError: () => showMessage("error", getTranslation("NetworkError")),
    MemberAccountRequired: () =>
        showMessage("warning", getTranslation("MemberAccountRequired")),
};
```

## 11. Localization Strategy

### 11.1 New Localization Keys

```json
{
    "LoginToRegister": "Login to Register",
    "MembershipRequired": "Membership required to register for events",
    "AdminManageOnly": "Admins manage events but cannot register as participants",
    "QuickRegister": "Quick Register",
    "RegistrationSuccess": "Successfully registered for event!",
    "UnregistrationSuccess": "Successfully unregistered from event",
    "NetworkError": "Network error occurred. Please try again.",
    "RegistrationIntent": "Complete your registration for {0}",
    "WelcomeBackRegister": "Welcome back! Ready to register for {0}?"
}
```

This design leverages the existing excellent architecture while adding the missing pieces for a complete, professional event subscription system that handles all user scenarios elegantly.
