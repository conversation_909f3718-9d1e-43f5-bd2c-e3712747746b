# Test Production Site After Migration Fix
# Run this script ON THE SIMBA SERVER

Write-Host "=== Testing Production Site ===" -ForegroundColor Green

# Restart IIS Application Pool to ensure clean start
Write-Host "`n🔄 Restarting Production Application Pool..." -ForegroundColor Yellow

try {
    $appcmd = "${env:windir}\system32\inetsrv\appcmd.exe"
    
    if (Test-Path $appcmd) {
        Write-Host "Stopping ParaHockey-Production..." -ForegroundColor Cyan
        & $appcmd stop apppool "ParaHockey-Production" 2>&1 | Out-Null
        
        Start-Sleep -Seconds 3
        
        Write-Host "Starting ParaHockey-Production..." -ForegroundColor Cyan
        & $appcmd start apppool "ParaHockey-Production" 2>&1 | Out-Null
        
        Write-Host "✅ Application pool restarted" -ForegroundColor Green
    }
} catch {
    Write-Warning "Could not restart app pool: $_"
}

# Test manual startup to verify no errors
Write-Host "`n🧪 Testing application startup..." -ForegroundColor Yellow

$prodPath = "C:\inetpub\ParaHockey\Production"
$dllPath = Join-Path $prodPath "ParaHockeyApp.dll"

if (Test-Path $dllPath) {
    try {
        # Set environment for Production
        $env:ASPNETCORE_ENVIRONMENT = "Production"
        
        Write-Host "Testing dotnet ParaHockeyApp.dll..." -ForegroundColor Cyan
        
        # Start process and capture output
        $processInfo = New-Object System.Diagnostics.ProcessStartInfo
        $processInfo.FileName = "dotnet"
        $processInfo.Arguments = "`"$dllPath`""
        $processInfo.WorkingDirectory = $prodPath
        $processInfo.RedirectStandardOutput = $true
        $processInfo.RedirectStandardError = $true
        $processInfo.UseShellExecute = $false
        $processInfo.CreateNoWindow = $true
        
        $process = New-Object System.Diagnostics.Process
        $process.StartInfo = $processInfo
        $process.Start() | Out-Null
        
        # Wait for startup (longer wait to ensure full startup)
        Write-Host "Waiting for application startup..." -ForegroundColor Cyan
        Start-Sleep -Seconds 10
        
        # Check if process is still running (good sign)
        if (!$process.HasExited) {
            Write-Host "✅ APPLICATION STARTED SUCCESSFULLY!" -ForegroundColor Green -BackgroundColor DarkGreen
            Write-Host "   Process is running without errors" -ForegroundColor Green
            
            # Kill the test process
            $process.Kill()
            Write-Host "   Test process terminated" -ForegroundColor Gray
        } else {
            Write-Host "❌ Application exited during startup" -ForegroundColor Red
            Write-Host "   Exit Code: $($process.ExitCode)" -ForegroundColor Red
        }
        
        # Get any output
        $stdout = $process.StandardOutput.ReadToEnd()
        $stderr = $process.StandardError.ReadToEnd()
        
        if ($stdout) {
            Write-Host "`n📤 Startup Output:" -ForegroundColor Green
            Write-Host $stdout -ForegroundColor White
        }
        
        if ($stderr) {
            Write-Host "`n🔴 Error Output:" -ForegroundColor Red
            Write-Host $stderr -ForegroundColor White
        }
        
    } catch {
        Write-Host "❌ Could not test startup: $_" -ForegroundColor Red
    }
} else {
    Write-Host "❌ ParaHockeyApp.dll not found at: $dllPath" -ForegroundColor Red
}

Write-Host "`n=== Test Results ===" -ForegroundColor Green

if ($stderr -or $process.HasExited) {
    Write-Host "🔴 ISSUES DETECTED - Check error output above" -ForegroundColor Red
} else {
    Write-Host "✅ PRODUCTION SITE SHOULD NOW BE WORKING!" -ForegroundColor Green -BackgroundColor DarkGreen
    Write-Host "`n🌐 Try accessing: https://parahockey.complys.com" -ForegroundColor Cyan
    Write-Host "🔐 Admin login should work with Azure AD" -ForegroundColor Cyan
}

Write-Host "`nPress any key to exit..." -ForegroundColor Yellow
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")