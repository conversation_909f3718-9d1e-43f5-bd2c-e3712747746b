# Enable detailed error logging for Production site debugging
# Run this script ON THE SIMBA SERVER as Administrator

Write-Host "=== Enabling Production Debug Logging ===" -ForegroundColor Green

$prodPath = "C:\inetpub\ParaHockey\Production"
$webConfigPath = Join-Path $prodPath "web.config"

if (Test-Path $webConfigPath) {
    Write-Host "✅ Found web.config at: $webConfigPath" -ForegroundColor Green
    
    # Backup current web.config
    Copy-Item $webConfigPath "$webConfigPath.backup" -Force
    Write-Host "✅ Backed up web.config" -ForegroundColor Green
    
    # Read and modify web.config to enable stdout logging
    [xml]$webConfig = Get-Content $webConfigPath
    
    $aspNetCoreNode = $webConfig.SelectSingleNode("//system.webServer/aspNetCore")
    if ($aspNetCoreNode -ne $null) {
        # Enable stdout logging
        $aspNetCoreNode.SetAttribute("stdoutLogEnabled", "true")
        $aspNetCoreNode.SetAttribute("stdoutLogFile", ".\logs\stdout")
        
        Write-Host "✅ Enabled stdout logging" -ForegroundColor Green
        
        # Create logs directory
        $logsPath = Join-Path $prodPath "logs"
        if (!(Test-Path $logsPath)) {
            New-Item -ItemType Directory -Path $logsPath -Force
            Write-Host "✅ Created logs directory" -ForegroundColor Green
        }
        
        # Set permissions on logs directory
        try {
            $acl = Get-Acl $logsPath
            $accessRule = New-Object System.Security.AccessControl.FileSystemAccessRule("IIS_IUSRS", "FullControl", "ContainerInherit,ObjectInherit", "None", "Allow")
            $acl.SetAccessRule($accessRule)
            Set-Acl $logsPath $acl
            Write-Host "✅ Set permissions on logs directory" -ForegroundColor Green
        } catch {
            Write-Warning "Could not set permissions on logs directory: $_"
        }
        
        # Save the modified web.config
        $webConfig.Save($webConfigPath)
        Write-Host "✅ Updated web.config with debug logging" -ForegroundColor Green
        
        # Show the updated aspNetCore section
        Write-Host "`n📋 Updated aspNetCore configuration:" -ForegroundColor Cyan
        Write-Host $aspNetCoreNode.OuterXml -ForegroundColor White
        
    } else {
        Write-Error "❌ Could not find aspNetCore node in web.config"
        exit 1
    }
    
} else {
    Write-Error "❌ web.config not found at: $webConfigPath"
    exit 1
}

# Stop and start the application pool to reload configuration
Write-Host "`n=== Restarting Production Site ===" -ForegroundColor Yellow

Import-Module WebAdministration -ErrorAction SilentlyContinue

if (Get-Module WebAdministration) {
    try {
        # Stop website and app pool
        Stop-Website -Name "ParaHockey-Production" -ErrorAction SilentlyContinue
        Stop-WebAppPool -Name "ParaHockey-Production" -ErrorAction SilentlyContinue
        Write-Host "✅ Stopped website and app pool" -ForegroundColor Green
        
        # Wait a moment
        Start-Sleep -Seconds 5
        
        # Start app pool and website
        Start-WebAppPool -Name "ParaHockey-Production"
        Start-Sleep -Seconds 3
        Start-Website -Name "ParaHockey-Production"
        Write-Host "✅ Started website and app pool" -ForegroundColor Green
        
    } catch {
        Write-Warning "Issue with IIS restart: $_"
    }
} else {
    Write-Warning "WebAdministration module not available. Please restart IIS manually."
}

Write-Host "`n=== Next Steps ===" -ForegroundColor Yellow
Write-Host "1. Try to access the Production website" -ForegroundColor White
Write-Host "2. Check the logs directory for detailed error information:" -ForegroundColor White
Write-Host "   $prodPath\logs\" -ForegroundColor Cyan
Write-Host "3. Look for stdout log files with timestamp names" -ForegroundColor White
Write-Host "4. Send me the contents of the latest log file to diagnose the issue" -ForegroundColor White

Write-Host "`n🔍 To view latest log:" -ForegroundColor Yellow
Write-Host "Get-ChildItem '$prodPath\logs\' | Sort-Object LastWriteTime -Descending | Select-Object -First 1 | Get-Content" -ForegroundColor Cyan

Write-Host "`nPress any key to continue..." -ForegroundColor Yellow
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")