@using Microsoft.Extensions.Localization
@inject IStringLocalizer<ParaHockeyApp.Resources.SharedResourceMarker> Localizer

@{
    ViewData["Title"] = Localizer["HomePageTitle"];
}

<!-- Skip navigation links for accessibility -->
<a href="#main-content" class="skip-link visually-hidden-focusable">@Localizer["SkipToMainContent"]</a>

<main id="main-content" role="main" aria-label="@Localizer["MainContentArea"]">
    <!-- Hero Section -->
    <section class="ph-hero" aria-labelledby="hero-title" role="banner">
        <div class="ph-container">
            <div class="row align-items-center min-vh-75">
                <div class="col-lg-6">
                    <div class="hero-content">
                        <div class="logo-container ph-mb-4">
                            <img src="~/assets/logos/parahockey-01.jpg" 
                                 alt="@Localizer["ParahockeyLogoAlt"]" 
                                 class="hero-logo"
                                 loading="lazy"
                                 decoding="async"
                                 width="200"
                                 height="150">
                        </div>
                        <h1 id="hero-title" class="ph-hero-title">@Localizer["WelcomeTitle"]</h1>
                        <p class="ph-hero-subtitle">
                            @Localizer["WelcomeSubtitle"]
                        </p>
                        <nav class="ph-hero-actions" aria-label="@Localizer["PrimaryActions"]">
                            <a href="@Url.Action("Register", "Members")" 
                               class="ph-btn ph-btn-primary ph-btn-lg"
                               aria-describedby="register-desc">
                                <i class="fas fa-user-plus" aria-hidden="true"></i> 
                                @Localizer["RegisterNow"]
                            </a>
                            <span id="register-desc" class="visually-hidden">@Localizer["RegisterButtonDesc"]</span>
                            
                            <a href="@Url.Action("PublicCalendar", "Home")" 
                               class="ph-btn ph-btn-outline ph-btn-lg"
                               aria-describedby="calendar-desc">
                                <i class="fas fa-calendar-alt" aria-hidden="true"></i> 
                                @Localizer["ViewCalendar"]
                            </a>
                            <span id="calendar-desc" class="visually-hidden">@Localizer["ViewCalendarDesc"]</span>
                            
                            <a href="@Url.Action("Subscribe", "Events")" 
                               class="ph-btn ph-btn-outline ph-btn-lg"
                               aria-describedby="events-desc">
                                <i class="fas fa-calendar-check" aria-hidden="true"></i> 
                                @Localizer["SubscribeToEvents"]
                            </a>
                            <span id="events-desc" class="visually-hidden">@Localizer["SubscribeEventsDesc"]</span>
                        </nav>
                    </div>
                </div>
                <div class="col-lg-6">
                    <aside class="hero-image" aria-labelledby="community-stats">
                        <div class="ph-stat-card">
                            <h2 id="community-stats">@Localizer["CommunityTitle"]</h2>
                            <div class="ph-stats-grid" role="group" aria-label="@Localizer["CommunityStatsLabel"]">
                                <div class="stat-item">
                                    <div class="ph-stat-number" aria-label="@Localizer["PlayersCount"]">150+</div>
                                    <div class="ph-stat-label">@Localizer["ActivePlayers"]</div>
                                </div>
                                <div class="stat-item">
                                    <div class="ph-stat-number" aria-label="@Localizer["TeamsCount"]">12</div>
                                    <div class="ph-stat-label">@Localizer["Teams"]</div>
                                </div>
                                <div class="stat-item">
                                    <div class="ph-stat-number" aria-label="@Localizer["CoachesCount"]">25</div>
                                    <div class="ph-stat-label">@Localizer["Coaches"]</div>
                                </div>
                                <div class="stat-item">
                                    <div class="ph-stat-number" aria-label="@Localizer["VolunteersCount"]">50+</div>
                                    <div class="ph-stat-label">@Localizer["Volunteers"]</div>
                                </div>
                            </div>
                        </div>
                    </aside>
                </div>
            </div>
        </div>
    </section>

    <!-- Registration Types Section -->
    <section class="ph-features" aria-labelledby="registration-types">
        <div class="ph-container">
            <header class="ph-section-header">
                <h2 id="registration-types" class="ph-section-title">@Localizer["RegistrationTypesTitle"]</h2>
                <p class="ph-section-subtitle">@Localizer["RegistrationTypesSubtitle"]</p>
            </header>
            <div class="ph-feature-grid" role="list" aria-label="@Localizer["RegistrationTypesList"]">
                <article class="ph-feature-card" role="listitem">
                    <div class="ph-feature-icon" aria-hidden="true">
                        <i class="fas fa-child"></i>
                    </div>
                    <h3 class="ph-feature-title">@Localizer["Junior"]</h3>
                    <p class="ph-feature-description">@Localizer["JuniorDesc"]</p>
                </article>
                <article class="ph-feature-card" role="listitem">
                    <div class="ph-feature-icon" aria-hidden="true">
                        <i class="fas fa-graduation-cap"></i>
                    </div>
                    <h3 class="ph-feature-title">@Localizer["Development"]</h3>
                    <p class="ph-feature-description">@Localizer["DevelopmentDesc"]</p>
                </article>
                <article class="ph-feature-card" role="listitem">
                    <div class="ph-feature-icon" aria-hidden="true">
                        <i class="fas fa-trophy"></i>
                    </div>
                    <h3 class="ph-feature-title">@Localizer["Elite"]</h3>
                    <p class="ph-feature-description">@Localizer["EliteDesc"]</p>
                </article>
                <article class="ph-feature-card" role="listitem">
                    <div class="ph-feature-icon" aria-hidden="true">
                        <i class="fas fa-whistle"></i>
                    </div>
                    <h3 class="ph-feature-title">@Localizer["Coach"]</h3>
                    <p class="ph-feature-description">@Localizer["CoachDesc"]</p>
                </article>
                <article class="ph-feature-card" role="listitem">
                    <div class="ph-feature-icon" aria-hidden="true">
                        <i class="fas fa-hands-helping"></i>
                    </div>
                    <h3 class="ph-feature-title">@Localizer["Volunteer"]</h3>
                    <p class="ph-feature-description">@Localizer["VolunteerDesc"]</p>
                </article>
                <article class="ph-feature-card" role="listitem">
                    <div class="ph-feature-icon" aria-hidden="true">
                        <i class="fas fa-users"></i>
                    </div>
                    <h3 class="ph-feature-title">@Localizer["FamilyFriends"]</h3>
                    <p class="ph-feature-description">@Localizer["FamilyFriendsDesc"]</p>
                </article>
            </div>
        </div>
    </section>

    <!-- Call to Action Section -->
    <section class="cta-section" aria-labelledby="cta-title" role="region">
        <div class="ph-container">
            <div class="row">
                <div class="col-12 text-center">
                    <h2 id="cta-title" class="text-white ph-mb-4">@Localizer["CtaTitle"]</h2>
                    <p class="text-white ph-mb-4">
                        @Localizer["CtaSubtitle"]
                    </p>
                    <a href="@Url.Action("Register", "Members")" 
                       class="ph-btn ph-btn-secondary ph-btn-lg"
                       aria-describedby="cta-desc">
                        <i class="fas fa-hockey-puck" aria-hidden="true"></i> 
                        @Localizer["CtaButton"]
                    </a>
                    <span id="cta-desc" class="visually-hidden">@Localizer["CtaButtonDesc"]</span>
                </div>
            </div>
        </div>
    </section>
</main>

