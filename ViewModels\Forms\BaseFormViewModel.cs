using ParaHockeyApp.Attributes;
using System.ComponentModel.DataAnnotations;

namespace ParaHockeyApp.ViewModels.Forms
{
    /// <summary>
    /// Base class for form ViewModels with common validation and functionality
    /// </summary>
    public abstract class BaseFormViewModel
    {
        /// <summary>
        /// Indicates if the form is in edit mode (vs create mode)
        /// </summary>
        public bool IsEditMode { get; set; }

        /// <summary>
        /// Form submission timestamp for tracking
        /// </summary>
        public DateTime SubmissionTimestamp { get; set; } = DateTime.UtcNow;

        /// <summary>
        /// Culture code for localization
        /// </summary>
        public string CultureCode { get; set; } = "fr-CA";

        /// <summary>
        /// Validates the model and returns validation results
        /// </summary>
        /// <returns>Collection of validation results</returns>
        public virtual IEnumerable<ValidationResult> Validate()
        {
            var context = new ValidationContext(this);
            var results = new List<ValidationResult>();
            Validator.TryValidateObject(this, context, results, true);
            return results;
        }

        /// <summary>
        /// Checks if the model is valid
        /// </summary>
        /// <returns>True if valid, false otherwise</returns>
        public virtual bool IsValid()
        {
            return !Validate().Any();
        }

        /// <summary>
        /// Gets client-side validation attributes for a property
        /// </summary>
        /// <param name="propertyName">Property name</param>
        /// <returns>Dictionary of validation attributes</returns>
        public virtual Dictionary<string, object> GetClientValidationAttributes(string propertyName)
        {
            var attributes = new Dictionary<string, object>();
            var property = GetType().GetProperty(propertyName);
            
            if (property == null) return attributes;

            var validationAttributes = property.GetCustomAttributes(typeof(ValidationAttribute), true)
                .Cast<ValidationAttribute>();

            foreach (var attr in validationAttributes)
            {
                switch (attr)
                {
                    case RequiredAttribute:
                        attributes["required"] = "required";
                        attributes["data-val"] = "true";
                        attributes["data-val-required"] = attr.ErrorMessage ?? "This field is required.";
                        break;
                        
                    case StringLengthAttribute stringLength:
                        attributes["maxlength"] = stringLength.MaximumLength.ToString();
                        attributes["data-val"] = "true";
                        attributes["data-val-length"] = attr.ErrorMessage ?? $"Maximum {stringLength.MaximumLength} characters.";
                        attributes["data-val-length-max"] = stringLength.MaximumLength.ToString();
                        if (stringLength.MinimumLength > 0)
                        {
                            attributes["data-val-length-min"] = stringLength.MinimumLength.ToString();
                        }
                        break;
                        
                    case EmailAddressAttribute:
                        attributes["type"] = "email";
                        attributes["data-val"] = "true";
                        attributes["data-val-email"] = attr.ErrorMessage ?? "Please enter a valid email address.";
                        break;
                        
                    case PhoneAttribute:
                        attributes["type"] = "tel";
                        attributes["data-val"] = "true";
                        attributes["data-val-phone"] = attr.ErrorMessage ?? "Please enter a valid phone number.";
                        break;
                        
                    case RegularExpressionAttribute regex:
                        attributes["pattern"] = regex.Pattern;
                        attributes["data-val"] = "true";
                        attributes["data-val-regex"] = attr.ErrorMessage ?? "Invalid format.";
                        attributes["data-val-regex-pattern"] = regex.Pattern;
                        break;
                }
            }

            return attributes;
        }

        /// <summary>
        /// Gets HTML5 input type for a property
        /// </summary>
        /// <param name="propertyName">Property name</param>
        /// <returns>HTML5 input type</returns>
        public virtual string GetInputType(string propertyName)
        {
            var property = GetType().GetProperty(propertyName);
            if (property == null) return "text";

            // Check for specific data type attributes
            var dataTypeAttr = property.GetCustomAttributes(typeof(DataTypeAttribute), true)
                .Cast<DataTypeAttribute>().FirstOrDefault();

            if (dataTypeAttr != null)
            {
                return dataTypeAttr.DataType switch
                {
                    DataType.EmailAddress => "email",
                    DataType.Password => "password",
                    DataType.PhoneNumber => "tel",
                    DataType.Date => "date",
                    DataType.DateTime => "datetime-local",
                    DataType.Time => "time",
                    DataType.Url => "url",
                    _ => "text"
                };
            }

            // Check for validation attributes that imply input type
            var validationAttrs = property.GetCustomAttributes(typeof(ValidationAttribute), true)
                .Cast<ValidationAttribute>();

            foreach (var attr in validationAttrs)
            {
                switch (attr)
                {
                    case EmailAddressAttribute:
                        return "email";
                    case PhoneAttribute:
                        return "tel";
                }
            }

            // Check property type
            var propertyType = Nullable.GetUnderlyingType(property.PropertyType) ?? property.PropertyType;
            
            return propertyType.Name switch
            {
                nameof(DateTime) => "date",
                nameof(Int32) or nameof(Int64) or nameof(Decimal) or nameof(Double) or nameof(Single) => "number",
                _ => "text"
            };
        }

        /// <summary>
        /// Gets input mode for mobile keyboards
        /// </summary>
        /// <param name="propertyName">Property name</param>
        /// <returns>Input mode value</returns>
        public virtual string GetInputMode(string propertyName)
        {
            var inputType = GetInputType(propertyName);
            
            return inputType switch
            {
                "email" => "email",
                "tel" => "tel",
                "number" => "numeric",
                "url" => "url",
                _ => "text"
            };
        }

        /// <summary>
        /// Gets HTML5 validation attributes for a property
        /// </summary>
        /// <param name="propertyName">Property name</param>
        /// <returns>Dictionary of HTML5 validation attributes</returns>
        public virtual Dictionary<string, string> GetHtml5ValidationAttributes(string propertyName)
        {
            var attributes = new Dictionary<string, string>();
            var property = GetType().GetProperty(propertyName);
            
            if (property == null) return attributes;

            var validationAttributes = property.GetCustomAttributes(typeof(ValidationAttribute), true)
                .Cast<ValidationAttribute>();

            foreach (var attr in validationAttributes)
            {
                switch (attr)
                {
                    case RequiredAttribute:
                        attributes["required"] = "required";
                        attributes["aria-required"] = "true";
                        break;
                        
                    case StringLengthAttribute stringLength:
                        if (stringLength.MaximumLength > 0)
                        {
                            attributes["maxlength"] = stringLength.MaximumLength.ToString();
                        }
                        if (stringLength.MinimumLength > 0)
                        {
                            attributes["minlength"] = stringLength.MinimumLength.ToString();
                        }
                        break;
                        
                    case RegularExpressionAttribute regex:
                        attributes["pattern"] = regex.Pattern;
                        break;
                        
                    case RangeAttribute range:
                        attributes["min"] = range.Minimum.ToString();
                        attributes["max"] = range.Maximum.ToString();
                        break;
                }
            }

            // Set input type
            attributes["type"] = GetInputType(propertyName);
            
            // Set input mode
            attributes["inputmode"] = GetInputMode(propertyName);

            // Add autocomplete for common fields
            var autocomplete = GetAutocompleteAttribute(propertyName);
            if (!string.IsNullOrEmpty(autocomplete))
            {
                attributes["autocomplete"] = autocomplete;
            }

            return attributes;
        }

        /// <summary>
        /// Gets autocomplete attribute for common form fields
        /// </summary>
        /// <param name="propertyName">Property name</param>
        /// <returns>Autocomplete attribute value</returns>
        public virtual string GetAutocompleteAttribute(string propertyName)
        {
            return propertyName.ToLowerInvariant() switch
            {
                "firstname" => "given-name",
                "lastname" => "family-name",
                "email" => "email",
                "phone" => "tel",
                "streetaddress" => "street-address",
                "city" => "address-level2",
                "province" => "address-level1",
                "postalcode" => "postal-code",
                "dateofbirth" => "bday",
                _ => ""
            };
        }

        /// <summary>
        /// Gets placeholder text for a property based on culture
        /// </summary>
        /// <param name="propertyName">Property name</param>
        /// <returns>Placeholder text</returns>
        public virtual string GetPlaceholder(string propertyName)
        {
            return propertyName.ToLowerInvariant() switch
            {
                "firstname" => CultureCode.StartsWith("fr") ? "Votre prénom" : "Your first name",
                "lastname" => CultureCode.StartsWith("fr") ? "Votre nom de famille" : "Your last name",
                "email" => CultureCode.StartsWith("fr") ? "<EMAIL>" : "<EMAIL>",
                "phone" => "(*************",
                "streetaddress" => CultureCode.StartsWith("fr") ? "123 Rue de la Patinoire" : "123 Rink Avenue",
                "city" => CultureCode.StartsWith("fr") ? "Montréal" : "Montreal",
                "postalcode" => "H1H 1H1",
                "dateofbirth" => CultureCode.StartsWith("fr") ? "AAAA-MM-JJ" : "YYYY-MM-DD",
                _ => ""
            };
        }
    }
}