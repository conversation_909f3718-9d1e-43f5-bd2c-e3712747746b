using ParaHockeyApp.Models.Entities;

namespace ParaHockeyApp.Services
{
    /// <summary>
    /// Service interface for tracking the current user context during operations.
    /// This helps distinguish between admin actions and member self-service actions.
    /// </summary>
    public interface IUserContextService
    {
        /// <summary>
        /// Gets the current user context for audit logging
        /// </summary>
        UserContext GetCurrentUser();

        /// <summary>
        /// Sets the current user context (used by authentication middleware)
        /// </summary>
        void SetCurrentUser(UserContext userContext);

        /// <summary>
        /// Gets the client IP address for the current request
        /// </summary>
        string? GetClientIPAddress();
    }

    /// <summary>
    /// Represents the current user context for audit logging
    /// </summary>
    public class UserContext
    {
        /// <summary>
        /// Whether the current user is an admin
        /// </summary>
        public bool IsAdmin { get; set; }

        /// <summary>
        /// Admin ID if the current user is an admin
        /// </summary>
        public int? AdminId { get; set; }

        /// <summary>
        /// Member ID if the current user is a member
        /// </summary>
        public int? MemberId { get; set; }

        /// <summary>
        /// Display name of the current user
        /// </summary>
        public string UserName { get; set; } = string.Empty;

        /// <summary>
        /// Source of the current action
        /// </summary>
        public ActionSource Source { get; set; }

        /// <summary>
        /// Email of the current user
        /// </summary>
        public string? Email { get; set; }

        /// <summary>
        /// Whether this is a system operation (no user)
        /// </summary>
        public bool IsSystem => Source == ActionSource.System;
    }
}