@using Microsoft.AspNetCore.Mvc.Localization
@inject IHtmlLocalizer<ParaHockeyApp.Resources.SharedResourceMarker> SharedLocalizer

<!-- Event Details Modal -->
<div class="modal fade" id="eventDetailsModal" tabindex="-1" role="dialog" aria-labelledby="eventDetailsModalTitle" aria-hidden="true">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header bg-primary text-white">
                <h5 class="modal-title" id="eventDetailsModalTitle">
                    <i class="fas fa-calendar-day"></i> @SharedLocalizer["EventDetails"]
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="@SharedLocalizer["Close"]"></button>
            </div>
            <div class="modal-body">
                <div id="eventDetailsContent">
                    <!-- Event details will be loaded here -->
                    <h3 id="eventTitle" class="mb-3"></h3>
                    
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <p class="mb-2">
                                <i class="fas fa-calendar" aria-hidden="true"></i> <strong>@SharedLocalizer["Date"]:</strong>
                                <span id="eventDate"></span>
                            </p>
                            <p class="mb-2">
                                <i class="fas fa-clock" aria-hidden="true"></i> <strong>@SharedLocalizer["Time"]:</strong>
                                <span id="eventTime"></span>
                            </p>
                        </div>
                        <div class="col-md-6">
                            <p class="mb-2">
                                <i class="fas fa-map-marker-alt" aria-hidden="true"></i> <strong>@SharedLocalizer["Location"]:</strong>
                                <span id="eventLocation"></span>
                            </p>
                            <p class="mb-2" id="eventSpotsContainer" style="display: none;">
                                <i class="fas fa-users" aria-hidden="true"></i> <strong>@SharedLocalizer["SpotsAvailable"]:</strong>
                                <span id="eventSpots"></span>
                            </p>
                        </div>
                    </div>
                    
                    <div id="eventDescriptionContainer" class="mb-3" style="display: none;">
                        <h5>@SharedLocalizer["Description"]</h5>
                        <p id="eventDescription"></p>
                    </div>
                    
                    <div id="eventContactContainer" class="mb-3" style="display: none;">
                        <h5>@SharedLocalizer["Contact"]</h5>
                        <p id="eventContact"></p>
                    </div>
                    
                    <div id="eventRegistrationDeadlineContainer" class="alert alert-warning" style="display: none;" role="alert">
                        <i class="fas fa-hourglass-half" aria-hidden="true"></i> <strong>@SharedLocalizer["RegistrationDeadline"]:</strong>
                        <span id="eventRegistrationDeadline"></span>
                    </div>
                    
                    <div id="eventFullAlert" class="alert alert-danger" style="display: none;" role="alert">
                        <i class="fas fa-times-circle" aria-hidden="true"></i> @SharedLocalizer["AlreadyFull"]
                    </div>
                    
                    <div id="eventAlreadyRegisteredAlert" class="alert alert-success" style="display: none;" role="alert">
                        <i class="fas fa-check-circle" aria-hidden="true"></i> @SharedLocalizer["YouAreRegistered"]
                    </div>
                    
                    <!-- Registration buttons container for JavaScript -->
                    <div id="eventRegistrationButtons" class="mt-3"></div>
                </div>
                
                <div id="eventDetailsLoading" class="text-center py-4" style="display: none;">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">@SharedLocalizer["Loading"]...</span>
                    </div>
                </div>
                
                <div id="eventDetailsError" class="alert alert-danger" style="display: none;" role="alert">
                    <i class="fas fa-exclamation-circle" aria-hidden="true"></i> @SharedLocalizer["ErrorLoadingEventDetails"]
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">@SharedLocalizer["Close"]</button>
                <button type="button" id="registerForEventBtn" class="btn btn-primary" style="display: none;">
                    <i class="fas fa-user-plus"></i> @SharedLocalizer["RegisterForEvent"]
                </button>
                <button type="button" id="unregisterFromEventBtn" class="btn btn-warning" style="display: none;">
                    <i class="fas fa-user-minus"></i> @SharedLocalizer["Unregister"]
                </button>
            </div>
        </div>
    </div>
</div>

<style>
    /* Event Details Modal Responsiveness */
    #eventDetailsModal .modal-body {
        max-height: 90vh;
        overflow-y: auto;
    }
    
    @@media (max-width: 768px) {
        #eventDetailsModal .modal-dialog {
            margin: 0.5rem;
        }
        
        #eventDetailsModal .modal-body {
            max-height: 80vh;
            padding: 1rem;
        }
        
        #eventDetailsModal .row {
            margin: 0;
        }
        
        #eventDetailsModal .col-md-6 {
            padding: 0;
            margin-bottom: 1rem;
        }
    }
    
    /* Modal content styling */
    #eventDetailsModal h3 {
        color: #007bff;
        word-wrap: break-word;
    }
    
    #eventDetailsModal .alert {
        margin-bottom: 1rem;
    }
    
    #eventDetailsModal p {
        margin-bottom: 0.5rem;
    }
    
    /* Mobile-responsive registration buttons with proper touch targets */
    #eventDetailsModal .modal-footer .btn {
        min-height: 44px;
        min-width: 120px;
        font-size: 1rem;
    }
    
    @@media (max-width: 768px) {
        #eventDetailsModal .modal-footer {
            flex-direction: column;
            gap: 0.5rem;
        }
        
        #eventDetailsModal .modal-footer .btn {
            min-height: 48px;
            min-width: 100%;
            font-size: 1.1rem;
            padding: 0.75rem 1rem;
        }
        
        /* Ensure registration buttons in modal body also meet touch targets */
        #eventRegistrationButtons .btn {
            min-height: 48px;
            min-width: 120px;
            font-size: 1.1rem;
            padding: 0.75rem 1rem;
            margin: 0.25rem;
        }
        
        /* Stack buttons vertically on mobile */
        #eventRegistrationButtons {
            display: flex;
            flex-direction: column;
            gap: 0.5rem;
        }
        
        /* Admin message and membership message styling for mobile */
        #eventAdminMessage,
        #membershipRequiredMsg {
            font-size: 1rem;
            padding: 0.75rem;
            margin-top: 0.5rem;
        }
    }
    
    /* Focus states for accessibility */
    #eventDetailsModal .btn:focus {
        outline: 2px solid #007bff;
        outline-offset: 2px;
    }
    
    /* Loading state adjustments for mobile */
    @@media (max-width: 768px) {
        #eventDetailsModal .btn:disabled {
            opacity: 0.6;
        }
        
        #eventDetailsModal .spinner-border {
            width: 1.25rem;
            height: 1.25rem;
        }
    }
</style>