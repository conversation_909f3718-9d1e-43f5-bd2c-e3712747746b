{"DetailedErrors": true, "Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning"}}, "ConnectionStrings": {"DefaultConnection": "Server=SIMBASQLEXPRESS;User Id=ParaHockeyUser;Password=***************;Database=ParaHockeyDB_TEST;Encrypt=False;TrustServerCertificate=True;"}, "Environment": {"Name": "TEST", "Theme": "danger", "ShowBanner": true, "UseAuthentication": true, "BannerText": "Parahockey TEST Site", "ShowDevelopmentTools": true, "EnableDetailedErrorLogging": true, "EnvironmentIndicatorColor": "danger", "ShowUserFriendlyErrors": true, "ErrorDetailLevel": "detailed"}, "Email": {"SmtpHost": "smtp.office365.com", "SmtpPort": "587", "Username": "<EMAIL>", "Password": "L@535539113654on", "FromEmail": "<EMAIL>", "FromName": "Parahockey Verification"}}