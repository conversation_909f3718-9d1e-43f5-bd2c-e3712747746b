using MailKit.Net.Smtp;
using MailKit.Security;
using MimeKit;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;

namespace ParaHockeyApp.Services
{
    public class EmailService : IEmailService
    {
        private readonly IConfiguration _configuration;
        private readonly ILogger<EmailService> _logger;

        public EmailService(IConfiguration configuration, ILogger<EmailService> logger)
        {
            _configuration = configuration;
            _logger = logger;
        }

        public async Task<bool> SendVerificationCodeAsync(string toEmail, string code, string memberName)
        {
            try
            {
                var smtpHost = _configuration["Email:SmtpHost"];
                var smtpPort = int.Parse(_configuration["Email:SmtpPort"] ?? "587");
                var username = _configuration["Email:Username"];
                var password = _configuration["Email:Password"];
                var fromEmail = _configuration["Email:FromEmail"] ?? "<EMAIL>";
                var fromName = _configuration["Email:FromName"] ?? "Parahockey Verification";

                if (string.IsNullOrEmpty(smtpHost) || string.IsNullOrEmpty(username) || string.IsNullOrEmpty(password))
                {
                    _logger.LogWarning("SMTP configuration not complete. Email not sent.");
                    // Fall back to logging for development
                    _logger.LogInformation($"🔐 DEVELOPMENT: Verification code for {toEmail}: {code}");
                    return false;
                }

                var message = new MimeMessage();
                
                // Set sender (who sends the email)
                message.From.Add(new MailboxAddress(fromName, fromEmail));
                
                // Set recipient
                message.To.Add(new MailboxAddress(memberName, toEmail));
                
                // PREVENT REPLIES - Multiple strategies:
                // 1. Set Reply-To to a no-reply address
                message.ReplyTo.Add(new MailboxAddress("Ne pas répondre / Do Not Reply", "<EMAIL>"));
                
                // 2. Add custom headers to discourage replies
                message.Headers.Add("X-Auto-Response-Suppress", "All");
                message.Headers.Add("Auto-Submitted", "auto-generated");
                message.Headers.Add("Precedence", "bulk");
                
                // 3. Set Return-Path to bounce back replies
                message.Headers.Add("Return-Path", "<>");

                message.Subject = "Code de vérification Parahockey / Parahockey Verification Code";

                var bodyBuilder = new BodyBuilder
                {
                    HtmlBody = GenerateEmailHtml(code, memberName),
                    TextBody = GenerateEmailText(code, memberName)
                };

                message.Body = bodyBuilder.ToMessageBody();

                using (var client = new SmtpClient())
                {
                    // Connect to SMTP server
                    await client.ConnectAsync(smtpHost, smtpPort, SecureSocketOptions.StartTls);
                    
                    // Authenticate
                    await client.AuthenticateAsync(username, password);
                    
                    // Send email
                    await client.SendAsync(message);
                    
                    // Disconnect
                    await client.DisconnectAsync(true);
                }

                _logger.LogInformation($"✅ Verification email sent successfully to {toEmail}");
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"❌ Exception sending verification email to {toEmail}");
                // Fall back to logging for development/testing
                _logger.LogInformation($"🔐 FALLBACK: Verification code for {toEmail}: {code}");
                return false;
            }
        }

        private string GenerateEmailHtml(string code, string memberName)
        {
            return $@"
<!DOCTYPE html>
<html>
<head>
    <meta charset='utf-8'>
    <style>
        body {{ font-family: Arial, sans-serif; line-height: 1.6; margin: 0; padding: 20px; background-color: #f4f4f4; }}
        .container {{ max-width: 600px; margin: 0 auto; background: white; padding: 30px; border-radius: 10px; box-shadow: 0 0 10px rgba(0,0,0,0.1); }}
        .header {{ text-align: center; border-bottom: 2px solid #007bff; padding-bottom: 20px; margin-bottom: 30px; }}
        .logo {{ max-height: 80px; margin-bottom: 10px; }}
        .code-box {{ background: #f8f9fa; border: 2px solid #007bff; border-radius: 8px; padding: 20px; text-align: center; margin: 20px 0; }}
        .code {{ font-size: 32px; font-weight: bold; color: #007bff; letter-spacing: 3px; font-family: monospace; }}
        .warning {{ background: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; border-radius: 5px; margin: 20px 0; }}
        .footer {{ text-align: center; margin-top: 30px; padding-top: 20px; border-top: 1px solid #eee; color: #666; font-size: 12px; }}
    </style>
</head>
<body>
    <div class='container'>
        <div class='header'>
            <h1 style='color: #007bff; margin: 0;'>Parahockey</h1>
            <p style='margin: 5px 0 0 0; color: #666;'>Code de vérification / Verification Code</p>
        </div>
        
        <p>Bonjour {memberName}, / Hello {memberName},</p>
        
        <p><strong>Français:</strong> Voici votre code de vérification pour accéder à votre profil Parahockey. Ce code est valide pour 15 minutes.</p>
        
        <p><strong>English:</strong> Here is your verification code to access your Parahockey profile. This code is valid for 15 minutes.</p>
        
        <div class='code-box'>
            <div class='code'>{code}</div>
        </div>
        
        <div class='warning'>
            <strong>⚠️ Important / Important:</strong><br>
            <strong>FR:</strong> Si vous n'avez pas demandé ce code, ignorez cet email.<br>
            <strong>EN:</strong> If you didn't request this code, please ignore this email.
        </div>
        
        <div style='background: #f8d7da; border: 1px solid #f5c6cb; padding: 15px; border-radius: 5px; margin: 20px 0; color: #721c24;'>
            <strong>🚫 NE PAS RÉPONDRE / DO NOT REPLY</strong><br>
            <strong>FR:</strong> Cet email est automatique. Toute réponse ne sera pas lue.<br>
            <strong>EN:</strong> This email is automated. Any replies will not be read.
        </div>
        
        <p style='text-align: center; margin-top: 30px;'>
            <strong>Merci / Thank you,</strong><br>
            L'équipe Parahockey / The Parahockey Team
        </p>
        
        <div class='footer'>
            <p>Cet email a été envoyé automatiquement, veuillez ne pas répondre.<br>
            This email was sent automatically, please do not reply.</p>
        </div>
    </div>
</body>
</html>";
        }

        private string GenerateEmailText(string code, string memberName)
        {
            return $@"
PARAHOCKEY - CODE DE VÉRIFICATION / VERIFICATION CODE

Bonjour {memberName}, / Hello {memberName},

Français: Voici votre code de vérification pour accéder à votre profil Parahockey. Ce code est valide pour 15 minutes.

English: Here is your verification code to access your Parahockey profile. This code is valid for 15 minutes.

VOTRE CODE / YOUR CODE: {code}

⚠️ IMPORTANT:
FR: Si vous n'avez pas demandé ce code, ignorez cet email.
EN: If you didn't request this code, please ignore this email.

🚫 NE PAS RÉPONDRE / DO NOT REPLY
FR: Cet email est automatique. Toute réponse ne sera pas lue.
EN: This email is automated. Any replies will not be read.

Merci / Thank you,
L'équipe Parahockey / The Parahockey Team

---
AVERTISSEMENT: Cet email a été envoyé automatiquement, veuillez ne pas répondre.
WARNING: This email was sent automatically, please do not reply.
";
        }
    }
}