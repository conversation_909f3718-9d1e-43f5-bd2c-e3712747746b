# Requirements Document

## Introduction

This feature enables administrators to import member data from Excel files (.xlsx format) with comprehensive validation, duplicate detection, and resolution capabilities. The system will handle incomplete or invalid data by staging it in a temporary table for review, and provide a sophisticated side-by-side merge interface for resolving duplicate members. The feature includes support for importing from multiple Excel files that need to be matched to correct parent or contact relationships.

## Requirements

### Requirement 1: Excel File Upload and Processing

**User Story:** As an administrator, I want to upload Excel files containing member data, so that I can efficiently import large numbers of members into the system.

#### Acceptance Criteria

1. WHEN an admin uploads an Excel file THEN the system SHALL accept .xlsx format files up to the configured maximum size (default 10 MB)
2. WHEN processing an Excel file THEN the system SHALL limit processing to the configured maximum rows (default 10,000 rows)
3. WHEN parsing Excel data THEN the system SHALL normalize data by trimming whitespace, converting emails to lowercase, stripping non-digits from phone numbers, and standardizing postal codes
4. WHEN an Excel file is uploaded THEN the system SHALL create a MemberImportBatch record tracking the file name, uploader, timestamp, and processing statistics
5. WHEN processing fails due to file format issues THEN the system SHALL display clear error messages to the administrator

### Requirement 2: Data Validation and Staging

**User Story:** As an administrator, I want invalid or incomplete member data to be identified and staged for review, so that I can fix issues before creating member records.

#### Acceptance Criteria

1. WHEN processing a member row THEN the system SHALL require either Email OR (Date of Birth + Phone) as minimum identification
2. WHEN processing a member row THEN the system SHALL require FirstName and LastName as mandatory fields
3. WHEN a row fails validation THEN the system SHALL create a TempMembers record with Status = "NeedsFix" and store validation errors in ValidationErrorsJson
4. WHEN a row passes validation THEN the system SHALL create a TempMembers record with Status = "ReadyToCreate" or "Duplicate" based on duplicate detection results
5. WHEN storing temp data THEN the system SHALL preserve the original raw data in RawSourceJson for traceability

### Requirement 3: Duplicate Detection and Resolution

**User Story:** As an administrator, I want the system to detect potential duplicate members and provide tools to resolve them, so that I can maintain data integrity without creating duplicate records.

#### Acceptance Criteria

1. WHEN checking for duplicates THEN the system SHALL first match on Email (case-insensitive exact match)
2. WHEN Email is not available or doesn't match THEN the system SHALL check FirstName + LastName + DateOfBirth exact match
3. WHEN a duplicate is detected THEN the system SHALL create a TempMembers record with Status = "Duplicate" and set ExistingMemberId to the matched member
4. WHEN multiple potential matches exist THEN the system SHALL select the strongest match (Email match takes precedence over name+DOB match)
5. WHEN no duplicates are found THEN the system SHALL mark the temp record as "ReadyToCreate"

### Requirement 4: Side-by-Side Merge Interface

**User Story:** As an administrator, I want to resolve duplicate members using a side-by-side comparison interface, so that I can selectively merge data while maintaining control over what changes are made.

#### Acceptance Criteria

1. WHEN viewing a duplicate resolution page THEN the system SHALL display the temp member data on the left and existing member data on the right
2. WHEN field values are identical THEN the system SHALL display the value without selection buttons and automatically mark it as resolved
3. WHEN field values differ THEN the system SHALL display "Use Left (Temp)" and "Use Right (Existing)" buttons for field selection
4. WHEN all differing fields have been resolved THEN the system SHALL enable the "Finalize" button
5. WHEN the Finalize button is clicked THEN the system SHALL open a confirmation modal showing exactly what will change

### Requirement 5: Merge Confirmation and Application

**User Story:** As an administrator, I want to review exactly what changes will be made before applying a merge, so that I can ensure the correct data is being updated.

#### Acceptance Criteria

1. WHEN the confirmation modal opens THEN the system SHALL display a table showing Field | Old Value → New Value for only the fields that will change
2. WHEN the Accept button is clicked THEN the system SHALL update the Members record with selected values, delete the temp record, and log the changes
3. WHEN the Edit button is clicked THEN the system SHALL close the modal and return to the side-by-side merge view
4. WHEN a merge is completed THEN the system SHALL update the import batch counters and display a success message
5. WHEN a merge fails THEN the system SHALL rollback all changes and display an error message

### Requirement 6: Temp Member Queue Management

**User Story:** As an administrator, I want to manage different queues of temporary member data, so that I can efficiently process imports based on their status.

#### Acceptance Criteria

1. WHEN viewing the batch summary THEN the system SHALL display counts for NeedsFix, Duplicate, ReadyToCreate, Created, Merged, and Rejected statuses
2. WHEN accessing the NeedsFix queue THEN the system SHALL display a grid of invalid records with options to fix individual records
3. WHEN accessing the ReadyToCreate queue THEN the system SHALL provide options to create individual members or bulk create selected members
4. WHEN accessing the Duplicates queue THEN the system SHALL display temp-existing member pairs with resolve actions
5. WHEN bulk creating members THEN the system SHALL process each creation in a transaction and update batch counters

### Requirement 7: Member Creation and Editing from Temp Data

**User Story:** As an administrator, I want to create or fix member records using data from the temp staging area, so that I can handle validation errors and incomplete data.

#### Acceptance Criteria

1. WHEN fixing a temp record with validation errors THEN the system SHALL open the Create Member form pre-filled with temp data
2. WHEN validation errors exist THEN the system SHALL display them inline on the form
3. WHEN a member is successfully created from temp data THEN the system SHALL insert the member record, delete the temp record, and update batch counters
4. WHEN creation is cancelled THEN the system SHALL leave the temp record unchanged for later processing
5. WHEN business rules apply (e.g., age restrictions for membership types) THEN the system SHALL enforce them during creation and merge operations

### Requirement 8: Multi-File Import with Relationship Matching

**User Story:** As an administrator, I want to import from multiple Excel files and match members to their correct parents or contacts, so that I can maintain proper family relationships in the system.

#### Acceptance Criteria

1. WHEN importing multiple Excel files THEN the system SHALL provide a way to specify the relationship type for each file (member, parent, contact)
2. WHEN processing related files THEN the system SHALL attempt to match members to existing parent or contact records based on identifying information
3. WHEN relationships cannot be automatically matched THEN the system SHALL flag these for manual review
4. WHEN creating members with relationships THEN the system SHALL validate that parent/contact records exist or are being created in the same batch
5. WHEN relationship matching fails THEN the system SHALL provide tools to manually establish the correct relationships

### Requirement 9: Audit Logging and Security

**User Story:** As an administrator, I want all import activities to be logged for audit purposes, so that I can track who made what changes and when.

#### Acceptance Criteria

1. WHEN an import batch is created THEN the system SHALL log the file name, uploader, timestamp, and row counts
2. WHEN a member is created from temp data THEN the system SHALL log all field values being set
3. WHEN a merge is performed THEN the system SHALL log field-by-field changes showing old and new values
4. WHEN accessing import functionality THEN the system SHALL verify the user has administrator privileges
5. WHEN processing sensitive data THEN the system SHALL apply input sanitization and CSRF protection

### Requirement 10: Performance and Error Handling

**User Story:** As an administrator, I want the import process to handle large files efficiently and provide clear feedback on progress and errors, so that I can successfully import data without system performance issues.

#### Acceptance Criteria

1. WHEN processing large files THEN the system SHALL use streaming and batch processing to avoid memory issues
2. WHEN validation or processing errors occur THEN the system SHALL provide specific, actionable error messages
3. WHEN concurrent access occurs THEN the system SHALL prevent multiple administrators from editing the same temp record simultaneously
4. WHEN database operations fail THEN the system SHALL rollback transactions and maintain data consistency
5. WHEN processing takes significant time THEN the system SHALL provide progress indicators to the user