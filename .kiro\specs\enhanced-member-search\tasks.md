# Implementation Plan

-   [x] 1. Create enhanced data models and DTOs

    -   Create MemberSearchRequest class with all search criteria properties ✅ (already existed)
    -   Create MemberSearchResult and MemberSearchResultItem classes for structured responses ✅
    -   Create SearchMatchInfo class to track which fields matched search terms ✅ (already existed)
    -   _Requirements: 1.1, 2.1, 3.1, 4.1, 6.1_

-   [x] 2. Create MemberSearchService interface and implementation

    -   Define IMemberSearchService interface with search, export, and saved search methods ✅
    -   Implement MemberSearchService with dynamic query building logic ✅
    -   Add phone number normalization helper method for consistent phone search ✅
    -   Implement search result highlighting logic to identify matched fields ✅
    -   _Requirements: 1.1, 1.2, 1.3, 2.1, 2.2, 2.3, 6.1, 6.2, 7.1, 7.2_

-   [x] 3. Implement core search functionality in MemberSearchService

    -   Build dynamic LINQ queries that search across name, email, phone, and address fields ✅
    -   Implement registration type filtering with support for multiple selections ✅
    -   Add active/inactive status filtering logic ✅
    -   Create efficient pagination logic that preserves search criteria ✅
    -   _Requirements: 1.1, 4.1, 4.2, 5.1, 5.2, 6.1, 6.2, 8.2, 8.3_

-   [x] 4. Add date and age-based filtering to MemberSearchService

    -   Implement date of birth range filtering logic ✅
    -   Add age range calculation and filtering functionality ✅
    -   Create date validation to ensure proper range handling ✅
    -   _Requirements: 3.1, 3.2, 3.3, 3.4_

-   [x] 5. Enhance AdminController with new search capabilities

    -   Modify Members action to accept MemberSearchRequest parameters ✅
    -   Update controller to use MemberSearchService instead of direct context queries ✅
    -   Add proper model validation and error handling for search parameters ✅
    -   Ensure search criteria and filters persist across pagination ✅
    -   Add ExportMembers, SaveSearch, GetSavedSearches, DeleteSavedSearch, LoadSavedSearch actions ✅
    -   _Requirements: 4.3, 6.3, 8.2, 8.3_

-   [ ] 6. Create SavedSearch entity and database migration

    -   Create SavedSearch entity class inheriting from BaseEntity
    -   Generate and implement database migration for SavedSearches table
    -   Add necessary indexes for performance optimization
    -   Configure entity relationships in ApplicationContext
    -   _Requirements: 10.1, 10.2_

-   [ ] 7. Implement saved search functionality in MemberSearchService

    -   Add methods to save, retrieve, and delete saved searches
    -   Implement JSON serialization/deserialization for search criteria
    -   Add user-specific saved search filtering
    -   Create validation for saved search names and criteria
    -   _Requirements: 10.1, 10.2, 10.3, 10.4_

-   [ ] 8. Add saved search actions to AdminController

    -   Create SaveSearch action to persist user search criteria
    -   Add GetSavedSearches action to retrieve user's saved searches
    -   Implement DeleteSavedSearch action with proper authorization
    -   Add proper error handling and validation for saved search operations
    -   _Requirements: 10.1, 10.2, 10.3, 10.4_

-   [ ] 9. Create CSV export functionality

    -   Add ExportMembers action to AdminController
    -   Implement CSV generation logic in MemberSearchService
    -   Apply current search filters to export data
    -   Add proper headers and formatting for exported member data
    -   _Requirements: 9.1, 9.2, 9.3, 9.4_

-   [ ] 10. Enhance Members.cshtml view with advanced search UI

    -   Replace simple search input with comprehensive search form
    -   Add registration type multi-select dropdown with all available types
    -   Implement active/inactive status filter radio buttons
    -   Create clear search/reset functionality
    -   _Requirements: 4.1, 4.2, 5.1, 5.2, 6.4_

-   [ ] 11. Add date and location filtering UI components

    -   Implement date range pickers for birth date filtering
    -   Add age range numeric input controls
    -   Create city, province, and postal code filter inputs
    -   Ensure all filter inputs maintain state during pagination
    -   _Requirements: 2.1, 2.2, 2.3, 3.1, 3.2, 3.3, 3.4_

-   [ ] 12. Implement search result highlighting and enhanced display

    -   Add JavaScript functionality to highlight matching search terms in results
    -   Enhance member result cards to show more relevant information
    -   Implement expandable member details in search results
    -   Add visual indicators for which fields matched the search
    -   _Requirements: 7.1, 7.2, 7.3_

-   [ ] 13. Add saved search UI components

    -   Create saved search dropdown in the search form
    -   Add "Save Search" button and modal dialog
    -   Implement saved search selection and application logic
    -   Add delete functionality for saved searches with confirmation
    -   _Requirements: 10.1, 10.2, 10.3, 10.4_

-   [ ] 14. Implement export functionality UI

    -   Add "Export Results" button that appears when search results are present
    -   Create export progress indicator for large datasets
    -   Add export format options and file download handling
    -   Ensure export respects current search filters and pagination
    -   _Requirements: 9.1, 9.2, 9.3, 9.4_

-   [ ] 15. Add performance optimizations and database indexes

    -   Create database migration to add search performance indexes
    -   Implement query optimization for phone number searching
    -   Add database indexes for frequently searched fields (phone, city, postal code, date of birth)
    -   Optimize pagination queries to improve performance with large datasets
    -   _Requirements: 8.1, 8.4_

-   [ ] 16. Implement comprehensive error handling and validation

    -   Add client-side validation for search form inputs
    -   Implement server-side validation for all search parameters
    -   Create user-friendly error messages for invalid search criteria
    -   Add graceful handling of search timeouts and database errors
    -   _Requirements: 7.4, 8.1_

-   [ ] 17. Add localization support for new search features

    -   Add localization keys for all new UI text and labels
    -   Implement French translations for search interface elements
    -   Ensure error messages and validation text support both languages
    -   Add localized formatting for dates and numbers in search results
    -   _Requirements: All requirements - multilingual support_

-   [ ] 18. Create comprehensive unit tests for MemberSearchService

    -   Write tests for dynamic query building with various search criteria
    -   Test phone number normalization and matching logic
    -   Create tests for date range and age filtering functionality
    -   Add tests for saved search serialization and persistence
    -   _Requirements: 1.1, 1.2, 1.3, 2.1, 2.2, 2.3, 3.1, 3.2, 3.3, 10.1_

-   [ ] 19. Write integration tests for enhanced search functionality

    -   Create tests for AdminController search actions with various parameter combinations
    -   Test search result accuracy and pagination consistency
    -   Add tests for CSV export functionality with different filter combinations
    -   Test saved search functionality end-to-end
    -   _Requirements: 4.1, 4.2, 6.1, 6.2, 8.2, 8.3, 9.1, 10.1_

-   [ ] 20. Implement final UI polish and responsive design
    -   Ensure search interface works properly on mobile devices
    -   Add keyboard shortcuts for common search operations
    -   Implement search suggestions and auto-complete functionality
    -   Add loading states and progress indicators for search operations
    -   _Requirements: 8.1, 8.4_
