using Microsoft.EntityFrameworkCore;
using ParaHockeyApp.Models;
using ParaHockeyApp.Models.Entities;
using ParaHockeyApp.ViewModels;

namespace ParaHockeyApp.Services
{
    /// <summary>
    /// Service for managing import batch operations and summary reporting
    /// </summary>
    public class ImportBatchService : IImportBatchService
    {
        private readonly ApplicationContext _context;
        private readonly IAuditLogService _auditLogService;

        public ImportBatchService(ApplicationContext context, IAuditLogService auditLogService)
        {
            _context = context;
            _auditLogService = auditLogService;
        }

        /// <summary>
        /// Gets all import batches with pagination and filtering
        /// </summary>
        public async Task<ImportBatchListViewModel> GetImportBatchesAsync(
            int pageNumber = 1, 
            int pageSize = 20, 
            string? statusFilter = null, 
            string? searchTerm = null, 
            DateTime? fromDate = null, 
            DateTime? toDate = null)
        {
            var query = _context.MemberImportBatches.AsQueryable();

            // Apply filters
            if (!string.IsNullOrWhiteSpace(statusFilter))
            {
                query = query.Where(b => b.Status.ToLower() == statusFilter.ToLower());
            }

            if (!string.IsNullOrWhiteSpace(searchTerm))
            {
                query = query.Where(b => b.FileName.ToLower().Contains(searchTerm.ToLower()) ||
                                        b.UploadedBy.ToLower().Contains(searchTerm.ToLower()));
            }

            if (fromDate.HasValue)
            {
                query = query.Where(b => b.UploadedAtUtc >= fromDate.Value);
            }

            if (toDate.HasValue)
            {
                query = query.Where(b => b.UploadedAtUtc <= toDate.Value.AddDays(1));
            }

            var totalCount = await query.CountAsync();

            var batches = await query
                .OrderByDescending(b => b.UploadedAtUtc)
                .Skip((pageNumber - 1) * pageSize)
                .Take(pageSize)
                .ToListAsync();

            var batchViewModels = new List<ImportBatchViewModel>();
            foreach (var batch in batches)
            {
                var viewModel = await MapToViewModelAsync(batch);
                batchViewModels.Add(viewModel);
            }

            return new ImportBatchListViewModel
            {
                Batches = batchViewModels,
                PageNumber = pageNumber,
                PageSize = pageSize,
                TotalCount = totalCount,
                StatusFilter = statusFilter,
                SearchTerm = searchTerm,
                FromDate = fromDate,
                ToDate = toDate
            };
        }

        /// <summary>
        /// Gets detailed summary for a specific import batch
        /// </summary>
        public async Task<ImportBatchViewModel> GetBatchSummaryAsync(int batchId)
        {
            var batch = await _context.MemberImportBatches
                .FirstOrDefaultAsync(b => b.Id == batchId);

            if (batch == null)
                throw new ArgumentException($"Import batch {batchId} not found");

            return await MapToViewModelAsync(batch);
        }

        /// <summary>
        /// Gets queue status for all statuses within a batch
        /// </summary>
        public async Task<List<ImportQueueStatusViewModel>> GetQueueStatusesAsync(int batchId)
        {
            var batch = await _context.MemberImportBatches
                .FirstOrDefaultAsync(b => b.Id == batchId);

            if (batch == null)
                throw new ArgumentException($"Import batch {batchId} not found");

            // Get status counts
            var statusCounts = await _context.TempMembers
                .Where(tm => tm.ImportBatchId == batchId)
                .GroupBy(tm => tm.Status)
                .Select(g => new { Status = g.Key, Count = g.Count() })
                .ToListAsync();

            var queueStatuses = new List<ImportQueueStatusViewModel>();

            // Create view models for each status that has members
            foreach (var statusCount in statusCounts.Where(sc => sc.Count > 0))
            {
                queueStatuses.Add(new ImportQueueStatusViewModel
                {
                    ImportBatchId = batchId,
                    FileName = batch.FileName,
                    QueueType = statusCount.Status,
                    TotalItems = statusCount.Count,
                    ProcessedItems = 0 // This could be enhanced with additional tracking
                });
            }

            // Sort by priority (actionable items first)
            return queueStatuses
                .OrderBy(q => q.RequiresAction ? 0 : 1)
                .ThenBy(q => q.QueueType)
                .ToList();
        }

        /// <summary>
        /// Gets import progress information for real-time updates
        /// </summary>
        public async Task<ImportProgressViewModel> GetImportProgressAsync(int batchId)
        {
            var batch = await _context.MemberImportBatches
                .FirstOrDefaultAsync(b => b.Id == batchId);

            if (batch == null)
                throw new ArgumentException($"Import batch {batchId} not found");

            var progress = new ImportProgressViewModel
            {
                ImportBatchId = batchId,
                FileName = batch.FileName,
                CurrentStage = batch.Status,
                StatusMessage = batch.ErrorMessage ?? $"Batch is {batch.Status.ToLower()}",
                LastUpdated = DateTime.UtcNow,
                IsProcessing = batch.Status == "Processing",
                HasError = !string.IsNullOrEmpty(batch.ErrorMessage) || batch.Status == "Failed",
                ErrorMessage = batch.ErrorMessage
            };

            // Set stage completion based on batch status
            var stages = progress.Stages;
            switch (batch.Status.ToLower())
            {
                case "processing":
                    stages[0].IsCompleted = true; // Upload
                    stages[1].IsActive = true;    // Parse
                    progress.CurrentStep = 2;
                    progress.TotalSteps = 5;
                    break;
                case "completed":
                    foreach (var stage in stages) stage.IsCompleted = true;
                    progress.CurrentStep = 5;
                    progress.TotalSteps = 5;
                    break;
                case "failed":
                    stages[0].IsCompleted = true;
                    stages[1].ErrorMessage = batch.ErrorMessage;
                    progress.CurrentStep = 1;
                    progress.TotalSteps = 5;
                    break;
                default:
                    progress.CurrentStep = 1;
                    progress.TotalSteps = 5;
                    break;
            }

            return progress;
        }

        /// <summary>
        /// Updates batch statistics by recalculating from temp members
        /// </summary>
        public async Task<ImportBatchViewModel> RefreshBatchStatisticsAsync(int batchId)
        {
            var batch = await _context.MemberImportBatches
                .FirstOrDefaultAsync(b => b.Id == batchId);

            if (batch == null)
                throw new ArgumentException($"Import batch {batchId} not found");

            // Recalculate statistics
            var statusCounts = await _context.TempMembers
                .Where(tm => tm.ImportBatchId == batchId)
                .GroupBy(tm => tm.Status)
                .Select(g => new { Status = g.Key, Count = g.Count() })
                .ToListAsync();

            // Reset counts
            batch.CreatedCount = 0;
            batch.DuplicateCount = 0;
            batch.NeedsFixCount = 0;
            batch.MergedCount = 0;
            batch.RejectedCount = 0;

            // Update counts based on current status
            foreach (var statusCount in statusCounts)
            {
                switch (statusCount.Status)
                {
                    case TempMemberStatus.Created:
                        batch.CreatedCount = statusCount.Count;
                        break;
                    case TempMemberStatus.Duplicate:
                        batch.DuplicateCount = statusCount.Count;
                        break;
                    case TempMemberStatus.NeedsFix:
                        batch.NeedsFixCount = statusCount.Count;
                        break;
                    case TempMemberStatus.Merged:
                        batch.MergedCount = statusCount.Count;
                        break;
                    case TempMemberStatus.Rejected:
                        batch.RejectedCount = statusCount.Count;
                        break;
                }
            }

            await _context.SaveChangesAsync();
            return await MapToViewModelAsync(batch);
        }

        /// <summary>
        /// Deletes an import batch and all associated temp members
        /// </summary>
        public async Task<bool> DeleteImportBatchAsync(int batchId, string deletedBy)
        {
            using var transaction = await _context.Database.BeginTransactionAsync();

            try
            {
                var batch = await _context.MemberImportBatches
                    .FirstOrDefaultAsync(b => b.Id == batchId);

                if (batch == null)
                    return false;

                // Check if batch can be deleted (only allow deletion of failed or completed batches)
                if (batch.Status.ToLower() == "processing")
                {
                    throw new InvalidOperationException("Cannot delete a batch that is currently processing");
                }

                // Delete temp members first
                var tempMembers = await _context.TempMembers
                    .Where(tm => tm.ImportBatchId == batchId)
                    .ToListAsync();

                _context.TempMembers.RemoveRange(tempMembers);

                // Delete the batch
                _context.MemberImportBatches.Remove(batch);

                await _context.SaveChangesAsync();

                // Log the deletion
                await _auditLogService.LogActionAsync(
                    $"Deleted import batch '{batch.FileName}' and {tempMembers.Count} temp members",
                    null,
                    ActionSource.AdminPanel,
                    deletedBy,
                    null);

                await transaction.CommitAsync();
                return true;
            }
            catch
            {
                await transaction.RollbackAsync();
                throw;
            }
        }

        /// <summary>
        /// Gets recent import activity for dashboard
        /// </summary>
        public async Task<List<ImportBatchViewModel>> GetRecentImportActivityAsync(int days = 7)
        {
            var cutoffDate = DateTime.UtcNow.AddDays(-days);

            var recentBatches = await _context.MemberImportBatches
                .Where(b => b.UploadedAtUtc >= cutoffDate)
                .OrderByDescending(b => b.UploadedAtUtc)
                .Take(10)
                .ToListAsync();

            var viewModels = new List<ImportBatchViewModel>();
            foreach (var batch in recentBatches)
            {
                var viewModel = await MapToViewModelAsync(batch);
                viewModels.Add(viewModel);
            }

            return viewModels;
        }

        /// <summary>
        /// Gets import statistics for reporting
        /// </summary>
        public async Task<ImportStatisticsViewModel> GetImportStatisticsAsync(DateTime fromDate, DateTime toDate)
        {
            var batches = await _context.MemberImportBatches
                .Where(b => b.UploadedAtUtc >= fromDate && b.UploadedAtUtc <= toDate.AddDays(1))
                .ToListAsync();

            var statistics = new ImportStatisticsViewModel
            {
                FromDate = fromDate,
                ToDate = toDate,
                TotalBatches = batches.Count,
                TotalMembersProcessed = batches.Sum(b => b.TotalRows),
                TotalMembersCreated = batches.Sum(b => b.CreatedCount),
                TotalMembersMerged = batches.Sum(b => b.MergedCount),
                TotalDuplicatesResolved = batches.Sum(b => b.MergedCount),
                TotalValidationErrors = batches.Sum(b => b.NeedsFixCount)
            };

            // Calculate top uploaders
            statistics.TopUploaders = batches
                .GroupBy(b => b.UploadedBy)
                .Select(g => new UploaderStatistic
                {
                    UploaderName = g.Key,
                    BatchCount = g.Count(),
                    MembersProcessed = g.Sum(b => b.TotalRows),
                    SuccessRate = g.Sum(b => b.TotalRows) > 0 ? 
                        (g.Sum(b => b.CreatedCount + b.MergedCount) / (double)g.Sum(b => b.TotalRows)) * 100 : 0
                })
                .OrderByDescending(u => u.MembersProcessed)
                .Take(5)
                .ToList();

            // Calculate daily activity
            statistics.DailyActivity = batches
                .GroupBy(b => b.UploadedAtUtc.Date)
                .Select(g => new DailyImportStatistic
                {
                    Date = g.Key,
                    BatchCount = g.Count(),
                    MembersProcessed = g.Sum(b => b.TotalRows),
                    MembersCreated = g.Sum(b => b.CreatedCount)
                })
                .OrderBy(d => d.Date)
                .ToList();

            return statistics;
        }

        /// <summary>
        /// Maps MemberImportBatch entity to view model
        /// </summary>
        private async Task<ImportBatchViewModel> MapToViewModelAsync(MemberImportBatch batch)
        {
            // Get current status counts from temp members
            var statusCounts = await _context.TempMembers
                .Where(tm => tm.ImportBatchId == batch.Id)
                .GroupBy(tm => tm.Status)
                .Select(g => new { Status = g.Key, Count = g.Count() })
                .ToListAsync();

            var viewModel = new ImportBatchViewModel
            {
                ImportBatchId = batch.Id,
                FileName = batch.FileName,
                UploadedAtUtc = batch.UploadedAtUtc,
                UploadedBy = batch.UploadedBy,
                TotalRows = batch.TotalRows,
                Status = batch.Status,
                ErrorMessage = batch.ErrorMessage
            };

            // Map status counts
            foreach (var statusCount in statusCounts)
            {
                switch (statusCount.Status)
                {
                    case TempMemberStatus.Imported:
                        viewModel.ImportedCount = statusCount.Count;
                        break;
                    case TempMemberStatus.Created:
                        viewModel.CreatedCount = statusCount.Count;
                        break;
                    case TempMemberStatus.Duplicate:
                        viewModel.DuplicateCount = statusCount.Count;
                        break;
                    case TempMemberStatus.NeedsFix:
                        viewModel.NeedsFixCount = statusCount.Count;
                        break;
                    case TempMemberStatus.Merged:
                        viewModel.MergedCount = statusCount.Count;
                        break;
                    case TempMemberStatus.Rejected:
                        viewModel.RejectedCount = statusCount.Count;
                        break;
                    case TempMemberStatus.ReadyToCreate:
                        viewModel.ReadyToCreateCount = statusCount.Count;
                        break;
                }
            }

            return viewModel;
        }
    }
}