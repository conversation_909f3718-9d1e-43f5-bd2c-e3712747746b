using System.ComponentModel.DataAnnotations;

namespace ParaHockeyApp.DTOs
{
    /// <summary>
    /// Response DTO for member search containing results and pagination metadata
    /// Provides structured search results with complete pagination information
    /// </summary>
    public class MemberSearchResult
    {
        /// <summary>
        /// List of member search result items
        /// </summary>
        public List<MemberSearchResultItem> Members { get; set; } = new List<MemberSearchResultItem>();

        /// <summary>
        /// Total number of members matching the search criteria (across all pages)
        /// </summary>
        public int TotalCount { get; set; }

        /// <summary>
        /// Current page number (1-based)
        /// </summary>
        public int CurrentPage { get; set; }

        /// <summary>
        /// Number of results per page
        /// </summary>
        public int PageSize { get; set; }

        /// <summary>
        /// Total number of pages available
        /// </summary>
        public int TotalPages => PageSize > 0 ? (int)Math.Ceiling((double)TotalCount / PageSize) : 0;

        /// <summary>
        /// Whether there is a previous page available
        /// </summary>
        public bool HasPreviousPage => CurrentPage > 1;

        /// <summary>
        /// Whether there is a next page available
        /// </summary>
        public bool HasNextPage => CurrentPage < TotalPages;

        /// <summary>
        /// The search criteria that were used to generate these results
        /// </summary>
        public MemberSearchRequest SearchCriteria { get; set; } = new MemberSearchRequest();

        /// <summary>
        /// Whether any search criteria were applied (not just default empty search)
        /// </summary>
        public bool HasActiveFilters => SearchCriteria.HasSearchCriteria();

        /// <summary>
        /// Starting result number for current page (for display purposes)
        /// </summary>
        public int StartResultNumber => TotalCount > 0 ? ((CurrentPage - 1) * PageSize) + 1 : 0;

        /// <summary>
        /// Ending result number for current page (for display purposes)
        /// </summary>
        public int EndResultNumber => Math.Min(CurrentPage * PageSize, TotalCount);
    }
}