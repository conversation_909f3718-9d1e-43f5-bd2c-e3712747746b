@using Microsoft.Extensions.Localization
@inject IStringLocalizer<ParaHockeyApp.Resources.SharedResourceMarker> SharedLocalizer
@{
    ViewData["Title"] = SharedLocalizer["SystemErrorTitle"];
    var errorCode = ViewBag.ErrorCode as string ?? "500";
    var errorMessage = ViewBag.ErrorMessage as string ?? SharedLocalizer["Error_UnexpectedError"];
    var errorId = ViewBag.ErrorId as string;
}

<div class="container mt-5">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card border-danger">
                <div class="card-header bg-danger text-white">
                    <h1 class="h3 mb-0">
                        <i class="fas fa-exclamation-triangle"></i>
                        @if (errorCode == "404")
                        {
                            @SharedLocalizer["PageNotFoundTitle"]
                        }
                        else
                        {
                            @SharedLocalizer["SystemErrorTitle"]
                        }
                    </h1>
                </div>
                <div class="card-body text-center">
                    <div class="mb-4">
                        @if (errorCode == "404")
                        {
                            <i class="fas fa-search fa-5x text-muted mb-3"></i>
                            <h2 class="h4">@SharedLocalizer["PageNotFoundHeading"]</h2>
                            <p class="text-muted">@SharedLocalizer["PageNotFoundMessage"]</p>
                        }
                        else
                        {
                            <i class="fas fa-exclamation-triangle fa-5x text-danger mb-3"></i>
                            <h2 class="h4">@SharedLocalizer["SystemErrorHeading"]</h2>
                            <p class="text-muted">@errorMessage</p>
                        }
                    </div>

                    @if (!string.IsNullOrEmpty(errorId))
                    {
                        <div class="alert alert-info">
                            <small>
                                <strong>@SharedLocalizer["SystemErrorRequestId"]:</strong> 
                                <code>@errorId</code>
                            </small>
                        </div>
                    }

                    <div class="mt-4">
                        <a href="@Url.Action("Index", "Home")" class="btn btn-primary me-2">
                            <i class="fas fa-home"></i> @SharedLocalizer["ReturnToHome"]
                        </a>
                        @if (errorCode == "404")
                        {
                            <a href="javascript:history.back()" class="btn btn-secondary">
                                <i class="fas fa-arrow-left"></i> @SharedLocalizer["GoBack"]
                            </a>
                        }
                        else
                        {
                            <a href="@Url.Action("PublicCalendar", "Home")" class="btn btn-outline-primary">
                                <i class="fas fa-calendar"></i> @SharedLocalizer["ViewCalendar"]
                            </a>
                        }
                    </div>

                    @if (errorCode == "404")
                    {
                        <div class="mt-4">
                            <h5>@SharedLocalizer["SuggestedLinks"]</h5>
                            <div class="row">
                                <div class="col-md-6">
                                    <ul class="list-unstyled">
                                        <li><a href="@Url.Action("Index", "Home")">@SharedLocalizer["HomePage"]</a></li>
                                        <li><a href="@Url.Action("PublicCalendar", "Home")">@SharedLocalizer["Calendar"]</a></li>
                                    </ul>
                                </div>
                                <div class="col-md-6">
                                    <ul class="list-unstyled">
                                        <li><a href="@Url.Action("Register", "Members")">@SharedLocalizer["MemberRegistration"]</a></li>
                                        <li><a href="@Url.Action("Login", "Members")">@SharedLocalizer["MemberLogin"]</a></li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    }
                </div>
            </div>
        </div>
    </div>
</div>