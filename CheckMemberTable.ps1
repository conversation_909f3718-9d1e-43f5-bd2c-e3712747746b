# Check Member table structure
$prodConn = "Server=SIMBA\SQLEXPRESS;Database=ParaHockeyDB;User Id=ParaHockeyUser;Password=***************;TrustServerCertificate=True;"

$conn = New-Object System.Data.SqlClient.SqlConnection($prodConn)
$conn.Open()

$cmd = $conn.CreateCommand()
$cmd.CommandText = "SELECT COLUMN_NAME, DATA_TYPE, IS_NULLABLE FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'Members' ORDER BY ORDINAL_POSITION"

Write-Host "Members table columns:"
$reader = $cmd.ExecuteReader()
while ($reader.Read()) {
    $nullable = if ($reader['IS_NULLABLE'] -eq 'YES') { 'NULL' } else { 'NOT NULL' }
    Write-Host "  $($reader['COLUMN_NAME']) $($reader['DATA_TYPE']) $nullable"
}
$reader.Close()

$conn.Close()