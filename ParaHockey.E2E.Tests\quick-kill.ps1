#!/usr/bin/env pwsh
# Quick Kill ChromeDriver - One-liner version

Write-Host "🔥 Quick killing ChromeDriver processes..." -ForegroundColor Red

# Kill all Chrome and ChromeDriver processes
Get-Process -Name "chromedriver", "chrome", "GoogleChromeForTesting", "msedgedriver", "geckodriver" -ErrorAction SilentlyContinue | ForEach-Object { 
    Write-Host "Killing $($_.ProcessName) (PID: $($_.Id))" -ForegroundColor Yellow
    $_.Kill() 
}

Start-Sleep -Seconds 1
Write-Host "✅ Done! All browser driver processes killed." -ForegroundColor Green