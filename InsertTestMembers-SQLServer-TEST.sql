-- =============================================
-- Para Hockey - Test Members Data (SQL Server TEST)
-- 50 members with TEST_ prefix, HQ_Id as NULL
-- =============================================

-- Clear existing test data
DELETE FROM Members WHERE Prenom LIKE 'TEST_%';

-- Insert 50 test members with HQ_Id as NULL (members don't know their HQ_Id yet)
INSERT INTO Members (HQ_Id, Prenom, Nom, DateNaissance, Sexe, Adresse, Ville, Province, CodePostal, Telephone, TypeTelephone, Courriel, TypeInscription, DateCreation, DateModification, EstActif, CreePar, ModifiePar) VALUES
(NULL, 'TEST_Alexandre', 'Tremblay', '1995-03-15', 1, '123 Rue Principale', 'Montréal', 'QC', 'H1A1A1', '5145550001', 1, '<EMAIL>', 1, GETDATE(), GETDATE(), 1, 'System', 'System'),
(NULL, 'TEST_Sophie', 'Gagnon', '1988-07-22', 2, '456 Avenue Érables', 'Québec', 'QC', 'G1B2B2', '4185550002', 2, '<EMAIL>', 2, GETDATE(), GETDATE(), 1, 'System', 'System'),
(NULL, 'TEST_Michael', 'Johnson', '1992-11-08', 1, '789 Oak Street', 'Toronto', 'ON', 'M1C3C3', '4165550003', 1, '<EMAIL>', 3, GETDATE(), GETDATE(), 1, 'System', 'System'),
(NULL, 'TEST_Emma', 'Williams', '1985-05-14', 2, '321 Pine Avenue', 'Vancouver', 'BC', 'V1D4D4', '6045550004', 1, '<EMAIL>', 4, GETDATE(), GETDATE(), 1, 'System', 'System'),
(NULL, 'TEST_Jordan', 'Smith', '1999-01-30', 3, '654 Maple Road', 'Calgary', 'AB', 'T1E5E5', '4035550005', 2, '<EMAIL>', 5, GETDATE(), GETDATE(), 1, 'System', 'System'),
(NULL, 'TEST_Isabelle', 'Roy', '1990-09-12', 2, '987 Blvd St-Laurent', 'Montréal', 'QC', 'H2F6F6', '5145550006', 1, '<EMAIL>', 1, GETDATE(), GETDATE(), 1, 'System', 'System'),
(NULL, 'TEST_David', 'Brown', '1987-04-25', 1, '147 King Street', 'Winnipeg', 'MB', 'R1G7G7', '2045550007', 2, '<EMAIL>', 2, GETDATE(), GETDATE(), 1, 'System', 'System'),
(NULL, 'TEST_Camille', 'Dubois', '1993-12-03', 2, '258 Chemin Pins', 'Sherbrooke', 'QC', 'J1H8H8', '8195550008', 1, '<EMAIL>', 3, GETDATE(), GETDATE(), 1, 'System', 'System'),
(NULL, 'TEST_Ryan', 'Davis', '1996-08-17', 1, '369 Victoria St', 'Regina', 'SK', 'S1I9I9', '3065550009', 1, '<EMAIL>', 4, GETDATE(), GETDATE(), 1, 'System', 'System'),
(NULL, 'TEST_Amélie', 'Moreau', '1991-06-21', 2, '741 Rue Paix', 'Trois-Rivières', 'QC', 'G2J1J1', '8195550010', 2, '<EMAIL>', 5, GETDATE(), GETDATE(), 1, 'System', 'System'),
(NULL, 'TEST_Tyler', 'Wilson', '1989-02-14', 1, '852 Main Street', 'Halifax', 'NS', 'B1K2K2', '9025550011', 1, '<EMAIL>', 1, GETDATE(), GETDATE(), 1, 'System', 'System'),
(NULL, 'TEST_Chloé', 'Martin', '1994-10-09', 2, '963 Avenue Parc', 'Laval', 'QC', 'H3L3L3', '4505550012', 2, '<EMAIL>', 2, GETDATE(), GETDATE(), 1, 'System', 'System'),
(NULL, 'TEST_Connor', 'Moore', '1997-07-04', 1, '159 Church Street', 'St. Johns', 'NL', 'A1M4M4', '7095550013', 1, '<EMAIL>', 3, GETDATE(), GETDATE(), 1, 'System', 'System'),
(NULL, 'TEST_Jade', 'Taylor', '1986-03-28', 2, '267 University Ave', 'Charlottetown', 'PE', 'C1N5N5', '9025550014', 1, '<EMAIL>', 4, GETDATE(), GETDATE(), 1, 'System', 'System'),
(NULL, 'TEST_Alex', 'Anderson', '1998-11-16', 3, '378 Government St', 'Whitehorse', 'YT', 'Y1O6O6', '8675550015', 2, '<EMAIL>', 5, GETDATE(), GETDATE(), 1, 'System', 'System'),
(NULL, 'TEST_Léa', 'Thomas', '1992-05-07', 2, '489 Franklin Ave', 'Yellowknife', 'NT', 'X1P7P7', '8675550016', 1, '<EMAIL>', 1, GETDATE(), GETDATE(), 1, 'System', 'System'),
(NULL, 'TEST_Brandon', 'Jackson', '1990-01-19', 1, '591 Federal Road', 'Iqaluit', 'NU', 'X0Q8Q8', '8675550017', 2, '<EMAIL>', 2, GETDATE(), GETDATE(), 1, 'System', 'System'),
(NULL, 'TEST_Océane', 'White', '1995-09-23', 2, '612 Water Street', 'Fredericton', 'NB', 'E1R9R9', '5065550018', 1, '<EMAIL>', 3, GETDATE(), GETDATE(), 1, 'System', 'System'),
(NULL, 'TEST_Ethan', 'Harris', '1988-12-11', 1, '723 Prince Street', 'Sydney', 'NS', 'B1S1S1', '9025550019', 1, '<EMAIL>', 4, GETDATE(), GETDATE(), 1, 'System', 'System'),
(NULL, 'TEST_Maëlle', 'Clark', '1993-04-02', 2, '834 George Street', 'Saint John', 'NB', 'E2T2T2', '5065550020', 2, '<EMAIL>', 5, GETDATE(), GETDATE(), 1, 'System', 'System'),
(NULL, 'TEST_Logan', 'Lewis', '1996-10-18', 1, '945 Broadway', 'Saskatoon', 'SK', 'S3U3U3', '3065550021', 1, '<EMAIL>', 1, GETDATE(), GETDATE(), 1, 'System', 'System'),
(NULL, 'TEST_Zoé', 'Robinson', '1991-08-06', 2, '156 Richmond St', 'London', 'ON', 'N4V4V4', '5195550022', 2, '<EMAIL>', 2, GETDATE(), GETDATE(), 1, 'System', 'System'),
(NULL, 'TEST_Noah', 'Walker', '1994-01-25', 1, '267 Dundas Street', 'Hamilton', 'ON', 'L5W5W5', '9055550023', 1, '<EMAIL>', 3, GETDATE(), GETDATE(), 1, 'System', 'System'),
(NULL, 'TEST_Lily', 'Hall', '1989-06-13', 2, '378 Portage Ave', 'Winnipeg', 'MB', 'R6X6X6', '2045550024', 1, '<EMAIL>', 4, GETDATE(), GETDATE(), 1, 'System', 'System'),
(NULL, 'TEST_Mason', 'Allen', '1997-03-31', 1, '489 Jasper Avenue', 'Edmonton', 'AB', 'T7Y7Y7', '7805550025', 2, '<EMAIL>', 5, GETDATE(), GETDATE(), 1, 'System', 'System'),
(NULL, 'TEST_Aria', 'Young', '1992-11-19', 2, '591 Granville St', 'Vancouver', 'BC', 'V8Z8Z8', '6045550026', 1, '<EMAIL>', 1, GETDATE(), GETDATE(), 1, 'System', 'System'),
(NULL, 'TEST_Liam', 'Hernandez', '1985-07-08', 1, '612 Sparks Street', 'Ottawa', 'ON', 'K9A9A9', '6135550027', 2, '<EMAIL>', 2, GETDATE(), GETDATE(), 1, 'System', 'System'),
(NULL, 'TEST_Mia', 'King', '1998-04-16', 2, '723 Yonge Street', 'Toronto', 'ON', 'M0B0B0', '4165550028', 1, '<EMAIL>', 3, GETDATE(), GETDATE(), 1, 'System', 'System'),
(NULL, 'TEST_Jacob', 'Wright', '1990-12-04', 1, '834 Sainte-Catherine', 'Montréal', 'QC', 'H1C1C1', '5145550029', 1, '<EMAIL>', 4, GETDATE(), GETDATE(), 1, 'System', 'System'),
(NULL, 'TEST_Grace', 'Lopez', '1995-08-22', 2, '945 Grande Allée', 'Québec', 'QC', 'G2D2D2', '4185550030', 2, '<EMAIL>', 5, GETDATE(), GETDATE(), 1, 'System', 'System'),
(NULL, 'TEST_Owen', 'Hill', '1987-05-10', 1, '156 Water Street', 'Vancouver', 'BC', 'V3E3E3', '6045550031', 1, '<EMAIL>', 1, GETDATE(), GETDATE(), 1, 'System', 'System'),
(NULL, 'TEST_Victoria', 'Scott', '1993-01-28', 2, '267 King Street', 'Kitchener', 'ON', 'N4F4F4', '5195550032', 2, '<EMAIL>', 2, GETDATE(), GETDATE(), 1, 'System', 'System'),
(NULL, 'TEST_Lucas', 'Green', '1996-09-15', 1, '378 Main Street', 'Moncton', 'NB', 'E5G5G5', '5065550033', 1, '<EMAIL>', 3, GETDATE(), GETDATE(), 1, 'System', 'System'),
(NULL, 'TEST_Hannah', 'Adams', '1991-04-03', 2, '489 Barrington St', 'Halifax', 'NS', 'B6H6H6', '9025550034', 1, '<EMAIL>', 4, GETDATE(), GETDATE(), 1, 'System', 'System'),
(NULL, 'TEST_Caleb', 'Baker', '1988-11-21', 1, '591 Duckworth St', 'St. Johns', 'NL', 'A7I7I7', '7095550035', 2, '<EMAIL>', 5, GETDATE(), GETDATE(), 1, 'System', 'System'),
(NULL, 'TEST_Avery', 'Gonzalez', '1994-07-09', 3, '612 University St', 'Charlottetown', 'PE', 'C8J8J8', '9025550036', 1, '<EMAIL>', 1, GETDATE(), GETDATE(), 1, 'System', 'System'),
(NULL, 'TEST_Henry', 'Nelson', '1997-02-26', 1, '723 2nd Avenue', 'Whitehorse', 'YT', 'Y9K9K9', '8675550037', 2, '<EMAIL>', 2, GETDATE(), GETDATE(), 1, 'System', 'System'),
(NULL, 'TEST_Scarlett', 'Carter', '1992-10-14', 2, '834 Franklin Ave', 'Yellowknife', 'NT', 'X0L0L0', '8675550038', 1, '<EMAIL>', 3, GETDATE(), GETDATE(), 1, 'System', 'System'),
(NULL, 'TEST_Sebastian', 'Mitchell', '1989-06-02', 1, '945 Federal Road', 'Iqaluit', 'NU', 'X1M1M1', '8675550039', 1, '<EMAIL>', 4, GETDATE(), GETDATE(), 1, 'System', 'System'),
(NULL, 'TEST_Layla', 'Perez', '1995-12-20', 2, '156 Elgin Street', 'Ottawa', 'ON', 'K2N2N2', '6135550040', 2, '<EMAIL>', 5, GETDATE(), GETDATE(), 1, 'System', 'System'),
(NULL, 'TEST_Jack', 'Roberts', '1986-08-08', 1, '267 Robson Street', 'Vancouver', 'BC', 'V3O3O3', '6045550041', 1, '<EMAIL>', 1, GETDATE(), GETDATE(), 1, 'System', 'System'),
(NULL, 'TEST_Chloe', 'Turner', '1998-03-26', 2, '378 Bloor Street', 'Toronto', 'ON', 'M4P4P4', '4165550042', 2, '<EMAIL>', 2, GETDATE(), GETDATE(), 1, 'System', 'System'),
(NULL, 'TEST_Benjamin', 'Phillips', '1993-11-13', 1, '489 Rue Saint-Denis', 'Montréal', 'QC', 'H5Q5Q5', '5145550043', 1, '<EMAIL>', 3, GETDATE(), GETDATE(), 1, 'System', 'System'),
(NULL, 'TEST_Addison', 'Campbell', '1990-07-01', 3, '591 Grande Allée Est', 'Québec', 'QC', 'G6R6R6', '4185550044', 2, '<EMAIL>', 4, GETDATE(), GETDATE(), 1, 'System', 'System'),
(NULL, 'TEST_Elijah', 'Parker', '1987-01-19', 1, '612 Hastings Street', 'Vancouver', 'BC', 'V7S7S7', '6045550045', 1, '<EMAIL>', 5, GETDATE(), GETDATE(), 1, 'System', 'System'),
(NULL, 'TEST_Aubrey', 'Evans', '1994-09-07', 2, '723 Wellington St', 'Ottawa', 'ON', 'K8T8T8', '6135550046', 1, '<EMAIL>', 1, GETDATE(), GETDATE(), 1, 'System', 'System'),
(NULL, 'TEST_William', 'Edwards', '1991-05-25', 1, '834 Queen Street', 'Toronto', 'ON', 'M9U9U9', '4165550047', 2, '<EMAIL>', 2, GETDATE(), GETDATE(), 1, 'System', 'System'),
(NULL, 'TEST_Luna', 'Collins', '1996-01-12', 2, '945 Rue Sherbrooke', 'Montréal', 'QC', 'H0V0V0', '5145550048', 1, '<EMAIL>', 3, GETDATE(), GETDATE(), 1, 'System', 'System'),
(NULL, 'TEST_James', 'Stewart', '1988-10-30', 1, '156 Avenue Cartier', 'Québec', 'QC', 'G1W1W1', '4185550049', 2, '<EMAIL>', 4, GETDATE(), GETDATE(), 1, 'System', 'System'),
(NULL, 'TEST_Penelope', 'Sanchez', '1992-06-18', 2, '267 Robson Street', 'Vancouver', 'BC', 'V2X2X2', '6045550050', 1, '<EMAIL>', 5, GETDATE(), GETDATE(), 1, 'System', 'System');

-- Display count of inserted records
SELECT COUNT(*) as 'Total TEST Members Inserted' FROM Members WHERE Prenom LIKE 'TEST_%'; 