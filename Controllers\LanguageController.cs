using Microsoft.AspNetCore.Localization;
using Microsoft.AspNetCore.Mvc;

namespace ParaHockeyApp.Controllers
{
    /// <summary>
    /// Controller for handling language switching
    /// </summary>
    public class LanguageController : Controller
    {
        /// <summary>
        /// Sets the culture for the current user and redirects back to the referring page
        /// </summary>
        /// <param name="culture">The culture code (e.g., "fr", "en")</param>
        /// <param name="returnUrl">The URL to redirect to after setting the culture</param>
        /// <returns>Redirect to the specified URL or home page</returns>
        [HttpGet]
        [HttpPost]
        public IActionResult Switch(string culture, string returnUrl)
        {
            // Validate the culture parameter and map to full culture codes
            if (string.IsNullOrEmpty(culture))
            {
                culture = "fr-CA"; // Default to French Canadian
            }
            else if (culture == "fr")
            {
                culture = "fr-CA";
            }
            else if (culture == "en")
            {
                culture = "en-CA";
            }

            // Set the culture cookie
            Response.Cookies.Append(
                CookieRequestCultureProvider.DefaultCookieName,
                CookieRequestCultureProvider.MakeCookieValue(new RequestCulture(culture)),
                new CookieOptions
                {
                    Expires = DateTimeOffset.UtcNow.AddYears(1),
                    IsEssential = true,
                    SameSite = SameSiteMode.Lax
                }
            );

            // Redirect to the return URL or home page
            if (!string.IsNullOrEmpty(returnUrl) && Url.IsLocalUrl(returnUrl))
            {
                return Redirect(returnUrl);
            }

            return RedirectToAction("Index", "Home");
        }
    }
}