using ParaHockeyApp.Models.Entities;

namespace ParaHockeyApp.Services
{
    /// <summary>
    /// Service for managing the registration flow based on age and other business rules
    /// </summary>
    public interface IRegistrationFlowService
    {
        /// <summary>
        /// Determines if a person is under the age of majority based on their date of birth
        /// </summary>
        /// <param name="dateOfBirth">The person's date of birth</param>
        /// <returns>True if the person is under the age of majority, false otherwise</returns>
        bool IsUnderAge(DateTime dateOfBirth);

        /// <summary>
        /// Calculates the age of a person based on their date of birth
        /// </summary>
        /// <param name="dateOfBirth">The person's date of birth</param>
        /// <returns>The person's age in years</returns>
        int CalculateAge(DateTime dateOfBirth);

        /// <summary>
        /// Determines if a specific membership type is allowed for a person based on their date of birth
        /// </summary>
        /// <param name="membershipType">The membership type to check (e.g., "Junior", "Adult", etc.)</param>
        /// <param name="dateOfBirth">The person's date of birth</param>
        /// <returns>True if the membership type is allowed, false otherwise</returns>
        bool IsMembershipTypeAllowed(string membershipType, DateTime dateOfBirth);

        /// <summary>
        /// Gets the next form step in the registration flow after DOB entry
        /// </summary>
        /// <param name="dateOfBirth">The person's date of birth</param>
        /// <returns>The next form step: "Parent" for under 18, "EmergencyContact" for 18+</returns>
        string GetNextFormStep(DateTime dateOfBirth);

        /// <summary>
        /// Gets a localized explanation for why a membership type is disabled
        /// </summary>
        /// <param name="membershipType">The membership type that is disabled</param>
        /// <param name="dateOfBirth">The person's date of birth</param>
        /// <returns>A localized explanation message</returns>
        string GetMembershipTypeDisabledReason(string membershipType, DateTime dateOfBirth);

        /// <summary>
        /// Gets the configured age of majority for the current environment
        /// </summary>
        /// <returns>The age of majority (default: 18)</returns>
        int GetAgeOfMajority();
    }
}