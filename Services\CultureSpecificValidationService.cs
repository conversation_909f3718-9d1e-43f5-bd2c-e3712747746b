using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Globalization;
using System.Linq;
using System.Text.RegularExpressions;
using Microsoft.Extensions.Localization;
using Microsoft.Extensions.Logging;
using ParaHockeyApp.Resources;

namespace ParaHockeyApp.Services
{
    /// <summary>
    /// Service for culture-specific form validation with proper error message localization.
    /// Provides validation rules that adapt to Canadian English and French cultures.
    /// </summary>
    public interface ICultureSpecificValidationService
    {
        /// <summary>
        /// Validates a model with culture-specific rules and localized error messages
        /// </summary>
        ValidationResult ValidateModel<T>(T model, string? culture = null) where T : class;
        
        /// <summary>
        /// Validates a phone number according to Canadian format
        /// </summary>
        ValidationResult ValidatePhoneNumber(string phoneNumber, string? culture = null);
        
        /// <summary>
        /// Validates a postal code according to Canadian format
        /// </summary>
        ValidationResult ValidatePostalCode(string postalCode, string? culture = null);
        
        /// <summary>
        /// Validates a date with culture-specific formatting
        /// </summary>
        ValidationResult ValidateDate(string dateString, string? culture = null);
        
        /// <summary>
        /// Validates an email address with localized error messages
        /// </summary>
        ValidationResult ValidateEmail(string email, string? culture = null);
        
        /// <summary>
        /// Gets localized validation error message for a specific validation rule
        /// </summary>
        string GetLocalizedErrorMessage(string validationKey, string? culture = null, params object[] arguments);
        
        /// <summary>
        /// Creates a validation context with culture-specific settings
        /// </summary>
        ValidationContext CreateValidationContext<T>(T model, string? culture = null) where T : class;
    }
    
    public class CultureSpecificValidationService : ICultureSpecificValidationService
    {
        private readonly IStringLocalizer<SharedResourceMarker> _localizer;
        private readonly ICultureAwareFormattingService _formattingService;
        private readonly ILogger<CultureSpecificValidationService> _logger;
        
        // Canadian phone number validation
        private static readonly Regex CanadianPhoneRegex = new(
            @"^(\+?1[-.\s]?)?(\(?[2-9]\d{2}\)?[-.\s]?[2-9]\d{2}[-.\s]?\d{4})$", 
            RegexOptions.Compiled);
        
        // Canadian postal code validation
        private static readonly Regex CanadianPostalCodeRegex = new(
            @"^[A-Za-z]\d[A-Za-z][\s-]?\d[A-Za-z]\d$", 
            RegexOptions.Compiled);
        
        // Email validation (more comprehensive than built-in)
        private static readonly Regex EmailRegex = new(
            @"^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$", 
            RegexOptions.Compiled | RegexOptions.IgnoreCase);
        
        public CultureSpecificValidationService(
            IStringLocalizer<SharedResourceMarker> localizer,
            ICultureAwareFormattingService formattingService,
            ILogger<CultureSpecificValidationService> logger)
        {
            _localizer = localizer;
            _formattingService = formattingService;
            _logger = logger;
        }
        
        public ValidationResult ValidateModel<T>(T model, string? culture = null) where T : class
        {
            if (model == null)
            {
                return ValidationResult.Failure(GetLocalizedErrorMessage(LocalizationKeys.Validation.Required, culture));
            }
            
            var validationContext = CreateValidationContext(model, culture);
            var validationResults = new List<System.ComponentModel.DataAnnotations.ValidationResult>();
            
            try
            {
                var isValid = Validator.TryValidateObject(model, validationContext, validationResults, true);
                
                if (isValid)
                {
                    return ValidationResult.Success();
                }
                
                // Convert validation results to localized messages
                var localizedErrors = validationResults.Select(vr => new ValidationError
                {
                    PropertyName = vr.MemberNames.FirstOrDefault() ?? string.Empty,
                    ErrorMessage = LocalizeValidationMessage(vr.ErrorMessage, culture),
                    AttemptedValue = GetPropertyValue(model, vr.MemberNames.FirstOrDefault())
                }).ToList();
                
                return ValidationResult.Failure(localizedErrors);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error validating model of type {ModelType}", typeof(T).Name);
                return ValidationResult.Failure(new List<ValidationError>
                {
                    new ValidationError
                    {
                        PropertyName = string.Empty,
                        ErrorMessage = GetLocalizedErrorMessage(LocalizationKeys.Errors.ValidationFailed, culture),
                        AttemptedValue = null
                    }
                });
            }
        }
        
        public ValidationResult ValidatePhoneNumber(string phoneNumber, string? culture = null)
        {
            if (string.IsNullOrWhiteSpace(phoneNumber))
            {
                return ValidationResult.Failure(new List<ValidationError>
                {
                    new ValidationError
                    {
                        PropertyName = "PhoneNumber",
                        ErrorMessage = GetLocalizedErrorMessage(LocalizationKeys.Validation.Required, culture),
                        AttemptedValue = phoneNumber
                    }
                });
            }
            
            if (!CanadianPhoneRegex.IsMatch(phoneNumber))
            {
                var expectedFormat = culture?.StartsWith("fr") == true 
                    ? "(XXX) XXX-XXXX ou +1 (XXX) XXX-XXXX"
                    : "(XXX) XXX-XXXX or +1 (XXX) XXX-XXXX";
                
                return ValidationResult.Failure(new List<ValidationError>
                {
                    new ValidationError
                    {
                        PropertyName = "PhoneNumber",
                        ErrorMessage = GetLocalizedErrorMessage(LocalizationKeys.Validation.PhoneFormat, culture, expectedFormat),
                        AttemptedValue = phoneNumber
                    }
                });
            }
            
            return ValidationResult.Success();
        }
        
        public ValidationResult ValidatePostalCode(string postalCode, string? culture = null)
        {
            if (string.IsNullOrWhiteSpace(postalCode))
            {
                return ValidationResult.Failure(new List<ValidationError>
                {
                    new ValidationError
                    {
                        PropertyName = "PostalCode",
                        ErrorMessage = GetLocalizedErrorMessage(LocalizationKeys.Validation.Required, culture),
                        AttemptedValue = postalCode
                    }
                });
            }
            
            if (!CanadianPostalCodeRegex.IsMatch(postalCode))
            {
                var expectedFormat = culture?.StartsWith("fr") == true 
                    ? "A1A 1A1 (format canadien)"
                    : "A1A 1A1 (Canadian format)";
                
                return ValidationResult.Failure(new List<ValidationError>
                {
                    new ValidationError
                    {
                        PropertyName = "PostalCode",
                        ErrorMessage = GetLocalizedErrorMessage(LocalizationKeys.Validation.PostalCodeFormat, culture, expectedFormat),
                        AttemptedValue = postalCode
                    }
                });
            }
            
            return ValidationResult.Success();
        }
        
        public ValidationResult ValidateDate(string dateString, string? culture = null)
        {
            if (string.IsNullOrWhiteSpace(dateString))
            {
                return ValidationResult.Failure(new List<ValidationError>
                {
                    new ValidationError
                    {
                        PropertyName = "Date",
                        ErrorMessage = GetLocalizedErrorMessage(LocalizationKeys.Validation.Required, culture),
                        AttemptedValue = dateString
                    }
                });
            }
            
            var cultureInfo = GetCultureInfo(culture);
            
            if (!DateTime.TryParse(dateString, cultureInfo, DateTimeStyles.None, out var parsedDate))
            {
                var expectedFormat = culture?.StartsWith("fr") == true 
                    ? cultureInfo.DateTimeFormat.ShortDatePattern + " (ex: 31/12/2023)"
                    : cultureInfo.DateTimeFormat.ShortDatePattern + " (e.g., 12/31/2023)";
                
                return ValidationResult.Failure(new List<ValidationError>
                {
                    new ValidationError
                    {
                        PropertyName = "Date",
                        ErrorMessage = GetLocalizedErrorMessage(LocalizationKeys.Validation.DateFormat, culture, expectedFormat),
                        AttemptedValue = dateString
                    }
                });
            }
            
            // Additional date validation (e.g., not in future for birth dates)
            if (parsedDate > DateTime.Today)
            {
                return ValidationResult.Failure(new List<ValidationError>
                {
                    new ValidationError
                    {
                        PropertyName = "Date",
                        ErrorMessage = GetLocalizedErrorMessage("Validation.Date.FutureNotAllowed", culture),
                        AttemptedValue = dateString
                    }
                });
            }
            
            return ValidationResult.Success();
        }
        
        public ValidationResult ValidateEmail(string email, string? culture = null)
        {
            if (string.IsNullOrWhiteSpace(email))
            {
                return ValidationResult.Failure(new List<ValidationError>
                {
                    new ValidationError
                    {
                        PropertyName = "Email",
                        ErrorMessage = GetLocalizedErrorMessage(LocalizationKeys.Validation.Required, culture),
                        AttemptedValue = email
                    }
                });
            }
            
            if (!EmailRegex.IsMatch(email))
            {
                return ValidationResult.Failure(new List<ValidationError>
                {
                    new ValidationError
                    {
                        PropertyName = "Email",
                        ErrorMessage = GetLocalizedErrorMessage(LocalizationKeys.Validation.EmailFormat, culture),
                        AttemptedValue = email
                    }
                });
            }
            
            return ValidationResult.Success();
        }
        
        public string GetLocalizedErrorMessage(string validationKey, string? culture = null, params object[] arguments)
        {
            try
            {
                // Set culture context if provided
                if (!string.IsNullOrEmpty(culture))
                {
                    var cultureInfo = GetCultureInfo(culture);
                    CultureInfo.CurrentCulture = cultureInfo;
                    CultureInfo.CurrentUICulture = cultureInfo;
                }
                
                var localizedMessage = _localizer[validationKey, arguments];
                
                if (localizedMessage.ResourceNotFound)
                {
                    _logger.LogWarning("Localization key not found: {Key}", validationKey);
                    return GenerateFallbackMessage(validationKey, arguments);
                }
                
                return localizedMessage.Value;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting localized error message for key: {Key}", validationKey);
                return GenerateFallbackMessage(validationKey, arguments);
            }
        }
        
        public ValidationContext CreateValidationContext<T>(T model, string? culture = null) where T : class
        {
            var validationContext = new ValidationContext(model);
            
            // Add culture-specific services to validation context
            validationContext.Items.Add("Culture", culture ?? "fr-CA");
            validationContext.Items.Add("FormattingService", _formattingService);
            validationContext.Items.Add("Localizer", _localizer);
            
            return validationContext;
        }
        
        #region Helper Methods
        
        private CultureInfo GetCultureInfo(string? culture)
        {
            try
            {
                return new CultureInfo(culture ?? "fr-CA");
            }
            catch (CultureNotFoundException ex)
            {
                _logger.LogWarning(ex, "Invalid culture specified: {Culture}. Using fr-CA", culture);
                return new CultureInfo("fr-CA");
            }
        }
        
        private string LocalizeValidationMessage(string? originalMessage, string? culture)
        {
            if (string.IsNullOrEmpty(originalMessage))
                return GetLocalizedErrorMessage(LocalizationKeys.Errors.ValidationFailed, culture);
            
            // Try to map common validation messages to localized keys
            var messageKeyMappings = new Dictionary<string, string>
            {
                { "required", LocalizationKeys.Validation.Required },
                { "email", LocalizationKeys.Validation.EmailFormat },
                { "phone", LocalizationKeys.Validation.PhoneFormat },
                { "range", LocalizationKeys.Validation.Range },
                { "length", LocalizationKeys.Validation.StringLength }
            };
            
            var lowerMessage = originalMessage.ToLowerInvariant();
            var matchingKey = messageKeyMappings.FirstOrDefault(kvp => lowerMessage.Contains(kvp.Key));
            
            if (!string.IsNullOrEmpty(matchingKey.Value))
            {
                return GetLocalizedErrorMessage(matchingKey.Value, culture);
            }
            
            return originalMessage;
        }
        
        private string GenerateFallbackMessage(string validationKey, params object[] arguments)
        {
            // Generate a user-friendly fallback message
            var keyParts = validationKey.Split('.');
            var messagePart = keyParts.LastOrDefault() ?? validationKey;
            
            var fallbackMessage = messagePart switch
            {
                "Required" => "This field is required.",
                "EmailFormat" => "Please enter a valid email address.",
                "PhoneFormat" => "Please enter a valid phone number.",
                "PostalCodeFormat" => "Please enter a valid postal code.",
                "DateFormat" => "Please enter a valid date.",
                _ => "Please check your input."
            };
            
            try
            {
                return arguments?.Length > 0 ? string.Format(fallbackMessage, arguments) : fallbackMessage;
            }
            catch
            {
                return fallbackMessage;
            }
        }
        
        private object? GetPropertyValue<T>(T model, string? propertyName) where T : class
        {
            if (string.IsNullOrEmpty(propertyName) || model == null)
                return null;
            
            try
            {
                var property = typeof(T).GetProperty(propertyName);
                return property?.GetValue(model);
            }
            catch
            {
                return null;
            }
        }
        
        #endregion
    }
    
    #region Data Models
    
    /// <summary>
    /// Represents the result of a validation operation
    /// </summary>
    public class ValidationResult
    {
        public bool IsValid { get; private set; }
        public List<ValidationError> Errors { get; private set; } = new();
        
        private ValidationResult(bool isValid, List<ValidationError>? errors = null)
        {
            IsValid = isValid;
            Errors = errors ?? new List<ValidationError>();
        }
        
        public static ValidationResult Success() => new(true);
        
        public static ValidationResult Failure(List<ValidationError> errors) => new(false, errors);
        
        public static ValidationResult Failure(ValidationError error) => new(false, new List<ValidationError> { error });
        
        public static ValidationResult Failure(string errorMessage, string propertyName = "")
        {
            return new ValidationResult(false, new List<ValidationError>
            {
                new ValidationError
                {
                    PropertyName = propertyName,
                    ErrorMessage = errorMessage,
                    AttemptedValue = null
                }
            });
        }
    }
    
    /// <summary>
    /// Represents a validation error with localized message
    /// </summary>
    public class ValidationError
    {
        public string PropertyName { get; set; } = string.Empty;
        public string ErrorMessage { get; set; } = string.Empty;
        public object? AttemptedValue { get; set; }
        public string? ErrorCode { get; set; }
    }
    
    #endregion
    
    #region Custom Validation Attributes
    
    /// <summary>
    /// Custom validation attribute for Canadian phone numbers with localized error messages
    /// </summary>
    public class CanadianPhoneAttribute : ValidationAttribute
    {
        public override bool IsValid(object? value)
        {
            if (value == null || string.IsNullOrWhiteSpace(value.ToString()))
                return true; // Let Required attribute handle null/empty
            
            var phoneNumber = value.ToString()!;
            return CanadianPhoneRegex.IsMatch(phoneNumber);
        }
        
        public override string FormatErrorMessage(string name)
        {
            return ErrorMessage ?? $"The {name} field must be a valid Canadian phone number.";
        }
        
        private static readonly Regex CanadianPhoneRegex = new(
            @"^(\+?1[-.\s]?)?(\(?[2-9]\d{2}\)?[-.\s]?[2-9]\d{2}[-.\s]?\d{4})$", 
            RegexOptions.Compiled);
    }
    
    /// <summary>
    /// Custom validation attribute for Canadian postal codes with localized error messages
    /// </summary>
    public class CanadianPostalCodeAttribute : ValidationAttribute
    {
        public override bool IsValid(object? value)
        {
            if (value == null || string.IsNullOrWhiteSpace(value.ToString()))
                return true; // Let Required attribute handle null/empty
            
            var postalCode = value.ToString()!;
            return CanadianPostalCodeRegex.IsMatch(postalCode);
        }
        
        public override string FormatErrorMessage(string name)
        {
            return ErrorMessage ?? $"The {name} field must be a valid Canadian postal code (A1A 1A1).";
        }
        
        private static readonly Regex CanadianPostalCodeRegex = new(
            @"^[A-Za-z]\d[A-Za-z][\s-]?\d[A-Za-z]\d$", 
            RegexOptions.Compiled);
    }
    
    /// <summary>
    /// Custom validation attribute for minimum age with localized error messages
    /// </summary>
    public class MinimumAgeAttribute : ValidationAttribute
    {
        private readonly int _minimumAge;
        
        public MinimumAgeAttribute(int minimumAge)
        {
            _minimumAge = minimumAge;
        }
        
        public override bool IsValid(object? value)
        {
            if (value is DateTime birthDate)
            {
                var age = DateTime.Today.Year - birthDate.Year;
                if (birthDate.Date > DateTime.Today.AddYears(-age))
                    age--;
                
                return age >= _minimumAge;
            }
            
            return false;
        }
        
        public override string FormatErrorMessage(string name)
        {
            return ErrorMessage ?? $"You must be at least {_minimumAge} years old.";
        }
    }
    
    #endregion
}