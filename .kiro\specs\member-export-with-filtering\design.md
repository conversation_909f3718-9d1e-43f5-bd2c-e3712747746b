# Design Document

## Overview

This feature enhances the admin members page with comprehensive export functionality and advanced filtering capabilities. The design integrates seamlessly with the existing member search infrastructure while adding new export formats (CSV and Excel) and intelligent file naming based on applied filters and search criteria.

## Architecture

### Component Structure

```
AdminController
├── Enhanced Members Action (existing)
├── New Export Actions
│   ├── ExportMembersAdvanced (CSV/Excel)
│   └── GetFilterOptions (AJAX endpoint)
├── Enhanced MemberSearchService (existing)
├── New MemberExportService
└── Enhanced UI Components
    ├── Filter Dropdown System
    ├── Export Button with Format Selection
    └── Dynamic Filter Options
```

### Data Flow

1. **Filter Selection**: User selects filter category → AJAX call retrieves filter options → Secondary dropdown populated
2. **Search/Filter Application**: User applies filters → MemberSearchService processes criteria → Results displayed
3. **Export Trigger**: User clicks export → Format selection modal → Export service generates file → Download initiated

## Components and Interfaces

### 1. Enhanced DTOs

#### MemberExportRequest

```csharp
public class MemberExportRequest : MemberSearchRequest
{
    public ExportFormat Format { get; set; } = ExportFormat.Excel;
    public bool IncludeHeaders { get; set; } = true;
    public List<string>? SelectedFields { get; set; }
}

public enum ExportFormat
{
    CSV = 1,
    Excel = 2
}
```

#### FilterOptionsResponse

```csharp
public class FilterOptionsResponse
{
    public string FilterType { get; set; }
    public List<FilterOption> Options { get; set; }
}

public class FilterOption
{
    public string Value { get; set; }
    public string Display { get; set; }
    public int Count { get; set; }
}
```

### 2. Services

#### IMemberExportService

```csharp
public interface IMemberExportService
{
    Task<byte[]> ExportToCsvAsync(MemberExportRequest request);
    Task<byte[]> ExportToExcelAsync(MemberExportRequest request);
    string GenerateFileName(MemberExportRequest request);
    Task<FilterOptionsResponse> GetFilterOptionsAsync(string filterType, string? searchTerm = null);
}
```

#### Enhanced IMemberSearchService

```csharp
// Add to existing interface
Task<List<string>> GetDistinctCitiesAsync(string? searchTerm = null);
Task<List<string>> GetDistinctProvincesAsync();
Task<Dictionary<string, int>> GetRegistrationTypeCountsAsync();
```

### 3. Controller Enhancements

#### New AdminController Actions

```csharp
[HttpGet]
public async Task<IActionResult> ExportMembersAdvanced(MemberExportRequest request)

[HttpGet]
public async Task<IActionResult> GetFilterOptions(string filterType, string? searchTerm = null)
```

### 4. UI Components

#### Filter System Structure

```html
<div class="filter-container">
    <select id="filterType" class="form-select">
        <option value="">Select Filter...</option>
        <option value="registrationType">Registration Type</option>
        <option value="province">Province</option>
        <option value="city">City</option>
        <option value="status">Status</option>
    </select>

    <select id="filterValue" class="form-select" style="display:none;">
        <!-- Dynamically populated -->
    </select>

    <input
        type="text"
        id="citySearch"
        class="form-control"
        placeholder="Type to search cities..."
        style="display:none;" />
</div>
```

#### Export Button System

```html
<div class="export-container">
    <button
        type="button"
        class="btn btn-success"
        data-bs-toggle="modal"
        data-bs-target="#exportModal">
        <i class="fas fa-download"></i> Export
    </button>
</div>
```

## Data Models

### Export Configuration

```csharp
public class ExportConfiguration
{
    public static readonly Dictionary<string, string> FieldMappings = new()
    {
        { "Id", "Member ID" },
        { "FirstName", "First Name" },
        { "LastName", "Last Name" },
        { "Email", "Email Address" },
        { "Phone", "Phone Number" },
        { "DateOfBirth", "Date of Birth" },
        { "Address", "Address" },
        { "City", "City" },
        { "Province", "Province" },
        { "PostalCode", "Postal Code" },
        { "RegistrationType", "Registration Type" },
        { "IsActive", "Status" },
        { "DateCreated", "Registration Date" }
    };
}
```

### File Naming Logic

```csharp
public class FileNamingService
{
    public string GenerateFileName(MemberExportRequest request)
    {
        var parts = new List<string>();

        if (!string.IsNullOrEmpty(request.SearchTerm))
            parts.Add($"Search_{SanitizeFileName(request.SearchTerm)}");

        if (request.RegistrationTypeIds?.Any() == true)
            parts.Add($"Type_{string.Join("-", request.RegistrationTypeIds)}");

        if (!string.IsNullOrEmpty(request.Province))
            parts.Add($"Province_{SanitizeFileName(request.Province)}");

        if (!string.IsNullOrEmpty(request.City))
            parts.Add($"City_{SanitizeFileName(request.City)}");

        if (request.IsActive.HasValue)
            parts.Add($"Status_{(request.IsActive.Value ? "Active" : "Inactive")}");

        var baseName = parts.Any() ? string.Join("_", parts) : "All_Members";
        var date = DateTime.Now.ToString("yyyy_MMMM_dd", CultureInfo.InvariantCulture);
        var extension = request.Format == ExportFormat.Excel ? "xlsx" : "csv";

        return $"{baseName}_{date}.{extension}";
    }
}
```

## Error Handling

### Export Error Scenarios

1. **Large Dataset**: Implement chunked processing for exports > 10,000 records
2. **Invalid Filters**: Validate filter combinations before processing
3. **File Generation Errors**: Graceful fallback to CSV if Excel generation fails
4. **Memory Constraints**: Stream-based processing for large exports

### Error Response Structure

```csharp
public class ExportErrorResponse
{
    public bool Success { get; set; }
    public string ErrorMessage { get; set; }
    public string? ErrorCode { get; set; }
    public Dictionary<string, string>? ValidationErrors { get; set; }
}
```

## Testing Strategy

### Unit Tests

-   **MemberExportService**: Test CSV/Excel generation with various data sets
-   **FileNamingService**: Test filename generation with different filter combinations
-   **FilterOptionsService**: Test filter option retrieval and caching

### Integration Tests

-   **Export Flow**: End-to-end export with different filter combinations
-   **Large Dataset**: Performance testing with 10,000+ member records
-   **Filter Interaction**: Test filter combinations and search integration

### UI Tests

-   **Filter Dropdown Behavior**: Test dynamic option loading
-   **Export Modal**: Test format selection and download trigger
-   **Responsive Design**: Test on mobile and tablet devices

## Performance Considerations

### Database Optimization

-   **Indexed Queries**: Ensure proper indexes on filterable fields (City, Province, RegistrationType)
-   **Query Optimization**: Use projection to select only needed fields for export
-   **Caching**: Cache filter options for 5 minutes to reduce database load

### Export Performance

-   **Streaming**: Use streaming for large exports to minimize memory usage
-   **Background Processing**: Consider background jobs for exports > 5,000 records
-   **Compression**: Compress large Excel files before download

### Frontend Performance

-   **Debounced Search**: Implement 300ms debounce for city search
-   **Lazy Loading**: Load filter options only when dropdown is opened
-   **Progress Indicators**: Show progress for large exports

## Security Considerations

### Access Control

-   **Admin Only**: Ensure only authenticated admins can access export functionality
-   **Audit Logging**: Log all export activities with user identification
-   **Rate Limiting**: Limit export requests to prevent abuse

### Data Protection

-   **Field Filtering**: Allow admins to exclude sensitive fields from exports
-   **File Cleanup**: Automatically delete temporary export files after download
-   **Secure Downloads**: Use secure headers for file downloads

## Localization Support

### Multilingual Elements

-   **Filter Labels**: All filter dropdown labels support French/English
-   **Export Modal**: Format selection modal in both languages
-   **Error Messages**: All error messages localized
-   **File Headers**: Excel/CSV headers in user's selected language

### Date Formatting

-   **Filename Dates**: Use culture-neutral format for filenames
-   **Export Data**: Format dates according to user's locale in export content
