Write-Host "🔧 RESTORING TEST SERVER CONFIGURATION" -ForegroundColor Yellow
Write-Host "=====================================" -ForegroundColor Yellow

$testConfigPath = "C:\inetpub\ParaHockey\Test\appsettings.Staging.json"

# Create the correct configuration for Test environment
$testConfig = @'
{
    "DetailedErrors": true,
    "Logging": {
        "LogLevel": {
            "Default": "Information",
            "Microsoft.AspNetCore": "Warning"
        }
    },
    "ConnectionStrings": {
        "DefaultConnection": "Server=SIMBA\\SQLEXPRESS;User Id=ParaHockeyUser;Password=***************;Database=ParaHockeyDB_TEST;Encrypt=False;TrustServerCertificate=True;"
    },
    "Environment": {
        "Name": "TEST",
        "Theme": "danger",
        "ShowBanner": true,
        "UseAuthentication": true,
        "BannerText": "Parahockey TEST Site",
        "ShowDevelopmentTools": true,
        "EnableDetailedErrorLogging": true,
        "EnvironmentIndicatorColor": "danger",
        "ShowUserFriendlyErrors": true,
        "ErrorDetailLevel": "detailed"
    },
    "Email": {
        "SmtpHost": "smtp.office365.com",
        "SmtpPort": "587",
        "Username": "<EMAIL>",
        "Password": "L@535539113654on",
        "FromEmail": "<EMAIL>",
        "FromName": "Parahockey Verification"
    }
}
'@

try {
    # Backup current file if it exists
    if (Test-Path $testConfigPath) {
        $backupPath = "$testConfigPath.backup_$(Get-Date -Format 'yyyyMMdd_HHmmss')"
        Copy-Item $testConfigPath $backupPath
        Write-Host "✅ Backed up current config to: $backupPath" -ForegroundColor Green
    }
    
    # Write the correct configuration
    Set-Content -Path $testConfigPath -Value $testConfig -Force
    Write-Host "✅ Test configuration restored!" -ForegroundColor Green
    
    # Restart the website and app pool
    Import-Module WebAdministration
    
    Write-Host "`nRestarting Test website and app pool..." -ForegroundColor Yellow
    Stop-WebAppPool -Name "ParaHockey-Test" -ErrorAction SilentlyContinue
    Stop-Website -Name "ParaHockey-Test" -ErrorAction SilentlyContinue
    Start-Sleep -Seconds 2
    Start-WebAppPool -Name "ParaHockey-Test"
    Start-Website -Name "ParaHockey-Test"
    
    Write-Host "✅ Test website restarted!" -ForegroundColor Green
    
    # Test the health endpoint
    Start-Sleep -Seconds 5
    Write-Host "`nTesting health endpoint..." -ForegroundColor Yellow
    try {
        $response = Invoke-WebRequest -Uri "https://parahockeytest.complys.com/health" -UseBasicParsing -TimeoutSec 10
        if ($response.StatusCode -eq 200) {
            Write-Host "✅ Health endpoint is working! Site is back online!" -ForegroundColor Green
            Write-Host "✅ Red theme should be restored!" -ForegroundColor Green
        }
    } catch {
        Write-Host "⚠️ Health check failed, but config is restored. May need more time to start." -ForegroundColor Yellow
    }
    
} catch {
    Write-Host "❌ ERROR: $_" -ForegroundColor Red
    Write-Host "You may need to manually restore the configuration on the server." -ForegroundColor Red
}

Write-Host "`n📝 Configuration has been written to: $testConfigPath" -ForegroundColor Cyan
Write-Host "The Test site should now have:" -ForegroundColor Cyan
Write-Host "  - Red theme (danger)" -ForegroundColor Red
Write-Host "  - TEST database connection" -ForegroundColor Yellow
Write-Host "  - Authentication enabled" -ForegroundColor Yellow