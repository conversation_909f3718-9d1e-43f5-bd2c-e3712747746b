using Xunit;
using FluentAssertions;
using ParaHockey.E2E.Tests.Infrastructure;
using OpenQA.Selenium;
using OpenQA.Selenium.Support.UI;
using System.Threading;

namespace ParaHockey.E2E.Tests.Tests
{
    public class EventCategoryTests : BaseTest, IDisposable
    {
        public EventCategoryTests() : base()
        {
        }

        [Fact]
        public void EventCreation_ShouldShowNewCategories_CampAndSerie()
        {
            try
            {
                // Arrange - Navigate to admin calendar page
                NavigateToAdminCalendar();
                
                // Act - Open add event modal
                var addEventBtn = Driver.FindElement(By.Id("addEventBtn"));
                addEventBtn.Click();
                Thread.Sleep(500); // Wait for modal
                
                // Find the category dropdown
                var categoryDropdown = Driver.FindElement(By.Id("eventCategory"));
                var selectElement = new SelectElement(categoryDropdown);
                
                // Get all options
                var options = selectElement.Options.Select(o => o.Text).ToList();
                
                // Assert - Verify Camp and Série categories exist
                options.Should().Contain("Camp");
                options.Should().Contain("Série");
                
                // Close modal
                var closeButton = Driver.FindElement(By.CssSelector("#addEventModal .btn-close"));
                closeButton.Click();
            }
            catch (Exception ex)
            {
                TestContext.WriteLine($"Test failed: {ex.Message}");
                throw;
            }
        }

        [Fact]
        public void EventWithTentatifInTitle_ShouldAutoAssignTentativeCategory()
        {
            try
            {
                // Arrange - Navigate to admin calendar page
                NavigateToAdminCalendar();
                
                // Act - Create event with "Tentatif" in title
                var addEventBtn = Driver.FindElement(By.Id("addEventBtn"));
                addEventBtn.Click();
                Thread.Sleep(500); // Wait for modal
                
                // Fill event form
                var titleInput = Driver.FindElement(By.Id("eventTitle"));
                titleInput.SendKeys("Match Tentatif - Équipe A vs B");
                
                // Don't select any category - let auto-assign work
                var categoryDropdown = Driver.FindElement(By.Id("eventCategory"));
                var selectElement = new SelectElement(categoryDropdown);
                selectElement.SelectByIndex(0); // Select first option (usually empty or "Select category")
                
                // Set dates
                var startDateInput = Driver.FindElement(By.Id("eventStartDate"));
                var endDateInput = Driver.FindElement(By.Id("eventEndDate"));
                
                var tomorrow = DateTime.Now.AddDays(1);
                var startTime = new DateTime(tomorrow.Year, tomorrow.Month, tomorrow.Day, 10, 0, 0);
                var endTime = startTime.AddHours(1);
                
                var startValue = startTime.ToString("yyyy-MM-ddTHH:mm");
                var endValue = endTime.ToString("yyyy-MM-ddTHH:mm");
                
                startDateInput.Clear();
                startDateInput.SendKeys(startValue);
                endDateInput.Clear();
                endDateInput.SendKeys(endValue);
                
                // Save event
                var saveButton = Driver.FindElement(By.Id("saveEventBtn"));
                saveButton.Click();
                Thread.Sleep(2000); // Wait for save
                
                // Refresh page to see the event
                Driver.Navigate().Refresh();
                Thread.Sleep(1000);
                
                // Find the event on calendar and verify it has the tentative category color
                var calendarEvent = Driver.FindElements(By.CssSelector(".fc-event"))
                    .FirstOrDefault(e => e.Text.Contains("Match Tentatif"));
                
                calendarEvent.Should().NotBeNull("Event should be visible on calendar");
                
                // Get the background color
                var backgroundColor = calendarEvent.GetCssValue("background-color");
                
                // Tentative category uses neutral grey (#adb5bd)
                // Note: Browser may return rgb format
                backgroundColor.Should().Contain("173"); // RGB for #adb5bd
            }
            catch (Exception ex)
            {
                TestContext.WriteLine($"Test failed: {ex.Message}");
                throw;
            }
        }

        [Fact]
        public void EventWithDefiSportifInTitle_ShouldAutoAssignTournamentCategory()
        {
            try
            {
                // Arrange - Navigate to admin calendar page
                NavigateToAdminCalendar();
                
                // Act - Create event with "Défi Sportif" in title
                var addEventBtn = Driver.FindElement(By.Id("addEventBtn"));
                addEventBtn.Click();
                Thread.Sleep(500); // Wait for modal
                
                // Fill event form
                var titleInput = Driver.FindElement(By.Id("eventTitle"));
                titleInput.SendKeys("Défi Sportif 2025 - ParaHockey");
                
                // Don't select any category - let auto-assign work
                var categoryDropdown = Driver.FindElement(By.Id("eventCategory"));
                var selectElement = new SelectElement(categoryDropdown);
                selectElement.SelectByIndex(0); // Select first option
                
                // Set dates
                var startDateInput = Driver.FindElement(By.Id("eventStartDate"));
                var endDateInput = Driver.FindElement(By.Id("eventEndDate"));
                
                var nextWeek = DateTime.Now.AddDays(7);
                var startTime = new DateTime(nextWeek.Year, nextWeek.Month, nextWeek.Day, 9, 0, 0);
                var endTime = startTime.AddHours(8);
                
                var startValue = startTime.ToString("yyyy-MM-ddTHH:mm");
                var endValue = endTime.ToString("yyyy-MM-ddTHH:mm");
                
                startDateInput.Clear();
                startDateInput.SendKeys(startValue);
                endDateInput.Clear();
                endDateInput.SendKeys(endValue);
                
                // Save event
                var saveButton = Driver.FindElement(By.Id("saveEventBtn"));
                saveButton.Click();
                Thread.Sleep(2000); // Wait for save
                
                // Refresh page to see the event
                Driver.Navigate().Refresh();
                Thread.Sleep(1000);
                
                // Find the event on calendar and verify it has the tournament category color
                var calendarEvent = Driver.FindElements(By.CssSelector(".fc-event"))
                    .FirstOrDefault(e => e.Text.Contains("Défi Sportif"));
                
                calendarEvent.Should().NotBeNull("Event should be visible on calendar");
                
                // Get the background color
                var backgroundColor = calendarEvent.GetCssValue("background-color");
                
                // Tournament category uses yellow (#ffc107)
                // Note: Browser may return rgb format
                backgroundColor.Should().Contain("255"); // RGB for #ffc107
                backgroundColor.Should().Contain("193");
            }
            catch (Exception ex)
            {
                TestContext.WriteLine($"Test failed: {ex.Message}");
                throw;
            }
        }

        [Fact]
        public void PublicCalendar_ShouldDisplayNewCategoriesCorrectly()
        {
            try
            {
                // Navigate to public calendar
                Driver.Navigate().GoToUrl($"{TestConfiguration.BaseUrl}/Home/PublicCalendar");
                Thread.Sleep(2000); // Wait for calendar to load
                
                // Check if legend contains new categories
                var legendItems = Driver.FindElements(By.CssSelector(".event-legend-item"));
                var legendTexts = legendItems.Select(item => item.Text).ToList();
                
                // Assert - Verify Camp and Série appear in legend
                legendTexts.Should().Contain(text => text.Contains("Camp"));
                legendTexts.Should().Contain(text => text.Contains("Série"));
                
                // Verify colors are correct
                var campLegend = legendItems.FirstOrDefault(item => item.Text.Contains("Camp"));
                if (campLegend != null)
                {
                    var colorChip = campLegend.FindElement(By.CssSelector(".color-chip"));
                    var backgroundColor = colorChip.GetCssValue("background-color");
                    // Camp uses teal (#20c997)
                    backgroundColor.Should().Contain("32"); // RGB for #20c997
                    backgroundColor.Should().Contain("201");
                }
                
                var serieLegend = legendItems.FirstOrDefault(item => item.Text.Contains("Série"));
                if (serieLegend != null)
                {
                    var colorChip = serieLegend.FindElement(By.CssSelector(".color-chip"));
                    var backgroundColor = colorChip.GetCssValue("background-color");
                    // Série uses indigo (#6610f2)
                    backgroundColor.Should().Contain("102"); // RGB for #6610f2
                    backgroundColor.Should().Contain("16");
                }
            }
            catch (Exception ex)
            {
                TestContext.WriteLine($"Test failed: {ex.Message}");
                throw;
            }
        }

        private void NavigateToAdminCalendar()
        {
            // Login as admin
            Driver.Navigate().GoToUrl($"{TestConfiguration.BaseUrl}/Admin/Login");
            Thread.Sleep(1000);
            
            var usernameInput = Driver.FindElement(By.Id("username"));
            var passwordInput = Driver.FindElement(By.Id("password"));
            var loginButton = Driver.FindElement(By.Id("loginButton"));
            
            usernameInput.SendKeys(TestData.AdminUsername);
            passwordInput.SendKeys(TestData.AdminPassword);
            loginButton.Click();
            
            Thread.Sleep(1000);
            
            // Navigate to calendar
            Driver.Navigate().GoToUrl($"{TestConfiguration.BaseUrl}/Admin/Calendar");
            Thread.Sleep(2000); // Wait for calendar to load
        }

        public void Dispose()
        {
            // Cleanup is handled by BaseTest
        }
    }
}