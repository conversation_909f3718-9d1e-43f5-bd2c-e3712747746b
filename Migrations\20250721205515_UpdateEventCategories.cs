﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace ParaHockeyApp.Migrations
{
    /// <inheritdoc />
    public partial class UpdateEventCategories : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            // Update Event Categories - Remove Social and Fundraiser, Add Tentative and FirstShift
            migrationBuilder.Sql(@"
                -- Deactivate Social and Fundraiser categories
                UPDATE EventCategories SET IsActive = 0 WHERE Id IN (6, 7);
                
                -- Add new categories: Tentative and FirstShift
                IF NOT EXISTS (SELECT 1 FROM EventCategories WHERE DisplayNameKey = 'EventCategory_Tentative')
                BEGIN
                    INSERT INTO EventCategories (DisplayNameKey, DescriptionKey, Color, IconClass, DisplayOrder, RequiresRegistration, MaxParticipants, DateCreated, IsActive, CreatedBySource)
                    VALUES 
                        ('EventCategory_Tentative', 'EventCategory_Tentative_Desc', '#ffa500', 'fas fa-question-circle', 6, 0, -1, GETUTCDATE(), 1, 0),
                        ('EventCategory_FirstShift', 'EventCategory_FirstShift_Desc', '#9932cc', 'fas fa-star', 7, 1, 25, GETUTCDATE(), 1, 0)
                END
                
                -- Update display order for Other category to be last
                UPDATE EventCategories SET DisplayOrder = 8 WHERE Id = 8;
            ");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            // Rollback Event Category Changes
            migrationBuilder.Sql(@"
                -- Reactivate Social and Fundraiser categories
                UPDATE EventCategories SET IsActive = 1 WHERE Id IN (6, 7);
                
                -- Remove new categories
                DELETE FROM EventCategories WHERE DisplayNameKey IN ('EventCategory_Tentative', 'EventCategory_FirstShift');
                
                -- Restore original display order for Other category
                UPDATE EventCategories SET DisplayOrder = 8 WHERE Id = 8;
            ");
        }
    }
}
