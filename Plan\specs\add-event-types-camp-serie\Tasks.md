# Tasks – Add Camp & Série Event Categories

---

## Database & Localization
- [x] DB-1 (FR-1) Create EF migration **AddCampAndSerieEventCategories** that inserts categories Camp (Id=9), <PERSON>érie (Id=10) and Tentatif (Id=11 if missing) into `EventCategories`  
- [x] DB-2 (FR-6) Within the same migration, execute SQL updates to recategorize existing Tentatif and Défi Sportif events (see Design §4.2)  
- [x] LOC-1 (FR-2) Add new keys & translations to `SharedResource.resx` and `SharedResource.en-CA.resx`  

## Service Layer
- [x] SRV-1 (FR-4, FR-5) Add `DetermineCategoryId` helper to `Services/EventService.cs`  
- [x] SRV-2 (FR-4, FR-5) Call helper from `CreateEventAsync` and any import pipeline if `EventCategoryId` is not set  

## UI / ViewModel
- [x] UI-1 (FR-3) Ensure `EventCategorySelectList` is repopulated from DB so new categories appear (likely automatic)  
- [x] UI-2 (FR-3) Verify colour legend & chips render correctly for new categories  

## Tests
- [x] TEST-1 Add unit test **EventServiceTests.DetermineCategoryId** for Tentatif & Défi Sportif mapping  
- [x] TEST-2 Extend E2E tests `EventTimeOffsetTests` or create new **EventCategoryTests** to validate calendars show Camp & Série  

## Documentation & Deployment
- [x] DOC-1 Update any admin help docs/screenshots referencing category list  
- [x] DEP-1 Include migration in pipeline & verify it runs in staging  

---

> **Legend**  
> *Bold IDs* map to Requirement IDs for traceability 