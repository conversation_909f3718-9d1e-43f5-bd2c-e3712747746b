using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text.RegularExpressions;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using Microsoft.AspNetCore.Hosting;
using System.Text.Json;

namespace ParaHockeyApp.Services
{
    /// <summary>
    /// Service to validate that all UI text uses proper localization resource keys.
    /// Scans views, controllers, and other source files to ensure no hard-coded text.
    /// </summary>
    public interface ILocalizationValidationService
    {
        /// <summary>
        /// Scans all views for hard-coded text that should be localized
        /// </summary>
        Task<LocalizationScanResult> ScanViewsAsync();
        
        /// <summary>
        /// Scans controllers for hard-coded strings in user-facing messages
        /// </summary>
        Task<LocalizationScanResult> ScanControllersAsync();
        
        /// <summary>
        /// Scans JavaScript files for hard-coded text
        /// </summary>
        Task<LocalizationScanResult> ScanJavaScriptAsync();
        
        /// <summary>
        /// Performs comprehensive scan of all source files
        /// </summary>
        Task<ComprehensiveLocalizationReport> PerformComprehensiveScanAsync();
        
        /// <summary>
        /// Validates that all used localization keys exist in resource files
        /// </summary>
        Task<ResourceValidationResult> ValidateResourceKeysAsync();
        
        /// <summary>
        /// Generates a report of localization compliance
        /// </summary>
        Task<string> GenerateComplianceReportAsync();
    }
    
    public class LocalizationValidationService : ILocalizationValidationService
    {
        private readonly IWebHostEnvironment _environment;
        private readonly ILogger<LocalizationValidationService> _logger;
        
        // Patterns for detecting hard-coded text that should be localized
        private static readonly Regex HardCodedTextInViews = new(
            @"(?<!@Localizer\[)(?<!asp-\w+="")\b[A-Z][a-zA-Z\s]{3,}(?![""']>)", 
            RegexOptions.Compiled | RegexOptions.Multiline);
        
        private static readonly Regex LocalizerUsage = new(
            @"@Localizer\[""([^""]+)""\]", 
            RegexOptions.Compiled | RegexOptions.Multiline);
        
        private static readonly Regex HardCodedStringsInControllers = new(
            @"(?:return\s+(?:View|Json|RedirectToAction)\s*\([^)]*""([^""]{10,})""|""([^""]{10,})"")", 
            RegexOptions.Compiled | RegexOptions.Multiline);
        
        private static readonly Regex JavaScriptStrings = new(
            @"(?:alert|confirm|prompt)\s*\(\s*['""]([^'""]{5,})['""]", 
            RegexOptions.Compiled | RegexOptions.Multiline);
        
        private static readonly Regex ResourceKeyPattern = new(
            @"@Localizer\[""([^""]+)""\]", 
            RegexOptions.Compiled | RegexOptions.Multiline);
        
        // File extensions to scan
        private static readonly string[] ViewExtensions = { ".cshtml", ".razor" };
        private static readonly string[] ControllerExtensions = { ".cs" };
        private static readonly string[] JavaScriptExtensions = { ".js", ".ts" };
        
        public LocalizationValidationService(
            IWebHostEnvironment environment,
            ILogger<LocalizationValidationService> logger)
        {
            _environment = environment;
            _logger = logger;
        }
        
        public async Task<LocalizationScanResult> ScanViewsAsync()
        {
            var result = new LocalizationScanResult { ScanType = "Views" };
            var viewsPath = Path.Combine(_environment.ContentRootPath, "Views");
            
            if (!Directory.Exists(viewsPath))
            {
                _logger.LogWarning("Views directory not found: {ViewsPath}", viewsPath);
                return result;
            }
            
            var viewFiles = Directory.GetFiles(viewsPath, "*.*", SearchOption.AllDirectories)
                .Where(f => ViewExtensions.Contains(Path.GetExtension(f).ToLowerInvariant()))
                .ToList();
            
            _logger.LogInformation("Scanning {Count} view files for localization issues", viewFiles.Count);
            
            foreach (var filePath in viewFiles)
            {
                try
                {
                    var content = await File.ReadAllTextAsync(filePath);
                    var relativePath = Path.GetRelativePath(_environment.ContentRootPath, filePath);
                    
                    // Find hard-coded text
                    var hardCodedMatches = HardCodedTextInViews.Matches(content);
                    foreach (Match match in hardCodedMatches)
                    {
                        // Skip common non-localizable content
                        if (IsNonLocalizableContent(match.Value))
                            continue;
                        
                        result.Issues.Add(new LocalizationIssue
                        {
                            FilePath = relativePath,
                            LineNumber = GetLineNumber(content, match.Index),
                            IssueType = LocalizationIssueType.HardCodedText,
                            Content = match.Value.Trim(),
                            Severity = GetSeverity(match.Value),
                            Suggestion = $"Replace with @Localizer[\"{GenerateSuggestedKey(relativePath, match.Value)}\"]"
                        });
                    }
                    
                    // Find existing localizer usage
                    var localizerMatches = LocalizerUsage.Matches(content);
                    foreach (Match match in localizerMatches)
                    {
                        result.LocalizedStrings.Add(new LocalizedStringUsage
                        {
                            FilePath = relativePath,
                            LineNumber = GetLineNumber(content, match.Index),
                            Key = match.Groups[1].Value,
                            Usage = match.Value
                        });
                    }
                    
                    result.ScannedFiles.Add(relativePath);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error scanning view file: {FilePath}", filePath);
                    result.Errors.Add($"Error scanning {filePath}: {ex.Message}");
                }
            }
            
            result.CompletedAt = DateTime.UtcNow;
            _logger.LogInformation("View scan completed. Found {IssueCount} issues in {FileCount} files", 
                result.Issues.Count, result.ScannedFiles.Count);
            
            return result;
        }
        
        public async Task<LocalizationScanResult> ScanControllersAsync()
        {
            var result = new LocalizationScanResult { ScanType = "Controllers" };
            var controllersPath = Path.Combine(_environment.ContentRootPath, "Controllers");
            
            if (!Directory.Exists(controllersPath))
            {
                _logger.LogWarning("Controllers directory not found: {ControllersPath}", controllersPath);
                return result;
            }
            
            var controllerFiles = Directory.GetFiles(controllersPath, "*.cs", SearchOption.AllDirectories);
            
            _logger.LogInformation("Scanning {Count} controller files for localization issues", controllerFiles.Length);
            
            foreach (var filePath in controllerFiles)
            {
                try
                {
                    var content = await File.ReadAllTextAsync(filePath);
                    var relativePath = Path.GetRelativePath(_environment.ContentRootPath, filePath);
                    
                    // Find hard-coded strings in user-facing methods
                    var hardCodedMatches = HardCodedStringsInControllers.Matches(content);
                    foreach (Match match in hardCodedMatches)
                    {
                        var text = match.Groups[1].Value.IsNullOrEmpty() ? match.Groups[2].Value : match.Groups[1].Value;
                        
                        if (IsNonLocalizableContent(text))
                            continue;
                        
                        result.Issues.Add(new LocalizationIssue
                        {
                            FilePath = relativePath,
                            LineNumber = GetLineNumber(content, match.Index),
                            IssueType = LocalizationIssueType.HardCodedText,
                            Content = text,
                            Severity = LocalizationIssueSeverity.High,
                            Suggestion = $"Replace with _localizer[\"{GenerateSuggestedKey(relativePath, text)}\"]"
                        });
                    }
                    
                    result.ScannedFiles.Add(relativePath);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error scanning controller file: {FilePath}", filePath);
                    result.Errors.Add($"Error scanning {filePath}: {ex.Message}");
                }
            }
            
            result.CompletedAt = DateTime.UtcNow;
            return result;
        }
        
        public async Task<LocalizationScanResult> ScanJavaScriptAsync()
        {
            var result = new LocalizationScanResult { ScanType = "JavaScript" };
            var wwwrootPath = Path.Combine(_environment.ContentRootPath, "wwwroot");
            
            if (!Directory.Exists(wwwrootPath))
            {
                _logger.LogWarning("wwwroot directory not found: {WwwrootPath}", wwwrootPath);
                return result;
            }
            
            var jsFiles = Directory.GetFiles(wwwrootPath, "*.*", SearchOption.AllDirectories)
                .Where(f => JavaScriptExtensions.Contains(Path.GetExtension(f).ToLowerInvariant()))
                .ToList();
            
            _logger.LogInformation("Scanning {Count} JavaScript files for localization issues", jsFiles.Count);
            
            foreach (var filePath in jsFiles)
            {
                try
                {
                    var content = await File.ReadAllTextAsync(filePath);
                    var relativePath = Path.GetRelativePath(_environment.ContentRootPath, filePath);
                    
                    // Find hard-coded strings in JavaScript alerts, confirms, etc.
                    var hardCodedMatches = JavaScriptStrings.Matches(content);
                    foreach (Match match in hardCodedMatches)
                    {
                        var text = match.Groups[1].Value;
                        
                        if (IsNonLocalizableContent(text))
                            continue;
                        
                        result.Issues.Add(new LocalizationIssue
                        {
                            FilePath = relativePath,
                            LineNumber = GetLineNumber(content, match.Index),
                            IssueType = LocalizationIssueType.HardCodedText,
                            Content = text,
                            Severity = LocalizationIssueSeverity.Medium,
                            Suggestion = "Consider using server-side localization or a JavaScript localization library"
                        });
                    }
                    
                    result.ScannedFiles.Add(relativePath);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error scanning JavaScript file: {FilePath}", filePath);
                    result.Errors.Add($"Error scanning {filePath}: {ex.Message}");
                }
            }
            
            result.CompletedAt = DateTime.UtcNow;
            return result;
        }
        
        public async Task<ComprehensiveLocalizationReport> PerformComprehensiveScanAsync()
        {
            _logger.LogInformation("Starting comprehensive localization scan");
            
            var report = new ComprehensiveLocalizationReport
            {
                StartedAt = DateTime.UtcNow
            };
            
            // Perform all scans
            report.ViewScanResult = await ScanViewsAsync();
            report.ControllerScanResult = await ScanControllersAsync();
            report.JavaScriptScanResult = await ScanJavaScriptAsync();
            report.ResourceValidationResult = await ValidateResourceKeysAsync();
            
            // Calculate summary statistics
            report.TotalIssues = report.ViewScanResult.Issues.Count + 
                               report.ControllerScanResult.Issues.Count + 
                               report.JavaScriptScanResult.Issues.Count;
            
            report.TotalScannedFiles = report.ViewScanResult.ScannedFiles.Count + 
                                     report.ControllerScanResult.ScannedFiles.Count + 
                                     report.JavaScriptScanResult.ScannedFiles.Count;
            
            report.ComplianceScore = CalculateComplianceScore(report);
            report.CompletedAt = DateTime.UtcNow;
            
            _logger.LogInformation("Comprehensive localization scan completed. Found {TotalIssues} issues across {TotalFiles} files. Compliance score: {ComplianceScore:F1}%",
                report.TotalIssues, report.TotalScannedFiles, report.ComplianceScore);
            
            return report;
        }
        
        public async Task<ResourceValidationResult> ValidateResourceKeysAsync()
        {
            var result = new ResourceValidationResult();
            
            // This would typically scan resource files (.resx) and compare with used keys
            // For now, we'll create a placeholder implementation
            
            _logger.LogInformation("Validating resource keys (placeholder implementation)");
            
            result.ValidatedAt = DateTime.UtcNow;
            return result;
        }
        
        public async Task<string> GenerateComplianceReportAsync()
        {
            var report = await PerformComprehensiveScanAsync();
            
            var reportData = new
            {
                GeneratedAt = DateTime.UtcNow,
                Summary = new
                {
                    report.TotalIssues,
                    report.TotalScannedFiles,
                    ComplianceScore = $"{report.ComplianceScore:F1}%",
                    ScanDuration = report.CompletedAt - report.StartedAt
                },
                IssuesByType = report.GetIssuesByType(),
                IssuesBySeverity = report.GetIssuesBySeverity(),
                TopIssues = report.GetTopIssues(10),
                Recommendations = GenerateRecommendations(report)
            };
            
            return JsonSerializer.Serialize(reportData, new JsonSerializerOptions
            {
                WriteIndented = true,
                PropertyNamingPolicy = JsonNamingPolicy.CamelCase
            });
        }
        
        #region Helper Methods
        
        private bool IsNonLocalizableContent(string content)
        {
            if (string.IsNullOrWhiteSpace(content))
                return true;
            
            // Skip common non-localizable patterns
            var nonLocalizablePatterns = new[]
            {
                @"^\d+$",                    // Numbers only
                @"^[A-Z]{2,}$",             // All caps (likely constants)
                @"^https?://",              // URLs
                @"^[a-zA-Z0-9._%+-]+@",     // Email addresses
                @"^\w+\.\w+",               // File extensions or namespaces
                @"^(true|false)$",          // Boolean values
                @"^(null|undefined)$",      // Null values
                @"^\s*$",                   // Whitespace only
                @"^[^a-zA-Z]*$"            // No letters (symbols, numbers only)
            };
            
            return nonLocalizablePatterns.Any(pattern => Regex.IsMatch(content, pattern, RegexOptions.IgnoreCase));
        }
        
        private LocalizationIssueSeverity GetSeverity(string content)
        {
            if (content.Length > 50) return LocalizationIssueSeverity.High;
            if (content.Length > 20) return LocalizationIssueSeverity.Medium;
            return LocalizationIssueSeverity.Low;
        }
        
        private string GenerateSuggestedKey(string filePath, string content)
        {
            var fileName = Path.GetFileNameWithoutExtension(filePath);
            var cleanContent = Regex.Replace(content, @"[^\w\s]", "").Trim();
            var words = cleanContent.Split(' ', StringSplitOptions.RemoveEmptyEntries);
            
            if (words.Length > 3)
                words = words.Take(3).ToArray();
            
            var keyPart = string.Join("", words.Select(w => char.ToUpper(w[0]) + w.Substring(1).ToLower()));
            return $"Page.{fileName}.{keyPart}";
        }
        
        private int GetLineNumber(string content, int index)
        {
            return content.Substring(0, index).Count(c => c == '\n') + 1;
        }
        
        private double CalculateComplianceScore(ComprehensiveLocalizationReport report)
        {
            var totalStrings = report.TotalIssues + 
                              report.ViewScanResult.LocalizedStrings.Count +
                              report.ControllerScanResult.LocalizedStrings.Count +
                              report.JavaScriptScanResult.LocalizedStrings.Count;
            
            if (totalStrings == 0) return 100.0;
            
            var localizedStrings = report.ViewScanResult.LocalizedStrings.Count +
                                 report.ControllerScanResult.LocalizedStrings.Count +
                                 report.JavaScriptScanResult.LocalizedStrings.Count;
            
            return (double)localizedStrings / totalStrings * 100;
        }
        
        private List<string> GenerateRecommendations(ComprehensiveLocalizationReport report)
        {
            var recommendations = new List<string>();
            
            if (report.TotalIssues > 0)
            {
                recommendations.Add($"Address {report.TotalIssues} hard-coded text issues found across the application");
            }
            
            if (report.ComplianceScore < 80)
            {
                recommendations.Add("Improve localization compliance by replacing hard-coded text with resource keys");
            }
            
            if (report.ViewScanResult.Issues.Count > 0)
            {
                recommendations.Add("Focus on view files which contain the most localization issues");
            }
            
            recommendations.Add("Implement automated localization validation in CI/CD pipeline");
            recommendations.Add("Train development team on localization best practices");
            
            return recommendations;
        }
        
        #endregion
    }
    
    #region Data Models
    
    public class LocalizationScanResult
    {
        public string ScanType { get; set; } = string.Empty;
        public List<string> ScannedFiles { get; set; } = new();
        public List<LocalizationIssue> Issues { get; set; } = new();
        public List<LocalizedStringUsage> LocalizedStrings { get; set; } = new();
        public List<string> Errors { get; set; } = new();
        public DateTime CompletedAt { get; set; }
    }
    
    public class LocalizationIssue
    {
        public string FilePath { get; set; } = string.Empty;
        public int LineNumber { get; set; }
        public LocalizationIssueType IssueType { get; set; }
        public string Content { get; set; } = string.Empty;
        public LocalizationIssueSeverity Severity { get; set; }
        public string Suggestion { get; set; } = string.Empty;
    }
    
    public class LocalizedStringUsage
    {
        public string FilePath { get; set; } = string.Empty;
        public int LineNumber { get; set; }
        public string Key { get; set; } = string.Empty;
        public string Usage { get; set; } = string.Empty;
    }
    
    public class ComprehensiveLocalizationReport
    {
        public DateTime StartedAt { get; set; }
        public DateTime CompletedAt { get; set; }
        public LocalizationScanResult ViewScanResult { get; set; } = new();
        public LocalizationScanResult ControllerScanResult { get; set; } = new();
        public LocalizationScanResult JavaScriptScanResult { get; set; } = new();
        public ResourceValidationResult ResourceValidationResult { get; set; } = new();
        public int TotalIssues { get; set; }
        public int TotalScannedFiles { get; set; }
        public double ComplianceScore { get; set; }
        
        public Dictionary<LocalizationIssueType, int> GetIssuesByType()
        {
            var allIssues = ViewScanResult.Issues
                .Concat(ControllerScanResult.Issues)
                .Concat(JavaScriptScanResult.Issues);
            
            return allIssues.GroupBy(i => i.IssueType)
                .ToDictionary(g => g.Key, g => g.Count());
        }
        
        public Dictionary<LocalizationIssueSeverity, int> GetIssuesBySeverity()
        {
            var allIssues = ViewScanResult.Issues
                .Concat(ControllerScanResult.Issues)
                .Concat(JavaScriptScanResult.Issues);
            
            return allIssues.GroupBy(i => i.Severity)
                .ToDictionary(g => g.Key, g => g.Count());
        }
        
        public List<LocalizationIssue> GetTopIssues(int count)
        {
            var allIssues = ViewScanResult.Issues
                .Concat(ControllerScanResult.Issues)
                .Concat(JavaScriptScanResult.Issues);
            
            return allIssues.OrderByDescending(i => i.Severity)
                .ThenBy(i => i.FilePath)
                .Take(count)
                .ToList();
        }
    }
    
    public class ResourceValidationResult
    {
        public List<string> ValidKeys { get; set; } = new();
        public List<string> MissingKeys { get; set; } = new();
        public List<string> UnusedKeys { get; set; } = new();
        public DateTime ValidatedAt { get; set; }
    }
    
    public enum LocalizationIssueType
    {
        HardCodedText,
        MissingResourceKey,
        UnusedResourceKey,
        InvalidKeyFormat
    }
    
    public enum LocalizationIssueSeverity
    {
        Low = 1,
        Medium = 2,
        High = 3,
        Critical = 4
    }
    
    #endregion
}

// Extension method for string null checking
public static class StringExtensions
{
    public static bool IsNullOrEmpty(this string? value)
    {
        return string.IsNullOrEmpty(value);
    }
}