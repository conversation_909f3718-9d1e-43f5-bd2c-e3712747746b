/**
 * Enhanced Forms CSS
 * Modern, accessible form styling with validation states
 */

/* Form Container */
.enhanced-form {
    max-width: 800px;
    margin: 0 auto;
    padding: 2rem;
    background: #ffffff;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

/* Form Groups */
.form-group {
    margin-bottom: 1.5rem;
    position: relative;
}

.form-group:last-child {
    margin-bottom: 0;
}

/* Labels */
.form-label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 600;
    color: #333;
    font-size: 0.95rem;
}

.form-label .required {
    color: #dc3545;
    font-weight: bold;
    margin-left: 2px;
}

/* Form Controls */
.form-control,
.form-select {
    display: block;
    width: 100%;
    padding: 0.75rem 1rem;
    font-size: 1rem;
    font-weight: 400;
    line-height: 1.5;
    color: #495057;
    background-color: #fff;
    background-clip: padding-box;
    border: 2px solid #ced4da;
    border-radius: 6px;
    transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
    min-height: 44px;
    /* Touch-friendly minimum */
}

.form-control:focus,
.form-select:focus {
    color: #495057;
    background-color: #fff;
    border-color: #0d6efd;
    outline: 0;
    box-shadow: 0 0 0 3px rgba(13, 110, 253, 0.25);
}

.form-control::placeholder {
    color: #6c757d;
    opacity: 1;
}

/* Validation States */
.form-control.is-valid,
.form-select.is-valid {
    border-color: #198754;
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 8 8'%3e%3cpath fill='%23198754' d='m2.3 6.73.94-.94 1.38 1.38L7.7 4.08 6.76 3.14 4.25 5.65z'/%3e%3c/svg%3e");
    background-repeat: no-repeat;
    background-position: right calc(0.375em + 0.1875rem) center;
    background-size: calc(0.75em + 0.375rem) calc(0.75em + 0.375rem);
}

.form-control.is-valid:focus,
.form-select.is-valid:focus {
    border-color: #198754;
    box-shadow: 0 0 0 3px rgba(25, 135, 84, 0.25);
}

.form-control.is-invalid,
.form-select.is-invalid {
    border-color: #dc3545;
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 12 12' width='12' height='12' fill='none' stroke='%23dc3545'%3e%3ccircle cx='6' cy='6' r='4.5'/%3e%3cpath d='m5.8 4.6 2.4 2.4M8.2 4.6l-2.4 2.4'/%3e%3c/svg%3e");
    background-repeat: no-repeat;
    background-position: right calc(0.375em + 0.1875rem) center;
    background-size: calc(0.75em + 0.375rem) calc(0.75em + 0.375rem);
}

.form-control.is-invalid:focus,
.form-select.is-invalid:focus {
    border-color: #dc3545;
    box-shadow: 0 0 0 3px rgba(220, 53, 69, 0.25);
}

/* Help Text */
.form-text {
    margin-top: 0.25rem;
    font-size: 0.875rem;
    color: #6c757d;
}

/* Validation Feedback */
.invalid-feedback {
    display: none;
    width: 100%;
    margin-top: 0.25rem;
    font-size: 0.875rem;
    color: #dc3545;
}

.form-control.is-invalid~.invalid-feedback,
.form-select.is-invalid~.invalid-feedback {
    display: block;
}

.valid-feedback {
    display: none;
    width: 100%;
    margin-top: 0.25rem;
    font-size: 0.875rem;
    color: #198754;
}

.form-control.is-valid~.valid-feedback,
.form-select.is-valid~.valid-feedback {
    display: block;
}

/* Validation Summary */
.validation-summary {
    margin-bottom: 2rem;
    padding: 1rem;
    background-color: #f8d7da;
    border: 1px solid #f5c6cb;
    border-radius: 6px;
    color: #721c24;
}

.validation-summary h4 {
    margin-top: 0;
    margin-bottom: 0.5rem;
    font-size: 1.1rem;
    font-weight: 600;
}

.validation-errors {
    margin: 0;
    padding-left: 1.5rem;
}

.validation-errors li {
    margin-bottom: 0.25rem;
}

/* Form Sections */
.form-section {
    margin-bottom: 2.5rem;
    padding-bottom: 2rem;
    border-bottom: 1px solid #dee2e6;
}

.form-section:last-child {
    border-bottom: none;
    margin-bottom: 0;
    padding-bottom: 0;
}

.form-section-title {
    margin-bottom: 1.5rem;
    font-size: 1.25rem;
    font-weight: 600;
    color: #495057;
    display: flex;
    align-items: center;
}

.form-section-title::before {
    content: '';
    display: inline-block;
    width: 4px;
    height: 1.5rem;
    background-color: #0d6efd;
    margin-right: 0.75rem;
    border-radius: 2px;
}

/* Form Grid */
.form-row {
    display: flex;
    flex-wrap: wrap;
    margin: 0 -0.75rem;
}

.form-col {
    flex: 1;
    padding: 0 0.75rem;
    min-width: 0;
}

.form-col-6 {
    flex: 0 0 50%;
    max-width: 50%;
}

.form-col-4 {
    flex: 0 0 33.333333%;
    max-width: 33.333333%;
}

.form-col-3 {
    flex: 0 0 25%;
    max-width: 25%;
}

/* Buttons */
.btn-group {
    display: flex;
    gap: 1rem;
    margin-top: 2rem;
    justify-content: flex-end;
}

.btn {
    display: inline-block;
    padding: 0.75rem 1.5rem;
    font-size: 1rem;
    font-weight: 500;
    line-height: 1.5;
    text-align: center;
    text-decoration: none;
    vertical-align: middle;
    cursor: pointer;
    border: 2px solid transparent;
    border-radius: 6px;
    transition: all 0.15s ease-in-out;
    min-height: 44px;
    /* Touch-friendly */
    min-width: 120px;
}

.btn:focus {
    outline: 0;
    box-shadow: 0 0 0 3px rgba(13, 110, 253, 0.25);
}

.btn-primary {
    color: #fff;
    background-color: #0d6efd;
    border-color: #0d6efd;
}

.btn-primary:hover {
    background-color: #0b5ed7;
    border-color: #0a58ca;
}

.btn-secondary {
    color: #6c757d;
    background-color: transparent;
    border-color: #6c757d;
}

.btn-secondary:hover {
    color: #fff;
    background-color: #6c757d;
    border-color: #6c757d;
}

/* Loading States */
.btn.loading {
    position: relative;
    color: transparent;
    pointer-events: none;
}

.btn.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 1rem;
    height: 1rem;
    margin: -0.5rem 0 0 -0.5rem;
    border: 2px solid transparent;
    border-top-color: currentColor;
    border-radius: 50%;
    animation: spin 0.8s linear infinite;
}

@keyframes spin {
    0% {
        transform: rotate(0deg);
    }

    100% {
        transform: rotate(360deg);
    }
}

/* Responsive Design */
@media (max-width: 768px) {
    .enhanced-form {
        padding: 1.5rem;
        margin: 1rem;
    }

    .form-row {
        flex-direction: column;
        margin: 0;
    }

    .form-col,
    .form-col-6,
    .form-col-4,
    .form-col-3 {
        flex: 1;
        max-width: 100%;
        padding: 0;
        margin-bottom: 1rem;
    }

    .btn-group {
        flex-direction: column;
        align-items: stretch;
    }

    .btn {
        margin-bottom: 0.5rem;
    }
}

@media (max-width: 480px) {
    .enhanced-form {
        padding: 1rem;
        margin: 0.5rem;
    }

    .form-control,
    .form-select {
        font-size: 16px;
        /* Prevent zoom on iOS */
    }
}

/* High Contrast Mode Support */
@media (prefers-contrast: high) {

    .form-control,
    .form-select {
        border-width: 3px;
    }

    .form-control:focus,
    .form-select:focus {
        border-width: 3px;
        box-shadow: 0 0 0 2px #000;
    }
}

/* Reduced Motion Support */
@media (prefers-reduced-motion: reduce) {

    .form-control,
    .form-select,
    .btn {
        transition: none;
    }

    .btn.loading::after {
        animation: none;
    }
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
    .enhanced-form {
        background-color: #2d3748;
        color: #e2e8f0;
    }

    .form-label {
        color: #e2e8f0;
    }

    .form-control,
    .form-select {
        background-color: #4a5568;
        border-color: #718096;
        color: #e2e8f0;
    }

    .form-control::placeholder {
        color: #a0aec0;
    }

    .form-control:focus,
    .form-select:focus {
        background-color: #4a5568;
        border-color: #63b3ed;
        color: #e2e8f0;
    }

    .form-text {
        color: #a0aec0;
    }

    .validation-summary {
        background-color: #fed7d7;
        border-color: #feb2b2;
        color: #742a2a;
    }
}

/* Print Styles */
@media print {
    .enhanced-form {
        box-shadow: none;
        border: 1px solid #000;
    }

    .btn-group {
        display: none;
    }

    .validation-summary {
        display: none;
    }
}