using Microsoft.AspNetCore.Http;
using ParaHockeyApp.Models.Entities;

namespace ParaHockeyApp.Services
{
    /// <summary>
    /// Service for streaming large file imports with progress tracking and cancellation
    /// </summary>
    public interface IStreamingImportService
    {
        /// <summary>
        /// Processes large Excel files using streaming to avoid memory issues
        /// </summary>
        /// <param name="file">The Excel file to process</param>
        /// <param name="uploadedBy">Username of the person uploading</param>
        /// <param name="progressCallback">Callback for progress updates</param>
        /// <param name="cancellationToken">Cancellation token for operation cancellation</param>
        /// <returns>The import batch ID</returns>
        Task<int> StreamProcessFileAsync(IFormFile file, string uploadedBy, 
            IProgress<ImportProgressUpdate>? progressCallback = null, 
            CancellationToken cancellationToken = default);

        /// <summary>
        /// Gets import progress for a specific batch
        /// </summary>
        /// <param name="batchId">The import batch ID</param>
        /// <returns>Current progress information</returns>
        Task<ImportProgressInfo> GetImportProgressAsync(int batchId);

        /// <summary>
        /// Cancels an ongoing import operation
        /// </summary>
        /// <param name="batchId">The import batch ID to cancel</param>
        /// <returns>True if cancellation was successful</returns>
        Task<bool> CancelImportAsync(int batchId);

        /// <summary>
        /// Validates file size and format for streaming import
        /// </summary>
        /// <param name="file">The file to validate</param>
        /// <returns>Validation result with streaming recommendations</returns>
        Task<StreamingValidationResult> ValidateForStreamingAsync(IFormFile file);
    }

    /// <summary>
    /// Progress update information for streaming import
    /// </summary>
    public class ImportProgressUpdate
    {
        public int BatchId { get; set; }
        public string Stage { get; set; } = string.Empty;
        public int ProcessedRows { get; set; }
        public int TotalRows { get; set; }
        public double PercentageComplete => TotalRows > 0 ? (ProcessedRows / (double)TotalRows) * 100 : 0;
        public string StatusMessage { get; set; } = string.Empty;
        public DateTime LastUpdated { get; set; } = DateTime.UtcNow;
        public List<string> Warnings { get; set; } = new();
        public List<string> Errors { get; set; } = new();
        public bool IsCompleted { get; set; }
        public bool HasErrors => Errors.Any();
    }

    /// <summary>
    /// Current progress information for an import batch
    /// </summary>
    public class ImportProgressInfo
    {
        public int BatchId { get; set; }
        public string FileName { get; set; } = string.Empty;
        public ImportStatus Status { get; set; }
        public string CurrentStage { get; set; } = string.Empty;
        public int ProcessedRows { get; set; }
        public int TotalRows { get; set; }
        public double PercentageComplete => TotalRows > 0 ? (ProcessedRows / (double)TotalRows) * 100 : 0;
        public DateTime StartedAt { get; set; }
        public DateTime? CompletedAt { get; set; }
        public TimeSpan ElapsedTime => (CompletedAt ?? DateTime.UtcNow) - StartedAt;
        public string? ErrorMessage { get; set; }
        public bool CanBeCancelled => Status == ImportStatus.InProgress;
        public List<string> ProcessingStages { get; set; } = new();
        public int CurrentStageIndex { get; set; }
    }

    /// <summary>
    /// Import status enumeration
    /// </summary>
    public enum ImportStatus
    {
        Pending = 0,
        InProgress = 1,
        Completed = 2,
        Failed = 3,
        Cancelled = 4
    }

    /// <summary>
    /// Validation result for streaming import
    /// </summary>
    public class StreamingValidationResult
    {
        public bool IsValid { get; set; }
        public bool RecommendStreaming { get; set; }
        public long FileSizeBytes { get; set; }
        public int EstimatedRowCount { get; set; }
        public List<string> Warnings { get; set; } = new();
        public List<string> Errors { get; set; } = new();
        public string RecommendedStrategy { get; set; } = string.Empty;
        public int RecommendedBatchSize { get; set; } = 500;
    }
}