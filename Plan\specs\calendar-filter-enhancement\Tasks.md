# Tasks – Calendar Filter Enhancement

- [x] **T1** – Confirm existing `EventCategory` IDs for First Shift, Pratique, and Pratique First Shift in seed data / database.
- [ ] **T2** – Create `ICategoryFilterService` interface with `List<int> GetFilterCategoryIds(int selectedCategoryId)`.
- [ ] **T3** – Implement `CategoryFilterService` using an in-memory `Dictionary<int, List<int>>` cached on first call.
- [ ] **T4** – Register the service in `Program.cs` via DI (`AddSingleton`).
- [ ] **T5** – Refactor `EventService.GetCalendarEventsAsync` to:  
        a. Inject `ICategoryFilterService`.  
        b. Replace equality filter with `Contains` for returned list.
- [ ] **T6** – Update unit tests / create new ones validating rule logic (xUnit). (FR1-FR4)
- [ ] **T7** – Add/extend E2E test in `ParaHockey.E2E.Tests` verifying UI behaviour for both filters on Admin calendar.
- [ ] **T8** – Update SQL seed/migration if `Pratique First Shift` category missing or inactive.
- [ ] **T9** – Run performance benchmark to ensure ≤100 ms response (baseline).
- [ ] **T10** – Update documentation & CHANGELOG; create announcement for users. 