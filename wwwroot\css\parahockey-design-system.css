/* ParaHockey Design System - Main Entry Point */

/* Import design system components in order */
@import url('./shared/variables.css');
@import url('./shared/typography.css');
@import url('./shared/components.css');
@import url('./shared/forms.css');
@import url('./shared/utilities.css');

/* ===== GLOBAL STYLES ===== */
*,
*::before,
*::after {
    box-sizing: border-box;
}

html {
    position: relative;
    min-height: 100%;
    scroll-behavior: smooth;
}

body {
    margin: 0;
    padding: 0;
    overflow-x: hidden;
    margin-bottom: var(--ph-footer-height);
}

/* ===== LAYOUT COMPONENTS ===== */

/* Header/Navbar */
.ph-navbar {
    background-color: var(--ph-white);
    border-bottom: 1px solid var(--ph-gray-200);
    box-shadow: var(--ph-shadow-sm);
    min-height: var(--ph-navbar-height);
    position: sticky;
    top: 0;
    z-index: var(--ph-z-sticky);
}

.ph-navbar-brand {
    display: flex;
    align-items: center;
    gap: var(--ph-spacing-sm);
    font-size: var(--ph-font-size-lg);
    font-weight: var(--ph-font-weight-semibold);
    color: var(--ph-primary);
    text-decoration: none;
    transition: color var(--ph-transition-fast);
}

.ph-navbar-brand:hover {
    color: var(--ph-primary-hover);
}

.ph-navbar-brand img {
    height: 2.5rem;
    width: auto;
    border-radius: var(--ph-radius-lg);
}

.ph-navbar-nav {
    display: flex;
    align-items: center;
    gap: var(--ph-spacing-base);
    list-style: none;
    margin: 0;
    padding: 0;
}

.ph-navbar-nav .ph-nav-link {
    padding: var(--ph-spacing-sm) var(--ph-spacing-base);
    color: var(--ph-gray-700);
    font-weight: var(--ph-font-weight-medium);
    transition: color var(--ph-transition-fast);
}

.ph-navbar-nav .ph-nav-link:hover {
    color: var(--ph-primary);
}

.ph-navbar-nav .ph-nav-link.active {
    color: var(--ph-primary);
    font-weight: var(--ph-font-weight-semibold);
}

/* Footer */
.ph-footer {
    position: absolute;
    bottom: 0;
    width: 100%;
    height: var(--ph-footer-height);
    background-color: var(--ph-light);
    border-top: 1px solid var(--ph-gray-200);
    display: flex;
    align-items: center;
    padding: 0 var(--ph-spacing-base);
    color: var(--ph-gray-600);
    font-size: var(--ph-font-size-sm);
}

/* Main Content Container */
.ph-container {
    width: 100%;
    padding-right: var(--ph-spacing-base);
    padding-left: var(--ph-spacing-base);
    margin-right: auto;
    margin-left: auto;
}

@media (min-width: 576px) {
    .ph-container {
        max-width: 540px;
    }
}

@media (min-width: 768px) {
    .ph-container {
        max-width: 720px;
    }
}

@media (min-width: 992px) {
    .ph-container {
        max-width: 960px;
    }
}

@media (min-width: 1200px) {
    .ph-container {
        max-width: 1140px;
    }
}

@media (min-width: 1400px) {
    .ph-container {
        max-width: 1320px;
    }
}

.ph-container-fluid {
    width: 100%;
    padding-right: var(--ph-spacing-base);
    padding-left: var(--ph-spacing-base);
    margin-right: auto;
    margin-left: auto;
}

/* ===== HERO SECTIONS ===== */
.ph-hero {
    background: linear-gradient(135deg, var(--ph-light) 0%, var(--ph-lighter) 100%);
    padding: var(--ph-spacing-4xl) 0;
    text-align: center;
}

.ph-hero-title {
    font-size: var(--ph-font-size-5xl);
    font-weight: var(--ph-font-weight-bold);
    color: var(--ph-gray-900);
    margin-bottom: var(--ph-spacing-base);
    line-height: var(--ph-line-height-tight);
}

.ph-hero-subtitle {
    font-size: var(--ph-font-size-xl);
    color: var(--ph-gray-600);
    margin-bottom: var(--ph-spacing-2xl);
    line-height: var(--ph-line-height-relaxed);
}

.ph-hero-actions {
    display: flex;
    gap: var(--ph-spacing-base);
    justify-content: center;
    flex-wrap: wrap;
}

/* ===== FEATURE SECTIONS ===== */
.ph-features {
    padding: var(--ph-spacing-5xl) 0;
}

.ph-feature-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: var(--ph-spacing-2xl);
    margin-top: var(--ph-spacing-3xl);
}

.ph-feature-card {
    text-align: center;
    padding: var(--ph-spacing-2xl);
    background: var(--ph-white);
    border-radius: var(--ph-radius-xl);
    box-shadow: var(--ph-shadow-base);
    transition: transform var(--ph-transition-base), box-shadow var(--ph-transition-base);
}

.ph-feature-card:hover {
    transform: translateY(-0.25rem);
    box-shadow: var(--ph-shadow-lg);
}

.ph-feature-icon {
    width: 4rem;
    height: 4rem;
    background: linear-gradient(135deg, var(--ph-primary), var(--ph-info));
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto var(--ph-spacing-lg);
    color: var(--ph-white);
    font-size: var(--ph-font-size-2xl);
}

.ph-feature-title {
    font-size: var(--ph-font-size-xl);
    font-weight: var(--ph-font-weight-semibold);
    color: var(--ph-gray-900);
    margin-bottom: var(--ph-spacing-base);
}

.ph-feature-description {
    color: var(--ph-gray-600);
    line-height: var(--ph-line-height-relaxed);
}

/* ===== SECTION HEADERS ===== */
.ph-section-header {
    text-align: center;
    margin-bottom: var(--ph-spacing-3xl);
}

.ph-section-title {
    font-size: var(--ph-font-size-4xl);
    font-weight: var(--ph-font-weight-bold);
    color: var(--ph-gray-900);
    margin-bottom: var(--ph-spacing-base);
}

.ph-section-subtitle {
    font-size: var(--ph-font-size-lg);
    color: var(--ph-gray-600);
    line-height: var(--ph-line-height-relaxed);
    max-width: 600px;
    margin: 0 auto;
}

/* ===== STATS COMPONENTS ===== */
.ph-stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: var(--ph-spacing-lg);
}

.ph-stat-card {
    background: var(--ph-white);
    padding: var(--ph-spacing-xl);
    border-radius: var(--ph-radius-lg);
    box-shadow: var(--ph-shadow-base);
    text-align: center;
}

.ph-stat-number {
    font-size: var(--ph-font-size-4xl);
    font-weight: var(--ph-font-weight-bold);
    color: var(--ph-primary);
    display: block;
    line-height: 1;
}

.ph-stat-label {
    font-size: var(--ph-font-size-sm);
    color: var(--ph-gray-600);
    margin-top: var(--ph-spacing-xs);
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

/* ===== ADMIN SPECIFIC STYLES ===== */
.ph-admin-sidebar {
    width: var(--ph-sidebar-width);
    background: var(--ph-white);
    border-right: 1px solid var(--ph-gray-200);
    height: calc(100vh - var(--ph-navbar-height));
    position: fixed;
    top: var(--ph-navbar-height);
    left: 0;
    overflow-y: auto;
    z-index: var(--ph-z-fixed);
    transition: transform var(--ph-transition-base);
}

.ph-admin-sidebar.collapsed {
    width: var(--ph-sidebar-collapsed-width);
}

.ph-admin-content {
    margin-left: var(--ph-sidebar-width);
    padding: var(--ph-spacing-lg);
    min-height: calc(100vh - var(--ph-navbar-height));
    transition: margin-left var(--ph-transition-base);
}

.ph-admin-content.sidebar-collapsed {
    margin-left: var(--ph-sidebar-collapsed-width);
}

/* ===== RESPONSIVE DESIGN ===== */
@media (max-width: 991.98px) {
    .ph-hero {
        padding: var(--ph-spacing-3xl) 0;
    }
    
    .ph-hero-title {
        font-size: var(--ph-font-size-4xl);
    }
    
    .ph-hero-subtitle {
        font-size: var(--ph-font-size-lg);
    }
    
    .ph-hero-actions {
        flex-direction: column;
        align-items: center;
    }
    
    .ph-hero-actions .ph-btn {
        width: 100%;
        max-width: 300px;
    }
    
    .ph-feature-grid {
        grid-template-columns: 1fr;
        gap: var(--ph-spacing-xl);
    }
    
    .ph-stats-grid {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .ph-admin-sidebar {
        transform: translateX(-100%);
    }
    
    .ph-admin-sidebar.show {
        transform: translateX(0);
    }
    
    .ph-admin-content {
        margin-left: 0;
    }
}

@media (max-width: 575.98px) {
    .ph-container,
    .ph-container-fluid {
        padding-right: var(--ph-spacing-sm);
        padding-left: var(--ph-spacing-sm);
    }
    
    .ph-hero {
        padding: var(--ph-spacing-2xl) 0;
    }
    
    .ph-hero-title {
        font-size: var(--ph-font-size-3xl);
    }
    
    .ph-hero-subtitle {
        font-size: var(--ph-font-size-base);
    }
    
    .ph-features {
        padding: var(--ph-spacing-3xl) 0;
    }
    
    .ph-feature-card {
        padding: var(--ph-spacing-lg);
    }
    
    .ph-stats-grid {
        grid-template-columns: 1fr;
    }
    
    .ph-section-title {
        font-size: var(--ph-font-size-3xl);
    }
}

/* ===== ACCESSIBILITY ENHANCEMENTS ===== */
@media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
        scroll-behavior: auto !important;
    }
}

@media (prefers-contrast: high) {
    .ph-card,
    .ph-feature-card,
    .ph-stat-card {
        border: 2px solid var(--ph-gray-400);
    }
    
    .ph-btn {
        border-width: 2px;
    }
}

/* Focus visible for better keyboard navigation */
.ph-btn:focus-visible,
.ph-form-control:focus-visible,
.ph-form-select:focus-visible,
.ph-form-check-input:focus-visible,
.ph-nav-link:focus-visible {
    outline: 2px solid var(--ph-primary);
    outline-offset: 2px;
}

/* ===== LEGACY COMPATIBILITY STYLES ===== */
/* Support for existing Bootstrap classes and legacy components */
.min-vh-75 {
    min-height: 75vh;
}

.hero-logo {
    max-height: 120px;
    width: auto;
    border-radius: var(--ph-radius-xl);
    box-shadow: var(--ph-shadow-lg);
}

.cta-section {
    background: linear-gradient(135deg, var(--ph-primary) 0%, var(--ph-info) 100%);
    padding: var(--ph-spacing-4xl) 0;
    margin-top: var(--ph-spacing-3xl);
}

/* Responsive adjustments for legacy components */
@media (max-width: 767.98px) {
    .hero-logo {
        max-height: 80px;
    }
    
    .min-vh-75 {
        min-height: 50vh;
    }
}

/* ===== PRINT STYLES ===== */
@media print {
    .ph-navbar,
    .ph-footer,
    .ph-admin-sidebar {
        display: none !important;
    }
    
    .ph-admin-content {
        margin-left: 0 !important;
    }
    
    body {
        margin-bottom: 0;
    }
    
    .ph-hero,
    .cta-section {
        background: none !important;
        color: black !important;
    }
    
    .ph-card,
    .ph-feature-card,
    .ph-stat-card {
        box-shadow: none !important;
        border: 1px solid #ccc !important;
    }
}