using Microsoft.Extensions.Options;
using ParaHockeyApp.Models.Configuration;

namespace ParaHockeyApp.Services
{
    /// <summary>
    /// Service for validating and managing environment configuration consistency
    /// </summary>
    public class EnvironmentConfigurationService : IEnvironmentConfigurationService
    {
        private readonly EnvironmentSettings _environmentSettings;
        private readonly ILogger<EnvironmentConfigurationService> _logger;
        private readonly IConfiguration _configuration;

        public EnvironmentConfigurationService(
            IOptions<EnvironmentSettings> environmentSettings,
            ILogger<EnvironmentConfigurationService> logger,
            IConfiguration configuration)
        {
            _environmentSettings = environmentSettings.Value;
            _logger = logger;
            _configuration = configuration;
        }

        /// <summary>
        /// Validates the current environment configuration
        /// </summary>
        public async Task<EnvironmentValidationResult> ValidateConfigurationAsync()
        {
            var result = new EnvironmentValidationResult
            {
                EnvironmentName = _environmentSettings.Name
            };

            try
            {
                // Validate basic environment settings
                var configErrors = _environmentSettings.ValidateConfiguration();
                result.Errors.AddRange(configErrors);

                // Validate environment-specific constraints
                await ValidateEnvironmentConstraintsAsync(result);

                // Validate configuration consistency
                ValidateConfigurationConsistency(result);

                // Check for security concerns
                ValidateSecurityConfiguration(result);

                result.IsValid = result.Errors.Count == 0;

                // Log validation results
                if (result.IsValid)
                {
                    _logger.LogInformation("Environment configuration validation passed for {Environment}", 
                        _environmentSettings.Name);
                }
                else
                {
                    _logger.LogWarning("Environment configuration validation failed for {Environment}. Errors: {Errors}", 
                        _environmentSettings.Name, string.Join("; ", result.Errors));
                }

                if (result.Warnings.Count > 0)
                {
                    _logger.LogWarning("Environment configuration warnings for {Environment}: {Warnings}", 
                        _environmentSettings.Name, string.Join("; ", result.Warnings));
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error during environment configuration validation");
                result.Errors.Add($"Validation error: {ex.Message}");
                result.IsValid = false;
            }

            return result;
        }

        /// <summary>
        /// Gets the current environment settings
        /// </summary>
        public EnvironmentSettings GetCurrentEnvironmentSettings()
        {
            return _environmentSettings;
        }

        /// <summary>
        /// Checks if the current configuration allows the specified feature
        /// </summary>
        public bool IsFeatureAllowed(string feature)
        {
            return feature?.ToLowerInvariant() switch
            {
                "development-tools" => _environmentSettings.ShowDevelopmentTools,
                "detailed-errors" => _environmentSettings.EnableDetailedErrorLogging,
                "environment-banner" => _environmentSettings.ShowBanner,
                "authentication" => _environmentSettings.UseAuthentication,
                _ => false
            };
        }

        /// <summary>
        /// Gets environment-specific error handling configuration
        /// </summary>
        public ErrorHandlingConfiguration GetErrorHandlingConfiguration()
        {
            return new ErrorHandlingConfiguration
            {
                ShowDetailedErrors = _environmentSettings.EnableDetailedErrorLogging,
                LogStackTraces = _environmentSettings.EnableDetailedErrorLogging,
                ShowUserFriendlyMessages = _environmentSettings.ShowUserFriendlyErrors,
                ErrorDetailLevel = _environmentSettings.ErrorDetailLevel,
                EnableErrorReporting = _environmentSettings.IsNonProduction
            };
        }

        /// <summary>
        /// Gets the configured age of majority for registration purposes
        /// </summary>
        public int GetAgeOfMajority()
        {
            return _environmentSettings.AgeOfMajority;
        }

        /// <summary>
        /// Validates environment-specific constraints
        /// </summary>
        private async Task ValidateEnvironmentConstraintsAsync(EnvironmentValidationResult result)
        {
            // Production environment specific validations
            if (_environmentSettings.IsProduction)
            {
                if (_environmentSettings.ShowDevelopmentTools)
                {
                    result.Errors.Add("Development tools must be disabled in Production");
                }

                if (_environmentSettings.EnableDetailedErrorLogging)
                {
                    result.Warnings.Add("Consider disabling detailed error logging in Production for security");
                }

                if (!_environmentSettings.ShowUserFriendlyErrors)
                {
                    result.Errors.Add("User-friendly errors should be enabled in Production");
                }
            }

            // Development environment specific validations
            if (_environmentSettings.IsDevelopment)
            {
                if (!_environmentSettings.ShowDevelopmentTools)
                {
                    result.Warnings.Add("Development tools should typically be enabled in Development environment");
                }

                if (!_environmentSettings.EnableDetailedErrorLogging)
                {
                    result.Warnings.Add("Detailed error logging should be enabled in Development environment");
                }
            }

            // Test environment specific validations
            if (_environmentSettings.IsTest)
            {
                if (!_environmentSettings.ShowBanner)
                {
                    result.Warnings.Add("Test environment should show banner to distinguish from Production");
                }

                if (string.IsNullOrWhiteSpace(_environmentSettings.BannerText))
                {
                    result.Warnings.Add("Test environment banner should have descriptive text");
                }
            }

            await Task.CompletedTask; // For future async validations
        }

        /// <summary>
        /// Validates configuration consistency across different sections
        /// </summary>
        private void ValidateConfigurationConsistency(EnvironmentValidationResult result)
        {
            // Check logging configuration consistency
            var loggingSection = _configuration.GetSection("Logging");
            if (loggingSection.Exists())
            {
                var defaultLogLevel = loggingSection["LogLevel:Default"];
                
                if (_environmentSettings.IsProduction && defaultLogLevel == "Debug")
                {
                    result.Warnings.Add("Debug logging level should not be used in Production");
                }

                if (_environmentSettings.IsDevelopment && defaultLogLevel == "Error")
                {
                    result.Warnings.Add("Error-only logging may hide important information in Development");
                }
            }

            // Check connection string configuration
            var connectionStrings = _configuration.GetSection("ConnectionStrings");
            if (connectionStrings.Exists())
            {
                var defaultConnection = connectionStrings["DefaultConnection"];
                
                if (_environmentSettings.IsProduction && !string.IsNullOrEmpty(defaultConnection))
                {
                    if (defaultConnection.Contains("Trusted_Connection=True"))
                    {
                        result.Warnings.Add("Production should use SQL authentication instead of Windows authentication");
                    }
                }
            }
        }

        /// <summary>
        /// Validates security-related configuration
        /// </summary>
        private void ValidateSecurityConfiguration(EnvironmentValidationResult result)
        {
            // Check for detailed errors in production
            if (_environmentSettings.IsProduction && _environmentSettings.EnableDetailedErrorLogging)
            {
                result.Warnings.Add("Detailed error logging in Production may expose sensitive information");
            }

            // Check for development tools in production
            if (_environmentSettings.IsProduction && _environmentSettings.ShowDevelopmentTools)
            {
                result.Errors.Add("Development tools must never be enabled in Production environment");
            }

            // Check authentication configuration
            if (_environmentSettings.IsProduction && !_environmentSettings.UseAuthentication)
            {
                result.Warnings.Add("Consider enabling authentication in Production environment");
            }

            // Check for test indicators in production
            if (_environmentSettings.IsProduction && _environmentSettings.ShowBanner)
            {
                result.Errors.Add("Environment banners should not be shown in Production");
            }
        }
    }
}