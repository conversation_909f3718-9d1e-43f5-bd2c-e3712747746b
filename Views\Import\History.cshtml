@model ImportBatchListViewModel
@{
    ViewBag.Title = "Import History";
}

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2><i class="fas fa-history text-primary"></i> Import History</h2>
                <a href="@Url.Action("Upload", "Import")" class="btn btn-primary">
                    <i class="fas fa-plus"></i> New Import
                </a>
            </div>
        </div>
    </div>

    <!-- Summary Statistics -->
    <div class="row mb-4">
        <div class="col-md-3 col-sm-6 mb-3">
            <div class="card text-center border-primary">
                <div class="card-body">
                    <i class="fas fa-file-import text-primary fa-2x mb-2"></i>
                    <h4 class="text-primary mb-1">@Model.TotalBatches</h4>
                    <small class="text-muted">Total Imports</small>
                </div>
            </div>
        </div>
        <div class="col-md-3 col-sm-6 mb-3">
            <div class="card text-center border-success">
                <div class="card-body">
                    <i class="fas fa-check-circle text-success fa-2x mb-2"></i>
                    <h4 class="text-success mb-1">@Model.CompletedBatches</h4>
                    <small class="text-muted">Completed</small>
                </div>
            </div>
        </div>
        <div class="col-md-3 col-sm-6 mb-3">
            <div class="card text-center border-warning">
                <div class="card-body">
                    <i class="fas fa-clock text-warning fa-2x mb-2"></i>
                    <h4 class="text-warning mb-1">@Model.ProcessingBatches</h4>
                    <small class="text-muted">In Progress</small>
                </div>
            </div>
        </div>
        <div class="col-md-3 col-sm-6 mb-3">
            <div class="card text-center border-danger">
                <div class="card-body">
                    <i class="fas fa-exclamation-triangle text-danger fa-2x mb-2"></i>
                    <h4 class="text-danger mb-1">@Model.FailedBatches</h4>
                    <small class="text-muted">Failed</small>
                </div>
            </div>
        </div>
    </div>

    <!-- Filters -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-light">
                    <h6 class="mb-0"><i class="fas fa-filter"></i> Filters</h6>
                </div>
                <div class="card-body">
                    <form method="get" class="row g-3">
                        <div class="col-md-3">
                            <label class="form-label">Search</label>
                            <input type="text" name="search" class="form-control" value="@Model.SearchTerm" 
                                   placeholder="File name or uploader...">
                        </div>
                        <div class="col-md-2">
                            <label class="form-label">Status</label>
                            <select name="status" class="form-select">
                                <option value="">All Statuses</option>
                                <option value="Processing" selected="@(Model.StatusFilter == "Processing")">Processing</option>
                                <option value="Completed" selected="@(Model.StatusFilter == "Completed")">Completed</option>
                                <option value="Failed" selected="@(Model.StatusFilter == "Failed")">Failed</option>
                            </select>
                        </div>
                        <div class="col-md-2">
                            <label class="form-label">From Date</label>
                            <input type="date" name="fromDate" class="form-control" 
                                   value="@(Model.FromDate?.ToString("yyyy-MM-dd"))">
                        </div>
                        <div class="col-md-2">
                            <label class="form-label">To Date</label>
                            <input type="date" name="toDate" class="form-control" 
                                   value="@(Model.ToDate?.ToString("yyyy-MM-dd"))">
                        </div>
                        <div class="col-md-3">
                            <label class="form-label">&nbsp;</label>
                            <div class="d-grid gap-2 d-md-flex">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-search"></i> Filter
                                </button>
                                <a href="@Url.Action("History")" class="btn btn-outline-secondary">
                                    <i class="fas fa-times"></i> Clear
                                </a>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Import Batches List -->
    <div class="row">
        <div class="col-12">
            @if (Model.Batches.Any())
            {
                <div class="card shadow-sm">
                    <div class="card-header bg-light">
                        <h6 class="mb-0">Import Batches (@Model.TotalCount total)</h6>
                    </div>
                    <div class="card-body p-0">
                        <div class="table-responsive">
                            <table class="table table-hover mb-0">
                                <thead class="table-light">
                                    <tr>
                                        <th>File Name</th>
                                        <th>Status</th>
                                        <th class="text-center">Total Rows</th>
                                        <th class="text-center">Progress</th>
                                        <th>Uploaded</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach (var batch in Model.Batches)
                                    {
                                        <tr>
                                            <td>
                                                <div class="d-flex align-items-center">
                                                    <i class="fas fa-file-excel text-success me-2"></i>
                                                    <div>
                                                        <strong>@batch.FileName</strong>
                                                        @if (batch.HasErrors)
                                                        {
                                                            <br><small class="text-danger">@batch.ErrorMessage</small>
                                                        }
                                                    </div>
                                                </div>
                                            </td>
                                            <td>
                                                <span class="badge @batch.StatusBadgeClass">@batch.Status</span>
                                            </td>
                                            <td class="text-center">
                                                <strong>@batch.TotalRows</strong>
                                            </td>
                                            <td class="text-center">
                                                @if (batch.TotalRows > 0)
                                                {
                                                    <div class="progress" style="height: 6px;">
                                                        <div class="progress-bar @(batch.IsCompleted ? "bg-success" : "bg-info")" 
                                                             role="progressbar" 
                                                             style="width: @(batch.ProgressPercentage)%"></div>
                                                    </div>
                                                    <small class="text-muted">@batch.ProgressText</small>
                                                }
                                                else
                                                {
                                                    <span class="text-muted">-</span>
                                                }
                                            </td>
                                            <td>
                                                <div>
                                                    <small class="text-muted">@batch.UploadedAtLocal</small><br>
                                                    <small class="text-primary">@batch.UploadedBy</small>
                                                </div>
                                            </td>
                                            <td>
                                                <div class="btn-group" role="group">
                                                    <a href="@Url.Action("BatchSummary", new { id = batch.ImportBatchId })" 
                                                       class="btn btn-sm btn-outline-primary" title="View Summary">
                                                        <i class="fas fa-eye"></i>
                                                    </a>
                                                    @if (batch.CanProcessMore)
                                                    {
                                                        <a href="@Url.Action("Queue", "TempMembers", new { batchId = batch.ImportBatchId })" 
                                                           class="btn btn-sm btn-outline-success" title="Process Members">
                                                            <i class="fas fa-play"></i>
                                                        </a>
                                                    }
                                                    @if (batch.Status != "Processing")
                                                    {
                                                        <form method="post" asp-action="DeleteBatch" asp-route-id="@batch.ImportBatchId" 
                                                              style="display: inline-block;" 
                                                              onsubmit="return confirm('Are you sure you want to delete this import batch? This action cannot be undone.')">
                                                            @Html.AntiForgeryToken()
                                                            <button type="submit" class="btn btn-sm btn-outline-danger" title="Delete Batch">
                                                                <i class="fas fa-trash"></i>
                                                            </button>
                                                        </form>
                                                    }
                                                </div>
                                            </td>
                                        </tr>
                                    }
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>

                <!-- Pagination -->
                @if (Model.TotalPages > 1)
                {
                    <nav class="mt-4">
                        <ul class="pagination justify-content-center">
                            @if (Model.HasPreviousPage)
                            {
                                <li class="page-item">
                                    <a class="page-link" href="@Url.Action("History", new { page = Model.PageNumber - 1, pageSize = Model.PageSize, status = Model.StatusFilter, search = Model.SearchTerm, fromDate = Model.FromDate?.ToString("yyyy-MM-dd"), toDate = Model.ToDate?.ToString("yyyy-MM-dd") })">
                                        <i class="fas fa-chevron-left"></i> Previous
                                    </a>
                                </li>
                            }

                            @for (int i = Math.Max(1, Model.PageNumber - 2); i <= Math.Min(Model.TotalPages, Model.PageNumber + 2); i++)
                            {
                                <li class="page-item @(i == Model.PageNumber ? "active" : "")">
                                    <a class="page-link" href="@Url.Action("History", new { page = i, pageSize = Model.PageSize, status = Model.StatusFilter, search = Model.SearchTerm, fromDate = Model.FromDate?.ToString("yyyy-MM-dd"), toDate = Model.ToDate?.ToString("yyyy-MM-dd") })">
                                        @i
                                    </a>
                                </li>
                            }

                            @if (Model.HasNextPage)
                            {
                                <li class="page-item">
                                    <a class="page-link" href="@Url.Action("History", new { page = Model.PageNumber + 1, pageSize = Model.PageSize, status = Model.StatusFilter, search = Model.SearchTerm, fromDate = Model.FromDate?.ToString("yyyy-MM-dd"), toDate = Model.ToDate?.ToString("yyyy-MM-dd") })">
                                        Next <i class="fas fa-chevron-right"></i>
                                    </a>
                                </li>
                            }
                        </ul>
                    </nav>
                }
            }
            else
            {
                <div class="card">
                    <div class="card-body text-center py-5">
                        <i class="fas fa-file-import text-muted fa-4x mb-3"></i>
                        <h5 class="text-muted">No Import Batches Found</h5>
                        <p class="text-muted">
                            @if (!string.IsNullOrEmpty(Model.SearchTerm) || !string.IsNullOrEmpty(Model.StatusFilter) || Model.FromDate.HasValue || Model.ToDate.HasValue)
                            {
                                <text>No import batches match your current filters.</text>
                            }
                            else
                            {
                                <text>You haven't imported any member files yet.</text>
                            }
                        </p>
                        <div class="mt-3">
                            @if (!string.IsNullOrEmpty(Model.SearchTerm) || !string.IsNullOrEmpty(Model.StatusFilter) || Model.FromDate.HasValue || Model.ToDate.HasValue)
                            {
                                <a href="@Url.Action("History")" class="btn btn-outline-secondary me-2">
                                    <i class="fas fa-times"></i> Clear Filters
                                </a>
                            }
                            <a href="@Url.Action("Upload")" class="btn btn-primary">
                                <i class="fas fa-plus"></i> Import Members
                            </a>
                        </div>
                    </div>
                </div>
            }
        </div>
    </div>
</div>

@section Scripts {
<script>
    // Show success/error messages from TempData
    @if (TempData["SuccessMessage"] != null)
    {
        <text>
        $(document).ready(function() {
            showAlert('success', '@TempData["SuccessMessage"]');
        });
        </text>
    }

    @if (TempData["ErrorMessage"] != null)
    {
        <text>
        $(document).ready(function() {
            showAlert('danger', '@TempData["ErrorMessage"]');
        });
        </text>
    }

    function showAlert(type, message) {
        const alertHtml = `
            <div class="alert alert-${type} alert-dismissible fade show" role="alert">
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        `;
        $('.container-fluid').prepend(alertHtml);
    }
</script>
}