using System.Collections.Generic;

namespace ParaHockeyApp.Services
{
    /// <summary>
    /// Service for mapping category filter selections to composite category lists.
    /// Enables showing related categories when filtering events (e.g., First Shift shows both First Shift and Pratique First Shift).
    /// </summary>
    public interface ICategoryFilterService
    {
        /// <summary>
        /// Gets the list of category IDs to include when filtering by the selected category.
        /// </summary>
        /// <param name="selectedCategoryId">The category ID selected by the user</param>
        /// <returns>List of category IDs to include in the filter</returns>
        List<int> GetFilterCategoryIds(int selectedCategoryId);
    }
}