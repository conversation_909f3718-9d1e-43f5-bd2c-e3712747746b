# Calendar Filter Enhancement – First Shift & Pratique

## Overview
When a calendar user selects the **First Shift** filter they should see both **First Shift** and **Pratique First Shift** events. When they choose the **Pratique** filter they should see both **Pratique** and **Pratique First Shift** events. Behaviour must be consistent across all calendar views (Admin, Public, Member).

## Functional Requirements
| Ref | Requirement |
|-----|-------------|
| FR1 | Selecting “First Shift” shows events whose category is **First Shift** **or** **Pratique First Shift**. |
| FR2 | Selecting “Pratique” shows events whose category is **Pratique** **or** **Pratique First Shift**. |
| FR3 | Filtering works identically on Desktop grid, Mobile agenda, Public calendar, and any other calendars that use the `GetCalendarEvents` endpoint. |
| FR4 | All other category filters continue to behave exactly as before. |
| FR5 | Server response time for a typical month view remains ≤ 100 ms (baseline performance must not regress). |

## Non-Functional Requirements
| Ref | Requirement |
|-----|-------------|
| NF1 | The solution must follow the existing ASP.NET Core MVC architecture; avoid breaking public API contracts. |
| NF2 | All user-facing text continues to use localization keys; no hard-coded strings. |
| NF3 | Unit and E2E tests must cover FR1–FR4. |
| NF4 | The change must not introduce client-side regressions. |

## Acceptance Criteria
1. On the Admin calendar, selecting “First Shift” → shows **First Shift** ✅ and **Pratique First Shift** ✅; does **not** show pure Pratique ❌.
2. On the Admin calendar, selecting “Pratique” → shows **Pratique** ✅ and **Pratique First Shift** ✅; does **not** show pure First Shift ❌.
3. The same two checks pass on Public and Member calendars.
4. Automated unit tests and E2E tests pass.

## User Stories
* **US-01** – As a coach, I want the First Shift filter to include hybrid sessions so that I can view all First Shift-related events in one place.
* **US-02** – As an organiser, I want the Pratique filter to include hybrid sessions so attendance and reporting are complete. 