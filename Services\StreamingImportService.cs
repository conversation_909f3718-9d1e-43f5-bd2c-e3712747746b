using ClosedXML.Excel;
using Microsoft.AspNetCore.Http;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Options;
using ParaHockeyApp.Models;
using ParaHockeyApp.Models.Entities;
using ParaHockeyApp.DTOs;
using ParaHockeyApp.Services;
using ParaHockeyApp.Exceptions;
using System.Text.Json;
using System.Collections.Concurrent;

namespace ParaHockeyApp.Services
{
    /// <summary>
    /// Service for streaming large file imports with performance optimizations
    /// </summary>
    public class StreamingImportService : IStreamingImportService
    {
        private readonly ApplicationContext _context;
        private readonly INormalizationService _normalizationService;
        private readonly IDuplicateDetectionService _duplicateDetectionService;
        private readonly IAuditLogService _auditLogService;
        private readonly IInputSanitizationService _inputSanitizationService;
        private readonly MemberImportConfig _config;
        private readonly ILogger<StreamingImportService> _logger;

        // Progress tracking
        private static readonly ConcurrentDictionary<int, CancellationTokenSource> _activeCancellationTokens = new();
        private static readonly ConcurrentDictionary<int, ImportProgressInfo> _progressCache = new();

        public StreamingImportService(
            ApplicationContext context,
            INormalizationService normalizationService,
            IDuplicateDetectionService duplicateDetectionService,
            IAuditLogService auditLogService,
            IInputSanitizationService inputSanitizationService,
            IOptions<MemberImportConfig> config,
            ILogger<StreamingImportService> logger)
        {
            _context = context;
            _normalizationService = normalizationService;
            _duplicateDetectionService = duplicateDetectionService;
            _auditLogService = auditLogService;
            _inputSanitizationService = inputSanitizationService;
            _config = config.Value;
            _logger = logger;
        }

        /// <summary>
        /// Processes large Excel files using streaming to avoid memory issues
        /// </summary>
        public async Task<int> StreamProcessFileAsync(IFormFile file, string uploadedBy,
            IProgress<ImportProgressUpdate>? progressCallback = null,
            CancellationToken cancellationToken = default)
        {
            var batchId = 0;
            var combinedCts = CancellationTokenSource.CreateLinkedTokenSource(cancellationToken);

            try
            {
                // Create import batch
                var batch = new MemberImportBatch
                {
                    FileName = _inputSanitizationService.SanitizeFileName(file.FileName),
                    UploadedBy = _inputSanitizationService.SanitizeText(uploadedBy),
                    UploadedAtUtc = DateTime.UtcNow,
                    Status = "Processing",
                    TotalRows = 0 // Will be updated as we process
                };

                _context.MemberImportBatches.Add(batch);
                await _context.SaveChangesAsync(cancellationToken);
                batchId = batch.Id;

                // Register cancellation token and progress tracking
                _activeCancellationTokens[batchId] = combinedCts;
                _progressCache[batchId] = new ImportProgressInfo
                {
                    BatchId = batchId,
                    FileName = batch.FileName,
                    Status = ImportStatus.InProgress,
                    StartedAt = DateTime.UtcNow,
                    ProcessingStages = new List<string> { "Reading File", "Processing Rows", "Duplicate Detection", "Finalizing" },
                    CurrentStageIndex = 0
                };

                // Process file in streaming mode
                await ProcessFileInBatchesAsync(file, batch, progressCallback, combinedCts.Token);

                _logger.LogInformation("Streaming import completed for batch {BatchId}", batchId);
                return batchId;
            }
            catch (OperationCanceledException)
            {
                _logger.LogWarning("Import was cancelled for batch {BatchId}", batchId);
                await UpdateBatchStatus(batchId, "Cancelled", "Import was cancelled by user");
                throw;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error during streaming import for batch {BatchId}", batchId);
                var userFriendlyMessage = GetUserFriendlyErrorMessage(ex);
                await UpdateBatchStatus(batchId, "Failed", userFriendlyMessage);
                throw new ImportException(userFriendlyMessage, ex);
            }
            finally
            {
                // Cleanup
                _activeCancellationTokens.TryRemove(batchId, out _);
                if (_progressCache.TryGetValue(batchId, out var progress))
                {
                    progress.Status = ImportStatus.Completed;
                    progress.CompletedAt = DateTime.UtcNow;
                }
            }
        }

        /// <summary>
        /// Gets import progress for a specific batch
        /// </summary>
        public async Task<ImportProgressInfo> GetImportProgressAsync(int batchId)
        {
            if (_progressCache.TryGetValue(batchId, out var cachedProgress))
            {
                return cachedProgress;
            }

            // Fall back to database lookup
            var batch = await _context.MemberImportBatches
                .FirstOrDefaultAsync(b => b.Id == batchId);

            if (batch == null)
                throw new ArgumentException($"Import batch {batchId} not found");

            return new ImportProgressInfo
            {
                BatchId = batchId,
                FileName = batch.FileName,
                Status = batch.Status switch
                {
                    "Processing" => ImportStatus.InProgress,
                    "Completed" => ImportStatus.Completed,
                    "Failed" => ImportStatus.Failed,
                    "Cancelled" => ImportStatus.Cancelled,
                    _ => ImportStatus.Pending
                },
                ProcessedRows = batch.TotalRows, // Approximation
                TotalRows = batch.TotalRows,
                StartedAt = batch.UploadedAtUtc,
                ErrorMessage = batch.ErrorMessage
            };
        }

        /// <summary>
        /// Cancels an ongoing import operation
        /// </summary>
        public async Task<bool> CancelImportAsync(int batchId)
        {
            if (_activeCancellationTokens.TryGetValue(batchId, out var cts))
            {
                cts.Cancel();
                await UpdateBatchStatus(batchId, "Cancelled", "Import was cancelled");
                _logger.LogInformation("Import cancellation requested for batch {BatchId}", batchId);
                return true;
            }

            return false;
        }

        /// <summary>
        /// Validates file size and format for streaming import
        /// </summary>
        public async Task<StreamingValidationResult> ValidateForStreamingAsync(IFormFile file)
        {
            var result = new StreamingValidationResult
            {
                FileSizeBytes = file.Length
            };

            // Check file extension
            if (!Path.GetExtension(file.FileName).Equals(".xlsx", StringComparison.OrdinalIgnoreCase))
            {
                result.Errors.Add("File must be an Excel (.xlsx) file");
                return result;
            }

            // Size-based recommendations
            if (file.Length > 50 * 1024 * 1024) // 50MB
            {
                result.RecommendStreaming = true;
                result.RecommendedStrategy = "Streaming required for large files";
                result.RecommendedBatchSize = 100; // Smaller batches for large files
            }
            else if (file.Length > 10 * 1024 * 1024) // 10MB
            {
                result.RecommendStreaming = true;
                result.RecommendedStrategy = "Streaming recommended for better performance";
                result.RecommendedBatchSize = 250;
                result.Warnings.Add("Large file detected - streaming mode recommended");
            }
            else
            {
                result.RecommendStreaming = false;
                result.RecommendedStrategy = "Standard processing suitable";
                result.RecommendedBatchSize = 500;
            }

            // Estimate row count (rough approximation)
            result.EstimatedRowCount = (int)(file.Length / 200); // Assume ~200 bytes per row

            if (result.EstimatedRowCount > _config.MaxRowCount)
            {
                result.Errors.Add($"Estimated {result.EstimatedRowCount:N0} rows exceeds maximum allowed {_config.MaxRowCount:N0} rows");
            }

            result.IsValid = result.Errors.Count == 0;
            return result;
        }

        /// <summary>
        /// Processes file in batches to handle large files efficiently
        /// </summary>
        private async Task ProcessFileInBatchesAsync(IFormFile file, MemberImportBatch batch,
            IProgress<ImportProgressUpdate>? progressCallback, CancellationToken cancellationToken)
        {
            using var stream = file.OpenReadStream();
            using var workbook = new XLWorkbook(stream);
            var worksheet = workbook.Worksheet(1);

            var headers = GetHeaderMapping(worksheet);
            var lastRowUsed = worksheet.LastRowUsed()?.RowNumber() ?? 1;
            var totalRows = lastRowUsed - 1; // Exclude header row

            batch.TotalRows = totalRows;
            await _context.SaveChangesAsync(cancellationToken);

            // Update progress tracking
            if (_progressCache.TryGetValue(batch.Id, out var progress))
            {
                progress.TotalRows = totalRows;
                progress.CurrentStage = "Processing Rows";
                progress.CurrentStageIndex = 1;
            }

            var batchSize = _config.BatchSize;
            var processedRows = 0;

            // Process in batches
            for (int startRow = 2; startRow <= lastRowUsed; startRow += batchSize)
            {
                cancellationToken.ThrowIfCancellationRequested();

                var endRow = Math.Min(startRow + batchSize - 1, lastRowUsed);
                var tempMembers = new List<TempMember>();

                // Process batch
                using (var transaction = await _context.Database.BeginTransactionAsync(cancellationToken))
                {
                    try
                    {
                        // Parse rows in current batch
                        for (int rowNumber = startRow; rowNumber <= endRow; rowNumber++)
                        {
                            var row = worksheet.Row(rowNumber);
                            var tempMember = ParseRowToTempMember(row, headers, batch.Id, rowNumber);
                            tempMembers.Add(tempMember);
                        }

                        // Process duplicates for this batch
                        await ProcessDuplicatesInBatch(tempMembers, cancellationToken);

                        // Save batch to database
                        _context.TempMembers.AddRange(tempMembers);
                        await _context.SaveChangesAsync(cancellationToken);
                        await transaction.CommitAsync(cancellationToken);

                        processedRows += tempMembers.Count;

                        // Report progress
                        var updateInfo = new ImportProgressUpdate
                        {
                            BatchId = batch.Id,
                            Stage = "Processing Rows",
                            ProcessedRows = processedRows,
                            TotalRows = totalRows,
                            StatusMessage = $"Processed {processedRows:N0} of {totalRows:N0} rows"
                        };

                        progressCallback?.Report(updateInfo);

                        // Update cache
                        if (_progressCache.TryGetValue(batch.Id, out var prog))
                        {
                            prog.ProcessedRows = processedRows;
                        }

                        _logger.LogDebug("Processed batch {StartRow}-{EndRow} for batch {BatchId}",
                            startRow, endRow, batch.Id);
                    }
                    catch (Exception ex)
                    {
                        await transaction.RollbackAsync(cancellationToken);
                        _logger.LogError(ex, "Error processing batch {StartRow}-{EndRow} for batch {BatchId}",
                            startRow, endRow, batch.Id);
                        throw;
                    }
                }

                // Small delay to prevent overwhelming the database
                await Task.Delay(10, cancellationToken);
            }

            // Final stages
            await UpdateBatchStatistics(batch.Id, cancellationToken);
            await UpdateBatchStatus(batch.Id, "Completed", null);

            // Final progress update
            progressCallback?.Report(new ImportProgressUpdate
            {
                BatchId = batch.Id,
                Stage = "Completed",
                ProcessedRows = totalRows,
                TotalRows = totalRows,
                StatusMessage = "Import completed successfully",
                IsCompleted = true
            });
        }

        /// <summary>
        /// Processes duplicates for a batch of temp members
        /// </summary>
        private async Task ProcessDuplicatesInBatch(List<TempMember> tempMembers, CancellationToken cancellationToken)
        {
            foreach (var tempMember in tempMembers)
            {
                cancellationToken.ThrowIfCancellationRequested();

                try
                {
                    // Sanitize input data
                    tempMember.FirstName = _inputSanitizationService.SanitizeText(tempMember.FirstName);
                    tempMember.LastName = _inputSanitizationService.SanitizeText(tempMember.LastName);
                    tempMember.Email = _inputSanitizationService.SanitizeEmail(tempMember.Email);
                    tempMember.Phone = _inputSanitizationService.SanitizePhone(tempMember.Phone);

                    // Normalize data
                    if (!string.IsNullOrEmpty(tempMember.Email))
                        tempMember.Email = _normalizationService.NormalizeEmail(tempMember.Email);

                    if (!string.IsNullOrEmpty(tempMember.Phone))
                        tempMember.Phone = _normalizationService.NormalizePhone(tempMember.Phone);

                    // Check for duplicates
                    var existingMember = await _duplicateDetectionService.FindExistingMemberAsync(tempMember);

                    if (existingMember != null)
                    {
                        tempMember.Status = TempMemberStatus.Duplicate;
                        tempMember.ExistingMemberId = existingMember.Id;
                    }
                    else
                    {
                        tempMember.Status = TempMemberStatus.ReadyToCreate;
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogWarning(ex, "Error processing temp member in row {RowNumber}",
                        tempMembers.IndexOf(tempMember) + 1);
                    tempMember.Status = TempMemberStatus.NeedsFix;
                    tempMember.ValidationErrorsJson = JsonSerializer.Serialize(new[] { ex.Message });
                }
            }
        }

        /// <summary>
        /// Updates batch status in database
        /// </summary>
        private async Task UpdateBatchStatus(int batchId, string status, string? errorMessage)
        {
            try
            {
                var batch = await _context.MemberImportBatches.FindAsync(batchId);
                if (batch != null)
                {
                    batch.Status = status;
                    batch.ErrorMessage = errorMessage;
                    await _context.SaveChangesAsync();
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to update batch status for batch {BatchId}", batchId);
            }
        }

        /// <summary>
        /// Updates batch statistics after processing
        /// </summary>
        private async Task UpdateBatchStatistics(int batchId, CancellationToken cancellationToken)
        {
            var batch = await _context.MemberImportBatches.FindAsync(new object[] { batchId }, cancellationToken);
            if (batch == null) return;

            var statusCounts = await _context.TempMembers
                .Where(tm => tm.ImportBatchId == batchId)
                .GroupBy(tm => tm.Status)
                .Select(g => new { Status = g.Key, Count = g.Count() })
                .ToListAsync(cancellationToken);

            // Update batch statistics (these would be additional fields on MemberImportBatch)
            // For now, just log the statistics
            _logger.LogInformation("Batch {BatchId} statistics: {StatusCounts}",
                batchId, string.Join(", ", statusCounts.Select(sc => $"{sc.Status}: {sc.Count}")));
        }

        /// <summary>
        /// Creates header mapping from Excel worksheet
        /// </summary>
        private Dictionary<string, int> GetHeaderMapping(IXLWorksheet worksheet)
        {
            var headerMapping = new Dictionary<string, int>(StringComparer.OrdinalIgnoreCase);
            var headerRow = worksheet.Row(1);
            var lastColumnUsed = worksheet.LastColumnUsed()?.ColumnNumber() ?? 1;

            for (int col = 1; col <= lastColumnUsed; col++)
            {
                var cellValue = headerRow.Cell(col).GetString().Trim();
                if (!string.IsNullOrEmpty(cellValue))
                {
                    headerMapping[cellValue] = col;
                }
            }

            return headerMapping;
        }

        /// <summary>
        /// Parses a single row into a TempMember object
        /// </summary>
        private TempMember ParseRowToTempMember(IXLRow row, Dictionary<string, int> headers, int importBatchId, int rowNumber)
        {
            var tempMember = new TempMember
            {
                ImportBatchId = importBatchId,
                FirstName = GetCellValue(row, headers, "Prénom") ?? string.Empty,
                LastName = GetCellValue(row, headers, "Nom") ?? string.Empty,
                Email = GetCellValue(row, headers, "Courriel"),
                Phone = GetCellValue(row, headers, "Numéro de téléphone"),
                HcrNumber = GetCellValue(row, headers, "Numéro HCR"),
                Address = GetCellValue(row, headers, "Adresse") ??
                         CombineAddress(
                             GetCellValue(row, headers, "Numéro d'unité"),
                             GetCellValue(row, headers, "Numéro de rue"),
                             GetCellValue(row, headers, "Rue")),
                City = GetCellValue(row, headers, "Ville"),
                PostalCode = GetCellValue(row, headers, "Code Postal"),
                GenderText = GetCellValue(row, headers, "Identité de genre"),
                ProvinceText = GetCellValue(row, headers, "Province"),
                RawSourceJson = JsonSerializer.Serialize(GetRowData(row, headers))
            };

            // Parse date of birth
            var dobText = GetCellValue(row, headers, "Date de naissance");
            if (!string.IsNullOrEmpty(dobText) && DateTime.TryParse(dobText, out var dob))
            {
                tempMember.DateOfBirth = dob;
            }

            return tempMember;
        }

        /// <summary>
        /// Gets cell value safely
        /// </summary>
        private string? GetCellValue(IXLRow row, Dictionary<string, int> headers, string columnName)
        {
            if (headers.TryGetValue(columnName, out var columnIndex))
            {
                var cellValue = row.Cell(columnIndex).GetString().Trim();
                return string.IsNullOrEmpty(cellValue) ? null : cellValue;
            }
            return null;
        }

        /// <summary>
        /// Combines address components
        /// </summary>
        private string? CombineAddress(string? unit, string? number, string? street)
        {
            var parts = new[] { unit, number, street }.Where(p => !string.IsNullOrEmpty(p));
            var combined = string.Join(" ", parts);
            return string.IsNullOrEmpty(combined) ? null : combined;
        }

        /// <summary>
        /// Gets all row data as dictionary
        /// </summary>
        private Dictionary<string, string> GetRowData(IXLRow row, Dictionary<string, int> headers)
        {
            var data = new Dictionary<string, string>();
            foreach (var header in headers)
            {
                var value = GetCellValue(row, headers, header.Key);
                if (!string.IsNullOrEmpty(value))
                {
                    data[header.Key] = value;
                }
            }
            return data;
        }

        /// <summary>
        /// Converts technical exceptions to user-friendly error messages
        /// </summary>
        private string GetUserFriendlyErrorMessage(Exception ex)
        {
            return ex switch
            {
                FileNotFoundException => "The import file could not be found. Please check the file path and try again.",
                UnauthorizedAccessException => "Access denied. Please check file permissions and try again.",
                InvalidDataException => "The file format is invalid or corrupted. Please check your Excel file and ensure it follows the expected format.",
                OutOfMemoryException => "The file is too large to process. Please split it into smaller files (under 10MB recommended).",
                TimeoutException => "The import operation timed out. Please try again with a smaller file or contact support.",
                ArgumentException when ex.Message.Contains("workbook") => "The Excel file format is not supported. Please use .xlsx format.",
                ArgumentException when ex.Message.Contains("worksheet") => "The Excel file must contain at least one worksheet with data.",
                DbUpdateException => "Database error occurred while saving import data. Please try again or contact support.",
                OperationCanceledException => "The import was cancelled. You can restart the import if needed.",
                System.ComponentModel.DataAnnotations.ValidationException => $"Data validation failed: {ex.Message}",
                ImportException => ex.Message, // Already user-friendly
                _ => "An unexpected error occurred during import. Please contact support if the problem persists."
            };
        }
    }
}