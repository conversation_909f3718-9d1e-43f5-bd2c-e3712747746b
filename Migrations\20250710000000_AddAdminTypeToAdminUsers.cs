using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace ParaHockeyApp.Migrations
{
    /// <inheritdoc />
    public partial class AddAdminTypeToAdminUsers : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            // Add AdminType column with default value of 3 (Normal Admin)
            migrationBuilder.AddColumn<int>(
                name: "AdminType",
                table: "AdminUsers",
                type: "int",
                nullable: false,
                defaultValue: 3);

            // Update existing records ONLY if IsMasterAdmin column exists
            migrationBuilder.Sql(@"
                IF EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('AdminUsers') AND name = 'IsMasterAdmin')
                BEGIN
                    UPDATE AdminUsers 
                    SET AdminType = CASE 
                        WHEN IsMasterAdmin = 1 THEN 9 
                        ELSE 3 
                    END
                END");

            // Note: We keep IsMasterAdmin column for now to ensure backward compatibility
            // It will be removed in a future migration after all code is updated
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "AdminType",
                table: "AdminUsers");
        }
    }
}