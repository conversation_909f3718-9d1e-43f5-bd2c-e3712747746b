using Microsoft.EntityFrameworkCore;
using ParaHockeyApp.DTOs;
using ParaHockeyApp.Models;

namespace ParaHockeyApp.Services
{
    /// <summary>
    /// Service implementation for detecting duplicate member registrations
    /// </summary>
    public class DuplicateMemberService : IDuplicateMemberService
    {
        private readonly ApplicationContext _context;
        private readonly ILogger<DuplicateMemberService> _logger;

        public DuplicateMemberService(ApplicationContext context, ILogger<DuplicateMemberService> logger)
        {
            _context = context;
            _logger = logger;
        }

        public async Task<DuplicateCheckResult> CheckForDuplicatesAsync(string email, string lastName, DateTime? dateOfBirth)
        {
            try
            {
                // Check for exact email match first (case-insensitive)
                var exactEmailMatch = await _context.Members
                    .FirstOrDefaultAsync(m => m.Email.ToLower() == email.ToLower());

                if (exactEmailMatch != null)
                {
                    _logger.LogWarning("Duplicate member detected: Type={Type}, ExistingMemberId={MemberId}",
                        DuplicateType.ExactEmailMatch, exactEmailMatch.Id);
                    
                    return new DuplicateCheckResult
                    {
                        Type = DuplicateType.ExactEmailMatch,
                        ExistingMember = exactEmailMatch
                    };
                }

                // Check for partial match (lastName + dateOfBirth) - only if dateOfBirth is provided
                var partialMatch = dateOfBirth.HasValue ? await _context.Members
                    .FirstOrDefaultAsync(m => m.LastName.ToLower() == lastName.ToLower()
                                           && m.DateOfBirth.Date == dateOfBirth.Value.Date) : null;

                if (partialMatch != null)
                {
                    _logger.LogWarning("Duplicate member detected: Type={Type}, ExistingMemberId={MemberId}",
                        DuplicateType.PartialMatch, partialMatch.Id);
                    
                    return new DuplicateCheckResult
                    {
                        Type = DuplicateType.PartialMatch,
                        ExistingMember = partialMatch,
                        MaskedEmail = MaskEmail(partialMatch.Email)
                    };
                }

                _logger.LogInformation("Duplicate check performed for email: {Email}, Result: {Result}",
                    MaskEmail(email), DuplicateType.NoDuplicate);

                return new DuplicateCheckResult { Type = DuplicateType.NoDuplicate };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error during duplicate member check");
                // In case of error, allow registration to proceed
                return new DuplicateCheckResult { Type = DuplicateType.NoDuplicate };
            }
        }

        public string MaskEmail(string email)
        {
            if (string.IsNullOrWhiteSpace(email) || !email.Contains('@'))
            {
                return "***";
            }

            var parts = email.Split('@');
            var localPart = parts[0];
            var domain = parts[1];

            // Show first 2 characters of local part, or just 1 if local part is short
            var visibleChars = Math.Min(2, Math.Max(1, localPart.Length / 3));
            var maskedLocal = localPart.Length <= visibleChars 
                ? localPart 
                : localPart.Substring(0, visibleChars) + "***";

            return $"{maskedLocal}@{domain}";
        }
    }
}