namespace ParaHockeyApp.DTOs
{
    /// <summary>
    /// Information about which fields matched the search criteria
    /// Used for highlighting search results and providing user feedback
    /// </summary>
    public class SearchMatchInfo
    {
        /// <summary>
        /// Name of the field that matched (e.g., "FirstName", "Email", "Phone")
        /// </summary>
        public string FieldName { get; set; } = string.Empty;

        /// <summary>
        /// Display name of the field for user interface (localized)
        /// </summary>
        public string FieldDisplayName { get; set; } = string.Empty;

        /// <summary>
        /// The actual value that matched the search term
        /// </summary>
        public string MatchedValue { get; set; } = string.Empty;

        /// <summary>
        /// The search term that caused the match
        /// </summary>
        public string SearchTerm { get; set; } = string.Empty;

        /// <summary>
        /// Type of match (exact, partial, normalized phone, etc.)
        /// </summary>
        public SearchMatchType MatchType { get; set; }

        /// <summary>
        /// Position where the match starts in the field value (for highlighting)
        /// </summary>
        public int MatchStartPosition { get; set; }

        /// <summary>
        /// Length of the matched text (for highlighting)
        /// </summary>
        public int MatchLength { get; set; }
    }

    /// <summary>
    /// Types of search matches for different highlighting and display purposes
    /// </summary>
    public enum SearchMatchType
    {
        /// <summary>
        /// Exact match of the search term
        /// </summary>
        Exact,

        /// <summary>
        /// Partial match (search term is contained within the field value)
        /// </summary>
        Partial,

        /// <summary>
        /// Phone number match after normalization (removing formatting)
        /// </summary>
        NormalizedPhone,

        /// <summary>
        /// Case-insensitive match
        /// </summary>
        CaseInsensitive,

        /// <summary>
        /// Match found in a related field (e.g., province name vs province ID)
        /// </summary>
        Related
    }
}