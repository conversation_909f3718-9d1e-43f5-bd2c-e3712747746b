# Auto-Fill Registration Form - Actually fills the form automatically
# Uses COM automation to interact with Internet Explorer/Edge

param(
    [Parameter(Mandatory=$true)]
    [ValidateSet("<PERSON>", "Development", "Elite", "Coach", "Volunteer")]
    [string]$MemberType,
    
    [Parameter(Mandatory=$false)]
    [int]$Count = 1
)

Add-Type -AssemblyName System.Windows.Forms

# Counter for incremental names
$counterFile = "registration-counter.txt"
$baseCounter = 1
if (Test-Path $counterFile) {
    $baseCounter = [int](Get-Content $counterFile)
}

# Sample data
$firstNames = @("<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>")
$lastNames = @("<PERSON><PERSON><PERSON><PERSON>", "<PERSON>ag<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "Bouchard", "Gauthier", "<PERSON>rin", "Lavoie", "Fortin", "Gagné")
$cities = @("Montréal", "Québec", "Laval", "Gatineau", "<PERSON><PERSON><PERSON>", "Sherbrooke")
$addresses = @("123 rue Principale", "456 avenue des Érables", "789 boulevard Saint-Laurent", "321 rue de la Paix")

function Get-RandomPostalCode {
    $letters = "ABCDEFGHIJKLMNOPQRSTUVWXYZ"
    $l1 = $letters[(Get-Random -Maximum 26)]
    $n1 = Get-Random -Maximum 10
    $l2 = $letters[(Get-Random -Maximum 26)]
    $n2 = Get-Random -Maximum 10
    $l3 = $letters[(Get-Random -Maximum 26)]
    $n3 = Get-Random -Maximum 10
    return "$l1$n1$l2 $n2$l3$n3"
}

function Get-RandomPhone {
    $area = Get-Random -Minimum 400 -Maximum 999
    $exchange = Get-Random -Minimum 200 -Maximum 999
    $number = Get-Random -Minimum 1000 -Maximum 9999
    return "($area) $exchange-$number"
}

function Get-RandomEmail {
    param($firstName, $lastName, $counter)
    $domains = @("gmail.com", "outlook.com", "yahoo.ca")
    $domain = $domains[(Get-Random -Maximum $domains.Count)]
    return "$firstName.$lastName$counter@$domain".ToLower()
}

function Get-RandomBirthDate {
    param($memberType)
    $today = Get-Date
    switch ($memberType) {
        "Junior" { $yearsAgo = Get-Random -Minimum 8 -Maximum 18 }
        "Development" { $yearsAgo = Get-Random -Minimum 16 -Maximum 26 }
        "Elite" { $yearsAgo = Get-Random -Minimum 18 -Maximum 36 }
        "Coach" { $yearsAgo = Get-Random -Minimum 25 -Maximum 56 }
        "Volunteer" { $yearsAgo = Get-Random -Minimum 18 -Maximum 71 }
    }
    return $today.AddYears(-$yearsAgo).ToString("yyyy-MM-dd")
}

function Get-RegistrationTypeId {
    param($memberType)
    switch ($memberType) {
        "Junior" { return "1" }
        "Development" { return "2" }
        "Elite" { return "3" }
        "Coach" { return "4" }
        "Volunteer" { return "5" }
    }
}

function Fill-FormWithJavaScript {
    param($memberData)
    
    $js = @"
// Auto-fill registration form
document.getElementsByName('FirstName')[0].value = '$($memberData.FirstName)';
document.getElementsByName('LastName')[0].value = '$($memberData.LastName)';
document.getElementsByName('Email')[0].value = '$($memberData.Email)';
document.getElementsByName('Phone')[0].value = '$($memberData.Phone)';
document.getElementsByName('DateOfBirth')[0].value = '$($memberData.BirthDate)';
document.getElementsByName('Address')[0].value = '$($memberData.Address)';
document.getElementsByName('City')[0].value = '$($memberData.City)';
document.getElementsByName('PostalCode')[0].value = '$($memberData.PostalCode)';

// Select radio buttons
document.getElementById('reg-$($memberData.RegistrationTypeId)').checked = true;
document.getElementById('gender-$($memberData.GenderId)').checked = true;
document.getElementById('phone-$($memberData.PhoneTypeId)').checked = true;

// Select province dropdown
document.getElementsByName('ProvinceId')[0].value = '$($memberData.ProvinceId)';
"@

    if ($memberData.MemberType -eq "Junior") {
        $js += @"

// Parent information
document.getElementsByName('Parents[0].ParentType')[0].value = 'Parent';
document.getElementsByName('Parents[0].FirstName')[0].value = '$($memberData.ParentFirstName)';
document.getElementsByName('Parents[0].LastName')[0].value = '$($memberData.ParentLastName)';
document.getElementsByName('Parents[0].Phone')[0].value = '$($memberData.ParentPhone)';
document.getElementsByName('Parents[0].Email')[0].value = '$($memberData.ParentEmail)';
"@
    } else {
        $js += @"

// Emergency contact
document.getElementsByName('EmergencyContact.FirstName')[0].value = '$($memberData.EmergencyFirstName)';
document.getElementsByName('EmergencyContact.LastName')[0].value = '$($memberData.EmergencyLastName)';
document.getElementsByName('EmergencyContact.Phone')[0].value = '$($memberData.EmergencyPhone)';
document.getElementsByName('EmergencyContact.RelationToUser')[0].value = 'Friend';
document.getElementsByName('EmergencyContact.Email')[0].value = '$($memberData.EmergencyEmail)';
"@
    }
    
    $js += @"

alert('Form filled! Review and click Register to submit.');
"@
    
    return $js
}

Write-Host "🏒 Auto-Fill Registration Form" -ForegroundColor Cyan
Write-Host "==============================" -ForegroundColor Cyan
Write-Host ""

# Open browser first
Write-Host "🌐 Opening registration page..." -ForegroundColor Yellow
Start-Process "http://localhost:5285/Members/Register"
Start-Sleep -Seconds 3

for ($i = 0; $i -lt $Count; $i++) {
    $currentCounter = $baseCounter + $i
    
    # Generate member data
    $memberData = @{
        MemberType = $MemberType
        FirstName = ($firstNames | Get-Random) + $currentCounter
        LastName = ($lastNames | Get-Random) + $currentCounter
        Email = ""
        Phone = Get-RandomPhone
        BirthDate = Get-RandomBirthDate -memberType $MemberType
        Address = $addresses | Get-Random
        City = $cities | Get-Random
        PostalCode = Get-RandomPostalCode
        RegistrationTypeId = Get-RegistrationTypeId -memberType $MemberType
        GenderId = (Get-Random -Minimum 1 -Maximum 4).ToString()
        ProvinceId = (if ((Get-Random -Maximum 10) -lt 7) { 1 } else { Get-Random -Minimum 1 -Maximum 14 }).ToString()
        PhoneTypeId = (if ((Get-Random -Maximum 10) -lt 8) { 1 } else { 2 }).ToString()
    }
    
    $memberData.Email = Get-RandomEmail -firstName $memberData.FirstName -lastName $memberData.LastName -counter $currentCounter
    
    # Add parent or emergency contact data
    if ($MemberType -eq "Junior") {
        $memberData.ParentFirstName = ($firstNames | Get-Random) + "_Parent$currentCounter"
        $memberData.ParentLastName = $memberData.LastName
        $memberData.ParentEmail = Get-RandomEmail -firstName $memberData.ParentFirstName -lastName $memberData.ParentLastName -counter $currentCounter
        $memberData.ParentPhone = Get-RandomPhone
    } else {
        $memberData.EmergencyFirstName = ($firstNames | Get-Random) + "_Emergency$currentCounter"
        $memberData.EmergencyLastName = $memberData.LastName
        $memberData.EmergencyEmail = Get-RandomEmail -firstName $memberData.EmergencyFirstName -lastName $memberData.EmergencyLastName -counter $currentCounter
        $memberData.EmergencyPhone = Get-RandomPhone
    }
    
    Write-Host "👤 Member $($i + 1)/$Count`: $($memberData.FirstName) $($memberData.LastName) ($MemberType)" -ForegroundColor Green
    
    # Generate JavaScript
    $jsCode = Fill-FormWithJavaScript -memberData $memberData
    $jsFileName = "autofill-$MemberType-$currentCounter.js"
    $jsCode | Out-File -FilePath $jsFileName -Encoding UTF8
    
    Write-Host "📝 JavaScript saved to: $jsFileName" -ForegroundColor Yellow
    Write-Host ""
    Write-Host "🖱️ TO FILL THE FORM:" -ForegroundColor Cyan
    Write-Host "1. Go to the browser with the registration page" -ForegroundColor White
    Write-Host "2. Press F12 to open Developer Tools" -ForegroundColor White
    Write-Host "3. Click 'Console' tab" -ForegroundColor White
    Write-Host "4. Copy and paste this JavaScript:" -ForegroundColor White
    Write-Host ""
    Write-Host $jsCode -ForegroundColor Gray
    Write-Host ""
    Write-Host "5. Press Enter - the form will be filled automatically!" -ForegroundColor White
    Write-Host "6. Click Register to submit" -ForegroundColor White
    Write-Host ""
    
    if ($i -lt ($Count - 1)) {
        $continue = Read-Host "Press Enter to generate next member (or 'q' to quit)"
        if ($continue -eq 'q') { break }
        Write-Host ""
    }
}

# Update counter
$newCounter = $baseCounter + $Count
$newCounter | Out-File -FilePath $counterFile -Encoding UTF8

Write-Host "✅ Generated $Count member(s) of type '$MemberType'" -ForegroundColor Green
Write-Host "📊 Next counter will start at: $newCounter" -ForegroundColor Yellow