using System.ComponentModel.DataAnnotations;

namespace ParaHockeyApp.Models.ViewModels
{
    public class ParentViewModel
    {
        [Required(ErrorMessageResourceName = "ValidationRequired", ErrorMessageResourceType = typeof(Resources.SharedResource))]
        [StringLength(50, MinimumLength = 2, ErrorMessageResourceName = "ValidationStringLength", ErrorMessageResourceType = typeof(Resources.SharedResource))]
        [Display(Name = "FirstName")]
        public string FirstName { get; set; } = string.Empty;

        [Required(ErrorMessageResourceName = "ValidationRequired", ErrorMessageResourceType = typeof(Resources.SharedResource))]
        [StringLength(50, MinimumLength = 2, ErrorMessageResourceName = "ValidationStringLength", ErrorMessageResourceType = typeof(Resources.SharedResource))]
        [Display(Name = "LastName")]
        public string LastName { get; set; } = string.Empty;

        [Required(ErrorMessageResourceName = "ValidationRequired", ErrorMessageResourceType = typeof(Resources.SharedResource))]
        [StringLength(50, ErrorMessageResourceName = "ValidationStringLength", ErrorMessageResourceType = typeof(Resources.SharedResource))]
        [Display(Name = "ParentType")]
        public string ParentType { get; set; } = string.Empty;

        [Required(ErrorMessageResourceName = "ValidationRequired", ErrorMessageResourceType = typeof(Resources.SharedResource))]
        [RegularExpression(@"^\(?\d{3}\)?[\s.-]?\d{3}[\s.-]?\d{4}$", ErrorMessageResourceName = "ValidationPhoneSpecific", ErrorMessageResourceType = typeof(Resources.SharedResource))]
        [Display(Name = "Phone")]
        public string Phone { get; set; } = string.Empty;

        [Required(ErrorMessageResourceName = "ValidationRequired", ErrorMessageResourceType = typeof(Resources.SharedResource))]
        [RegularExpression(@"^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$", ErrorMessageResourceName = "ValidationEmailSpecific", ErrorMessageResourceType = typeof(Resources.SharedResource))]
        [Display(Name = "Email")]
        public string Email { get; set; } = string.Empty;
    }
}