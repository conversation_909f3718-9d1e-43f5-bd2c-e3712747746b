using ParaHockeyApp.Models.Entities;
using System.Globalization;
using System.Text.RegularExpressions;

namespace ParaHockeyApp.Services
{
    /// <summary>
    /// Service for normalizing and cleaning imported member data
    /// </summary>
    public class NormalizationService : INormalizationService
    {
        /// <summary>
        /// Normalizes email address (lowercase, trim)
        /// </summary>
        public string? NormalizeEmail(string? email)
        {
            if (string.IsNullOrWhiteSpace(email))
                return null;

            var normalized = email.Trim().ToLowerInvariant();

            // Basic email validation
            if (!normalized.Contains("@") || normalized.Length < 5)
                return null;

            // More comprehensive email validation using regex
            var emailRegex = new Regex(@"^[^@\s]+@[^@\s]+\.[^@\s]+$", RegexOptions.IgnoreCase);
            if (!emailRegex.IsMatch(normalized))
                return null;

            return normalized;
        }

        /// <summary>
        /// Normalizes phone number (strip non-digits, format)
        /// </summary>
        public string? NormalizePhone(string? phone)
        {
            if (string.IsNullOrWhiteSpace(phone))
                return null;

            // Remove all non-digit characters except +
            var digitsOnly = Regex.Replace(phone, @"[^\d+]", "");

            // Remove leading +1 for North American numbers
            if (digitsOnly.StartsWith("+1"))
                digitsOnly = digitsOnly.Substring(2);
            else if (digitsOnly.StartsWith("1") && digitsOnly.Length == 11)
                digitsOnly = digitsOnly.Substring(1);

            // Must be exactly 10 digits for North American phone numbers
            if (digitsOnly.Length != 10 || !Regex.IsMatch(digitsOnly, @"^\d{10}$"))
                return null;

            // Format as (XXX) XXX-XXXX
            return $"({digitsOnly.Substring(0, 3)}) {digitsOnly.Substring(3, 3)}-{digitsOnly.Substring(6, 4)}";
        }

        /// <summary>
        /// Normalizes postal code (uppercase, remove spaces, add space if Canadian)
        /// </summary>
        public string? NormalizePostalCode(string? postalCode)
        {
            if (string.IsNullOrWhiteSpace(postalCode))
                return null;

            var normalized = postalCode.Trim().ToUpperInvariant().Replace(" ", "");

            // Canadian postal code format: A1A 1A1
            if (Regex.IsMatch(normalized, @"^[A-Z]\d[A-Z]\d[A-Z]\d$"))
            {
                return $"{normalized.Substring(0, 3)} {normalized.Substring(3, 3)}";
            }

            // US ZIP code format: 12345 or 12345-1234
            if (Regex.IsMatch(normalized, @"^\d{5}$") || Regex.IsMatch(normalized, @"^\d{9}$"))
            {
                return normalized.Length == 5 ? normalized : $"{normalized.Substring(0, 5)}-{normalized.Substring(5, 4)}";
            }

            return null;
        }

        /// <summary>
        /// Normalizes name (trim, proper case)
        /// </summary>
        public string? NormalizeName(string? name)
        {
            if (string.IsNullOrWhiteSpace(name))
                return null;

            var normalized = name.Trim();

            // Convert to title case while preserving certain patterns
            var textInfo = CultureInfo.CurrentCulture.TextInfo;
            normalized = textInfo.ToTitleCase(normalized.ToLowerInvariant());

            // Handle special cases like "McDonald", "O'Connor", etc.
            normalized = Regex.Replace(normalized, @"\bMc([A-Z])", m => "Mc" + m.Groups[1].Value.ToUpperInvariant());
            normalized = Regex.Replace(normalized, @"\bO'([A-Z])", m => "O'" + m.Groups[1].Value.ToUpperInvariant());

            // Handle hyphenated names
            normalized = Regex.Replace(normalized, @"-([a-z])", m => "-" + m.Groups[1].Value.ToUpperInvariant());

            return normalized;
        }

        /// <summary>
        /// Normalizes address components and combines them
        /// </summary>
        public string? NormalizeAddress(string? unitNumber, string? streetNumber, string? streetName)
        {
            var parts = new List<string>();

            // Add unit number if present
            if (!string.IsNullOrWhiteSpace(unitNumber))
            {
                parts.Add(unitNumber.Trim());
            }

            // Add street number if present
            if (!string.IsNullOrWhiteSpace(streetNumber))
            {
                parts.Add(streetNumber.Trim());
            }

            // Add street name if present
            if (!string.IsNullOrWhiteSpace(streetName))
            {
                var normalizedStreetName = NormalizeName(streetName);
                if (!string.IsNullOrEmpty(normalizedStreetName))
                {
                    parts.Add(normalizedStreetName);
                }
            }

            return parts.Count > 0 ? string.Join(" ", parts) : null;
        }

        /// <summary>
        /// Normalizes gender text to match database values
        /// </summary>
        public string? NormalizeGender(string? genderText)
        {
            if (string.IsNullOrWhiteSpace(genderText))
                return null;

            var normalized = genderText.Trim().ToLowerInvariant();

            // French mappings from CSV
            if (normalized.Contains("garçon") || normalized.Contains("homme") || normalized.Contains("male"))
                return "Male";

            if (normalized.Contains("fille") || normalized.Contains("femme") || normalized.Contains("female"))
                return "Female";

            // English mappings
            if (normalized.Contains("boy") || normalized.Contains("man"))
                return "Male";

            if (normalized.Contains("girl") || normalized.Contains("woman"))
                return "Female";

            // Direct mappings
            switch (normalized)
            {
                case "m":
                case "male":
                case "masculin":
                    return "Male";
                case "f":
                case "female":
                case "féminin":
                    return "Female";
                default:
                    return "Other"; // Default fallback
            }
        }

        /// <summary>
        /// Normalizes province code
        /// </summary>
        public string? NormalizeProvince(string? provinceText)
        {
            if (string.IsNullOrWhiteSpace(provinceText))
                return null;

            var normalized = provinceText.Trim().ToUpperInvariant();

            // Handle common Canadian provinces
            var provinceMap = new Dictionary<string, string>
            {
                { "QC", "QC" }, { "QUEBEC", "QC" }, { "QUÉBEC", "QC" },
                { "ON", "ON" }, { "ONTARIO", "ON" },
                { "BC", "BC" }, { "BRITISH COLUMBIA", "BC" },
                { "AB", "AB" }, { "ALBERTA", "AB" },
                { "MB", "MB" }, { "MANITOBA", "MB" },
                { "SK", "SK" }, { "SASKATCHEWAN", "SK" },
                { "NS", "NS" }, { "NOVA SCOTIA", "NS" },
                { "NB", "NB" }, { "NEW BRUNSWICK", "NB" },
                { "PE", "PE" }, { "PRINCE EDWARD ISLAND", "PE" },
                { "NL", "NL" }, { "NEWFOUNDLAND", "NL" }, { "NEWFOUNDLAND AND LABRADOR", "NL" },
                { "NT", "NT" }, { "NORTHWEST TERRITORIES", "NT" },
                { "NU", "NU" }, { "NUNAVUT", "NU" },
                { "YT", "YT" }, { "YUKON", "YT" }
            };

            return provinceMap.TryGetValue(normalized, out var provinceCode) ? provinceCode : null;
        }

        /// <summary>
        /// Applies all applicable normalizations to a TempMember
        /// </summary>
        public TempMember NormalizeTempMember(TempMember tempMember)
        {
            if (tempMember == null)
                throw new ArgumentNullException(nameof(tempMember));

            // Normalize basic fields
            tempMember.FirstName = NormalizeName(tempMember.FirstName) ?? tempMember.FirstName;
            tempMember.LastName = NormalizeName(tempMember.LastName) ?? tempMember.LastName;
            tempMember.Email = NormalizeEmail(tempMember.Email);
            tempMember.Phone = NormalizePhone(tempMember.Phone);
            tempMember.PostalCode = NormalizePostalCode(tempMember.PostalCode);
            tempMember.City = NormalizeName(tempMember.City);

            // Normalize lookup field texts
            tempMember.GenderText = NormalizeGender(tempMember.GenderText);
            tempMember.ProvinceText = NormalizeProvince(tempMember.ProvinceText);

            // Set default phone type if phone exists but no type specified
            if (!string.IsNullOrEmpty(tempMember.Phone) && string.IsNullOrEmpty(tempMember.PhoneTypeText))
            {
                tempMember.PhoneTypeText = "Mobile";
            }

            return tempMember;
        }
    }
}