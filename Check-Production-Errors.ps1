# Check Windows Event Log for ASP.NET Core startup errors
# Run this script ON THE SIMBA SERVER as Administrator

Write-Host "=== Checking Production Site Errors ===" -ForegroundColor Green

# Method 1: Check Windows Event Log for Application errors
Write-Host "`n📋 Checking Windows Event Log for ASP.NET Core errors..." -ForegroundColor Yellow

try {
    # Get recent Application errors related to ASP.NET Core
    $events = Get-WinEvent -FilterHashtable @{LogName='Application'; Level=2; StartTime=(Get-Date).AddHours(-2)} -ErrorAction SilentlyContinue | 
              Where-Object { $_.Message -like "*ASP.NET Core*" -or $_.Message -like "*ParaHockey*" -or $_.Message -like "*500.30*" } |
              Select-Object -First 5

    if ($events) {
        Write-Host "🔴 Found ASP.NET Core related errors:" -ForegroundColor Red
        foreach ($event in $events) {
            Write-Host "`n📅 Time: $($event.TimeCreated)" -ForegroundColor Cyan
            Write-Host "🏷️ Source: $($event.ProviderName)" -ForegroundColor Cyan  
            Write-Host "📝 Message:" -ForegroundColor Cyan
            Write-Host $event.Message -ForegroundColor White
            Write-Host "-" * 80 -ForegroundColor Gray
        }
    } else {
        Write-Host "ℹ️ No ASP.NET Core related errors found in Application log" -ForegroundColor Yellow
    }
} catch {
    Write-Warning "Could not access Windows Event Log: $_"
}

# Method 2: Check System Event Log for IIS/W3SVC errors  
Write-Host "`n📋 Checking System Event Log for IIS errors..." -ForegroundColor Yellow

try {
    $systemEvents = Get-WinEvent -FilterHashtable @{LogName='System'; Level=2; StartTime=(Get-Date).AddHours(-2)} -ErrorAction SilentlyContinue |
                    Where-Object { $_.ProviderName -like "*W3SVC*" -or $_.ProviderName -like "*IIS*" } |
                    Select-Object -First 3

    if ($systemEvents) {
        Write-Host "🔴 Found IIS related errors:" -ForegroundColor Red
        foreach ($event in $systemEvents) {
            Write-Host "`n📅 Time: $($event.TimeCreated)" -ForegroundColor Cyan
            Write-Host "🏷️ Source: $($event.ProviderName)" -ForegroundColor Cyan
            Write-Host "📝 Message:" -ForegroundColor Cyan  
            Write-Host $event.Message -ForegroundColor White
            Write-Host "-" * 80 -ForegroundColor Gray
        }
    } else {
        Write-Host "ℹ️ No IIS related errors found in System log" -ForegroundColor Yellow
    }
} catch {
    Write-Warning "Could not access System Event Log: $_"
}

# Method 3: Try to create logs directory manually and check for detailed output
Write-Host "`n📋 Attempting to create logs directory manually..." -ForegroundColor Yellow

$prodPath = "C:\inetpub\ParaHockey\Production"
$logsPath = Join-Path $prodPath "logs"

try {
    # Create logs directory as Administrator
    if (!(Test-Path $logsPath)) {
        New-Item -ItemType Directory -Path $logsPath -Force | Out-Null
        Write-Host "✅ Created logs directory: $logsPath" -ForegroundColor Green
    }
    
    # Give IIS_IUSRS full control
    $acl = Get-Acl $logsPath
    $accessRule = New-Object System.Security.AccessControl.FileSystemAccessRule("IIS_IUSRS", "FullControl", "ContainerInherit,ObjectInherit", "None", "Allow")
    $acl.SetAccessRule($accessRule)
    Set-Acl $logsPath $acl
    Write-Host "✅ Set permissions for IIS_IUSRS on logs directory" -ForegroundColor Green
    
} catch {
    Write-Warning "Could not create/configure logs directory: $_"
}

# Method 4: Test the database connection
Write-Host "`n📋 Testing database connection..." -ForegroundColor Yellow

$connectionString = "Server=SIMBA\SQLEXPRESS;User Id=ParaHockeyUser;Password=***************;Database=ParaHockeyDB;Encrypt=False;"

try {
    $connection = New-Object System.Data.SqlClient.SqlConnection($connectionString)
    $connection.Open()
    Write-Host "✅ Database connection successful" -ForegroundColor Green
    $connection.Close()
} catch {
    Write-Host "❌ Database connection failed: $_" -ForegroundColor Red
}

# Method 5: Restart IIS and try to capture startup output
Write-Host "`n📋 Restarting IIS Application Pool..." -ForegroundColor Yellow

try {
    # Use appcmd instead of PowerShell WebAdministration module
    $appcmd = "${env:windir}\system32\inetsrv\appcmd.exe"
    
    if (Test-Path $appcmd) {
        Write-Host "Stopping ParaHockey-Production app pool..." -ForegroundColor Cyan
        & $appcmd stop apppool "ParaHockey-Production" 2>&1 | Out-String | Write-Host
        
        Start-Sleep -Seconds 3
        
        Write-Host "Starting ParaHockey-Production app pool..." -ForegroundColor Cyan  
        & $appcmd start apppool "ParaHockey-Production" 2>&1 | Out-String | Write-Host
        
        Write-Host "✅ App pool restarted using appcmd" -ForegroundColor Green
    } else {
        Write-Warning "appcmd.exe not found at expected location"
    }
} catch {
    Write-Warning "Could not restart app pool: $_"
}

Write-Host "`n📋 Checking if logs are now being created..." -ForegroundColor Yellow
Start-Sleep -Seconds 5

if (Test-Path $logsPath) {
    $logFiles = Get-ChildItem $logsPath -File | Sort-Object LastWriteTime -Descending
    if ($logFiles) {
        Write-Host "✅ Found log files:" -ForegroundColor Green
        foreach ($file in $logFiles | Select-Object -First 3) {
            Write-Host "  📄 $($file.Name) - $($file.LastWriteTime)" -ForegroundColor Cyan
        }
        
        Write-Host "`n📝 Contents of latest log file:" -ForegroundColor Yellow
        $latestLog = $logFiles | Select-Object -First 1
        Get-Content $latestLog.FullName | Write-Host -ForegroundColor White
    } else {
        Write-Host "ℹ️ No log files found yet" -ForegroundColor Yellow
    }
} else {
    Write-Host "ℹ️ Logs directory still not accessible" -ForegroundColor Yellow
}

Write-Host "`n=== Summary ===" -ForegroundColor Green
Write-Host "Please try accessing the Production website now and check above for:" -ForegroundColor White
Write-Host "1. 🔴 Event log errors with specific details" -ForegroundColor White  
Write-Host "2. ❌ Database connection issues" -ForegroundColor White
Write-Host "3. 📄 New stdout log files with startup errors" -ForegroundColor White