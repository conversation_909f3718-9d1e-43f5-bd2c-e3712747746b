using Microsoft.Extensions.Configuration;
using System.ComponentModel.DataAnnotations;

namespace ParaHockeyApp.Configuration
{
    /// <summary>
    /// Configuration settings for the member import system
    /// </summary>
    public class ImportConfiguration
    {
        public const string SectionName = "Import";

        /// <summary>
        /// Maximum file size for uploads in megabytes
        /// </summary>
        [Range(1, 1000, ErrorMessage = "Maximum file size must be between 1 and 1000 MB")]
        public int MaxFileSizeMB { get; set; } = 50;

        /// <summary>
        /// Maximum number of records that can be processed in a single import
        /// </summary>
        [Range(1, 100000, ErrorMessage = "Maximum records must be between 1 and 100,000")]
        public int MaxRecordsPerImport { get; set; } = 10000;

        /// <summary>
        /// Batch size for processing records
        /// </summary>
        [Range(1, 1000, ErrorMessage = "Batch size must be between 1 and 1,000")]
        public int BatchSize { get; set; } = 100;

        /// <summary>
        /// Timeout for individual import operations in minutes
        /// </summary>
        [Range(1, 120, ErrorMessage = "Timeout must be between 1 and 120 minutes")]
        public int TimeoutMinutes { get; set; } = 30;

        /// <summary>
        /// Supported file extensions for import
        /// </summary>
        public string[] SupportedFileExtensions { get; set; } = { ".xlsx", ".xls" };

        /// <summary>
        /// Whether to allow duplicate detection and merging
        /// </summary>
        public bool EnableDuplicateDetection { get; set; } = true;

        /// <summary>
        /// Threshold for considering records as potential duplicates (0.0 to 1.0)
        /// </summary>
        [Range(0.0, 1.0, ErrorMessage = "Duplicate threshold must be between 0.0 and 1.0")]
        public double DuplicateThreshold { get; set; } = 0.8;

        /// <summary>
        /// Whether to enable detailed audit logging for imports
        /// </summary>
        public bool EnableAuditLogging { get; set; } = true;

        /// <summary>
        /// Directory path for storing temporary import files
        /// </summary>
        public string TempFileDirectory { get; set; } = Path.Combine(Path.GetTempPath(), "ParaHockey", "Imports");

        /// <summary>
        /// How long to keep temporary files in hours
        /// </summary>
        [Range(1, 72, ErrorMessage = "Temp file retention must be between 1 and 72 hours")]
        public int TempFileRetentionHours { get; set; } = 24;

        /// <summary>
        /// Whether to automatically clean up old temporary files
        /// </summary>
        public bool AutoCleanupTempFiles { get; set; } = true;

        /// <summary>
        /// Default culture for localization
        /// </summary>
        public string DefaultCulture { get; set; } = "en";

        /// <summary>
        /// Supported cultures for localization
        /// </summary>
        public string[] SupportedCultures { get; set; } = { "en", "fr" };

        /// <summary>
        /// Email notification settings
        /// </summary>
        public EmailNotificationSettings EmailNotifications { get; set; } = new EmailNotificationSettings();

        /// <summary>
        /// Performance monitoring settings
        /// </summary>
        public PerformanceSettings Performance { get; set; } = new PerformanceSettings();
    }

    /// <summary>
    /// Email notification configuration for import operations
    /// </summary>
    public class EmailNotificationSettings
    {
        /// <summary>
        /// Whether to send email notifications for import completion
        /// </summary>
        public bool Enabled { get; set; } = false;

        /// <summary>
        /// Whether to send notifications only for failed imports
        /// </summary>
        public bool OnlyOnFailure { get; set; } = true;

        /// <summary>
        /// Email addresses to notify (comma-separated)
        /// </summary>
        public string NotificationEmails { get; set; } = string.Empty;

        /// <summary>
        /// Email template for success notifications
        /// </summary>
        public string SuccessTemplate { get; set; } = "Import completed successfully. {0} records processed.";

        /// <summary>
        /// Email template for failure notifications
        /// </summary>
        public string FailureTemplate { get; set; } = "Import failed with error: {0}";
    }

    /// <summary>
    /// Performance monitoring configuration
    /// </summary>
    public class PerformanceSettings
    {
        /// <summary>
        /// Whether to enable performance monitoring
        /// </summary>
        public bool Enabled { get; set; } = true;

        /// <summary>
        /// Whether to log detailed timing information
        /// </summary>
        public bool LogDetailedTimings { get; set; } = false;

        /// <summary>
        /// Memory usage threshold for warnings (in MB)
        /// </summary>
        [Range(100, 10000, ErrorMessage = "Memory threshold must be between 100 and 10,000 MB")]
        public int MemoryThresholdMB { get; set; } = 1000;

        /// <summary>
        /// Processing time threshold for warnings (in minutes)
        /// </summary>
        [Range(1, 60, ErrorMessage = "Processing time threshold must be between 1 and 60 minutes")]
        public int ProcessingTimeThresholdMinutes { get; set; } = 10;
    }

    /// <summary>
    /// Service for validating and managing import configuration
    /// </summary>
    public interface IImportConfigurationService
    {
        /// <summary>
        /// Gets the current import configuration
        /// </summary>
        ImportConfiguration GetConfiguration();

        /// <summary>
        /// Validates the configuration and returns any validation errors
        /// </summary>
        IEnumerable<ValidationResult> ValidateConfiguration();

        /// <summary>
        /// Gets a specific configuration value with fallback to default
        /// </summary>
        T GetValue<T>(string key, T defaultValue);

        /// <summary>
        /// Checks if a file extension is supported for import
        /// </summary>
        bool IsFileExtensionSupported(string extension);

        /// <summary>
        /// Checks if a culture is supported for localization
        /// </summary>
        bool IsCultureSupported(string culture);
    }

    /// <summary>
    /// Implementation of import configuration service
    /// </summary>
    public class ImportConfigurationService : IImportConfigurationService
    {
        private readonly IConfiguration _configuration;
        private readonly ImportConfiguration _importConfig;

        public ImportConfigurationService(IConfiguration configuration)
        {
            _configuration = configuration;
            _importConfig = new ImportConfiguration();
            _configuration.GetSection(ImportConfiguration.SectionName).Bind(_importConfig);

            // Ensure temp directory exists
            if (!Directory.Exists(_importConfig.TempFileDirectory))
            {
                Directory.CreateDirectory(_importConfig.TempFileDirectory);
            }
        }

        public ImportConfiguration GetConfiguration()
        {
            return _importConfig;
        }

        public IEnumerable<ValidationResult> ValidateConfiguration()
        {
            var context = new ValidationContext(_importConfig);
            var results = new List<ValidationResult>();

            Validator.TryValidateObject(_importConfig, context, results, true);

            // Additional custom validations
            if (_importConfig.SupportedFileExtensions == null || !_importConfig.SupportedFileExtensions.Any())
            {
                results.Add(new ValidationResult("At least one supported file extension must be specified"));
            }

            if (_importConfig.SupportedCultures == null || !_importConfig.SupportedCultures.Any())
            {
                results.Add(new ValidationResult("At least one supported culture must be specified"));
            }

            if (!_importConfig.SupportedCultures.Contains(_importConfig.DefaultCulture))
            {
                results.Add(new ValidationResult("Default culture must be included in supported cultures"));
            }

            // Validate email notification settings if enabled
            if (_importConfig.EmailNotifications.Enabled)
            {
                if (string.IsNullOrWhiteSpace(_importConfig.EmailNotifications.NotificationEmails))
                {
                    results.Add(new ValidationResult("Notification emails must be specified when email notifications are enabled"));
                }
            }

            return results;
        }

        public T GetValue<T>(string key, T defaultValue)
        {
            var sectionKey = $"{ImportConfiguration.SectionName}:{key}";
            return _configuration.GetValue<T>(sectionKey, defaultValue);
        }

        public bool IsFileExtensionSupported(string extension)
        {
            if (string.IsNullOrWhiteSpace(extension))
                return false;

            return _importConfig.SupportedFileExtensions.Contains(extension.ToLowerInvariant());
        }

        public bool IsCultureSupported(string culture)
        {
            if (string.IsNullOrWhiteSpace(culture))
                return false;

            return _importConfig.SupportedCultures.Contains(culture.ToLowerInvariant());
        }
    }
}
