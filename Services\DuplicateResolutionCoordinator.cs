using Microsoft.EntityFrameworkCore;
using ParaHockeyApp.Models;
using ParaHockeyApp.Models.Entities;
using ParaHockeyApp.ViewModels;

namespace ParaHockeyApp.Services
{
    /// <summary>
    /// Coordinates complex duplicate resolution operations across multiple services
    /// </summary>
    public class DuplicateResolutionCoordinator : IDuplicateResolutionCoordinator
    {
        private readonly ApplicationContext _context;
        private readonly IDuplicateDetectionService _duplicateDetectionService;
        private readonly ITempMemberService _tempMemberService;
        private readonly IImportBatchService _importBatchService;
        private readonly IAuditLogService _auditLogService;

        public DuplicateResolutionCoordinator(
            ApplicationContext context,
            IDuplicateDetectionService duplicateDetectionService,
            ITempMemberService tempMemberService,
            IImportBatchService importBatchService,
            IAuditLogService auditLogService)
        {
            _context = context;
            _duplicateDetectionService = duplicateDetectionService;
            _tempMemberService = tempMemberService;
            _importBatchService = importBatchService;
            _auditLogService = auditLogService;
        }

        /// <summary>
        /// Resolves a duplicate by merging with existing member and updates batch statistics
        /// </summary>
        public async Task<Member> ResolveDuplicateAsync(Guid tempMemberId, Dictionary<string, string> fieldChoices, string performedBy)
        {
            using var transaction = await _context.Database.BeginTransactionAsync();
            
            try
            {
                // Get temp member to determine batch ID
                var tempMember = await _context.TempMembers
                    .FirstOrDefaultAsync(tm => tm.TempMemberId == tempMemberId);

                if (tempMember == null)
                    throw new ArgumentException($"Temp member {tempMemberId} not found");

                // Apply the merge operation
                var mergedMember = await _duplicateDetectionService.ApplyMergeAsync(tempMemberId, fieldChoices, performedBy);

                // Update batch statistics
                await _importBatchService.RefreshBatchStatisticsAsync(tempMember.ImportBatchId);

                await transaction.CommitAsync();
                return mergedMember;
            }
            catch
            {
                await transaction.RollbackAsync();
                throw;
            }
        }

        /// <summary>
        /// Rejects a duplicate and updates batch statistics
        /// </summary>
        public async Task<TempMember> RejectDuplicateAsync(Guid tempMemberId, string rejectedBy)
        {
            using var transaction = await _context.Database.BeginTransactionAsync();
            
            try
            {
                // Get temp member to determine batch ID
                var tempMember = await _context.TempMembers
                    .FirstOrDefaultAsync(tm => tm.TempMemberId == tempMemberId);

                if (tempMember == null)
                    throw new ArgumentException($"Temp member {tempMemberId} not found");

                // Reject the duplicate
                var rejectedTempMember = await _duplicateDetectionService.RejectDuplicateAsync(tempMemberId, rejectedBy);

                // Update batch statistics
                await _importBatchService.RefreshBatchStatisticsAsync(tempMember.ImportBatchId);

                await transaction.CommitAsync();
                return rejectedTempMember;
            }
            catch
            {
                await transaction.RollbackAsync();
                throw;
            }
        }

        /// <summary>
        /// Bulk rejects multiple duplicates and updates batch statistics
        /// </summary>
        public async Task<int> BulkRejectDuplicatesAsync(List<Guid> tempMemberIds, string rejectedBy)
        {
            using var transaction = await _context.Database.BeginTransactionAsync();
            
            try
            {
                // Get batch IDs that will be affected
                var batchIds = await _context.TempMembers
                    .Where(tm => tempMemberIds.Contains(tm.TempMemberId))
                    .Select(tm => tm.ImportBatchId)
                    .Distinct()
                    .ToListAsync();

                // Bulk reject duplicates
                var rejectedCount = await _duplicateDetectionService.BulkRejectDuplicatesAsync(tempMemberIds, rejectedBy);

                // Update batch statistics for all affected batches
                foreach (var batchId in batchIds)
                {
                    await _importBatchService.RefreshBatchStatisticsAsync(batchId);
                }

                await transaction.CommitAsync();
                return rejectedCount;
            }
            catch
            {
                await transaction.RollbackAsync();
                throw;
            }
        }

        /// <summary>
        /// Auto-merges duplicates that have identical field values (no conflicts)
        /// </summary>
        public async Task<int> AutoMergeIdenticalDuplicatesAsync(int batchId, string performedBy)
        {
            using var transaction = await _context.Database.BeginTransactionAsync();
            
            try
            {
                var duplicates = await _context.TempMembers
                    .Where(tm => tm.ImportBatchId == batchId && tm.Status == TempMemberStatus.Duplicate)
                    .Include(tm => tm.ExistingMember)
                    .ToListAsync();

                var mergedCount = 0;
                
                foreach (var tempMember in duplicates)
                {
                    if (tempMember.ExistingMember != null)
                    {
                        // Check if temp member and existing member have identical field values
                        var fieldComparisons = await GetFieldComparisonsAsync(tempMember.TempMemberId);
                        var hasConflicts = fieldComparisons.FieldComparisons.Values.Any(fc => !fc.AreIdentical);

                        if (!hasConflicts)
                        {
                            // Auto-merge since there are no conflicts (fields are identical)
                            var fieldChoices = fieldComparisons.FieldComparisons.Keys
                                .ToDictionary(key => key, key => "existing"); // Choose existing values since they're identical

                            await _duplicateDetectionService.ApplyMergeAsync(tempMember.TempMemberId, fieldChoices, performedBy);
                            mergedCount++;
                        }
                    }
                }

                // Update batch statistics
                await _importBatchService.RefreshBatchStatisticsAsync(batchId);

                // Log the auto-merge operation
                await _auditLogService.LogActionAsync(
                    $"Auto-merged {mergedCount} identical duplicate records in batch {batchId}",
                    null,
                    ActionSource.AdminPanel,
                    performedBy,
                    null);

                await transaction.CommitAsync();
                return mergedCount;
            }
            catch
            {
                await transaction.RollbackAsync();
                throw;
            }
        }

        /// <summary>
        /// Gets field comparisons for a duplicate temp member
        /// </summary>
        private async Task<DuplicateResolutionViewModel> GetFieldComparisonsAsync(Guid tempMemberId)
        {
            return await _duplicateDetectionService.GetDuplicateResolutionViewModelAsync(tempMemberId);
        }
    }

    /// <summary>
    /// Interface for duplicate resolution coordination operations
    /// </summary>
    public interface IDuplicateResolutionCoordinator
    {
        /// <summary>
        /// Resolves a duplicate by merging with existing member and updates batch statistics
        /// </summary>
        /// <param name="tempMemberId">ID of the temp member to resolve</param>
        /// <param name="fieldChoices">Dictionary of field choices for merge</param>
        /// <param name="performedBy">Username of the person performing the resolution</param>
        /// <returns>The merged member</returns>
        Task<Member> ResolveDuplicateAsync(Guid tempMemberId, Dictionary<string, string> fieldChoices, string performedBy);

        /// <summary>
        /// Rejects a duplicate and updates batch statistics
        /// </summary>
        /// <param name="tempMemberId">ID of the temp member to reject</param>
        /// <param name="rejectedBy">Username of the person rejecting</param>
        /// <returns>The rejected temp member</returns>
        Task<TempMember> RejectDuplicateAsync(Guid tempMemberId, string rejectedBy);

        /// <summary>
        /// Bulk rejects multiple duplicates and updates batch statistics
        /// </summary>
        /// <param name="tempMemberIds">List of temp member IDs to reject</param>
        /// <param name="rejectedBy">Username of the person rejecting</param>
        /// <returns>Number of temp members rejected</returns>
        Task<int> BulkRejectDuplicatesAsync(List<Guid> tempMemberIds, string rejectedBy);

        /// <summary>
        /// Auto-merges duplicates that have identical field values (no conflicts)
        /// </summary>
        /// <param name="batchId">Import batch ID</param>
        /// <param name="performedBy">Username of the person performing the operation</param>
        /// <returns>Number of duplicates auto-merged</returns>
        Task<int> AutoMergeIdenticalDuplicatesAsync(int batchId, string performedBy);
    }
}