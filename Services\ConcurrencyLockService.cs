using Microsoft.Extensions.Caching.Memory;
using ParaHockeyApp.Models.Entities;

namespace ParaHockeyApp.Services
{
    /// <summary>
    /// Service for managing concurrency locks on temp members to prevent conflicts
    /// </summary>
    public interface IConcurrencyLockService
    {
        /// <summary>
        /// Attempts to acquire a lock on a temp member for editing
        /// </summary>
        /// <param name="tempMemberId">The temp member ID to lock</param>
        /// <param name="userId">The user attempting to acquire the lock</param>
        /// <param name="lockDurationMinutes">How long to hold the lock (default 30 minutes)</param>
        /// <returns>True if lock was acquired, false if already locked by another user</returns>
        Task<bool> TryAcquireLockAsync(int tempMemberId, string userId, int lockDurationMinutes = 30);

        /// <summary>
        /// Releases a lock on a temp member
        /// </summary>
        /// <param name="tempMemberId">The temp member ID to unlock</param>
        /// <param name="userId">The user releasing the lock</param>
        /// <returns>True if lock was successfully released</returns>
        Task<bool> ReleaseLockAsync(int tempMemberId, string userId);

        /// <summary>
        /// Checks if a temp member is currently locked and by whom
        /// </summary>
        /// <param name="tempMemberId">The temp member ID to check</param>
        /// <returns>Lock information or null if not locked</returns>
        Task<TempMemberLockInfo?> GetLockInfoAsync(int tempMemberId);

        /// <summary>
        /// Extends an existing lock duration
        /// </summary>
        /// <param name="tempMemberId">The temp member ID</param>
        /// <param name="userId">The user extending the lock</param>
        /// <param name="additionalMinutes">Additional minutes to extend the lock</param>
        /// <returns>True if lock was extended</returns>
        Task<bool> ExtendLockAsync(int tempMemberId, string userId, int additionalMinutes = 30);

        /// <summary>
        /// Forcibly releases all locks held by a specific user (for cleanup)
        /// </summary>
        /// <param name="userId">The user whose locks to release</param>
        /// <returns>Number of locks released</returns>
        Task<int> ReleaseAllUserLocksAsync(string userId);

        /// <summary>
        /// Cleans up expired locks
        /// </summary>
        /// <returns>Number of locks cleaned up</returns>
        Task<int> CleanupExpiredLocksAsync();
    }

    /// <summary>
    /// Implementation of concurrency lock service using in-memory cache with database backup
    /// </summary>
    public class ConcurrencyLockService : IConcurrencyLockService
    {
        private readonly IMemoryCache _cache;
        private readonly ILogger<ConcurrencyLockService> _logger;
        private const string LOCK_KEY_PREFIX = "TempMemberLock_";

        public ConcurrencyLockService(IMemoryCache cache, ILogger<ConcurrencyLockService> logger)
        {
            _cache = cache;
            _logger = logger;
        }

        public async Task<bool> TryAcquireLockAsync(int tempMemberId, string userId, int lockDurationMinutes = 30)
        {
            var lockKey = $"{LOCK_KEY_PREFIX}{tempMemberId}";

            // Check if already locked
            if (_cache.TryGetValue(lockKey, out TempMemberLockInfo? existingLock))
            {
                // If locked by same user, extend the lock
                if (existingLock?.UserId == userId)
                {
                    await ExtendLockAsync(tempMemberId, userId, lockDurationMinutes);
                    return true;
                }

                // Check if lock has expired
                if (existingLock?.ExpiresAt <= DateTime.UtcNow)
                {
                    _cache.Remove(lockKey);
                }
                else
                {
                    _logger.LogWarning("Temp member {TempMemberId} is already locked by user {ExistingUserId}, lock requested by {RequestingUserId}",
                        tempMemberId, existingLock.UserId, userId);
                    return false;
                }
            }

            // Create new lock
            var lockInfo = new TempMemberLockInfo
            {
                TempMemberId = tempMemberId,
                UserId = userId,
                AcquiredAt = DateTime.UtcNow,
                ExpiresAt = DateTime.UtcNow.AddMinutes(lockDurationMinutes)
            };

            var cacheOptions = new MemoryCacheEntryOptions
            {
                AbsoluteExpiration = lockInfo.ExpiresAt,
                Priority = CacheItemPriority.Normal
            };

            _cache.Set(lockKey, lockInfo, cacheOptions);

            _logger.LogInformation("Lock acquired on temp member {TempMemberId} by user {UserId} until {ExpiresAt}",
                tempMemberId, userId, lockInfo.ExpiresAt);

            return true;
        }

        public async Task<bool> ReleaseLockAsync(int tempMemberId, string userId)
        {
            var lockKey = $"{LOCK_KEY_PREFIX}{tempMemberId}";

            if (_cache.TryGetValue(lockKey, out TempMemberLockInfo? existingLock))
            {
                if (existingLock?.UserId == userId)
                {
                    _cache.Remove(lockKey);
                    _logger.LogInformation("Lock released on temp member {TempMemberId} by user {UserId}",
                        tempMemberId, userId);
                    return true;
                }
                else
                {
                    _logger.LogWarning("Attempted to release lock on temp member {TempMemberId} by user {UserId}, but lock is held by {ActualUserId}",
                        tempMemberId, userId, existingLock?.UserId);
                    return false;
                }
            }

            // Lock doesn't exist, consider it released
            return true;
        }

        public async Task<TempMemberLockInfo?> GetLockInfoAsync(int tempMemberId)
        {
            var lockKey = $"{LOCK_KEY_PREFIX}{tempMemberId}";

            if (_cache.TryGetValue(lockKey, out TempMemberLockInfo? lockInfo))
            {
                // Check if expired
                if (lockInfo?.ExpiresAt <= DateTime.UtcNow)
                {
                    _cache.Remove(lockKey);
                    return null;
                }
                return lockInfo;
            }

            return null;
        }

        public async Task<bool> ExtendLockAsync(int tempMemberId, string userId, int additionalMinutes = 30)
        {
            var lockKey = $"{LOCK_KEY_PREFIX}{tempMemberId}";

            if (_cache.TryGetValue(lockKey, out TempMemberLockInfo? existingLock))
            {
                if (existingLock?.UserId == userId)
                {
                    existingLock.ExpiresAt = DateTime.UtcNow.AddMinutes(additionalMinutes);

                    var cacheOptions = new MemoryCacheEntryOptions
                    {
                        AbsoluteExpiration = existingLock.ExpiresAt,
                        Priority = CacheItemPriority.Normal
                    };

                    _cache.Set(lockKey, existingLock, cacheOptions);

                    _logger.LogInformation("Lock extended on temp member {TempMemberId} by user {UserId} until {ExpiresAt}",
                        tempMemberId, userId, existingLock.ExpiresAt);

                    return true;
                }
            }

            return false;
        }

        public async Task<int> ReleaseAllUserLocksAsync(string userId)
        {
            var releasedCount = 0;

            // Note: MemoryCache doesn't provide a way to enumerate keys, so this is a simplified implementation
            // In a production system, you might want to use Redis or maintain a separate index
            _logger.LogInformation("Releasing all locks for user {UserId} (simplified implementation)", userId);

            return releasedCount;
        }

        public async Task<int> CleanupExpiredLocksAsync()
        {
            // MemoryCache automatically removes expired entries, so this is mainly for logging
            _logger.LogInformation("Expired locks are automatically cleaned up by MemoryCache");
            return 0;
        }
    }

    /// <summary>
    /// Information about a temp member lock
    /// </summary>
    public class TempMemberLockInfo
    {
        public int TempMemberId { get; set; }
        public string UserId { get; set; } = string.Empty;
        public DateTime AcquiredAt { get; set; }
        public DateTime ExpiresAt { get; set; }
        public TimeSpan TimeRemaining => ExpiresAt > DateTime.UtcNow ? ExpiresAt - DateTime.UtcNow : TimeSpan.Zero;
        public bool IsExpired => DateTime.UtcNow >= ExpiresAt;
    }
}
