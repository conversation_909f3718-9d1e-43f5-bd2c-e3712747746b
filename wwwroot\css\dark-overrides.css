/* Dark Mode Overrides for ParaHockey
   These rules apply when the user has enabled a dark colour scheme in the OS/browser.
   They complement the CSS variable swaps defined in shared/variables.css by fixing
   specific Bootstrap components that still render with poor contrast by default. */

@media (prefers-color-scheme: dark) {
    /* ===== NAVBAR ===== */
    .navbar-light {
        background-color: var(--ph-bg-primary) !important;
        border-bottom: 1px solid var(--ph-border) !important;
    }
    .navbar-light .navbar-brand,
    .navbar-light .nav-link,
    .navbar-light .navbar-text {
        color: var(--ph-text-primary) !important;
    }
    .navbar-light .nav-link:hover,
    .navbar-light .nav-link:focus {
        color: var(--ph-link) !important;
    }

    /* ===== FORM & INPUTS (adjusted contrast) ===== */
    .form-control,
    .form-select,
    .ph-form-control,
    .datepicker-input {
        background-color: var(--ph-gray-200) !important; /* dark grey background */
        color: var(--ph-text-primary) !important;
        border-color: var(--ph-gray-600) !important;
    }

    .form-control::placeholder,
    .ph-form-control::placeholder,
    .datepicker-input::placeholder {
        color: var(--ph-text-secondary) !important;
        opacity: 0.9;
    }

    /* ===== DROPDOWNS ===== */
    .dropdown-menu {
        background-color: var(--ph-bg-secondary) !important;
        color: var(--ph-text-primary) !important;
        border: 1px solid var(--ph-border) !important;
    }
    .dropdown-item {
        color: var(--ph-text-primary) !important;
    }
    .dropdown-item:hover,
    .dropdown-item:focus {
        background-color: var(--ph-bg-secondary) !important;
        color: var(--ph-link) !important;
    }

    /* ===== TABLES ===== */
    .table,
    .table > :not(caption) > * > * {
        background-color: var(--ph-bg-primary) !important;
        color: var(--ph-text-primary) !important;
    }

    /* ===== CUSTOM PH FORM CONTROL ===== */
    .ph-form-control {
        background-color: var(--ph-gray-200) !important;
        color: var(--ph-text-primary) !important;
        border-color: var(--ph-gray-600) !important;
    }

    /* ===== CARD BACKGROUND ===== */
    .card,
    .card-header,
    .card-body {
        background-color: var(--ph-bg-secondary) !important;
        color: var(--ph-text-primary) !important;
        border-color: var(--ph-border) !important;
    }

    /* ===== UTILITY CLASSES OVERRIDES ===== */
    .bg-white {
        background-color: var(--ph-bg-secondary) !important;
    }

    /* Datepicker input icon & calendar */
    .datepicker-input {
        background-color: var(--ph-gray-200) !important;
        color: var(--ph-text-primary) !important;
        border-color: var(--ph-gray-600) !important;
    }

    /* ===== MODAL STYLING ===== */
    .modal-content {
        background-color: var(--ph-bg-secondary) !important;
        color: var(--ph-text-primary) !important;
        border: 1px solid var(--ph-border) !important;
    }

    .modal-header,
    .modal-footer {
        border-color: var(--ph-border) !important;
    }

    .btn-close {
        filter: invert(1) grayscale(100%) !important; /* ensure close icon visible */
    }
} 