@model TempMember
@{
    ViewData["Title"] = "Edit Temp Member";
}

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <!-- Header -->
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h2 class="mb-1">Edit Temp Member</h2>
                    <p class="text-muted mb-0">Fix validation errors and update member data</p>
                </div>
                <div>
                    <a href="@Url.Action("Details", new { id = Model.Id })" class="btn btn-outline-secondary me-2">
                        <i class="fas fa-arrow-left me-1"></i>Back to Details
                    </a>
                </div>
            </div>

            <!-- Validation Errors Summary -->
            @if (!string.IsNullOrEmpty(Model.ValidationErrorsJson))
            {
                <div class="alert alert-warning mb-4">
                    <h5 class="alert-heading">
                        <i class="fas fa-exclamation-triangle me-2"></i>Validation Issues to Fix
                    </h5>
                    <p class="mb-2">Please address the following issues:</p>
                    <div class="validation-errors">
                        @{
                            try
                            {
                                var errors = System.Text.Json.JsonSerializer.Deserialize<List<dynamic>>(Model.ValidationErrorsJson);
                                if (errors != null)
                                {
                                    <ul class="mb-0">
                                        @foreach (var error in errors)
                                        {
                                            <li>@error.ToString()</li>
                                        }
                                    </ul>
                                }
                            }
                            catch
                            {
                                <div class="text-muted">Unable to parse validation errors.</div>
                            }
                        }
                    </div>
                </div>
            }

            <!-- Edit Form -->
            <form method="post" action="@Url.Action("Edit", new { id = Model.TempMemberId })">
                @Html.AntiForgeryToken()
                
                <div class="row">
                    <!-- Basic Information -->
                    <div class="col-lg-6">
                        <div class="card mb-4">
                            <div class="card-header">
                                <h5 class="card-title mb-0">
                                    <i class="fas fa-user me-2"></i>Basic Information
                                </h5>
                            </div>
                            <div class="card-body">
                                <div class="mb-3">
                                    <label for="FirstName" class="form-label">First Name <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control" id="FirstName" name="FirstName" 
                                           value="@Model.FirstName" required maxlength="100">
                                    <span asp-validation-for="FirstName" class="text-danger"></span>
                                </div>

                                <div class="mb-3">
                                    <label for="LastName" class="form-label">Last Name <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control" id="LastName" name="LastName" 
                                           value="@Model.LastName" required maxlength="100">
                                    <span asp-validation-for="LastName" class="text-danger"></span>
                                </div>

                                <div class="mb-3">
                                    <label for="DateOfBirth" class="form-label">Date of Birth <span class="text-danger">*</span></label>
                                    <input type="date" class="form-control" id="DateOfBirth" name="DateOfBirth" 
                                           value="@Model.DateOfBirth?.ToString("yyyy-MM-dd")" required>
                                    <span asp-validation-for="DateOfBirth" class="text-danger"></span>
                                </div>

                                <div class="mb-3">
                                    <label for="GenderId" class="form-label">Gender <span class="text-danger">*</span></label>
                                    <select class="form-select" id="GenderId" name="GenderId" required>
                                        <option value="">Select Gender...</option>
                                        @if (ViewBag.Genders != null)
                                        {
                                            @foreach (var gender in (List<dynamic>)ViewBag.Genders)
                                            {
                                                <option value="@gender.Id" selected="@(gender.Id == Model.GenderId)">
                                                    @gender.DisplayNameKey
                                                </option>
                                            }
                                        }
                                    </select>
                                    <span asp-validation-for="GenderId" class="text-danger"></span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Contact Information -->
                    <div class="col-lg-6">
                        <div class="card mb-4">
                            <div class="card-header">
                                <h5 class="card-title mb-0">
                                    <i class="fas fa-envelope me-2"></i>Contact Information
                                </h5>
                            </div>
                            <div class="card-body">
                                <div class="mb-3">
                                    <label for="Email" class="form-label">Email Address <span class="text-danger">*</span></label>
                                    <input type="email" class="form-control" id="Email" name="Email" 
                                           value="@Model.Email" required maxlength="255">
                                    <span asp-validation-for="Email" class="text-danger"></span>
                                </div>

                                <div class="mb-3">
                                    <label for="Phone" class="form-label">Phone Number</label>
                                    <input type="tel" class="form-control phone-mask" id="Phone" name="Phone" 
                                           value="@Model.Phone" maxlength="20" placeholder="(*************">
                                    <span asp-validation-for="Phone" class="text-danger"></span>
                                </div>

                                <div class="mb-3">
                                    <label for="PhoneTypeId" class="form-label">Phone Type</label>
                                    <select class="form-select" id="PhoneTypeId" name="PhoneTypeId">
                                        <option value="">Select Phone Type...</option>
                                        @if (ViewBag.PhoneTypes != null)
                                        {
                                            @foreach (var phoneType in (List<dynamic>)ViewBag.PhoneTypes)
                                            {
                                                <option value="@phoneType.Id" selected="@(phoneType.Id == Model.PhoneTypeId)">
                                                    @phoneType.DisplayNameKey
                                                </option>
                                            }
                                        }
                                    </select>
                                    <span asp-validation-for="PhoneTypeId" class="text-danger"></span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <!-- Address Information -->
                    <div class="col-lg-6">
                        <div class="card mb-4">
                            <div class="card-header">
                                <h5 class="card-title mb-0">
                                    <i class="fas fa-map-marker-alt me-2"></i>Address Information
                                </h5>
                            </div>
                            <div class="card-body">
                                <div class="mb-3">
                                    <label for="Address" class="form-label">Street Address</label>
                                    <input type="text" class="form-control" id="Address" name="Address" 
                                           value="@Model.Address" maxlength="200">
                                    <span asp-validation-for="Address" class="text-danger"></span>
                                </div>

                                <div class="mb-3">
                                    <label for="City" class="form-label">City</label>
                                    <input type="text" class="form-control" id="City" name="City" 
                                           value="@Model.City" maxlength="100">
                                    <span asp-validation-for="City" class="text-danger"></span>
                                </div>

                                <div class="mb-3">
                                    <label for="ProvinceId" class="form-label">Province</label>
                                    <select class="form-select" id="ProvinceId" name="ProvinceId">
                                        <option value="">Select Province...</option>
                                        @if (ViewBag.Provinces != null)
                                        {
                                            @foreach (var province in (List<dynamic>)ViewBag.Provinces)
                                            {
                                                <option value="@province.Id" selected="@(province.Id == Model.ProvinceId)">
                                                    @province.DisplayNameKey
                                                </option>
                                            }
                                        }
                                    </select>
                                    <span asp-validation-for="ProvinceId" class="text-danger"></span>
                                </div>

                                <div class="mb-3">
                                    <label for="PostalCode" class="form-label">Postal Code</label>
                                    <input type="text" class="form-control postal-code-mask" id="PostalCode" name="PostalCode" 
                                           value="@Model.PostalCode" maxlength="10" placeholder="L0L 0L0">
                                    <span asp-validation-for="PostalCode" class="text-danger"></span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Registration Information -->
                    <div class="col-lg-6">
                        <div class="card mb-4">
                            <div class="card-header">
                                <h5 class="card-title mb-0">
                                    <i class="fas fa-id-card me-2"></i>Registration Information
                                </h5>
                            </div>
                            <div class="card-body">
                                <div class="mb-3">
                                    <label for="RegistrationTypeId" class="form-label">Registration Type</label>
                                    <select class="form-select" id="RegistrationTypeId" name="RegistrationTypeId">
                                        <option value="">Select Registration Type...</option>
                                        @if (ViewBag.RegistrationTypes != null)
                                        {
                                            @foreach (var regType in (List<dynamic>)ViewBag.RegistrationTypes)
                                            {
                                                <option value="@regType.Id" selected="@(regType.Id == Model.RegistrationTypeId)">
                                                    @regType.DisplayNameKey
                                                </option>
                                            }
                                        }
                                    </select>
                                    <span asp-validation-for="RegistrationTypeId" class="text-danger"></span>
                                </div>

                                <div class="mb-3">
                                    <label for="HcrNumber" class="form-label">HCR Number</label>
                                    <input type="text" class="form-control" id="HcrNumber" name="HcrNumber" 
                                           value="@Model.HcrNumber" maxlength="50">
                                    <span asp-validation-for="HcrNumber" class="text-danger"></span>
                                </div>

                                <!-- Note: Health Card and SIN are not stored in TempMember entity -->
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Form Actions -->
                <div class="row">
                    <div class="col-12">
                        <div class="card">
                            <div class="card-body">
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <a href="@Url.Action("Details", new { id = Model.TempMemberId })" class="btn btn-outline-secondary">
                                            <i class="fas fa-times me-1"></i>Cancel
                                        </a>
                                    </div>
                                    <div>
                                        <button type="submit" class="btn btn-primary me-2">
                                            <i class="fas fa-save me-1"></i>Save Changes
                                        </button>
                                        <a href="@Url.Action("Register", "Members", new { tempMemberId = Model.TempMemberId })" class="btn btn-success">
                                            <i class="fas fa-user-plus me-1"></i>Create Member
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>

@section Scripts {
    <script src="~/lib/jquery-mask-plugin/dist/jquery.mask.min.js"></script>
    <script>
        $(document).ready(function() {
            // Apply input masks
            $('.phone-mask').mask('(*************');
            $('.postal-code-mask').mask('L0L 0L0', {
                'translation': {
                    'L': { pattern: /[A-Za-z]/, optional: false },
                    '0': { pattern: /[0-9]/, optional: false }
                }
            });
            $('.sin-mask').mask('000-000-000');

            // Convert postal code to uppercase
            $('.postal-code-mask').on('input', function() {
                this.value = this.value.toUpperCase();
            });

            // Form validation feedback
            $('form').on('submit', function(e) {
                const form = this;
                
                // Clear previous validation states
                $('.form-control').removeClass('is-valid is-invalid');
                
                // Validate required fields
                let isValid = true;
                $('[required]').each(function() {
                    if (!this.value.trim()) {
                        $(this).addClass('is-invalid');
                        isValid = false;
                    } else {
                        $(this).addClass('is-valid');
                    }
                });

                // Validate email format
                const emailField = $('#Email');
                if (emailField.val() && !isValidEmail(emailField.val())) {
                    emailField.addClass('is-invalid');
                    isValid = false;
                } else if (emailField.val()) {
                    emailField.addClass('is-valid');
                }

                if (!isValid) {
                    e.preventDefault();
                    $('html, body').animate({
                        scrollTop: $('.is-invalid').first().offset().top - 100
                    }, 500);
                }
            });

            function isValidEmail(email) {
                return true; // Simplified validation - browser will handle basic email validation
            }

            // Real-time validation feedback
            $('[required]').on('blur', function() {
                if (this.value.trim()) {
                    $(this).removeClass('is-invalid').addClass('is-valid');
                } else {
                    $(this).removeClass('is-valid').addClass('is-invalid');
                }
            });

            $('#Email').on('blur', function() {
                if (this.value && isValidEmail(this.value)) {
                    $(this).removeClass('is-invalid').addClass('is-valid');
                } else if (this.value) {
                    $(this).removeClass('is-valid').addClass('is-invalid');
                }
            });
        });
    </script>
}