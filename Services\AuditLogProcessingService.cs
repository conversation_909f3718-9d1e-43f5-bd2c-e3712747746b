using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Localization;
using ParaHockeyApp.Models;
using ParaHockeyApp.Models.Entities;
using ParaHockeyApp.Models.ViewModels;
using ParaHockeyApp.Resources;
using System.Text.Json;

namespace ParaHockeyApp.Services
{
    /// <summary>
    /// Service responsible for processing and consolidating raw audit logs into user-friendly view models
    /// </summary>
    public class AuditLogProcessingService : IAuditLogProcessingService
    {
        private readonly ApplicationContext _context;
        private readonly IStringLocalizer<SharedResourceMarker> _localizer;

        public AuditLogProcessingService(ApplicationContext context, IStringLocalizer<SharedResourceMarker> localizer)
        {
            _context = context;
            _localizer = localizer;
        }

        /// <summary>
        /// Processes a list of raw audit logs and consolidates them into user-friendly view models
        /// </summary>
        public async Task<List<AuditLogViewModel>> ProcessAuditLogsAsync(List<AuditLog> rawLogs)
        {
            var viewModels = new List<AuditLogViewModel>();

            // Group logs by timestamp window and user to identify related changes in the same transaction
            // Use a 1-second window to catch all related changes that happen in the same web request
            var transactionGroups = rawLogs
                .GroupBy(log => new { 
                    TimeWindow = new DateTime(log.Timestamp.Ticks / TimeSpan.TicksPerSecond * TimeSpan.TicksPerSecond), 
                    log.PerformedByAdminId, 
                    log.PerformedByMemberId 
                })
                .OrderByDescending(group => group.Key.TimeWindow)
                .ToList();

            foreach (var transactionGroup in transactionGroups)
            {
                var processedViewModel = await ProcessTransactionGroupAsync(transactionGroup.ToList());
                if (processedViewModel != null)
                {
                    viewModels.Add(processedViewModel);
                }
            }

            return viewModels;
        }

        /// <summary>
        /// Processes a group of audit logs that occurred in the same transaction
        /// </summary>
        private async Task<AuditLogViewModel?> ProcessTransactionGroupAsync(List<AuditLog> transactionLogs)
        {
            if (!transactionLogs.Any()) return null;

            var firstLog = transactionLogs.First();
            var performedBy = await GetPerformedByTextAsync(firstLog);

            // Check for Member creation (consolidate with Parent/EmergencyContact creation)
            var memberCreationLog = transactionLogs.FirstOrDefault(log => log.EntityType == "Member" && log.Action == "Create");
            if (memberCreationLog != null)
            {
                return await ProcessMemberCreationAsync(memberCreationLog, transactionLogs);
            }

            // Check for Member-related edits (Parent or EmergencyContact updates)
            var memberRelatedEdits = transactionLogs.Where(log => 
                (log.EntityType == "Parent" || log.EntityType == "EmergencyContact") && 
                log.Action == "Update").ToList();
            
            if (memberRelatedEdits.Any())
            {
                return await ProcessMemberRelatedEditsAsync(memberRelatedEdits, transactionLogs);
            }

            // Check for Event operations
            var eventLogs = transactionLogs.Where(log => log.EntityType == "Event").ToList();
            if (eventLogs.Any())
            {
                return await ProcessEventLogsAsync(eventLogs.First(), transactionLogs);
            }

            // Check for Member updates (direct member changes)
            var memberUpdateLog = transactionLogs.FirstOrDefault(log => log.EntityType == "Member" && log.Action == "Update");
            if (memberUpdateLog != null)
            {
                return await ProcessMemberUpdateAsync(memberUpdateLog, transactionLogs);
            }

            // Fallback for other entity types
            return await ProcessGenericEntityAsync(transactionLogs.First(), transactionLogs);
        }

        /// <summary>
        /// Processes member creation, consolidating with parent/emergency contact creation
        /// </summary>
        private async Task<AuditLogViewModel> ProcessMemberCreationAsync(AuditLog memberCreationLog, List<AuditLog> transactionLogs)
        {
            var memberName = await GetMemberNameFromAuditLogAsync(memberCreationLog);
            var description = $"{_localizer["NewMember"]}: {memberName}";
            
            // For member creation, try to get the actual member ID from the database
            var actualMemberId = await GetActualMemberIdFromAuditLogAsync(memberCreationLog);
            var linkUrl = actualMemberId > 0 
                ? $"/Admin/MemberDetails/{actualMemberId}" 
                : "/Admin/Members"; // Fallback to members list
            
            var performedBy = await GetPerformedByTextAsync(memberCreationLog);

            return new AuditLogViewModel
            {
                Timestamp = memberCreationLog.Timestamp,
                PerformedBy = performedBy,
                Description = description,
                LinkUrl = linkUrl,
                ConsolidatedAuditLogIds = transactionLogs.Select(log => log.Id).ToList()
            };
        }

        /// <summary>
        /// Processes member-related edits (Parent or EmergencyContact changes)
        /// </summary>
        private async Task<AuditLogViewModel> ProcessMemberRelatedEditsAsync(List<AuditLog> memberRelatedEdits, List<AuditLog> transactionLogs)
        {
            var firstEdit = memberRelatedEdits.First();
            var memberId = await GetMemberIdFromRelatedEntityAsync(firstEdit.EntityType, firstEdit.EntityId);
            var memberName = await GetMemberNameAsync(memberId);
            var performedBy = await GetPerformedByTextAsync(firstEdit);

            var changeDescriptions = new List<string>();

            foreach (var edit in memberRelatedEdits)
            {
                if (!string.IsNullOrEmpty(edit.Description))
                {
                    var entityTypeDisplay = GetEntityTypeDisplayName(edit.EntityType);
                    changeDescriptions.Add($"{entityTypeDisplay}: {edit.Description}");
                }
            }

            var description = changeDescriptions.Any() 
                ? $"{_localizer["Member"]} {memberName} {_localizer["edited"]}: {string.Join(", ", changeDescriptions)}"
                : $"{_localizer["Member"]} {memberName} {_localizer["edited"]}";

            var linkUrl = $"/Admin/MemberDetails/{memberId}";

            return new AuditLogViewModel
            {
                Timestamp = firstEdit.Timestamp,
                PerformedBy = performedBy,
                Description = description,
                LinkUrl = linkUrl,
                ConsolidatedAuditLogIds = transactionLogs.Select(log => log.Id).ToList()
            };
        }

        /// <summary>
        /// Processes event-related logs
        /// </summary>
        private async Task<AuditLogViewModel> ProcessEventLogsAsync(AuditLog eventLog, List<AuditLog> transactionLogs)
        {
            var eventName = await GetEventNameAsync(eventLog.EntityId);
            var performedBy = await GetPerformedByTextAsync(eventLog);

            string description;
            switch (eventLog.Action)
            {
                case "Create":
                    var eventDetails = await GetEventDetailsAsync(eventLog.EntityId);
                    description = eventDetails.HasValue 
                        ? $"{_localizer["Event"]}: {eventName} ({_localizer["Start"]}: {eventDetails.Value.StartDate:yyyy-MM-dd HH:mm}, {_localizer["End"]}: {eventDetails.Value.EndDate:yyyy-MM-dd HH:mm})"
                        : $"{_localizer["Event"]}: {eventName}";
                    break;
                case "Update":
                    var changes = !string.IsNullOrEmpty(eventLog.Description) ? $": {eventLog.Description}" : "";
                    description = $"{_localizer["Event"]}: {eventName} {_localizer["edited"]}{changes}";
                    break;
                case "Delete":
                    description = $"{_localizer["Event"]}: {eventName} {_localizer["deleted"]}";
                    break;
                default:
                    description = $"{_localizer["Event"]}: {eventName} - {eventLog.Action}";
                    break;
            }

            var eventDetails2 = await GetEventDetailsAsync(eventLog.EntityId);
            var linkUrl = eventDetails2.HasValue 
                ? $"/Admin/Calendar?year={eventDetails2.Value.StartDate.Year}&month={eventDetails2.Value.StartDate.Month}"
                : "/Admin/Calendar";

            return new AuditLogViewModel
            {
                Timestamp = eventLog.Timestamp,
                PerformedBy = performedBy,
                Description = description,
                LinkUrl = linkUrl,
                ConsolidatedAuditLogIds = transactionLogs.Select(log => log.Id).ToList()
            };
        }

        /// <summary>
        /// Processes direct member updates
        /// </summary>
        private async Task<AuditLogViewModel> ProcessMemberUpdateAsync(AuditLog memberUpdateLog, List<AuditLog> transactionLogs)
        {
            var memberName = await GetMemberNameAsync(memberUpdateLog.EntityId);
            var performedBy = await GetPerformedByTextAsync(memberUpdateLog);

            var changes = !string.IsNullOrEmpty(memberUpdateLog.Description) ? $": {memberUpdateLog.Description}" : "";
            var description = $"{_localizer["Member"]} {memberName} {_localizer["edited"]}{changes}";
            var linkUrl = $"/Admin/MemberDetails/{memberUpdateLog.EntityId}";

            return new AuditLogViewModel
            {
                Timestamp = memberUpdateLog.Timestamp,
                PerformedBy = performedBy,
                Description = description,
                LinkUrl = linkUrl,
                ConsolidatedAuditLogIds = transactionLogs.Select(log => log.Id).ToList()
            };
        }

        /// <summary>
        /// Processes generic entity changes
        /// </summary>
        private async Task<AuditLogViewModel> ProcessGenericEntityAsync(AuditLog auditLog, List<AuditLog> transactionLogs)
        {
            var entityDisplayName = await GetEntityDisplayNameAsync(auditLog.EntityType, auditLog.EntityId);
            var entityTypeDisplayName = GetEntityTypeDisplayName(auditLog.EntityType);
            var performedBy = await GetPerformedByTextAsync(auditLog);

            string description;
            switch (auditLog.Action)
            {
                case "Create":
                    description = $"New {entityTypeDisplayName}: {entityDisplayName}";
                    break;
                case "Update":
                    var changes = !string.IsNullOrEmpty(auditLog.Description) ? $": {auditLog.Description}" : "";
                    description = $"{entityTypeDisplayName} {entityDisplayName} edited{changes}";
                    break;
                case "Delete":
                    description = $"{entityTypeDisplayName} {entityDisplayName} deleted";
                    break;
                default:
                    description = $"{auditLog.Action} performed on {entityTypeDisplayName} {entityDisplayName}";
                    break;
            }

            var linkUrl = GenerateGenericLinkUrl(auditLog.EntityType, auditLog.EntityId);

            return new AuditLogViewModel
            {
                Timestamp = auditLog.Timestamp,
                PerformedBy = performedBy,
                Description = description,
                LinkUrl = linkUrl,
                ConsolidatedAuditLogIds = transactionLogs.Select(log => log.Id).ToList()
            };
        }

        /// <summary>
        /// Helper methods for data retrieval and formatting
        /// </summary>
        private async Task<int> GetActualMemberIdFromAuditLogAsync(AuditLog auditLog)
        {
            if (auditLog.EntityType == "Member" && auditLog.Action == "Create" && !string.IsNullOrEmpty(auditLog.NewValues))
            {
                try
                {
                    var newValues = JsonSerializer.Deserialize<Dictionary<string, object>>(auditLog.NewValues);
                    if (newValues != null && 
                        newValues.TryGetValue("FirstName", out var firstNameObj) && 
                        newValues.TryGetValue("LastName", out var lastNameObj) &&
                        newValues.TryGetValue("Email", out var emailObj))
                    {
                        var firstName = firstNameObj?.ToString() ?? "";
                        var lastName = lastNameObj?.ToString() ?? "";
                        var email = emailObj?.ToString() ?? "";
                        
                        // Try to find the member by name and email (created around the same time)
                        var member = await _context.Members
                            .Where(m => m.FirstName == firstName && 
                                       m.LastName == lastName && 
                                       m.Email == email &&
                                       Math.Abs(EF.Functions.DateDiffSecond(m.DateCreated, auditLog.Timestamp)) < 5)
                            .FirstOrDefaultAsync();
                        
                        return member?.Id ?? 0;
                    }
                }
                catch (JsonException)
                {
                    // Fall back to using the EntityId if JSON parsing fails
                }
            }
            
            // Return the EntityId if it's positive, otherwise 0
            return auditLog.EntityId > 0 ? auditLog.EntityId : 0;
        }

        private async Task<string> GetMemberNameFromAuditLogAsync(AuditLog auditLog)
        {
            if (auditLog.EntityType == "Member" && auditLog.Action == "Create" && !string.IsNullOrEmpty(auditLog.NewValues))
            {
                try
                {
                    var newValues = JsonSerializer.Deserialize<Dictionary<string, object>>(auditLog.NewValues);
                    if (newValues != null && 
                        newValues.TryGetValue("FirstName", out var firstNameObj) && 
                        newValues.TryGetValue("LastName", out var lastNameObj))
                    {
                        var firstName = firstNameObj?.ToString() ?? "";
                        var lastName = lastNameObj?.ToString() ?? "";
                        return $"{firstName} {lastName}".Trim();
                    }
                }
                catch (JsonException)
                {
                    // Fall back to database lookup if JSON parsing fails
                }
            }
            
            // Fallback to database lookup
            return await GetMemberNameAsync(auditLog.EntityId);
        }

        private async Task<string> GetMemberNameAsync(int memberId)
        {
            try
            {
                var member = await _context.Members
                    .Where(m => m.Id == memberId)
                    .Select(m => new { m.FirstName, m.LastName })
                    .FirstOrDefaultAsync();
                return member != null ? $"{member.FirstName} {member.LastName}" : $"Member #{memberId}";
            }
            catch
            {
                return $"Member #{memberId}";
            }
        }

        private async Task<int> GetMemberIdFromRelatedEntityAsync(string entityType, int entityId)
        {
            try
            {
                switch (entityType)
                {
                    case "Parent":
                        var parent = await _context.Parents
                            .Where(p => p.Id == entityId)
                            .Select(p => p.MemberId)
                            .FirstOrDefaultAsync();
                        return parent;
                    case "EmergencyContact":
                        var emergencyContact = await _context.EmergencyContacts
                            .Where(ec => ec.Id == entityId)
                            .Select(ec => ec.MemberId)
                            .FirstOrDefaultAsync();
                        return emergencyContact;
                    default:
                        return 0;
                }
            }
            catch
            {
                return 0;
            }
        }

        private async Task<string> GetEventNameAsync(int eventId)
        {
            try
            {
                var eventName = await _context.Events
                    .Where(e => e.Id == eventId)
                    .Select(e => e.Title)
                    .FirstOrDefaultAsync();
                return eventName ?? $"Event #{eventId}";
            }
            catch
            {
                return $"Event #{eventId}";
            }
        }

        private async Task<(DateTime StartDate, DateTime EndDate)?> GetEventDetailsAsync(int eventId)
        {
            try
            {
                var eventDetails = await _context.Events
                    .Where(e => e.Id == eventId)
                    .Select(e => new { e.StartDate, e.EndDate })
                    .FirstOrDefaultAsync();
                return eventDetails != null ? (eventDetails.StartDate, eventDetails.EndDate) : null;
            }
            catch
            {
                return null;
            }
        }

        private async Task<string> GetEntityDisplayNameAsync(string entityType, int entityId)
        {
            try
            {
                switch (entityType)
                {
                    case "Member":
                        return await GetMemberNameAsync(entityId);
                    case "Parent":
                        var parent = await _context.Parents
                            .Where(p => p.Id == entityId)
                            .Select(p => new { p.FirstName, p.LastName })
                            .FirstOrDefaultAsync();
                        return parent != null ? $"{parent.FirstName} {parent.LastName}" : $"Parent #{entityId}";
                    case "EmergencyContact":
                        var emergencyContact = await _context.EmergencyContacts
                            .Where(ec => ec.Id == entityId)
                            .Select(ec => new { ec.FirstName, ec.LastName })
                            .FirstOrDefaultAsync();
                        return emergencyContact != null ? $"{emergencyContact.FirstName} {emergencyContact.LastName}" : $"Emergency Contact #{entityId}";
                    case "AdminUser":
                        var admin = await _context.AdminUsers
                            .Where(au => au.Id == entityId)
                            .Select(au => au.Name)
                            .FirstOrDefaultAsync();
                        return admin ?? $"Admin #{entityId}";
                    case "Event":
                        return await GetEventNameAsync(entityId);
                    default:
                        return $"#{entityId}";
                }
            }
            catch
            {
                return $"#{entityId}";
            }
        }

        private string GetEntityTypeDisplayName(string entityType)
        {
            return entityType switch
            {
                "Member" => "Member",
                "Parent" => "Parent",
                "EmergencyContact" => "Emergency Contact",
                "AdminUser" => "Admin User",
                "Event" => "Event",
                "EventCategory" => "Event Category",
                _ => entityType
            };
        }

        private async Task<string> GetPerformedByTextAsync(AuditLog auditLog)
        {
            // For admin actions, show "Admin: [email]" format
            if (auditLog.PerformedBySource == ActionSource.AdminPanel && !string.IsNullOrEmpty(auditLog.PerformerName))
            {
                return $"Admin: {auditLog.PerformerName}";
            }

            // For member actions, show just the member name
            if (auditLog.PerformedBySource == ActionSource.SelfService && !string.IsNullOrEmpty(auditLog.PerformerName))
            {
                return auditLog.PerformerName;
            }

            // For member creation (whether self-service or system), try to get the member name
            if (auditLog.EntityType == "Member" && auditLog.Action == "Create")
            {
                var memberName = await GetMemberNameFromAuditLogAsync(auditLog);
                if (!string.IsNullOrEmpty(memberName) && memberName != $"Member #{auditLog.EntityId}")
                {
                    return memberName;
                }
            }

            // For member-related updates where performer name is empty, show the member name
            if (auditLog.EntityType == "Member" && auditLog.Action == "Update" && string.IsNullOrEmpty(auditLog.PerformerName))
            {
                var memberName = await GetMemberNameAsync(auditLog.EntityId);
                if (!string.IsNullOrEmpty(memberName) && memberName != $"Member #{auditLog.EntityId}")
                {
                    return memberName;
                }
            }

            // Return the actual performer name if available, otherwise fall back to source type
            if (!string.IsNullOrEmpty(auditLog.PerformerName))
            {
                return auditLog.PerformerName;
            }

            return auditLog.PerformedBySource switch
            {
                ActionSource.AdminPanel => "Admin",
                ActionSource.SelfService => "Member",
                ActionSource.System => "System",
                _ => "Unknown"
            };
        }

        private string GenerateGenericLinkUrl(string entityType, int entityId)
        {
            return entityType switch
            {
                "Member" => $"/Admin/MemberDetails/{entityId}",
                "AdminUser" => "/Admin/AdminUsers",
                "Event" => "/Admin/Calendar",
                _ => "/Admin"
            };
        }
    }
}