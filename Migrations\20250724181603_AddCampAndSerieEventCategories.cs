﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace ParaHockeyApp.Migrations
{
    /// <inheritdoc />
    public partial class AddCampAndSerieEventCategories : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            // Defensive check: Ensure EventCategories table exists before seeding
            migrationBuilder.Sql(@"
                IF EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'EventCategories')
                BEGIN
                    -- Add Camp category if it doesn't exist
                    IF NOT EXISTS (SELECT 1 FROM EventCategories WHERE Id = 9)
                    BEGIN
                        SET IDENTITY_INSERT EventCategories ON
                        INSERT INTO EventCategories (Id, DisplayNameKey, DescriptionKey, Color, IconClass, DisplayOrder, RequiresRegistration, MaxParticipants, DateCreated, IsActive, CreatedBySource)
                        VALUES (9, 'EventCategory_Camp', 'EventCategory_Camp_Desc', '#20c997', 'fas fa-campground', 9, 1, -1, GETUTCDATE(), 1, 0)
                        SET IDENTITY_INSERT EventCategories OFF
                    END

                    -- Add Série category if it doesn't exist
                    IF NOT EXISTS (SELECT 1 FROM EventCategories WHERE Id = 10)
                    BEGIN
                        SET IDENTITY_INSERT EventCategories ON
                        INSERT INTO EventCategories (Id, DisplayNameKey, DescriptionKey, Color, IconClass, DisplayOrder, RequiresRegistration, MaxParticipants, DateCreated, IsActive, CreatedBySource)
                        VALUES (10, 'EventCategory_Serie', 'EventCategory_Serie_Desc', '#6610f2', 'fas fa-stream', 10, 0, -1, GETUTCDATE(), 1, 0)
                        SET IDENTITY_INSERT EventCategories OFF
                    END

                    -- Update existing events with 'Tentatif' in title to use Tentative category
                    UPDATE Events 
                    SET EventCategoryId = (SELECT Id FROM EventCategories WHERE DisplayNameKey = 'EventCategory_Tentative')
                    WHERE (Title LIKE '%Tentatif%' OR Title LIKE '%tentatif%')
                      AND EventCategoryId = (SELECT Id FROM EventCategories WHERE DisplayNameKey = 'EventCategory_Other')

                    -- Update existing events with 'Défi Sportif' in title to use Tournament category
                    UPDATE Events 
                    SET EventCategoryId = (SELECT Id FROM EventCategories WHERE DisplayNameKey = 'EventCategory_Tournament')
                    WHERE (Title LIKE '%Défi Sportif%' OR Title LIKE '%défi sportif%')
                      AND EventCategoryId = (SELECT Id FROM EventCategories WHERE DisplayNameKey = 'EventCategory_Other')

                    PRINT 'Successfully added Camp, Série, and Tentatif event categories and updated existing events'
                END
                ELSE
                BEGIN
                    RAISERROR('EventCategories table does not exist. Ensure AddEventSystem migration runs first.', 16, 1)
                END
            ");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            // Revert events back to Other category before removing new categories
            migrationBuilder.Sql(@"
                IF EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'EventCategories')
                BEGIN
                    -- Revert Tentatif events back to Other category
                    UPDATE Events 
                    SET EventCategoryId = (SELECT Id FROM EventCategories WHERE DisplayNameKey = 'EventCategory_Other')
                    WHERE EventCategoryId = (SELECT Id FROM EventCategories WHERE DisplayNameKey = 'EventCategory_Tentative')
                      AND (Title LIKE '%Tentatif%' OR Title LIKE '%tentatif%')

                    -- Revert any events that might have been assigned to Camp or Série
                    UPDATE Events 
                    SET EventCategoryId = (SELECT Id FROM EventCategories WHERE DisplayNameKey = 'EventCategory_Other')
                    WHERE EventCategoryId IN (9, 10)

                    -- Remove the new categories
                    DELETE FROM EventCategories WHERE Id IN (9, 10)
                    
                    PRINT 'Removed Camp, Série, and Tentatif event categories and reverted events'
                END
                ELSE
                BEGIN
                    PRINT 'EventCategories table does not exist - nothing to remove'
                END
            ");
        }
    }
}
