# Design Document

## 1 Overview
The goal is to ensure the Azure DevOps pipeline can reliably decide whether the ParaHockey site is running after each deployment, without being tripped up by self-signed TLS certificates or hard-coded ports.  The fix is split into two parts:

1. **Application change** – expose a lightweight unauthenticated health-check endpoint (`/health`) that always returns `200 OK` once the app’s DI container, DB connection and migrations are ready.
2. **Pipeline change** – update the **Verify Test Application is Running** PowerShell step so it hits the `/health` endpoint, ignores TLS warnings, and uses URLs passed in as variables instead of hard-coded addresses.

## 2 Architecture & File Changes

### 2.1 Add Health-check endpoint
| File | Change |
|------|--------|
| `Program.cs` | Add `app.MapHealthChecks("/health");` right before `app.Run();` |
| `ParaHockeyApp.csproj` | Add package `<PackageReference Include="AspNetCore.HealthChecks.SqlServer" Version="7.*" />` so DB is included in the probe |

*Implementation notes*
- We already register EF Core and run `context.Database.Migrate()` at startup. The built-in health-check middleware will return `200` once the DB connection succeeds.
- No authentication is applied to `/health` so the pipeline can call it anonymously. In Prod we rely on firewall rules to block public access.

### 2.2 Pipeline variables
Add two new variables in `azure-pipelines.yml` so stages can reuse the same script:
```yaml
variables:
  testBaseUrl:  'https://parahockeytest.complys.com'
  prodBaseUrl:  'https://parahockey.complys.com'
```

### 2.3 Replace legacy health-check script
Remove the block that loops over `http://localhost:8080` and hard-coded URLs.  Replace with:
```yaml
- powershell: |
    param(
      [string]$BaseUrl,
      [int]$Retries = 6,
      [int]$Delay   = 5
    )
    $url = "$BaseUrl/health"
    Write-Host "🔍 Checking $url"
    for ($i=1; $i -le $Retries; $i++) {
      try {
        Invoke-WebRequest -Uri $url -UseBasicParsing -SkipCertificateCheck | Out-Null
        Write-Host "✅ Health check passed"
        exit 0
      } catch {
        Write-Warning "Attempt $i failed: $($_.Exception.Message)"
        Start-Sleep -Seconds $Delay
      }
    }
    throw "Health endpoint failed after $Retries attempts"
  displayName: 'Verify Test site is running'
  failOnStderr: true
  arguments: -BaseUrl $(testBaseUrl)
```
(The same script is reused in Prod with `-BaseUrl $(prodBaseUrl)`.)

### 2.4 Remove port 8080 usage
The new variables point to the canonical host names and IIS bindings, so port 8080 delete/copy logic is unaffected.

## 3 Testing Strategy
1. **Local test** – run the site in Dev and browse to `http://localhost:7285/health` (whatever Kestrel port) → expect `Healthy` JSON.
2. **Pipeline dry-run** – trigger a Test-only pipeline run; ensure the health step prints `✅ Health check passed`.
3. **TLS trust** – confirm the agent ignores the self-signed cert warnings; if not, import the cert into the agent machine store.
4. **Failure simulation** – stop the IIS Test site manually and re-run the health script; it must fail and abort the stage.

## 4 Risks & Mitigations
| Risk | Mitigation |
|------|------------|
| `/health` exposes info | Use default HealthChecks UI JSON (no stack traces); firewall blocks external traffic |
| Agent still fails on TLS | Use `-SkipCertificateCheck`; optional cert import step |
| Hard-coded URLs reappear | Script relies on pipeline variables; add PR check to enforce |

## 5 Timeline
1. Add health-check code + package (30 min)
2. Update `azure-pipelines.yml` variables + script (15 min)
3. Commit & run pipeline (10 min)
4. Verify in Test, then Prod (10 min)

---
End of Design v0.1 