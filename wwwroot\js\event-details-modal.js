// Enhanced Event Details Modal functionality
(function() {
    'use strict';

    // Calendar-specific modal configurations (T5.1)
    const CalendarModalConfigs = {
        'admin': {
            name: 'Admin Calendar',
            detailsUrl: '/Home/GetPublicEventDetails',
            joinUrl: '/Events/Join/',
            leaveUrl: '/Events/Leave/',
            joinAjaxUrl: '/Events/JoinAjax',
            leaveAjaxUrl: '/Events/LeaveAjax',
            showRegistrationButtons: false, // Ad<PERSON> manage events but don't register
            emphasizeManagement: true,
            allowEventEditing: true,
            showAllEvents: true // Including unpublished
        },
        'subscribe': {
            name: 'Subscribe Calendar',
            detailsUrl: '/Home/GetPublicEventDetails',
            joinUrl: '/Events/Join/',
            leaveUrl: '/Events/Leave/',
            joinAjaxUrl: '/Events/JoinAjax',
            leaveAjaxUrl: '/Events/LeaveAjax',
            showRegistrationButtons: true,
            emphasizeRegistration: true,
            allowEventEditing: false,
            showAllEvents: false // Only published events
        },
        'public': {
            name: 'Public Calendar',
            detailsUrl: '/Home/GetPublicEventDetails',
            joinUrl: '/Events/Join/',
            leaveUrl: '/Events/Leave/',
            joinAjaxUrl: '/Events/JoinAjax',
            leaveAjaxUrl: '/Events/LeaveAjax',
            showRegistrationButtons: true,
            emphasizeVisitorConversion: true,
            allowEventEditing: false,
            showAllEvents: false // Only published events
        },
        'members': {
            name: 'Members Calendar',
            detailsUrl: '/Home/GetPublicEventDetails',
            joinUrl: '/Events/Join/',
            leaveUrl: '/Events/Leave/',
            joinAjaxUrl: '/Events/JoinAjax',
            leaveAjaxUrl: '/Events/LeaveAjax',
            showRegistrationButtons: true,
            emphasizeReadOnlyStatus: true,
            allowEventEditing: false,
            showAllEvents: false // Only published events
        }
    };

    // Default modal configuration (backward compatibility)
    const modalConfig = {
        detailsUrl: '/Home/GetPublicEventDetails',
        joinUrl: '/Events/Join/',
        leaveUrl: '/Events/Leave/',
        joinAjaxUrl: '/Events/JoinAjax',
        leaveAjaxUrl: '/Events/LeaveAjax',
        locale: document.documentElement.lang || 'fr-CA',
        translations: {
            'fr-CA': {
                allDay: 'Toute la journée',
                locationTBA: 'Lieu à confirmer',
                loading: 'Chargement...',
                errorLoading: 'Erreur lors du chargement des détails',
                registering: 'Inscription en cours...',
                unregistering: 'Désinscription en cours...',
                loginToRegister: 'Se connecter pour s\'inscrire',
                membershipRequired: 'Adhésion requise pour s\'inscrire aux événements',
                adminManageOnly: 'Les administrateurs gèrent les événements mais ne peuvent pas s\'inscrire',
                registrationSuccess: 'Inscription réussie!',
                unregistrationSuccess: 'Désinscription réussie!',
                networkError: 'Erreur réseau. Veuillez réessayer.',
                unregisterConfirm: 'Êtes-vous sûr de vouloir vous désinscrire de cet événement?',
                register: 'S\'inscrire',
                unregister: 'Se désinscrire',
                alreadyRegistered: 'Déjà inscrit'
            },
            'en-CA': {
                allDay: 'All Day',
                locationTBA: 'Location TBA',
                loading: 'Loading...',
                errorLoading: 'Error loading event details',
                registering: 'Registering...',
                unregistering: 'Unregistering...',
                loginToRegister: 'Login to Register',
                membershipRequired: 'Membership required to register for events',
                adminManageOnly: 'Admins manage events but cannot register as participants',
                registrationSuccess: 'Successfully registered!',
                unregistrationSuccess: 'Successfully unregistered!',
                networkError: 'Network error. Please try again.',
                unregisterConfirm: 'Are you sure you want to unregister from this event?',
                register: 'Register',
                unregister: 'Unregister',
                alreadyRegistered: 'Already Registered'
            }
        }
    };

    // Get translation
    function getTranslation(key) {
        const translations = modalConfig.translations[modalConfig.locale] || modalConfig.translations['fr-CA'];
        return translations[key] || key;
    }
    
    // Clear modal cache to prevent admin/member state confusion
    function clearModalCache() {
        console.log('🔄 Clearing modal cache to prevent authentication state caching...');
        
        // Clear any browser cache for modal requests
        if ('caches' in window) {
            caches.delete('event-details');
            caches.delete('api-responses');
            caches.delete('user-context');
        }
        
        // Clear ALL session storage to prevent admin/member state confusion
        sessionStorage.clear();
        
        // Clear any stored modal state
        localStorage.removeItem('modalUserState');
        localStorage.removeItem('modalEventData');
        localStorage.removeItem('userContext');
        localStorage.removeItem('adminSession');
        localStorage.removeItem('memberSession');
        localStorage.removeItem('authenticationState');
        localStorage.removeItem('cachedUserState');
        
        // Clear any cached HTTP responses in browser
        if ('caches' in window) {
            caches.keys().then(function(names) {
                for (let name of names) {
                    if (name.includes('event') || name.includes('user') || name.includes('auth')) {
                        caches.delete(name);
                    }
                }
            });
        }
        
        // Reset authentication detection - check current state fresh
        window.isAuthenticated = undefined;
        delete window.isAuthenticated;
        
        console.log('✅ Modal cache completely cleared - authentication state will be refreshed');
    }

    // Show event details modal with calendar-specific configuration
    window.showEventDetailsModal = async function(eventId, isAuthenticated, options = {}) {
        // Store current event ID for badge updates
        window.currentEventId = eventId;
        
        // Force clear any cached modal data to prevent admin/member state confusion
        clearModalCache();
        
        // Determine calendar configuration (T5.1, T5.6)
        const calendarType = options.calendarType || 'public'; // Default to public
        const calendarConfig = CalendarModalConfigs[calendarType] || CalendarModalConfigs['public'];
        
        // Merge calendar config with any provided options
        const effectiveConfig = {
            ...calendarConfig,
            ...options,
            // Ensure core properties are maintained
            detailsUrl: options.detailsUrl || calendarConfig.detailsUrl || modalConfig.detailsUrl,
            joinUrl: options.joinUrl || calendarConfig.joinUrl || modalConfig.joinUrl,
            leaveUrl: options.leaveUrl || calendarConfig.leaveUrl || modalConfig.leaveUrl
        };
        
        // Allow override of URLs if provided (backward compatibility)
        const detailsUrl = effectiveConfig.detailsUrl;
        const joinUrl = effectiveConfig.joinUrl;
        const leaveUrl = effectiveConfig.leaveUrl;
        
        const modalElement = document.getElementById('eventDetailsModal');
        const modal = new bootstrap.Modal(modalElement);
        const detailsContent = document.getElementById('eventDetailsContent');
        const loadingDiv = document.getElementById('eventDetailsLoading');
        const errorDiv = document.getElementById('eventDetailsError');
        
        // Fix accessibility issue - remove aria-hidden when modal is shown
        modalElement.addEventListener('shown.bs.modal', function() {
            modalElement.removeAttribute('aria-hidden');
        });
        
        modalElement.addEventListener('hidden.bs.modal', function() {
            modalElement.setAttribute('aria-hidden', 'true');
        });
        
        // Reset modal state
        detailsContent.style.display = 'none';
        loadingDiv.style.display = 'block';
        errorDiv.style.display = 'none';
        document.getElementById('eventAlreadyRegisteredAlert').style.display = 'none';
        
        modal.show();
        
        try {
            // CRITICAL: Aggressive cache-busting to prevent authentication state caching
            const timestamp = Date.now();
            const randomId = Math.random().toString(36).substring(2);
            const cacheBuster = `&_t=${timestamp}&_auth=${randomId}&_refresh=${Math.floor(timestamp/1000)}`;
            const response = await fetch(detailsUrl + '?id=' + eventId + cacheBuster, {
                method: 'GET',
                credentials: 'same-origin', // Ensure cookies are sent
                cache: 'no-store', // Prevent ANY caching
                headers: {
                    'Cache-Control': 'no-cache, no-store, must-revalidate, max-age=0',
                    'Pragma': 'no-cache',
                    'Expires': 'Thu, 01 Jan 1970 00:00:00 GMT',
                    'If-None-Match': '*', // Prevent conditional requests
                    'X-Requested-With': 'XMLHttpRequest'
                }
            });
            
            if (!response.ok) {
                throw new Error('Failed to load event details');
            }
            
            const eventData = await response.json();
            
            // DEBUG: Log the received event data
            console.log('Event data received:', eventData);
            console.log('User state:', eventData.userState);
            console.log('Is authenticated (parameter):', isAuthenticated);
            
            // Populate event details
            document.getElementById('eventTitle').textContent = eventData.title || '';
            
            // Date formatting
            const startDate = new Date(eventData.startDate);
            const endDate = new Date(eventData.endDate);
            const dateOptions = { year: 'numeric', month: 'long', day: 'numeric' };
            document.getElementById('eventDate').textContent = startDate.toLocaleDateString(modalConfig.locale, dateOptions);
            
            // Time formatting
            if (eventData.isAllDay) {
                document.getElementById('eventTime').textContent = getTranslation('allDay');
            } else {
                const timeOptions = { hour: '2-digit', minute: '2-digit' };
                const startTime = startDate.toLocaleTimeString(modalConfig.locale, timeOptions);
                const endTime = endDate.toLocaleTimeString(modalConfig.locale, timeOptions);
                document.getElementById('eventTime').textContent = startTime + ' - ' + endTime;
            }
            
            // Location
            document.getElementById('eventLocation').textContent = eventData.location || getTranslation('locationTBA');
            
            // Description
            if (eventData.description) {
                document.getElementById('eventDescription').textContent = eventData.description;
                document.getElementById('eventDescriptionContainer').style.display = 'block';
            } else {
                document.getElementById('eventDescriptionContainer').style.display = 'none';
            }
            
            // Contact info
            if (eventData.contactPerson || eventData.contactEmail || eventData.contactPhone) {
                let contactInfo = [];
                if (eventData.contactPerson) contactInfo.push(eventData.contactPerson);
                if (eventData.contactEmail) contactInfo.push(eventData.contactEmail);
                if (eventData.contactPhone) contactInfo.push(eventData.contactPhone);
                document.getElementById('eventContact').textContent = contactInfo.join(' - ');
                document.getElementById('eventContactContainer').style.display = 'block';
            } else {
                document.getElementById('eventContactContainer').style.display = 'none';
            }
            
            // Registration info using enhanced user state
            if (eventData.requiresRegistration) {
                // Show spots available
                if (eventData.maxParticipants > 0) {
                    document.getElementById('eventSpots').textContent = eventData.availableSpots + ' / ' + eventData.maxParticipants;
                    document.getElementById('eventSpotsContainer').style.display = 'block';
                } else {
                    document.getElementById('eventSpotsContainer').style.display = 'none';
                }
                
                // Show registration deadline
                if (eventData.registrationDeadline) {
                    const deadline = new Date(eventData.registrationDeadline);
                    document.getElementById('eventRegistrationDeadline').textContent = deadline.toLocaleDateString(modalConfig.locale, dateOptions);
                    document.getElementById('eventRegistrationDeadlineContainer').style.display = 'block';
                } else {
                    document.getElementById('eventRegistrationDeadlineContainer').style.display = 'none';
                }
                
                // Render smart registration buttons based on user state and calendar config
                renderRegistrationButtons(eventData, effectiveConfig);
            } else {
                // No registration required
                document.getElementById('eventSpotsContainer').style.display = 'none';
                document.getElementById('eventRegistrationDeadlineContainer').style.display = 'none';
                document.getElementById('eventFullAlert').style.display = 'none';
                document.getElementById('eventAlreadyRegisteredAlert').style.display = 'none';
                hideAllRegistrationButtons();
            }
            
            // Show content, hide loading
            loadingDiv.style.display = 'none';
            detailsContent.style.display = 'block';
            
        } catch (error) {
            console.error('Error loading event details:', error);
            loadingDiv.style.display = 'none';
            errorDiv.style.display = 'block';
        }
    };
    
    // Smart button rendering based on user registration state and calendar config
    function renderRegistrationButtons(eventData, calendarConfig = {}) {
        console.log('DEBUG renderRegistrationButtons - eventData.userState:', eventData.userState);
        console.log('DEBUG renderRegistrationButtons - calendarConfig:', calendarConfig);
        
        const buttonContainer = document.getElementById('eventRegistrationButtons');
        const registerBtn = document.getElementById('registerForEventBtn');
        const unregisterBtn = document.getElementById('unregisterFromEventBtn');
        
        // Reset alerts
        document.getElementById('eventAlreadyRegisteredAlert').style.display = 'none';
        document.getElementById('eventFullAlert').style.display = 'none';
        
        // Clear any existing admin message
        const existingAdminMsg = document.getElementById('eventAdminMessage');
        if (existingAdminMsg) {
            existingAdminMsg.remove();
        }
        
        // Check if registration buttons should be shown for this calendar type (T5.2, T5.7)
        if (calendarConfig.showRegistrationButtons === false && eventData.userState === 'Admin') {
            renderAdminManagementMessage(calendarConfig);
            return;
        }
        
        switch(eventData.userState) {
            case 'Visitor':
                console.log('DEBUG: Rendering Visitor buttons');
                if (calendarConfig.emphasizeVisitorConversion) {
                    renderEnhancedVisitorButtons(eventData, calendarConfig);
                } else {
                    renderVisitorButtons(eventData);
                }
                break;
                
            case 'Member':
                console.log('DEBUG: Rendering Member buttons');
                if (eventData.isRegistrationOpen && !eventData.isFull) {
                    if (calendarConfig.emphasizeReadOnlyStatus) {
                        renderMemberReadOnlyButtons(eventData, calendarConfig);
                    } else {
                        renderMemberButtons(eventData);
                    }
                } else if (eventData.isFull) {
                    showEventFullAlert();
                } else {
                    hideAllRegistrationButtons();
                }
                break;
                
            case 'MemberRegistered':
                console.log('DEBUG: Rendering MemberRegistered buttons');
                if (calendarConfig.emphasizeReadOnlyStatus) {
                    renderMemberRegisteredReadOnly(eventData, calendarConfig);
                } else {
                    renderMemberRegisteredButtons(eventData);
                }
                break;
                
            case 'Admin':
                console.log('DEBUG: Rendering Admin buttons');
                if (calendarConfig.emphasizeManagement) {
                    renderAdminManagementMessage(calendarConfig);
                } else {
                    renderAdminMessage();
                }
                break;
                
            default:
                console.log('DEBUG: Unknown user state, hiding all buttons. UserState was:', eventData.userState);
                hideAllRegistrationButtons();
        }
    }
    
    function renderVisitorButtons(eventData) {
        const registerBtn = document.getElementById('registerForEventBtn');
        const unregisterBtn = document.getElementById('unregisterFromEventBtn');
        
        console.log('DEBUG renderVisitorButtons - Current locale:', modalConfig.locale);
        console.log('DEBUG renderVisitorButtons - Available translations:', modalConfig.translations);
        console.log('DEBUG renderVisitorButtons - loginToRegister translation:', getTranslation('loginToRegister'));
        console.log('DEBUG renderVisitorButtons - membershipRequired translation:', getTranslation('membershipRequired'));
        
        registerBtn.style.display = 'inline-block';
        registerBtn.innerHTML = '<i class="fas fa-sign-in-alt"></i> ' + getTranslation('loginToRegister');
        registerBtn.className = 'btn btn-primary';
        registerBtn.onclick = function() { promptLogin(eventData.id); };
        
        unregisterBtn.style.display = 'none';
        
        // Add membership required message
        let membershipMsg = document.getElementById('membershipRequiredMsg');
        if (!membershipMsg) {
            membershipMsg = document.createElement('p');
            membershipMsg.id = 'membershipRequiredMsg';
            membershipMsg.className = 'text-muted mt-2';
            registerBtn.parentNode.appendChild(membershipMsg);
        }
        membershipMsg.textContent = getTranslation('membershipRequired');
        membershipMsg.style.display = 'block';
    }
    
    function renderMemberButtons(eventData) {
        const registerBtn = document.getElementById('registerForEventBtn');
        const unregisterBtn = document.getElementById('unregisterFromEventBtn');
        
        registerBtn.style.display = 'inline-block';
        registerBtn.innerHTML = '<i class="fas fa-user-plus"></i> ' + getTranslation('register');
        registerBtn.className = 'btn btn-primary';
        registerBtn.onclick = function() { registerForEvent(eventData.id); };
        
        unregisterBtn.style.display = 'none';
        
        hideMembershipMessage();
    }
    
    function renderMemberRegisteredButtons(eventData) {
        const registerBtn = document.getElementById('registerForEventBtn');
        const unregisterBtn = document.getElementById('unregisterFromEventBtn');
        
        document.getElementById('eventAlreadyRegisteredAlert').style.display = 'block';
        
        registerBtn.style.display = 'none';
        unregisterBtn.style.display = 'inline-block';
        unregisterBtn.innerHTML = '<i class="fas fa-user-minus"></i> ' + getTranslation('unregister');
        unregisterBtn.className = 'btn btn-outline-danger';
        unregisterBtn.onclick = function() { unregisterFromEvent(eventData.id); };
        
        hideMembershipMessage();
    }
    
    function renderAdminMessage() {
        const registerBtn = document.getElementById('registerForEventBtn');
        const unregisterBtn = document.getElementById('unregisterFromEventBtn');
        
        registerBtn.style.display = 'none';
        unregisterBtn.style.display = 'none';
        
        // Create admin message if it doesn't exist
        let adminMsg = document.getElementById('eventAdminMessage');
        if (!adminMsg) {
            adminMsg = document.createElement('div');
            adminMsg.id = 'eventAdminMessage';
            adminMsg.className = 'alert alert-info';
            registerBtn.parentNode.appendChild(adminMsg);
        }
        
        adminMsg.innerHTML = '<i class="fas fa-info-circle"></i> ' + getTranslation('adminManageOnly');
        adminMsg.style.display = 'block';
        
        hideMembershipMessage();
    }
    
    // Calendar-specific render functions (T5.2, T5.3, T5.4)
    function renderEnhancedVisitorButtons(eventData, calendarConfig) {
        const registerBtn = document.getElementById('registerForEventBtn');
        const unregisterBtn = document.getElementById('unregisterFromEventBtn');
        
        registerBtn.style.display = 'inline-block';
        registerBtn.innerHTML = '<i class="fas fa-sign-in-alt"></i> ' + getTranslation('loginToRegister');
        registerBtn.className = 'btn btn-success btn-lg'; // Larger for emphasis
        registerBtn.onclick = function() { promptLogin(eventData.id); };
        
        unregisterBtn.style.display = 'none';
        
        // Enhanced membership message with benefits
        let membershipMsg = document.getElementById('membershipRequiredMsg');
        if (!membershipMsg) {
            membershipMsg = document.createElement('div');
            membershipMsg.id = 'membershipRequiredMsg';
            membershipMsg.className = 'alert alert-info mt-3';
            registerBtn.parentNode.appendChild(membershipMsg);
        }
        membershipMsg.innerHTML = `
            <h6><i class="fas fa-star"></i> ${getTranslation('MemberBenefitsTitle')}</h6>
            <ul class="mb-2">
                <li>${getTranslation('MemberBenefitEventRegistration')}</li>
                <li>${getTranslation('MemberBenefitPriorityAccess')}</li>
                <li>${getTranslation('MemberBenefitUpdates')}</li>
            </ul>
            <small>${getTranslation('MembershipIsFree')}</small>
        `;
        membershipMsg.style.display = 'block';
    }
    
    function renderMemberReadOnlyButtons(eventData, calendarConfig) {
        const registerBtn = document.getElementById('registerForEventBtn');
        const unregisterBtn = document.getElementById('unregisterFromEventBtn');
        
        registerBtn.style.display = 'inline-block';
        registerBtn.innerHTML = '<i class="fas fa-eye"></i> ' + getTranslation('ViewDetails');
        registerBtn.className = 'btn btn-outline-primary';
        registerBtn.onclick = function() { 
            // Redirect to Subscribe page for actual registration
            window.location.href = '/Events/Subscribe#event-' + eventData.id;
        };
        
        unregisterBtn.style.display = 'none';
        
        // Add read-only message
        let readOnlyMsg = document.getElementById('readOnlyMessage');
        if (!readOnlyMsg) {
            readOnlyMsg = document.createElement('p');
            readOnlyMsg.id = 'readOnlyMessage';
            readOnlyMsg.className = 'text-muted mt-2';
            registerBtn.parentNode.appendChild(readOnlyMsg);
        }
        readOnlyMsg.innerHTML = '<i class="fas fa-info-circle"></i> Visit Subscribe page to register for events';
        readOnlyMsg.style.display = 'block';
        
        hideMembershipMessage();
    }
    
    function renderMemberRegisteredReadOnly(eventData, calendarConfig) {
        const registerBtn = document.getElementById('registerForEventBtn');
        const unregisterBtn = document.getElementById('unregisterFromEventBtn');
        
        document.getElementById('eventAlreadyRegisteredAlert').style.display = 'block';
        
        registerBtn.style.display = 'none';
        unregisterBtn.style.display = 'inline-block';
        unregisterBtn.innerHTML = '<i class="fas fa-eye"></i> ' + getTranslation('ManageRegistration');
        unregisterBtn.className = 'btn btn-outline-primary';
        unregisterBtn.onclick = function() { 
            // Redirect to Subscribe page for registration management
            window.location.href = '/Events/Subscribe#event-' + eventData.id;
        };
        
        hideMembershipMessage();
    }
    
    function renderAdminManagementMessage(calendarConfig) {
        const registerBtn = document.getElementById('registerForEventBtn');
        const unregisterBtn = document.getElementById('unregisterFromEventBtn');
        
        registerBtn.style.display = 'none';
        unregisterBtn.style.display = 'none';
        
        // Create enhanced admin message
        let adminMsg = document.getElementById('eventAdminMessage');
        if (!adminMsg) {
            adminMsg = document.createElement('div');
            adminMsg.id = 'eventAdminMessage';
            adminMsg.className = 'alert alert-warning';
            registerBtn.parentNode.appendChild(adminMsg);
        }
        
        adminMsg.innerHTML = `
            <h6><i class="fas fa-cog"></i> ${calendarConfig.name || 'Admin'} Management</h6>
            <p class="mb-2">${getTranslation('adminManageOnly')}</p>
            ${calendarConfig.allowEventEditing ? '<small><i class="fas fa-edit"></i> You can edit event details from the admin panel.</small>' : ''}
        `;
        adminMsg.style.display = 'block';
        
        hideMembershipMessage();
    }
    
    function showEventFullAlert() {
        document.getElementById('eventFullAlert').style.display = 'block';
        hideAllRegistrationButtons();
    }
    
    function hideAllRegistrationButtons() {
        document.getElementById('registerForEventBtn').style.display = 'none';
        document.getElementById('unregisterFromEventBtn').style.display = 'none';
        hideMembershipMessage();
    }
    
    function hideMembershipMessage() {
        const membershipMsg = document.getElementById('membershipRequiredMsg');
        if (membershipMsg) {
            membershipMsg.style.display = 'none';
        }
    }
    
    // AJAX registration for members
    async function registerForEvent(eventId) {
        const registerBtn = document.getElementById('registerForEventBtn');
        const originalContent = registerBtn.innerHTML;
        
        // Show loading state
        registerBtn.disabled = true;
        registerBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> ' + getTranslation('registering');
        
        try {
            const response = await fetch(modalConfig.joinAjaxUrl, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'RequestVerificationToken': document.querySelector('input[name="__RequestVerificationToken"]')?.value || ''
                },
                body: JSON.stringify({ 
                    eventId: eventId,
                    notes: '',
                    guestCount: 0
                })
            });
            
            const result = await response.json();
            
            if (result.success) {
                // Show success feedback
                showToast('success', result.message || getTranslation('registrationSuccess'));
                
                // Update UI to registered state
                updateUIAfterRegistration(true);
            } else {
                // Handle different error types
                handleRegistrationError(result.error);
                
                // Restore button
                registerBtn.disabled = false;
                registerBtn.innerHTML = originalContent;
            }
        } catch (error) {
            console.error('Registration error:', error);
            showToast('error', getTranslation('networkError'));
            
            // Restore button
            registerBtn.disabled = false;
            registerBtn.innerHTML = originalContent;
        }
    }
    
    // AJAX unregistration for members
    async function unregisterFromEvent(eventId) {
        if (!confirm(getTranslation('unregisterConfirm'))) {
            return;
        }
        
        const unregisterBtn = document.getElementById('unregisterFromEventBtn');
        const originalContent = unregisterBtn.innerHTML;
        
        // Show loading state
        unregisterBtn.disabled = true;
        unregisterBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> ' + getTranslation('unregistering');
        
        try {
            const response = await fetch(modalConfig.leaveAjaxUrl, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'RequestVerificationToken': document.querySelector('input[name="__RequestVerificationToken"]')?.value || ''
                },
                body: JSON.stringify({ 
                    eventId: eventId,
                    cancellationReason: ''
                })
            });
            
            const result = await response.json();
            
            if (result.success) {
                // Show success feedback
                showToast('success', result.message || getTranslation('unregistrationSuccess'));
                
                // Update UI to unregistered state
                updateUIAfterRegistration(false);
            } else {
                // Handle different error types
                handleRegistrationError(result.error);
                
                // Restore button
                unregisterBtn.disabled = false;
                unregisterBtn.innerHTML = originalContent;
            }
        } catch (error) {
            console.error('Unregistration error:', error);
            showToast('error', getTranslation('networkError'));
            
            // Restore button
            unregisterBtn.disabled = false;
            unregisterBtn.innerHTML = originalContent;
        }
    }
    
    // Handle registration errors with specific messaging
    function handleRegistrationError(errorCode) {
        let message;
        switch(errorCode) {
            case 'AuthenticationRequired':
                promptLogin();
                return;
            case 'AdminCannotRegister':
                message = getTranslation('adminManageOnly');
                break;
            case 'MemberAccountRequired':
                message = getTranslation('membershipRequired');
                break;
            default:
                message = errorCode || getTranslation('networkError');
        }
        showToast('error', message);
    }
    
    // Update UI state after successful registration/unregistration
    function updateUIAfterRegistration(isRegistered) {
        const registerBtn = document.getElementById('registerForEventBtn');
        const unregisterBtn = document.getElementById('unregisterFromEventBtn');
        const alreadyRegisteredAlert = document.getElementById('eventAlreadyRegisteredAlert');
        
        if (isRegistered) {
            // Switch to registered state
            registerBtn.style.display = 'none';
            unregisterBtn.style.display = 'inline-block';
            unregisterBtn.disabled = false;
            unregisterBtn.innerHTML = '<i class="fas fa-user-minus"></i> ' + getTranslation('unregister');
            alreadyRegisteredAlert.style.display = 'block';
        } else {
            // Switch to unregistered state
            unregisterBtn.style.display = 'none';
            registerBtn.style.display = 'inline-block';
            registerBtn.disabled = false;
            registerBtn.innerHTML = '<i class="fas fa-user-plus"></i> ' + getTranslation('register');
            alreadyRegisteredAlert.style.display = 'none';
        }
        
        // Update event list badge if the function exists (for Subscribe page)
        if (typeof window.updateEventListBadge === 'function' && window.currentEventId) {
            const successMessage = isRegistered ? 
                getTranslation('registrationSuccess') : 
                getTranslation('unregistrationSuccess');
            window.updateEventListBadge(window.currentEventId, isRegistered, successMessage);
        }
    }
    
    // Visitor login prompt with registration intent storage
    function promptLogin(eventId) {
        // Store registration intent for after login
        if (eventId) {
            storeRegistrationIntent(eventId);
        }
        
        // Hide event details modal
        bootstrap.Modal.getInstance(document.getElementById('eventDetailsModal')).hide();
        
        // Show login required modal if available
        if (typeof showLoginRequiredModal === 'function') {
            showLoginRequiredModal();
        } else {
            // Fallback: redirect to login
            window.location.href = '/Members/Login';
        }
    }
    
    // Store registration intent for visitors
    function storeRegistrationIntent(eventId) {
        sessionStorage.setItem('registrationIntent', JSON.stringify({
            eventId: eventId,
            timestamp: Date.now(),
            returnUrl: window.location.href
        }));
    }
    
    // Show toast notifications
    function showToast(type, message) {
        // Try to use existing toast system if available
        if (typeof window.showNotification === 'function') {
            window.showNotification(type, message);
            return;
        }
        
        // Fallback: create simple toast
        const toast = document.createElement('div');
        toast.className = `alert alert-${type === 'success' ? 'success' : 'danger'} position-fixed`;
        toast.style.cssText = 'top: 20px; right: 20px; z-index: 9999; max-width: 300px;';
        toast.innerHTML = `
            <i class="fas fa-${type === 'success' ? 'check-circle' : 'exclamation-triangle'}"></i>
            ${message}
            <button type="button" class="btn-close float-end" onclick="this.parentElement.remove()"></button>
        `;
        
        document.body.appendChild(toast);
        
        // Auto-remove after 5 seconds
        setTimeout(() => {
            if (toast.parentElement) {
                toast.remove();
            }
        }, 5000);
    }
    
    // Execute pending registration intent (called after login)
    window.executePendingRegistration = function() {
        const intent = sessionStorage.getItem('registrationIntent');
        if (intent) {
            try {
                const data = JSON.parse(intent);
                // Check if intent is not too old (30 minutes)
                if (Date.now() - data.timestamp < 30 * 60 * 1000) {
                    sessionStorage.removeItem('registrationIntent');
                    
                    // Show event modal and attempt registration
                    showEventDetailsModal(data.eventId, true).then(() => {
                        // Auto-click register button after modal loads
                        setTimeout(() => {
                            const registerBtn = document.getElementById('registerForEventBtn');
                            if (registerBtn && registerBtn.style.display !== 'none') {
                                registerBtn.click();
                            }
                        }, 500);
                    });
                    
                    return true;
                }
            } catch (e) {
                console.error('Error processing registration intent:', e);
            }
            sessionStorage.removeItem('registrationIntent');
        }
        return false;
    };
    
    // Auto-execute pending registration on page load if user is authenticated
    // DISABLED: This was causing auto-modal opening and auto-subscription
    /*
    document.addEventListener('DOMContentLoaded', function() {
        // Check if user is authenticated
        const isAuthenticated = document.body.classList.contains('authenticated') || 
                               (typeof window.isAuthenticated !== 'undefined' && window.isAuthenticated);
        
        if (isAuthenticated) {
            window.executePendingRegistration();
        }
    });
    */
    
    // Legacy function for backward compatibility
    window.showEventDetails = function(eventId) {
        // Determine if user is authenticated by checking if certain elements exist
        const isAuthenticated = document.body.classList.contains('authenticated') || 
                               (typeof window.isAuthenticated !== 'undefined' && window.isAuthenticated);
        
        showEventDetailsModal(eventId, isAuthenticated);
    };
    
    // Global modal opening function for Subscribe page
    window.openEventModal = function(eventId) {
        // Determine if user is authenticated by checking if certain elements exist
        const isAuthenticated = document.body.classList.contains('authenticated') || 
                               (typeof window.isAuthenticated !== 'undefined' && window.isAuthenticated);
        
        showEventDetailsModal(eventId, isAuthenticated, {
            calendarType: 'subscribe'
        });
    };
})();