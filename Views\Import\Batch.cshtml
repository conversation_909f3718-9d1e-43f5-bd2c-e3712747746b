@using ParaHockeyApp.Services
@model ImportBatchSummary
@{
    ViewData["Title"] = @Localizer["Import_BatchSummary"];
}

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1 class="h3 mb-0">@Localizer["Import_BatchSummary"]</h1>
                <div>
                    <a href="@Url.Action("Members", "Import")" class="btn btn-outline-primary">
                        <i class="fas fa-plus"></i> @Localizer["Import_NewImport"]
                    </a>
                    <a href="@Url.Action("History", "Import")" class="btn btn-outline-secondary">
                        <i class="fas fa-history"></i> @Localizer["Import_ViewHistory"]
                    </a>
                </div>
            </div>

            <div class="row mb-4">
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-file"></i> @Localizer["Import_FileInformation"]
                            </h5>
                        </div>
                        <div class="card-body">
                            <dl class="row">
                                <dt class="col-sm-4">@Localizer["Import_FileName"]:</dt>
                                <dd class="col-sm-8">@Model.FileName</dd>
                                
                                <dt class="col-sm-4">@Localizer["Import_UploadedBy"]:</dt>
                                <dd class="col-sm-8">@Model.UploadedBy</dd>
                                
                                <dt class="col-sm-4">@Localizer["Import_UploadedAt"]:</dt>
                                <dd class="col-sm-8">@Model.UploadedAtUtc.ToString("yyyy-MM-dd HH:mm")</dd>
                                
                                <dt class="col-sm-4">@Localizer["Import_Status"]:</dt>
                                <dd class="col-sm-8">
                                    <span class="badge bg-@(Model.Status == "Completed" ? "success" : Model.Status == "Failed" ? "danger" : "warning")">
                                        @Model.Status
                                    </span>
                                </dd>
                            </dl>

                            @if (!string.IsNullOrEmpty(Model.ErrorMessage))
                            {
                                <div class="alert alert-danger mt-3">
                                    <i class="fas fa-exclamation-triangle"></i> @Model.ErrorMessage
                                </div>
                            }
                        </div>
                    </div>
                </div>

                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-chart-pie"></i> @Localizer["Import_Statistics"]
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="row text-center">
                                <div class="col-6 col-md-3 mb-3">
                                    <div class="h4 text-primary mb-0">@Model.TotalRows</div>
                                    <small class="text-muted">@Localizer["Import_TotalRows"]</small>
                                </div>
                                <div class="col-6 col-md-3 mb-3">
                                    <div class="h4 text-success mb-0">@Model.CreatedCount</div>
                                    <small class="text-muted">@Localizer["Import_Created"]</small>
                                </div>
                                <div class="col-6 col-md-3 mb-3">
                                    <div class="h4 text-info mb-0">@Model.MergedCount</div>
                                    <small class="text-muted">@Localizer["Import_Merged"]</small>
                                </div>
                                <div class="col-6 col-md-3 mb-3">
                                    <div class="h4 text-secondary mb-0">@Model.RejectedCount</div>
                                    <small class="text-muted">@Localizer["Import_Rejected"]</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="row">
                <div class="col-md-4 mb-3">
                    <div class="card h-100">
                        <div class="card-header bg-warning text-dark">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-exclamation-triangle"></i> @Localizer["Import_NeedsFix"]
                                <span class="badge bg-dark ms-2">@Model.NeedsFixCount</span>
                            </h5>
                        </div>
                        <div class="card-body">
                            <p class="card-text">@Localizer["Import_NeedsFixDescription"]</p>
                            @if (Model.NeedsFixCount > 0)
                            {
                                <a href="@Url.Action("NeedsFix", "TempMembers", new { batchId = Model.ImportBatchId })" 
                                   class="btn btn-warning">
                                    <i class="fas fa-tools"></i> @Localizer["Import_FixRecords"]
                                </a>
                            }
                            else
                            {
                                <span class="text-muted">@Localizer["Import_NoRecordsToFix"]</span>
                            }
                        </div>
                    </div>
                </div>

                <div class="col-md-4 mb-3">
                    <div class="card h-100">
                        <div class="card-header bg-danger text-white">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-copy"></i> @Localizer["Import_Duplicates"]
                                <span class="badge bg-dark ms-2">@Model.DuplicateCount</span>
                            </h5>
                        </div>
                        <div class="card-body">
                            <p class="card-text">@Localizer["Import_DuplicatesDescription"]</p>
                            @if (Model.DuplicateCount > 0)
                            {
                                <a href="@Url.Action("Duplicates", "TempMembers", new { batchId = Model.ImportBatchId })" 
                                   class="btn btn-danger">
                                    <i class="fas fa-code-branch"></i> @Localizer["Import_ResolveDuplicates"]
                                </a>
                            }
                            else
                            {
                                <span class="text-muted">@Localizer["Import_NoDuplicates"]</span>
                            }
                        </div>
                    </div>
                </div>

                <div class="col-md-4 mb-3">
                    <div class="card h-100">
                        <div class="card-header bg-success text-white">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-check-circle"></i> @Localizer["Import_ReadyToCreate"]
                                <span class="badge bg-dark ms-2">@Model.ReadyToCreateCount</span>
                            </h5>
                        </div>
                        <div class="card-body">
                            <p class="card-text">@Localizer["Import_ReadyToCreateDescription"]</p>
                            @if (Model.ReadyToCreateCount > 0)
                            {
                                <a href="@Url.Action("ReadyToCreate", "TempMembers", new { batchId = Model.ImportBatchId })" 
                                   class="btn btn-success">
                                    <i class="fas fa-user-plus"></i> @Localizer["Import_CreateMembers"]
                                </a>
                            }
                            else
                            {
                                <span class="text-muted">@Localizer["Import_NoReadyRecords"]</span>
                            }
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script>
        // Auto-refresh statistics every 10 seconds if import is in progress
        @if (Model.Status == "Processing")
        {
            <text>
            setInterval(function() {
                fetch('@Url.Action("UpdateBatchStatistics", "Import")', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'RequestVerificationToken': $('input[name="__RequestVerificationToken"]').val()
                    },
                    body: JSON.stringify({ batchId: @Model.ImportBatchId })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        location.reload();
                    }
                })
                .catch(error => console.error('Error updating statistics:', error));
            }, 10000);
            </text>
        }
    </script>
}