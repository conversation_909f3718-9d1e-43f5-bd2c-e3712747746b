# Test script for Page Inventory Generation
# This script tests the basic functionality of generating a page inventory

Write-Host "🏒 Para Hockey - Page Inventory Generation Test" -ForegroundColor Green
Write-Host "===============================================" -ForegroundColor Green

Write-Host "`n📋 Testing Page Inventory Generation..." -ForegroundColor Yellow

# Test 1: Build the project
Write-Host "`n1. Building the project..." -ForegroundColor Cyan
$buildResult = dotnet build ParaHockeyApp.csproj --verbosity quiet
if ($LASTEXITCODE -eq 0) {
    Write-Host "   ✅ Build successful" -ForegroundColor Green
} else {
    Write-Host "   ❌ Build failed" -ForegroundColor Red
    exit 1
}

# Test 2: Check if AdminController has PageAudit action
Write-Host "`n2. Checking AdminController for PageAudit action..." -ForegroundColor Cyan
$controllerContent = Get-Content "Controllers/AdminController.cs" -Raw
if ($controllerContent -match "public async Task<IActionResult> PageAudit") {
    Write-Host "   ✅ PageAudit action found in AdminController" -ForegroundColor Green
} else {
    Write-Host "   ❌ PageAudit action not found in AdminController" -ForegroundColor Red
}

# Test 3: Check if GenerateInventory action exists
Write-Host "`n3. Checking for GenerateInventory action..." -ForegroundColor Cyan
if ($controllerContent -match "public async Task<IActionResult> GenerateInventory") {
    Write-Host "   ✅ GenerateInventory action found" -ForegroundColor Green
} else {
    Write-Host "   ❌ GenerateInventory action not found" -ForegroundColor Red
}

# Test 4: Check if PageAudit view exists
Write-Host "`n4. Checking for PageAudit view..." -ForegroundColor Cyan
if (Test-Path "Views/Admin/PageAudit.cshtml") {
    Write-Host "   ✅ PageAudit.cshtml view exists" -ForegroundColor Green
} else {
    Write-Host "   ❌ PageAudit.cshtml view missing" -ForegroundColor Red
}

# Test 5: Check if localization keys were added
Write-Host "`n5. Checking localization keys..." -ForegroundColor Cyan
$frenchResource = Get-Content "Resources/SharedResourceMarker.resx" -Raw
$englishResource = Get-Content "Resources/SharedResourceMarker.en-CA.resx" -Raw

$requiredKeys = @("PageAuditSystem", "GenerateInventory", "PageInventory", "InventoryGeneratedSuccessfully")
$allKeysFound = $true

foreach ($key in $requiredKeys) {
    if ($frenchResource -match "name=`"$key`"" -and $englishResource -match "name=`"$key`"") {
        Write-Host "   ✅ $key found in both languages" -ForegroundColor Green
    } else {
        Write-Host "   ❌ $key missing from resource files" -ForegroundColor Red
        $allKeysFound = $false
    }
}

# Test 6: Check if Admin Index has link to PageAudit
Write-Host "`n6. Checking Admin Index for PageAudit link..." -ForegroundColor Cyan
$adminIndexContent = Get-Content "Views/Admin/Index.cshtml" -Raw
if ($adminIndexContent -match "PageAudit.*Admin") {
    Write-Host "   ✅ PageAudit link found in Admin Index" -ForegroundColor Green
} else {
    Write-Host "   ❌ PageAudit link not found in Admin Index" -ForegroundColor Red
}

Write-Host "`n📊 Test Summary:" -ForegroundColor Yellow
Write-Host "=================" -ForegroundColor Yellow

if ($allKeysFound) {
    Write-Host "✅ Page Inventory Generation is ready for testing!" -ForegroundColor Green
    Write-Host "`n🚀 Next Steps:" -ForegroundColor Cyan
    Write-Host "1. Start the application: dotnet run" -ForegroundColor White
    Write-Host "2. Navigate to Admin Dashboard" -ForegroundColor White
    Write-Host "3. Click on 'Page Audit System' button" -ForegroundColor White
    Write-Host "4. Click 'Generate Inventory' to scan all pages" -ForegroundColor White
    Write-Host "5. Verify that pages are discovered and categorized" -ForegroundColor White
} else {
    Write-Host "❌ Some components are missing. Please check the errors above." -ForegroundColor Red
}

Write-Host "`n🏒 Page Inventory Generation Test Complete!" -ForegroundColor Green