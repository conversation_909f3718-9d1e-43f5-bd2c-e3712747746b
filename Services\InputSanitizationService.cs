using System.Text.RegularExpressions;
using System.Web;

namespace ParaHockeyApp.Services
{
    /// <summary>
    /// Service for sanitizing user input to prevent XSS and other injection attacks
    /// </summary>
    public class InputSanitizationService : IInputSanitizationService
    {
        // Regex patterns for validation and sanitization
        private static readonly Regex EmailRegex = new(@"^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$", RegexOptions.Compiled);
        private static readonly Regex PhoneRegex = new(@"[^\d\s\(\)\-\+\.]", RegexOptions.Compiled);
        private static readonly Regex FileNameRegex = new(@"[<>:""/\\|?*\x00-\x1F]", RegexOptions.Compiled);
        private static readonly Regex ScriptTagRegex = new(@"<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>", RegexOptions.IgnoreCase | RegexOptions.Compiled);
        private static readonly Regex HtmlTagRegex = new(@"<[^>]*>", RegexOptions.Compiled);

        /// <summary>
        /// Sanitizes a text input by removing potentially dangerous HTML and scripts
        /// </summary>
        public string SanitizeText(string? input)
        {
            if (string.IsNullOrWhiteSpace(input))
                return string.Empty;

            // Remove any script tags first
            var sanitized = ScriptTagRegex.Replace(input, string.Empty);
            
            // Remove all HTML tags
            sanitized = HtmlTagRegex.Replace(sanitized, string.Empty);
            
            // HTML decode to handle encoded entities
            sanitized = HttpUtility.HtmlDecode(sanitized);
            
            // Trim whitespace and limit length
            sanitized = sanitized.Trim();
            if (sanitized.Length > 1000) // Reasonable limit for most text fields
            {
                sanitized = sanitized.Substring(0, 1000);
            }

            return sanitized;
        }

        /// <summary>
        /// Sanitizes HTML content while preserving safe formatting tags
        /// </summary>
        public string SanitizeHtml(string? html)
        {
            if (string.IsNullOrWhiteSpace(html))
                return string.Empty;

            // Remove script tags
            var sanitized = ScriptTagRegex.Replace(html, string.Empty);
            
            // For now, we'll be conservative and remove all HTML tags
            // In a real implementation, you might use a library like HtmlSanitizer
            // to preserve safe tags like <b>, <i>, <p>, etc.
            sanitized = HtmlTagRegex.Replace(sanitized, string.Empty);
            
            return HttpUtility.HtmlDecode(sanitized).Trim();
        }

        /// <summary>
        /// Validates and sanitizes an email address
        /// </summary>
        public string? SanitizeEmail(string? email)
        {
            if (string.IsNullOrWhiteSpace(email))
                return null;

            // Trim and convert to lowercase
            var sanitized = email.Trim().ToLowerInvariant();
            
            // Remove any HTML tags
            sanitized = HtmlTagRegex.Replace(sanitized, string.Empty);
            
            // Validate email format
            if (!EmailRegex.IsMatch(sanitized))
                return null;

            // Additional length check
            if (sanitized.Length > 254) // RFC 5321 limit
                return null;

            return sanitized;
        }

        /// <summary>
        /// Sanitizes a phone number by removing potentially dangerous characters
        /// </summary>
        public string? SanitizePhone(string? phone)
        {
            if (string.IsNullOrWhiteSpace(phone))
                return null;

            // Remove HTML tags first
            var sanitized = HtmlTagRegex.Replace(phone, string.Empty);
            
            // Remove dangerous characters, keeping only digits, spaces, and common phone symbols
            sanitized = PhoneRegex.Replace(sanitized, string.Empty);
            
            // Trim whitespace
            sanitized = sanitized.Trim();
            
            // Length check
            if (sanitized.Length > 20) // Reasonable phone number length
            {
                sanitized = sanitized.Substring(0, 20);
            }

            return string.IsNullOrWhiteSpace(sanitized) ? null : sanitized;
        }

        /// <summary>
        /// Sanitizes file names to prevent directory traversal attacks
        /// </summary>
        public string SanitizeFileName(string? fileName)
        {
            if (string.IsNullOrWhiteSpace(fileName))
                return "untitled";

            // Remove HTML tags
            var sanitized = HtmlTagRegex.Replace(fileName, string.Empty);
            
            // Remove dangerous characters that could be used for directory traversal
            sanitized = FileNameRegex.Replace(sanitized, string.Empty);
            
            // Remove path separators and relative path components
            sanitized = sanitized.Replace("..", string.Empty)
                                 .Replace("./", string.Empty)
                                 .Replace(".\\", string.Empty);
            
            // Trim whitespace and dots (Windows doesn't like trailing dots)
            sanitized = sanitized.Trim().TrimEnd('.');
            
            // Ensure we have a valid filename
            if (string.IsNullOrWhiteSpace(sanitized))
                sanitized = "untitled";
            
            // Length limit
            if (sanitized.Length > 255) // Windows/Unix filename limit
            {
                sanitized = sanitized.Substring(0, 255);
            }

            return sanitized;
        }
    }
}