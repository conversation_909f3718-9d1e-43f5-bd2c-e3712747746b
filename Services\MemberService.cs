using AutoMapper;
using ParaHockeyApp.Models;
using ParaHockeyApp.Models.Entities;
using ParaHockeyApp.Models.ViewModels;
using System.Threading.Tasks;

namespace ParaHockeyApp.Services
{
    public class MemberService : IMemberService
    {
        private readonly ApplicationContext _context;
        private readonly IMapper _mapper;

        public MemberService(ApplicationContext context, IMapper mapper)
        {
            _context = context;
            _mapper = mapper;
        }

        public async Task RegisterMemberAsync(MemberRegistrationViewModel viewModel)
        {
            var member = _mapper.Map<Member>(viewModel);
            _context.Add(member);
            await _context.SaveChangesAsync();
            
            // Audit logging is now handled automatically by AuditInterceptor
        }

        public async Task RegisterMemberAsync(MemberRegistrationViewModel viewModel, Member member)
        {
            _context.Add(member);
            await _context.SaveChangesAsync();
            
            // Audit logging is now handled automatically by AuditInterceptor
        }

        public async Task UpdateMemberAsync(Member updatedMember, int editorId)
        {
            var originalMember = await _context.Members.FindAsync(updatedMember.Id);
            if (originalMember != null)
            {
                _context.Entry(originalMember).CurrentValues.SetValues(updatedMember);
                await _context.SaveChangesAsync();
                
                // Audit logging is now handled automatically by AuditInterceptor
                // No need to manually create copy of original or call MemberLogService
            }
        }

        public async Task<Parent> AddParentAsync(Parent parent)
        {
            _context.Parents.Add(parent);
            await _context.SaveChangesAsync();
            return parent;
        }

        public async Task<Parent?> GetParentAsync(int id)
        {
            return await _context.Parents.FindAsync(id);
        }

        public async Task UpdateParentAsync(Parent parent)
        {
            _context.Parents.Update(parent);
            await _context.SaveChangesAsync();
        }

        public async Task DeleteParentAsync(int id)
        {
            var parent = await _context.Parents.FindAsync(id);
            if (parent != null)
            {
                _context.Parents.Remove(parent);
                await _context.SaveChangesAsync();
            }
        }

        public async Task<EmergencyContact> AddEmergencyContactAsync(EmergencyContact contact)
        {
            _context.EmergencyContacts.Add(contact);
            await _context.SaveChangesAsync();
            return contact;
        }

        public async Task<EmergencyContact?> GetEmergencyContactAsync(int id)
        {
            return await _context.EmergencyContacts.FindAsync(id);
        }

        public async Task UpdateEmergencyContactAsync(EmergencyContact contact)
        {
            _context.EmergencyContacts.Update(contact);
            await _context.SaveChangesAsync();
        }

        public async Task DeleteEmergencyContactAsync(int id)
        {
            var contact = await _context.EmergencyContacts.FindAsync(id);
            if (contact != null)
            {
                _context.EmergencyContacts.Remove(contact);
                await _context.SaveChangesAsync();
            }
        }
    }
}