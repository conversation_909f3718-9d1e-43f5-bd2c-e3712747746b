/* ParaHockey Design System - Typography */

/* Base Typography */
html {
    font-size: 16px;
    scroll-behavior: smooth;
}

@media (max-width: 767.98px) {
    html {
        font-size: 14px;
    }
}

body {
    font-family: var(--ph-font-family-base);
    font-size: var(--ph-font-size-base);
    font-weight: var(--ph-font-weight-normal);
    line-height: var(--ph-line-height-normal);
    color: var(--ph-gray-900);
    background-color: var(--ph-white);
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

/* Headings */
h1, h2, h3, h4, h5, h6,
.h1, .h2, .h3, .h4, .h5, .h6 {
    font-family: var(--ph-font-family-heading);
    font-weight: var(--ph-font-weight-semibold);
    line-height: var(--ph-line-height-tight);
    color: var(--ph-gray-900);
    margin-top: 0;
    margin-bottom: var(--ph-spacing-md);
}

h1, .h1 {
    font-size: var(--ph-font-size-4xl);
    font-weight: var(--ph-font-weight-bold);
}

h2, .h2 {
    font-size: var(--ph-font-size-3xl);
    font-weight: var(--ph-font-weight-semibold);
}

h3, .h3 {
    font-size: var(--ph-font-size-2xl);
    font-weight: var(--ph-font-weight-semibold);
}

h4, .h4 {
    font-size: var(--ph-font-size-xl);
    font-weight: var(--ph-font-weight-medium);
}

h5, .h5 {
    font-size: var(--ph-font-size-lg);
    font-weight: var(--ph-font-weight-medium);
}

h6, .h6 {
    font-size: var(--ph-font-size-base);
    font-weight: var(--ph-font-weight-medium);
}

/* Display Headings */
.display-1 {
    font-size: var(--ph-font-size-5xl);
    font-weight: var(--ph-font-weight-extrabold);
    line-height: var(--ph-line-height-tight);
}

.display-2 {
    font-size: var(--ph-font-size-4xl);
    font-weight: var(--ph-font-weight-bold);
    line-height: var(--ph-line-height-tight);
}

.display-3 {
    font-size: var(--ph-font-size-3xl);
    font-weight: var(--ph-font-weight-bold);
    line-height: var(--ph-line-height-tight);
}

.display-4 {
    font-size: var(--ph-font-size-2xl);
    font-weight: var(--ph-font-weight-semibold);
    line-height: var(--ph-line-height-tight);
}

/* Paragraph and Text */
p {
    margin-top: 0;
    margin-bottom: var(--ph-spacing-base);
    line-height: var(--ph-line-height-normal);
}

.lead {
    font-size: var(--ph-font-size-lg);
    font-weight: var(--ph-font-weight-normal);
    line-height: var(--ph-line-height-relaxed);
    color: var(--ph-gray-700);
}

.text-large {
    font-size: var(--ph-font-size-lg);
}

.text-small {
    font-size: var(--ph-font-size-sm);
}

.text-xs {
    font-size: var(--ph-font-size-xs);
}

/* Font Weights */
.font-light {
    font-weight: var(--ph-font-weight-light);
}

.font-normal {
    font-weight: var(--ph-font-weight-normal);
}

.font-medium {
    font-weight: var(--ph-font-weight-medium);
}

.font-semibold {
    font-weight: var(--ph-font-weight-semibold);
}

.font-bold {
    font-weight: var(--ph-font-weight-bold);
}

.font-extrabold {
    font-weight: var(--ph-font-weight-extrabold);
}

/* Text Colors */
.text-primary {
    color: var(--ph-primary) !important;
}

.text-secondary {
    color: var(--ph-secondary) !important;
}

.text-success {
    color: var(--ph-success) !important;
}

.text-warning {
    color: var(--ph-warning-dark) !important;
}

.text-danger {
    color: var(--ph-danger) !important;
}

.text-info {
    color: var(--ph-info) !important;
}

.text-light {
    color: var(--ph-gray-600) !important;
}

.text-dark {
    color: var(--ph-gray-900) !important;
}

.text-muted {
    color: var(--ph-gray-600) !important;
}

.text-white {
    color: var(--ph-white) !important;
}

/* Text Alignment */
.text-left {
    text-align: left !important;
}

.text-center {
    text-align: center !important;
}

.text-right {
    text-align: right !important;
}

.text-justify {
    text-align: justify !important;
}

/* Text Transform */
.text-lowercase {
    text-transform: lowercase !important;
}

.text-uppercase {
    text-transform: uppercase !important;
}

.text-capitalize {
    text-transform: capitalize !important;
}

/* Text Decoration */
.text-decoration-none {
    text-decoration: none !important;
}

.text-decoration-underline {
    text-decoration: underline !important;
}

/* Line Height */
.lh-1 {
    line-height: 1 !important;
}

.lh-sm {
    line-height: var(--ph-line-height-tight) !important;
}

.lh-base {
    line-height: var(--ph-line-height-normal) !important;
}

.lh-lg {
    line-height: var(--ph-line-height-relaxed) !important;
}

/* Links */
a {
    color: var(--ph-primary);
    text-decoration: none;
    transition: color var(--ph-transition-fast);
}

a:hover,
a:focus {
    color: var(--ph-primary-hover);
    text-decoration: underline;
}

a:focus {
    outline: 2px solid var(--ph-focus-ring-color);
    outline-offset: var(--ph-focus-ring-offset);
    border-radius: var(--ph-radius-sm);
}

/* Lists */
ul, ol {
    margin-top: 0;
    margin-bottom: var(--ph-spacing-base);
    padding-left: var(--ph-spacing-lg);
}

li {
    margin-bottom: var(--ph-spacing-xs);
}

.list-unstyled {
    padding-left: 0;
    list-style: none;
}

.list-inline {
    padding-left: 0;
    list-style: none;
}

.list-inline-item {
    display: inline-block;
}

.list-inline-item:not(:last-child) {
    margin-right: var(--ph-spacing-sm);
}

/* Blockquotes */
blockquote {
    margin: 0 0 var(--ph-spacing-base);
    padding: var(--ph-spacing-base) var(--ph-spacing-lg);
    border-left: 4px solid var(--ph-primary);
    background-color: var(--ph-primary-light);
    font-style: italic;
}

blockquote p:last-child {
    margin-bottom: 0;
}

/* Code */
code {
    font-family: var(--ph-font-family-mono);
    font-size: 0.875em;
    color: var(--ph-danger);
    background-color: var(--ph-gray-100);
    padding: 0.125rem 0.25rem;
    border-radius: var(--ph-radius-sm);
}

pre {
    font-family: var(--ph-font-family-mono);
    font-size: var(--ph-font-size-sm);
    color: var(--ph-gray-900);
    background-color: var(--ph-gray-100);
    padding: var(--ph-spacing-base);
    border-radius: var(--ph-radius-md);
    overflow-x: auto;
    margin-bottom: var(--ph-spacing-base);
}

pre code {
    color: inherit;
    background-color: transparent;
    padding: 0;
    border-radius: 0;
}

/* Responsive Typography */
@media (max-width: 575.98px) {
    h1, .h1 {
        font-size: var(--ph-font-size-3xl);
    }
    
    h2, .h2 {
        font-size: var(--ph-font-size-2xl);
    }
    
    h3, .h3 {
        font-size: var(--ph-font-size-xl);
    }
    
    .display-1 {
        font-size: var(--ph-font-size-4xl);
    }
    
    .display-2 {
        font-size: var(--ph-font-size-3xl);
    }
    
    .display-3 {
        font-size: var(--ph-font-size-2xl);
    }
    
    .display-4 {
        font-size: var(--ph-font-size-xl);
    }
    
    .lead {
        font-size: var(--ph-font-size-base);
    }
}

/* Accessibility Improvements */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    a {
        text-decoration: underline;
    }
    
    code {
        border: 1px solid currentColor;
    }
}

/* Print styles */
@media print {
    h1, h2, h3, h4, h5, h6 {
        page-break-after: avoid;
    }
    
    p, blockquote {
        page-break-inside: avoid;
    }
    
    a {
        color: inherit;
        text-decoration: underline;
    }
}