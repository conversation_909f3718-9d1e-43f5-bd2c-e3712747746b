using ParaHockeyApp.Models.Entities;
using ParaHockeyApp.Models.Enums;

namespace ParaHockeyApp.Services
{
    /// <summary>
    /// Service for comprehensive accessibility auditing and WCAG 2.2 AA compliance testing
    /// </summary>
    public interface IAccessibilityAuditService
    {
        /// <summary>
        /// Performs comprehensive accessibility audit of a specific page
        /// </summary>
        /// <param name="pageName">Name of the page to audit (Controller/Action)</param>
        /// <returns>Accessibility audit result with WCAG findings</returns>
        Task<AccessibilityAuditResult> AuditPageAccessibilityAsync(string pageName);

        /// <summary>
        /// Validates semantic HTML structure including landmarks and headings
        /// </summary>
        /// <param name="htmlContent">HTML content to validate</param>
        /// <returns>Semantic HTML validation results</returns>
        Task<SemanticHtmlValidationResult> ValidateSemanticHtmlAsync(string htmlContent);

        /// <summary>
        /// Validates form accessibility including labels and ARIA attributes
        /// </summary>
        /// <param name="htmlContent">HTML content containing forms</param>
        /// <returns>Form accessibility validation results</returns>
        Task<FormAccessibilityResult> ValidateFormAccessibilityAsync(string htmlContent);

        /// <summary>
        /// Tests keyboard navigation accessibility
        /// </summary>
        /// <param name="pageName">Page to test keyboard navigation</param>
        /// <returns>Keyboard navigation test results</returns>
        Task<KeyboardNavigationResult> TestKeyboardNavigationAsync(string pageName);

        /// <summary>
        /// Validates screen reader compatibility
        /// </summary>
        /// <param name="htmlContent">HTML content to validate</param>
        /// <returns>Screen reader compatibility results</returns>
        Task<ScreenReaderCompatibilityResult> ValidateScreenReaderCompatibilityAsync(string htmlContent);

        /// <summary>
        /// Runs automated axe-core accessibility testing
        /// </summary>
        /// <param name="pageName">Page to test with axe-core</param>
        /// <returns>Axe-core test results</returns>
        Task<AxeCoreTestResult> RunAxeCoreTestAsync(string pageName);

        /// <summary>
        /// Generates comprehensive accessibility report for all pages
        /// </summary>
        /// <returns>Complete accessibility audit report</returns>
        Task<AccessibilityAuditReport> GenerateAccessibilityReportAsync();

        /// <summary>
        /// Automatically fixes common accessibility issues where possible
        /// </summary>
        /// <param name="pageName">Page to fix, or null for all pages</param>
        /// <param name="fixTypes">Types of fixes to apply</param>
        /// <returns>Results of the automated fixes</returns>
        Task<AccessibilityFixResult> AutoFixAccessibilityIssuesAsync(string? pageName = null, AccessibilityFixType fixTypes = AccessibilityFixType.All);
    }

    /// <summary>
    /// Comprehensive accessibility audit result for a page
    /// </summary>
    public class AccessibilityAuditResult
    {
        public string PageName { get; set; } = string.Empty;
        public int AccessibilityScore { get; set; }
        public WcagComplianceLevel ComplianceLevel { get; set; }
        public bool HasLandmarkStructure { get; set; }
        public bool HasProperHeadingHierarchy { get; set; }
        public bool HasFormLabels { get; set; }
        public bool HasAltTextForImages { get; set; }
        public bool HasKeyboardNavigation { get; set; }
        public bool HasScreenReaderSupport { get; set; }
        public bool HasColorContrastCompliance { get; set; }
        public bool HasFocusManagement { get; set; }
        public List<AccessibilityIssue> Issues { get; set; } = new();
        public List<AccessibilityRecommendation> Recommendations { get; set; } = new();
        public DateTime AuditDate { get; set; } = DateTime.UtcNow;
    }

    /// <summary>
    /// Individual accessibility issue found during audit
    /// </summary>
    public class AccessibilityIssue
    {
        public string Id { get; set; } = Guid.NewGuid().ToString();
        public string PageName { get; set; } = string.Empty;
        public WcagLevel WcagLevel { get; set; }
        public string WcagCriterion { get; set; } = string.Empty;
        public string Category { get; set; } = string.Empty;
        public string Issue { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public string Location { get; set; } = string.Empty;
        public string Recommendation { get; set; } = string.Empty;
        public string FixCode { get; set; } = string.Empty;
        public bool IsAutoFixable { get; set; }
        public AccessibilityImpact Impact { get; set; }
        public DateTime FoundDate { get; set; } = DateTime.UtcNow;
    }

    /// <summary>
    /// Accessibility recommendation
    /// </summary>
    public class AccessibilityRecommendation
    {
        public string Title { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public WcagLevel Priority { get; set; }
        public string Implementation { get; set; } = string.Empty;
        public int EstimatedHours { get; set; }
        public string WcagReference { get; set; } = string.Empty;
    }

    /// <summary>
    /// Semantic HTML validation result
    /// </summary>
    public class SemanticHtmlValidationResult
    {
        public bool HasValidLandmarks { get; set; }
        public bool HasProperHeadingStructure { get; set; }
        public bool HasSemanticElements { get; set; }
        public List<string> MissingLandmarks { get; set; } = new();
        public List<string> HeadingIssues { get; set; } = new();
        public List<string> SemanticIssues { get; set; } = new();
        public int SemanticScore { get; set; }
    }

    /// <summary>
    /// Form accessibility validation result
    /// </summary>
    public class FormAccessibilityResult
    {
        public bool HasProperLabels { get; set; }
        public bool HasFieldsetLegends { get; set; }
        public bool HasAriaAttributes { get; set; }
        public bool HasErrorAssociation { get; set; }
        public List<string> UnlabeledFields { get; set; } = new();
        public List<string> MissingAriaAttributes { get; set; } = new();
        public List<string> ErrorHandlingIssues { get; set; } = new();
        public int FormAccessibilityScore { get; set; }
    }

    /// <summary>
    /// Keyboard navigation test result
    /// </summary>
    public class KeyboardNavigationResult
    {
        public bool HasLogicalTabOrder { get; set; }
        public bool HasVisibleFocusIndicators { get; set; }
        public bool HasSkipLinks { get; set; }
        public bool HasKeyboardTraps { get; set; }
        public List<string> TabOrderIssues { get; set; } = new();
        public List<string> FocusIssues { get; set; } = new();
        public List<string> KeyboardAccessibilityIssues { get; set; } = new();
        public int KeyboardScore { get; set; }
    }

    /// <summary>
    /// Screen reader compatibility result
    /// </summary>
    public class ScreenReaderCompatibilityResult
    {
        public bool HasProperAriaLabels { get; set; }
        public bool HasLiveRegions { get; set; }
        public bool HasDescriptiveText { get; set; }
        public bool HasProperRoles { get; set; }
        public List<string> AriaIssues { get; set; } = new();
        public List<string> LiveRegionIssues { get; set; } = new();
        public List<string> DescriptionIssues { get; set; } = new();
        public int ScreenReaderScore { get; set; }
    }

    /// <summary>
    /// Axe-core automated testing result
    /// </summary>
    public class AxeCoreTestResult
    {
        public bool TestPassed { get; set; }
        public int ViolationsCount { get; set; }
        public int IncompleteCount { get; set; }
        public List<AxeViolation> Violations { get; set; } = new();
        public List<AxeIncomplete> Incomplete { get; set; } = new();
        public DateTime TestDate { get; set; } = DateTime.UtcNow;
    }

    /// <summary>
    /// Axe-core violation
    /// </summary>
    public class AxeViolation
    {
        public string Id { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public string Impact { get; set; } = string.Empty;
        public List<string> Tags { get; set; } = new();
        public List<string> Nodes { get; set; } = new();
        public string HelpUrl { get; set; } = string.Empty;
    }

    /// <summary>
    /// Axe-core incomplete test
    /// </summary>
    public class AxeIncomplete
    {
        public string Id { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public List<string> Nodes { get; set; } = new();
        public string HelpUrl { get; set; } = string.Empty;
    }

    /// <summary>
    /// Comprehensive accessibility audit report
    /// </summary>
    public class AccessibilityAuditReport
    {
        public int TotalPages { get; set; }
        public int CompliantPages { get; set; }
        public int NonCompliantPages { get; set; }
        public int CriticalIssues { get; set; }
        public int SeriousIssues { get; set; }
        public int ModerateIssues { get; set; }
        public int MinorIssues { get; set; }
        public double AverageAccessibilityScore { get; set; }
        public WcagComplianceLevel OverallComplianceLevel { get; set; }
        public List<AccessibilityIssue> TopIssues { get; set; } = new();
        public List<string> RecommendedActions { get; set; } = new();
        public DateTime GeneratedDate { get; set; } = DateTime.UtcNow;
    }

    /// <summary>
    /// Accessibility fix result
    /// </summary>
    public class AccessibilityFixResult
    {
        public string PageName { get; set; } = string.Empty;
        public bool Success { get; set; }
        public List<string> AppliedFixes { get; set; } = new();
        public List<string> ManualFixesRequired { get; set; } = new();
        public List<string> Messages { get; set; } = new();
        public DateTime AppliedDate { get; set; } = DateTime.UtcNow;
    }
}