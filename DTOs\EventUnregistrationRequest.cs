using System.ComponentModel.DataAnnotations;

namespace ParaHockeyApp.DTOs
{
    /// <summary>
    /// DTO for AJAX event unregistration requests
    /// </summary>
    public class EventUnregistrationRequest
    {
        [Required]
        public int EventId { get; set; }

        [StringLength(500, ErrorMessageResourceName = "ValidationStringLength", ErrorMessageResourceType = typeof(Resources.SharedResource))]
        public string? CancellationReason { get; set; }
    }
}