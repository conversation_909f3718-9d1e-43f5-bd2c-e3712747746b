# Simple Database Connection Test
Write-Host "=== ParaHockey Database Test ===" -ForegroundColor Cyan

# Function to test connection
function Test-Connection {
    param($server, $database, $user, $password, $envName)
    
    Write-Host "`n$envName Environment:" -ForegroundColor Yellow
    Write-Host "Server: $server"
    Write-Host "Database: $database"
    
    try {
        if ($user) {
            $connStr = "Server=$server;Database=$database;User Id=$user;Password=$password;TrustServerCertificate=True;"
            Write-Host "Auth: SQL Server ($user)"
        } else {
            $connStr = "Server=$server;Database=$database;Integrated Security=True;TrustServerCertificate=True;"
            Write-Host "Auth: Windows"
        }
        
        $conn = New-Object System.Data.SqlClient.SqlConnection($connStr)
        $conn.Open()
        
        $cmd = $conn.CreateCommand()
        $cmd.CommandText = "SELECT COUNT(*) FROM Members"
        $memberCount = $cmd.ExecuteScalar()
        
        $cmd.CommandText = "SELECT SUSER_SNAME(), USER_NAME()"
        $reader = $cmd.ExecuteReader()
        if ($reader.Read()) {
            $login = $reader.GetValue(0)
            $dbUser = $reader.GetValue(1)
            Write-Host "Login: $login"
            Write-Host "DB User: $dbUser"
        }
        $reader.Close()
        
        Write-Host "Members: $memberCount" -ForegroundColor Green
        $conn.Close()
        return $true
    }
    catch {
        Write-Host "ERROR: $_" -ForegroundColor Red
        return $false
    }
}

# Test environments
$testOk = Test-Connection "SIMBA\SQLEXPRESS" "ParaHockeyDB_TEST" "ParaHockeyUser" "ParaHockey2025!" "TEST"
$prodSqlOk = Test-Connection "SIMBA\SQLEXPRESS" "ParaHockeyDB" "ParaHockeyUser" "ParaHockey2025!" "PROD-SQL"
$prodWinOk = Test-Connection "SIMBA\SQLEXPRESS" "ParaHockeyDB" $null $null "PROD-WIN"

Write-Host "`n=== SUMMARY ===" -ForegroundColor Cyan
Write-Host "TEST: $(if($testOk){'OK'}else{'FAIL'})" -ForegroundColor $(if($testOk){'Green'}else{'Red'})
Write-Host "PROD-SQL: $(if($prodSqlOk){'OK'}else{'FAIL'})" -ForegroundColor $(if($prodSqlOk){'Green'}else{'Red'})
Write-Host "PROD-WIN: $(if($prodWinOk){'OK'}else{'FAIL'})" -ForegroundColor $(if($prodWinOk){'Green'}else{'Red'})