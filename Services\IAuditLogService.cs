using ParaHockeyApp.Models.Entities;

namespace ParaHockeyApp.Services
{
    public interface IAuditLogService
    {
        /// <summary>
        /// Get all audit logs for a specific entity
        /// </summary>
        Task<List<AuditLog>> GetEntityAuditLogsAsync(string entityType, int entityId);

        /// <summary>
        /// Get recent audit logs across all entities
        /// </summary>
        Task<List<AuditLog>> GetRecentAuditLogsAsync(int pageNumber = 1, int pageSize = 50);

        /// <summary>
        /// Get audit logs by performer (admin or member)
        /// </summary>
        Task<List<AuditLog>> GetAuditLogsByPerformerAsync(int? adminId, int? memberId, int pageNumber = 1, int pageSize = 50);

        /// <summary>
        /// Get audit summary for an entity (created by, modified by, dates)
        /// </summary>
        Task<AuditSummary> GetEntityAuditSummaryAsync(string entityType, int entityId);

        /// <summary>
        /// Get comprehensive audit logs for a member including parent and emergency contact changes
        /// </summary>
        Task<List<AuditLog>> GetMemberComprehensiveAuditLogsAsync(int memberId);

        /// <summary>
        /// Manually log an action (used for actions that need explicit audit trail beyond EF changes)
        /// </summary>
        Task LogActionAsync(string description, Member? member, ActionSource source, string performerName, string? ipAddress);

        /// <summary>
        /// Log a member merge operation with field-level changes
        /// </summary>
        Task LogMemberMergeAsync(int memberId, Dictionary<string, object?> originalValues, Dictionary<string, object?> newValues, string performedBy);

    }

    public class AuditSummary
    {
        public DateTime? DateCreated { get; set; }
        public DateTime? DateModified { get; set; }
        public string? CreatedBy { get; set; }
        public string? ModifiedBy { get; set; }
        public ActionSource? CreatedBySource { get; set; }
        public ActionSource? ModifiedBySource { get; set; }
        public int TotalChanges { get; set; }
    }
}