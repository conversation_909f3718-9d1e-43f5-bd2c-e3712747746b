using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace ParaHockeyApp.Migrations
{
    /// <inheritdoc />
    public partial class AddIsActiveToAdminUsers : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            // Defensive: Add IsActive column only if it doesn't exist
            migrationBuilder.Sql(@"
                IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('AdminUsers') AND name = 'IsActive')
                BEGIN
                    ALTER TABLE AdminUsers ADD IsActive bit NOT NULL DEFAULT 1
                END");

            // Defensive: Set all existing admin users to active only if column exists and table has rows
            migrationBuilder.Sql(@"
                IF EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('AdminUsers') AND name = 'IsActive')
                AND EXISTS (SELECT 1 FROM AdminUsers)
                BEGIN
                    UPDATE AdminUsers SET IsActive = 1 WHERE IsActive IS NULL OR IsActive = 0
                END");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "IsActive",
                table: "AdminUsers");
        }
    }
}