# Script to check and fix ParaHockey deployment on SIMBA server
# Run this script ON THE SIMBA SERVER as Administrator

Write-Host "=== ParaHockey Deployment Checker ===" -ForegroundColor Green

# Check Test deployment
Write-Host "`n=== Checking Test Deployment ===" -ForegroundColor Yellow
$testPath = "C:\inetpub\wwwroot\ParaHockey-Test"

if (Test-Path $testPath) {
    Write-Host "✅ Test folder exists: $testPath" -ForegroundColor Green
    
    # Check for key files
    $keyFiles = @("ParaHockeyApp.dll", "web.config", "appsettings.json")
    foreach ($file in $keyFiles) {
        $filePath = Join-Path $testPath $file
        if (Test-Path $filePath) {
            Write-Host "✅ Found: $file" -ForegroundColor Green
        }
        else {
            Write-Host "❌ Missing: $file" -ForegroundColor Red
        }
    }
    
    # Check web.config content
    $webConfigPath = Join-Path $testPath "web.config"
    if (Test-Path $webConfigPath) {
        Write-Host "`nChecking web.config content..." -ForegroundColor Cyan
        $webConfigContent = Get-Content $webConfigPath -Raw
        if ($webConfigContent -like "*aspNetCore*") {
            Write-Host "✅ web.config contains ASP.NET Core configuration" -ForegroundColor Green
        }
        else {
            Write-Host "❌ web.config missing ASP.NET Core configuration" -ForegroundColor Red
            Write-Host "Creating proper web.config..." -ForegroundColor Yellow
            
            $webConfigContent = @"
<?xml version="1.0" encoding="utf-8"?>
<configuration>
  <location path="." inheritInChildApplications="false">
    <system.webServer>
      <handlers>
        <add name="aspNetCore" path="*" verb="*" modules="AspNetCoreModuleV2" resourceType="Unspecified" />
      </handlers>
      <aspNetCore processPath="dotnet" arguments=".\ParaHockeyApp.dll" stdoutLogEnabled="false" stdoutLogFile=".\logs\stdout" hostingModel="inprocess">
        <environmentVariables>
          <environmentVariable name="ASPNETCORE_ENVIRONMENT" value="Staging" />
        </environmentVariables>
      </aspNetCore>
    </system.webServer>
  </location>
</configuration>
"@
            $webConfigContent | Out-File -FilePath $webConfigPath -Encoding UTF8
            Write-Host "✅ Created proper web.config for Test environment" -ForegroundColor Green
        }
    }
}
else {
    Write-Host "❌ Test folder not found: $testPath" -ForegroundColor Red
}

# Check Production deployment
Write-Host "`n=== Checking Production Deployment ===" -ForegroundColor Yellow
$prodPath = "C:\inetpub\wwwroot\ParaHockey-Production"

if (Test-Path $prodPath) {
    Write-Host "✅ Production folder exists: $prodPath" -ForegroundColor Green
    
    # Check for key files
    foreach ($file in $keyFiles) {
        $filePath = Join-Path $prodPath $file
        if (Test-Path $filePath) {
            Write-Host "✅ Found: $file" -ForegroundColor Green
        }
        else {
            Write-Host "❌ Missing: $file" -ForegroundColor Red
        }
    }
    
    # Check web.config content
    $webConfigPath = Join-Path $prodPath "web.config"
    if (Test-Path $webConfigPath) {
        Write-Host "`nChecking web.config content..." -ForegroundColor Cyan
        $webConfigContent = Get-Content $webConfigPath -Raw
        if ($webConfigContent -like "*aspNetCore*") {
            Write-Host "✅ web.config contains ASP.NET Core configuration" -ForegroundColor Green
        }
        else {
            Write-Host "❌ web.config missing ASP.NET Core configuration" -ForegroundColor Red
            Write-Host "Creating proper web.config..." -ForegroundColor Yellow
            
            $webConfigContent = @"
<?xml version="1.0" encoding="utf-8"?>
<configuration>
  <location path="." inheritInChildApplications="false">
    <system.webServer>
      <handlers>
        <add name="aspNetCore" path="*" verb="*" modules="AspNetCoreModuleV2" resourceType="Unspecified" />
      </handlers>
      <aspNetCore processPath="dotnet" arguments=".\ParaHockeyApp.dll" stdoutLogEnabled="false" stdoutLogFile=".\logs\stdout" hostingModel="inprocess">
        <environmentVariables>
          <environmentVariable name="ASPNETCORE_ENVIRONMENT" value="Production" />
        </environmentVariables>
      </aspNetCore>
    </system.webServer>
  </location>
</configuration>
"@
            $webConfigContent | Out-File -FilePath $webConfigPath -Encoding UTF8
            Write-Host "✅ Created proper web.config for Production environment" -ForegroundColor Green
        }
    }
}
else {
    Write-Host "❌ Production folder not found: $prodPath" -ForegroundColor Red
}

# Check IIS Application Pool
Write-Host "`n=== Checking IIS Configuration ===" -ForegroundColor Yellow

Import-Module WebAdministration -ErrorAction SilentlyContinue

if (Get-Module WebAdministration) {
    # Check if sites exist
    $testSite = Get-Website | Where-Object { $_.Name -eq "ParaHockey-Test" }
    $prodSite = Get-Website | Where-Object { $_.Name -eq "ParaHockey-Production" }
    
    if ($testSite) {
        Write-Host "✅ Test website exists in IIS" -ForegroundColor Green
        Write-Host "   Port: $($testSite.bindings.Collection.bindingInformation)" -ForegroundColor Cyan
    }
    else {
        Write-Host "❌ Test website not found in IIS" -ForegroundColor Red
    }
    
    if ($prodSite) {
        Write-Host "✅ Production website exists in IIS" -ForegroundColor Green
        Write-Host "   Port: $($prodSite.bindings.Collection.bindingInformation)" -ForegroundColor Cyan
    }
    else {
        Write-Host "❌ Production website not found in IIS" -ForegroundColor Red
    }
    
    # Check ASP.NET Core Module
    $aspNetCoreModule = Get-WebGlobalModule | Where-Object { $_.Name -eq "AspNetCoreModuleV2" }
    if ($aspNetCoreModule) {
        Write-Host "✅ ASP.NET Core Module V2 is installed" -ForegroundColor Green
    }
    else {
        Write-Host "❌ ASP.NET Core Module V2 not found - you may need to install ASP.NET Core Hosting Bundle" -ForegroundColor Red
    }
}
else {
    Write-Host "❌ WebAdministration module not available" -ForegroundColor Red
}

Write-Host "`n=== Recommendations ===" -ForegroundColor Yellow
Write-Host "1. If web.config was created/fixed, restart the IIS sites" -ForegroundColor White
Write-Host "2. Check that ASP.NET Core Hosting Bundle is installed on SIMBA" -ForegroundColor White
Write-Host "3. Test the websites after restarting IIS" -ForegroundColor White

Write-Host "`nPress any key to continue..." -ForegroundColor Yellow
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown") 