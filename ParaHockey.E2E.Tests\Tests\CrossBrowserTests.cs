using Xunit;
using FluentAssertions;
using ParaHockey.E2E.Tests.Infrastructure;
using ParaHockey.E2E.Tests.PageObjects;

namespace ParaHockey.E2E.Tests.Tests
{
    public class CrossBrowserTests : IDisposable
    {
        private readonly TestConfiguration _config;
        private readonly WebDriverFactory _driverFactory;

        public CrossBrowserTests()
        {
            _config = TestConfiguration.Load();
            _driverFactory = new WebDriverFactory(_config);
        }

        [Theory]
        [InlineData("Chrome")]
        [InlineData("Firefox")]
        [InlineData("Edge")]
        public void HomePage_ShouldLoadCorrectly_InAllBrowsers(string browserName)
        {
            using var driver = _driverFactory.CreateDriver(browserName);
            var wait = new OpenQA.Selenium.Support.UI.WebDriverWait(driver, TimeSpan.FromSeconds(_config.ExplicitWaitSeconds));
            var homePage = new HomePage(driver, wait);

            try
            {
                // Arrange & Act
                driver.Navigate().GoToUrl(_config.BaseUrl);
                wait.Until(driver => ((OpenQA.Selenium.IJavaScriptExecutor)driver).ExecuteScript("return document.readyState").Equals("complete"));

                // Assert
                homePage.IsLogoDisplayed().Should().BeTrue($"Logo should be displayed in {browserName}");
                homePage.IsRegisterButtonDisplayed().Should().BeTrue($"Register button should be displayed in {browserName}");
                homePage.AreStatisticsDisplayed().Should().BeTrue($"Statistics should be displayed in {browserName}");
                homePage.AreRegistrationTypesDisplayed().Should().BeTrue($"Registration types should be displayed in {browserName}");

                var title = driver.Title;
                title.Should().Contain("Parahockey", $"Page title should contain 'Parahockey' in {browserName}");
            }
            catch (Exception ex)
            {
                TakeScreenshot(driver, $"HomePage_CrossBrowser_{browserName}");
                throw new Exception($"Test failed in {browserName}: {ex.Message}", ex);
            }
        }

        [Theory]
        [InlineData("Chrome")]
        [InlineData("Firefox")]
        [InlineData("Edge")]
        public void RegistrationForm_ShouldWorkCorrectly_InAllBrowsers(string browserName)
        {
            using var driver = _driverFactory.CreateDriver(browserName);
            var wait = new OpenQA.Selenium.Support.UI.WebDriverWait(driver, TimeSpan.FromSeconds(_config.ExplicitWaitSeconds));
            var registrationPage = new RegistrationPage(driver, wait);

            try
            {
                // Arrange
                driver.Navigate().GoToUrl($"{_config.BaseUrl}/Members/Register");
                wait.Until(driver => ((OpenQA.Selenium.IJavaScriptExecutor)driver).ExecuteScript("return document.readyState").Equals("complete"));

                // Act - Fill basic form
                registrationPage.FillBasicInformation("CrossBrowser", "Test", "1990-01-01", "male");
                registrationPage.FillAddress("123 Test St", "Montreal", "QC", "H3B 2Y5");
                registrationPage.FillContactInformation("(*************", "mobile", "<EMAIL>");

                // Assert - Form fields should work in all browsers
                registrationPage.GetFirstNameValue().Should().Be("CrossBrowser", $"First name should be filled correctly in {browserName}");
                registrationPage.GetLastNameValue().Should().Be("Test", $"Last name should be filled correctly in {browserName}");
                registrationPage.GetEmailValue().Should().Be("<EMAIL>", $"Email should be filled correctly in {browserName}");
                
                // Test input masking
                registrationPage.IsPhoneMasked().Should().BeTrue($"Phone masking should work in {browserName}");
                registrationPage.IsPostalCodeMasked().Should().BeTrue($"Postal code masking should work in {browserName}");
            }
            catch (Exception ex)
            {
                TakeScreenshot(driver, $"RegistrationForm_CrossBrowser_{browserName}");
                throw new Exception($"Registration form test failed in {browserName}: {ex.Message}", ex);
            }
        }

        [Theory]
        [InlineData("Chrome")]
        [InlineData("Firefox")]
        [InlineData("Edge")]
        public void DynamicFields_ShouldWork_InAllBrowsers(string browserName)
        {
            using var driver = _driverFactory.CreateDriver(browserName);
            var wait = new OpenQA.Selenium.Support.UI.WebDriverWait(driver, TimeSpan.FromSeconds(_config.ExplicitWaitSeconds));
            var registrationPage = new RegistrationPage(driver, wait);

            try
            {
                // Arrange
                driver.Navigate().GoToUrl($"{_config.BaseUrl}/Members/Register");
                wait.Until(driver => ((OpenQA.Selenium.IJavaScriptExecutor)driver).ExecuteScript("return document.readyState").Equals("complete"));

                // Act & Assert - Test Junior registration type
                registrationPage.SelectRegistrationType("Junior");
                Thread.Sleep(2000);

                registrationPage.IsParentSectionVisible().Should().BeTrue($"Parent section should be visible for Junior in {browserName}");
                registrationPage.IsEmergencyContactSectionVisible().Should().BeFalse($"Emergency contact should be hidden for Junior in {browserName}");

                // Test Development registration type
                registrationPage.SelectRegistrationType("Development");
                Thread.Sleep(2000);

                registrationPage.IsEmergencyContactSectionVisible().Should().BeTrue($"Emergency contact section should be visible for Development in {browserName}");
                registrationPage.IsParentSectionVisible().Should().BeFalse($"Parent section should be hidden for Development in {browserName}");
            }
            catch (Exception ex)
            {
                TakeScreenshot(driver, $"DynamicFields_CrossBrowser_{browserName}");
                throw new Exception($"Dynamic fields test failed in {browserName}: {ex.Message}", ex);
            }
        }

        [Theory]
        [InlineData("Chrome")]
        [InlineData("Firefox")]
        [InlineData("Edge")]
        public void LanguageSwitching_ShouldWork_InAllBrowsers(string browserName)
        {
            using var driver = _driverFactory.CreateDriver(browserName);
            var wait = new OpenQA.Selenium.Support.UI.WebDriverWait(driver, TimeSpan.FromSeconds(_config.ExplicitWaitSeconds));
            var homePage = new HomePage(driver, wait);

            try
            {
                // Arrange
                driver.Navigate().GoToUrl(_config.BaseUrl);
                wait.Until(driver => ((OpenQA.Selenium.IJavaScriptExecutor)driver).ExecuteScript("return document.readyState").Equals("complete"));

                // Act & Assert - Test language switching
                var originalTitle = homePage.GetWelcomeTitle();
                
                homePage.SwitchToEnglish();
                Thread.Sleep(3000);

                var englishTitle = homePage.GetWelcomeTitle();
                englishTitle.Should().NotBe(originalTitle, $"Title should change when switching to English in {browserName}");
                englishTitle.Should().Contain("Welcome", $"English title should contain 'Welcome' in {browserName}");

                homePage.SwitchToFrench();
                Thread.Sleep(3000);

                var frenchTitle = homePage.GetWelcomeTitle();
                frenchTitle.Should().NotBe(englishTitle, $"Title should change when switching to French in {browserName}");
                frenchTitle.Should().Contain("Bienvenue", $"French title should contain 'Bienvenue' in {browserName}");
            }
            catch (Exception ex)
            {
                TakeScreenshot(driver, $"LanguageSwitching_CrossBrowser_{browserName}");
                throw new Exception($"Language switching test failed in {browserName}: {ex.Message}", ex);
            }
        }

        [Theory]
        [InlineData("Chrome")]
        [InlineData("Firefox")]
        [InlineData("Edge")]
        public void FormValidation_ShouldWork_InAllBrowsers(string browserName)
        {
            using var driver = _driverFactory.CreateDriver(browserName);
            var wait = new OpenQA.Selenium.Support.UI.WebDriverWait(driver, TimeSpan.FromSeconds(_config.ExplicitWaitSeconds));
            var registrationPage = new RegistrationPage(driver, wait);

            try
            {
                // Arrange
                driver.Navigate().GoToUrl($"{_config.BaseUrl}/Members/Register");
                wait.Until(driver => ((OpenQA.Selenium.IJavaScriptExecutor)driver).ExecuteScript("return document.readyState").Equals("complete"));

                // Act - Submit empty form
                registrationPage.SubmitForm();
                Thread.Sleep(2000);

                // Assert - Validation should work
                registrationPage.HasValidationErrors().Should().BeTrue($"Empty form should show validation errors in {browserName}");

                // Test specific field validation
                registrationPage.FillContactInformation("invalid-phone", "mobile", "invalid-email");
                Thread.Sleep(1000);

                var phoneField = driver.FindElement(OpenQA.Selenium.By.Name("Phone"));
                var emailField = driver.FindElement(OpenQA.Selenium.By.Name("Email"));

                var phoneClasses = phoneField.GetAttribute("class");
                var emailClasses = emailField.GetAttribute("class");

                phoneClasses.Should().Contain("is-invalid", $"Invalid phone should show error styling in {browserName}");
                emailClasses.Should().Contain("is-invalid", $"Invalid email should show error styling in {browserName}");
            }
            catch (Exception ex)
            {
                TakeScreenshot(driver, $"FormValidation_CrossBrowser_{browserName}");
                throw new Exception($"Form validation test failed in {browserName}: {ex.Message}", ex);
            }
        }

        [Fact]
        public void JavaScriptFeatures_ShouldWork_InAllBrowsers()
        {
            var browsers = new[] { "Chrome", "Firefox", "Edge" };
            var results = new Dictionary<string, bool>();

            foreach (var browserName in browsers)
            {
                using var driver = _driverFactory.CreateDriver(browserName);
                var wait = new OpenQA.Selenium.Support.UI.WebDriverWait(driver, TimeSpan.FromSeconds(_config.ExplicitWaitSeconds));

                try
                {
                    // Test JavaScript execution
                    driver.Navigate().GoToUrl($"{_config.BaseUrl}/Members/Register");
                    wait.Until(driver => ((OpenQA.Selenium.IJavaScriptExecutor)driver).ExecuteScript("return document.readyState").Equals("complete"));

                    // Test jQuery and mask plugin availability
                    var jqueryLoaded = (bool)((OpenQA.Selenium.IJavaScriptExecutor)driver).ExecuteScript("return typeof $ !== 'undefined'");
                    var maskPluginLoaded = (bool)((OpenQA.Selenium.IJavaScriptExecutor)driver).ExecuteScript("return typeof $.fn.mask !== 'undefined'");
                    var validatorLoaded = (bool)((OpenQA.Selenium.IJavaScriptExecutor)driver).ExecuteScript("return typeof $.validator !== 'undefined'");

                    results[browserName] = jqueryLoaded && maskPluginLoaded && validatorLoaded;

                    // Test actual JavaScript functionality
                    var phoneField = driver.FindElement(OpenQA.Selenium.By.Name("Phone"));
                    phoneField.SendKeys("5141234567");
                    Thread.Sleep(1000);

                    var maskedValue = phoneField.GetAttribute("value");
                    var isMasked = maskedValue.Contains("(") && maskedValue.Contains(")") && maskedValue.Contains("-");
                    
                    results[browserName] = results[browserName] && isMasked;
                }
                catch (Exception ex)
                {
                    TakeScreenshot(driver, $"JavaScriptFeatures_{browserName}");
                    results[browserName] = false;
                    Console.WriteLine($"JavaScript test failed in {browserName}: {ex.Message}");
                }
            }

            // Assert all browsers passed
            foreach (var result in results)
            {
                result.Value.Should().BeTrue($"JavaScript features should work in {result.Key}");
            }
        }

        private void TakeScreenshot(OpenQA.Selenium.IWebDriver driver, string testName)
        {
            try
            {
                var screenshot = ((OpenQA.Selenium.ITakesScreenshot)driver).GetScreenshot();
                var directory = _config.ScreenshotPath;
                Directory.CreateDirectory(directory);
                
                var fileName = $"{testName}_{DateTime.Now:yyyyMMdd_HHmmss}.png";
                var filePath = Path.Combine(directory, fileName);
                
                screenshot.SaveAsFile(filePath);
                Console.WriteLine($"Screenshot saved: {filePath}");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Failed to take screenshot: {ex.Message}");
            }
        }

        public void Dispose()
        {
            // Cleanup is handled by using statements in each test method
        }
    }
}