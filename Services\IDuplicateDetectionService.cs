using ParaHockeyApp.Models.Entities;
using ParaHockeyApp.ViewModels;

namespace ParaHockeyApp.Services
{
    /// <summary>
    /// Service for detecting duplicate members and handling merge operations
    /// </summary>
    public interface IDuplicateDetectionService
    {
        /// <summary>
        /// Finds an existing member that matches the temp member based on duplicate detection rules
        /// Priority: Email match first, then FirstName + LastName + DateOfBirth
        /// </summary>
        /// <param name="tempMember">The temp member to check for duplicates</param>
        /// <returns>The existing member if found, null otherwise</returns>
        Task<Member?> FindExistingMemberAsync(TempMember tempMember);

        /// <summary>
        /// Previews a merge operation showing what fields will change
        /// </summary>
        /// <param name="tempMemberId">ID of the temp member</param>
        /// <param name="fieldChoices">Dictionary of field names to chosen values (temp or existing)</param>
        /// <returns>Preview of the merge operation</returns>
        Task<DuplicateResolutionPreview> PreviewMergeAsync(Guid tempMemberId, Dictionary<string, string> fieldChoices);

        /// <summary>
        /// Applies a merge operation by updating the existing member with selected field values
        /// </summary>
        /// <param name="tempMemberId">ID of the temp member</param>
        /// <param name="fieldChoices">Dictionary of field names to chosen values (temp or existing)</param>
        /// <param name="performedBy">Username of the person performing the merge</param>
        /// <returns>The updated member</returns>
        Task<Member> ApplyMergeAsync(Guid tempMemberId, Dictionary<string, string> fieldChoices, string performedBy);

        /// <summary>
        /// Gets duplicate resolution view model for UI display
        /// </summary>
        /// <param name="tempMemberId">ID of the temp member</param>
        /// <returns>View model with temp member, existing member, and field comparisons</returns>
        Task<DuplicateResolutionViewModel> GetDuplicateResolutionViewModelAsync(Guid tempMemberId);

        /// <summary>
        /// Gets paginated list of duplicate queue items for a batch
        /// </summary>
        /// <param name="batchId">Import batch ID</param>
        /// <param name="pageNumber">Page number (1-based)</param>
        /// <param name="pageSize">Number of items per page</param>
        /// <param name="searchTerm">Optional search term</param>
        /// <param name="matchTypeFilter">Optional match type filter</param>
        /// <returns>Paginated duplicate queue view model</returns>
        Task<DuplicateQueueViewModel> GetDuplicateQueueAsync(int batchId, int pageNumber = 1, int pageSize = 20, string? searchTerm = null, DuplicateMatchType? matchTypeFilter = null);

        /// <summary>
        /// Determines the type of duplicate match between temp member and existing member
        /// </summary>
        /// <param name="tempMember">Temp member</param>
        /// <param name="existingMember">Existing member</param>
        /// <returns>Type of match found</returns>
        DuplicateMatchType GetMatchType(TempMember tempMember, Member existingMember);

        /// <summary>
        /// Rejects a duplicate temp member (marks as rejected)
        /// </summary>
        /// <param name="tempMemberId">ID of the temp member to reject</param>
        /// <param name="rejectedBy">Username of the person rejecting</param>
        /// <returns>Updated temp member</returns>
        Task<TempMember> RejectDuplicateAsync(Guid tempMemberId, string rejectedBy);

        /// <summary>
        /// Bulk rejects multiple duplicate temp members
        /// </summary>
        /// <param name="tempMemberIds">List of temp member IDs to reject</param>
        /// <param name="rejectedBy">Username of the person rejecting</param>
        /// <returns>Number of temp members rejected</returns>
        Task<int> BulkRejectDuplicatesAsync(List<Guid> tempMemberIds, string rejectedBy);
    }

    /// <summary>
    /// Preview of a merge operation showing what will change
    /// </summary>
    public class DuplicateResolutionPreview
    {
        public TempMember TempMember { get; set; } = null!;
        public Member ExistingMember { get; set; } = null!;
        public Dictionary<string, FieldComparison> FieldComparisons { get; set; } = new();
        public List<FieldChange> Changes { get; set; } = new();
    }

    /// <summary>
    /// Comparison of a field between temp and existing member
    /// </summary>
    public class FieldComparison
    {
        public string FieldName { get; set; } = string.Empty;
        public string DisplayName { get; set; } = string.Empty;
        public string? TempValue { get; set; }
        public string? ExistingValue { get; set; }
        public bool AreIdentical { get; set; }
        public string? SelectedValue { get; set; }
    }

    /// <summary>
    /// Represents a field that will change during merge
    /// </summary>
    public class FieldChange
    {
        public string FieldName { get; set; } = string.Empty;
        public string DisplayName { get; set; } = string.Empty;
        public string? OldValue { get; set; }
        public string? NewValue { get; set; }
    }
}