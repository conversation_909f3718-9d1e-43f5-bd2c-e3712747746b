using ParaHockeyApp.Models.Entities;

namespace ParaHockeyApp.Services
{
    /// <summary>
    /// Service for detecting and resolving duplicate members during import
    /// </summary>
    public interface IDuplicateDetectionService
    {
        /// <summary>
        /// Finds existing member that matches the temp member using duplicate detection logic
        /// Priority: Email match first, then FirstName + LastName + DateOfBirth match
        /// </summary>
        /// <param name="tempMember">Temp member to find duplicates for</param>
        /// <returns>Existing member if found, null if no duplicates</returns>
        Task<Member?> FindExistingMemberAsync(TempMember tempMember);

        /// <summary>
        /// Finds existing member by email (case-insensitive exact match)
        /// </summary>
        /// <param name="email">Normalized email address</param>
        /// <returns>Existing member if found, null otherwise</returns>
        Task<Member?> FindMemberByEmailAsync(string email);

        /// <summary>
        /// Finds existing member by name and date of birth (exact match)
        /// </summary>
        /// <param name="firstName">First name</param>
        /// <param name="lastName">Last name</param>
        /// <param name="dateOfBirth">Date of birth</param>
        /// <returns>Existing member if found, null otherwise</returns>
        Task<Member?> FindMemberByNameAndDobAsync(string firstName, string lastName, DateTime dateOfBirth);

        /// <summary>
        /// Previews merge operation showing field-by-field comparison
        /// </summary>
        /// <param name="tempMemberId">Temp member ID</param>
        /// <param name="fieldChoices">Dictionary of field choices (fieldName -> "temp" or "existing")</param>
        /// <returns>Preview of what changes will be made</returns>
        Task<DuplicateResolutionPreview> PreviewMergeAsync(Guid tempMemberId, Dictionary<string, string> fieldChoices);

        /// <summary>
        /// Applies merge operation using selected field choices
        /// </summary>
        /// <param name="tempMemberId">Temp member ID</param>
        /// <param name="fieldChoices">Dictionary of field choices (fieldName -> "temp" or "existing")</param>
        /// <param name="performedBy">Admin performing the merge</param>
        /// <returns>Updated existing member</returns>
        Task<Member> ApplyMergeAsync(Guid tempMemberId, Dictionary<string, string> fieldChoices, string performedBy);

        /// <summary>
        /// Gets field-by-field comparison between temp member and existing member
        /// </summary>
        /// <param name="tempMember">Temp member data</param>
        /// <param name="existingMember">Existing member data</param>
        /// <returns>Dictionary of field comparisons</returns>
        Dictionary<string, FieldComparison> GetFieldComparisons(TempMember tempMember, Member existingMember);
    }

    /// <summary>
    /// Preview of merge operation showing what will change
    /// </summary>
    public class DuplicateResolutionPreview
    {
        public TempMember TempMember { get; set; } = null!;
        public Member ExistingMember { get; set; } = null!;
        public Dictionary<string, FieldChange> Changes { get; set; } = new();
        public bool HasChanges => Changes.Any(c => c.Value.WillChange);
    }

    /// <summary>
    /// Represents a change that will be made to a field
    /// </summary>
    public class FieldChange
    {
        public string FieldName { get; set; } = string.Empty;
        public string CurrentValue { get; set; } = string.Empty;
        public string NewValue { get; set; } = string.Empty;
        public bool WillChange { get; set; }
    }

    /// <summary>
    /// Comparison between temp and existing member field values
    /// </summary>
    public class FieldComparison
    {
        public string FieldName { get; set; } = string.Empty;
        public string TempValue { get; set; } = string.Empty;
        public string ExistingValue { get; set; } = string.Empty;
        public bool AreIdentical { get; set; }
        public string SelectedValue { get; set; } = string.Empty;
    }
}