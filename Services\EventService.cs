using Microsoft.EntityFrameworkCore;
using ParaHockeyApp.Models;
using ParaHockeyApp.Models.Entities;
using ParaHockeyApp.DTOs;

namespace ParaHockeyApp.Services
{
    public class EventService : IEventService
    {
        private readonly ApplicationContext _context;
        private readonly ICategoryFilterService _categoryFilterService;
        private readonly IAuditLogService _auditLogService;

        public EventService(ApplicationContext context, ICategoryFilterService categoryFilterService, IAuditLogService auditLogService)
        {
            _context = context;
            _categoryFilterService = categoryFilterService;
            _auditLogService = auditLogService;
        }

        #region Event Management

        public async Task<List<Event>> GetAllEventsAsync()
        {
            return await _context.Events
                .Include(e => e.EventCategory)
                .Include(e => e.EventRegistrations)
                .ThenInclude(er => er.Member)
                .OrderByDescending(e => e.StartDate)
                .ToListAsync();
        }

        public async Task<List<Event>> GetPublishedEventsAsync()
        {
            return await _context.Events
                .Include(e => e.EventCategory)
                .Include(e => e.EventRegistrations)
                .ThenInclude(er => er.Member)
                .Where(e => e.IsPublished && 
                           e.IsActive &&
                           e.EventCategory.DisplayNameKey != "EventCategory_Tentative")
                .OrderByDescending(e => e.StartDate)
                .ToListAsync();
        }

        public async Task<List<Event>> GetEventsBetweenDatesAsync(DateTime startDate, DateTime endDate)
        {
            return await _context.Events
                .Include(e => e.EventCategory)
                .Include(e => e.EventRegistrations)
                .ThenInclude(er => er.Member)
                .Where(e => e.StartDate >= startDate && e.StartDate <= endDate && e.IsActive)
                .OrderBy(e => e.StartDate)
                .ToListAsync();
        }

        public async Task<Event?> GetEventByIdAsync(int id)
        {
            return await _context.Events
                .Include(e => e.EventCategory)
                .Include(e => e.EventRegistrations)
                .ThenInclude(er => er.Member)
                .FirstOrDefaultAsync(e => e.Id == id);
        }

        public async Task<List<Event>> GetEventsByCategoryAsync(int categoryId)
        {
            return await _context.Events
                .Include(e => e.EventCategory)
                .Include(e => e.EventRegistrations)
                .ThenInclude(er => er.Member)
                .Where(e => e.EventCategoryId == categoryId && e.IsActive)
                .OrderByDescending(e => e.StartDate)
                .ToListAsync();
        }

        public async Task<Event> CreateEventAsync(Event eventEntity)
        {
            // Auto-assign category if not set
            if (eventEntity.EventCategoryId == 0)
            {
                eventEntity.EventCategoryId = await DetermineCategoryIdAsync(eventEntity.Title);
            }

            // Apply category-specific defaults for RequiresRegistration and IsPublished
            await ApplyCategoryDefaults(eventEntity);

            _context.Events.Add(eventEntity);
            await _context.SaveChangesAsync();
            return await GetEventByIdAsync(eventEntity.Id) ?? eventEntity;
        }

        public async Task<Event> UpdateEventAsync(Event eventEntity)
        {
            // First, get the existing event from the database
            var existingEvent = await _context.Events.FindAsync(eventEntity.Id);
            if (existingEvent == null)
            {
                throw new ArgumentException($"Event with ID {eventEntity.Id} not found");
            }

            // Compare all relevant fields to detect changes
            bool hasChanges = false;
            
            if (existingEvent.Title != eventEntity.Title)
            {
                existingEvent.Title = eventEntity.Title;
                hasChanges = true;
            }
            
            if (existingEvent.Description != eventEntity.Description)
            {
                existingEvent.Description = eventEntity.Description;
                hasChanges = true;
            }
            
            if (existingEvent.StartDate != eventEntity.StartDate)
            {
                existingEvent.StartDate = eventEntity.StartDate;
                hasChanges = true;
            }
            
            if (existingEvent.EndDate != eventEntity.EndDate)
            {
                existingEvent.EndDate = eventEntity.EndDate;
                hasChanges = true;
            }
            
            if (existingEvent.IsAllDay != eventEntity.IsAllDay)
            {
                existingEvent.IsAllDay = eventEntity.IsAllDay;
                hasChanges = true;
            }
            
            if (existingEvent.Location != eventEntity.Location)
            {
                existingEvent.Location = eventEntity.Location;
                hasChanges = true;
            }
            
            if (existingEvent.EventCategoryId != eventEntity.EventCategoryId)
            {
                existingEvent.EventCategoryId = eventEntity.EventCategoryId;
                hasChanges = true;
            }
            
            if (existingEvent.RequiresRegistration != eventEntity.RequiresRegistration)
            {
                existingEvent.RequiresRegistration = eventEntity.RequiresRegistration;
                hasChanges = true;
            }
            
            if (existingEvent.MaxParticipants != eventEntity.MaxParticipants)
            {
                existingEvent.MaxParticipants = eventEntity.MaxParticipants;
                hasChanges = true;
            }
            
            if (existingEvent.RegistrationDeadline != eventEntity.RegistrationDeadline)
            {
                existingEvent.RegistrationDeadline = eventEntity.RegistrationDeadline;
                hasChanges = true;
            }
            
            if (existingEvent.IsPublished != eventEntity.IsPublished)
            {
                existingEvent.IsPublished = eventEntity.IsPublished;
                hasChanges = true;
            }
            
            if (existingEvent.Priority != eventEntity.Priority)
            {
                existingEvent.Priority = eventEntity.Priority;
                hasChanges = true;
            }
            
            if (existingEvent.ContactPerson != eventEntity.ContactPerson)
            {
                existingEvent.ContactPerson = eventEntity.ContactPerson;
                hasChanges = true;
            }
            
            if (existingEvent.ContactEmail != eventEntity.ContactEmail)
            {
                existingEvent.ContactEmail = eventEntity.ContactEmail;
                hasChanges = true;
            }
            
            if (existingEvent.ContactPhone != eventEntity.ContactPhone)
            {
                existingEvent.ContactPhone = eventEntity.ContactPhone;
                hasChanges = true;
            }
            
            if (existingEvent.OrganizerNotes != eventEntity.OrganizerNotes)
            {
                existingEvent.OrganizerNotes = eventEntity.OrganizerNotes;
                hasChanges = true;
            }

            // Only save if there are actual changes
            if (hasChanges)
            {
                existingEvent.DateModified = DateTime.UtcNow;
                await _context.SaveChangesAsync();
            }

            return existingEvent;
        }

        public async Task<bool> DeleteEventAsync(int id)
        {
            var eventEntity = await _context.Events.FindAsync(id);
            if (eventEntity == null) return false;

            eventEntity.IsActive = false;
            await _context.SaveChangesAsync();
            return true;
        }

        public async Task<bool> PublishEventAsync(int id)
        {
            var eventEntity = await _context.Events.FindAsync(id);
            if (eventEntity == null) return false;

            eventEntity.IsPublished = true;
            await _context.SaveChangesAsync();
            return true;
        }

        public async Task<bool> UnpublishEventAsync(int id)
        {
            var eventEntity = await _context.Events.FindAsync(id);
            if (eventEntity == null) return false;

            eventEntity.IsPublished = false;
            await _context.SaveChangesAsync();
            return true;
        }

        private async Task<int> DetermineCategoryIdAsync(string title)
        {
            if (string.IsNullOrWhiteSpace(title))
            {
                var otherCategory = await _context.EventCategories.FirstOrDefaultAsync(c => c.DisplayNameKey == "EventCategory_Other");
                return otherCategory?.Id ?? 8; // Default to Other category ID
            }

            title = title.ToLowerInvariant();

            // Check for Tentatif
            if (title.Contains("tentatif"))
            {
                var tentativeCategory = await _context.EventCategories.FirstOrDefaultAsync(c => c.DisplayNameKey == "EventCategory_Tentative");
                return tentativeCategory?.Id ?? 8;
            }

            // Check for Défi Sportif
            if (title.Contains("défi sportif"))
            {
                var tournamentCategory = await _context.EventCategories.FirstOrDefaultAsync(c => c.DisplayNameKey == "EventCategory_Tournament");
                return tournamentCategory?.Id ?? 3; // Tournament category ID
            }

            // Default to Other
            var defaultCategory = await _context.EventCategories.FirstOrDefaultAsync(c => c.DisplayNameKey == "EventCategory_Other");
            return defaultCategory?.Id ?? 8;
        }

        private async Task ApplyCategoryDefaults(Event eventEntity)
        {
            var category = await GetEventCategoryByIdAsync(eventEntity.EventCategoryId);
            if (category == null) return;

            switch (category.DisplayNameKey)
            {
                case "EventCategory_Tentative":
                    // Tentative events: both RequiresRegistration and IsPublished are false
                    eventEntity.RequiresRegistration = false;
                    eventEntity.IsPublished = false;
                    break;
                
                case "EventCategory_Practice":
                    // Practice events: RequiresRegistration is false, IsPublished is true
                    eventEntity.RequiresRegistration = false;
                    eventEntity.IsPublished = true;
                    break;
                
                default:
                    // All other categories: both RequiresRegistration and IsPublished are true
                    eventEntity.RequiresRegistration = true;
                    eventEntity.IsPublished = true;
                    break;
            }
        }

        #endregion

        #region Event Categories

        public async Task<List<EventCategory>> GetAllEventCategoriesAsync()
        {
            return await _context.EventCategories
                .Where(ec => ec.IsActive)
                .OrderBy(ec => ec.DisplayOrder)
                .ToListAsync();
        }

        public async Task<List<EventCategory>> GetAllEventCategoriesSortedAsync()
        {
            var categories = await _context.EventCategories
                .Where(ec => ec.IsActive)
                .ToListAsync();

            // Sort alphabetically by DisplayNameKey, but put "Other" at the end
            return categories
                .OrderBy(c => c.DisplayNameKey == "EventCategory_Other" ? "zzz" : c.DisplayNameKey)
                .ToList();
        }

        public async Task<EventCategory?> GetEventCategoryByIdAsync(int id)
        {
            return await _context.EventCategories
                .FirstOrDefaultAsync(ec => ec.Id == id);
        }

        public async Task<EventCategory> CreateEventCategoryAsync(EventCategory category)
        {
            _context.EventCategories.Add(category);
            await _context.SaveChangesAsync();
            return category;
        }

        public async Task<EventCategory> UpdateEventCategoryAsync(EventCategory category)
        {
            _context.Entry(category).State = EntityState.Modified;
            await _context.SaveChangesAsync();
            return category;
        }

        public async Task<bool> DeleteEventCategoryAsync(int id)
        {
            var category = await _context.EventCategories.FindAsync(id);
            if (category == null) return false;

            var hasEvents = await _context.Events.AnyAsync(e => e.EventCategoryId == id);
            if (hasEvents) return false; // Cannot delete category with existing events

            category.IsActive = false;
            await _context.SaveChangesAsync();
            return true;
        }

        #endregion

        #region Event Registrations

        public async Task<List<EventRegistration>> GetEventRegistrationsAsync(int eventId)
        {
            return await _context.EventRegistrations
                .Include(er => er.Member)
                .Include(er => er.Event)
                .Where(er => er.EventId == eventId)
                .OrderBy(er => er.RegistrationDate)
                .ToListAsync();
        }

        public async Task<List<EventRegistration>> GetMemberRegistrationsAsync(int memberId)
        {
            return await _context.EventRegistrations
                .Include(er => er.Event)
                .ThenInclude(e => e.EventCategory)
                .Include(er => er.Member)
                .Where(er => er.MemberId == memberId)
                .OrderByDescending(er => er.Event.StartDate)
                .ToListAsync();
        }

        public async Task<EventRegistration?> GetEventRegistrationAsync(int eventId, int memberId)
        {
            return await _context.EventRegistrations
                .Include(er => er.Event)
                .Include(er => er.Member)
                .FirstOrDefaultAsync(er => er.EventId == eventId && er.MemberId == memberId);
        }

        public async Task<EventRegistration?> GetEventRegistrationByIdAsync(int registrationId)
        {
            return await _context.EventRegistrations
                .Include(er => er.Event)
                .Include(er => er.Member)
                .FirstOrDefaultAsync(er => er.Id == registrationId);
        }

        public async Task<EventRegistration> RegisterMemberForEventAsync(int eventId, int memberId, string? memberNotes = null, int guestCount = 0)
        {
            var existingRegistration = await GetEventRegistrationAsync(eventId, memberId);
            if (existingRegistration != null)
            {
                throw new InvalidOperationException("Member is already registered for this event");
            }

            var eventEntity = await GetEventByIdAsync(eventId);
            if (eventEntity == null)
            {
                throw new ArgumentException("Event not found");
            }

            if (eventEntity.IsFull)
            {
                throw new InvalidOperationException("Event is full");
            }

            // Get member details to populate registration fields
            var member = await _context.Members
                .FirstOrDefaultAsync(m => m.Id == memberId);

            if (member == null)
            {
                throw new ArgumentException("Member not found");
            }

            // Get primary emergency contact for registration
            var primaryEmergencyContact = await _context.EmergencyContacts
                .Where(ec => ec.MemberId == memberId)
                .FirstOrDefaultAsync();

            var registration = new EventRegistration
            {
                EventId = eventId,
                MemberId = memberId,
                Status = RegistrationStatus.Pending,
                MemberNotes = memberNotes,
                GuestCount = guestCount,
                RegistrationDate = DateTime.UtcNow,
                EmergencyContact = primaryEmergencyContact != null 
                    ? $"{primaryEmergencyContact.FirstName} {primaryEmergencyContact.LastName}"
                    : null,
                EmergencyPhone = primaryEmergencyContact?.Phone,
                SpecialRequirements = null // This would need to come from the registration form
            };

            _context.EventRegistrations.Add(registration);
            await _context.SaveChangesAsync();

            return await _context.EventRegistrations
                .Include(er => er.Event)
                .Include(er => er.Member)
                .FirstAsync(er => er.Id == registration.Id);
        }

        public async Task<bool> CancelEventRegistrationAsync(int eventId, int memberId)
        {
            var registration = await GetEventRegistrationAsync(eventId, memberId);
            if (registration == null || !registration.CanBeCancelled)
            {
                return false;
            }

            registration.Status = RegistrationStatus.Cancelled;
            registration.CancellationDate = DateTime.UtcNow;
            await _context.SaveChangesAsync();
            return true;
        }

        public async Task<bool> ConfirmEventRegistrationAsync(int registrationId)
        {
            var registration = await _context.EventRegistrations.FindAsync(registrationId);
            if (registration == null) return false;

            registration.Status = RegistrationStatus.Confirmed;
            registration.ConfirmationDate = DateTime.UtcNow;
            await _context.SaveChangesAsync();
            return true;
        }

        public async Task<bool> RejectEventRegistrationAsync(int registrationId, string? adminNotes = null)
        {
            var registration = await _context.EventRegistrations.FindAsync(registrationId);
            if (registration == null) return false;

            registration.Status = RegistrationStatus.Rejected;
            registration.AdminNotes = adminNotes;
            await _context.SaveChangesAsync();
            return true;
        }

        public async Task<bool> MarkAttendanceAsync(int registrationId, bool attended, AdminContext? adminContext = null)
        {
            var registration = await _context.EventRegistrations
                .Include(r => r.Event)
                .Include(r => r.Member)
                .FirstOrDefaultAsync(r => r.Id == registrationId);
            
            if (registration == null) return false;

            var previousAttendance = registration.Attended;
            registration.Attended = attended;
            await _context.SaveChangesAsync();

            // Create audit log entry if admin context is provided
            if (adminContext != null)
            {
                await _auditLogService.LogActionAsync(
                    $"Attendance marked as {(attended ? "Present" : "Absent")} for event '{registration.Event.Title}' (was: {(previousAttendance?.ToString() ?? "Not Set")})",
                    registration.Member,
                    ActionSource.AdminPanel,
                    adminContext.AdminName,
                    adminContext.IPAddress);
            }

            return true;
        }

        public async Task<bool> UpdateRegistrationStatusAsync(int registrationId, RegistrationStatus newStatus,
            string? adminNotes, AdminContext adminContext)
        {
            var registration = await _context.EventRegistrations
                .Include(r => r.Event)
                .Include(r => r.Member)
                .FirstOrDefaultAsync(r => r.Id == registrationId);

            if (registration == null)
                return false;

            var oldStatus = registration.Status;
            registration.Status = newStatus;
            registration.AdminNotes = adminNotes;

            if (newStatus == RegistrationStatus.Confirmed && !registration.ConfirmationDate.HasValue)
            {
                registration.ConfirmationDate = DateTime.UtcNow;
            }

            await _context.SaveChangesAsync();

            // Create audit log entry
            await _auditLogService.LogActionAsync(
                $"Registration status changed from {oldStatus} to {newStatus} for event '{registration.Event.Title}'",
                registration.Member,
                ActionSource.AdminPanel,
                adminContext.AdminName,
                adminContext.IPAddress);

            return true;
        }

        public async Task<bool> BulkUpdateRegistrationStatusAsync(List<int> registrationIds,
            RegistrationStatus newStatus, AdminContext adminContext)
        {
            var registrations = await _context.EventRegistrations
                .Include(r => r.Event)
                .Include(r => r.Member)
                .Where(r => registrationIds.Contains(r.Id))
                .ToListAsync();

            foreach (var registration in registrations)
            {
                var oldStatus = registration.Status;
                registration.Status = newStatus;
                if (newStatus == RegistrationStatus.Confirmed && !registration.ConfirmationDate.HasValue)
                {
                    registration.ConfirmationDate = DateTime.UtcNow;
                }
            }

            await _context.SaveChangesAsync();

            await _auditLogService.LogActionAsync(
                $"Bulk status update: {registrations.Count} registrations set to {newStatus}",
                null,
                ActionSource.AdminPanel,
                adminContext.AdminName,
                adminContext.IPAddress);

            return true;
        }

        #endregion

        #region Statistics and Reporting

        public async Task<int> GetEventCountAsync(bool publishedOnly = false)
        {
            var query = _context.Events.Where(e => e.IsActive);
            if (publishedOnly)
            {
                query = query.Where(e => e.IsPublished);
            }
            return await query.CountAsync();
        }

        public async Task<int> GetRegistrationCountAsync(int eventId)
        {
            return await _context.EventRegistrations
                .Where(er => er.EventId == eventId && er.Status == RegistrationStatus.Confirmed)
                .CountAsync();
        }

        public async Task<int> GetMemberEventCountAsync(int memberId)
        {
            return await _context.EventRegistrations
                .Where(er => er.MemberId == memberId && er.Status == RegistrationStatus.Confirmed)
                .CountAsync();
        }

        public async Task<List<Event>> GetUpcomingEventsAsync(int count = 10)
        {
            return await _context.Events
                .Include(e => e.EventCategory)
                .Where(e => e.IsPublished && e.IsActive && e.StartDate > DateTime.Now)
                .OrderBy(e => e.StartDate)
                .Take(count)
                .ToListAsync();
        }

        public async Task<List<Event>> GetRecentEventsAsync(int count = 10)
        {
            return await _context.Events
                .Include(e => e.EventCategory)
                .Where(e => e.IsPublished && e.IsActive && e.StartDate < DateTime.Now)
                .OrderByDescending(e => e.StartDate)
                .Take(count)
                .ToListAsync();
        }

        public async Task<bool> IsEventFullAsync(int eventId)
        {
            var eventEntity = await GetEventByIdAsync(eventId);
            return eventEntity?.IsFull ?? false;
        }

        public async Task<bool> CanMemberRegisterAsync(int eventId, int memberId)
        {
            var eventEntity = await GetEventByIdAsync(eventId);
            if (eventEntity == null || !eventEntity.IsRegistrationOpen)
            {
                return false;
            }

            var existingRegistration = await GetEventRegistrationAsync(eventId, memberId);
            return existingRegistration == null;
        }

        public async Task<List<Event>> GetSubscribableEventsAsync()
        {
            var now = DateTime.Now;
            var today = DateTime.Today; // Start of today
            
            // Get only the next 5 subscribable events starting from today
            // Exclude Practice events and Tentative events from Subscribe page
            var events = await _context.Events
                .Include(e => e.EventCategory)
                .Include(e => e.EventRegistrations.Where(er => er.IsActive && er.Status == RegistrationStatus.Confirmed))
                .AsSplitQuery()
                .Where(e => e.IsPublished && 
                           e.IsActive && 
                           e.RequiresRegistration &&
                           e.EventCategory.DisplayNameKey != "EventCategory_Practice" &&
                           e.EventCategory.DisplayNameKey != "EventCategory_Tentative" &&
                           e.StartDate >= today && // Only events from today onwards
                           (e.RegistrationDeadline == null || now <= e.RegistrationDeadline))
                .OrderBy(e => e.StartDate)
                .Take(5) // Limit to next 5 events
                .ToListAsync();
            
            // Filter out full events (computed properties will work now) - but still respect the 5 limit
            return events.Where(e => !e.IsFull).ToList();
        }

        public async Task<bool> AddAdminNoteAsync(int registrationId, string adminNote, AdminContext adminContext)
        {
            var registration = await _context.EventRegistrations
                .Include(r => r.Event)
                .Include(r => r.Member)
                .FirstOrDefaultAsync(r => r.Id == registrationId);

            if (registration == null)
                return false;

            var previousNotes = registration.AdminNotes;
            registration.AdminNotes = adminNote;
            await _context.SaveChangesAsync();

            // Create audit log entry
            await _auditLogService.LogActionAsync(
                $"Admin note added to registration for event '{registration.Event.Title}'. Previous: '{previousNotes ?? "None"}', New: '{adminNote}'",
                registration.Member,
                ActionSource.AdminPanel,
                adminContext.AdminName,
                adminContext.IPAddress);

            return true;
        }

        public async Task<List<EventRegistration>> GetEventRegistrationsOptimizedAsync(int eventId)
        {
            return await _context.EventRegistrations
                .Include(er => er.Member)
                .Include(er => er.Event)
                .ThenInclude(e => e.EventCategory)
                .Where(er => er.EventId == eventId)
                .OrderBy(er => er.RegistrationDate)
                .ToListAsync();
        }

        public async Task<Dictionary<RegistrationStatus, int>> GetRegistrationCountsByStatusAsync(int eventId)
        {
            var registrations = await _context.EventRegistrations
                .Where(er => er.EventId == eventId)
                .GroupBy(er => er.Status)
                .Select(g => new { Status = g.Key, Count = g.Count() })
                .ToListAsync();

            var result = new Dictionary<RegistrationStatus, int>();
            foreach (RegistrationStatus status in Enum.GetValues<RegistrationStatus>())
            {
                result[status] = registrations.FirstOrDefault(r => r.Status == status)?.Count ?? 0;
            }

            return result;
        }

        public async Task<Dictionary<RegistrationStatus, List<EventRegistration>>> GetRegistrationsGroupedByStatusAsync(int eventId)
        {
            var registrations = await GetEventRegistrationsOptimizedAsync(eventId);
            
            return registrations
                .GroupBy(r => r.Status)
                .ToDictionary(g => g.Key, g => g.ToList());
        }

        #endregion

        #region Calendar Specific Methods

        public async Task<List<Event>> GetCalendarEventsAsync(DateTime startDate, DateTime endDate, bool publishedOnly = true, int? categoryId = null)
        {
            var query = _context.Events
                .Include(e => e.EventCategory)
                .Where(e => e.IsActive && e.StartDate >= startDate && e.StartDate <= endDate);

            if (publishedOnly)
            {
                query = query.Where(e => e.IsPublished);
            }

            if (categoryId.HasValue)
            {
                // Use CategoryFilterService to get composite category list
                var categoryIds = _categoryFilterService.GetFilterCategoryIds(categoryId.Value);
                query = query.Where(e => categoryIds.Contains(e.EventCategoryId));
            }

            return await query.OrderBy(e => e.StartDate).ToListAsync();
        }

        public async Task<List<object>> GetCalendarEventsJsonAsync(DateTime startDate, DateTime endDate, bool publishedOnly = true, int? categoryId = null)
        {
            var events = await GetCalendarEventsAsync(startDate, endDate, publishedOnly, categoryId);
            
            return events.Select(e => new
            {
                id = e.Id,
                title = e.Title,
                start = e.StartDate.ToString("yyyy-MM-ddTHH:mm:ss"),
                end = e.EndDate.ToString("yyyy-MM-ddTHH:mm:ss"),
                allDay = e.IsAllDay,
                backgroundColor = e.EventCategory.Color,
                borderColor = e.EventCategory.Color,
                textColor = "#ffffff",
                description = e.Description ?? "",
                location = e.Location ?? "",
                category = e.EventCategory.DisplayNameKey,
                eventCategoryId = e.EventCategoryId,
                requiresRegistration = e.RequiresRegistration,
                maxParticipants = e.MaxParticipants,
                currentRegistrations = e.CurrentRegistrations,
                isRegistrationOpen = e.IsRegistrationOpen,
                extendedProps = new
                {
                    categoryId = e.EventCategoryId,
                    categoryIcon = e.EventCategory.IconClass,
                    priority = e.Priority
                }
            }).Cast<object>().ToList();
        }

        public async Task<List<object>> GetSubscribableCalendarEventsJsonAsync(DateTime startDate, DateTime endDate, int? categoryId = null)
        {
            // Get ALL subscribable events (excludes Practice and Tentative, requires registration)
            // This includes Camp, Match, Formation, Tournament, Serie, FirstShift, Other, etc.
            var events = await _context.Events
                .Include(e => e.EventCategory)
                .Include(e => e.EventRegistrations.Where(er => er.IsActive && er.Status == RegistrationStatus.Confirmed))
                .AsSplitQuery()
                .Where(e => e.IsPublished && 
                           e.IsActive && 
                           e.RequiresRegistration &&
                           e.EventCategory.DisplayNameKey != "EventCategory_Practice" &&
                           e.EventCategory.DisplayNameKey != "EventCategory_Tentative" &&
                           e.StartDate >= startDate && e.StartDate <= endDate)
                .OrderBy(e => e.StartDate)
                .ToListAsync();

            // Apply category filter if specified
            if (categoryId.HasValue)
            {
                var categoryIds = _categoryFilterService.GetFilterCategoryIds(categoryId.Value);
                events = events.Where(e => categoryIds.Contains(e.EventCategoryId)).ToList();
            }
            
            return events.Select(e => new
            {
                id = e.Id,
                title = e.Title,
                start = e.StartDate.ToString("yyyy-MM-ddTHH:mm:ss"),
                end = e.EndDate.ToString("yyyy-MM-ddTHH:mm:ss"),
                allDay = e.IsAllDay,
                backgroundColor = e.EventCategory.Color,
                borderColor = e.EventCategory.Color,
                textColor = "#ffffff",
                description = e.Description ?? "",
                location = e.Location ?? "",
                category = e.EventCategory.DisplayNameKey,
                eventCategoryId = e.EventCategoryId,
                requiresRegistration = e.RequiresRegistration,
                maxParticipants = e.MaxParticipants,
                currentRegistrations = e.CurrentRegistrations,
                isRegistrationOpen = e.IsRegistrationOpen,
                extendedProps = new
                {
                    categoryId = e.EventCategoryId,
                    categoryIcon = e.EventCategory.IconClass,
                    priority = e.Priority
                }
            }).Cast<object>().ToList();
        }

        public async Task<List<Event>> GetEventsForDateRangeAsync(DateTime startDate, DateTime endDate)
        {
            return await _context.Events
                .Include(e => e.EventCategory)
                .Include(e => e.EventRegistrations)
                .ThenInclude(er => er.Member)
                .Where(e => e.IsActive && e.StartDate >= startDate && e.StartDate <= endDate)
                .OrderBy(e => e.StartDate)
                .ToListAsync();
        }

        public async Task<EventDetailsResponse?> GetEventDetailsWithUserContextAsync(int eventId, UserContext userContext)
        {
            // DEBUG: Log detailed UserContext information
            Console.WriteLine("🔍 EventService.GetEventDetailsWithUserContextAsync - DEBUGGING USER STATE:");
            Console.WriteLine($"   📋 EventId: {eventId}");
            Console.WriteLine($"   👤 UserContext.IsAdmin: {userContext.IsAdmin}");
            Console.WriteLine($"   🆔 UserContext.AdminId: {userContext.AdminId}");
            Console.WriteLine($"   👥 UserContext.MemberId: {userContext.MemberId}");
            Console.WriteLine($"   📝 UserContext.UserName: {userContext.UserName}");
            Console.WriteLine($"   🔄 UserContext.Source: {userContext.Source}");
            
            var eventEntity = await GetEventByIdAsync(eventId);
            if (eventEntity == null) 
            {
                Console.WriteLine($"   ❌ Event {eventId} not found");
                return null;
            }
            
            var response = new EventDetailsResponse
            {
                Id = eventEntity.Id,
                Title = eventEntity.Title,
                StartDate = eventEntity.StartDate,
                EndDate = eventEntity.EndDate,
                Location = eventEntity.Location,
                Description = eventEntity.Description,
                RequiresRegistration = eventEntity.RequiresRegistration,
                MaxParticipants = eventEntity.MaxParticipants,
                CurrentRegistrations = eventEntity.CurrentRegistrations,
                AvailableSpots = eventEntity.AvailableSpots,
                IsFull = eventEntity.IsFull,
                IsRegistrationOpen = eventEntity.IsRegistrationOpen,
                RegistrationDeadline = eventEntity.RegistrationDeadline,
                ContactPerson = eventEntity.ContactPerson,
                ContactEmail = eventEntity.ContactEmail,
                ContactPhone = eventEntity.ContactPhone,
                CategoryName = eventEntity.EventCategory?.DisplayNameKey,
                CategoryColor = eventEntity.EventCategory?.Color,
                IsAllDay = eventEntity.IsAllDay,
                DateRangeDisplay = eventEntity.DateRangeDisplay
            };
            
            // Determine user registration state with detailed debugging
            Console.WriteLine("🔍 DETERMINING USER STATE:");
            
            if (!userContext.IsAdmin && userContext.MemberId.HasValue)
            {
                Console.WriteLine($"   ✅ Branch: Member (IsAdmin=false, MemberId={userContext.MemberId})");
                var registration = await GetEventRegistrationAsync(eventId, userContext.MemberId.Value);
                Console.WriteLine($"   📋 Registration found: {registration != null}");
                
                response.IsUserRegistered = registration != null;
                response.UserState = registration != null 
                    ? UserRegistrationState.MemberRegistered 
                    : UserRegistrationState.Member;
                    
                Console.WriteLine($"   🎯 Final UserState: {response.UserState}");
                Console.WriteLine($"   🎯 IsUserRegistered: {response.IsUserRegistered}");
            }
            else if (userContext.IsAdmin)
            {
                Console.WriteLine($"   ✅ Branch: Admin (IsAdmin=true, AdminId={userContext.AdminId})");
                response.UserState = UserRegistrationState.Admin;
                Console.WriteLine($"   🎯 Final UserState: {response.UserState}");
            }
            else
            {
                Console.WriteLine($"   ✅ Branch: Visitor (IsAdmin=false, MemberId=null)");
                response.UserState = UserRegistrationState.Visitor;
                Console.WriteLine($"   🎯 Final UserState: {response.UserState}");
            }
            
            Console.WriteLine("🔍 EventService.GetEventDetailsWithUserContextAsync - DEBUG COMPLETE");
            return response;
        }

        public async Task<bool> CanUserRegisterAsync(int eventId, UserContext userContext)
        {
            // Admin check
            if (userContext.IsAdmin) return false;
            
            // Member check
            if (!userContext.MemberId.HasValue) return false;
            
            // Existing registration check
            var existing = await GetEventRegistrationAsync(eventId, userContext.MemberId.Value);
            if (existing != null) return false;
            
            // Event business rules
            var eventEntity = await GetEventByIdAsync(eventId);
            return eventEntity?.IsRegistrationOpen == true && !eventEntity.IsFull;
        }

        #endregion
    }
}