using ClosedXML.Excel;
using Microsoft.AspNetCore.Http;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Options;
using ParaHockeyApp.Models;
using ParaHockeyApp.Models.Entities;
using ParaHockeyApp.DTOs;
using System.Text.Json;

namespace ParaHockeyApp.Services
{
    /// <summary>
    /// Service for importing member data from Excel files using ClosedXML
    /// </summary>
    public class MemberImportService : IMemberImportService
    {
        private readonly ApplicationContext _context;
        private readonly INormalizationService _normalizationService;
        private readonly IDuplicateDetectionService _duplicateDetectionService;
        private readonly IAuditLogService _auditLogService;
        private readonly MemberImportConfig _config;

        public MemberImportService(
            ApplicationContext context,
            INormalizationService normalizationService,
            IDuplicateDetectionService duplicateDetectionService,
            IAuditLogService auditLogService,
            IOptions<MemberImportConfig> config)
        {
            _context = context;
            _normalizationService = normalizationService;
            _duplicateDetectionService = duplicateDetectionService;
            _auditLogService = auditLogService;
            _config = config.Value;
        }

        /// <summary>
        /// Validates an uploaded Excel file before processing
        /// </summary>
        public async Task<FileValidationResult> ValidateFileAsync(IFormFile file)
        {
            var result = new FileValidationResult
            {
                FileName = file.FileName,
                FileSizeBytes = file.Length
            };

            // Check file extension
            if (!Path.GetExtension(file.FileName).Equals(".xlsx", StringComparison.OrdinalIgnoreCase))
            {
                result.Errors.Add("File must be an Excel (.xlsx) file");
                return result;
            }

            // Check file size
            if (file.Length > _config.MaxFileSizeBytes)
            {
                result.Errors.Add($"File size ({file.Length / 1024 / 1024} MB) exceeds maximum allowed size ({_config.MaxFileSizeBytes / 1024 / 1024} MB)");
                return result;
            }

            // Check file content and headers
            try
            {
                using var stream = file.OpenReadStream();
                using var workbook = new XLWorkbook(stream);
                var worksheet = workbook.Worksheets.FirstOrDefault();

                if (worksheet == null)
                {
                    result.Errors.Add("Excel file must contain at least one worksheet");
                    return result;
                }

                // Check row count
                var lastRowUsed = worksheet.LastRowUsed()?.RowNumber() ?? 0;
                var dataRowCount = Math.Max(0, lastRowUsed - 1); // Subtract header row
                result.EstimatedRowCount = dataRowCount;

                if (dataRowCount > _config.MaxRowCount)
                {
                    result.Errors.Add($"File contains {dataRowCount} rows, which exceeds the maximum allowed ({_config.MaxRowCount} rows)");
                    return result;
                }

                if (dataRowCount == 0)
                {
                    result.Errors.Add("Excel file contains no data rows");
                    return result;
                }

                // Validate headers if required
                if (_config.ValidateHeaders)
                {
                    var headerErrors = ValidateHeaders(worksheet);
                    result.Errors.AddRange(headerErrors);
                }

                result.IsValid = result.Errors.Count == 0;
                return result;
            }
            catch (Exception ex)
            {
                result.Errors.Add($"Error reading Excel file: {ex.Message}");
                return result;
            }
        }

        /// <summary>
        /// Parses and stages member data from Excel file into TempMembers table
        /// </summary>
        public async Task<int> ParseAndStageAsync(IFormFile file, string uploadedBy)
        {
            // Validate file first
            var validationResult = await ValidateFileAsync(file);
            if (!validationResult.IsValid)
            {
                throw new InvalidOperationException($"File validation failed: {string.Join(", ", validationResult.Errors)}");
            }

            using var transaction = await _context.Database.BeginTransactionAsync();
            
            try
            {
                // Create import batch
                var importBatch = new MemberImportBatch
                {
                    FileName = file.FileName,
                    UploadedBy = uploadedBy,
                    UploadedAtUtc = DateTime.UtcNow,
                    TotalRows = validationResult.EstimatedRowCount,
                    Status = "Processing",
                    ConfigurationJson = JsonSerializer.Serialize(_config)
                };

                _context.MemberImportBatches.Add(importBatch);
                await _context.SaveChangesAsync();

                // Parse Excel file and process rows
                using var stream = file.OpenReadStream();
                using var workbook = new XLWorkbook(stream);
                var worksheet = workbook.Worksheets.First();

                var tempMembers = await ParseWorksheetAsync(worksheet, importBatch.Id);
                
                // Process temp members in batches
                await ProcessTempMembersInBatchesAsync(tempMembers, uploadedBy);

                // Update batch statistics
                await UpdateBatchStatisticsAsync(importBatch.Id);

                importBatch.Status = "Completed";
                await _context.SaveChangesAsync();

                await transaction.CommitAsync();

                // Log the import operation
                await _auditLogService.LogActionAsync(
                    $"Imported {tempMembers.Count} members from file '{file.FileName}'", 
                    null, 
                    ActionSource.AdminPanel, 
                    uploadedBy, 
                    null);

                return importBatch.Id;
            }
            catch (Exception ex)
            {
                await transaction.RollbackAsync();
                
                // Update batch with error if it was created
                var batch = await _context.MemberImportBatches
                    .Where(b => b.UploadedBy == uploadedBy && b.FileName == file.FileName)
                    .OrderByDescending(b => b.UploadedAtUtc)
                    .FirstOrDefaultAsync();
                
                if (batch != null)
                {
                    batch.Status = "Failed";
                    batch.ErrorMessage = ex.Message;
                    await _context.SaveChangesAsync();
                }

                throw;
            }
        }

        /// <summary>
        /// Gets summary information for an import batch
        /// </summary>
        public async Task<ImportBatchSummary> GetBatchSummaryAsync(int batchId)
        {
            var batch = await _context.MemberImportBatches
                .FirstOrDefaultAsync(b => b.Id == batchId);

            if (batch == null)
                throw new ArgumentException($"Import batch {batchId} not found");

            // Get current status counts from temp members
            var statusCounts = await _context.TempMembers
                .Where(tm => tm.ImportBatchId == batchId)
                .GroupBy(tm => tm.Status)
                .Select(g => new { Status = g.Key, Count = g.Count() })
                .ToListAsync();

            var summary = new ImportBatchSummary
            {
                ImportBatchId = batch.Id,
                FileName = batch.FileName,
                UploadedAtUtc = batch.UploadedAtUtc,
                UploadedBy = batch.UploadedBy,
                TotalRows = batch.TotalRows,
                Status = batch.Status,
                ErrorMessage = batch.ErrorMessage
            };

            // Map status counts
            foreach (var statusCount in statusCounts)
            {
                switch (statusCount.Status)
                {
                    case TempMemberStatus.Created:
                        summary.CreatedCount = statusCount.Count;
                        break;
                    case TempMemberStatus.Duplicate:
                        summary.DuplicateCount = statusCount.Count;
                        break;
                    case TempMemberStatus.NeedsFix:
                        summary.NeedsFixCount = statusCount.Count;
                        break;
                    case TempMemberStatus.Merged:
                        summary.MergedCount = statusCount.Count;
                        break;
                    case TempMemberStatus.Rejected:
                        summary.RejectedCount = statusCount.Count;
                        break;
                    case TempMemberStatus.ReadyToCreate:
                        summary.ReadyToCreateCount = statusCount.Count;
                        break;
                }
            }

            return summary;
        }

        /// <summary>
        /// Gets temp members by status for queue processing
        /// </summary>
        public async Task<List<TempMember>> GetQueueAsync(int batchId, TempMemberStatus status)
        {
            return await _context.TempMembers
                .Where(tm => tm.ImportBatchId == batchId && tm.Status == status)
                .Include(tm => tm.ExistingMember)
                .OrderBy(tm => tm.DateCreated)
                .ToListAsync();
        }

        /// <summary>
        /// Updates batch statistics after processing temp members
        /// </summary>
        public async Task UpdateBatchStatisticsAsync(int batchId)
        {
            var batch = await _context.MemberImportBatches
                .FirstOrDefaultAsync(b => b.Id == batchId);

            if (batch == null)
                return;

            var statusCounts = await _context.TempMembers
                .Where(tm => tm.ImportBatchId == batchId)
                .GroupBy(tm => tm.Status)
                .Select(g => new { Status = g.Key, Count = g.Count() })
                .ToListAsync();

            // Reset counts
            batch.CreatedCount = 0;
            batch.DuplicateCount = 0;
            batch.NeedsFixCount = 0;
            batch.MergedCount = 0;
            batch.RejectedCount = 0;

            // Update counts based on current status
            foreach (var statusCount in statusCounts)
            {
                switch (statusCount.Status)
                {
                    case TempMemberStatus.Created:
                        batch.CreatedCount = statusCount.Count;
                        break;
                    case TempMemberStatus.Duplicate:
                        batch.DuplicateCount = statusCount.Count;
                        break;
                    case TempMemberStatus.NeedsFix:
                        batch.NeedsFixCount = statusCount.Count;
                        break;
                    case TempMemberStatus.Merged:
                        batch.MergedCount = statusCount.Count;
                        break;
                    case TempMemberStatus.Rejected:
                        batch.RejectedCount = statusCount.Count;
                        break;
                }
            }

            await _context.SaveChangesAsync();
        }

        /// <summary>
        /// Parses worksheet data into TempMember objects
        /// </summary>
        private async Task<List<TempMember>> ParseWorksheetAsync(IXLWorksheet worksheet, int importBatchId)
        {
            var tempMembers = new List<TempMember>();
            var headers = GetHeaderMapping(worksheet);

            var lastRowUsed = worksheet.LastRowUsed()?.RowNumber() ?? 1;
            
            // Skip header row, start from row 2
            for (int rowNumber = 2; rowNumber <= lastRowUsed; rowNumber++)
            {
                var row = worksheet.Row(rowNumber);
                var tempMember = ParseRowToTempMember(row, headers, importBatchId, rowNumber);
                tempMembers.Add(tempMember);
            }

            return tempMembers;
        }

        /// <summary>
        /// Creates header mapping from Excel worksheet
        /// </summary>
        private Dictionary<string, int> GetHeaderMapping(IXLWorksheet worksheet)
        {
            var headerMapping = new Dictionary<string, int>(StringComparer.OrdinalIgnoreCase);
            var headerRow = worksheet.Row(1);

            var lastColumnUsed = worksheet.LastColumnUsed()?.ColumnNumber() ?? 1;
            
            for (int col = 1; col <= lastColumnUsed; col++)
            {
                var cellValue = headerRow.Cell(col).GetString().Trim();
                if (!string.IsNullOrEmpty(cellValue))
                {
                    headerMapping[cellValue] = col;
                }
            }

            return headerMapping;
        }

        /// <summary>
        /// Parses a single Excel row into a TempMember object
        /// </summary>
        private TempMember ParseRowToTempMember(IXLRow row, Dictionary<string, int> headers, int importBatchId, int rowNumber)
        {
            var tempMember = new TempMember
            {
                ImportBatchId = importBatchId,
                Status = TempMemberStatus.Imported,
                DateCreated = DateTime.UtcNow,
                CreatedBySource = ActionSource.AdminPanel
            };

            // Store raw data for traceability
            var rawData = new Dictionary<string, string>();
            foreach (var header in headers)
            {
                var cellValue = row.Cell(header.Value).GetString();
                rawData[header.Key] = cellValue;
            }
            tempMember.RawSourceJson = JsonSerializer.Serialize(rawData);

            // Parse standard fields using French column names
            tempMember.FirstName = GetCellValue(row, headers, "Prénom") ?? "";
            tempMember.LastName = GetCellValue(row, headers, "Nom") ?? "";
            tempMember.Email = GetCellValue(row, headers, "Courriel");
            tempMember.Phone = GetCellValue(row, headers, "Numéro de téléphone");
            
            // Parse address fields
            var streetNumber = GetCellValue(row, headers, "Numéro de rue");
            var street = GetCellValue(row, headers, "Rue");
            var unitNumber = GetCellValue(row, headers, "Numéro d'unité");
            
            // Combine address components
            var addressParts = new List<string>();
            if (!string.IsNullOrWhiteSpace(unitNumber))
                addressParts.Add(unitNumber);
            if (!string.IsNullOrWhiteSpace(streetNumber))
                addressParts.Add(streetNumber);
            if (!string.IsNullOrWhiteSpace(street))
                addressParts.Add(street);
            tempMember.Address = string.Join(" ", addressParts);
            
            tempMember.City = GetCellValue(row, headers, "Ville");
            tempMember.PostalCode = GetCellValue(row, headers, "Code Postal");

            // Parse date of birth
            var dobString = GetCellValue(row, headers, "Date de naissance");
            if (!string.IsNullOrWhiteSpace(dobString))
            {
                if (DateTime.TryParse(dobString, out var dateOfBirth))
                {
                    tempMember.DateOfBirth = dateOfBirth;
                }
            }

            // Store lookup field text values using French column names
            tempMember.GenderText = GetCellValue(row, headers, "Identité de genre");
            tempMember.ProvinceText = GetCellValue(row, headers, "Province");
            tempMember.RegistrationTypeText = GetCellValue(row, headers, "Statut du membre");
            
            // Store HCR number for matching with parent/emergency contact files
            tempMember.HcrNumber = GetCellValue(row, headers, "Numéro HCR");

            // Apply normalization
            tempMember = _normalizationService.NormalizeTempMember(tempMember);

            return tempMember;
        }

        /// <summary>
        /// Gets cell value from row using header mapping
        /// </summary>
        private string? GetCellValue(IXLRow row, Dictionary<string, int> headers, string headerName)
        {
            if (headers.TryGetValue(headerName, out var columnIndex))
            {
                var cellValue = row.Cell(columnIndex).GetString().Trim();
                return string.IsNullOrEmpty(cellValue) ? null : cellValue;
            }
            return null;
        }

        /// <summary>
        /// Processes temp members in batches for performance
        /// </summary>
        private async Task ProcessTempMembersInBatchesAsync(List<TempMember> tempMembers, string uploadedBy)
        {
            for (int i = 0; i < tempMembers.Count; i += _config.BatchSize)
            {
                var batch = tempMembers.Skip(i).Take(_config.BatchSize).ToList();
                
                foreach (var tempMember in batch)
                {
                    // Validate temp member
                    var validationErrors = ValidateTempMember(tempMember);
                    if (validationErrors.Any())
                    {
                        tempMember.Status = TempMemberStatus.NeedsFix;
                        tempMember.ValidationErrorsJson = JsonSerializer.Serialize(validationErrors);
                    }
                    else
                    {
                        // Check for duplicates
                        var existingMember = await _duplicateDetectionService.FindExistingMemberAsync(tempMember);
                        if (existingMember != null)
                        {
                            tempMember.Status = TempMemberStatus.Duplicate;
                            tempMember.ExistingMemberId = existingMember.Id;
                        }
                        else
                        {
                            tempMember.Status = TempMemberStatus.ReadyToCreate;
                        }
                    }
                }

                // Save batch to database
                _context.TempMembers.AddRange(batch);
                await _context.SaveChangesAsync();
            }
        }

        /// <summary>
        /// Validates a temp member and returns list of errors
        /// </summary>
        private List<string> ValidateTempMember(TempMember tempMember)
        {
            var errors = new List<string>();

            // Required fields validation
            if (string.IsNullOrWhiteSpace(tempMember.FirstName))
                errors.Add("First Name is required");

            if (string.IsNullOrWhiteSpace(tempMember.LastName))
                errors.Add("Last Name is required");

            // Minimum identification requirement: Email OR (Date of Birth + Phone)
            var hasEmail = !string.IsNullOrWhiteSpace(tempMember.Email);
            var hasDobAndPhone = tempMember.DateOfBirth.HasValue && !string.IsNullOrWhiteSpace(tempMember.Phone);

            if (!hasEmail && !hasDobAndPhone)
            {
                errors.Add("Either Email OR (Date of Birth + Phone) is required for member identification");
            }

            // Email format validation
            if (!string.IsNullOrWhiteSpace(tempMember.Email))
            {
                var emailValidator = new System.ComponentModel.DataAnnotations.EmailAddressAttribute();
                if (!emailValidator.IsValid(tempMember.Email))
                {
                    errors.Add("Email format is invalid");
                }
            }

            // Date of birth validation
            if (tempMember.DateOfBirth.HasValue)
            {
                if (tempMember.DateOfBirth.Value > DateTime.Today)
                {
                    errors.Add("Date of Birth cannot be in the future");
                }
                if (tempMember.DateOfBirth.Value < DateTime.Today.AddYears(-120))
                {
                    errors.Add("Date of Birth is too far in the past");
                }
            }

            return errors;
        }

        /// <summary>
        /// Validates Excel headers against required/optional lists
        /// </summary>
        private List<string> ValidateHeaders(IXLWorksheet worksheet)
        {
            var errors = new List<string>();
            var headerRow = worksheet.Row(1);
            var lastColumnUsed = worksheet.LastColumnUsed()?.ColumnNumber() ?? 0;
            
            var foundHeaders = new List<string>();
            for (int col = 1; col <= lastColumnUsed; col++)
            {
                var cellValue = headerRow.Cell(col).GetString().Trim();
                if (!string.IsNullOrEmpty(cellValue))
                {
                    foundHeaders.Add(cellValue);
                }
            }

            // Check for required headers
            foreach (var requiredHeader in _config.RequiredHeaders)
            {
                if (!foundHeaders.Contains(requiredHeader, StringComparer.OrdinalIgnoreCase))
                {
                    errors.Add($"Required header '{requiredHeader}' is missing");
                }
            }

            return errors;
        }

        /// <summary>
        /// Processes multi-file import with relationship matching
        /// </summary>
        public async Task<(int batchId, RelationshipMatchingResult matchingResult)> ProcessMultiFileImportAsync(MultiFileImportRequest request, string uploadedBy)
        {
            // First process the main member file
            int batchId = await ParseAndStageAsync(request.MemberFile!, uploadedBy);

            // Process relationship files and match with members
            var matchingResult = await ProcessRelationshipFilesAsync(batchId, request);

            // Update batch with relationship data
            await UpdateTempMembersWithRelationshipDataAsync(batchId, matchingResult);

            return (batchId, matchingResult);
        }

        /// <summary>
        /// Validates multiple files for multi-file import
        /// </summary>
        public async Task<MultiFileValidationResult> ValidateMultiFileImportAsync(MultiFileImportRequest request)
        {
            var result = new MultiFileValidationResult();

            // Validate member file (required)
            if (request.MemberFile != null)
            {
                result.MemberFileResult = await ValidateFileAsync(request.MemberFile);
            }
            else
            {
                result.GeneralErrors.Add("Member file is required for multi-file import");
            }

            // Validate parent file (optional)
            if (request.ParentFile != null)
            {
                result.ParentFileResult = await ValidateRelationshipFileAsync(request.ParentFile, ImportFileType.Parent);
            }

            // Validate emergency contact file (optional)
            if (request.EmergencyContactFile != null)
            {
                result.EmergencyContactFileResult = await ValidateRelationshipFileAsync(request.EmergencyContactFile, ImportFileType.EmergencyContact);
            }

            // Check if at least one relationship file is provided
            if (request.ParentFile == null && request.EmergencyContactFile == null)
            {
                result.GeneralErrors.Add("At least one relationship file (parent or emergency contact) must be provided");
            }

            result.IsValid = result.AllErrors.Count == 0;
            return result;
        }

        /// <summary>
        /// Gets unmatched relationship records for manual review
        /// </summary>
        public async Task<List<UnmatchedRelationshipRecord>> GetUnmatchedRelationshipRecordsAsync(int batchId)
        {
            var unmatchedRecords = new List<UnmatchedRelationshipRecord>();

            // This would be populated during the multi-file processing
            // For now, return empty list - in real implementation, you'd store unmatched records in database
            return unmatchedRecords;
        }

        /// <summary>
        /// Processes relationship files and creates matching data
        /// </summary>
        private async Task<RelationshipMatchingResult> ProcessRelationshipFilesAsync(int batchId, MultiFileImportRequest request)
        {
            var result = new RelationshipMatchingResult();
            var memberRelationships = new Dictionary<string, ImportMemberWithRelationships>();

            // Get all temp members from the batch
            var tempMembers = await _context.TempMembers
                .Where(tm => tm.ImportBatchId == batchId)
                .ToListAsync();

            result.TotalMemberRecords = tempMembers.Count;

            // Initialize relationship containers for each member
            foreach (var member in tempMembers)
            {
                if (!string.IsNullOrEmpty(member.HcrNumber))
                {
                    memberRelationships[member.HcrNumber] = new ImportMemberWithRelationships
                    {
                        HcrNumber = member.HcrNumber
                    };
                }
            }

            // Process parent file if provided
            if (request.ParentFile != null)
            {
                await ProcessParentFileAsync(request.ParentFile, memberRelationships, result);
            }

            // Process emergency contact file if provided
            if (request.EmergencyContactFile != null)
            {
                await ProcessEmergencyContactFileAsync(request.EmergencyContactFile, memberRelationships, result);
            }

            // Count matched relationships
            foreach (var relationship in memberRelationships.Values)
            {
                if (relationship.Parents.Any())
                    result.MembersWithParents++;
                if (relationship.EmergencyContacts.Any())
                    result.MembersWithEmergencyContacts++;
            }

            return result;
        }

        /// <summary>
        /// Validates a relationship file (parent or emergency contact)
        /// </summary>
        private async Task<FileValidationResult> ValidateRelationshipFileAsync(IFormFile file, ImportFileType fileType)
        {
            var result = new FileValidationResult
            {
                FileName = file.FileName,
                FileSizeBytes = file.Length
            };

            // Check file extension
            if (!Path.GetExtension(file.FileName).Equals(".csv", StringComparison.OrdinalIgnoreCase) &&
                !Path.GetExtension(file.FileName).Equals(".xlsx", StringComparison.OrdinalIgnoreCase))
            {
                result.Errors.Add("Relationship files must be CSV (.csv) or Excel (.xlsx) files");
                return result;
            }

            // Check file size
            if (file.Length > _config.MaxFileSizeBytes)
            {
                result.Errors.Add($"File size ({file.Length:N0} bytes) exceeds maximum allowed size ({_config.MaxFileSizeBytes:N0} bytes)");
                return result;
            }

            if (file.Length == 0)
            {
                result.Errors.Add("File is empty");
                return result;
            }

            result.IsValid = result.Errors.Count == 0;
            return result;
        }

        /// <summary>
        /// Processes parent CSV file and extracts parent relationships
        /// </summary>
        private async Task ProcessParentFileAsync(IFormFile parentFile, Dictionary<string, ImportMemberWithRelationships> memberRelationships, RelationshipMatchingResult result)
        {
            using var reader = new StreamReader(parentFile.OpenReadStream());
            var csvContent = await reader.ReadToEndAsync();
            var lines = csvContent.Split('\n', StringSplitOptions.RemoveEmptyEntries);

            if (lines.Length <= 1) return; // No data rows

            // Parse header to find column indices
            var header = lines[0];
            var hcrIndex = GetCsvColumnIndex(header, "Numéro HCR");
            
            // Handle both structured parent columns and individual parent rows
            var parent1FirstNameIndex = GetCsvColumnIndex(header, "Parent 1 - Prénom");
            var parent1LastNameIndex = GetCsvColumnIndex(header, "Parent 1 - Nom");
            var parent1EmailIndex = GetCsvColumnIndex(header, "Parent 1 - Courriel");
            var parent1PhoneIndex = GetCsvColumnIndex(header, "Parent 1 - Téléphone");
            
            var parent2FirstNameIndex = GetCsvColumnIndex(header, "Parent 2 - Prénom");
            var parent2LastNameIndex = GetCsvColumnIndex(header, "Parent 2 - Nom");
            var parent2EmailIndex = GetCsvColumnIndex(header, "Parent 2 - Courriel");
            var parent2PhoneIndex = GetCsvColumnIndex(header, "Parent 2 - Téléphone");
            
            // Individual parent row format
            var parentTypeIndex = GetCsvColumnIndex(header, "Type de parent");
            var parentFirstNameIndex = GetCsvColumnIndex(header, "Prénom");
            var parentLastNameIndex = GetCsvColumnIndex(header, "Nom");
            var parentPhoneIndex = GetCsvColumnIndex(header, "Numéro de téléphone");
            var parentEmailIndex = GetCsvColumnIndex(header, "Courriel");

            // Process data rows
            for (int i = 1; i < lines.Length; i++)
            {
                var columns = ParseCsvLine(lines[i]);
                var hcrNumber = GetCsvValue(columns, hcrIndex);

                if (string.IsNullOrEmpty(hcrNumber)) continue;

                if (memberRelationships.ContainsKey(hcrNumber))
                {
                    // Check for structured parent data (Parent 1 and Parent 2 columns)
                    if (parent1FirstNameIndex >= 0 || parent1LastNameIndex >= 0)
                    {
                        // Add parent 1 if data exists
                        var parent1FirstName = GetCsvValue(columns, parent1FirstNameIndex);
                        var parent1LastName = GetCsvValue(columns, parent1LastNameIndex);
                        
                        if (!string.IsNullOrEmpty(parent1FirstName) || !string.IsNullOrEmpty(parent1LastName))
                        {
                            memberRelationships[hcrNumber].Parents.Add(new ImportParentData
                            {
                                FirstName = parent1FirstName,
                                LastName = parent1LastName,
                                Email = GetCsvValue(columns, parent1EmailIndex),
                                Phone = GetCsvValue(columns, parent1PhoneIndex),
                                ParentType = "Parent 1"
                            });
                        }
                        
                        // Add parent 2 if data exists
                        var parent2FirstName = GetCsvValue(columns, parent2FirstNameIndex);
                        var parent2LastName = GetCsvValue(columns, parent2LastNameIndex);
                        
                        if (!string.IsNullOrEmpty(parent2FirstName) || !string.IsNullOrEmpty(parent2LastName))
                        {
                            memberRelationships[hcrNumber].Parents.Add(new ImportParentData
                            {
                                FirstName = parent2FirstName,
                                LastName = parent2LastName,
                                Email = GetCsvValue(columns, parent2EmailIndex),
                                Phone = GetCsvValue(columns, parent2PhoneIndex),
                                ParentType = "Parent 2"
                            });
                        }
                    }
                    
                    // Check for individual parent row format
                    if (parentTypeIndex >= 0)
                    {
                        var parentType = GetCsvValue(columns, parentTypeIndex);
                        var parentFirstName = GetCsvValue(columns, parentFirstNameIndex);
                        var parentLastName = GetCsvValue(columns, parentLastNameIndex);
                        
                        if (!string.IsNullOrEmpty(parentFirstName) || !string.IsNullOrEmpty(parentLastName))
                        {
                            memberRelationships[hcrNumber].Parents.Add(new ImportParentData
                            {
                                FirstName = parentFirstName,
                                LastName = parentLastName,
                                Email = GetCsvValue(columns, parentEmailIndex),
                                Phone = GetCsvValue(columns, parentPhoneIndex),
                                ParentType = parentType ?? "Parent"
                            });
                        }
                    }
                }
                else
                {
                    result.UnmatchedParentRecords++;
                    result.UnmatchedHcrNumbers.Add(hcrNumber);
                }
            }
        }

        /// <summary>
        /// Processes emergency contact CSV file and extracts emergency contact relationships
        /// </summary>
        private async Task ProcessEmergencyContactFileAsync(IFormFile contactFile, Dictionary<string, ImportMemberWithRelationships> memberRelationships, RelationshipMatchingResult result)
        {
            using var reader = new StreamReader(contactFile.OpenReadStream());
            var csvContent = await reader.ReadToEndAsync();
            var lines = csvContent.Split('\n', StringSplitOptions.RemoveEmptyEntries);

            if (lines.Length <= 1) return; // No data rows

            // Parse header to find column indices
            var header = lines[0];
            var hcrIndex = GetCsvColumnIndex(header, "Numéro HCR");
            var contactFirstNameIndex = GetCsvColumnIndex(header, "Prénom");
            var contactLastNameIndex = GetCsvColumnIndex(header, "Nom");
            var relationshipIndex = GetCsvColumnIndex(header, "Relation avec l'inscrit");
            var contactPhoneIndex = GetCsvColumnIndex(header, "Numéro de téléphone");
            var contactEmailIndex = GetCsvColumnIndex(header, "Courriel");

            // Process data rows
            for (int i = 1; i < lines.Length; i++)
            {
                var columns = ParseCsvLine(lines[i]);
                var hcrNumber = GetCsvValue(columns, hcrIndex);

                if (string.IsNullOrEmpty(hcrNumber)) continue;

                if (memberRelationships.ContainsKey(hcrNumber))
                {
                    var contactFirstName = GetCsvValue(columns, contactFirstNameIndex);
                    var contactLastName = GetCsvValue(columns, contactLastNameIndex);
                    
                    if (!string.IsNullOrEmpty(contactFirstName) || !string.IsNullOrEmpty(contactLastName))
                    {
                        memberRelationships[hcrNumber].EmergencyContacts.Add(new ImportEmergencyContactData
                        {
                            FirstName = contactFirstName,
                            LastName = contactLastName,
                            Email = GetCsvValue(columns, contactEmailIndex),
                            Phone = GetCsvValue(columns, contactPhoneIndex),
                            Relationship = GetCsvValue(columns, relationshipIndex)
                        });
                    }
                }
                else
                {
                    result.UnmatchedEmergencyContactRecords++;
                    result.UnmatchedHcrNumbers.Add(hcrNumber);
                }
            }
        }

        /// <summary>
        /// Updates temp members with relationship data in JSON format
        /// </summary>
        private async Task UpdateTempMembersWithRelationshipDataAsync(int batchId, RelationshipMatchingResult matchingResult)
        {
            // This method would update the ParentData and EmergencyContactData JSON fields
            // in the TempMember records based on the processed relationship data
            
            // For now, we'll skip the actual implementation as it requires the relationship data
            // to be passed through the matching result or stored temporarily
            
            await Task.CompletedTask;
        }

        /// <summary>
        /// Helper method to find column index in CSV header
        /// </summary>
        private int GetCsvColumnIndex(string header, string columnName)
        {
            var columns = ParseCsvLine(header);
            for (int i = 0; i < columns.Length; i++)
            {
                if (columns[i].Equals(columnName, StringComparison.OrdinalIgnoreCase))
                    return i;
            }
            return -1;
        }

        /// <summary>
        /// Helper method to parse CSV line handling quoted values
        /// </summary>
        private string[] ParseCsvLine(string line)
        {
            // Simple CSV parsing - in production, use a proper CSV library
            return line.Split(',').Select(s => s.Trim().Trim('"')).ToArray();
        }

        /// <summary>
        /// Helper method to safely get CSV value by index
        /// </summary>
        private string GetCsvValue(string[] columns, int index)
        {
            return index >= 0 && index < columns.Length ? columns[index] : string.Empty;
        }
    }
}