using ClosedXML.Excel;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Options;
using ParaHockeyApp.Models;
using ParaHockeyApp.Models.Entities;
using System.Text.Json;

namespace ParaHockeyApp.Services
{
    /// <summary>
    /// Service for processing member import files (CSV/Excel)
    /// </summary>
    public class MemberImportService : IMemberImportService
    {
        private readonly ApplicationContext _context;
        private readonly INormalizationService _normalizationService;
        private readonly IDuplicateDetectionService _duplicateDetectionService;
        private readonly IAuditLogService _auditLogService;
        private readonly IConfiguration _configuration;

        public MemberImportService(
            ApplicationContext context,
            INormalizationService normalizationService,
            IDuplicateDetectionService duplicateDetectionService,
            IAuditLogService auditLogService,
            IConfiguration configuration)
        {
            _context = context;
            _normalizationService = normalizationService;
            _duplicateDetectionService = duplicateDetectionService;
            _auditLogService = auditLogService;
            _configuration = configuration;
        }

        /// <summary>
        /// Parses Excel/CSV file and stages data in TempMembers table
        /// </summary>
        public async Task<Guid> ParseAndStageAsync(IFormFile file, string uploadedBy)
        {
            if (!await ValidateFileAsync(file))
                throw new InvalidOperationException("File validation failed");

            // Create import batch
            var batch = new MemberImportBatch
            {
                FileName = file.FileName,
                UploadedBy = uploadedBy,
                UploadedAtUtc = DateTime.UtcNow,
                Status = "Processing"
            };

            _context.MemberImportBatches.Add(batch);
            await _context.SaveChangesAsync();

            try
            {
                using var stream = file.OpenReadStream();
                using var workbook = new XLWorkbook(stream);
                var worksheet = workbook.Worksheets.First();

                var tempMembers = new List<TempMember>();
                var rowNumber = 2; // Skip header row

                foreach (var row in worksheet.RowsUsed().Skip(1)) // Skip header
                {
                    try
                    {
                        var tempMember = await ParseRowToTempMemberAsync(row, batch.Id, rowNumber);
                        if (tempMember != null)
                        {
                            tempMembers.Add(tempMember);
                        }
                    }
                    catch (Exception ex)
                    {
                        // Log row parsing error but continue
                        Console.WriteLine($"Error parsing row {rowNumber}: {ex.Message}");
                    }
                    
                    rowNumber++;
                }

                // Batch insert temp members
                if (tempMembers.Any())
                {
                    _context.TempMembers.AddRange(tempMembers);
                    await _context.SaveChangesAsync();

                    // Update batch statistics
                    batch.TotalRows = tempMembers.Count;
                    await UpdateBatchStatisticsInternalAsync(batch);
                }

                batch.Status = "Completed";
                await _context.SaveChangesAsync();

                return batch.ImportBatchId;
            }
            catch (Exception ex)
            {
                batch.Status = "Failed";
                batch.ErrorMessage = ex.Message;
                await _context.SaveChangesAsync();
                throw;
            }
        }

        /// <summary>
        /// Parses a single Excel row to TempMember
        /// </summary>
        private async Task<TempMember?> ParseRowToTempMemberAsync(IXLRow row, int batchId, int rowNumber)
        {
            try
            {
                // Create raw data dictionary for traceability
                var rawData = new Dictionary<string, string>();
                for (int col = 1; col <= row.CellsUsed().Count(); col++)
                {
                    var cell = row.Cell(col);
                    rawData[$"Col{col}"] = cell.GetString();
                }

                // Map CSV columns based on header analysis
                var tempMember = new TempMember
                {
                    ImportBatchId = batchId,
                    HcrNumber = GetCellValue(row, 1), // Column A: Numéro HCR
                    FirstName = GetCellValue(row, 2) ?? "", // Column B: Prénom
                    LastName = GetCellValue(row, 3) ?? "", // Column C: Nom
                    Email = GetCellValue(row, 4), // Column D: Courriel
                    GenderText = GetCellValue(row, 6), // Column F: Identité de genre
                    Phone = GetCellValue(row, 16), // Column P: Numéro de téléphone
                    City = GetCellValue(row, 12), // Column L: Ville
                    ProvinceText = GetCellValue(row, 13), // Column M: Province
                    PostalCode = GetCellValue(row, 15), // Column O: Code Postal
                    Status = TempMemberStatus.Imported,
                    RawSourceJson = JsonSerializer.Serialize(rawData)
                };

                // Parse date of birth (Column E)
                var dobString = GetCellValue(row, 5);
                if (!string.IsNullOrEmpty(dobString) && DateTime.TryParse(dobString, out var dob))
                {
                    tempMember.DateOfBirth = dob;
                }

                // Combine address parts (Columns J + K + L)
                var unitNumber = GetCellValue(row, 9); // Numéro d'unité
                var streetNumber = GetCellValue(row, 10); // Numéro de rue
                var streetName = GetCellValue(row, 11); // Rue
                tempMember.Address = _normalizationService.NormalizeAddress(unitNumber, streetNumber, streetName);

                // Normalize the temp member data
                _normalizationService.NormalizeTempMember(tempMember);

                // Validate and set status
                await ValidateAndSetStatusAsync(tempMember);

                return tempMember;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error parsing row {rowNumber}: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// Gets cell value as string, handling empty cells
        /// </summary>
        private string? GetCellValue(IXLRow row, int columnNumber)
        {
            try
            {
                var cell = row.Cell(columnNumber);
                var value = cell.GetString()?.Trim();
                return string.IsNullOrEmpty(value) ? null : value;
            }
            catch
            {
                return null;
            }
        }

        /// <summary>
        /// Validates temp member and sets appropriate status
        /// </summary>
        private async Task ValidateAndSetStatusAsync(TempMember tempMember)
        {
            var validationErrors = new List<string>();

            // Require either Email OR (Date of Birth + Phone) as minimum identification
            var hasEmail = !string.IsNullOrEmpty(tempMember.Email);
            var hasDobAndPhone = tempMember.DateOfBirth.HasValue && !string.IsNullOrEmpty(tempMember.Phone);

            if (!hasEmail && !hasDobAndPhone)
            {
                validationErrors.Add("Must have either Email OR (Date of Birth + Phone) for identification");
            }

            // Require FirstName and LastName
            if (string.IsNullOrEmpty(tempMember.FirstName))
                validationErrors.Add("FirstName is required");

            if (string.IsNullOrEmpty(tempMember.LastName))
                validationErrors.Add("LastName is required");

            // If there are validation errors, mark as NeedsFix
            if (validationErrors.Any())
            {
                tempMember.Status = TempMemberStatus.NeedsFix;
                tempMember.ValidationErrorsJson = JsonSerializer.Serialize(validationErrors);
                return;
            }

            // Check for duplicates
            var existingMember = await _duplicateDetectionService.FindExistingMemberAsync(tempMember);
            if (existingMember != null)
            {
                tempMember.Status = TempMemberStatus.Duplicate;
                tempMember.ExistingMemberId = existingMember.Id;
            }
            else
            {
                tempMember.Status = TempMemberStatus.ReadyToCreate;
            }
        }

        /// <summary>
        /// Validates file format and size before processing
        /// </summary>
        public async Task<bool> ValidateFileAsync(IFormFile file)
        {
            if (file == null || file.Length == 0)
                return false;

            // Check file extension
            var allowedExtensions = new[] { ".xlsx", ".xls", ".csv" };
            var extension = Path.GetExtension(file.FileName).ToLowerInvariant();
            if (!allowedExtensions.Contains(extension))
                return false;

            // Check file size (default 10MB)
            var maxFileSizeBytes = _configuration.GetValue<long>("MemberImport:MaxFileSizeBytes", 10 * 1024 * 1024);
            if (file.Length > maxFileSizeBytes)
                return false;

            return true;
        }

        /// <summary>
        /// Gets summary statistics for an import batch
        /// </summary>
        public async Task<ImportBatchSummary> GetBatchSummaryAsync(int batchId)
        {
            var batch = await _context.MemberImportBatches
                .FirstOrDefaultAsync(b => b.Id == batchId);

            if (batch == null)
                throw new ArgumentException("Batch not found", nameof(batchId));

            return new ImportBatchSummary
            {
                ImportBatchId = batch.Id,
                ImportBatchGuid = batch.ImportBatchId,
                FileName = batch.FileName,
                UploadedAtUtc = batch.UploadedAtUtc,
                UploadedBy = batch.UploadedBy,
                TotalRows = batch.TotalRows,
                CreatedCount = batch.CreatedCount,
                DuplicateCount = batch.DuplicateCount,
                NeedsFixCount = batch.NeedsFixCount,
                MergedCount = batch.MergedCount,
                RejectedCount = batch.RejectedCount,
                Status = batch.Status,
                ErrorMessage = batch.ErrorMessage,
                ReadyToCreateCount = await _context.TempMembers
                    .CountAsync(tm => tm.ImportBatchId == batchId && tm.Status == TempMemberStatus.ReadyToCreate)
            };
        }

        /// <summary>
        /// Gets temp members in a specific queue (status)
        /// </summary>
        public async Task<List<TempMember>> GetQueueAsync(int batchId, TempMemberStatus status)
        {
            return await _context.TempMembers
                .Where(tm => tm.ImportBatchId == batchId && tm.Status == status)
                .Include(tm => tm.ExistingMember)
                .OrderBy(tm => tm.LastName)
                .ThenBy(tm => tm.FirstName)
                .ToListAsync();
        }

        /// <summary>
        /// Gets list of all import batches for admin overview
        /// </summary>
        public async Task<List<ImportBatchSummary>> GetAllBatchesAsync()
        {
            var batches = await _context.MemberImportBatches
                .OrderByDescending(b => b.UploadedAtUtc)
                .ToListAsync();

            var summaries = new List<ImportBatchSummary>();
            foreach (var batch in batches)
            {
                summaries.Add(await GetBatchSummaryAsync(batch.Id));
            }

            return summaries;
        }

        /// <summary>
        /// Updates batch statistics after processing operations
        /// </summary>
        public async Task UpdateBatchStatisticsAsync(int batchId)
        {
            var batch = await _context.MemberImportBatches
                .FirstOrDefaultAsync(b => b.Id == batchId);

            if (batch != null)
            {
                await UpdateBatchStatisticsInternalAsync(batch);
                await _context.SaveChangesAsync();
            }
        }

        /// <summary>
        /// Internal method to update batch statistics
        /// </summary>
        private async Task UpdateBatchStatisticsInternalAsync(MemberImportBatch batch)
        {
            var statusCounts = await _context.TempMembers
                .Where(tm => tm.ImportBatchId == batch.Id)
                .GroupBy(tm => tm.Status)
                .Select(g => new { Status = g.Key, Count = g.Count() })
                .ToListAsync();

            batch.CreatedCount = statusCounts.FirstOrDefault(s => s.Status == TempMemberStatus.Created)?.Count ?? 0;
            batch.DuplicateCount = statusCounts.FirstOrDefault(s => s.Status == TempMemberStatus.Duplicate)?.Count ?? 0;
            batch.NeedsFixCount = statusCounts.FirstOrDefault(s => s.Status == TempMemberStatus.NeedsFix)?.Count ?? 0;
            batch.MergedCount = statusCounts.FirstOrDefault(s => s.Status == TempMemberStatus.Merged)?.Count ?? 0;
            batch.RejectedCount = statusCounts.FirstOrDefault(s => s.Status == TempMemberStatus.Rejected)?.Count ?? 0;
        }
    }
}