﻿// ParaHockey Modern Form Validation
// Implements "reward early, punish late" validation pattern

$(document).ready(function () {
    initializeFormValidation();
    initializeInputMasking();
    initializeDatePicker();

    // --- DOB Input Handling for Registration Page ---
    var $dobInput = $(".datepicker-input");
    if ($dobInput.length && $("#registrationForm").length) {
        // Auto-format 8-digit input to YYYY-MM-DD on blur/change
        $dobInput.on("blur.dobformat change.dobformat", function () {
            var raw = $(this).val();
            if (/^\d{8}$/.test(raw)) {
                var formatted =
                    raw.substring(0, 4) +
                    "-" +
                    raw.substring(4, 6) +
                    "-" +
                    raw.substring(6, 8);
                $(this).val(formatted);
            }
            $(this).trigger("input").trigger("change.dobflow");
        });

        // Validate and provide feedback while typing
        $dobInput.on("input.dobvalidate", function () {
            var value = $(this).val();
            var isValid = true;
            if (value.length >= 7) {
                var parts = value.split("-");
                var year = parseInt(parts[0]);
                var month = parseInt(parts[1]);
                if (year < 1900 || year > new Date().getFullYear())
                    isValid = false;
                if (month < 1 || month > 12) isValid = false;
                if (value.length === 10) {
                    var day = parseInt(parts[2]);
                    if (day < 1 || day > 31) isValid = false;
                    if (month === 2) {
                        var isLeapYear =
                            (year % 4 === 0 && year % 100 !== 0) ||
                            year % 400 === 0;
                        if (day > (isLeapYear ? 29 : 28)) isValid = false;
                    } else if ([4, 6, 9, 11].includes(month)) {
                        if (day > 30) isValid = false;
                    }
                }
            }
            if (value.length > 0) {
                if (isValid) {
                    $(this).removeClass("is-invalid").addClass("is-valid");
                } else {
                    $(this).removeClass("is-valid").addClass("is-invalid");
                }
            } else {
                $(this).removeClass("is-valid is-invalid");
            }
        });

        // Age-based form logic (Parent/Emergency Contact)
        function triggerAgeBasedFormLogic() {
            var dobValue = $dobInput.val();
            if (/^\d{8}$/.test(dobValue)) {
                dobValue =
                    dobValue.substring(0, 4) +
                    "-" +
                    dobValue.substring(4, 6) +
                    "-" +
                    dobValue.substring(6, 8);
                $dobInput.val(dobValue);
            }
            if (dobValue && dobValue.length === 10) {
                var dateOfBirth = new Date(dobValue);
                var today = new Date();
                var age = today.getFullYear() - dateOfBirth.getFullYear();
                var monthDiff = today.getMonth() - dateOfBirth.getMonth();
                if (
                    monthDiff < 0 ||
                    (monthDiff === 0 && today.getDate() < dateOfBirth.getDate())
                ) {
                    age--;
                }
                if (typeof window.toggleContactFieldsByAge === "function") {
                    window.toggleContactFieldsByAge(age);
                }
            } else {
                if (typeof window.resetFormSections === "function") {
                    window.resetFormSections();
                }
            }
        }

        $dobInput.on(
            "change.dobflow input.dobage paste.dobage",
            triggerAgeBasedFormLogic
        );
        // Also trigger on Enter key (keydown)
        $dobInput.on("keydown.dobenter", function (e) {
            if (e.key === "Enter" || e.keyCode === 13) {
                triggerAgeBasedFormLogic();
            }
        });
    }
});

// Track field states to implement proper validation timing
const fieldStates = {
    pristine: new Set(), // Never been touched
    touched: new Set(), // Been focused at least once
    valid: new Set(), // Currently valid
    invalid: new Set(), // Currently invalid
};

function initializeFormValidation() {
    const form = $("#registrationForm");
    if (form.length === 0) return;

    // Disable browser default validation
    form.attr("novalidate", "novalidate");

    // Track all form fields
    const fields = form.find("input, select, textarea");

    fields.each(function () {
        const field = $(this);
        const fieldName = field.attr("name");

        if (fieldName) {
            fieldStates.pristine.add(fieldName);

            // Set up event handlers
            setupFieldValidation(field, fieldName);
        }
    });

    // Handle form submission
    form.on("submit", function (e) {
        e.preventDefault();
        handleFormSubmission(form);
    });
}

function setupFieldValidation(field, fieldName) {
    // On first focus: mark as touched, remove from pristine
    field.on("focus", function () {
        if (fieldStates.pristine.has(fieldName)) {
            fieldStates.pristine.delete(fieldName);
            fieldStates.touched.add(fieldName);
        }
    });

    // On input: only show positive feedback for touched fields
    field.on("input", function () {
        if (fieldStates.touched.has(fieldName)) {
            const isValid = validateField(field, fieldName);

            if (isValid) {
                // Reward early: show success immediately
                showFieldSuccess(field, fieldName);
            } else {
                // Don't punish while typing - just remove success state
                clearFieldFeedback(field, fieldName);
            }
        }
    });

    // On blur: show errors for invalid fields
    field.on("blur", function () {
        if (fieldStates.touched.has(fieldName)) {
            const isValid = validateField(field, fieldName);

            if (isValid) {
                showFieldSuccess(field, fieldName);
            } else {
                // Punish late: show errors on blur
                showFieldError(field, fieldName);
            }
        }
    });
}

function validateField(field, fieldName) {
    const value = field.val().trim();
    const fieldType = field.attr("type") || field.prop("tagName").toLowerCase();
    const isRequired = field.prop("required");

    // Required field check
    if (isRequired && !value) {
        return false;
    }

    // Skip validation for empty optional fields
    if (!isRequired && !value) {
        return true;
    }

    // Field-specific validation
    switch (fieldName) {
        case "Email":
            return validateEmail(value);
        case "Phone":
            return validatePhone(value);
        case "PostalCode":
            return validatePostalCode(value);
        case "DateOfBirth":
            return validateDateOfBirth(value);
        case "FirstName":
        case "LastName":
            return validateName(value);
        case "Address":
            return validateAddress(value);
        case "City":
            return validateCity(value);
        default:
            return true;
    }
}

function validateEmail(email) {
    const emailRegex = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
    return emailRegex.test(email);
}

function validatePhone(phone) {
    // Remove all non-digits
    const digits = phone.replace(/\D/g, "");
    return digits.length === 10;
}

function validatePostalCode(postalCode) {
    const postalRegex = /^[A-Za-z]\d[A-Za-z][\s-]?\d[A-Za-z]\d$/;
    return postalRegex.test(postalCode);
}

function validateDateOfBirth(dateStr) {
    if (!dateStr) return false;

    const date = new Date(dateStr);
    const now = new Date();
    const minDate = new Date(now.getFullYear() - 120, 0, 1);

    return date instanceof Date && !isNaN(date) && date < now && date > minDate;
}

function validateName(name) {
    return name && name.length >= 2 && name.length <= 50;
}

function validateAddress(address) {
    return address && address.length >= 5 && address.length <= 200;
}

function validateCity(city) {
    return city && city.length >= 2 && city.length <= 100;
}

function showFieldSuccess(field, fieldName) {
    clearFieldFeedback(field, fieldName);

    field.removeClass("is-invalid").addClass("is-valid");
    fieldStates.invalid.delete(fieldName);
    fieldStates.valid.add(fieldName);

    // Don't show intrusive success messages - subtle styling is enough
}

function showFieldError(field, fieldName) {
    clearFieldFeedback(field, fieldName);

    field.removeClass("is-valid").addClass("is-invalid");
    fieldStates.valid.delete(fieldName);
    fieldStates.invalid.add(fieldName);

    // Error message will be shown by server-side validation span
    const validationSpan = field.siblings(
        '[data-valmsg-for="' + fieldName + '"]'
    );
    if (validationSpan.length) {
        const errorMessage = getErrorMessage(fieldName, field.val());
        validationSpan.text(errorMessage).show();
    }
}

function clearFieldFeedback(field, fieldName) {
    field.removeClass("is-valid is-invalid");
    fieldStates.valid.delete(fieldName);
    fieldStates.invalid.delete(fieldName);

    // Clear error messages
    field.siblings('[data-valmsg-for="' + fieldName + '"]').hide();
}

// Global variable to store localized validation messages
var localizedValidationMessages = {};

// Function to fetch localized messages from server
function loadLocalizedValidationMessages() {
    // This will be populated from the server-side in the view
    // The messages are loaded via hidden elements in the HTML
}

function getErrorMessage(fieldName, value) {
    // Use localized messages if available, fallback to English
    switch (fieldName) {
        case "Email":
            return (
                localizedValidationMessages.emailInvalid ||
                "Please enter a valid email address (e.g., <EMAIL>)"
            );
        case "Phone":
            return (
                localizedValidationMessages.phoneInvalid ||
                "Please enter a 10-digit phone number"
            );
        case "PostalCode":
            return (
                localizedValidationMessages.postalCodeInvalid ||
                "Please enter a valid Canadian postal code (e.g., A1A 1A1)"
            );
        case "DateOfBirth":
            if (!value)
                return (
                    localizedValidationMessages.dateInvalid ||
                    "Please enter a valid date in YYYY-MM-DD format"
                );
            const date = new Date(value);
            if (date > new Date())
                return (
                    localizedValidationMessages.dateFuture ||
                    "Date of birth cannot be in the future"
                );
            return (
                localizedValidationMessages.dateGeneral ||
                "Please enter a valid birth date"
            );
        case "FirstName":
        case "LastName":
            return (
                localizedValidationMessages.nameTooShort ||
                "Name must be at least 2 characters long"
            );
        case "Address":
            return (
                localizedValidationMessages.addressTooShort ||
                "Address must be at least 5 characters long"
            );
        case "City":
            return (
                localizedValidationMessages.cityTooShort ||
                "City must be at least 2 characters long"
            );
        default:
            return (
                localizedValidationMessages.required || "This field is required"
            );
    }
}

function handleFormSubmission(form) {
    // Mark all fields as touched
    const fields = form.find("input, select, textarea");
    let isFormValid = true;

    fields.each(function () {
        const field = $(this);
        const fieldName = field.attr("name");

        if (fieldName) {
            fieldStates.pristine.delete(fieldName);
            fieldStates.touched.add(fieldName);

            const isValid = validateField(field, fieldName);

            if (!isValid) {
                showFieldError(field, fieldName);
                isFormValid = false;
            } else {
                showFieldSuccess(field, fieldName);
            }
        }
    });

    if (isFormValid) {
        // Submit the form
        form.off("submit").submit();
    } else {
        // Scroll to first error
        const firstError = form.find(".is-invalid").first();
        if (firstError.length) {
            firstError.focus();
            $("html, body").animate(
                {
                    scrollTop: firstError.offset().top - 100,
                },
                300
            );
        }
    }
}

function initializeInputMasking() {
    // Phone number masking
    $('input[name="Phone"]').mask("(*************", {
        placeholder: "(___) ___-____",
    });

    // Postal code masking
    $('input[name="PostalCode"]').mask("L0L 0L0", {
        placeholder: "A1A 1A1",
        translation: {
            L: { pattern: /[A-Za-z]/ },
            0: { pattern: /[0-9]/ },
        },
    });
}

function initializeDatePicker() {
    if (typeof $.datepicker !== "undefined") {
        $(".datepicker-input").datepicker({
            dateFormat: "yy-mm-dd",
            changeMonth: true,
            changeYear: true,
            yearRange: "1900:+0",
            maxDate: new Date(),
        });

        // Handle datepicker icon click
        $(".datepicker-icon").on("click", function () {
            $(this)
                .closest(".col-md-6")
                .find(".datepicker-input")
                .datepicker("show");
        });
    }
}

// Form reset functionality
$(document).on("click", "[data-reset-form]", function () {
    const confirmed = confirm(
        window.ParaHockeyLocalizations?.formResetConfirm ||
            "Do you want to clear all form fields?"
    );
    if (confirmed) {
        const form = $(this).closest("form");
        form[0].reset();

        // Clear all validation states
        form.find("input, select, textarea").removeClass("is-valid is-invalid");
        form.find("[data-valmsg-for]").hide();

        // Reset field states
        Object.keys(fieldStates).forEach((key) => {
            fieldStates[key].clear();
        });

        // Re-add all fields to pristine state
        form.find("input, select, textarea").each(function () {
            const fieldName = $(this).attr("name");
            if (fieldName) {
                fieldStates.pristine.add(fieldName);
            }
        });
    }
});
