trigger:
    - main

pool: ParaHockeyServers

variables:
    buildConfiguration: "Release"
    testWebsitePath: "C:\\inetpub\\ParaHockey\\Test"
    prodWebsitePath: "C:\\inetpub\\ParaHockey\\Production"

stages:
    - stage: Build
      displayName: "Build"
      jobs:
          - job: Build
            displayName: "Build"
            steps:
                - task: UseDotNet@2
                  displayName: "Use .NET 8.0"
                  inputs:
                      packageType: "sdk"
                      version: "8.0.x"
                      includePreviewVersions: false

                - task: DotNetCoreCLI@2
                  displayName: "Restore packages"
                  inputs:
                      command: "restore"
                      projects: "**/*.csproj"
                      feedsToUse: "select"

                - task: DotNetCoreCLI@2
                  displayName: "Build project"
                  inputs:
                      command: "build"
                      projects: "**/*.csproj"
                      arguments: "--configuration $(buildConfiguration) --no-restore"

                - task: DotNetCoreCLI@2
                  displayName: "Publish project"
                  inputs:
                      command: "publish"
                      projects: "**/*.csproj"
                      arguments: "--configuration $(buildConfiguration) --output $(build.artifactStagingDirectory)/publish --no-build"
                      zipAfterPublish: true

                - task: PublishBuildArtifacts@1
                  displayName: "Publish Artifact"
                  inputs:
                      PathtoPublish: "$(build.artifactStagingDirectory)/publish"
                      ArtifactName: "drop"

    - stage: Test
      displayName: "Deploy to Test with Integrated Migrations"
      dependsOn: Build
      condition: succeeded()
      jobs:
          - job: DeployToTest
            displayName: "Deploy to Test"
            steps:
                - task: DownloadBuildArtifacts@1
                  displayName: "Download Build Artifacts"
                  inputs:
                      buildType: "current"
                      downloadType: "single"
                      artifactName: "drop"
                      downloadPath: "$(System.ArtifactsDirectory)"

                - task: PowerShell@2
                  displayName: "Deploy and Migrate Test Environment"
                  inputs:
                      targetType: "inline"
                      errorActionPreference: "stop"
                      failOnStderr: true
                      script: |
                          Write-Host "🚀 Starting integrated deployment and migration process..."
                          
                          # Import WebAdministration module
                          Import-Module WebAdministration -Force
                          
                          # Stop test website and app pool
                          Stop-Website -Name "ParaHockey-Test" -ErrorAction SilentlyContinue
                          Stop-WebAppPool -Name "ParaHockey-Test" -ErrorAction SilentlyContinue
                          Write-Host "✅ Stopped test website and app pool"
                          
                          # Wait for processes to release file handles
                          Start-Sleep -Seconds 10
                          
                          # Clean test directory
                          if (Test-Path "$(testWebsitePath)") {
                              Remove-Item "$(testWebsitePath)\*" -Recurse -Force
                              Write-Host "✅ Cleaned test directory"
                          } else {
                              New-Item -ItemType Directory -Path "$(testWebsitePath)" -Force
                              Write-Host "✅ Created test directory"
                          }
                          
                          # Copy and extract files
                          Write-Host "📂 Deploying application files..."
                          Copy-Item "$(System.ArtifactsDirectory)\drop\*" -Destination "$(testWebsitePath)" -Recurse -Force
                          
                          # Extract zip files if any
                          $zipFiles = Get-ChildItem "$(testWebsitePath)" -Filter "*.zip"
                          foreach ($zipFile in $zipFiles) {
                              Write-Host "📦 Extracting: $($zipFile.Name)"
                              Expand-Archive -Path $zipFile.FullName -DestinationPath "$(testWebsitePath)" -Force
                              Remove-Item $zipFile.FullName -Force
                          }
                          
                          # Verify ParaHockey.dll exists
                          if (Test-Path "$(testWebsitePath)\ParaHockey.dll") {
                              Write-Host "✅ Application deployed successfully - ParaHockey.dll found"
                          } else {
                              Write-Host "❌ ParaHockey.dll not found. Checking directory contents:"
                              Get-ChildItem "$(testWebsitePath)" -Recurse | Select-Object -First 20
                              throw "Application deployment failed"
                          }
                          
                          # Configure appsettings for test
                          $stagingSettings = @{
                              "DetailedErrors" = $true
                              "Logging" = @{
                                  "LogLevel" = @{
                                      "Default" = "Information"
                                      "Microsoft.AspNetCore" = "Warning"
                                  }
                              }
                              "ConnectionStrings" = @{
                                  "DefaultConnection" = "Server=SIMBA\SQLEXPRESS;User Id=ParaHockeyUser;Password=***************;Database=ParaHockeyDB_TEST;Encrypt=False;"
                              }
                              "Environment" = @{
                                  "Name" = "TEST"
                                  "Theme" = "danger"
                                  "ShowBanner" = $true
                                  "UseAuthentication" = $true
                                  "BannerText" = "Para Hockey TEST Site"
                              }
                          }
                          
                          $stagingSettingsPath = "$(testWebsitePath)\appsettings.Staging.json"
                          $stagingSettings | ConvertTo-Json -Depth 10 | Set-Content $stagingSettingsPath
                          Write-Host "✅ Created appsettings.Staging.json"
                          
                          # 🗄️ RUN DATABASE MIGRATIONS
                          Write-Host "🗄️ Running database migrations..."
                          
                          # Check if EF tools are available
                          $efCheck = dotnet tool list -g | Select-String "dotnet-ef"
                          if (-not $efCheck) {
                              dotnet tool install --global dotnet-ef
                              Write-Host "✅ Installed EF tools"
                          }
                          
                          # Run migrations
                          $connectionString = "Server=SIMBA\SQLEXPRESS;User Id=ParaHockeyUser;Password=***************;Database=ParaHockeyDB_TEST;Encrypt=False;"
                          
                          try {
                              $migrationResult = dotnet ef database update --connection $connectionString --verbose 2>&1
                              $migrationResult | ForEach-Object { Write-Host $_ }
                              
                              if ($LASTEXITCODE -eq 0) {
                                  Write-Host "✅ Database migrations completed successfully"
                              } else {
                                  # Check for the IsMasterAdmin issue
                                  if (($migrationResult -join " ") -like "*IsMasterAdmin*") {
                                      Write-Host "⚠️ Applying migration fix for IsMasterAdmin issue..."
                                      $sqlFix = "IF NOT EXISTS (SELECT 1 FROM [__EFMigrationsHistory] WHERE [MigrationId] = '20250710211205_ReplaceAdminTypeWithLookupTable') INSERT INTO [__EFMigrationsHistory] ([MigrationId], [ProductVersion]) VALUES ('20250710211205_ReplaceAdminTypeWithLookupTable', '8.0.0')"
                                      sqlcmd -S "SIMBA\SQLEXPRESS" -d "ParaHockeyDB_TEST" -U "ParaHockeyUser" -P "***************" -Q $sqlFix
                                      
                                      # Retry migration
                                      $migrationResult = dotnet ef database update --connection $connectionString --verbose 2>&1
                                      $migrationResult | ForEach-Object { Write-Host $_ }
                                  }
                                  
                                  if ($LASTEXITCODE -ne 0) {
                                      throw "Migration failed with exit code: $LASTEXITCODE"
                                  } else {
                                      Write-Host "✅ Database migrations completed after fix"
                                  }
                              }
                          } catch {
                              Write-Host "❌ Migration error: $_"
                              throw "Database migration failed: $_"
                          }
                          
                          # Start test website
                          Start-WebAppPool -Name "ParaHockey-Test"
                          Start-Sleep -Seconds 5
                          Start-Website -Name "ParaHockey-Test"
                          Write-Host "✅ Test website started successfully"
                          
                          Write-Host "🎉 Test deployment and migration completed successfully!"