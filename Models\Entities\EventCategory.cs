using System.ComponentModel.DataAnnotations;

namespace ParaHockeyApp.Models.Entities
{
    /// <summary>
    /// Event category entity for organizing different types of events
    /// </summary>
    public class EventCategory : BaseEntity
    {
        /// <summary>
        /// Display name key for localization
        /// </summary>
        [Required]
        [StringLength(100)]
        public string DisplayNameKey { get; set; } = string.Empty;

        /// <summary>
        /// Description key for localization
        /// </summary>
        [StringLength(500)]
        public string? DescriptionKey { get; set; }

        /// <summary>
        /// Color for calendar display (hex format)
        /// </summary>
        [Required]
        [StringLength(7)]
        public string Color { get; set; } = "#0074D9";

        /// <summary>
        /// Icon class for display (e.g., "fas fa-calendar")
        /// </summary>
        [StringLength(50)]
        public string? IconClass { get; set; }

        /// <summary>
        /// Display order for sorting
        /// </summary>
        public int DisplayOrder { get; set; } = 0;

        /// <summary>
        /// Whether registration is required for events in this category
        /// </summary>
        public bool RequiresRegistration { get; set; } = false;

        /// <summary>
        /// Maximum number of participants allowed (-1 for unlimited)
        /// </summary>
        public int MaxParticipants { get; set; } = -1;

        /// <summary>
        /// Navigation property to events in this category
        /// </summary>
        public virtual ICollection<Event> Events { get; set; } = new List<Event>();
    }
}