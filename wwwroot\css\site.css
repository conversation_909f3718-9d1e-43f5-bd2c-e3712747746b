/* Import ParaHockey Design System */
@import url('./parahockey-design-system.css');

/* Legacy compatibility and site-specific overrides */
/* Base typography - Mobile first (maintained for compatibility) */
html {
  font-size: 14px;
  scroll-behavior: smooth;
}

/* Larger screens */
@media (min-width: 768px) {
  html {
    font-size: 16px;
  }
}

/* Focus styles for accessibility */
.btn:focus, .btn:active:focus, .btn-link.nav-link:focus, .form-control:focus, .form-check-input:focus {
  box-shadow: 0 0 0 0.1rem var(--ph-bg-primary), 0 0 0 0.25rem var(--ph-focus-ring-color);
}

/* Layout */
html {
  position: relative;
  min-height: 100%;
}

body {
  margin-bottom: 60px;
  overflow-x: hidden; /* Prevent horizontal scroll on mobile */
}

/* Mobile-first responsive design */
.container-fluid {
  padding-left: 1rem;
  padding-right: 1rem;
}

/* Navigation improvements for mobile */
.navbar-brand {
  font-size: 1.1rem;
}

@media (max-width: 991.98px) {
  .navbar-brand {
    font-size: 1rem;
  }
  
  .navbar-nav .nav-link {
    padding: 0.75rem 1rem;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  }
  
  .navbar-nav .nav-link:last-child {
    border-bottom: none;
  }
  
  .dropdown-menu {
    border: none;
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
  }
}

/* Language switcher styling */
.dropdown-item.active {
  background-color: var(--ph-primary);
  color: var(--ph-bg-primary);
}

.dropdown-item:hover {
  background-color: var(--ph-bg-secondary);
}

/* Card and content responsive design */
.card {
  margin-bottom: 1rem;
}

@media (max-width: 576px) {
  .card {
    margin-left: -0.5rem;
    margin-right: -0.5rem;
    border-left: none;
    border-right: none;
    border-radius: 0;
  }
  
  .container {
    padding-left: 0.75rem;
    padding-right: 0.75rem;
  }
}

/* Table responsive improvements */
.table-responsive {
  border-radius: 0.375rem;
}

@media (max-width: 768px) {
  .table-responsive {
    font-size: 0.875rem;
  }
  
  .table th,
  .table td {
    padding: 0.5rem 0.25rem;
  }
}

/* Form improvements for mobile */
.form-control,
.form-select {
  font-size: 16px; /* Prevents zoom on iOS */
}

@media (max-width: 576px) {
  .btn {
    padding: 0.5rem 1rem;
    font-size: 0.9rem;
  }
  
  .btn-group-vertical .btn {
    margin-bottom: 0.25rem;
  }
}

/* Footer improvements */
.footer {
  padding: 1rem 0;
}

@media (max-width: 768px) {
  .footer .row > div {
    text-align: center !important;
    margin-bottom: 0.5rem;
  }
  
  .footer .row > div:last-child {
    margin-bottom: 0;
  }
}

/* Alert improvements for mobile */
@media (max-width: 576px) {
  .alert {
    margin-left: -0.75rem;
    margin-right: -0.75rem;
    border-left: none;
    border-right: none;
    border-radius: 0;
  }
}

/* Utility classes for responsive text */
@media (max-width: 576px) {
  .text-responsive {
    font-size: 0.875rem;
  }
  
  .lead {
    font-size: 1.1rem;
  }
  
  .display-4 {
    font-size: 2rem;
  }
}

/* Improved touch targets for mobile */
@media (max-width: 768px) {
  .nav-link,
  .dropdown-item,
  .btn {
    min-height: 44px; /* Apple's recommended touch target size */
    display: flex;
    align-items: center;
  }
}

/* Loading states and animations */
.fade-in {
  animation: fadeIn 0.3s ease-in;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(-10px); }
  to { opacity: 1; transform: translateY(0); }
}

/* Datepicker custom styling */
.ui-datepicker .ui-datepicker-header .ui-datepicker-prev,
.ui-datepicker .ui-datepicker-header .ui-datepicker-next {
    margin: 0 6px !important; /* Adds horizontal space */
}

/* Datepicker styling - fix transparent background issue */
.ui-datepicker {
    background-color: var(--ph-bg-primary) !important;
    border: 1px solid var(--ph-border) !important;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2) !important;
    z-index: 9999 !important;
    font-family: inherit !important;
}

.ui-datepicker-header {
    background-color: var(--ph-primary) !important;
    color: var(--ph-bg-primary) !important;
    border: none !important;
}

.ui-datepicker-title {
    color: var(--ph-bg-primary) !important;
}

.ui-datepicker-prev,
.ui-datepicker-next {
    background-color: transparent !important;
    border: none !important;
    color: var(--ph-bg-primary) !important;
}

.ui-datepicker-prev:hover,
.ui-datepicker-next:hover {
    background-color: rgba(255, 255, 255, 0.2) !important;
}

.ui-datepicker td {
    background-color: var(--ph-bg-primary) !important;
    border: 1px solid var(--ph-border) !important;
}

.ui-datepicker td a {
    background-color: transparent !important;
    color: var(--ph-text-primary) !important;
    text-decoration: none !important;
    padding: 8px !important;
    display: block !important;
}

.ui-datepicker td a:hover {
    background-color: var(--ph-bg-secondary) !important;
    color: var(--ph-link) !important;
}

.ui-datepicker .ui-state-active {
    background-color: var(--ph-primary) !important;
    color: var(--ph-bg-primary) !important;
}

.ui-datepicker .ui-state-highlight {
    background-color: #fff3cd !important;
    color: #856404 !important;
}

/* Modern Form Validation Styles */
/* Remove aggressive green validation feedback */
.form-control.is-valid {
    border-color: var(--ph-border) !important; /* Neutral border instead of green */
    padding-right: calc(1.5em + 0.75rem) !important;
    background-image: none !important; /* Remove green checkmark */
    background-repeat: no-repeat !important;
    background-position: right calc(0.375em + 0.1875rem) center !important;
    background-size: calc(0.75em + 0.375rem) calc(0.75em + 0.375rem) !important;
}

/* Subtle focus state - modern blue */
.form-control:focus {
    border-color: var(--ph-primary-light) !important;
    box-shadow: 0 0 0 0.25rem var(--ph-focus-ring-color) !important;
}

/* Error state styling */
.form-control.is-invalid {
    border-color: #dc3545 !important;
    padding-right: calc(1.5em + 0.75rem) !important;
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 12 12' width='12' height='12' fill='none' stroke='%23dc3545'%3e%3ccircle cx='6' cy='6' r='4.5'/%3e%3cpath d='m5.8 4.6 1.4 1.4-1.4 1.4'/%3e%3c/svg%3e") !important;
    background-repeat: no-repeat !important;
    background-position: right calc(0.375em + 0.1875rem) center !important;
    background-size: calc(0.75em + 0.375rem) calc(0.75em + 0.375rem) !important;
}

/* Field icons and help text */
.field-icon {
    position: absolute;
    right: 10px;
    top: 50%;
    transform: translateY(-50%);
    color: var(--ph-text-secondary);
    pointer-events: none;
}

.field-with-icon {
    position: relative;
}

.field-with-icon .form-control {
    padding-right: 2.5rem;
}

/* Enhanced error messages */
.field-error {
    display: block;
    width: 100%;
    margin-top: 0.25rem;
    font-size: 0.875rem;
    color: #dc3545;
}

.field-error::before {
    content: "⚠️ ";
    margin-right: 0.25rem;
}

/* Help text styling */
.field-help {
    display: block;
    margin-top: 0.25rem;
    font-size: 0.875rem;
    color: var(--ph-text-secondary);
}

/* Radio button and checkbox validation styles */
.form-check-input.is-invalid ~ .form-check-label {
    color: var(--ph-danger);
}

.form-check-input.is-invalid {
    border-color: var(--ph-danger);
}

/* Success confirmation (subtle) */
.form-success-message {
    color: var(--ph-success);
    font-size: 0.875rem;
    margin-top: 0.25rem;
}

.form-success-message::before {
    content: "✓ ";
    margin-right: 0.25rem;
}

/* Smooth transitions for validation states */
.form-control {
    transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

/* Required field indicator (modern) */
.form-label.required::after {
    content: " *";
    color: var(--ph-danger);
    font-weight: bold;
}

/* Validation summary improvements */
.validation-summary-errors {
    background-color: #f8d7da;
    border: 1px solid #f5c6cb;
    color: #721c24;
    padding: 0.75rem 1rem;
    border-radius: 0.375rem;
    margin-bottom: 1rem;
}

.validation-summary-errors ul {
    margin-bottom: 0;
    padding-left: 1.25rem;
}

.validation-summary-errors li {
    margin-bottom: 0.25rem;
}

/* Mobile enhancements for validation */
@media (max-width: 576px) {
    .field-error {
        font-size: 0.8rem;
    }
    
    .field-help {
        font-size: 0.8rem;
    }
    
    .form-control.is-invalid,
    .form-control.is-valid {
        background-size: 1rem 1rem;
        padding-right: 2rem;
    }
}
