# Design Document

## 1 Overview
Introduce a **token-based theming system** using CSS variables, extend it to dark-mode & forced-colours situations, and add automated contrast tests so the ParaHockey app stays readable under any user theme.

## 2 Key Principles
1. Semantic colour tokens (`--ph-text-primary`, etc.).  
2. Single-CSS strategy: light values by default, overridden via media queries.  
3. Graceful degradation if CSS variables unsupported.  
4. CI blocks merges on WCAG contrast failures.

## 3 Architecture & File Layout
```
wwwroot/css/
  shared/
    variables.css        # light theme + tokens
    forced-colors.css    # only in @media (forced-colors: active)
  site.css               # uses tokens
wwwroot/js/theme-listener.js  # optional runtime watcher
Views/Shared/_Layout.cshtml    # imports CSS & JS
```

### 3.1 Tokens to Add
```
--ph-bg-primary           --ph-text-primary
--ph-bg-secondary         --ph-text-secondary
--ph-border               --ph-link
--ph-link-hover
```

### 3.2 Dark-Mode Overrides
```css
@media (prefers-color-scheme: dark) {
  :root {
    --ph-bg-primary: #1a1a1a;
    --ph-text-primary: #e5e5e5;
    --ph-border: #404040;
    /* …other tokens… */
  }
}
```

### 3.3 Forced-Colours Styles
`wwwroot/css/forced-colors.css`
```css
@media (forced-colors: active) {
  :root {
    --ph-bg-primary: Canvas;
    --ph-text-primary: CanvasText;
    --ph-link: LinkText;
    --ph-border: GrayText;
  }
  a, button, input, select {
      forced-color-adjust: none;
  }
}
```

### 3.4 Component Refactor
Replace hard-coded colours in all CSS with tokens; update Razor inline styles.

### 3.5 Theme Listener (optional)
```js
// wwwroot/js/theme-listener.js
(function () {
  if (!window.matchMedia) return;
  const mq = window.matchMedia('(prefers-color-scheme: dark)');
  mq.addEventListener('change', () =>
    document.documentElement.classList.toggle('ph-dark', mq.matches));
})();
```

### 3.6 View Integration (`_Layout.cshtml`)
```html
<meta name="color-scheme" content="light dark">
<link rel="stylesheet" href="~/css/shared/variables.css" asp-append-version="true" />
<link rel="stylesheet" href="~/css/forced-colors.css" asp-append-version="true" />
<script src="~/js/theme-listener.js" asp-append-version="true"></script>
```

## 4 Testing Strategy
| Layer             | Tool / Method                               | Pass Criteria |
|-------------------|---------------------------------------------|---------------|
| CI Pipeline       | axe-core contrast rule (light/dark)         | 0 violations  |
| Manual Smoke QA   | Browsers + High Contrast                    | Text readable |
| E2E               | Playwright toggling themes                  | Green build   |

## 5 Implementation Phases
1. Add tokens.  2. Dark overrides.  3. Forced-colours sheet.  
4. Refactor CSS.  5. Layout update.  6. Tests in CI.  7. Docs.

## 6 Risks & Mitigations
| Risk                                   | Mitigation                      |
|----------------------------------------|---------------------------------|
| Large CSS refactor touches many files  | Incremental PRs + visual review |
| Dark palette clashes with branding     | Designer review, tweak tokens   |
| High-contrast users rely on system col | Use Canvas / CanvasText values  | 