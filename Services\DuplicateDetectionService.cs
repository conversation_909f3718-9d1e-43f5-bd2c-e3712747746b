using Microsoft.EntityFrameworkCore;
using ParaHockeyApp.Models;
using ParaHockeyApp.Models.Entities;
using ParaHockeyApp.ViewModels;

namespace ParaHockeyApp.Services
{
    /// <summary>
    /// Service for detecting duplicate members and handling merge operations
    /// </summary>
    public class DuplicateDetectionService : IDuplicateDetectionService
    {
        private readonly ApplicationContext _context;
        private readonly INormalizationService _normalizationService;
        private readonly IAuditLogService _auditLogService;
        private readonly IUserContextService _userContextService;

        public DuplicateDetectionService(
            ApplicationContext context, 
            INormalizationService normalizationService,
            IAuditLogService auditLogService,
            IUserContextService userContextService)
        {
            _context = context;
            _normalizationService = normalizationService;
            _auditLogService = auditLogService;
            _userContextService = userContextService;
        }

        /// <summary>
        /// Finds an existing member that matches the temp member based on duplicate detection rules
        /// Priority: Email match first, then FirstName + LastName + DateOfBirth
        /// </summary>
        public async Task<Member?> FindExistingMemberAsync(TempMember tempMember)
        {
            if (tempMember == null)
                return null;

            // Priority 1: Email match (case-insensitive exact match)
            if (!string.IsNullOrWhiteSpace(tempMember.Email))
            {
                var normalizedEmail = _normalizationService.NormalizeEmail(tempMember.Email);
                var emailMatch = await _context.Members
                    .Where(m => m.Email.ToLower() == normalizedEmail.ToLower())
                    .FirstOrDefaultAsync();

                if (emailMatch != null)
                    return emailMatch;
            }

            // Priority 2: FirstName + LastName + DateOfBirth exact match
            if (!string.IsNullOrWhiteSpace(tempMember.FirstName) && 
                !string.IsNullOrWhiteSpace(tempMember.LastName) && 
                tempMember.DateOfBirth.HasValue)
            {
                var normalizedFirstName = _normalizationService.NormalizeName(tempMember.FirstName);
                var normalizedLastName = _normalizationService.NormalizeName(tempMember.LastName);

                var nameAndDobMatch = await _context.Members
                    .Where(m => m.FirstName.ToLower() == normalizedFirstName.ToLower() &&
                               m.LastName.ToLower() == normalizedLastName.ToLower() &&
                               m.DateOfBirth.Date == tempMember.DateOfBirth.Value.Date)
                    .FirstOrDefaultAsync();

                if (nameAndDobMatch != null)
                    return nameAndDobMatch;
            }

            return null;
        }

        /// <summary>
        /// Previews a merge operation showing what fields will change
        /// </summary>
        public async Task<DuplicateResolutionPreview> PreviewMergeAsync(Guid tempMemberId, Dictionary<string, string> fieldChoices)
        {
            var tempMember = await _context.TempMembers
                .Include(tm => tm.ExistingMember)
                .FirstOrDefaultAsync(tm => tm.TempMemberId == tempMemberId);

            if (tempMember?.ExistingMember == null)
                throw new InvalidOperationException("Temp member or existing member not found");

            var preview = new DuplicateResolutionPreview
            {
                TempMember = tempMember,
                ExistingMember = tempMember.ExistingMember,
                FieldComparisons = BuildFieldComparisons(tempMember, tempMember.ExistingMember),
                Changes = new List<FieldChange>()
            };

            // Calculate what will change based on field choices
            foreach (var choice in fieldChoices)
            {
                var fieldName = choice.Key;
                var selectedSource = choice.Value; // "temp" or "existing"

                if (preview.FieldComparisons.TryGetValue(fieldName, out var comparison) && !comparison.AreIdentical)
                {
                    var newValue = selectedSource == "temp" ? comparison.TempValue : comparison.ExistingValue;
                    var currentValue = GetFieldValue(tempMember.ExistingMember, fieldName);

                    if (newValue != currentValue)
                    {
                        preview.Changes.Add(new FieldChange
                        {
                            FieldName = fieldName,
                            DisplayName = comparison.DisplayName,
                            OldValue = currentValue,
                            NewValue = newValue
                        });
                    }
                }
            }

            return preview;
        }

        /// <summary>
        /// Applies a merge operation by updating the existing member with selected field values
        /// </summary>
        public async Task<Member> ApplyMergeAsync(Guid tempMemberId, Dictionary<string, string> fieldChoices, string performedBy)
        {
            using var transaction = await _context.Database.BeginTransactionAsync();
            
            try
            {
                var tempMember = await _context.TempMembers
                    .Include(tm => tm.ExistingMember)
                    .FirstOrDefaultAsync(tm => tm.TempMemberId == tempMemberId);

                if (tempMember?.ExistingMember == null)
                    throw new InvalidOperationException("Temp member or existing member not found");

                var existingMember = tempMember.ExistingMember;
                var fieldComparisons = BuildFieldComparisons(tempMember, existingMember);

                // Apply field changes
                var changes = new List<string>();
                foreach (var choice in fieldChoices)
                {
                    var fieldName = choice.Key;
                    var selectedSource = choice.Value; // "temp" or "existing"

                    if (fieldComparisons.TryGetValue(fieldName, out var comparison) && !comparison.AreIdentical)
                    {
                        var newValue = selectedSource == "temp" ? comparison.TempValue : comparison.ExistingValue;
                        var oldValue = GetFieldValue(existingMember, fieldName);

                        if (newValue != oldValue)
                        {
                            SetFieldValue(existingMember, fieldName, newValue);
                            changes.Add($"{comparison.DisplayName}: '{oldValue}' → '{newValue}'");
                        }
                    }
                }

                // Update temp member status and existing member
                tempMember.Status = TempMemberStatus.Merged;
                tempMember.DateModified = DateTime.UtcNow;
                tempMember.ModifiedBySource = ActionSource.AdminPanel;

                existingMember.DateModified = DateTime.UtcNow;
                existingMember.ModifiedBySource = ActionSource.AdminPanel;

                await _context.SaveChangesAsync();

                // Log the merge operation
                await _auditLogService.LogActionAsync(
                    $"Merged with temp member {tempMemberId}. Changes: {string.Join(", ", changes)}", 
                    existingMember, 
                    ActionSource.AdminPanel, 
                    performedBy, 
                    null);

                await transaction.CommitAsync();
                return existingMember;
            }
            catch
            {
                await transaction.RollbackAsync();
                throw;
            }
        }

        /// <summary>
        /// Builds field comparisons between temp member and existing member
        /// </summary>
        private Dictionary<string, FieldComparison> BuildFieldComparisons(TempMember tempMember, Member existingMember)
        {
            var comparisons = new Dictionary<string, FieldComparison>();

            // Compare basic fields
            AddComparison(comparisons, "FirstName", "First Name", tempMember.FirstName, existingMember.FirstName);
            AddComparison(comparisons, "LastName", "Last Name", tempMember.LastName, existingMember.LastName);
            AddComparison(comparisons, "DateOfBirth", "Date of Birth", 
                tempMember.DateOfBirth?.ToString("yyyy-MM-dd"), existingMember.DateOfBirth.ToString("yyyy-MM-dd"));
            AddComparison(comparisons, "Email", "Email", tempMember.Email, existingMember.Email);
            AddComparison(comparisons, "Phone", "Phone", tempMember.Phone, existingMember.Phone);
            AddComparison(comparisons, "Address", "Address", tempMember.Address, existingMember.Address);
            AddComparison(comparisons, "City", "City", tempMember.City, existingMember.City);
            AddComparison(comparisons, "PostalCode", "Postal Code", tempMember.PostalCode, existingMember.PostalCode);

            return comparisons;
        }

        /// <summary>
        /// Adds a field comparison if values are different
        /// </summary>
        private void AddComparison(Dictionary<string, FieldComparison> comparisons, string fieldName, string displayName, string? tempValue, string? existingValue)
        {
            var tempNormalized = string.IsNullOrWhiteSpace(tempValue) ? null : tempValue.Trim();
            var existingNormalized = string.IsNullOrWhiteSpace(existingValue) ? null : existingValue.Trim();
            
            var areIdentical = string.Equals(tempNormalized, existingNormalized, StringComparison.OrdinalIgnoreCase);

            comparisons[fieldName] = new FieldComparison
            {
                FieldName = fieldName,
                DisplayName = displayName,
                TempValue = tempNormalized,
                ExistingValue = existingNormalized,
                AreIdentical = areIdentical
            };
        }

        /// <summary>
        /// Gets duplicate resolution view model for UI display
        /// </summary>
        public async Task<DuplicateResolutionViewModel> GetDuplicateResolutionViewModelAsync(Guid tempMemberId)
        {
            var tempMember = await _context.TempMembers
                .Include(tm => tm.ExistingMember)
                .FirstOrDefaultAsync(tm => tm.TempMemberId == tempMemberId);

            if (tempMember?.ExistingMember == null)
                throw new InvalidOperationException("Temp member or existing member not found");

            var fieldComparisons = BuildFieldComparisons(tempMember, tempMember.ExistingMember);

            return new DuplicateResolutionViewModel
            {
                TempMember = tempMember,
                ExistingMember = tempMember.ExistingMember,
                FieldComparisons = fieldComparisons,
                FieldChoices = new Dictionary<string, string>()
            };
        }

        /// <summary>
        /// Gets paginated list of duplicate queue items for a batch
        /// </summary>
        public async Task<DuplicateQueueViewModel> GetDuplicateQueueAsync(int batchId, int pageNumber = 1, int pageSize = 20, string? searchTerm = null, DuplicateMatchType? matchTypeFilter = null)
        {
            var batch = await _context.MemberImportBatches
                .FirstOrDefaultAsync(b => b.Id == batchId);

            if (batch == null)
                throw new ArgumentException($"Import batch {batchId} not found");

            // Build the base query with includes
            var baseQuery = _context.TempMembers
                .Where(tm => tm.ImportBatchId == batchId && tm.Status == TempMemberStatus.Duplicate)
                .Include(tm => tm.ExistingMember)
                .AsQueryable();

            // Apply search filter if provided
            if (!string.IsNullOrWhiteSpace(searchTerm))
            {
                var lowerSearchTerm = searchTerm.ToLower();
                baseQuery = baseQuery.Where(tm => 
                    tm.FirstName.ToLower().Contains(lowerSearchTerm) ||
                    tm.LastName.ToLower().Contains(lowerSearchTerm) ||
                    (tm.Email != null && tm.Email.ToLower().Contains(lowerSearchTerm)) ||
                    (tm.ExistingMember != null && tm.ExistingMember.FirstName.ToLower().Contains(lowerSearchTerm)) ||
                    (tm.ExistingMember != null && tm.ExistingMember.LastName.ToLower().Contains(lowerSearchTerm)) ||
                    (tm.ExistingMember != null && tm.ExistingMember.Email != null && tm.ExistingMember.Email.ToLower().Contains(lowerSearchTerm)));
            }

            var totalCount = await baseQuery.CountAsync();

            var tempMembers = await baseQuery
                .OrderBy(tm => tm.DateCreated)
                .Skip((pageNumber - 1) * pageSize)
                .Take(pageSize)
                .ToListAsync();

            var duplicateItems = new List<DuplicateQueueItem>();
            foreach (var tempMember in tempMembers)
            {
                if (tempMember.ExistingMember != null)
                {
                    var matchType = GetMatchType(tempMember, tempMember.ExistingMember);
                    
                    // Apply match type filter after loading data (since match type is computed)
                    if (matchTypeFilter.HasValue && matchType != matchTypeFilter.Value)
                        continue;

                    var fieldComparisons = BuildFieldComparisons(tempMember, tempMember.ExistingMember);
                    var differenceCount = fieldComparisons.Values.Count(fc => !fc.AreIdentical);

                    duplicateItems.Add(new DuplicateQueueItem
                    {
                        TempMemberId = tempMember.TempMemberId,
                        ImportBatchId = tempMember.ImportBatchId,
                        TempMemberName = $"{tempMember.FirstName} {tempMember.LastName}",
                        TempMemberEmail = tempMember.Email ?? string.Empty,
                        TempMemberDateOfBirth = tempMember.DateOfBirth,
                        ExistingMemberId = tempMember.ExistingMember.Id,
                        ExistingMemberName = $"{tempMember.ExistingMember.FirstName} {tempMember.ExistingMember.LastName}",
                        ExistingMemberEmail = tempMember.ExistingMember.Email ?? string.Empty,
                        ExistingMemberDateOfBirth = tempMember.ExistingMember.DateOfBirth,
                        MatchType = matchType,
                        DifferenceCount = differenceCount,
                        CreatedAt = tempMember.DateCreated
                    });
                }
            }

            return new DuplicateQueueViewModel
            {
                ImportBatchId = batchId,
                FileName = batch.FileName,
                DuplicateItems = duplicateItems,
                PageNumber = pageNumber,
                PageSize = pageSize,
                TotalCount = duplicateItems.Count, // Use filtered count (post-match-type-filter)
                SearchTerm = searchTerm,
                MatchTypeFilter = matchTypeFilter
            };
        }

        /// <summary>
        /// Determines the type of duplicate match between temp member and existing member
        /// </summary>
        public DuplicateMatchType GetMatchType(TempMember tempMember, Member existingMember)
        {
            // Check email match first (highest priority)
            if (!string.IsNullOrWhiteSpace(tempMember.Email) && !string.IsNullOrWhiteSpace(existingMember.Email))
            {
                var tempEmail = _normalizationService.NormalizeEmail(tempMember.Email);
                var existingEmail = _normalizationService.NormalizeEmail(existingMember.Email);
                
                if (string.Equals(tempEmail, existingEmail, StringComparison.OrdinalIgnoreCase))
                    return DuplicateMatchType.Email;
            }

            // Check name + DOB match
            if (!string.IsNullOrWhiteSpace(tempMember.FirstName) && 
                !string.IsNullOrWhiteSpace(tempMember.LastName) && 
                tempMember.DateOfBirth.HasValue)
            {
                var tempFirstName = _normalizationService.NormalizeName(tempMember.FirstName);
                var tempLastName = _normalizationService.NormalizeName(tempMember.LastName);
                var existingFirstName = _normalizationService.NormalizeName(existingMember.FirstName);
                var existingLastName = _normalizationService.NormalizeName(existingMember.LastName);

                if (string.Equals(tempFirstName, existingFirstName, StringComparison.OrdinalIgnoreCase) &&
                    string.Equals(tempLastName, existingLastName, StringComparison.OrdinalIgnoreCase) &&
                    tempMember.DateOfBirth.Value.Date == existingMember.DateOfBirth.Date)
                {
                    return DuplicateMatchType.NameAndDateOfBirth;
                }
            }

            // Fallback - shouldn't happen if FindExistingMemberAsync worked correctly
            return DuplicateMatchType.Email;
        }

        /// <summary>
        /// Rejects a duplicate temp member (marks as rejected)
        /// </summary>
        public async Task<TempMember> RejectDuplicateAsync(Guid tempMemberId, string rejectedBy)
        {
            var tempMember = await _context.TempMembers
                .Include(tm => tm.ExistingMember)
                .FirstOrDefaultAsync(tm => tm.TempMemberId == tempMemberId);

            if (tempMember == null)
                throw new ArgumentException($"Temp member {tempMemberId} not found");

            if (tempMember.Status != TempMemberStatus.Duplicate)
                throw new InvalidOperationException($"Temp member {tempMemberId} is not in duplicate status");

            tempMember.Status = TempMemberStatus.Rejected;
            tempMember.DateModified = DateTime.UtcNow;
            tempMember.ModifiedBySource = ActionSource.AdminPanel;

            await _context.SaveChangesAsync();

            // Log the rejection
            await _auditLogService.LogActionAsync(
                $"Rejected duplicate temp member: {tempMember.FirstName} {tempMember.LastName} (matched with existing member {tempMember.ExistingMember?.Id})",
                tempMember.ExistingMember,
                ActionSource.AdminPanel,
                rejectedBy,
                null);

            return tempMember;
        }

        /// <summary>
        /// Bulk rejects multiple duplicate temp members
        /// </summary>
        public async Task<int> BulkRejectDuplicatesAsync(List<Guid> tempMemberIds, string rejectedBy)
        {
            using var transaction = await _context.Database.BeginTransactionAsync();
            
            try
            {
                var tempMembers = await _context.TempMembers
                    .Include(tm => tm.ExistingMember)
                    .Where(tm => tempMemberIds.Contains(tm.TempMemberId) && tm.Status == TempMemberStatus.Duplicate)
                    .ToListAsync();

                var rejectedCount = 0;
                foreach (var tempMember in tempMembers)
                {
                    tempMember.Status = TempMemberStatus.Rejected;
                    tempMember.DateModified = DateTime.UtcNow;
                    tempMember.ModifiedBySource = ActionSource.AdminPanel;
                    rejectedCount++;
                }

                await _context.SaveChangesAsync();

                // Log bulk rejection
                await _auditLogService.LogActionAsync(
                    $"Bulk rejected {rejectedCount} duplicate temp members",
                    null,
                    ActionSource.AdminPanel,
                    rejectedBy,
                    null);

                await transaction.CommitAsync();
                return rejectedCount;
            }
            catch
            {
                await transaction.RollbackAsync();
                throw;
            }
        }

        /// <summary>
        /// Gets a field value from a member object using reflection
        /// </summary>
        private string? GetFieldValue(Member member, string fieldName)
        {
            var property = typeof(Member).GetProperty(fieldName);
            var value = property?.GetValue(member);
            
            if (value is DateTime dateTime)
                return dateTime.ToString("yyyy-MM-dd");
            
            return value?.ToString();
        }

        /// <summary>
        /// Sets a field value on a member object using reflection
        /// </summary>
        private void SetFieldValue(Member member, string fieldName, string? value)
        {
            var property = typeof(Member).GetProperty(fieldName);
            if (property == null) return;

            if (property.PropertyType == typeof(DateTime) || property.PropertyType == typeof(DateTime?))
            {
                if (DateTime.TryParse(value, out var dateValue))
                    property.SetValue(member, dateValue);
            }
            else if (property.PropertyType == typeof(string))
            {
                property.SetValue(member, value);
            }
        }
    }
}