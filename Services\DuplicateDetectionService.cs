using Microsoft.EntityFrameworkCore;
using ParaHockeyApp.Models;
using ParaHockeyApp.Models.Entities;
using System.Text.Json;

namespace ParaHockeyApp.Services
{
    /// <summary>
    /// Service for detecting and resolving duplicate members during import
    /// </summary>
    public class DuplicateDetectionService : IDuplicateDetectionService
    {
        private readonly ApplicationContext _context;
        private readonly INormalizationService _normalizationService;
        private readonly IAuditLogService _auditLogService;

        public DuplicateDetectionService(
            ApplicationContext context,
            INormalizationService normalizationService,
            IAuditLogService auditLogService)
        {
            _context = context;
            _normalizationService = normalizationService;
            _auditLogService = auditLogService;
        }

        /// <summary>
        /// Finds existing member that matches the temp member using duplicate detection logic
        /// Priority: Email match first, then FirstName + LastName + DateOfBirth match
        /// </summary>
        public async Task<Member?> FindExistingMemberAsync(TempMember tempMember)
        {
            if (tempMember == null)
                return null;

            // Priority 1: Email match (if email exists and is valid)
            if (!string.IsNullOrEmpty(tempMember.Email))
            {
                var normalizedEmail = _normalizationService.NormalizeEmail(tempMember.Email);
                if (!string.IsNullOrEmpty(normalizedEmail))
                {
                    var memberByEmail = await FindMemberByEmailAsync(normalizedEmail);
                    if (memberByEmail != null)
                        return memberByEmail;
                }
            }

            // Priority 2: Name + DOB match (if all fields exist)
            if (!string.IsNullOrEmpty(tempMember.FirstName) && 
                !string.IsNullOrEmpty(tempMember.LastName) && 
                tempMember.DateOfBirth.HasValue)
            {
                var memberByNameDob = await FindMemberByNameAndDobAsync(
                    tempMember.FirstName,
                    tempMember.LastName,
                    tempMember.DateOfBirth.Value);
                
                if (memberByNameDob != null)
                    return memberByNameDob;
            }

            return null;
        }

        /// <summary>
        /// Finds existing member by email (case-insensitive exact match)
        /// </summary>
        public async Task<Member?> FindMemberByEmailAsync(string email)
        {
            if (string.IsNullOrWhiteSpace(email))
                return null;

            var normalizedEmail = _normalizationService.NormalizeEmail(email);
            if (string.IsNullOrEmpty(normalizedEmail))
                return null;

            return await _context.Members
                .Where(m => m.IsActive && m.Email.ToLower() == normalizedEmail.ToLower())
                .FirstOrDefaultAsync();
        }

        /// <summary>
        /// Finds existing member by name and date of birth (exact match)
        /// </summary>
        public async Task<Member?> FindMemberByNameAndDobAsync(string firstName, string lastName, DateTime dateOfBirth)
        {
            if (string.IsNullOrWhiteSpace(firstName) || string.IsNullOrWhiteSpace(lastName))
                return null;

            var normalizedFirstName = _normalizationService.NormalizeName(firstName);
            var normalizedLastName = _normalizationService.NormalizeName(lastName);

            if (string.IsNullOrEmpty(normalizedFirstName) || string.IsNullOrEmpty(normalizedLastName))
                return null;

            return await _context.Members
                .Where(m => m.IsActive && 
                           m.FirstName == normalizedFirstName &&
                           m.LastName == normalizedLastName &&
                           m.DateOfBirth.Date == dateOfBirth.Date)
                .FirstOrDefaultAsync();
        }

        /// <summary>
        /// Previews merge operation showing field-by-field comparison
        /// </summary>
        public async Task<DuplicateResolutionPreview> PreviewMergeAsync(Guid tempMemberId, Dictionary<string, string> fieldChoices)
        {
            var tempMember = await _context.TempMembers
                .Include(tm => tm.ExistingMember)
                .FirstOrDefaultAsync(tm => tm.TempMemberId == tempMemberId);

            if (tempMember?.ExistingMember == null)
                throw new InvalidOperationException("Temp member or existing member not found");

            var changes = new Dictionary<string, FieldChange>();

            // Get all comparable fields
            var fieldComparisons = GetFieldComparisons(tempMember, tempMember.ExistingMember);

            foreach (var field in fieldComparisons)
            {
                var fieldName = field.Key;
                var comparison = field.Value;

                // Skip if identical values
                if (comparison.AreIdentical)
                    continue;

                // Check if user selected temp value and it's different from existing
                if (fieldChoices.ContainsKey(fieldName) && fieldChoices[fieldName] == "temp")
                {
                    changes[fieldName] = new FieldChange
                    {
                        FieldName = fieldName,
                        CurrentValue = comparison.ExistingValue,
                        NewValue = comparison.TempValue,
                        WillChange = true
                    };
                }
            }

            return new DuplicateResolutionPreview
            {
                TempMember = tempMember,
                ExistingMember = tempMember.ExistingMember,
                Changes = changes
            };
        }

        /// <summary>
        /// Applies merge operation using selected field choices
        /// </summary>
        public async Task<Member> ApplyMergeAsync(Guid tempMemberId, Dictionary<string, string> fieldChoices, string performedBy)
        {
            using var transaction = await _context.Database.BeginTransactionAsync();

            try
            {
                var tempMember = await _context.TempMembers
                    .Include(tm => tm.ExistingMember)
                    .FirstOrDefaultAsync(tm => tm.TempMemberId == tempMemberId);

                if (tempMember?.ExistingMember == null)
                    throw new InvalidOperationException("Temp member or existing member not found");

                var existingMember = tempMember.ExistingMember;
                var originalValues = new Dictionary<string, object?>();

                // Apply selected field changes
                var fieldComparisons = GetFieldComparisons(tempMember, existingMember);

                foreach (var field in fieldComparisons)
                {
                    var fieldName = field.Key;
                    var comparison = field.Value;

                    // Skip if identical values
                    if (comparison.AreIdentical)
                        continue;

                    // Apply temp value if selected
                    if (fieldChoices.ContainsKey(fieldName) && fieldChoices[fieldName] == "temp")
                    {
                        originalValues[fieldName] = GetMemberFieldValue(existingMember, fieldName);
                        SetMemberFieldValue(existingMember, fieldName, comparison.TempValue);
                    }
                }

                // Update temp member status
                tempMember.Status = TempMemberStatus.Merged;
                
                // Save changes
                await _context.SaveChangesAsync();

                // Log the merge operation
                if (originalValues.Any())
                {
                    await _auditLogService.LogMemberMergeAsync(
                        existingMember.Id,
                        originalValues,
                        GetMemberCurrentValues(existingMember, originalValues.Keys),
                        performedBy);
                }

                await transaction.CommitAsync();
                return existingMember;
            }
            catch
            {
                await transaction.RollbackAsync();
                throw;
            }
        }

        /// <summary>
        /// Gets field-by-field comparison between temp member and existing member
        /// </summary>
        public Dictionary<string, FieldComparison> GetFieldComparisons(TempMember tempMember, Member existingMember)
        {
            var comparisons = new Dictionary<string, FieldComparison>();

            // Compare basic fields
            AddFieldComparison(comparisons, "FirstName", tempMember.FirstName, existingMember.FirstName);
            AddFieldComparison(comparisons, "LastName", tempMember.LastName, existingMember.LastName);
            AddFieldComparison(comparisons, "Email", tempMember.Email, existingMember.Email);
            AddFieldComparison(comparisons, "Phone", tempMember.Phone, existingMember.Phone);
            AddFieldComparison(comparisons, "Address", tempMember.Address, existingMember.Address);
            AddFieldComparison(comparisons, "City", tempMember.City, existingMember.City);
            AddFieldComparison(comparisons, "PostalCode", tempMember.PostalCode, existingMember.PostalCode);
            
            // Compare date fields
            AddFieldComparison(comparisons, "DateOfBirth", 
                tempMember.DateOfBirth?.ToString("yyyy-MM-dd"), 
                existingMember.DateOfBirth.ToString("yyyy-MM-dd"));

            return comparisons;
        }

        private void AddFieldComparison(Dictionary<string, FieldComparison> comparisons, string fieldName, string? tempValue, string? existingValue)
        {
            tempValue ??= string.Empty;
            existingValue ??= string.Empty;

            comparisons[fieldName] = new FieldComparison
            {
                FieldName = fieldName,
                TempValue = tempValue,
                ExistingValue = existingValue,
                AreIdentical = string.Equals(tempValue, existingValue, StringComparison.OrdinalIgnoreCase),
                SelectedValue = existingValue // Default to existing value
            };
        }

        private object? GetMemberFieldValue(Member member, string fieldName)
        {
            return fieldName switch
            {
                "FirstName" => member.FirstName,
                "LastName" => member.LastName,
                "Email" => member.Email,
                "Phone" => member.Phone,
                "Address" => member.Address,
                "City" => member.City,
                "PostalCode" => member.PostalCode,
                "DateOfBirth" => member.DateOfBirth,
                _ => null
            };
        }

        private void SetMemberFieldValue(Member member, string fieldName, string value)
        {
            switch (fieldName)
            {
                case "FirstName":
                    member.FirstName = value;
                    break;
                case "LastName":
                    member.LastName = value;
                    break;
                case "Email":
                    member.Email = value;
                    break;
                case "Phone":
                    member.Phone = value;
                    break;
                case "Address":
                    member.Address = value;
                    break;
                case "City":
                    member.City = value;
                    break;
                case "PostalCode":
                    member.PostalCode = value;
                    break;
                case "DateOfBirth":
                    if (DateTime.TryParse(value, out var date))
                        member.DateOfBirth = date;
                    break;
            }
        }

        private Dictionary<string, object?> GetMemberCurrentValues(Member member, IEnumerable<string> fieldNames)
        {
            var values = new Dictionary<string, object?>();
            foreach (var fieldName in fieldNames)
            {
                values[fieldName] = GetMemberFieldValue(member, fieldName);
            }
            return values;
        }
    }
}