@model IEnumerable<ParaHockeyApp.Models.Entities.AuditLog>
@{
    ViewData["Title"] = Localizer["ViewAllAuditHistory"];
}

<div class="container-fluid mt-4">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col">
            <h1 class="h2 text-primary">
                <i class="fas fa-history"></i> @SharedLocalizer["ViewAllAuditHistory"]
            </h1>
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="@Url.Action("Index", "Admin")">@SharedLocalizer["AdminPanel"]</a></li>
                    <li class="breadcrumb-item active" aria-current="page">@SharedLocalizer["ViewAllAuditHistory"]</li>
                </ol>
            </nav>
        </div>
    </div>

    <!-- Filters -->
    <div class="row mb-4">
        <div class="col">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-filter"></i> @SharedLocalizer["Filters"]
                    </h5>
                </div>
                <div class="card-body">
                    <form method="get" action="@Url.Action("AllAuditHistory", "Admin")">
                        <div class="row">
                            <div class="col-md-4">
                                <label for="entityType" class="form-label">@SharedLocalizer["EntityType"]</label>
                                <select name="entityType" id="entityType" class="form-select">
                                    <option value="">@SharedLocalizer["AllEntityTypes"]</option>
                                    @foreach (var entityType in (List<string>)ViewBag.EntityTypes)
                                    {
                                        <option value="@entityType" selected="@(ViewBag.EntityType == entityType)">
                                            @entityType
                                        </option>
                                    }
                                </select>
                            </div>
                            <div class="col-md-4">
                                <label for="action" class="form-label">@SharedLocalizer["Action"]</label>
                                <select name="action" id="action" class="form-select">
                                    <option value="">@SharedLocalizer["AllActions"]</option>
                                    @foreach (var action in (List<string>)ViewBag.Actions)
                                    {
                                        <option value="@action" selected="@(ViewBag.Action == action)">
                                            @SharedLocalizer[action]
                                        </option>
                                    }
                                </select>
                            </div>
                            <div class="col-md-4 d-flex align-items-end">
                                <button type="submit" class="btn btn-primary me-2">
                                    <i class="fas fa-search"></i> @SharedLocalizer["Filter"]
                                </button>
                                <a href="@Url.Action("AllAuditHistory", "Admin")" class="btn btn-outline-secondary">
                                    <i class="fas fa-times"></i> @SharedLocalizer["Clear"]
                                </a>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Results Summary -->
    <div class="row mb-3">
        <div class="col">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <span class="text-muted">
                        @SharedLocalizer["ShowingResults", (ViewBag.CurrentPage - 1) * ViewBag.PageSize + 1, Math.Min(ViewBag.CurrentPage * ViewBag.PageSize, ViewBag.TotalRecords), ViewBag.TotalRecords]
                    </span>
                </div>
                <div>
                    <a href="@Url.Action("Index", "Admin")" class="btn btn-outline-primary">
                        <i class="fas fa-arrow-left"></i> @SharedLocalizer["BackToDashboard"]
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Audit History Table -->
    <div class="row">
        <div class="col">
            <div class="card">
                <div class="card-body">
                    @if (Model.Any())
                    {
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>@SharedLocalizer["AuditDateTime"]</th>
                                        <th>@SharedLocalizer["EntityType"]</th>
                                        <th>@SharedLocalizer["Action"]</th>
                                        <th>@SharedLocalizer["PerformedBy"]</th>
                                        <th>@SharedLocalizer["Description"]</th>
                                        <th>@SharedLocalizer["Source"]</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach (var auditLog in Model)
                                    {
                                        <tr>
                                            <td>
                                                <small class="text-muted">@auditLog.Timestamp.ToString("yyyy-MM-dd")</small><br>
                                                <strong>@auditLog.Timestamp.ToString("HH:mm:ss")</strong>
                                            </td>
                                            <td>
                                                <span class="badge bg-light text-dark">@auditLog.EntityType</span>
                                                <small class="d-block text-muted">ID: @auditLog.EntityId</small>
                                            </td>
                                            <td>
                                                @if (auditLog.Action == "Create")
                                                {
                                                    <span class="badge bg-success">@SharedLocalizer["Create"]</span>
                                                }
                                                else if (auditLog.Action == "Update")
                                                {
                                                    <span class="badge bg-warning">@SharedLocalizer["Update"]</span>
                                                }
                                                else if (auditLog.Action == "Delete")
                                                {
                                                    <span class="badge bg-danger">@SharedLocalizer["Delete"]</span>
                                                }
                                                else
                                                {
                                                    <span class="badge bg-secondary">@auditLog.Action</span>
                                                }
                                            </td>
                                            <td>
                                                <strong>@auditLog.PerformerName</strong>
                                                @if (auditLog.PerformedBySource == ParaHockeyApp.Models.Entities.ActionSource.AdminPanel)
                                                {
                                                    <br><small class="text-danger">@SharedLocalizer["Admin"]</small>
                                                }
                                                else if (auditLog.PerformedBySource == ParaHockeyApp.Models.Entities.ActionSource.SelfService)
                                                {
                                                    <br><small class="text-primary">@SharedLocalizer["Member"]</small>
                                                }
                                                else
                                                {
                                                    <br><small class="text-secondary">@SharedLocalizer["System"]</small>
                                                }
                                            </td>
                                            <td>
                                                @if (!string.IsNullOrEmpty(auditLog.Description))
                                                {
                                                    <span title="@auditLog.Description">@auditLog.ShortDescription</span>
                                                }
                                                else
                                                {
                                                    <em class="text-muted">@SharedLocalizer["NoDescriptionAvailable"]</em>
                                                }
                                            </td>
                                            <td>
                                                @if (!string.IsNullOrEmpty(auditLog.IPAddress))
                                                {
                                                    <small class="text-muted">@auditLog.IPAddress</small>
                                                }
                                            </td>
                                        </tr>
                                    }
                                </tbody>
                            </table>
                        </div>

                        <!-- Pagination -->
                        @if (ViewBag.TotalPages > 1)
                        {
                            <nav aria-label="Audit history pagination" class="mt-4">
                                <ul class="pagination justify-content-center">
                                    @if (ViewBag.CurrentPage > 1)
                                    {
                                        <li class="page-item">
                                            <a class="page-link" href="@Url.Action("AllAuditHistory", "Admin", new { page = ViewBag.CurrentPage - 1, entityType = ViewBag.EntityType, action = ViewBag.Action })">
                                                @SharedLocalizer["Previous"]
                                            </a>
                                        </li>
                                    }

                                    @for (int i = Math.Max(1, ViewBag.CurrentPage - 2); i <= Math.Min(ViewBag.TotalPages, ViewBag.CurrentPage + 2); i++)
                                    {
                                        <li class="page-item @(i == ViewBag.CurrentPage ? "active" : "")">
                                            <a class="page-link" href="@Url.Action("AllAuditHistory", "Admin", new { page = i, entityType = ViewBag.EntityType, action = ViewBag.Action })">
                                                @i
                                            </a>
                                        </li>
                                    }

                                    @if (ViewBag.CurrentPage < ViewBag.TotalPages)
                                    {
                                        <li class="page-item">
                                            <a class="page-link" href="@Url.Action("AllAuditHistory", "Admin", new { page = ViewBag.CurrentPage + 1, entityType = ViewBag.EntityType, action = ViewBag.Action })">
                                                @SharedLocalizer["Next"]
                                            </a>
                                        </li>
                                    }
                                </ul>
                            </nav>
                        }
                    }
                    else
                    {
                        var emptyState = ViewBag.EmptyState as ParaHockeyApp.Models.ViewModels.EmptyStateViewModel;
                        @if (emptyState != null)
                        {
                            <div class="text-center py-5">
                                <i class="@emptyState.IconClass fa-3x text-muted mb-3"></i>
                                <h5 class="text-muted">@emptyState.Title</h5>
                                <p class="text-muted">@emptyState.Message</p>
                                @if (emptyState.ShowAction && !string.IsNullOrEmpty(emptyState.ActionUrl) && !string.IsNullOrEmpty(emptyState.ActionText))
                                {
                                    <a href="@emptyState.ActionUrl" class="btn btn-primary">
                                        @emptyState.ActionText
                                    </a>
                                }
                            </div>
                        }
                        else
                        {
                            <!-- Fallback to original empty state -->
                            <div class="text-center py-5">
                                <i class="fas fa-history fa-3x text-muted mb-3"></i>
                                <h5 class="text-muted">@SharedLocalizer["NoAuditLogsFound"]</h5>
                                <p class="text-muted">@SharedLocalizer["NoAuditLogsFoundDesc"]</p>
                                <a href="@Url.Action("AllAuditHistory", "Admin")" class="btn btn-primary">
                                    @SharedLocalizer["ViewAllRecords"]
                                </a>
                            </div>
                        }
                    }
                </div>
            </div>
        </div>
    </div>
</div>