@{
    ViewData["Title"] = Localizer["AccessDenied"];
}

<div class="container mt-4">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header bg-danger text-white">
                    <h3 class="mb-0">
                        <i class="fas fa-ban"></i> @SharedLocalizer["AccessDenied"]
                    </h3>
                </div>
                <div class="card-body text-center">
                    <div class="alert alert-danger">
                        <i class="fas fa-exclamation-triangle fa-3x mb-3"></i>
                        <h4>@SharedLocalizer["NoPermissionMessage"]</h4>
                        <p>@SharedLocalizer["OnlyAdminsMessage"]</p>
                    </div>
                    
                    @if (User.Identity?.IsAuthenticated == true)
                    {
                        <p>@SharedLocalizer["YouAreLoggedInAs"] <strong>@User.Identity.Name</strong></p>
                        <p>@SharedLocalizer["ContactAdminMessage"]</p>
                    }
                    else
                    {
                        <p>@SharedLocalizer["PleaseLoginMessage"]</p>
                        <a href="@Url.Action("SignIn", "Account", new { area = "MicrosoftIdentity" })" class="btn btn-primary">
                            <i class="fas fa-sign-in-alt"></i> @SharedLocalizer["Login"]
                        </a>
                    }
                    
                    <div class="mt-4">
                        <a href="@Url.Action("Index", "Home")" class="btn btn-secondary">
                            <i class="fas fa-home"></i> @SharedLocalizer["ReturnToHome"]
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>