# ParaHockey Registration Auto-Fill with Browser Automation
# This script actually fills the registration form in the browser

param(
    [Parameter(Mandatory=$true)]
    [ValidateSet("Junior", "Development", "Elite", "Coach", "Volunteer")]
    [string]$MemberType,
    
    [Parameter(Mandatory=$false)]
    [int]$Count = 1,
    
    [Parameter(Mandatory=$false)]
    [string]$Url = "http://localhost:5285/Members/Register"
)

# Check if Selenium WebDriver module is available
Write-Host "🔍 Checking for Selenium module..." -ForegroundColor Yellow
if (!(Get-Module -ListAvailable -Name "Selenium")) {
    Write-Host "❌ Selenium module not found. Installing..." -ForegroundColor Red
    try {
        Write-Host "📦 Installing Selenium module (this may take a moment)..." -ForegroundColor Yellow
        Install-Module -Name Selenium -Force -Scope CurrentUser -AllowClobber
        Write-Host "✅ Selenium installed successfully" -ForegroundColor Green
    } catch {
        Write-Host "❌ Failed to install Selenium: $($_.Exception.Message)" -ForegroundColor Red
        Write-Host "❌ Please install manually: Install-Module -Name Selenium -Force" -ForegroundColor Red
        return
    }
}

Write-Host "📥 Importing Selenium module..." -ForegroundColor Yellow
try {
    Import-Module Selenium -Force
    Write-Host "✅ Selenium module loaded successfully" -ForegroundColor Green
} catch {
    Write-Host "❌ Failed to import Selenium: $($_.Exception.Message)" -ForegroundColor Red
    return
}

# Counter file to track incremental numbers
$counterFile = "registration-counter.txt"
$baseCounter = 1

if (Test-Path $counterFile) {
    $baseCounter = [int](Get-Content $counterFile)
}

# Sample data pools (same as before)
$firstNames = @("Alexandre", "Marie", "Jean", "Sophie", "Pierre", "Catherine", "Michel", "Isabelle", "David", "Nathalie")
$lastNames = @("Tremblay", "Gagnon", "Roy", "Côté", "Bouchard", "Gauthier", "Morin", "Lavoie", "Fortin", "Gagné")
$cities = @("Montréal", "Québec", "Laval", "Gatineau", "Longueuil", "Sherbrooke")
$addresses = @("123 rue Principale", "456 avenue des Érables", "789 boulevard Saint-Laurent", "321 rue de la Paix")

function Get-RandomPostalCode {
    $letters = "ABCDEFGHIJKLMNOPQRSTUVWXYZ"
    $l1 = $letters[(Get-Random -Maximum 26)]
    $n1 = Get-Random -Maximum 10
    $l2 = $letters[(Get-Random -Maximum 26)]
    $n2 = Get-Random -Maximum 10
    $l3 = $letters[(Get-Random -Maximum 26)]
    $n3 = Get-Random -Maximum 10
    return "$l1$n1$l2 $n2$l3$n3"
}

function Get-RandomPhone {
    $area = Get-Random -Minimum 400 -Maximum 999
    $exchange = Get-Random -Minimum 200 -Maximum 999
    $number = Get-Random -Minimum 1000 -Maximum 9999
    return "($area) $exchange-$number"
}

function Get-RandomEmail {
    param($firstName, $lastName, $counter)
    $domains = @("gmail.com", "outlook.com", "yahoo.ca")
    $domain = $domains[(Get-Random -Maximum $domains.Count)]
    return "$firstName.$lastName$counter@$domain".ToLower()
}

function Get-RandomBirthDate {
    param($memberType)
    $today = Get-Date
    switch ($memberType) {
        "Junior" { 
            $yearsAgo = Get-Random -Minimum 8 -Maximum 18
            return $today.AddYears(-$yearsAgo).ToString("yyyy-MM-dd")
        }
        "Development" { 
            $yearsAgo = Get-Random -Minimum 16 -Maximum 26
            return $today.AddYears(-$yearsAgo).ToString("yyyy-MM-dd")
        }
        "Elite" { 
            $yearsAgo = Get-Random -Minimum 18 -Maximum 36
            return $today.AddYears(-$yearsAgo).ToString("yyyy-MM-dd")
        }
        "Coach" { 
            $yearsAgo = Get-Random -Minimum 25 -Maximum 56
            return $today.AddYears(-$yearsAgo).ToString("yyyy-MM-dd")
        }
        "Volunteer" { 
            $yearsAgo = Get-Random -Minimum 18 -Maximum 71
            return $today.AddYears(-$yearsAgo).ToString("yyyy-MM-dd")
        }
    }
}

function Get-RegistrationTypeId {
    param($memberType)
    switch ($memberType) {
        "Junior" { return "1" }
        "Development" { return "2" }
        "Elite" { return "3" }
        "Coach" { return "4" }
        "Volunteer" { return "5" }
    }
}

function Fill-Form {
    param($driver, $memberData)
    
    Write-Host "🖱️ Filling form for $($memberData.FirstName) $($memberData.LastName)..." -ForegroundColor Yellow
    
    try {
        # Wait for page to be ready
        Start-Sleep -Seconds 2
        
        # Fill basic information
        $driver.FindElement([OpenQA.Selenium.By]::Name("FirstName")).Clear()
        $driver.FindElement([OpenQA.Selenium.By]::Name("FirstName")).SendKeys($memberData.FirstName)
        
        $driver.FindElement([OpenQA.Selenium.By]::Name("LastName")).Clear()
        $driver.FindElement([OpenQA.Selenium.By]::Name("LastName")).SendKeys($memberData.LastName)
        
        $driver.FindElement([OpenQA.Selenium.By]::Name("Email")).Clear()
        $driver.FindElement([OpenQA.Selenium.By]::Name("Email")).SendKeys($memberData.Email)
        
        $driver.FindElement([OpenQA.Selenium.By]::Name("Phone")).Clear()
        $driver.FindElement([OpenQA.Selenium.By]::Name("Phone")).SendKeys($memberData.Phone)
        
        $driver.FindElement([OpenQA.Selenium.By]::Name("DateOfBirth")).Clear()
        $driver.FindElement([OpenQA.Selenium.By]::Name("DateOfBirth")).SendKeys($memberData.BirthDate)
        
        $driver.FindElement([OpenQA.Selenium.By]::Name("Address")).Clear()
        $driver.FindElement([OpenQA.Selenium.By]::Name("Address")).SendKeys($memberData.Address)
        
        $driver.FindElement([OpenQA.Selenium.By]::Name("City")).Clear()
        $driver.FindElement([OpenQA.Selenium.By]::Name("City")).SendKeys($memberData.City)
        
        $driver.FindElement([OpenQA.Selenium.By]::Name("PostalCode")).Clear()
        $driver.FindElement([OpenQA.Selenium.By]::Name("PostalCode")).SendKeys($memberData.PostalCode)
        
        # Select radio buttons instead of dropdowns
        Write-Host "Selecting registration type: $($memberData.RegistrationTypeId)" -ForegroundColor Gray
        $regTypeRadio = $driver.FindElement([OpenQA.Selenium.By]::Id("reg-$($memberData.RegistrationTypeId)"))
        $regTypeRadio.Click()
        
        Write-Host "Selecting gender: $($memberData.GenderId)" -ForegroundColor Gray
        $genderRadio = $driver.FindElement([OpenQA.Selenium.By]::Id("gender-$($memberData.GenderId)"))
        $genderRadio.Click()
        
        Write-Host "Selecting province: $($memberData.ProvinceId)" -ForegroundColor Gray
        $provinceSelect = New-Object OpenQA.Selenium.Support.UI.SelectElement($driver.FindElement([OpenQA.Selenium.By]::Name("ProvinceId")))
        $provinceSelect.SelectByValue($memberData.ProvinceId)
        
        Write-Host "Selecting phone type: $($memberData.PhoneTypeId)" -ForegroundColor Gray
        $phoneTypeRadio = $driver.FindElement([OpenQA.Selenium.By]::Id("phone-$($memberData.PhoneTypeId)"))
        $phoneTypeRadio.Click()
        
        # Give the form time to process the registration type selection
        Start-Sleep -Seconds 1
        
        # Handle Junior parent information or Emergency contact
        if ($memberData.MemberType -eq "Junior") {
            Write-Host "Filling parent information..." -ForegroundColor Gray
            try {
                # Set parent type
                $driver.FindElement([OpenQA.Selenium.By]::Name("Parents[0].ParentType")).Clear()
                $driver.FindElement([OpenQA.Selenium.By]::Name("Parents[0].ParentType")).SendKeys("Parent")
                
                $driver.FindElement([OpenQA.Selenium.By]::Name("Parents[0].FirstName")).Clear()
                $driver.FindElement([OpenQA.Selenium.By]::Name("Parents[0].FirstName")).SendKeys($memberData.ParentFirstName)
                
                $driver.FindElement([OpenQA.Selenium.By]::Name("Parents[0].LastName")).Clear()
                $driver.FindElement([OpenQA.Selenium.By]::Name("Parents[0].LastName")).SendKeys($memberData.ParentLastName)
                
                $driver.FindElement([OpenQA.Selenium.By]::Name("Parents[0].Phone")).Clear()
                $driver.FindElement([OpenQA.Selenium.By]::Name("Parents[0].Phone")).SendKeys($memberData.ParentPhone)
                
                # Email is optional for parents
                try {
                    $driver.FindElement([OpenQA.Selenium.By]::Name("Parents[0].Email")).Clear()
                    $driver.FindElement([OpenQA.Selenium.By]::Name("Parents[0].Email")).SendKeys($memberData.ParentEmail)
                } catch {
                    Write-Host "⚠️ Parent email field not found (optional)" -ForegroundColor Yellow
                }
                
                Write-Host "✅ Parent information filled" -ForegroundColor Green
            } catch {
                Write-Host "⚠️ Could not fill parent fields: $($_.Exception.Message)" -ForegroundColor Yellow
            }
        } else {
            Write-Host "Filling emergency contact information..." -ForegroundColor Gray
            try {
                $driver.FindElement([OpenQA.Selenium.By]::Name("EmergencyContact.FirstName")).Clear()
                $driver.FindElement([OpenQA.Selenium.By]::Name("EmergencyContact.FirstName")).SendKeys($memberData.EmergencyFirstName)
                
                $driver.FindElement([OpenQA.Selenium.By]::Name("EmergencyContact.LastName")).Clear()
                $driver.FindElement([OpenQA.Selenium.By]::Name("EmergencyContact.LastName")).SendKeys($memberData.EmergencyLastName)
                
                $driver.FindElement([OpenQA.Selenium.By]::Name("EmergencyContact.Phone")).Clear()
                $driver.FindElement([OpenQA.Selenium.By]::Name("EmergencyContact.Phone")).SendKeys($memberData.EmergencyPhone)
                
                $driver.FindElement([OpenQA.Selenium.By]::Name("EmergencyContact.RelationToUser")).Clear()
                $driver.FindElement([OpenQA.Selenium.By]::Name("EmergencyContact.RelationToUser")).SendKeys("Friend")
                
                # Email is optional for emergency contact
                try {
                    $driver.FindElement([OpenQA.Selenium.By]::Name("EmergencyContact.Email")).Clear()
                    $driver.FindElement([OpenQA.Selenium.By]::Name("EmergencyContact.Email")).SendKeys($memberData.EmergencyEmail)
                } catch {
                    Write-Host "⚠️ Emergency contact email field not found (optional)" -ForegroundColor Yellow
                }
                
                Write-Host "✅ Emergency contact information filled" -ForegroundColor Green
            } catch {
                Write-Host "⚠️ Could not fill emergency contact fields: $($_.Exception.Message)" -ForegroundColor Yellow
            }
        }
        
        Write-Host "✅ Form filled successfully!" -ForegroundColor Green
        
        # Ask user if they want to submit
        Write-Host ""
        $submit = Read-Host "Submit this registration? (y/n/s to skip remaining)"
        if ($submit -eq "y" -or $submit -eq "Y") {
            $submitButton = $driver.FindElement([OpenQA.Selenium.By]::XPath("//button[@type='submit']"))
            $submitButton.Click()
            Write-Host "✅ Registration submitted!" -ForegroundColor Green
            Start-Sleep -Seconds 5
            return "submitted"
        } elseif ($submit -eq "s" -or $submit -eq "S") {
            return "skip"
        }
        
    } catch {
        Write-Host "❌ Error filling form: $($_.Exception.Message)" -ForegroundColor Red
        Write-Host "❌ Error details: $($_.ScriptStackTrace)" -ForegroundColor Red
        return $false
    }
    
    return $true
}

# Main script
Write-Host "🏒 ParaHockey Browser Auto-Fill" -ForegroundColor Cyan
Write-Host "================================" -ForegroundColor Cyan

try {
    # Try to start browser (Chrome first, then Edge)
    $driver = $null
    Write-Host "🌐 Starting browser..." -ForegroundColor Yellow
    
    # Try Chrome first
    try {
        Write-Host "🔍 Trying Chrome..." -ForegroundColor Gray
        $driver = Start-SeChrome
        Write-Host "✅ Chrome browser started successfully" -ForegroundColor Green
    } catch {
        Write-Host "⚠️ Chrome failed: $($_.Exception.Message)" -ForegroundColor Yellow
        
        # Try Edge as fallback
        try {
            Write-Host "🔍 Trying Edge..." -ForegroundColor Gray
            $driver = Start-SeEdge
            Write-Host "✅ Edge browser started successfully" -ForegroundColor Green
        } catch {
            Write-Host "❌ Failed to start both Chrome and Edge: $($_.Exception.Message)" -ForegroundColor Red
            Write-Host "💡 Please install Chrome or make sure Edge is updated" -ForegroundColor Yellow
            return
        }
    }
    
    # Navigate to registration page
    Write-Host "📄 Opening registration page: $Url" -ForegroundColor Yellow
    try {
        $driver.Navigate().GoToUrl($Url)
        Start-Sleep -Seconds 3
        Write-Host "✅ Registration page loaded" -ForegroundColor Green
    } catch {
        Write-Host "❌ Failed to load registration page: $($_.Exception.Message)" -ForegroundColor Red
        Write-Host "💡 Make sure the ParaHockey app is running at $Url" -ForegroundColor Yellow
        if ($driver) { $driver.Quit() }
        return
    }
    
    for ($i = 0; $i -lt $Count; $i++) {
        $currentCounter = $baseCounter + $i
        
        # Generate member data
        $memberData = @{
            MemberType = $MemberType
            FirstName = ($firstNames | Get-Random) + $currentCounter
            LastName = ($lastNames | Get-Random) + $currentCounter
            Email = ""
            Phone = Get-RandomPhone
            BirthDate = Get-RandomBirthDate -memberType $MemberType
            Address = $addresses | Get-Random
            City = $cities | Get-Random
            PostalCode = Get-RandomPostalCode
            RegistrationTypeId = Get-RegistrationTypeId -memberType $MemberType
            GenderId = (Get-Random -Minimum 1 -Maximum 4).ToString()
            ProvinceId = (if ((Get-Random -Maximum 10) -lt 7) { 1 } else { Get-Random -Minimum 1 -Maximum 14 }).ToString()
            PhoneTypeId = (if ((Get-Random -Maximum 10) -lt 8) { 1 } else { 2 }).ToString()
        }
        
        $memberData.Email = Get-RandomEmail -firstName $memberData.FirstName -lastName $memberData.LastName -counter $currentCounter
        
        # Add parent or emergency contact data
        if ($MemberType -eq "Junior") {
            $memberData.ParentFirstName = ($firstNames | Get-Random) + "_Parent$currentCounter"
            $memberData.ParentLastName = $memberData.LastName
            $memberData.ParentEmail = Get-RandomEmail -firstName $memberData.ParentFirstName -lastName $memberData.ParentLastName -counter $currentCounter
            $memberData.ParentPhone = Get-RandomPhone
        } else {
            $memberData.EmergencyFirstName = ($firstNames | Get-Random) + "_Emergency$currentCounter"
            $memberData.EmergencyLastName = $memberData.LastName
            $memberData.EmergencyEmail = Get-RandomEmail -firstName $memberData.EmergencyFirstName -lastName $memberData.EmergencyLastName -counter $currentCounter
            $memberData.EmergencyPhone = Get-RandomPhone
        }
        
        # Fill the form
        $result = Fill-Form -driver $driver -memberData $memberData
        if ($result -eq "submitted") {
            Write-Host "✅ Member $($i + 1)/$Count submitted successfully" -ForegroundColor Green
            # Navigate back to registration page
            $driver.Navigate().GoToUrl($Url)
            Start-Sleep -Seconds 3
        } elseif ($result -eq "skip") {
            Write-Host "⏭️ Skipping remaining members..." -ForegroundColor Yellow
            break
        } elseif ($result) {
            Write-Host "✅ Member $($i + 1)/$Count filled (not submitted)" -ForegroundColor Green
            # If more members to process, refresh page
            if ($i -lt ($Count - 1)) {
                Write-Host "🔄 Refreshing page for next member..." -ForegroundColor Yellow
                $driver.Navigate().Refresh()
                Start-Sleep -Seconds 2
            }
        } else {
            Write-Host "❌ Failed to fill member $($i + 1)/$Count" -ForegroundColor Red
            # Try to refresh and continue
            if ($i -lt ($Count - 1)) {
                Write-Host "🔄 Refreshing page and continuing..." -ForegroundColor Yellow
                $driver.Navigate().Refresh()
                Start-Sleep -Seconds 2
            }
        }
    }
    
    # Update counter
    $newCounter = $baseCounter + $Count
    $newCounter | Out-File -FilePath $counterFile -Encoding UTF8
    
    Write-Host ""
    Write-Host "✅ All done! Browser will stay open for manual review." -ForegroundColor Green
    Write-Host "Press any key to close browser..."
    $null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
    
} catch {
    Write-Host "❌ Error: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "Falling back to JavaScript generation..." -ForegroundColor Yellow
    & .\auto-fill-registration.ps1 -MemberType $MemberType -Count $Count
} finally {
    if ($driver) {
        $driver.Quit()
    }
}