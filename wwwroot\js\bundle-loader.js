/**
 * Bundle Loader - Manages dynamic loading of JavaScript and CSS bundles
 * Supports lazy loading, dependency management, and performance optimization
 */
class BundleLoader {
    constructor() {
        this.loadedBundles = new Set();
        this.loadingPromises = new Map();
        this.bundleConfig = null;
        this.performanceObserver = null;
        
        this.initializePerformanceMonitoring();
    }

    /**
     * Initialize the bundle loader with configuration
     */
    async initialize() {
        try {
            // In a real implementation, this would fetch from an API endpoint
            // For now, we'll use a static configuration
            this.bundleConfig = await this.loadBundleConfiguration();
            console.log('Bundle loader initialized with configuration:', this.bundleConfig);
        } catch (error) {
            console.error('Failed to initialize bundle loader:', error);
        }
    }

    /**
     * Load a specific bundle by name
     */
    async loadBundle(bundleName) {
        if (this.loadedBundles.has(bundleName)) {
            return Promise.resolve();
        }

        if (this.loadingPromises.has(bundleName)) {
            return this.loadingPromises.get(bundleName);
        }

        const loadPromise = this._loadBundleInternal(bundleName);
        this.loadingPromises.set(bundleName, loadPromise);

        try {
            await loadPromise;
            this.loadedBundles.add(bundleName);
            this.loadingPromises.delete(bundleName);
            console.log(`Bundle '${bundleName}' loaded successfully`);
        } catch (error) {
            this.loadingPromises.delete(bundleName);
            console.error(`Failed to load bundle '${bundleName}':`, error);
            throw error;
        }

        return loadPromise;
    }

    /**
     * Load multiple bundles in parallel
     */
    async loadBundles(bundleNames) {
        const loadPromises = bundleNames.map(name => this.loadBundle(name));
        return Promise.all(loadPromises);
    }

    /**
     * Preload a bundle for future use
     */
    preloadBundle(bundleName) {
        if (!this.bundleConfig) {
            console.warn('Bundle configuration not loaded yet');
            return;
        }

        const bundle = this.findBundle(bundleName);
        if (!bundle) {
            console.warn(`Bundle '${bundleName}' not found in configuration`);
            return;
        }

        // Preload CSS files
        bundle.files.forEach(file => {
            if (file.endsWith('.css')) {
                this.preloadResource(file, 'style');
            } else if (file.endsWith('.js')) {
                this.preloadResource(file, 'script');
            }
        });
    }

    /**
     * Load bundles based on page requirements
     */
    async loadPageBundles(pageName) {
        const requiredBundles = this.getRequiredBundlesForPage(pageName);
        
        // Load critical bundles first
        const criticalBundles = requiredBundles.filter(name => {
            const bundle = this.findBundle(name);
            return bundle && bundle.loadStrategy === 'Critical';
        });

        if (criticalBundles.length > 0) {
            await this.loadBundles(criticalBundles);
        }

        // Load deferred bundles after a short delay
        const deferredBundles = requiredBundles.filter(name => {
            const bundle = this.findBundle(name);
            return bundle && bundle.loadStrategy === 'Deferred';
        });

        if (deferredBundles.length > 0) {
            setTimeout(() => {
                this.loadBundles(deferredBundles);
            }, 100);
        }

        // Preload lazy bundles for faster future loading
        const lazyBundles = requiredBundles.filter(name => {
            const bundle = this.findBundle(name);
            return bundle && bundle.loadStrategy === 'LazyLoad';
        });

        lazyBundles.forEach(bundleName => {
            this.preloadBundle(bundleName);
        });
    }

    /**
     * Internal method to load a bundle with dependency resolution
     */
    async _loadBundleInternal(bundleName) {
        const bundle = this.findBundle(bundleName);
        if (!bundle) {
            throw new Error(`Bundle '${bundleName}' not found in configuration`);
        }

        // Load dependencies first
        if (bundle.dependencies && bundle.dependencies.length > 0) {
            await this.loadBundles(bundle.dependencies);
        }

        // Load bundle files
        const loadPromises = bundle.files.map(file => {
            if (file.endsWith('.css')) {
                return this.loadStylesheet(file);
            } else if (file.endsWith('.js')) {
                return this.loadScript(file);
            }
            return Promise.resolve();
        });

        await Promise.all(loadPromises);
    }

    /**
     * Load a CSS file dynamically
     */
    loadStylesheet(href) {
        return new Promise((resolve, reject) => {
            // Check if already loaded
            const existing = document.querySelector(`link[href="${href}"]`);
            if (existing) {
                resolve();
                return;
            }

            const link = document.createElement('link');
            link.rel = 'stylesheet';
            link.href = href;
            
            link.onload = () => {
                console.log(`Stylesheet loaded: ${href}`);
                resolve();
            };
            
            link.onerror = () => {
                console.error(`Failed to load stylesheet: ${href}`);
                reject(new Error(`Failed to load stylesheet: ${href}`));
            };

            document.head.appendChild(link);
        });
    }

    /**
     * Load a JavaScript file dynamically
     */
    loadScript(src) {
        return new Promise((resolve, reject) => {
            // Check if already loaded
            const existing = document.querySelector(`script[src="${src}"]`);
            if (existing) {
                resolve();
                return;
            }

            const script = document.createElement('script');
            script.src = src;
            script.async = true;
            
            script.onload = () => {
                console.log(`Script loaded: ${src}`);
                resolve();
            };
            
            script.onerror = () => {
                console.error(`Failed to load script: ${src}`);
                reject(new Error(`Failed to load script: ${src}`));
            };

            document.head.appendChild(script);
        });
    }

    /**
     * Preload a resource using link rel="preload"
     */
    preloadResource(href, as) {
        const existing = document.querySelector(`link[href="${href}"][rel="preload"]`);
        if (existing) return;

        const link = document.createElement('link');
        link.rel = 'preload';
        link.href = href;
        link.as = as;
        
        if (as === 'style') {
            link.onload = function() {
                this.onload = null;
                this.rel = 'stylesheet';
            };
        }

        document.head.appendChild(link);
    }

    /**
     * Find a bundle in the configuration
     */
    findBundle(bundleName) {
        if (!this.bundleConfig) return null;

        // Search in CSS bundles
        let bundle = this.bundleConfig.cssBundles?.find(b => b.name === bundleName);
        if (bundle) return bundle;

        // Search in JavaScript bundles
        bundle = this.bundleConfig.javaScriptBundles?.find(b => b.name === bundleName);
        return bundle;
    }

    /**
     * Get required bundles for a specific page
     */
    getRequiredBundlesForPage(pageName) {
        const pageRequirements = {
            'home': ['core', 'site'],
            'register': ['core', 'forms', 'site'],
            'login': ['core', 'forms', 'site'],
            'admin': ['core', 'ui-components', 'site'],
            'members': ['core', 'forms', 'ui-components', 'site'],
            'events': ['core', 'ui-components', 'site']
        };

        return pageRequirements[pageName.toLowerCase()] || ['core', 'site'];
    }

    /**
     * Load bundle configuration (in real implementation, this would be an API call)
     */
    async loadBundleConfiguration() {
        // Simulate API call delay
        await new Promise(resolve => setTimeout(resolve, 50));

        return {
            cssBundles: [
                {
                    name: 'critical',
                    loadStrategy: 'Critical',
                    priority: 1,
                    files: [
                        '/css/shared/variables.css',
                        '/css/site.css',
                        '/css/forced-colors.css'
                    ]
                },
                {
                    name: 'enhanced-forms',
                    loadStrategy: 'Deferred',
                    priority: 2,
                    files: ['/css/enhanced-forms.css']
                },
                {
                    name: 'theme',
                    loadStrategy: 'Deferred',
                    priority: 3,
                    files: [
                        '/css/dark-overrides.css',
                        '/css/parahockey-design-system.css'
                    ]
                }
            ],
            javaScriptBundles: [
                {
                    name: 'core',
                    loadStrategy: 'Critical',
                    priority: 1,
                    files: [
                        '/lib/jquery/dist/jquery.min.js',
                        '/lib/bootstrap/dist/js/bootstrap.bundle.min.js'
                    ]
                },
                {
                    name: 'forms',
                    loadStrategy: 'LazyLoad',
                    priority: 2,
                    dependencies: ['core'],
                    files: [
                        '/js/enhanced-form-validation.js',
                        '/lib/jquery-validation/dist/jquery.validate.min.js',
                        '/lib/jquery-validation-unobtrusive/jquery.validate.unobtrusive.min.js'
                    ]
                },
                {
                    name: 'ui-components',
                    loadStrategy: 'LazyLoad',
                    priority: 3,
                    dependencies: ['core'],
                    files: [
                        '/js/datepicker-fr.js',
                        '/js/member-filters-export.js',
                        '/js/event-details-modal.js'
                    ]
                },
                {
                    name: 'site',
                    loadStrategy: 'Deferred',
                    priority: 4,
                    dependencies: ['core'],
                    files: [
                        '/js/site.js',
                        '/js/theme-listener.js'
                    ]
                }
            ]
        };
    }

    /**
     * Initialize performance monitoring
     */
    initializePerformanceMonitoring() {
        if ('PerformanceObserver' in window) {
            this.performanceObserver = new PerformanceObserver((list) => {
                for (const entry of list.getEntries()) {
                    if (entry.entryType === 'resource') {
                        console.log(`Resource loaded: ${entry.name} (${entry.duration.toFixed(2)}ms)`);
                    }
                }
            });

            this.performanceObserver.observe({ entryTypes: ['resource'] });
        }
    }

    /**
     * Get performance metrics for loaded bundles
     */
    getPerformanceMetrics() {
        if (!window.performance) return null;

        const resources = performance.getEntriesByType('resource');
        const bundleMetrics = {
            totalResources: resources.length,
            totalLoadTime: 0,
            cssFiles: [],
            jsFiles: []
        };

        resources.forEach(resource => {
            if (resource.name.includes('.css')) {
                bundleMetrics.cssFiles.push({
                    name: resource.name,
                    loadTime: resource.duration,
                    size: resource.transferSize || 0
                });
            } else if (resource.name.includes('.js')) {
                bundleMetrics.jsFiles.push({
                    name: resource.name,
                    loadTime: resource.duration,
                    size: resource.transferSize || 0
                });
            }
            bundleMetrics.totalLoadTime += resource.duration;
        });

        return bundleMetrics;
    }
}

// Global bundle loader instance
window.bundleLoader = new BundleLoader();

// Auto-initialize when DOM is ready
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => {
        window.bundleLoader.initialize();
    });
} else {
    window.bundleLoader.initialize();
}

// Utility functions for easy bundle loading
window.loadBundle = (bundleName) => window.bundleLoader.loadBundle(bundleName);
window.loadPageBundles = (pageName) => window.bundleLoader.loadPageBundles(pageName);
window.preloadBundle = (bundleName) => window.bundleLoader.preloadBundle(bundleName);

// Export for module systems
if (typeof module !== 'undefined' && module.exports) {
    module.exports = BundleLoader;
}