@using Microsoft.Extensions.Localization
@inject IStringLocalizer<ParaHockeyApp.Resources.SharedResourceMarker> SharedLocalizer
@{
    ViewData["Title"] = SharedLocalizer["PageNotFoundTitle"];
    var requestPath = Context.Request.Path.ToString();
}

<div class="container mt-5">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card border-warning">
                <div class="card-header bg-warning text-dark">
                    <h1 class="h3 mb-0">
                        <i class="fas fa-search"></i>
                        @SharedLocalizer["PageNotFoundTitle"]
                    </h1>
                </div>
                <div class="card-body text-center">
                    <div class="mb-4">
                        <i class="fas fa-search fa-5x text-warning mb-3"></i>
                        <h2 class="h4">@SharedLocalizer["PageNotFoundHeading"]</h2>
                        <p class="text-muted">@SharedLocalizer["PageNotFoundMessage"]</p>
                        
                        @if (!string.IsNullOrEmpty(requestPath) && requestPath != "/")
                        {
                            <div class="alert alert-light mt-3">
                                <small>
                                    <strong>@SharedLocalizer["RequestedUrl"]:</strong> 
                                    <code>@requestPath</code>
                                </small>
                            </div>
                        }
                    </div>

                    <div class="mt-4">
                        <a href="@Url.Action("Index", "Home")" class="btn btn-primary me-2">
                            <i class="fas fa-home"></i> @SharedLocalizer["ReturnToHome"]
                        </a>
                        <a href="javascript:history.back()" class="btn btn-secondary me-2">
                            <i class="fas fa-arrow-left"></i> @SharedLocalizer["GoBack"]
                        </a>
                    </div>

                    <div class="mt-4">
                        <h5>@SharedLocalizer["SuggestedLinks"]</h5>
                        <div class="row">
                            <div class="col-md-6">
                                <ul class="list-unstyled">
                                    <li><a href="@Url.Action("Index", "Home")" class="text-decoration-none">
                                        <i class="fas fa-home"></i> @SharedLocalizer["HomePage"]
                                    </a></li>
                                    <li><a href="@Url.Action("PublicCalendar", "Home")" class="text-decoration-none">
                                        <i class="fas fa-calendar"></i> @SharedLocalizer["Calendar"]
                                    </a></li>
                                </ul>
                            </div>
                            <div class="col-md-6">
                                <ul class="list-unstyled">
                                    <li><a href="@Url.Action("Register", "Members")" class="text-decoration-none">
                                        <i class="fas fa-user-plus"></i> @SharedLocalizer["MemberRegistration"]
                                    </a></li>
                                    <li><a href="@Url.Action("Login", "Members")" class="text-decoration-none">
                                        <i class="fas fa-sign-in-alt"></i> @SharedLocalizer["MemberLogin"]
                                    </a></li>
                                </ul>
                            </div>
                        </div>
                    </div>

                    <div class="mt-4 pt-3 border-top">
                        <p class="text-muted small mb-0">
                            @SharedLocalizer["NeedHelp"] 
                            <a href="mailto:<EMAIL>" class="text-decoration-none"><EMAIL></a>
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script>
        // Track 404 errors for analytics (if available)
        if (typeof gtag !== 'undefined') {
            gtag('event', 'exception', {
                'description': 'Page Not Found: @requestPath',
                'fatal': false
            });
        }
        
        // Optional: Auto-redirect to home after 30 seconds
        // setTimeout(() => {
        //     if (confirm('@SharedLocalizer["AutoRedirectConfirm"]')) {
        //         window.location.href = '@Url.Action("Index", "Home")';
        //     }
        // }, 30000);
    </script>
}