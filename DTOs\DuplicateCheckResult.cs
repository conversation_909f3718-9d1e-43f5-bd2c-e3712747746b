using ParaHockeyApp.Models.Entities;

namespace ParaHockeyApp.DTOs
{
    /// <summary>
    /// Result object returned by duplicate member detection service
    /// </summary>
    public class DuplicateCheckResult
    {
        /// <summary>
        /// Type of duplicate found (if any)
        /// </summary>
        public DuplicateType Type { get; set; } = DuplicateType.NoDuplicate;
        
        /// <summary>
        /// The existing member that was found as a duplicate (if any)
        /// </summary>
        public Member? ExistingMember { get; set; }
        
        /// <summary>
        /// Masked email address for partial matches (e.g., "jo***@example.com")
        /// </summary>
        public string? MaskedEmail { get; set; }
    }
}