using Microsoft.Extensions.Localization;
using ParaHockeyApp.Models.ViewModels;
using ParaHockeyApp.Resources;
using System;

namespace ParaHockeyApp.Services
{
    /// <summary>
    /// Service for managing empty state messages across the application.
    /// Provides localized empty state messages for different entity types and search scenarios.
    /// </summary>
    public class EmptyStateService : IEmptyStateService
    {
        private readonly IStringLocalizer<SharedResourceMarker> _localizer;
        private readonly IServiceProvider _serviceProvider;

        public EmptyStateService(
            IStringLocalizer<SharedResourceMarker> localizer,
            IServiceProvider serviceProvider)
        {
            _localizer = localizer;
            _serviceProvider = serviceProvider;
        }

        /// <summary>
        /// Gets a simple empty state message for the specified entity type.
        /// </summary>
        public string GetEmptyMessage(string entityType, string searchTerm = null)
        {
            if (!string.IsNullOrEmpty(searchTerm))
            {
                return GetSearchMessage(entityType, searchTerm);
            }

            return entityType.ToLower() switch
            {
                "members" => _localizer["EmptyState_NoMembers"],
                "events" => _localizer["EmptyState_NoEvents"],
                "admin" => _localizer["EmptyState_NoAdminData"],
                "logs" => _localizer["EmptyState_NoLogs"],
                "exports" => _localizer["EmptyState_NoExports"],
                "searches" => _localizer["EmptyState_NoSavedSearches"],
                _ => _localizer["EmptyState_NoData"]
            };
        }

        /// <summary>
        /// Gets a complete empty state view model with title, message, icon, and optional action.
        /// </summary>
        public EmptyStateViewModel GetEmptyState(string entityType, string searchTerm = null, string actionUrl = null, string actionText = null)
        {
            var isSearch = !string.IsNullOrEmpty(searchTerm);
            
            return new EmptyStateViewModel
            {
                Title = GetEmptyTitle(entityType, isSearch),
                Message = GetEmptyMessage(entityType, searchTerm),
                IconClass = GetIconClass(entityType, isSearch),
                ActionUrl = actionUrl,
                ActionText = actionText ?? GetDefaultActionText(entityType),
                ShowAction = !string.IsNullOrEmpty(actionUrl),
                CssClass = GetEmptyStateCssClass(entityType, isSearch)
            };
        }

        /// <summary>
        /// Gets an empty state for search-specific scenarios with helpful suggestions.
        /// </summary>
        public EmptyStateViewModel GetSearchEmptyState(string entityType, string searchTerm, string[] suggestions = null)
        {
            var message = GetSearchMessage(entityType, searchTerm);
            
            if (suggestions != null && suggestions.Length > 0)
            {
                var suggestionText = _localizer["EmptyState_SearchSuggestions", string.Join(", ", suggestions)];
                message += " " + suggestionText;
            }

            return new EmptyStateViewModel
            {
                Title = _localizer["EmptyState_SearchNoResults"],
                Message = message,
                IconClass = "fas fa-search text-muted",
                ActionUrl = null,
                ActionText = null,
                ShowAction = false,
                Subtitle = !string.IsNullOrEmpty(searchTerm) ? _localizer["EmptyState_SearchTryDifferent"] : null,
                CssClass = "search-empty-state"
            };
        }

        /// <summary>
        /// Gets an empty state with actionable suggestions based on the context.
        /// </summary>
        public EmptyStateViewModel GetActionableEmptyState(string entityType, string context = null, string actionUrl = null)
        {
            var title = GetEmptyTitle(entityType, false);
            var message = GetEmptyMessage(entityType);
            var actionText = GetDefaultActionText(entityType);
            var iconClass = GetIconClass(entityType, false);
            
            // Add context-specific suggestions
            var suggestions = GetContextSuggestions(entityType, context);
            
            return new EmptyStateViewModel
            {
                Title = title,
                Message = message,
                IconClass = iconClass,
                ActionUrl = actionUrl,
                ActionText = actionText,
                ShowAction = !string.IsNullOrEmpty(actionUrl),
                Subtitle = suggestions,
                CssClass = $"actionable-empty-state {entityType.ToLower()}-empty-state"
            };
        }

        /// <summary>
        /// Gets an empty state for error scenarios with appropriate messaging.
        /// </summary>
        public EmptyStateViewModel GetErrorEmptyState(string entityType, string errorType, bool isRetryable = false)
        {
            var title = _localizer["EmptyState_ErrorTitle"];
            var message = GetErrorMessage(entityType, errorType);
            var iconClass = GetErrorIconClass(errorType);
            
            return new EmptyStateViewModel
            {
                Title = title,
                Message = message,
                IconClass = iconClass,
                ActionUrl = isRetryable ? "#" : null,
                ActionText = isRetryable ? _localizer["EmptyState_RetryAction"] : null,
                ShowAction = isRetryable,
                CssClass = $"error-empty-state {errorType.ToLower()}-error"
            };
        }

        /// <summary>
        /// Gets the appropriate title for empty states.
        /// </summary>
        private string GetEmptyTitle(string entityType, bool isSearch)
        {
            if (isSearch)
            {
                return _localizer["EmptyState_SearchNoResults"];
            }

            return entityType.ToLower() switch
            {
                "members" => _localizer["EmptyState_NoMembersTitle"],
                "events" => _localizer["EmptyState_NoEventsTitle"],
                "admin" => _localizer["EmptyState_NoAdminDataTitle"],
                "logs" => _localizer["EmptyState_NoLogsTitle"],
                "exports" => _localizer["EmptyState_NoExportsTitle"],
                "searches" => _localizer["EmptyState_NoSavedSearchesTitle"],
                _ => _localizer["EmptyState_NoDataTitle"]
            };
        }

        /// <summary>
        /// Gets the appropriate icon class for empty states.
        /// </summary>
        private string GetIconClass(string entityType, bool isSearch)
        {
            if (isSearch)
            {
                return "fas fa-search text-muted";
            }

            return entityType.ToLower() switch
            {
                "members" => "fas fa-users text-muted",
                "events" => "fas fa-calendar text-muted",
                "admin" => "fas fa-cog text-muted",
                "logs" => "fas fa-list text-muted",
                "exports" => "fas fa-download text-muted",
                "searches" => "fas fa-bookmark text-muted",
                _ => "fas fa-inbox text-muted"
            };
        }

        /// <summary>
        /// Gets search-specific messages.
        /// </summary>
        private string GetSearchMessage(string entityType, string searchTerm)
        {
            return entityType.ToLower() switch
            {
                "members" => _localizer["EmptyState_SearchNoMembers", searchTerm],
                "events" => _localizer["EmptyState_SearchNoEvents", searchTerm],
                "admin" => _localizer["EmptyState_SearchNoAdminData", searchTerm],
                "logs" => _localizer["EmptyState_SearchNoLogs", searchTerm],
                "exports" => _localizer["EmptyState_SearchNoExports", searchTerm],
                "searches" => _localizer["EmptyState_SearchNoSavedSearches", searchTerm],
                _ => _localizer["EmptyState_SearchNoData", searchTerm]
            };
        }

        /// <summary>
        /// Gets default action text for different entity types.
        /// </summary>
        private string GetDefaultActionText(string entityType)
        {
            return entityType.ToLower() switch
            {
                "members" => _localizer["EmptyState_AddFirstMember"],
                "events" => _localizer["EmptyState_CreateFirstEvent"],
                "admin" => _localizer["EmptyState_ConfigureSystem"],
                "logs" => _localizer["EmptyState_ViewAllLogs"],
                "exports" => _localizer["EmptyState_CreateExport"],
                "searches" => _localizer["EmptyState_CreateSearch"],
                _ => _localizer["EmptyState_GetStarted"]
            };
        }
        
        /// <summary>
        /// Gets context-specific suggestions for empty states.
        /// </summary>
        private string GetContextSuggestions(string entityType, string context)
        {
            if (string.IsNullOrEmpty(context))
            {
                return null;
            }
            
            var key = $"EmptyState_{entityType}_{context}Suggestion";
            return _localizer[key];
        }
        
        /// <summary>
        /// Gets error-specific messages.
        /// </summary>
        private string GetErrorMessage(string entityType, string errorType)
        {
            var entityKey = entityType.ToLower() switch
            {
                "members" => "Members",
                "events" => "Events",
                "admin" => "AdminData",
                "logs" => "Logs",
                "exports" => "Exports",
                "searches" => "SavedSearches",
                _ => "Data"
            };
            
            var errorKey = errorType.ToLower() switch
            {
                "database" => "Database",
                "network" => "Network",
                "permission" => "Permission",
                _ => "General"
            };
            
            return _localizer[$"EmptyState_Error{errorKey}{entityKey}"];
        }
        
        /// <summary>
        /// Gets error-specific icon classes.
        /// </summary>
        private string GetErrorIconClass(string errorType)
        {
            return errorType.ToLower() switch
            {
                "database" => "fas fa-database text-danger",
                "network" => "fas fa-wifi text-danger",
                "permission" => "fas fa-lock text-danger",
                _ => "fas fa-exclamation-triangle text-danger"
            };
        }
        
        /// <summary>
        /// Gets CSS classes for empty state styling.
        /// </summary>
        private string GetEmptyStateCssClass(string entityType, bool isSearch)
        {
            var baseClass = "empty-state";
            var entityClass = $"{entityType.ToLower()}-empty-state";
            var typeClass = isSearch ? "search-empty-state" : "standard-empty-state";
            
            return $"{baseClass} {entityClass} {typeClass}";
        }
    }
}