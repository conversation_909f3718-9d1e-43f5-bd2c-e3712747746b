using System.ComponentModel.DataAnnotations;

namespace ParaHockeyApp.Models.Entities
{
    public enum AdminType
    {
        Disabled = 0,
        Normal = 3,
        Master = 9
    }

    public class AdminUser : BaseEntity
    {
        [Required]
        [StringLength(255)]
        public string Email { get; set; } = string.Empty;
        
        [Required]
        [StringLength(100)]
        public string Name { get; set; } = string.Empty;
        
        public AdminType AdminType { get; set; } = AdminType.Normal;
        
        // Foreign key to AdminTypes lookup table
        [Required]
        public int AdminTypeId { get; set; }
        public virtual AdminTypeEntity AdminTypeEntity { get; set; } = null!;
        
        public bool IsDisabled => AdminType == AdminType.Disabled;
        public bool IsMasterAdmin => AdminType == AdminType.Master;
        public bool IsNormalAdmin => AdminType == AdminType.Normal;
        
        public bool CanLogin => (int)AdminType > 0 && IsActive;
        public string AdminTypeDescription => AdminType switch
        {
            AdminType.Disabled => "Disabled",
            AdminType.Normal => "Normal Admin",
            AdminType.Master => "Master Admin",
            _ => "Unknown"
        };
    }
}