namespace ParaHockeyApp.Models.ViewModels
{
    /// <summary>
    /// View model for displaying empty state messages across the application.
    /// Provides consistent empty state handling with localized messages, icons, and optional actions.
    /// </summary>
    public class EmptyStateViewModel
    {
        /// <summary>
        /// The title to display for the empty state (e.g., "No Members Found").
        /// </summary>
        public string Title { get; set; } = string.Empty;

        /// <summary>
        /// The main message to display explaining the empty state.
        /// </summary>
        public string Message { get; set; } = string.Empty;

        /// <summary>
        /// CSS class for the icon to display (e.g., "fas fa-users text-muted").
        /// </summary>
        public string IconClass { get; set; } = "fas fa-inbox text-muted";

        /// <summary>
        /// Text for the action button (e.g., "Add First Member").
        /// </summary>
        public string? ActionText { get; set; }

        /// <summary>
        /// URL for the action button to navigate to.
        /// </summary>
        public string? ActionUrl { get; set; }

        /// <summary>
        /// Whether to show the action button.
        /// </summary>
        public bool ShowAction { get; set; } = false;

        /// <summary>
        /// Additional CSS classes to apply to the empty state container.
        /// </summary>
        public string? CssClass { get; set; }

        /// <summary>
        /// Optional subtitle for additional context.
        /// </summary>
        public string? Subtitle { get; set; }
    }
}