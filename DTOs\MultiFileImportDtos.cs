namespace ParaHockeyApp.DTOs
{
    /// <summary>
    /// Represents parent data from the parent contact CSV file
    /// </summary>
    public class ImportParentData
    {
        public string? FirstName { get; set; }
        public string? LastName { get; set; }
        public string? Email { get; set; }
        public string? Phone { get; set; }
        public string? ParentType { get; set; } // "M<PERSON>", "P<PERSON>", etc.
    }

    /// <summary>
    /// Represents emergency contact data from the emergency contact CSV file
    /// </summary>
    public class ImportEmergencyContactData
    {
        public string? FirstName { get; set; }
        public string? LastName { get; set; }
        public string? Email { get; set; }
        public string? Phone { get; set; }
        public string? Relationship { get; set; } // "Conjoint(e)", etc.
    }

    /// <summary>
    /// Container for all related data for a member
    /// </summary>
    public class ImportMemberWithRelationships
    {
        public string? HcrNumber { get; set; }
        public Dictionary<string, string> MemberData { get; set; } = new();
        public List<ImportParentData> Parents { get; set; } = new();
        public List<ImportEmergencyContactData> EmergencyContacts { get; set; } = new();
    }

    /// <summary>
    /// Enum for different file types during multi-file import
    /// </summary>
    public enum ImportFileType
    {
        Member = 1,
        Parent = 2,
        EmergencyContact = 3
    }

    /// <summary>
    /// Upload request for multi-file import
    /// </summary>
    public class MultiFileImportRequest
    {
        public IFormFile? MemberFile { get; set; }
        public IFormFile? ParentFile { get; set; }
        public IFormFile? EmergencyContactFile { get; set; }
        public string? Description { get; set; }
    }

    /// <summary>
    /// Result of relationship matching process
    /// </summary>
    public class RelationshipMatchingResult
    {
        public int TotalMemberRecords { get; set; }
        public int MembersWithParents { get; set; }
        public int MembersWithEmergencyContacts { get; set; }
        public int UnmatchedParentRecords { get; set; }
        public int UnmatchedEmergencyContactRecords { get; set; }
        public List<string> UnmatchedHcrNumbers { get; set; } = new();
        public bool HasUnmatchedRecords => UnmatchedParentRecords > 0 || UnmatchedEmergencyContactRecords > 0;
    }
}