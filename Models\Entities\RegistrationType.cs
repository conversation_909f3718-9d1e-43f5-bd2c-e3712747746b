using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace ParaHockeyApp.Models.Entities
{
    [Table("RegistrationTypes")]
    public class RegistrationType
    {
        [Key]
        public int Id { get; set; }

        [Required]
        [StringLength(100)]
        public string DisplayNameKey { get; set; } = string.Empty;

        [Required]
        [StringLength(250)]
        public string DescriptionKey { get; set; } = string.Empty;
    }
}