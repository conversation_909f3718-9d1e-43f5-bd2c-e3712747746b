@model ParaHockey.Models.ViewModels.EventSubscribeViewModel
@using Microsoft.AspNetCore.Mvc.Localization
@inject IHtmlLocalizer<ParaHockeyApp.Resources.SharedResourceMarker> SharedLocalizer
@{
    ViewData["Title"] = SharedLocalizer["SubscribeToEvents"];
}

<!-- CSRF Token for AJAX requests -->
@Html.AntiForgeryToken()

<!-- Skip navigation links for accessibility -->
<a href="#main-content" class="skip-link visually-hidden-focusable">@SharedLocalizer["SkipToMainContent"]</a>
<a href="#upcoming-events" class="skip-link visually-hidden-focusable">@SharedLocalizer["SkipToUpcomingEvents"]</a>
<a href="#event-calendar" class="skip-link visually-hidden-focusable">@SharedLocalizer["SkipToEventCalendar"]</a>

<main id="main-content" role="main" aria-label="@SharedLocalizer["EventSubscriptionPage"]">
    <!-- ARIA live regions for dynamic feedback -->
    <div id="page-status" aria-live="polite" aria-atomic="true" class="visually-hidden"></div>
    <div id="page-errors" aria-live="assertive" aria-atomic="true" class="visually-hidden"></div>
    
    <div class="container-fluid mt-4">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col">
            <h1 class="h2 text-primary">
                <i class="fas fa-calendar-check"></i> @SharedLocalizer["SubscribeToEvents"]
            </h1>
            <p class="text-muted">@SharedLocalizer["BrowseAndSubscribeToUpcomingEvents"]</p>
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="@Url.Action("Index", "Home")">@SharedLocalizer["Home"]</a></li>
                    <li class="breadcrumb-item active" aria-current="page">@SharedLocalizer["SubscribeToEvents"]</li>
                </ol>
            </nav>
        </div>
    </div>

    <!-- Alerts -->
    @if (TempData["SuccessMessage"] != null)
    {
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="fas fa-check-circle"></i> @TempData["SuccessMessage"]
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    }
    @if (TempData["ErrorMessage"] != null)
    {
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="fas fa-exclamation-circle"></i> @TempData["ErrorMessage"]
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    }

    <div class="row">
        <!-- Upcoming Events List -->
        <div class="col-lg-7 col-md-12 mb-4" id="upcoming-events">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-clock"></i> @SharedLocalizer["UpcomingEvents"]
                    </h5>
                </div>
                <div class="card-body">
                    @if (Model.UpcomingEvents != null && Model.UpcomingEvents.Any())
                    {
                        <div class="list-group">
                            @foreach (var evt in Model.UpcomingEvents)
                            {
                                <div class="list-group-item event-list-item" 
                                     data-event-id="@evt.Id" 
                                     role="button" 
                                     tabindex="0" 
                                     style="cursor: pointer;"
                                     aria-label="@SharedLocalizer["ViewEventDetails"]: @evt.Title"
                                     aria-describedby="<EMAIL>-status">
                                    <div class="d-flex w-100 justify-content-between">
                                        <div class="flex-grow-1">
                                            <h5 class="mb-2">
                                                @evt.Title
                                                @if (evt.CategoryColor != null)
                                                {
                                                    <span class="badge" style="background-color: @evt.CategoryColor;">@evt.CategoryName</span>
                                                }
                                            </h5>
                                            @if (!string.IsNullOrEmpty(evt.Description))
                                            {
                                                <p class="mb-2">@evt.Description</p>
                                            }
                                            <div class="small text-muted">
                                                <i class="fas fa-calendar-day"></i> @evt.StartDate.ToString("MMM dd, yyyy")
                                                @if (!evt.IsAllDay)
                                                {
                                                    <span class="ms-2"><i class="fas fa-clock"></i> @evt.StartDate.ToString("h:mm tt") - @evt.EndDate.ToString("h:mm tt")</span>
                                                }
                                                @if (!string.IsNullOrEmpty(evt.Location))
                                                {
                                                    <br><i class="fas fa-map-marker-alt"></i> @evt.Location
                                                }
                                                @if (evt.RequiresRegistration)
                                                {
                                                    <br>
                                                    @if (evt.MaxParticipants > 0)
                                                    {
                                                        <span class="badge bg-info">
                                                            <i class="fas fa-users"></i> @evt.AvailableSpots / @evt.MaxParticipants @SharedLocalizer["SpotsAvailable"]
                                                        </span>
                                                    }
                                                    @if (evt.RegistrationDeadline.HasValue)
                                                    {
                                                        <span class="badge bg-warning text-dark ms-1">
                                                            <i class="fas fa-hourglass-half"></i> @SharedLocalizer["RegistrationDeadline"]: @evt.RegistrationDeadline.Value.ToString("MMM dd")
                                                        </span>
                                                    }
                                                }
                                            </div>
                                            @if (!string.IsNullOrEmpty(evt.ContactPerson) || !string.IsNullOrEmpty(evt.ContactEmail))
                                            {
                                                <div class="small mt-2">
                                                    <strong>@SharedLocalizer["Contact"]:</strong>
                                                    @if (!string.IsNullOrEmpty(evt.ContactPerson))
                                                    {
                                                        <span>@evt.ContactPerson</span>
                                                    }
                                                    @if (!string.IsNullOrEmpty(evt.ContactEmail))
                                                    {
                                                        <a href="mailto:@evt.ContactEmail" class="ms-2">@evt.ContactEmail</a>
                                                    }
                                                    @if (!string.IsNullOrEmpty(evt.ContactPhone))
                                                    {
                                                        <span class="ms-2">@evt.ContactPhone</span>
                                                    }
                                                </div>
                                            }
                                        </div>
                                        <div class="ms-3 text-end d-flex flex-column align-items-end event-status-container" 
                                             data-event-id="@evt.Id"
                                             id="<EMAIL>-status"
                                             role="status"
                                             aria-live="polite">
                                            @if (evt.RequiresRegistration && evt.IsRegistrationOpen)
                                            {
                                                @if (evt.IsUserRegistered)
                                                {
                                                    <span class="badge bg-success" id="<EMAIL>">
                                                        <i class="fas fa-check-circle"></i> @SharedLocalizer["Registered"]
                                                    </span>
                                                    <small class="text-success mt-1">
                                                        @SharedLocalizer["YouAreRegistered"]
                                                    </small>
                                                }
                                                else if (evt.IsFull)
                                                {
                                                    <span class="badge bg-danger" id="<EMAIL>">
                                                        <i class="fas fa-times-circle"></i> @SharedLocalizer["Full"]
                                                    </span>
                                                    <small class="text-muted mt-1">
                                                        @SharedLocalizer["EventFull"]
                                                    </small>
                                                }
                                                else
                                                {
                                                    <span class="badge bg-primary" id="<EMAIL>">
                                                        <i class="fas fa-calendar-plus"></i> @SharedLocalizer["Available"]
                                                    </span>
                                                    <small class="text-info mt-1">
                                                        @SharedLocalizer["ClickToRegister"]
                                                    </small>
                                                }
                                            }
                                            else if (!evt.RequiresRegistration)
                                            {
                                                <span class="badge bg-secondary" id="<EMAIL>">
                                                    <i class="fas fa-calendar"></i> @SharedLocalizer["NoRegistrationRequired"]
                                                </span>
                                            }
                                            else
                                            {
                                                <span class="badge bg-warning text-dark" id="<EMAIL>">
                                                    <i class="fas fa-lock"></i> @SharedLocalizer["RegistrationClosed"]
                                                </span>
                                            }
                                        </div>
                                    </div>
                                </div>
                            }
                        </div>
                    }
                    else
                    {
                        <div class="text-center py-5">
                            <i class="fas fa-calendar-times fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">@SharedLocalizer["NoUpcomingEvents"]</h5>
                            <p class="text-muted">@SharedLocalizer["CheckBackLaterForNewEvents"]</p>
                        </div>
                    }
                </div>
            </div>
        </div>

        <!-- Mini Calendar -->
        <div class="col-lg-5 col-md-12" id="event-calendar">
            <div class="card">
                <div class="card-header bg-info text-white">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-calendar-alt"></i> @SharedLocalizer["EventCalendar"]
                    </h5>
                    @if (Model.CalendarOptions.ShowFilters && Model.CalendarOptions.Categories.Any())
                    {
                        <div class="dropdown mt-2">
                            <button class="btn btn-sm btn-light dropdown-toggle" type="button" id="calendarFilterDropdown" data-bs-toggle="dropdown">
                                <i class="fas fa-filter"></i> @SharedLocalizer["Filter"]
                            </button>
                            <ul class="dropdown-menu" aria-labelledby="calendarFilterDropdown">
                                <li><a class="dropdown-item calendar-filter" href="#" data-filter="all">@SharedLocalizer["AllEvents"]</a></li>
                                @foreach (var category in Model.CalendarOptions.Categories)
                                {
                                    <li><a class="dropdown-item calendar-filter" href="#" data-filter="@category.Id">@category.Name</a></li>
                                }
                            </ul>
                        </div>
                    }
                </div>
                <div class="card-body p-0">
                    <div id="miniCalendar" class="calendar-mini"></div>
                </div>
            </div>
        </div>
    </div>
</main>

<!-- Include shared Event Details Modal -->
@await Html.PartialAsync("_EventDetailsModal")

<!-- FullCalendar CSS -->
<link href="https://cdn.jsdelivr.net/npm/fullcalendar@6.1.8/index.global.min.css" rel="stylesheet" />

<!-- Enhanced Event List Styles -->
<style>
    .event-list-item {
        transition: all 0.2s ease !important;
        cursor: pointer !important;
        border-radius: 8px !important;
        margin-bottom: 8px !important;
    }
    
    .event-list-item:hover {
        background-color: #f8f9fa !important;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1) !important;
        transform: translateY(-1px) !important;
    }
    
    .event-list-item:focus {
        outline: 3px solid #0d6efd !important;
        outline-offset: 2px !important;
        background-color: #e7f3ff !important;
        box-shadow: 0 0 0 2px rgba(13, 110, 253, 0.25) !important;
    }
    
    .event-list-item:focus-visible {
        outline: 3px solid #0d6efd !important;
        outline-offset: 2px !important;
        background-color: #e7f3ff !important;
    }
    
    .event-list-item:active {
        transform: translateY(0) !important;
        box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1) !important;
    }
    
    /* Ensure buttons and links inside event items don't interfere with clicking */
    .event-list-item a:not(.btn) {
        position: relative;
        z-index: 2;
    }
    
    .event-list-item .btn {
        position: relative;
        z-index: 3;
    }
    
    .event-status-container {
        position: relative;
        z-index: 3;
    }
</style>

@section Scripts {
    <!-- FullCalendar JS -->
    <script src="https://cdn.jsdelivr.net/npm/fullcalendar@6.1.8/index.global.min.js"></script>
    
    <!-- Event Details Modal JS -->
    <script src="~/js/event-details-modal.js"></script>
    
    <!-- Debug calendar script -->
    <script src="~/js/calendar-debug.js"></script>

    <script>
        // Global authentication status - check both ASP.NET Identity and member session
        const isAuthenticated = @Json.Serialize(User.Identity.IsAuthenticated || ViewBag.HasMemberSession == true);
        
        // Make isAuthenticated globally available for other scripts
        window.isAuthenticated = isAuthenticated;
        
        // DEBUG: Log server-side authentication state
        console.log('DEBUG: Server-side User.Identity.IsAuthenticated:', isAuthenticated);
        console.log('DEBUG: Server-side User.Identity.Name:', '@(User.Identity.Name ?? "NULL")');
        console.log('DEBUG: Server-side User.Identity.AuthenticationType:', '@(User.Identity.AuthenticationType ?? "NULL")');
        
        document.addEventListener('DOMContentLoaded', function() {
            console.log('Subscribe page: DOM loaded, initializing calendar...');
            console.log('DEBUG: FullCalendar available?', typeof FullCalendar);
            console.log('DEBUG: showEventDetailsModal available?', typeof showEventDetailsModal);
            console.log('DEBUG: isAuthenticated value:', isAuthenticated);
            
            // Initialize FullCalendar
            var calendarEl = document.getElementById('miniCalendar');
            if (!calendarEl) {
                console.error('Subscribe page: Calendar element not found');
                return;
            }
            
            console.log('Subscribe page: Calendar element found, creating calendar...');
            
            try {
                var calendar = new FullCalendar.Calendar(calendarEl, {
                    initialView: 'dayGridMonth',
                    headerToolbar: {
                        left: 'prev,next',
                        center: 'title',
                        right: 'today'
                    },
                    buttonText: {
                        today: '@System.Globalization.CultureInfo.CurrentCulture.TwoLetterISOLanguageName' === 'fr' ? 'Aujourd\'hui' : 'Today'
                    },
                    events: {
                        url: '@Url.Action("GetSubscribableCalendarEvents", "Home")',
                        method: 'GET',
                        failure: function() {
                            console.error('Subscribe page: Failed to load events');
                        }
                    },
                    eventClick: function(info) {
                        console.log('Subscribe page: Event clicked:', info.event.id);
                        info.jsEvent.preventDefault();
                        
                        if (typeof showEventDetailsModal === 'function') {
                            showEventDetailsModal(info.event.id, isAuthenticated, {
                                calendarType: 'subscribe',
                                detailsUrl: '@Url.Action("GetPublicEventDetails", "Home")',
                                joinUrl: '@Url.Action("Join", "Events")',
                                leaveUrl: '@Url.Action("Leave", "Events")'
                            });
                        } else {
                            console.error('Subscribe page: showEventDetailsModal function not available');
                        }
                    },
                    height: 400,
                    contentHeight: 350,
                    aspectRatio: 1.1,
                    eventDisplay: 'block',
                    displayEventTime: false,
                    dayMaxEvents: 3,
                    moreLinkClick: 'popover',
                    locale: '@(System.Globalization.CultureInfo.CurrentCulture.TwoLetterISOLanguageName)'
                });

                console.log('Subscribe page: Calendar created, rendering...');
                calendar.render();
                console.log('Subscribe page: Calendar rendered successfully');
            } catch (error) {
                console.error('Subscribe page: Error creating calendar:', error);
            }

            // Filter functionality
            document.querySelectorAll('.calendar-filter').forEach(function(filterBtn) {
                filterBtn.addEventListener('click', function(e) {
                    e.preventDefault();
                    var filter = this.dataset.filter;
                    
                    // Remove existing filter sources
                    calendar.getEventSources().forEach(function(eventSource) {
                        eventSource.remove();
                    });
                    
                    // Add new event source with filter
                    var url = '@Url.Action("GetSubscribableCalendarEvents", "Home")';
                    if (filter !== 'all') {
                        url += '?categoryId=' + filter;
                    }
                    
                    calendar.addEventSource({
                        url: url,
                        method: 'GET',
                        failure: function() {
                            console.error('Failed to load events');
                        }
                    });
                });
            });
            
            // Add click handlers for event list items - ENHANCED for better clickability
            console.log('Subscribe page: Setting up event list click handlers...');
            document.querySelectorAll('.event-list-item').forEach(function(item) {
                console.log('Subscribe page: Adding click handler to event item:', item.dataset.eventId);
                
                // Add hover effect for better UX
                item.style.transition = 'background-color 0.2s ease';
                
                item.addEventListener('mouseenter', function() {
                    this.style.backgroundColor = '#f8f9fa';
                });
                
                item.addEventListener('mouseleave', function() {
                    this.style.backgroundColor = '';
                });
                
                item.addEventListener('click', function(e) {
                    console.log('Subscribe page: Event list item clicked:', this.dataset.eventId);
                    console.log('Subscribe page: Click target:', e.target.tagName, e.target.className);
                    
                    // Don't trigger if clicking on interactive elements (buttons, links)
                    if (e.target.tagName === 'BUTTON' || 
                        e.target.closest('button') || 
                        e.target.tagName === 'A' || 
                        e.target.closest('a') ||
                        e.target.closest('.event-status-container')) {
                        console.log('Subscribe page: Click ignored - clicked on interactive element');
                        return;
                    }
                    
                    // Prevent bubbling to avoid double handlers
                    e.preventDefault();
                    e.stopPropagation();
                    
                    var eventId = this.dataset.eventId;
                    if (eventId) {
                        console.log('Subscribe page: Opening modal for event:', eventId);
                        if (typeof showEventDetailsModal === 'function') {
                            showEventDetailsModal(eventId, isAuthenticated, {
                                calendarType: 'subscribe',
                                detailsUrl: '@Url.Action("GetPublicEventDetails", "Home")',
                                joinUrl: '@Url.Action("Join", "Events")',
                                leaveUrl: '@Url.Action("Leave", "Events")'
                            });
                        } else {
                            console.error('Subscribe page: showEventDetailsModal function not available');
                        }
                    }
                });
                
                // Handle keyboard navigation
                item.addEventListener('keypress', function(e) {
                    if (e.key === 'Enter' || e.key === ' ') {
                        console.log('Subscribe page: Keyboard event triggered for:', this.dataset.eventId);
                        e.preventDefault();
                        e.stopPropagation();
                        
                        var eventId = this.dataset.eventId;
                        if (eventId && typeof showEventDetailsModal === 'function') {
                            showEventDetailsModal(eventId, isAuthenticated, {
                                calendarType: 'subscribe',
                                detailsUrl: '@Url.Action("GetPublicEventDetails", "Home")',
                                joinUrl: '@Url.Action("Join", "Events")',
                                leaveUrl: '@Url.Action("Leave", "Events")'
                            });
                        }
                    }
                });
            });
            
            // Quick registration functionality (T4.1, T4.2, T4.3, T4.4)
            // isAuthenticated already declared globally
            
            // Update status badge after successful registration/unregistration
            function updateEventStatusBadge(eventId, isRegistered, showFeedback = true) {
                const statusBadge = document.getElementById(`status-badge-${eventId}`);
                const container = document.querySelector(`[data-event-id="${eventId}"] .event-status-container`);
                const statusText = container?.querySelector('small');
                
                if (!statusBadge || !container) return;
                
                if (isRegistered) {
                    // Update to registered state - USE LOCALIZED STRINGS
                    statusBadge.innerHTML = '<i class="fas fa-check-circle"></i> @SharedLocalizer["Registered"]';
                    statusBadge.className = 'badge bg-success';
                    
                    if (statusText) {
                        statusText.innerHTML = '@SharedLocalizer["YouAreRegistered"]';
                        statusText.className = 'text-success mt-1';
                    }
                } else {
                    // Update to available state - USE LOCALIZED STRINGS
                    statusBadge.innerHTML = '<i class="fas fa-calendar-plus"></i> @SharedLocalizer["Available"]';
                    statusBadge.className = 'badge bg-primary';
                    
                    if (statusText) {
                        statusText.innerHTML = '@SharedLocalizer["ClickToRegister"]';
                        statusText.className = 'text-info mt-1';
                    }
                }
                
                if (showFeedback) {
                    // Add a subtle animation to indicate the change
                    statusBadge.style.animation = 'badgeUpdate 0.8s ease-in-out';
                    setTimeout(() => {
                        statusBadge.style.animation = '';
                    }, 800);
                }
            }
            
            // Show status update feedback
            function showStatusFeedback(eventId, type, message) {
                const container = document.querySelector(`[data-event-id="${eventId}"] .event-status-container`);
                if (!container) return;
                
                // Remove any existing feedback
                const existingFeedback = container.querySelector('.status-feedback');
                if (existingFeedback) {
                    existingFeedback.remove();
                }
                
                const feedback = document.createElement('div');
                feedback.className = `alert alert-${type === 'success' ? 'success' : 'danger'} alert-sm mt-2 status-feedback`;
                feedback.style.cssText = 'font-size: 0.75rem; padding: 0.25rem 0.5rem; animation: fadeInOut 3s forwards; margin-bottom: 0;';
                feedback.innerHTML = `<i class="fas fa-${type === 'success' ? 'check' : 'exclamation'}-circle"></i> ${message}`;
                
                container.appendChild(feedback);
                
                // Remove after animation
                setTimeout(() => {
                    if (feedback.parentElement) {
                        feedback.remove();
                    }
                }, 3000);
            }
            
            // Listen for successful registrations from the event details modal
            // This will be called by the modal's registration success handler
            window.updateEventListBadge = function(eventId, isRegistered, message) {
                updateEventStatusBadge(eventId, isRegistered, true);
                if (message) {
                    showStatusFeedback(eventId, 'success', message);
                }
            };
            
            // Refresh authentication state and event statuses (called when user logs in/out)
            window.refreshAuthenticationState = function() {
                console.log('🔄 Refreshing authentication state and event statuses...');
                
                // Clear any cached authentication state
                if (typeof clearModalCache === 'function') {
                    clearModalCache();
                }
                
                // Reload the page to get fresh authentication state
                // This ensures all event statuses reflect the current user state
                location.reload();
            };
            
            // Ensure event list items open modal on click (except for badge clicks) - backup handler
            document.addEventListener('click', function(e) {
                const listItem = e.target.closest('.event-list-item');
                if (!listItem) return;
                
                // Don't trigger modal if clicking on the status badge area
                if (e.target.closest('.event-status-container')) {
                    return;
                }
                
                // Only trigger if no other handler has handled this
                if (!e.defaultPrevented) {
                    const eventId = listItem.getAttribute('data-event-id');
                    if (eventId) {
                        showEventDetailsModal(eventId, isAuthenticated, {
                            calendarType: 'subscribe',
                            detailsUrl: '@Url.Action("GetPublicEventDetails", "Home")',
                            joinUrl: '@Url.Action("Join", "Events")',
                            leaveUrl: '@Url.Action("Leave", "Events")'
                        });
                    }
                }
            });
            
            // Accessibility enhancements
            // Announce page load to screen readers
            const statusRegion = document.getElementById('page-status');
            if (statusRegion) {
                statusRegion.textContent = 'Page loaded successfully';
                setTimeout(() => {
                    statusRegion.textContent = '';
                }, 2000);
            }
            
        });
        
        // Add quick registration function for available events (global scope)
        window.quickRegisterEvent = function(eventId) {
            if (!isAuthenticated) {
                const errorRegion = document.getElementById('page-errors');
                if (errorRegion) {
                    errorRegion.textContent = 'Must be logged in to register';
                    setTimeout(() => {
                        errorRegion.textContent = '';
                    }, 3000);
                }
                return;
            }
            
            // Open the event details modal for registration
            showEventDetailsModal(eventId, isAuthenticated, {
                calendarType: 'subscribe',
                detailsUrl: '@Url.Action("GetPublicEventDetails", "Home")',
                joinUrl: '@Url.Action("Join", "Events")',
                leaveUrl: '@Url.Action("Leave", "Events")'
            });
        };
    </script>
}

<style>
    .calendar-mini {
        font-size: 0.85rem;
    }
    
    .calendar-mini .fc-toolbar-title {
        font-size: 1.1rem;
    }
    
    .calendar-mini .fc-button {
        padding: 0.2rem 0.4rem;
        font-size: 0.85rem;
    }
    
    .calendar-mini .fc-daygrid-event {
        font-size: 0.75rem;
        padding: 1px;
    }
    
    .calendar-mini .fc-daygrid-day-number {
        font-size: 0.85rem;
    }
    
    .calendar-mini .fc-col-header-cell {
        font-size: 0.85rem;
    }
    
    .list-group-item {
        border-left: 4px solid transparent;
        transition: all 0.3s ease;
    }
    
    .event-list-item {
        cursor: pointer;
    }
    
    .event-list-item:hover {
        background-color: #f8f9fa;
    }
    
    .event-list-item:focus {
        outline: 2px solid #007bff;
        outline-offset: -2px;
    }
    
    .list-group-item:hover {
        background-color: #f8f9fa;
        border-left-color: #007bff;
    }
    
    /* Mobile-first responsive design */
    @@media (max-width: 991px) {
        .col-lg-7, .col-lg-5 {
            margin-bottom: 1rem;
        }
        
        /* Stack cards vertically on mobile */
        .container-fluid .row {
            flex-direction: column;
        }
        
        /* Make events list full-width on mobile */
        .col-lg-7 {
            order: 1;
        }
        
        /* Move calendar below events on mobile */
        .col-lg-5 {
            order: 2;
        }
    }
    
    /* Tablet optimizations */
    @@media (max-width: 768px) {
        /* Touch-friendly buttons with minimum 44px size */
        .btn {
            min-width: 44px;
            min-height: 44px;
            padding: 0.75rem 1rem;
        }
        
        .btn-sm {
            min-width: 36px;
            min-height: 36px;
            padding: 0.5rem 0.75rem;
        }
        
        /* Larger tap targets for event items */
        .event-list-item {
            padding: 1.25rem !important;
            margin-bottom: 1rem !important;
        }
        
        /* Responsive text sizing */
        .card-title {
            font-size: 1.1rem;
        }
        
        h1, .h1 {
            font-size: 1.75rem;
        }
        
        h2, .h2 {
            font-size: 1.5rem;
        }
        
        /* Calendar responsive adjustments */
        #miniCalendar {
            font-size: 0.85rem;
        }
        
        .calendar-mini .fc-toolbar {
            flex-direction: column;
            gap: 0.5rem;
        }
        
        .calendar-mini .fc-toolbar-chunk {
            display: flex;
            justify-content: center;
        }
    }
    
    /* Mobile phone optimizations */
    @@media (max-width: 576px) {
        /* Compact header */
        .container-fluid {
            padding-left: 1rem;
            padding-right: 1rem;
        }
        
        /* Stack breadcrumbs */
        .breadcrumb {
            flex-wrap: wrap;
            font-size: 0.875rem;
        }
        
        /* Simplified event layout */
        .event-list-item .d-flex {
            flex-direction: column;
            gap: 0.75rem;
        }
        
        .event-list-item .ms-3 {
            margin-left: 0 !important;
            align-items: flex-start !important;
        }
        
        /* Optimize badges for mobile */
        .badge {
            font-size: 0.8rem;
            white-space: normal;
            line-height: 1.2;
        }
        
        /* Calendar mobile optimization */
        .calendar-mini {
            font-size: 0.8rem;
        }
        
        .calendar-mini .fc-daygrid-day {
            min-height: 2rem;
        }
        
        .calendar-mini .fc-daygrid-event {
            font-size: 0.7rem;
            padding: 0.1rem;
        }
        
        /* Hide secondary information on very small screens */
        .event-list-item .small {
            font-size: 0.8rem;
        }
        
        /* Simplify dropdown on mobile */
        .dropdown-menu {
            font-size: 0.9rem;
        }
    }
    
    /* Quick action animations and mobile styles */
    @@keyframes fadeInOut {
        0% { opacity: 0; transform: translateY(-10px); }
        20% { opacity: 1; transform: translateY(0); }
        80% { opacity: 1; transform: translateY(0); }
        100% { opacity: 0; transform: translateY(-10px); }
    }
    
    .event-status-container {
        min-width: 120px;
    }
    
    .event-status-container .badge {
        font-size: 0.9rem;
        padding: 0.5rem 0.75rem;
        white-space: nowrap;
    }
    
    /* Badge update animation */
    @@keyframes badgeUpdate {
        0% { transform: scale(1); }
        50% { transform: scale(1.1); box-shadow: 0 2px 8px rgba(0,123,255,0.3); }
        100% { transform: scale(1); }
    }
    
    @@media (max-width: 768px) {
        .event-status-container {
            min-width: 100px;
        }
        
        .event-status-container .badge {
            font-size: 0.85rem;
            padding: 0.4rem 0.6rem;
        }
        
        .event-status-container small {
            font-size: 0.7rem;
        }
        
        .event-list-item .d-flex {
            flex-direction: column;
            align-items: stretch;
        }
        
        .event-list-item .ms-3 {
            margin-left: 0 !important;
            margin-top: 1rem;
            align-items: stretch !important;
        }
    }
</style>