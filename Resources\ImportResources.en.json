{"Import": {"Title": "Import Members", "SelectFile": "Select Excel File", "FileFormat": "Supported formats: .xlsx, .xls", "Upload": "Upload File", "Processing": "Processing import...", "Success": "Import completed successfully", "Error": "An error occurred during import", "ValidationErrors": "Validation Errors Found", "DuplicatesFound": "Potential Duplicates Detected", "ReviewRequired": "Review Required Before Processing"}, "Validation": {"RequiredField": "This field is required", "InvalidEmail": "Please enter a valid email address", "InvalidDate": "Please enter a valid date", "InvalidPhone": "Please enter a valid phone number", "InvalidPostalCode": "Please enter a valid postal code", "FutureDate": "Date cannot be in the future", "DuplicateEmail": "This email address already exists", "InvalidStatus": "Please select a valid status"}, "Duplicates": {"Title": "Duplicate Resolution", "PotentialMatch": "Potential duplicate found", "ConfidenceHigh": "High confidence match", "ConfidenceMedium": "Medium confidence match", "ConfidenceLow": "Low confidence match", "ExactEmail": "Exact email match", "SimilarName": "Similar name and date of birth", "Actions": {"KeepExisting": "Keep Existing Member", "ReplaceExisting": "Replace with New Data", "MergeFields": "<PERSON><PERSON>", "CreateAsNew": "Create as New Member"}, "Reasons": {"MoreComplete": "Existing record is more complete", "MoreRecent": "New record has more recent information", "DifferentPerson": "These are different people", "CombineBest": "Combine the best information from both"}}, "Processing": {"Title": "Processing Members", "ValidatingData": "Validating imported data...", "DetectingDuplicates": "Detecting potential duplicates...", "CreatingMembers": "Creating member records...", "UpdatingRecords": "Updating existing records...", "GeneratingReport": "Generating import report...", "Complete": "Processing complete", "RecordsProcessed": "Records processed: {0}", "RecordsCreated": "New members created: {0}", "RecordsUpdated": "Existing members updated: {0}", "ErrorsFound": "Errors found: {0}"}, "Errors": {"FileNotFound": "The selected file could not be found", "InvalidFileFormat": "The file format is not supported. Please use .xlsx or .xls files", "FileCorrupted": "The file appears to be corrupted or cannot be read", "FileTooLarge": "The file is too large. Maximum size is {0} MB", "NoDataFound": "No data was found in the file", "DatabaseError": "A database error occurred. Please try again later", "UnexpectedError": "An unexpected error occurred. Please contact support if the problem persists", "ValidationFailed": "Data validation failed. Please correct the errors and try again", "DuplicateProcessingFailed": "Duplicate processing failed. Please review and try again", "PermissionDenied": "You do not have permission to perform this operation"}, "Buttons": {"Cancel": "Cancel", "Continue": "Continue", "Retry": "Retry", "Download": "Download", "Back": "Back", "Next": "Next", "Finish": "Finish", "Save": "Save", "Delete": "Delete", "Edit": "Edit", "View": "View"}, "Messages": {"ConfirmDelete": "Are you sure you want to delete this item?", "UnsavedChanges": "You have unsaved changes. Are you sure you want to leave?", "OperationComplete": "Operation completed successfully", "PleaseWait": "Please wait while we process your request...", "NoRecordsFound": "No records found matching your criteria", "LoadingData": "Loading data..."}}