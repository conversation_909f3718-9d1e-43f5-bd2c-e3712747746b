# ParaHockey Database Permissions Test Script
# This script tests database connectivity and permissions for both Test and Production environments

Write-Host "=== ParaHockey Database Permissions Test ===" -ForegroundColor Cyan
Write-Host ""

# Function to test database connection and permissions
function Test-DatabasePermissions {
    param(
        [string]$ServerInstance,
        [string]$Database,
        [string]$Username = $null,
        [string]$Password = $null,
        [string]$Environment
    )
    
    Write-Host "Testing $Environment Environment:" -ForegroundColor Yellow
    Write-Host "Server: $ServerInstance"
    Write-Host "Database: $Database"
    
    try {
        # Build connection string
        if ($Username) {
            Write-Host "Authentication: SQL Server Authentication (User: $Username)"
            $connectionString = "Server=$ServerInstance;Database=$Database;User Id=$Username;Password=$Password;TrustServerCertificate=True;"
        } else {
            Write-Host "Authentication: Windows Authentication"
            $connectionString = "Server=$ServerInstance;Database=$Database;Integrated Security=True;TrustServerCertificate=True;"
        }
        
        # Test basic connection
        $connection = New-Object System.Data.SqlClient.SqlConnection($connectionString)
        $connection.Open()
        Write-Host "✓ Connection successful" -ForegroundColor Green
        
        # Get current user info
        $userCmd = $connection.CreateCommand()
        $userCmd.CommandText = @"
SELECT 
    SUSER_SNAME() as LoginName,
    USER_NAME() as DatabaseUser,
    IS_ROLEMEMBER('db_owner') as IsDbOwner,
    IS_ROLEMEMBER('db_datareader') as IsDataReader,
    IS_ROLEMEMBER('db_datawriter') as IsDataWriter,
    HAS_PERMS_BY_NAME(NULL, NULL, 'VIEW SERVER STATE') as CanViewServerState
"@
        
        $reader = $userCmd.ExecuteReader()
        if ($reader.Read()) {
            Write-Host ""
            Write-Host "User Information:" -ForegroundColor Cyan
            Write-Host "  Login Name: $($reader['LoginName'])"
            Write-Host "  Database User: $($reader['DatabaseUser'])"
            Write-Host "  Is db_owner: $($reader['IsDbOwner'])"
            Write-Host "  Is db_datareader: $($reader['IsDataReader'])"
            Write-Host "  Is db_datawriter: $($reader['IsDataWriter'])"
            Write-Host "  Can View Server State: $($reader['CanViewServerState'])"
        }
        $reader.Close()
        
        # Test table access
        Write-Host ""
        Write-Host "Testing Table Access:" -ForegroundColor Cyan
        
        # Test Members table
        $testCmd = $connection.CreateCommand()
        $testCmd.CommandText = "SELECT COUNT(*) FROM Members"
        $count = $testCmd.ExecuteScalar()
        Write-Host "  ✓ Members table: $count records" -ForegroundColor Green
        
        # Test Events table
        $testCmd.CommandText = "SELECT COUNT(*) FROM Events"
        $count = $testCmd.ExecuteScalar()
        Write-Host "  ✓ Events table: $count records" -ForegroundColor Green
        
        # Test EventCategories table
        $testCmd.CommandText = "SELECT COUNT(*) FROM EventCategories"
        $count = $testCmd.ExecuteScalar()
        Write-Host "  ✓ EventCategories table: $count records" -ForegroundColor Green
        
        # Test write permissions
        Write-Host ""
        Write-Host "Testing Write Permissions:" -ForegroundColor Cyan
        
        # Start transaction for rollback
        $transaction = $connection.BeginTransaction()
        
        try {
            $insertCmd = $connection.CreateCommand()
            $insertCmd.Transaction = $transaction
            $insertCmd.CommandText = @"
INSERT INTO AuditLogs (TableName, RecordId, Action, UserId, UserName, Timestamp, OldValues, NewValues)
VALUES ('TEST', 0, 'PERMISSION_TEST', 'TEST_USER', 'Permission Test', GETUTCDATE(), '{}', '{}')
"@
            $insertCmd.ExecuteNonQuery() | Out-Null
            Write-Host "  ✓ INSERT permission verified" -ForegroundColor Green
            
            # Test UPDATE
            $updateCmd = $connection.CreateCommand()
            $updateCmd.Transaction = $transaction
            $updateCmd.CommandText = "UPDATE AuditLogs SET UserName = 'Updated Test' WHERE TableName = 'TEST' AND Action = 'PERMISSION_TEST'"
            $updateCmd.ExecuteNonQuery() | Out-Null
            Write-Host "  ✓ UPDATE permission verified" -ForegroundColor Green
            
            # Test DELETE
            $deleteCmd = $connection.CreateCommand()
            $deleteCmd.Transaction = $transaction
            $deleteCmd.CommandText = "DELETE FROM AuditLogs WHERE TableName = 'TEST' AND Action = 'PERMISSION_TEST'"
            $deleteCmd.ExecuteNonQuery() | Out-Null
            Write-Host "  ✓ DELETE permission verified" -ForegroundColor Green
            
            # Rollback - we don't want to actually modify data
            $transaction.Rollback()
            Write-Host "  ✓ Transaction rolled back (test data not saved)" -ForegroundColor Gray
        }
        catch {
            $transaction.Rollback()
            Write-Host "  ✗ Write permissions test failed: $_" -ForegroundColor Red
        }
        
        # Check role membership details
        Write-Host ""
        Write-Host "Database Role Membership:" -ForegroundColor Cyan
        $roleCmd = $connection.CreateCommand()
        $roleCmd.CommandText = @"
SELECT 
    dp.name AS PrincipalName,
    dp.type_desc AS PrincipalType,
    dp2.name AS RoleName
FROM sys.database_role_members rm
JOIN sys.database_principals dp ON rm.member_principal_id = dp.principal_id
JOIN sys.database_principals dp2 ON rm.role_principal_id = dp2.principal_id
WHERE dp.name = USER_NAME() OR dp.name = 'ParaHockeyUser'
ORDER BY dp.name, dp2.name
"@
        
        $reader = $roleCmd.ExecuteReader()
        while ($reader.Read()) {
            Write-Host "  User: $($reader['PrincipalName']) ($($reader['PrincipalType'])) -> Role: $($reader['RoleName'])"
        }
        $reader.Close()
        
        # Check explicit permissions
        Write-Host ""
        Write-Host "Explicit Permissions:" -ForegroundColor Cyan
        $permCmd = $connection.CreateCommand()
        $permCmd.CommandText = @"
SELECT 
    p.permission_name,
    p.state_desc,
    s.name AS schema_name,
    o.name AS object_name,
    pr.name AS principal_name
FROM sys.database_permissions p
LEFT JOIN sys.objects o ON p.major_id = o.object_id
LEFT JOIN sys.schemas s ON o.schema_id = s.schema_id
LEFT JOIN sys.database_principals pr ON p.grantee_principal_id = pr.principal_id
WHERE pr.name = USER_NAME() OR pr.name = 'ParaHockeyUser'
ORDER BY pr.name, p.permission_name
"@
        
        $reader = $permCmd.ExecuteReader()
        $hasPerms = $false
        while ($reader.Read()) {
            $hasPerms = $true
            $objName = if ($reader['object_name']) { "$($reader['schema_name']).$($reader['object_name'])" } else { "DATABASE" }
            Write-Host "  $($reader['principal_name']): $($reader['permission_name']) on $objName ($($reader['state_desc']))"
        }
        if (-not $hasPerms) {
            Write-Host "  No explicit permissions found (inheriting from roles)" -ForegroundColor Gray
        }
        $reader.Close()
        
        $connection.Close()
        
        Write-Host ""
        Write-Host "✓ All tests completed successfully for $Environment" -ForegroundColor Green
        Write-Host ""
        
        return $true
    }
    catch {
        Write-Host "✗ Error testing $Environment`: $_" -ForegroundColor Red
        Write-Host ""
        return $false
    }
}

# Test both environments
Write-Host "Starting database permission tests..." -ForegroundColor White
Write-Host "=" * 60
Write-Host ""

# Test TEST environment
$testResult = Test-DatabasePermissions `
    -ServerInstance "SIMBA\SQLEXPRESS" `
    -Database "ParaHockeyDB_TEST" `
    -Username "ParaHockeyUser" `
    -Password "ParaHockey2025!" `
    -Environment "TEST"

Write-Host "=" * 60
Write-Host ""

# Test PRODUCTION environment with SQL Authentication
$prodSqlResult = Test-DatabasePermissions `
    -ServerInstance "SIMBA\SQLEXPRESS" `
    -Database "ParaHockeyDB" `
    -Username "ParaHockeyUser" `
    -Password "ParaHockey2025!" `
    -Environment "PRODUCTION (SQL Auth)"

Write-Host "=" * 60
Write-Host ""

# Test PRODUCTION environment with Windows Authentication
$prodWinResult = Test-DatabasePermissions `
    -ServerInstance "SIMBA\SQLEXPRESS" `
    -Database "ParaHockeyDB" `
    -Environment "PRODUCTION (Windows Auth)"

Write-Host "=" * 60
Write-Host ""

# Summary
Write-Host "Test Summary:" -ForegroundColor Yellow
Write-Host "  TEST Environment: $(if($testResult){'PASSED'}else{'FAILED'})" -ForegroundColor $(if($testResult){'Green'}else{'Red'})
Write-Host "  PRODUCTION (SQL Auth): $(if($prodSqlResult){'PASSED'}else{'FAILED'})" -ForegroundColor $(if($prodSqlResult){'Green'}else{'Red'})
Write-Host "  PRODUCTION (Windows Auth): $(if($prodWinResult){'PASSED'}else{'FAILED'})" -ForegroundColor $(if($prodWinResult){'Green'}else{'Red'})

Write-Host ""
Write-Host "Script completed." -ForegroundColor Cyan