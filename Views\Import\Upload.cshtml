@model MemberImportUploadViewModel
@{
    ViewBag.Title = "Import Members from Excel";
}

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2><i class="fas fa-file-upload text-primary"></i> Import Members from Excel</h2>
                <a href="@Url.Action("History", "Import")" class="btn btn-outline-secondary">
                    <i class="fas fa-history"></i> View Import History
                </a>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Upload Form -->
        <div class="col-lg-8">
            <div class="card shadow-sm">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0"><i class="fas fa-upload"></i> Upload Excel File</h5>
                </div>
                <div class="card-body">
                    @if (!ViewData.ModelState.IsValid)
                    {
                        <div class="alert alert-danger">
                            <h6><i class="fas fa-exclamation-triangle"></i> Please correct the following errors:</h6>
                            <ul class="mb-0">
                                @foreach (var error in ViewData.ModelState.Values.SelectMany(v => v.Errors))
                                {
                                    <li>@error.ErrorMessage</li>
                                }
                            </ul>
                        </div>
                    }

                    <form asp-action="Upload" method="post" enctype="multipart/form-data" id="uploadForm">
                        @Html.AntiForgeryToken()
                        
                        <div class="mb-4">
                            <label asp-for="ExcelFile" class="form-label fw-bold">Excel File <span class="text-danger">*</span></label>
                            <input asp-for="ExcelFile" type="file" class="form-control" accept=".xlsx,.xls" required>
                            <div class="form-text">
                                <i class="fas fa-info-circle text-info"></i> 
                                Supported formats: .xlsx, .xls | Maximum file size: 10 MB | Maximum rows: 10,000
                            </div>
                            <span asp-validation-for="ExcelFile" class="text-danger"></span>
                        </div>

                        <div class="mb-4">
                            <label asp-for="Description" class="form-label">Description (Optional)</label>
                            <textarea asp-for="Description" class="form-control" rows="3" 
                                      placeholder="Add a description for this import batch (e.g., 'New member registrations January 2025')"></textarea>
                            <span asp-validation-for="Description" class="text-danger"></span>
                        </div>

                        <div class="d-grid gap-2 d-md-flex">
                            <button type="submit" class="btn btn-primary btn-lg" id="uploadButton">
                                <i class="fas fa-cloud-upload-alt"></i> Upload and Process
                            </button>
                            <button type="button" class="btn btn-outline-secondary" onclick="resetForm()">
                                <i class="fas fa-undo"></i> Reset
                            </button>
                        </div>
                    </form>

                    <!-- Progress Bar (hidden initially) -->
                    <div id="uploadProgress" class="mt-4" style="display: none;">
                        <div class="d-flex align-items-center mb-2">
                            <strong>Processing file...</strong>
                            <div class="spinner-border spinner-border-sm ms-2" role="status">
                                <span class="visually-hidden">Loading...</span>
                            </div>
                        </div>
                        <div class="progress">
                            <div class="progress-bar progress-bar-striped progress-bar-animated" 
                                 role="progressbar" style="width: 0%" id="progressBar">
                                <span id="progressText">0%</span>
                            </div>
                        </div>
                        <small class="text-muted">Please wait while we process your file...</small>
                    </div>
                </div>
            </div>
        </div>

        <!-- Help and Instructions -->
        <div class="col-lg-4">
            <div class="card shadow-sm">
                <div class="card-header bg-info text-white">
                    <h6 class="mb-0"><i class="fas fa-question-circle"></i> File Format Requirements</h6>
                </div>
                <div class="card-body">
                    <h6 class="text-success"><i class="fas fa-check-circle"></i> Required Columns:</h6>
                    <ul class="list-unstyled mb-3">
                        <li><i class="fas fa-dot-circle text-danger me-1"></i> <strong>FirstName</strong></li>
                        <li><i class="fas fa-dot-circle text-danger me-1"></i> <strong>LastName</strong></li>
                    </ul>

                    <h6 class="text-info"><i class="fas fa-info-circle"></i> Optional Columns:</h6>
                    <ul class="list-unstyled mb-3">
                        <li><i class="fas fa-circle text-secondary me-1" style="font-size: 0.5rem;"></i> Email</li>
                        <li><i class="fas fa-circle text-secondary me-1" style="font-size: 0.5rem;"></i> Phone</li>
                        <li><i class="fas fa-circle text-secondary me-1" style="font-size: 0.5rem;"></i> DateOfBirth</li>
                        <li><i class="fas fa-circle text-secondary me-1" style="font-size: 0.5rem;"></i> Address</li>
                        <li><i class="fas fa-circle text-secondary me-1" style="font-size: 0.5rem;"></i> City</li>
                        <li><i class="fas fa-circle text-secondary me-1" style="font-size: 0.5rem;"></i> PostalCode</li>
                        <li><i class="fas fa-circle text-secondary me-1" style="font-size: 0.5rem;"></i> Gender</li>
                        <li><i class="fas fa-circle text-secondary me-1" style="font-size: 0.5rem;"></i> Province</li>
                        <li><i class="fas fa-circle text-secondary me-1" style="font-size: 0.5rem;"></i> PhoneType</li>
                        <li><i class="fas fa-circle text-secondary me-1" style="font-size: 0.5rem;"></i> RegistrationType</li>
                    </ul>

                    <div class="alert alert-warning">
                        <h6><i class="fas fa-exclamation-triangle"></i> Important Notes:</h6>
                        <ul class="mb-0 small">
                            <li>First row must contain column headers</li>
                            <li>Duplicate emails will be flagged for review</li>
                            <li>Invalid data will be marked for correction</li>
                            <li>Large files may take several minutes to process</li>
                        </ul>
                    </div>
                </div>
            </div>

            <!-- Recent Activity -->
            <div class="card shadow-sm mt-4">
                <div class="card-header bg-secondary text-white">
                    <h6 class="mb-0"><i class="fas fa-clock"></i> Import Tips</h6>
                </div>
                <div class="card-body">
                    <div class="list-group list-group-flush">
                        <div class="list-group-item border-0 px-0">
                            <i class="fas fa-lightbulb text-warning me-2"></i>
                            <small>Use consistent date formats (YYYY-MM-DD recommended)</small>
                        </div>
                        <div class="list-group-item border-0 px-0">
                            <i class="fas fa-lightbulb text-warning me-2"></i>
                            <small>Phone numbers can include spaces and dashes</small>
                        </div>
                        <div class="list-group-item border-0 px-0">
                            <i class="fas fa-lightbulb text-warning me-2"></i>
                            <small>Province codes: QC, ON, BC, AB, etc.</small>
                        </div>
                        <div class="list-group-item border-0 px-0">
                            <i class="fas fa-lightbulb text-warning me-2"></i>
                            <small>Gender: Male/Female or M/F accepted</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
<script>
    function resetForm() {
        document.getElementById('uploadForm').reset();
        document.getElementById('uploadProgress').style.display = 'none';
    }

    document.getElementById('uploadForm').addEventListener('submit', function(e) {
        // Show progress bar
        document.getElementById('uploadProgress').style.display = 'block';
        document.getElementById('uploadButton').disabled = true;
        
        // Simulate progress (real implementation would track actual progress)
        let progress = 0;
        const progressBar = document.getElementById('progressBar');
        const progressText = document.getElementById('progressText');
        
        const interval = setInterval(() => {
            progress += Math.random() * 20;
            if (progress > 90) progress = 90; // Don't complete until real response
            
            progressBar.style.width = progress + '%';
            progressText.textContent = Math.round(progress) + '%';
        }, 500);
        
        // Clean up on page unload
        window.addEventListener('beforeunload', () => {
            clearInterval(interval);
        });
    });

    // File validation
    document.querySelector('input[type="file"]').addEventListener('change', function(e) {
        const file = e.target.files[0];
        if (file) {
            const maxSize = 10 * 1024 * 1024; // 10MB
            const validTypes = [
                'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', // .xlsx
                'application/vnd.ms-excel' // .xls
            ];
            
            if (file.size > maxSize) {
                alert('File size must be less than 10MB');
                e.target.value = '';
                return;
            }
            
            if (!validTypes.includes(file.type)) {
                alert('Please select a valid Excel file (.xlsx or .xls)');
                e.target.value = '';
                return;
            }
        }
    });
</script>
}