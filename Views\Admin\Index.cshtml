@{
    ViewData["Title"] = SharedLocalizer["AdminDashboard"];
}

<!-- Skip navigation links for accessibility -->
<a href="#main-content" class="skip-link visually-hidden-focusable">@SharedLocalizer["SkipToMainContent"]</a>
<a href="#statistics-cards" class="skip-link visually-hidden-focusable">@SharedLocalizer["SkipToStatistics"]</a>
<a href="#quick-actions" class="skip-link visually-hidden-focusable">@SharedLocalizer["SkipToQuickActions"]</a>

<main id="main-content" role="main" aria-label="@SharedLocalizer["AdminDashboardMainContent"]">
    <div class="container-fluid mt-4">
        <!-- ARIA live regions for dynamic feedback -->
        <div id="dashboard-status" aria-live="polite" aria-atomic="true" class="visually-hidden"></div>
        <div id="dashboard-errors" aria-live="assertive" aria-atomic="true" class="visually-hidden"></div>

        <!-- Header -->
        <header class="row mb-4" role="banner">
            <div class="col">
                <h1 class="h2 text-primary" id="dashboard-title">
                    <i class="fas fa-tachometer-alt" aria-hidden="true"></i> 
                    @SharedLocalizer["AdminDashboard"]
                </h1>
                <p class="text-muted" aria-describedby="dashboard-title">
                    @SharedLocalizer["AdminWelcomeMessage", ViewBag.CurrentUser, ViewBag.Environment]
                </p>
            </div>
        </header>

        <!-- Statistics Cards -->
        <section id="statistics-cards" class="row mb-4" aria-labelledby="statistics-title">
            <div class="col-12 mb-3">
                <h2 id="statistics-title" class="h4 text-secondary">
                    <i class="fas fa-chart-bar" aria-hidden="true"></i>
                    @SharedLocalizer["SystemStatistics"]
                </h2>
            </div>

            <!-- Total Members Card -->
            <div class="col-xl-3 col-lg-6 col-md-6 col-sm-12 mb-3">
                <article class="card bg-primary text-white h-100" role="region" aria-labelledby="members-card-title">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h3 id="members-card-title" class="card-title h4 mb-2">
                                    @ViewBag.MemberCount
                                </h3>
                                <p class="card-text mb-0">@SharedLocalizer["TotalMembers"]</p>
                            </div>
                            <div class="align-self-center" aria-hidden="true">
                                <i class="fas fa-users fa-2x opacity-75"></i>
                            </div>
                        </div>
                    </div>
                    <div class="card-footer border-0 bg-transparent">
                        <a href="@Url.Action("Members", "Admin")" 
                           class="btn btn-link text-white text-decoration-none p-0 d-flex align-items-center"
                           aria-describedby="members-card-title">
                            @SharedLocalizer["ViewAllMembers"] 
                            <i class="fas fa-arrow-circle-right ms-2" aria-hidden="true"></i>
                        </a>
                    </div>
                </article>
            </div>
            
            <!-- Total Parents Card -->
            <div class="col-xl-3 col-lg-6 col-md-6 col-sm-12 mb-3">
                <article class="card bg-success text-white h-100" role="region" aria-labelledby="parents-card-title">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h3 id="parents-card-title" class="card-title h4 mb-2">
                                    @ViewBag.ParentCount
                                </h3>
                                <p class="card-text mb-0">@SharedLocalizer["TotalParents"]</p>
                            </div>
                            <div class="align-self-center" aria-hidden="true">
                                <i class="fas fa-user-friends fa-2x opacity-75"></i>
                            </div>
                        </div>
                    </div>
                </article>
            </div>
            
            <!-- Emergency Contacts Card -->
            <div class="col-xl-3 col-lg-6 col-md-6 col-sm-12 mb-3">
                <article class="card bg-warning text-white h-100" role="region" aria-labelledby="emergency-card-title">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h3 id="emergency-card-title" class="card-title h4 mb-2">
                                    @ViewBag.EmergencyContactCount
                                </h3>
                                <p class="card-text mb-0">@SharedLocalizer["EmergencyContacts"]</p>
                            </div>
                            <div class="align-self-center" aria-hidden="true">
                                <i class="fas fa-phone fa-2x opacity-75"></i>
                            </div>
                        </div>
                    </div>
                </article>
            </div>
            
            <!-- Environment Info Card -->
            <div class="col-xl-3 col-lg-6 col-md-6 col-sm-12 mb-3">
                <article class="card bg-info text-white h-100" role="region" aria-labelledby="environment-card-title">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h3 id="environment-card-title" class="card-title h4 mb-2">
                                    @ViewBag.Environment
                                </h3>
                                <p class="card-text mb-0">@SharedLocalizer["CurrentEnvironment"]</p>
                            </div>
                            <div class="align-self-center" aria-hidden="true">
                                <i class="fas fa-server fa-2x opacity-75"></i>
                            </div>
                        </div>
                    </div>
                    <div class="card-footer border-0 bg-transparent">
                        <a href="@Url.Action("SystemInfo", "Admin")" 
                           class="btn btn-link text-white text-decoration-none p-0 d-flex align-items-center"
                           aria-describedby="environment-card-title">
                            @SharedLocalizer["ViewSystemInfo"] 
                            <i class="fas fa-arrow-circle-right ms-2" aria-hidden="true"></i>
                        </a>
                    </div>
                </article>
            </div>
        </section>

        <!-- Quick Actions -->
        <section id="quick-actions" class="row mb-4" aria-labelledby="quick-actions-title">
            <div class="col">
                <article class="card">
                    <header class="card-header">
                        <h2 id="quick-actions-title" class="card-title mb-0 h4">
                            <i class="fas fa-bolt" aria-hidden="true"></i> 
                            @SharedLocalizer["QuickActions"]
                        </h2>
                        <p class="text-muted mb-0 mt-2">@SharedLocalizer["QuickActionsDescription"]</p>
                    </header>
                    <div class="card-body">
                        <nav aria-label="@SharedLocalizer["AdminQuickActions"]">
                            <ul class="list-unstyled row" role="list">
                                <!-- Primary Actions -->
                                <li class="col-xl-3 col-lg-4 col-md-6 col-sm-12 mb-3">
                                    <a href="@Url.Action("Members", "Admin")" 
                                       class="btn btn-outline-primary d-flex align-items-center justify-content-start h-100 p-3"
                                       aria-describedby="members-action-desc">
                                        <i class="fas fa-users me-3" aria-hidden="true"></i>
                                        <div class="text-start">
                                            <div class="fw-bold">@SharedLocalizer["ViewAllMembers"]</div>
                                            <small id="members-action-desc" class="text-muted">@SharedLocalizer["ViewAllMembersDescription"]</small>
                                        </div>
                                    </a>
                                </li>
                                <li class="col-xl-3 col-lg-4 col-md-6 col-sm-12 mb-3">
                                    <a href="@Url.Action("Register", "Members")" 
                                       class="btn btn-outline-success d-flex align-items-center justify-content-start h-100 p-3"
                                       aria-describedby="add-member-desc">
                                        <i class="fas fa-user-plus me-3" aria-hidden="true"></i>
                                        <div class="text-start">
                                            <div class="fw-bold">@SharedLocalizer["AddNewMember"]</div>
                                            <small id="add-member-desc" class="text-muted">@SharedLocalizer["AddNewMemberDescription"]</small>
                                        </div>
                                    </a>
                                </li>
                                <li class="col-xl-3 col-lg-4 col-md-6 col-sm-12 mb-3">
                                    <a href="@Url.Action("Calendar", "Admin")" 
                                       class="btn btn-outline-warning d-flex align-items-center justify-content-start h-100 p-3"
                                       aria-describedby="calendar-desc">
                                        <i class="fas fa-calendar-alt me-3" aria-hidden="true"></i>
                                        <div class="text-start">
                                            <div class="fw-bold">@SharedLocalizer["ManageCalendar"]</div>
                                            <small id="calendar-desc" class="text-muted">@SharedLocalizer["ManageCalendarDescription"]</small>
                                        </div>
                                    </a>
                                </li>
                                <li class="col-xl-3 col-lg-4 col-md-6 col-sm-12 mb-3">
                                    <a href="@Url.Action("AdminUsers", "Admin")" 
                                       class="btn btn-outline-danger d-flex align-items-center justify-content-start h-100 p-3"
                                       aria-describedby="admin-users-desc">
                                        <i class="fas fa-users-cog me-3" aria-hidden="true"></i>
                                        <div class="text-start">
                                            <div class="fw-bold">@SharedLocalizer["ManageAdmins"]</div>
                                            <small id="admin-users-desc" class="text-muted">@SharedLocalizer["ManageAdminsDescription"]</small>
                                        </div>
                                    </a>
                                </li>

                                <!-- Import Error Review -->
                                <li class="col-xl-3 col-lg-4 col-md-6 col-sm-12 mb-3">
                                    <a href="@Url.Action("Queue", "TempMembers", new { status = "NeedsFix" })" 
                                       class="btn btn-outline-warning d-flex align-items-center justify-content-start h-100 p-3"
                                       aria-describedby="import-errors-desc">
                                        <i class="fas fa-exclamation-triangle me-3" aria-hidden="true"></i>
                                        <div class="text-start">
                                            <div class="fw-bold">@SharedLocalizer["ReviewImportErrors"]</div>
                                            <small id="import-errors-desc" class="text-muted">@SharedLocalizer["ReviewImportErrorsDescription"]</small>
                                        </div>
                                    </a>
                                </li>

                                <!-- Secondary Actions -->
                                <li class="col-xl-3 col-lg-4 col-md-6 col-sm-12 mb-3">
                                    <a href="@Url.Action("PageAudit", "Admin")" 
                                       class="btn btn-outline-info d-flex align-items-center justify-content-start h-100 p-3"
                                       aria-describedby="audit-desc">
                                        <i class="fas fa-search me-3" aria-hidden="true"></i>
                                        <div class="text-start">
                                            <div class="fw-bold">@SharedLocalizer["PageAuditSystem"]</div>
                                            <small id="audit-desc" class="text-muted">@SharedLocalizer["PageAuditDescription"]</small>
                                        </div>
                                    </a>
                                </li>
                                <li class="col-xl-3 col-lg-4 col-md-6 col-sm-12 mb-3">
                                    <a href="@Url.Action("SystemInfo", "Admin")" 
                                       class="btn btn-outline-secondary d-flex align-items-center justify-content-start h-100 p-3"
                                       aria-describedby="system-info-desc">
                                        <i class="fas fa-cog me-3" aria-hidden="true"></i>
                                        <div class="text-start">
                                            <div class="fw-bold">@SharedLocalizer["SystemInformation"]</div>
                                            <small id="system-info-desc" class="text-muted">@SharedLocalizer["SystemInfoDescription"]</small>
                                        </div>
                                    </a>
                                </li>
                                <li class="col-xl-3 col-lg-4 col-md-6 col-sm-12 mb-3">
                                    <a href="@Url.Action("Teams", "Admin")" 
                                       class="btn btn-outline-primary d-flex align-items-center justify-content-start h-100 p-3"
                                       aria-describedby="teams-desc">
                                        <i class="fas fa-users-cog me-3" aria-hidden="true"></i>
                                        <div class="text-start">
                                            <div class="fw-bold">@SharedLocalizer["TeamsManagement"]</div>
                                            <small id="teams-desc" class="text-muted">@SharedLocalizer["TeamsDescription"]</small>
                                        </div>
                                    </a>
                                </li>
                                <li class="col-xl-3 col-lg-4 col-md-6 col-sm-12 mb-3">
                                    <a href="@Url.Action("Statistics", "Admin")" 
                                       class="btn btn-outline-success d-flex align-items-center justify-content-start h-100 p-3"
                                       aria-describedby="stats-desc">
                                        <i class="fas fa-chart-bar me-3" aria-hidden="true"></i>
                                        <div class="text-start">
                                            <div class="fw-bold">@SharedLocalizer["ViewStatistics"]</div>
                                            <small id="stats-desc" class="text-muted">@SharedLocalizer["StatisticsDescription"]</small>
                                        </div>
                                    </a>
                                </li>

                                <!-- Navigation Actions -->
                                <li class="col-xl-3 col-lg-4 col-md-6 col-sm-12 mb-3">
                                    <a href="@Url.Action("Index", "Home")" 
                                       class="btn btn-outline-secondary d-flex align-items-center justify-content-start h-100 p-3"
                                       aria-describedby="back-site-desc">
                                        <i class="fas fa-home me-3" aria-hidden="true"></i>
                                        <div class="text-start">
                                            <div class="fw-bold">@SharedLocalizer["BackToSite"]</div>
                                            <small id="back-site-desc" class="text-muted">@SharedLocalizer["BackToSiteDescription"]</small>
                                        </div>
                                    </a>
                                </li>
                            </ul>
                        </nav>
                    </div>
                </article>
            </div>
        </section>

        <!-- Recent Member Audit -->
        <section class="row mb-4" aria-labelledby="audit-logs-title">
            <div class="col-12">
                <article class="card">
                    <header class="card-header">
                        <h2 id="audit-logs-title" class="card-title mb-0 h4">
                            <i class="fas fa-history" aria-hidden="true"></i> 
                            @SharedLocalizer["RecentMemberAudit"]
                        </h2>
                        <p class="text-muted mb-0 mt-2">@SharedLocalizer["RecentAuditDescription"]</p>
                    </header>
                    <div class="card-body">
                        @if (ViewBag.RecentAuditLogs != null && ((IEnumerable<ParaHockeyApp.Models.ViewModels.AuditLogViewModel>)ViewBag.RecentAuditLogs).Any())
                        {
                            <div class="table-responsive">
                                <table class="table table-hover table-sm" 
                                       role="table" 
                                       aria-labelledby="audit-logs-title"
                                       aria-describedby="audit-table-description">
                                    <caption id="audit-table-description" class="visually-hidden">
                                        @SharedLocalizer["AuditTableDescription"]
                                    </caption>
                                    <thead class="table-light">
                                        <tr role="row">
                                            <th scope="col" class="text-nowrap">
                                                <i class="fas fa-clock me-1" aria-hidden="true"></i>
                                                @SharedLocalizer["AuditDateTime"]
                                            </th>
                                            <th scope="col" style="width: 60%;">
                                                <i class="fas fa-info-circle me-1" aria-hidden="true"></i>
                                                @SharedLocalizer["Description"]
                                            </th>
                                            <th scope="col">
                                                <i class="fas fa-user me-1" aria-hidden="true"></i>
                                                @SharedLocalizer["PerformedBy"]
                                            </th>
                                            <th scope="col" class="visually-hidden">@SharedLocalizer["Actions"]</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        @{
                                            var auditIndex = 0;
                                        }
                                        @foreach (var auditLog in (IEnumerable<ParaHockeyApp.Models.ViewModels.AuditLogViewModel>)ViewBag.RecentAuditLogs)
                                        {
                                            <tr role="row" 
                                                class="audit-row"
                                                tabindex="0"
                                                data-href="@auditLog.LinkUrl"
                                                aria-describedby="audit-row-@auditIndex">
                                                <td class="text-nowrap">
                                                    <time datetime="@auditLog.Timestamp.ToString("yyyy-MM-ddTHH:mm:ss")">
                                                        @auditLog.TimestampFormatted
                                                    </time>
                                                </td>
                                                <td>
                                                    <span id="audit-row-@auditIndex">
                                                        @auditLog.Description
                                                    </span>
                                                </td>
                                                <td>
                                                    @if (auditLog.IsAdminAction)
                                                    {
                                                        <span class="badge bg-danger" role="img" aria-label="@SharedLocalizer["AdminAction"]">
                                                            <i class="fas fa-user-shield me-1" aria-hidden="true"></i>
                                                            @SharedLocalizer["Admin"]:
                                                        </span>
                                                        <span class="ms-1">@auditLog.AdminEmail</span>
                                                    }
                                                    else
                                                    {
                                                        <span class="badge @auditLog.SourceBadgeClass" role="img" aria-label="@SharedLocalizer["SystemAction"]">
                                                            @auditLog.PerformedBy
                                                        </span>
                                                    }
                                                </td>
                                                <td>
                                                    <a href="@auditLog.LinkUrl" 
                                                       class="btn btn-sm btn-outline-primary"
                                                       aria-label="@SharedLocalizer["ViewAuditDetails", auditLog.Description]">
                                                        <i class="fas fa-eye" aria-hidden="true"></i>
                                                        <span class="visually-hidden">@SharedLocalizer["ViewDetails"]</span>
                                                    </a>
                                                </td>
                                            </tr>
                                            auditIndex++;
                                        }
                                    </tbody>
                                </table>
                            </div>
                            <div class="text-center mt-3">
                                <a href="@Url.Action("AllAuditHistory", "Admin")" 
                                   class="btn btn-info">
                                    <i class="fas fa-history me-2" aria-hidden="true"></i>
                                    @SharedLocalizer["ViewAllAuditHistory"]
                                    <i class="fas fa-arrow-right ms-2" aria-hidden="true"></i>
                                </a>
                            </div>
                        }
                        else
                        {
                            <div class="text-center text-muted py-5" role="status" aria-live="polite">
                                <i class="fas fa-history fa-3x mb-3 opacity-50" aria-hidden="true"></i>
                                <h3 class="h5">@SharedLocalizer["NoRecentActivity"]</h3>
                                <p class="mb-0">@SharedLocalizer["NoAuditActivityDescription"]</p>
                            </div>
                        }
                    </div>
                </article>
            </div>
        </section>

        <!-- Recent Members -->
        <section class="row mb-4" aria-labelledby="recent-members-title">
            <div class="col-12">
                <article class="card">
                    <header class="card-header">
                        <h2 id="recent-members-title" class="card-title mb-0 h4">
                            <i class="fas fa-user-plus" aria-hidden="true"></i> 
                            @SharedLocalizer["RecentMemberRegistrations"]
                        </h2>
                        <p class="text-muted mb-0 mt-2">@SharedLocalizer["RecentMembersDescription"]</p>
                    </header>
                    <div class="card-body">
                        @if (ViewBag.RecentMembers != null && ((IEnumerable<dynamic>)ViewBag.RecentMembers).Any())
                        {
                            <div class="table-responsive">
                                <table class="table table-hover table-sm" 
                                       role="table" 
                                       aria-labelledby="recent-members-title"
                                       aria-describedby="members-table-description">
                                    <caption id="members-table-description" class="visually-hidden">
                                        @SharedLocalizer["MembersTableDescription"]
                                    </caption>
                                    <thead class="table-light">
                                        <tr role="row">
                                            <th scope="col">
                                                <i class="fas fa-hashtag me-1" aria-hidden="true"></i>
                                                @SharedLocalizer["MemberID"]
                                            </th>
                                            <th scope="col">
                                                <i class="fas fa-user me-1" aria-hidden="true"></i>
                                                @SharedLocalizer["FullName"]
                                            </th>
                                            <th scope="col">
                                                <i class="fas fa-envelope me-1" aria-hidden="true"></i>
                                                @SharedLocalizer["Email"]
                                            </th>
                                            <th scope="col">
                                                <i class="fas fa-calendar me-1" aria-hidden="true"></i>
                                                @SharedLocalizer["RegistrationDate"]
                                            </th>
                                            <th scope="col" class="visually-hidden">@SharedLocalizer["Actions"]</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        @foreach (dynamic member in ViewBag.RecentMembers)
                                        {
                                            <tr role="row" 
                                                class="member-row"
                                                tabindex="0"
                                                data-href="@Url.Action("MemberDetails", "Admin", new { id = member.Id })"
                                                aria-describedby="<EMAIL>">
                                                <td>
                                                    <span class="badge bg-secondary">#@member.Id</span>
                                                </td>
                                                <td>
                                                    <strong id="<EMAIL>">
                                                        @member.FirstName @member.LastName
                                                    </strong>
                                                </td>
                                                <td>
                                                    <a href="mailto:@member.Email" 
                                                       class="text-decoration-none"
                                                       aria-label="@SharedLocalizer["SendEmailTo", member.FirstName + " " + member.LastName]">
                                                        @member.Email
                                                    </a>
                                                </td>
                                                <td>
                                                    <time datetime="@member.DateCreated.ToString("yyyy-MM-ddTHH:mm:ss")" 
                                                          title="@member.DateCreated.ToString("F")">
                                                        @member.DateCreated.ToString("yyyy-MM-dd HH:mm")
                                                    </time>
                                                </td>
                                                <td>
                                                    <a href="@Url.Action("MemberDetails", "Admin", new { id = member.Id })" 
                                                       class="btn btn-sm btn-outline-primary"
                                                       aria-label="@SharedLocalizer["ViewMemberDetails", member.FirstName + " " + member.LastName]">
                                                        <i class="fas fa-eye" aria-hidden="true"></i>
                                                        <span class="visually-hidden">@SharedLocalizer["ViewDetails"]</span>
                                                    </a>
                                                </td>
                                            </tr>
                                        }
                                    </tbody>
                                </table>
                            </div>
                            <div class="text-center mt-3">
                                <a href="@Url.Action("Members", "Admin")" 
                                   class="btn btn-primary">
                                    <i class="fas fa-users me-2" aria-hidden="true"></i>
                                    @SharedLocalizer["ViewAllMembers"]
                                    <i class="fas fa-arrow-right ms-2" aria-hidden="true"></i>
                                </a>
                            </div>
                        }
                        else
                        {
                            <div class="text-center text-muted py-5" role="status" aria-live="polite">
                                <i class="fas fa-user-plus fa-3x mb-3 opacity-50" aria-hidden="true"></i>
                                <h3 class="h5">@SharedLocalizer["NoMembersRegistered"]</h3>
                                <p class="mb-3">@SharedLocalizer["NoMembersDescription"]</p>
                                <a href="@Url.Action("Register", "Members")" 
                                   class="btn btn-primary">
                                    <i class="fas fa-user-plus me-2" aria-hidden="true"></i>
                                    @SharedLocalizer["RegisterFirstMember"]
                                </a>
                            </div>
                        }
                    </div>
                </article>
            </div>
        </section>
    </div>
</main>

<!-- JavaScript for accessibility enhancements -->
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Make audit and member table rows clickable with keyboard support
    const clickableRows = document.querySelectorAll('.audit-row, .member-row');
    
    clickableRows.forEach(row => {
        row.addEventListener('click', function() {
            const href = this.dataset.href;
            if (href) {
                window.location.href = href;
            }
        });
        
        row.addEventListener('keydown', function(e) {
            if (e.key === 'Enter' || e.key === ' ') {
                e.preventDefault();
                const href = this.dataset.href;
                if (href) {
                    window.location.href = href;
                }
            }
        });
    });
    
    // Announce dashboard load to screen readers
    const statusRegion = document.getElementById('dashboard-status');
    if (statusRegion) {
        statusRegion.textContent = '@SharedLocalizer["DashboardLoaded"]';
        setTimeout(() => {
            statusRegion.textContent = '';
        }, 2000);
    }
});
</script>