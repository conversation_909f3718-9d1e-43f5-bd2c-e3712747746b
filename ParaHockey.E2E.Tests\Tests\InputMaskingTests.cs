using Xunit;
using FluentAssertions;
using ParaHockey.E2E.Tests.Infrastructure;
using ParaHockey.E2E.Tests.PageObjects;
using OpenQA.Selenium;

namespace ParaHockey.E2E.Tests.Tests
{
    public class InputMaskingTests : BaseTest, IDisposable
    {
        private readonly RegistrationPage _registrationPage;

        public InputMaskingTests()
        {
            _registrationPage = new RegistrationPage(Driver, Wait);
        }

        [Fact]
        public void PostalCode_ShouldPreventInvalidCharacters()
        {
            try
            {
                // Arrange
                NavigateToRegistrationPage();
                var postalField = Driver.FindElement(By.Name("PostalCode"));

                // Act - Try special characters
                postalField.SendKeys("!@#$%^");
                Thread.Sleep(500);

                // Assert - Field should be empty
                postalField.GetAttribute("value").Should().BeEmpty("Special characters should be blocked");

                // Act - Try valid pattern
                postalField.SendKeys("H3B2Y5");
                Thread.Sleep(500);

                // Assert - Should be formatted
                postalField.GetAttribute("value").Should().Be("H3B 2Y5", "Valid postal code should be formatted");
            }
            catch (Exception ex)
            {
                TakeScreenshot("PostalCode_PreventInvalidCharacters");
                throw;
            }
        }

        [Fact]
        public void PostalCode_ShouldLimitLength()
        {
            try
            {
                // Arrange
                NavigateToRegistrationPage();
                var postalField = Driver.FindElement(By.Name("PostalCode"));

                // Act - Try to type more than 6 characters
                postalField.SendKeys("H3B2Y5EXTRA");
                Thread.Sleep(500);

                // Assert - Should only accept 6 characters
                postalField.GetAttribute("value").Should().Be("H3B 2Y5", "Should limit to 6 characters plus space");
            }
            catch (Exception ex)
            {
                TakeScreenshot("PostalCode_LimitLength");
                throw;
            }
        }

        [Fact]
        public void PhoneNumber_ShouldAutoFormatWhileTyping()
        {
            try
            {
                // Arrange
                NavigateToRegistrationPage();
                var phoneField = Driver.FindElement(By.Name("Phone"));

                // Act & Assert - Type one digit at a time
                phoneField.SendKeys("5");
                Thread.Sleep(200);
                phoneField.GetAttribute("value").Should().Be("(5", "Should add opening parenthesis");

                phoneField.SendKeys("1");
                Thread.Sleep(200);
                phoneField.GetAttribute("value").Should().Be("(51", "Should continue inside parenthesis");

                phoneField.SendKeys("4");
                Thread.Sleep(200);
                phoneField.GetAttribute("value").Should().Be("(514", "Should complete area code");

                phoneField.SendKeys("1");
                Thread.Sleep(200);
                phoneField.GetAttribute("value").Should().Be("(514) 1", "Should close parenthesis and add space");

                phoneField.SendKeys("234");
                Thread.Sleep(200);
                phoneField.GetAttribute("value").Should().Be("(514) 123-4", "Should add dash after first 3 digits");

                phoneField.SendKeys("567");
                Thread.Sleep(200);
                phoneField.GetAttribute("value").Should().Be("(*************", "Should complete phone number");
            }
            catch (Exception ex)
            {
                TakeScreenshot("PhoneNumber_AutoFormatWhileTyping");
                throw;
            }
        }

        [Fact]
        public void PhoneNumber_ShouldHandleBackspace()
        {
            try
            {
                // Arrange
                NavigateToRegistrationPage();
                var phoneField = Driver.FindElement(By.Name("Phone"));

                // Act - Type full number
                phoneField.SendKeys("5141234567");
                Thread.Sleep(500);
                phoneField.GetAttribute("value").Should().Be("(*************");

                // Act - Delete last digit
                phoneField.SendKeys(Keys.Backspace);
                Thread.Sleep(200);
                phoneField.GetAttribute("value").Should().Be("(514) 123-456");

                // Continue deleting
                phoneField.SendKeys(Keys.Backspace + Keys.Backspace + Keys.Backspace);
                Thread.Sleep(200);
                phoneField.GetAttribute("value").Should().Be("(514) 123");

                // Delete past dash
                phoneField.SendKeys(Keys.Backspace + Keys.Backspace + Keys.Backspace);
                Thread.Sleep(200);
                phoneField.GetAttribute("value").Should().Be("(514)");

                // Delete area code
                phoneField.SendKeys(Keys.Backspace + Keys.Backspace + Keys.Backspace);
                Thread.Sleep(200);
                phoneField.GetAttribute("value").Should().Be("(5");
            }
            catch (Exception ex)
            {
                TakeScreenshot("PhoneNumber_HandleBackspace");
                throw;
            }
        }

        [Fact]
        public void PostalCode_ShouldHandleBackspace()
        {
            try
            {
                // Arrange
                NavigateToRegistrationPage();
                var postalField = Driver.FindElement(By.Name("PostalCode"));

                // Act - Type full postal code
                postalField.SendKeys("H3B2Y5");
                Thread.Sleep(500);
                postalField.GetAttribute("value").Should().Be("H3B 2Y5");

                // Act - Delete last character
                postalField.SendKeys(Keys.Backspace);
                Thread.Sleep(200);
                postalField.GetAttribute("value").Should().Be("H3B 2Y");

                // Continue deleting
                postalField.SendKeys(Keys.Backspace + Keys.Backspace);
                Thread.Sleep(200);
                postalField.GetAttribute("value").Should().Be("H3B");

                // Delete to empty
                postalField.SendKeys(Keys.Backspace + Keys.Backspace + Keys.Backspace);
                Thread.Sleep(200);
                postalField.GetAttribute("value").Should().BeEmpty();
            }
            catch (Exception ex)
            {
                TakeScreenshot("PostalCode_HandleBackspace");
                throw;
            }
        }

        [Fact]
        public void PhoneNumber_ShouldHandlePaste()
        {
            try
            {
                // Arrange
                NavigateToRegistrationPage();
                var phoneField = Driver.FindElement(By.Name("Phone"));

                // Act - Simulate paste using JavaScript
                ((IJavaScriptExecutor)Driver).ExecuteScript(
                    "arguments[0].value = '5141234567'; arguments[0].dispatchEvent(new Event('input'));", 
                    phoneField);
                Thread.Sleep(500);

                // Assert
                phoneField.GetAttribute("value").Should().Be("(*************", "Should format pasted phone number");

                // Test paste with formatting
                phoneField.Clear();
                ((IJavaScriptExecutor)Driver).ExecuteScript(
                    "arguments[0].value = '************'; arguments[0].dispatchEvent(new Event('input'));", 
                    phoneField);
                Thread.Sleep(500);

                phoneField.GetAttribute("value").Should().Be("(*************", "Should normalize pasted formatted number");
            }
            catch (Exception ex)
            {
                TakeScreenshot("PhoneNumber_HandlePaste");
                throw;
            }
        }

        [Fact]
        public void PostalCode_ShouldHandlePaste()
        {
            try
            {
                // Arrange
                NavigateToRegistrationPage();
                var postalField = Driver.FindElement(By.Name("PostalCode"));

                // Act - Paste lowercase without space
                ((IJavaScriptExecutor)Driver).ExecuteScript(
                    "arguments[0].value = 'h3b2y5'; arguments[0].dispatchEvent(new Event('input'));", 
                    postalField);
                Thread.Sleep(500);

                // Assert
                postalField.GetAttribute("value").Should().Be("H3B 2Y5", "Should uppercase and format pasted postal code");

                // Test paste with space
                postalField.Clear();
                ((IJavaScriptExecutor)Driver).ExecuteScript(
                    "arguments[0].value = 'H3B 2Y5'; arguments[0].dispatchEvent(new Event('input'));", 
                    postalField);
                Thread.Sleep(500);

                postalField.GetAttribute("value").Should().Be("H3B 2Y5", "Should accept properly formatted paste");
            }
            catch (Exception ex)
            {
                TakeScreenshot("PostalCode_HandlePaste");
                throw;
            }
        }

        [Fact]
        public void EmergencyContactPhone_ShouldHaveSameMasking()
        {
            try
            {
                // Arrange
                NavigateToRegistrationPage();
                _registrationPage.SelectRegistrationType("junior");
                Thread.Sleep(1000);

                var emergencyPhoneField = Driver.FindElement(By.Id("EmergencyContact_Phone"));

                // Act
                emergencyPhoneField.SendKeys("5141234567");
                Thread.Sleep(500);

                // Assert
                emergencyPhoneField.GetAttribute("value").Should().Be("(*************", 
                    "Emergency contact phone should have same masking as main phone");
            }
            catch (Exception ex)
            {
                TakeScreenshot("EmergencyContactPhone_SameMasking");
                throw;
            }
        }

        [Fact]
        public void ParentPhone_ShouldHaveSameMasking()
        {
            try
            {
                // Arrange
                NavigateToRegistrationPage();
                _registrationPage.FillBasicInformation("Child", "Test", TestData.MinorBirthDate, "male");
                Thread.Sleep(1000);

                var parentPhoneField = Driver.FindElement(By.Id("Parents_0__Phone"));

                // Act
                parentPhoneField.SendKeys("4501234567");
                Thread.Sleep(500);

                // Assert
                parentPhoneField.GetAttribute("value").Should().Be("(*************", 
                    "Parent phone should have same masking as main phone");
            }
            catch (Exception ex)
            {
                TakeScreenshot("ParentPhone_SameMasking");
                throw;
            }
        }

        [Fact]
        public void DateField_ShouldNotAcceptLetters()
        {
            try
            {
                // Arrange
                NavigateToRegistrationPage();
                var dateField = Driver.FindElement(By.Name("DateOfBirth"));

                // Act - Try to type letters
                dateField.SendKeys("abcdefgh");
                Thread.Sleep(500);

                // Assert - Should either be empty or show placeholder
                var value = dateField.GetAttribute("value");
                value.Should().NotContain("a", "Should not accept letters");
                value.Should().NotContain("b", "Should not accept letters");
            }
            catch (Exception ex)
            {
                TakeScreenshot("DateField_NotAcceptLetters");
                throw;
            }
        }

        [Fact]
        public void PhoneNumber_ShouldRejectInternationalFormats()
        {
            try
            {
                // Arrange
                NavigateToRegistrationPage();
                var phoneField = Driver.FindElement(By.Name("Phone"));

                // Act - Try international format
                phoneField.SendKeys("+15141234567");
                phoneField.SendKeys(Keys.Tab);
                Thread.Sleep(1000);

                // Assert
                var classes = phoneField.GetAttribute("class");
                classes.Should().Contain("is-invalid", "International format should be invalid");

                // Try with country code without +
                phoneField.Clear();
                phoneField.SendKeys("15141234567");
                phoneField.SendKeys(Keys.Tab);
                Thread.Sleep(1000);

                // Should either format as 11 digits or show invalid
                var value = phoneField.GetAttribute("value");
                if (value.Length == 14) // Formatted
                {
                    value.Should().Be("(*************", "Should format 11 digits but may be invalid");
                }
                else
                {
                    classes = phoneField.GetAttribute("class");
                    classes.Should().Contain("is-invalid", "11 digit number should be invalid");
                }
            }
            catch (Exception ex)
            {
                TakeScreenshot("PhoneNumber_RejectInternationalFormats");
                throw;
            }
        }

        [Fact]
        public void MaskingPerformance_ShouldHandleRapidInput()
        {
            try
            {
                // Arrange
                NavigateToRegistrationPage();
                var phoneField = Driver.FindElement(By.Name("Phone"));
                var postalField = Driver.FindElement(By.Name("PostalCode"));

                // Act - Rapid phone input
                var startTime = DateTime.Now;
                phoneField.SendKeys("5141234567890123456"); // Extra digits to test limiting
                var phoneTime = DateTime.Now - startTime;

                Thread.Sleep(500);
                phoneField.GetAttribute("value").Should().Be("(*************", "Should limit to 10 digits");

                // Act - Rapid postal input
                startTime = DateTime.Now;
                postalField.SendKeys("H3B2Y5ABCDEF"); // Extra chars to test limiting
                var postalTime = DateTime.Now - startTime;

                Thread.Sleep(500);
                postalField.GetAttribute("value").Should().Be("H3B 2Y5", "Should limit to 6 characters");

                // Assert - Performance
                phoneTime.TotalMilliseconds.Should().BeLessThan(2000, "Phone masking should be responsive");
                postalTime.TotalMilliseconds.Should().BeLessThan(2000, "Postal masking should be responsive");
            }
            catch (Exception ex)
            {
                TakeScreenshot("MaskingPerformance_RapidInput");
                throw;
            }
        }

        public new void Dispose()
        {
            base.Dispose();
        }
    }
}