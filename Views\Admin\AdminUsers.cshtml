@{
    ViewData["Title"] = Localizer["AdminUsers"];
}

<div class="container mt-4">
    <div class="row">
        <div class="col-12">
            <h1>
                <i class="fas fa-users-cog"></i> @SharedLocalizer["AdminUsers"]
            </h1>
            
            @if (TempData["SuccessMessage"] != null)
            {
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    <i class="fas fa-check-circle"></i> @TempData["SuccessMessage"]
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            }
            
            @if (TempData["ErrorMessage"] != null)
            {
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    <i class="fas fa-exclamation-triangle"></i> @TempData["ErrorMessage"]
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            }

            <!-- Current Admins -->
            <div class="card mt-4">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-shield-alt"></i> @SharedLocalizer["CurrentAdministrators"]
                    </h5>
                </div>
                <div class="card-body">
                    @if (ViewBag.Admins != null && ((List<ParaHockeyApp.Models.Entities.AdminUser>)ViewBag.Admins).Any())
                    {
                        <div class="table-responsive">
                            <table class="table table-striped">
                                <thead>
                                    <tr>
                                        <th>@SharedLocalizer["Name"]</th>
                                        <th>@SharedLocalizer["Email"]</th>
                                        <th>@SharedLocalizer["Role"]</th>
                                        <th>@SharedLocalizer["Added"]</th>
                                        <th>@SharedLocalizer["Actions"]</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach (var admin in (List<ParaHockeyApp.Models.Entities.AdminUser>)ViewBag.Admins)
                                    {
                                        <tr>
                                            <td>@admin.Name</td>
                                            <td>@admin.Email</td>
                                            <td>
                                                @if (admin.AdminType == ParaHockeyApp.Models.Entities.AdminType.Master)
                                                {
                                                    <span class="badge bg-danger">
                                                        <i class="fas fa-crown"></i> @SharedLocalizer["MasterAdmin"]
                                                    </span>
                                                }
                                                else if (admin.AdminType == ParaHockeyApp.Models.Entities.AdminType.Normal)
                                                {
                                                    <span class="badge bg-primary">
                                                        <i class="fas fa-user-shield"></i> @SharedLocalizer["NormalAdmin"]
                                                    </span>
                                                }
                                                else
                                                {
                                                    <span class="badge bg-secondary">
                                                        <i class="fas fa-ban"></i> @SharedLocalizer["Disabled"]
                                                    </span>
                                                }
                                            </td>
                                            <td>@admin.DateCreated.ToString("MMM dd, yyyy")</td>
                                            <td>
                                                @if (admin.AdminType != ParaHockeyApp.Models.Entities.AdminType.Master)
                                                {
                                                    <form method="post" asp-action="RemoveAdmin" style="display: inline;">
                                                        @Html.AntiForgeryToken()
                                                        <input type="hidden" name="id" value="@admin.Id" />
                                                        <button type="submit" class="btn btn-sm btn-outline-danger" 
                                                                onclick="return confirm('@SharedLocalizer["AreYouSureRemoveAdmin"]')">
                                                            <i class="fas fa-trash"></i> @SharedLocalizer["Remove"]
                                                        </button>
                                                    </form>
                                                }
                                                else
                                                {
                                                    <span class="text-muted">@SharedLocalizer["CannotRemove"]</span>
                                                }
                                            </td>
                                        </tr>
                                    }
                                </tbody>
                            </table>
                        </div>
                    }
                    else
                    {
                        var emptyState = ViewBag.EmptyState as ParaHockeyApp.Models.ViewModels.EmptyStateViewModel;
                        @if (emptyState != null)
                        {
                            <div class="text-center text-muted py-4">
                                <i class="@emptyState.IconClass fa-2x mb-3"></i>
                                <h6>@emptyState.Title</h6>
                                <p>@emptyState.Message</p>
                            </div>
                        }
                        else
                        {
                            <p class="text-muted">@SharedLocalizer["NoAdministratorsFound"]</p>
                        }
                    }
                </div>
            </div>

            <!-- Add New Admin -->
            <div class="card mt-4">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-user-plus"></i> @SharedLocalizer["AddNewAdministrator"]
                    </h5>
                </div>
                <div class="card-body">
                    <form method="post" asp-action="AddAdmin">
                        @Html.AntiForgeryToken()
                        <div class="row">
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label for="email">@SharedLocalizer["EmailAddress"]</label>
                                    <input type="email" class="form-control" name="email" id="email" required 
                                           placeholder="<EMAIL>" autocomplete="email" />
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label for="name">@SharedLocalizer["FullName"]</label>
                                    <input type="text" class="form-control" name="name" id="name" required 
                                           placeholder="John Doe" autocomplete="name" />
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label>&nbsp;</label>
                                    <button type="submit" class="btn btn-primary d-block">
                                        <i class="fas fa-plus"></i> @SharedLocalizer["AddAdmin"]
                                    </button>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Current User Info -->
            <div class="card mt-4">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-user"></i> @SharedLocalizer["YourAccount"]
                    </h5>
                </div>
                <div class="card-body">
                    <p><strong>@SharedLocalizer["LoggedInAs"]</strong> @ViewBag.CurrentUser</p>
                    <p><strong>@SharedLocalizer["Authentication"]</strong> @SharedLocalizer["MicrosoftAzureAD"]</p>
                </div>
            </div>

            <div class="mt-4">
                <a asp-controller="Admin" asp-action="Index" class="btn btn-secondary">
                    <i class="fas fa-arrow-left"></i> @SharedLocalizer["BackToDashboard"]
                </a>
            </div>
        </div>
    </div>
</div>