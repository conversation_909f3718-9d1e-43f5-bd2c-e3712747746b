﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace ParaHockeyApp.Migrations
{
    /// <inheritdoc />
    public partial class FixCampAndSerieEventCategories : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            // Add Camp and Serie categories with correct IDs
            migrationBuilder.Sql(@"
                -- Add Camp category with ID 11
                IF NOT EXISTS (SELECT 1 FROM EventCategories WHERE DisplayNameKey = 'EventCategory_Camp')
                BEGIN
                    SET IDENTITY_INSERT EventCategories ON
                    INSERT INTO EventCategories (Id, DisplayNameKey, DescriptionKey, Color, IconClass, DisplayOrder, RequiresRegistration, MaxParticipants, DateCreated, IsActive, CreatedBySource)
                    VALUES (11, 'EventCategory_Camp', 'EventCategory_Camp_Desc', '#20c997', 'fas fa-campground', 11, 1, -1, GETUTCDATE(), 1, 0)
                    SET IDENTITY_INSERT EventCategories OFF
                    PRINT 'Camp category added with ID 11'
                END

                -- Add Série category with ID 12
                IF NOT EXISTS (SELECT 1 FROM EventCategories WHERE DisplayNameKey = 'EventCategory_Serie')
                BEGIN
                    SET IDENTITY_INSERT EventCategories ON
                    INSERT INTO EventCategories (Id, DisplayNameKey, DescriptionKey, Color, IconClass, DisplayOrder, RequiresRegistration, MaxParticipants, DateCreated, IsActive, CreatedBySource)
                    VALUES (12, 'EventCategory_Serie', 'EventCategory_Serie_Desc', '#6610f2', 'fas fa-stream', 12, 0, -1, GETUTCDATE(), 1, 0)
                    SET IDENTITY_INSERT EventCategories OFF
                    PRINT 'Serie category added with ID 12'
                END

                -- Update existing events with 'Tentatif' in title to use Tentative category
                UPDATE Events 
                SET EventCategoryId = (SELECT Id FROM EventCategories WHERE DisplayNameKey = 'EventCategory_Tentative')
                WHERE (Title LIKE '%Tentatif%' OR Title LIKE '%tentatif%')
                  AND EventCategoryId = (SELECT Id FROM EventCategories WHERE DisplayNameKey = 'EventCategory_Other')

                -- Update existing events with 'Défi Sportif' in title to use Tournament category
                UPDATE Events 
                SET EventCategoryId = (SELECT Id FROM EventCategories WHERE DisplayNameKey = 'EventCategory_Tournament')
                WHERE (Title LIKE '%Défi Sportif%' OR Title LIKE '%défi sportif%')
                  AND EventCategoryId = (SELECT Id FROM EventCategories WHERE DisplayNameKey = 'EventCategory_Other')
            ");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.Sql(@"
                -- Remove Camp and Serie categories
                DELETE FROM EventCategories WHERE DisplayNameKey IN ('EventCategory_Camp', 'EventCategory_Serie')
            ");
        }
    }
}
