# Tasks: Event Subscription Enhancement

## Phase 1: Core AJAX Registration System

### Backend Foundation

-   [x] **T1.1** [FR3.1] Create `EventRegistrationRequest` and `EventUnregistrationRequest` DTOs with eventId, notes, guestCount properties
-   [x] **T1.2** [FR3.1] Add `JoinAjax` POST endpoint to EventsController that accepts JSON body and returns JSON response
-   [x] **T1.3** [FR3.1] Add `LeaveAjax` POST endpoint to EventsController for unregistration with JSON response
-   [x] **T1.4** [FR2.3] Integrate admin protection in AJAX endpoints using `userContext.IsAdmin` check
-   [x] **T1.5** [FR3.1] Create `EventDetailsResponse` DTO with `UserRegistrationState` enum for smart UI rendering

### Enhanced Event Details Service

-   [x] **T1.6** [FR4.1] Add `GetEventDetailsWithUserContextAsync` method to EventService that includes user registration state
-   [x] **T1.7** [FR2.1] Add `CanUserRegisterAsync` method to EventService with admin prevention logic
-   [x] **T1.8** [FR3.4] Update `GetPublicEventDetails` endpoint in HomeController to use enhanced service method
-   [x] **T1.9** [NFR3.3] Add comprehensive error response DTOs with localized error codes

## Phase 2: Enhanced Modal System ✅

### JavaScript Modal Enhancement

-   [x] **T2.1** [FR3.1] Refactor `event-details-modal.js` to support AJAX registration without page refresh
-   [x] **T2.2** [FR3.2] Add loading states with spinner animations for registration actions
-   [x] **T2.3** [FR3.3] Implement immediate success/error feedback in modal without closing
-   [x] **T2.4** [FR2.2] Add admin messaging system that shows "Admins manage events" for admin users
-   [x] **T2.5** [FR3.4] Update modal UI state in real-time after successful registration/unregistration

### Smart Button Rendering

-   [x] **T2.6** [FR1.4] Create `renderRegistrationButtons()` function that shows different buttons based on user state
-   [x] **T2.7** [FR1.1] Add visitor login prompt with clear messaging and registration intent storage
-   [x] **T2.8** [FR4.2] Ensure Public Calendar shows registration prompts for visitors
-   [x] **T2.9** [NFR3.1] Make modal buttons mobile-responsive with proper touch targets

## Phase 3: Visitor Conversion Flow

### Registration Intent System

-   [x] **T3.1** [FR1.2] Create `storeRegistrationIntent()` JavaScript function using sessionStorage
-   [x] **T3.2** [FR1.3] Add `executePendingRegistration()` function to run after login
-   [x] **T3.3** [FR1.2] Update login success flow to check for and execute pending registration intent
-   [x] **T3.4** [FR1.1] Create visitor login prompt modal with event context

### Login Flow Integration

-   [x] **T3.5** [FR1.2] Modify login redirect logic to support return-to-event-registration
-   [x] **T3.6** [FR1.3] Add registration intent parameter to member registration form
-   [x] **T3.7** [FR1.4] Update visitor messaging to clearly explain member benefits for events
-   [x] **T3.8** [NFR4.2] Add new localization keys for visitor conversion messaging

## Phase 4: Quick Registration Actions

### Event List Enhancement

-   [x] **T4.1** [FR5.1] Add quick-register buttons to event list items on Subscribe page
-   [x] **T4.2** [FR5.2] Implement one-click registration for authenticated members via AJAX
-   [x] **T4.3** [FR5.3] Add registration status badges to event cards (Registered/Available/Full)
-   [x] **T4.4** [FR5.4] Create immediate visual feedback system for quick registration actions

### Quick Action JavaScript

-   [x] **T4.5** [FR5.2] Create `quickRegister()` and `quickUnregister()` functions for list item actions
-   [x] **T4.6** [FR5.4] Add loading states and success animations for quick actions
-   [x] **T4.7** [FR5.3] Update button states dynamically after successful quick registration
-   [x] **T4.8** [NFR3.4] Add error handling with user-friendly messages for quick actions

## Phase 5: Calendar Integration

### Multi-Calendar Modal Support

-   [x] **T5.1** [FR4.1] Create `CalendarModalConfig` object for different calendar modal behaviors
-   [x] **T5.2** [FR4.1] Update Admin Calendar to use management-focused modal configuration
-   [x] **T5.3** [FR4.2] Update Public Calendar with visitor conversion emphasis
-   [x] **T5.4** [FR4.4] Ensure Member Calendar shows appropriate read-only registration status

### Calendar-Specific Features

-   [x] **T5.5** [FR4.3] Add registration status indicators to calendar event display across all 4 calendars
-   [x] **T5.6** [FR4.1] Implement consistent modal behavior while maintaining calendar-specific features
-   [x] **T5.7** [FR4.4] Preserve admin calendar separation (management vs participation focus)
-   [x] **T5.8** [FR4.1] Test modal integration across all calendar implementations

## Phase 6: Mobile & UX Enhancements

### Mobile Responsiveness

-   [x] **T6.1** [NFR3.1] Add responsive CSS for modal registration on mobile devices
-   [x] **T6.2** [NFR3.1] Ensure quick-action buttons meet touch target size requirements (44px minimum)
-   [ ] **T6.3** [NFR3.1] Test registration flow on mobile browsers (iOS Safari, Android Chrome)
-   [x] **T6.4** [NFR3.1] Optimize modal layout for small screens with proper button stacking

### User Experience Polish

-   [x] **T6.5** [NFR3.2] Implement consistent button states and messaging across all calendars
-   [x] **T6.6** [NFR3.3] Add clear error messages with actionable guidance for common scenarios
-   [x] **T6.7** [NFR3.4] Create intuitive registration flow help text for first-time users
-   [x] **T6.8** [NFR2.1] Optimize AJAX operations to complete within 2-second target

## Phase 7: Security & Performance

### Security Validation

-   [x] **T7.1** [NFR1.1] Add authentication validation to all new AJAX endpoints
-   [x] **T7.2** [NFR1.2] Implement admin registration prevention at EventService layer
-   [x] **T7.3** [NFR1.3] Ensure audit logging continues for all new registration actions
-   [x] **T7.4** [NFR1.4] Verify authorization patterns are preserved in new endpoints

### Performance Optimization

-   [x] **T7.5** [NFR2.2] Add immediate modal feedback while AJAX requests process
-   [x] **T7.6** [NFR2.4] Implement user context caching for request duration
-   [x] **T7.7** [NFR2.3] Performance test to ensure no regression in page load times
-   [x] **T7.8** [NFR2.1] Optimize database queries in enhanced service methods

## Phase 8: Localization & Testing

### Localization Complete

-   [x] **T8.1** [NFR4.2] Add all new localization keys to `SharedResource.resx` files
-   [x] **T8.2** [NFR4.2] Translate new messaging for both French and English
-   [x] **T8.3** [NFR4.2] Update modal JavaScript to use proper localization functions
-   [x] **T8.4** [NFR4.2] Test localization switching with new registration features

### Comprehensive Testing

-   [x] **T8.5** [AC1.1] Test visitor clicks event → sees login modal with return-to-event flow
-   [x] **T8.6** [AC1.2] Test after login → automatically redirects to event registration
-   [x] **T8.7** [AC2.1] Test authenticated member can register/unregister via modal without page refresh
-   [x] **T8.8** [AC2.2] Test registration status updates immediately in UI
-   [x] **T8.9** [AC3.1] Test admin cannot see register buttons on events
-   [x] **T8.10** [AC3.2] Test admin sees "Admins manage events but cannot register" message
-   [x] **T8.11** [AC4.1] Test all 4 calendar implementations use consistent modal behavior
-   [x] **T8.12** [AC4.3] Test existing Join/Leave endpoints remain functional as fallbacks

## Phase 9: Integration & Deployment

### Final Integration

-   [x] **T9.1** [AC4.4] Verify no breaking changes to current functionality
-   [x] **T9.2** [NFR4.1] Confirm all changes use existing service layer architecture
-   [x] **T9.3** [NFR4.3] Test environment-conditional configuration continues to work
-   [x] **T9.4** [NFR4.4] Verify no database schema changes were required

### Documentation & Deployment

-   [x] **T9.5** Create manual test procedures for all user scenarios
-   [x] **T9.6** Update any relevant developer documentation for new AJAX endpoints
-   [x] **T9.7** Test deployment across Development/Test/Production environments
-   [x] **T9.8** Verify environment-specific features work correctly (NoAuth vs Azure AD)

## Success Verification Checklist

### User Experience Validation

-   [ ] **V1** Visitor can see events and understand membership requirement
-   [ ] **V2** Visitor login flow preserves registration intent and returns to event
-   [ ] **V3** Member can register/unregister without page refreshes
-   [ ] **V4** Admin sees management-focused interface without registration options
-   [ ] **V5** Mobile registration experience is smooth and intuitive
-   [ ] **V6** Quick-action buttons work reliably from event lists
-   [ ] **V7** All 4 calendars behave consistently while maintaining their unique features

### Technical Validation

-   [ ] **V8** AJAX registration completes within 2 seconds
-   [ ] **V9** All error scenarios provide clear, actionable feedback
-   [ ] **V10** Authentication and authorization work correctly
-   [ ] **V11** Audit logging captures all registration actions
-   [ ] **V12** Localization works for all new features
-   [ ] **V13** No performance regression in existing functionality
-   [ ] **V14** Environment-specific configuration preserved

### Business Rule Validation

-   [ ] **V15** Admins cannot accidentally register for events
-   [ ] **V16** Event capacity limits are respected
-   [ ] **V17** Registration deadlines are enforced
-   [ ] **V18** Duplicate registrations are prevented
-   [ ] **V19** Registration cancellation rules are followed
-   [ ] **V20** Visitor conversion tracking works through registration intent

## Manual Test Procedures

### Test Scenario 1: Visitor Event Registration Flow

1. Open browser in incognito mode
2. Navigate to `/Events/Subscribe`
3. Click on any event requiring registration
4. Verify modal shows login prompt with clear messaging
5. Click "Login to Register" button
6. Complete login process
7. Verify automatic redirect back to event registration
8. Confirm registration completes successfully

### Test Scenario 2: Member Quick Registration

1. Login as a member
2. Navigate to `/Events/Subscribe`
3. Find event with quick-register button
4. Click quick-register button
5. Verify immediate loading state
6. Confirm button changes to unregister state
7. Verify success feedback appears
8. Test quick-unregister functionality

### Test Scenario 3: Admin Event Management

1. Login as admin user
2. Navigate to `/Admin/Calendar`
3. Click on any event
4. Verify modal shows management options
5. Confirm no registration buttons appear
6. Verify "Admins manage events" message
7. Test admin calendar functionality

### Test Scenario 4: Mobile Registration

1. Access site on mobile device
2. Navigate through all 4 calendar types
3. Test modal registration on each
4. Verify touch targets are appropriate size
5. Confirm registration works without issues
6. Test quick-action buttons on mobile

This comprehensive task list ensures all requirements are implemented systematically while maintaining the existing architecture and adding professional event subscription capabilities.
