using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace ParaHockeyApp.Migrations
{
    /// <inheritdoc />
    public partial class AddAdminUsersTable : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            // Defensive: Only create table if it doesn't exist
            migrationBuilder.Sql(@"
                IF NOT EXISTS (SELECT * FROM sys.tables WHERE name = 'AdminUsers')
                BEGIN
                    CREATE TABLE AdminUsers (
                        Id int IDENTITY(1,1) PRIMARY KEY,
                        Email nvarchar(255) NOT NULL,
                        Name nvarchar(100) NOT NULL,
                        IsMasterAdmin bit NOT NULL,
                        DateCreated datetime2 NOT NULL
                    )
                END");

            // Defensive: Only create index if it doesn't exist
            migrationBuilder.Sql(@"
                IF NOT EXISTS (SELECT * FROM sys.indexes WHERE object_id = OBJECT_ID('AdminUsers') AND name = 'IX_AdminUsers_Email')
                BEGIN
                    CREATE UNIQUE INDEX IX_AdminUsers_Email ON AdminUsers(Email)
                END");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "AdminUsers");
        }
    }
}