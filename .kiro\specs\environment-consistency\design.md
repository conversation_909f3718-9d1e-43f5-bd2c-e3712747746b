# Design Document
SYSTEM (role = architect & senior engineer)
You are building “DecisionDeck”, a polished cross-platform mobile app (Android + iOS) that helps groups quickly choose or randomize items (e.g., board games) with optional voting.

Core Principles
Fast, reliable, offline-first. Local peer sessions (QR / code) work with no internet. Cloud sync & remote play unlock when user has a paid plan.

Beautiful & accessible. Themed UI, adaptive dark/light, responsive up to tablets. Use item images/covers (fetch from future BGG/OMDB/etc.).

Vendor-agnostic clean architecture (feature layers, DI, testability).

**Full bilingual ( en / fr ) via lib/l10n/*.arb; default device locale; easy to add more languages.

Up to 25 players per session.

Privacy-by-design. Only store nickname + optional avatar

Extensible (tags, player counts, play­time, custom fields, ratings).

Local comms: Multipeer + QRCode / deep-link join

Key Features / User Flows
Deck Management – CRUD decks & items, import/export (CSV, JSON, share link).

Filter & Availability Toggle – Host marks which items are “in play now.”

Randomizer

Pick 1: RNG among available items (optionally weighted by weight).

Shuffle: Produce full random order list.

Pick First Player / Player Order: Separate RNG on joined player list.

Voting Session

Host selects deck subset + “max picks per player” (1-N) or ranked ballot.

Players join, pick within limit; UI shows “Waiting X/ total.”

Host ✔️ to reveal; everyone sees tally (bar chart).

Ties: Host chooses ➜ runoff vote only on ties, random resolver, or restart vote.

Offline Party-Mode – If no internet, sessions run P2P; data stored only on host until closed.

Paywall – Pro flag unlocks: cloud backup, remote invites, unlimited decks, analytics.

Settings – Language toggle, theme, export data, clear cache.

Telemetry – Firebase Crashlytics + simple anonymized usage events.

Testing – Unit, widget, integration; mock Supabase; golden tests for L10N.

Screens (wireframes)
Home → Decks list (+ new)

Deck Detail (items grid, “Select Available”)

Start Session (choose mode, vote config, first-player toggle)

Share Code / QR

Vote Screen (multiselect or rank-drag)

Progress / Waiting

Results (tally, confetti)

Runoff Prompt (ties)

Settings / Paywall

> ---
> 
> 
> You are tasked with creating a minimal plan for a non-technical person based on their brain dump of an idea. Your goal is to provide a simple, easy-to-understand plan that includes a suggested tech stack and external services required. Remember to keep everything non-technical and avoid jargon that a non-technical person wouldn't understand.
> 
> Based on this brain dump, create a plan that includes the following sections:
> 
> 1. Executive Summary: Write a single paragraph that simplifies the idea for people hearing it for the first time. Make sure they can understand it instantly without further questions.
> 
> 2. Core Features: List the main features of the product or service, categorized into sections like User Account, Subscriptions, etc. Keep descriptions brief and non-technical.
> 
> 3. Suggested Tech Stack: Provide a list of suggested technologies or tools, with non-technical descriptions of what each tool does. Focus on the essentials for a minimal viable product (MVP).
> 
> 4. External Services: List any external services that might be needed. For each service, provide 3-4 bullet points describing in non-technical terms what it is used for.
> 
> 5. Idea to Market Tips: Offer proven, MVP-focused tips for indie builders to get the idea built and validated quickly. Mention any potential challenges or "gotchas" that the user may not be aware of based on their brain dump. Include other helpful tips for non-technical AI builders.
> 
> When writing your plan, keep these guidelines in mind:
> 
> - Use simple, clear language that a non-technical person can easily understand
> - Avoid technical jargon or complex concepts
> - Focus on the minimal viable version of the idea
> - Provide enough structure for an AI agent to start building the scaffolding
> 
> Your final output should be a well-structured, easy-to-skim plan that addresses all the sections mentioned above. Present only your plan without writing code.
>
## Overview

This design establishes a comprehensive environment consistency framework for the ParaHockey application, ensuring identical behavior across Development, Test, and Production environments while maintaining appropriate visual indicators and development tools. The solution focuses on standardizing error handling, empty state management, and environment-specific configurations.

## Architecture

### Environment Configuration System

The existing `EnvironmentSettings` configuration class will be enhanced to support consistent behavior patterns:

```csharp
public class EnvironmentSettings
{
    // Existing properties...
    public bool ShowDevelopmentTools { get; set; } = false;
    public bool EnableDetailedErrorLogging { get; set; } = true;
    public string EnvironmentIndicatorColor { get; set; } = "primary";
}
```

### Configuration Structure

```
appsettings.json (Development)
├── Environment.ShowDevelopmentTools = true
├── Environment.ShowBanner = false
├── Environment.EnvironmentIndicatorColor = "info"

appsettings.Staging.json (Test)
├── Environment.ShowDevelopmentTools = true
├── Environment.ShowBanner = true
├── Environment.EnvironmentIndicatorColor = "danger"

appsettings.Production.json
├── Environment.ShowDevelopmentTools = false
├── Environment.ShowBanner = false
├── Environment.EnvironmentIndicatorColor = "primary"
```

## Components and Interfaces

### 1. Environment Indicator Component

**Location**: `Views/Shared/_EnvironmentIndicator.cshtml`

Visual indicators that clearly distinguish environments:

-   Development: Blue info bar with development tools
-   Test: Red warning indicators with "TEST" labels
-   Production: No environment indicators

### 2. Development Tools Component

**Location**: `Views/Shared/_DevelopmentTools.cshtml`

Quick access tools for non-production environments:

-   Form auto-fill buttons
-   Test data generators
-   Debug information toggles

### 3. Empty State Handler Service

**Interface**: `IEmptyStateService`
**Implementation**: `EmptyStateService`

Centralized service for managing empty state messages across all views:

```csharp
public interface IEmptyStateService
{
    string GetEmptyMessage(string entityType, string searchTerm = null);
    EmptyStateViewModel GetEmptyState(string entityType, string searchTerm = null, string actionUrl = null);
}
```

### 4. Global Error Handler Middleware

**Location**: `Middleware/GlobalErrorHandlerMiddleware.cs`

Consistent error handling across all environments:

-   Catches unhandled exceptions
-   Logs detailed information
-   Returns user-friendly messages
-   Maintains data integrity

## Data Models

### EmptyStateViewModel

```csharp
public class EmptyStateViewModel
{
    public string Title { get; set; }
    public string Message { get; set; }
    public string IconClass { get; set; }
    public string ActionText { get; set; }
    public string ActionUrl { get; set; }
    public bool ShowAction { get; set; }
}
```

### ErrorResponseModel

```csharp
public class ErrorResponseModel
{
    public string UserMessage { get; set; }
    public string ErrorId { get; set; }
    public DateTime Timestamp { get; set; }
    public bool IsRetryable { get; set; }
}
```

## Error Handling

### Standardized Error Response Pattern

All controllers will implement consistent error handling:

1. **Database Query Errors**: Return empty collections with appropriate messages
2. **Validation Errors**: Return structured validation responses
3. **System Errors**: Log details, return generic user-friendly messages
4. **Empty Results**: Display helpful empty state messages

### Error Logging Strategy

```csharp
public class StandardizedLogger
{
    public void LogError(Exception ex, string context, object additionalData = null)
    {
        // Consistent logging format across all environments
        // Environment-specific detail levels
        // Structured logging for monitoring
    }
}
```

## Testing Strategy

### Environment Parity Testing

1. **Automated Tests**: Verify identical behavior across environments
2. **Configuration Tests**: Ensure only allowed differences exist
3. **Error Handling Tests**: Validate consistent error responses
4. **Empty State Tests**: Verify proper empty state handling

### Test Data Management

Standardized test data sets that work identically across all environments:

-   Member test data
-   Registration type data
-   Event test scenarios

### Integration Testing

```csharp
[TestClass]
public class EnvironmentConsistencyTests
{
    [TestMethod]
    public void EmptyMembersList_ShouldShowConsistentMessage()
    {
        // Test that empty members list shows same message in all environments
    }

    [TestMethod]
    public void DatabaseError_ShouldReturnConsistentResponse()
    {
        // Test that database errors are handled consistently
    }
}
```

## Implementation Plan

### Phase 1: Error Handling Standardization

-   Implement global error handler middleware
-   Standardize empty state messages
-   Update all views to use consistent empty state handling

### Phase 2: Environment Indicator Enhancement

-   Update environment indicators for clear visual distinction
-   Implement development tools component
-   Configure environment-specific settings

### Phase 3: Configuration Consistency

-   Audit and standardize configuration differences
-   Implement configuration validation
-   Add environment parity checks

### Phase 4: Testing and Validation

-   Create comprehensive test suite
-   Implement automated environment parity checks
-   Add monitoring for environment-specific issues

## Security Considerations

1. **Development Tools**: Only available in non-production environments
2. **Error Information**: Detailed errors only logged, never exposed to users
3. **Configuration Validation**: Prevent accidental production deployment of dev settings
4. **Access Control**: Development tools require appropriate permissions

## Performance Considerations

1. **Error Handling**: Minimal performance impact through efficient exception handling
2. **Environment Checks**: Cached environment settings to avoid repeated configuration reads
3. **Logging**: Asynchronous logging to prevent blocking operations
4. **Empty State Queries**: Optimized queries to quickly detect empty results

## Monitoring and Observability

1. **Environment Health Checks**: Verify environment-specific configurations
2. **Error Rate Monitoring**: Track error patterns across environments
3. **Performance Metrics**: Compare response times across environments
4. **Configuration Drift Detection**: Alert on unexpected configuration differences
