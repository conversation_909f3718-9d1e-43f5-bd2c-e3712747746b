@section Scripts {
    @{
        await Html.RenderPartialAsync("_ValidationScriptsPartial");
    }
    <script src="~/js/jquery.mask.min.js"></script>
    <script>
        $(document).ready(function () {
            console.log('🟢 Registration form loaded');

            // Initialize basic field masks
            $('#postal-code-input').mask('L0L 0L0', {
                'translation': {
                    'L': { pattern: /[A-Za-z]/ },
                    '0': { pattern: /[0-9]/ }
                }
            }).on('input', function () {
                this.value = this.value.toUpperCase();
            });

            $('input[type="tel"]').mask('(*************');
            $('.datepicker-input').mask('0000-00-00');

            // Main DOB logic function
            function handleDOBChange() {
                console.log('🟢 DOB change detected');
                var dobValue = $('.datepicker-input').val();
                console.log('DOB value:', dobValue);

                // Auto-format 8 digits to YYYY-MM-DD
                if (/^\d{8}$/.test(dobValue)) {
                    dobValue = dobValue.substring(0, 4) + '-' + dobValue.substring(4, 6) + '-' + dobValue.substring(6, 8);
                    $('.datepicker-input').val(dobValue);
                    console.log('Formatted DOB:', dobValue);
                }

                if (dobValue && dobValue.length === 10) {
                    // Calculate age
                    var dateOfBirth = new Date(dobValue);
                    var today = new Date();
                    var age = today.getFullYear() - dateOfBirth.getFullYear();
                    var monthDiff = today.getMonth() - dateOfBirth.getMonth();
                    if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < dateOfBirth.getDate())) {
                        age--;
                    }
                    console.log('Age calculated:', age);

                    // Show appropriate form based on age
                    var ageOfMajority = @Model.EnvironmentSettings.AgeOfMajority;
                    if (age < ageOfMajority) {
                        console.log('Showing Parent form');
                        $('#parent-fields-section').show();
                        $('#second-parent-card').show(); // Also show second parent
                        $('#emergency-contact-section').hide();
// Enable Junior option
                        $('input[name="RegistrationTypeId"][value="1"]').prop('disabled', false);
                    } else {
                        console.log('Showing Emergency Contact form');
$('#emergency-contact-section').show();
                        $('#parent-fields-section').hide();
                        $('#second-parent-card').hide(); // Hide second parent
                        // Disable Junior option
                        var juniorOption = $('input[name="RegistrationTypeId"][value="1"]');
                        juniorOption.prop('disabled', true).prop('checked', false);
                        console.log('Junior option disabled for 18+');
                    }

                    console.log('DOB incomplete, hiding forms');
                    $('#parent-fields-section').hide();
                    $('#second-parent-card').hide();
emergency-contact-section').hide();
                }
            }

            // Bind DOB events
            $('.datepicker-input').on('input blur change keydown', function (e) {
                if (e.type === 'keydown' && e.key !== 'Enter') return;
                handleDOBChange();
            });

            // Initialize on page load
BChange();
        });
    </script>
}
       