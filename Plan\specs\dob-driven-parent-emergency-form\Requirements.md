# Requirements: DOB-Driven Parent/Emergency Form Logic

## Functional Requirements

### FR1: Dynamic Form Flow

-   FR1.1: After entering date of birth (DOB), the registration process must immediately display the Parent Form if the person is under 18 years old, regardless of membership type selection.
-   FR1.2: If the person is 18 or older, the Emergency Contact Form must be shown instead of the Parent Form, regardless of membership type selection.
-   FR1.3: The membership type selection step must occur only after the appropriate Parent or Emergency Contact form is completed.

### FR2: Membership Type Restrictions

-   FR2.1: If the person is under 18, all membership types are selectable.
-   FR2.2: If the person is 18 or older, the "Junior" membership type must be visually disabled (greyed out, non-clickable) and cannot be selected. This restriction is independent of the Parent/Emergency Contact form logic.

### FR3: UI Feedback

-   FR3.1: The transition between DOB entry and the next form must be immediate and visually clear.
-   FR3.2: Disabled membership options must have a tooltip or message explaining why they are unavailable.

## Non-Functional Requirements

### NFR1: Localization

-   NFR1.1: All new UI text, tooltips, and validation messages must be fully localized using the existing resource system.

### NFR2: Accessibility

-   NFR2.1: Disabled membership options must be accessible to screen readers and keyboard navigation.

### NFR3: Maintainability

-   NFR3.1: All age-based logic must reside in the service layer, not in controllers or views.
-   NFR3.2: No hardcoded age values; use a single configuration value for the age of majority (default: 18).

## Acceptance Criteria

-   [ ] AC1: Entering a DOB under 18 immediately shows the Parent Form, skipping membership type selection.
-   [ ] AC2: Entering a DOB 18 or older immediately shows the Emergency Contact Form, skipping membership type selection.
-   [ ] AC3: After completing the Parent/Emergency Contact form, the membership type selection is shown.
-   [ ] AC4: "Junior" membership is disabled for users 18 or older, with a localized tooltip explaining why.
-   [ ] AC5: All new UI and validation text is localized and accessible.
-   [ ] AC6: All age logic is in the service layer and uses a configurable age of majority.
