using ParaHockeyApp.Models.Entities;
using ParaHockeyApp.Services;

namespace ParaHockeyApp.ViewModels
{
    /// <summary>
    /// View model for duplicate resolution interface
    /// </summary>
    public class DuplicateResolutionViewModel
    {
        public TempMember TempMember { get; set; } = null!;
        public Member ExistingMember { get; set; } = null!;
        public Dictionary<string, FieldComparison> FieldComparisons { get; set; } = new();
        public Dictionary<string, string> FieldChoices { get; set; } = new();
        
        // UI helper properties
        public int ImportBatchId => TempMember.ImportBatchId;
        public string TempMemberDisplayName => $"{TempMember.FirstName} {TempMember.LastName}";
        public string ExistingMemberDisplayName => $"{ExistingMember.FirstName} {ExistingMember.LastName}";
        public bool HasDifferences => FieldComparisons.Values.Any(fc => !fc.AreIdentical);
        public int DifferenceCount => FieldComparisons.Values.Count(fc => !fc.AreIdentical);
        
        // Navigation properties
        public string ReturnUrl { get; set; } = string.Empty;
        public bool ShowConfirmation { get; set; } = false;
        public List<FieldChange> PreviewChanges { get; set; } = new();
    }

    /// <summary>
    /// View model for merge confirmation modal
    /// </summary>
    public class MergeConfirmationViewModel
    {
        public Guid TempMemberId { get; set; }
        public string TempMemberDisplayName { get; set; } = string.Empty;
        public string ExistingMemberDisplayName { get; set; } = string.Empty;
        public Dictionary<string, string> FieldChoices { get; set; } = new();
        public List<FieldChange> Changes { get; set; } = new();
        public string ReturnUrl { get; set; } = string.Empty;
        
        // Summary properties
        public bool HasChanges => Changes.Count > 0;
        public int ChangeCount => Changes.Count;
        public string ChangeSummary => HasChanges ? $"{ChangeCount} field(s) will be updated" : "No changes will be made";
    }

    /// <summary>
    /// View model for duplicate resolution list/queue
    /// </summary>
    public class DuplicateQueueViewModel
    {
        public int ImportBatchId { get; set; }
        public string FileName { get; set; } = string.Empty;
        public List<DuplicateQueueItem> DuplicateItems { get; set; } = new();
        public int PageNumber { get; set; } = 1;
        public int PageSize { get; set; } = 20;
        public int TotalCount { get; set; }
        public int TotalPages => (int)Math.Ceiling((double)TotalCount / PageSize);
        public bool HasPreviousPage => PageNumber > 1;
        public bool HasNextPage => PageNumber < TotalPages;
        
        // Filter properties
        public string? SearchTerm { get; set; }
        public DuplicateMatchType? MatchTypeFilter { get; set; }
        
        // Summary statistics
        public int EmailMatches => DuplicateItems.Count(d => d.MatchType == DuplicateMatchType.Email);
        public int NameDobMatches => DuplicateItems.Count(d => d.MatchType == DuplicateMatchType.NameAndDateOfBirth);
    }

    /// <summary>
    /// Individual duplicate item in the queue
    /// </summary>
    public class DuplicateQueueItem
    {
        public Guid TempMemberId { get; set; }
        public int ImportBatchId { get; set; }
        public string TempMemberName { get; set; } = string.Empty;
        public string TempMemberEmail { get; set; } = string.Empty;
        public DateTime? TempMemberDateOfBirth { get; set; }
        
        public int ExistingMemberId { get; set; }
        public string ExistingMemberName { get; set; } = string.Empty;
        public string ExistingMemberEmail { get; set; } = string.Empty;
        public DateTime ExistingMemberDateOfBirth { get; set; }
        
        public DuplicateMatchType MatchType { get; set; }
        public int DifferenceCount { get; set; }
        public DateTime CreatedAt { get; set; }
        
        // Display properties
        public string MatchTypeDisplayName => MatchType switch
        {
            DuplicateMatchType.Email => "Email Match",
            DuplicateMatchType.NameAndDateOfBirth => "Name & Date of Birth Match",
            _ => "Unknown Match"
        };
        
        public string MatchTypeIconClass => MatchType switch
        {
            DuplicateMatchType.Email => "fas fa-envelope",
            DuplicateMatchType.NameAndDateOfBirth => "fas fa-id-card", 
            _ => "fas fa-question-circle"
        };
        
        public string MatchTypeColorClass => MatchType switch
        {
            DuplicateMatchType.Email => "text-primary",
            DuplicateMatchType.NameAndDateOfBirth => "text-info",
            _ => "text-secondary"
        };
        
        public string DifferenceText => DifferenceCount switch
        {
            0 => "Identical records",
            1 => "1 difference",
            _ => $"{DifferenceCount} differences"
        };
    }

    /// <summary>
    /// Type of duplicate match found
    /// </summary>
    public enum DuplicateMatchType
    {
        Email = 1,
        NameAndDateOfBirth = 2
    }

    /// <summary>
    /// View model for bulk duplicate resolution operations
    /// </summary>
    public class BulkDuplicateResolutionViewModel
    {
        public int ImportBatchId { get; set; }
        public List<Guid> SelectedTempMemberIds { get; set; } = new();
        public BulkResolutionAction Action { get; set; }
        public string ReturnUrl { get; set; } = string.Empty;
        
        // Summary information
        public int SelectedCount => SelectedTempMemberIds.Count;
        public string ActionDisplayName => Action switch
        {
            BulkResolutionAction.AutoMergeIdentical => "Auto-merge identical records",
            BulkResolutionAction.RejectAllSelected => "Reject all selected",
            BulkResolutionAction.MarkForManualReview => "Mark for manual review",
            _ => "Unknown action"
        };
    }

    /// <summary>
    /// Bulk resolution actions available
    /// </summary>
    public enum BulkResolutionAction
    {
        AutoMergeIdentical = 1,
        RejectAllSelected = 2,
        MarkForManualReview = 3
    }
}