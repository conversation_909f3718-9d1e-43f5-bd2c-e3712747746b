# Requirements Document

## Introduction

This feature implements duplicate member detection during the registration process to prevent duplicate member accounts and provide options for handling existing members who attempt to register again. The system will check for duplicates based on email address (exact match) and a combination of last name and date of birth (partial match), providing appropriate user feedback and resolution options.

## Requirements

### Requirement 1

**User Story:** As a new member registering on the platform, I want to be notified if my email is already in use by another member, so that I can either clear the form or modify the existing member information.

#### Acceptance Criteria

1. WHEN a user submits the registration form THEN the system SHALL check if the entered email already exists in the Members table
2. WHEN an exact email match is found THEN the system SHALL display a localized message stating "This email is already in use by an existing member"
3. WHEN the duplicate email message is displayed THEN the system SHALL provide two action buttons: "OK" and "Modify Member"
4. WHEN the user clicks "OK" THEN the system SHALL dismiss the message and clear all form fields
5. WHEN the user clicks "Modify Member" THEN the system SHALL redirect to the login page with the email field pre-filled

### Requirement 2

**User Story:** As a new member registering with a different email but same personal details, I want to be asked if I'm the same person as an existing member, so that I can avoid creating a duplicate account.

#### Acceptance Criteria

1. WHEN no exact email match is found THEN the system SHALL check for members with matching last name AND date of birth
2. WHEN a partial match is found (same last name and date of birth, different email) THEN the system SHALL display a localized message asking "A member with this name and birth date already exists with email [partial email]. Is this your correct email?"
3. WHEN displaying the partial email THEN the system SHALL show only the first 2 characters, asterisks for the middle portion, and the domain (e.g., "jo\*\*\*@example.com")
4. WHEN the partial match message is displayed THEN the system SHALL provide two action buttons: "Yes" and "No"
5. WHEN the user clicks "Yes" THEN the system SHALL display the same duplicate email message and options as Requirement 1
6. WHEN the user clicks "No" THEN the system SHALL proceed with creating the new member account

### Requirement 3

**User Story:** As an admin creating a new member, I want the same duplicate detection functionality to apply, so that I don't accidentally create duplicate member accounts.

#### Acceptance Criteria

1. WHEN an admin submits a member registration form THEN the system SHALL apply the same duplicate detection logic as for regular member registration
2. WHEN duplicate detection messages are displayed to an admin THEN the system SHALL use the same localized messages and action buttons
3. WHEN an admin clicks "Modify Member" from the duplicate detection dialog THEN the system SHALL redirect to the member edit page instead of the login page

### Requirement 4

**User Story:** As a user of the system, I want all duplicate detection messages and buttons to be displayed in my preferred language, so that I can understand and interact with them properly.

#### Acceptance Criteria

1. WHEN duplicate detection messages are displayed THEN the system SHALL show all text in the user's current language (French or English)
2. WHEN the system displays the duplicate email message THEN the message SHALL be localized using the SharedLocalizer
3. WHEN the system displays the partial match message THEN the message SHALL be localized using the SharedLocalizer
4. WHEN action buttons are displayed THEN the button text SHALL be localized using the SharedLocalizer

### Requirement 5

**User Story:** As a system administrator, I want duplicate detection to only check against member records (not parent or emergency contact records), so that the system correctly identifies actual member duplicates.

#### Acceptance Criteria

1. WHEN checking for email duplicates THEN the system SHALL only query the Members table email field
2. WHEN checking for partial matches THEN the system SHALL only query the Members table for last name and date of birth combinations
3. WHEN performing duplicate checks THEN the system SHALL NOT include parent email addresses from the parent-child relationships
4. WHEN performing duplicate checks THEN the system SHALL NOT include emergency contact email addresses

### Requirement 6

**User Story:** As a user, I want the duplicate detection to work seamlessly with the existing form validation, so that I receive clear feedback without disrupting the normal registration flow.

#### Acceptance Criteria

1. WHEN duplicate detection is triggered THEN the system SHALL prevent the normal form submission process
2. WHEN no duplicates are found THEN the system SHALL proceed with the normal registration process
3. WHEN duplicate detection messages are displayed THEN the system SHALL maintain all existing form data until the user takes an action
4. WHEN the user chooses to clear the form THEN the system SHALL reset all form fields to their initial state
5. WHEN duplicate detection occurs THEN the system SHALL not interfere with existing client-side validation
