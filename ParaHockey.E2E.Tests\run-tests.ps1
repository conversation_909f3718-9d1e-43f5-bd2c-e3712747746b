# ParaHockey E2E Test Runner
# This script runs the comprehensive test suite for the development environment

param(
    [string]$TestCategory = "All",
    [string]$Browser = "Chrome",
    [switch]$Headless = $false,
    [switch]$Parallel = $false,
    [switch]$GenerateReport = $true
)

Write-Host "🚀 ParaHockey E2E Test Suite" -ForegroundColor Cyan
Write-Host "================================" -ForegroundColor Cyan

# Check if website is running
Write-Host "🔍 Checking if website is running..." -ForegroundColor Yellow
try {
    $response = Invoke-WebRequest -Uri "http://localhost:5285" -TimeoutSec 5 -UseBasicParsing -ErrorAction Stop
    Write-Host "✅ Website is running at http://localhost:5285" -ForegroundColor Green
} catch {
    Write-Host "❌ ERROR: Website is not running!" -ForegroundColor Red
    Write-Host ""
    Write-Host "🚨 BEFORE RUNNING TESTS:" -ForegroundColor Red
    Write-Host "   1. Open a new PowerShell window" -ForegroundColor Yellow
    Write-Host "   2. Navigate to: C:\Users\<USER>\OneDrive\Documents\Repos\2-Complys\Clients\ParaHockey" -ForegroundColor Yellow
    Write-Host "   3. Run: dotnet run" -ForegroundColor Yellow
    Write-Host "   4. Wait for: 'Now listening on: http://localhost:5285'" -ForegroundColor Yellow
    Write-Host "   5. Then run tests in this window" -ForegroundColor Yellow
    Write-Host ""
    exit 1
}

# Set test environment variables
$env:ASPNETCORE_ENVIRONMENT = "Development"
$env:PARAHOCKEY_TEST_BROWSER = $Browser
$env:PARAHOCKEY_TEST_HEADLESS = $Headless.ToString().ToLower()

# Create output directory
$outputDir = "TestResults"
if (!(Test-Path $outputDir)) {
    New-Item -ItemType Directory -Path $outputDir | Out-Null
}

# Create screenshots directory
$screenshotDir = "Screenshots"
if (!(Test-Path $screenshotDir)) {
    New-Item -ItemType Directory -Path $screenshotDir | Out-Null
}

Write-Host "🔧 Configuration:" -ForegroundColor Yellow
Write-Host "  Test Category: $TestCategory" -ForegroundColor Gray
Write-Host "  Browser: $Browser" -ForegroundColor Gray
Write-Host "  Headless Mode: $Headless" -ForegroundColor Gray
Write-Host "  Parallel Execution: $Parallel" -ForegroundColor Gray
Write-Host "  Generate Report: $GenerateReport" -ForegroundColor Gray
Write-Host ""

# Build test arguments
$testArgs = @()

# Add test category filter
switch ($TestCategory.ToLower()) {
    "validation" {
        $testArgs += "--filter", "FullyQualifiedName~RegistrationFormValidationTests|FullyQualifiedName~MultiLanguageValidationTests"
    }
    "workflow" {
        $testArgs += "--filter", "FullyQualifiedName~RegistrationWorkflowTests|FullyQualifiedName~FormSubmissionSuccessTests"
    }
    "localization" {
        $testArgs += "--filter", "FullyQualifiedName~LocalizationTests|FullyQualifiedName~MultiLanguageValidationTests"
    }
    "crossbrowser" {
        $testArgs += "--filter", "FullyQualifiedName~CrossBrowserTests"
    }
    "responsive" {
        $testArgs += "--filter", "FullyQualifiedName~ResponsiveDesignTests"
    }
    "performance" {
        $testArgs += "--filter", "FullyQualifiedName~PerformanceTests"
    }
    "smoke" {
        # Quick smoke tests - basic functionality only
        $testArgs += "--filter", "FullyQualifiedName~SmokeTests"
    }
    "http" {
        # HTTP-only tests (no browser required)
        $testArgs += "--filter", "FullyQualifiedName~SimpleWorkingTests"
    }
    "masking" {
        # Input masking tests
        $testArgs += "--filter", "FullyQualifiedName~InputMaskingTests"
    }
    "edge" {
        # Edge cases and error handling
        $testArgs += "--filter", "FullyQualifiedName~EdgeCaseAndErrorTests"
    }
    "critical" {
        # Critical path tests
        $testArgs += "--filter", "FullyQualifiedName~SmokeTests|FullyQualifiedName~RegistrationWorkflowTests"
    }
    "all" {
        # Run all tests
    }
    default {
        Write-Host "❌ Unknown test category: $TestCategory" -ForegroundColor Red
        Write-Host "Available categories: All, Validation, Workflow, Localization, CrossBrowser, Responsive, Performance, Smoke, HTTP, Masking, Edge, Critical" -ForegroundColor Yellow
        exit 1
    }
}

# Add parallel execution if requested
if ($Parallel) {
    $testArgs += "--parallel"
}

# Add logger for XML output
$testArgs += "--logger", "trx;LogFileName=TestResults.trx"
$testArgs += "--logger", "console;verbosity=normal"

# Add results directory
$testArgs += "--results-directory", $outputDir

Write-Host "🧪 Running Tests..." -ForegroundColor Green
Write-Host "dotnet test $($testArgs -join ' ')" -ForegroundColor Gray
Write-Host ""

# Run the tests
$stopwatch = [System.Diagnostics.Stopwatch]::StartNew()

try {
    $testResult = dotnet test @testArgs
    $exitCode = $LASTEXITCODE
} catch {
    Write-Host "❌ Test execution failed: $_" -ForegroundColor Red
    exit 1
}

$stopwatch.Stop()
$elapsed = $stopwatch.Elapsed

Write-Host ""
Write-Host "⏱️  Test execution completed in $($elapsed.ToString('mm\:ss'))" -ForegroundColor Cyan

# Parse test results
$testsPassed = $exitCode -eq 0
if ($testsPassed) {
    Write-Host "✅ All tests passed!" -ForegroundColor Green
} else {
    Write-Host "❌ Some tests failed (Exit code: $exitCode)" -ForegroundColor Red
}

# Extract test results and send to API
Write-Host ""
Write-Host "📤 Sending test results to application..." -ForegroundColor Yellow

try {
    # Parse the TRX file to get actual test results
    $trxFile = Get-ChildItem -Path $outputDir -Filter "*.trx" | Sort-Object LastWriteTime -Descending | Select-Object -First 1
    
    if ($trxFile) {
        try {
            [xml]$trxContent = Get-Content $trxFile.FullName
            $testResults = @()
            
            # Extract test results from TRX with null checks
            if ($trxContent -and $trxContent.TestRun -and $trxContent.TestRun.Results) {
                $unitTestResults = $trxContent.TestRun.Results.UnitTestResult
                if ($unitTestResults) {
                    # Handle both single result and array of results
                    if ($unitTestResults -is [array]) {
                        $resultsArray = $unitTestResults
                    } else {
                        $resultsArray = @($unitTestResults)
                    }
                    
                    foreach ($result in $resultsArray) {
                        if ($result) {
                            # Extract test name more safely
                            $testName = if ($result.testName) { 
                                $fullName = $result.testName
                                if ($fullName.Contains('.')) {
                                    $fullName.Split('.')[-1]
                                } else {
                                    $fullName
                                }
                            } else { 
                                "Unknown Test" 
                            }
                            $passed = $result.outcome -eq "Passed"
                            
                            # Safe duration parsing
                            $duration = [TimeSpan]::Zero
                            if ($result.duration) {
                                try {
                                    $duration = [TimeSpan]::Parse($result.duration)
                                } catch {
                                    Write-Warning "Could not parse duration: $($result.duration)"
                                }
                            }
                            
                            $errorMessage = $null
                            if (!$passed -and $result.Output -and $result.Output.ErrorInfo -and $result.Output.ErrorInfo.Message) {
                                $errorMessage = $result.Output.ErrorInfo.Message
                            }
                            
                            $testResults += @{
                                TestName = $testName
                                Passed = $passed
                                ErrorMessage = $errorMessage
                                Duration = $duration.ToString()
                                ExecutedAt = [DateTime]::Now.ToString("yyyy-MM-ddTHH:mm:ss")
                            }
                        }
                    }
                }
            } else {
                Write-Warning "TRX file has unexpected structure or is empty"
            }
        } catch {
            Write-Host "Error parsing TRX file: $_" -ForegroundColor Red
            Write-Host "TRX file path: $($trxFile.FullName)" -ForegroundColor Yellow
            Write-Host "Error details: $($_.Exception.Message)" -ForegroundColor Yellow
        }
        
        # Create test run result with safe property access
        $passedCount = if ($testResults) { ($testResults | Where-Object { $_.Passed }).Count } else { 0 }
        $failedCount = if ($testResults) { ($testResults | Where-Object { !$_.Passed }).Count } else { 0 }
        
        $testRunResult = @{
            TestCategory = if ($TestCategory) { $TestCategory } else { "Unknown" }
            StartedAt = if ($stopwatch -and $stopwatch.StartTime) { $stopwatch.StartTime.ToString("yyyy-MM-ddTHH:mm:ss") } else { [DateTime]::Now.ToString("yyyy-MM-ddTHH:mm:ss") }
            CompletedAt = [DateTime]::Now.ToString("yyyy-MM-ddTHH:mm:ss")
            TotalDuration = if ($elapsed) { $elapsed.ToString() } else { "00:00:00" }
            TotalTests = if ($testResults) { $testResults.Count } else { 0 }
            PassedTests = $passedCount
            FailedTests = $failedCount
            TestResults = if ($testResults) { $testResults } else { @() }
            Environment = "Development"
            Browser = if ($Browser) { $Browser } else { "Chrome" }
        }
        
        # Convert to JSON and send to API
        $jsonPayload = $testRunResult | ConvertTo-Json -Depth 20
        $payloadSizeKB = [Math]::Round(([System.Text.Encoding]::UTF8.GetBytes($jsonPayload).Length / 1024), 2)
        Write-Host "📊 Sending test results payload: $payloadSizeKB KB" -ForegroundColor Cyan
        
        try {
            # If payload is too large (>2MB), create a summarized version
            if ($payloadSizeKB -gt 2048) {
                Write-Host "⚠️  Large payload detected ($payloadSizeKB KB), creating summary..." -ForegroundColor Yellow
                
                # Create summarized version with only essential data
                $summarizedTestResults = $testResults | ForEach-Object {
                    @{
                        TestName = if ($_.TestName.Length -gt 100) { $_.TestName.Substring(0, 100) + "..." } else { $_.TestName }
                        Passed = $_.Passed
                        ErrorMessage = if ($_.ErrorMessage -and $_.ErrorMessage.Length -gt 200) { $_.ErrorMessage.Substring(0, 200) + "..." } else { $_.ErrorMessage }
                        Duration = $_.Duration
                        ExecutedAt = $_.ExecutedAt
                    }
                }
                
                $testRunResult.TestResults = $summarizedTestResults
                $jsonPayload = $testRunResult | ConvertTo-Json -Depth 20
                $payloadSizeKB = [Math]::Round(([System.Text.Encoding]::UTF8.GetBytes($jsonPayload).Length / 1024), 2)
                Write-Host "📊 Summarized payload size: $payloadSizeKB KB" -ForegroundColor Cyan
            }
            
            $response = Invoke-RestMethod -Uri "http://localhost:5285/api/testresults" -Method POST -Body $jsonPayload -ContentType "application/json" -TimeoutSec 60
            Write-Host "✅ Test results sent successfully to application" -ForegroundColor Green
        } catch {
            Write-Host "⚠️  Could not send test results to application: $_" -ForegroundColor Yellow
            Write-Host "   Payload size: $payloadSizeKB KB, Tests: $($testResults.Count)" -ForegroundColor Gray
            Write-Host "   This could be due to large payload size or server not running" -ForegroundColor Gray
        }
    } else {
        Write-Host "⚠️  No TRX file found to parse test results" -ForegroundColor Yellow
    }
} catch {
    Write-Host "⚠️  Error processing test results: $_" -ForegroundColor Yellow
}

# Generate HTML report if requested
if ($GenerateReport) {
    Write-Host ""
    Write-Host "📊 Generating Test Report..." -ForegroundColor Yellow
    
    $trxFile = Get-ChildItem -Path $outputDir -Filter "*.trx" | Sort-Object LastWriteTime -Descending | Select-Object -First 1
    
    if ($trxFile) {
        $reportFile = Join-Path $outputDir "TestReport.html"
        
        # Simple HTML report generation
        $html = @"
<!DOCTYPE html>
<html>
<head>
    <title>ParaHockey E2E Test Report</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; }
        .header { background: linear-gradient(135deg, #0d6efd 0%, #6610f2 100%); color: white; padding: 20px; border-radius: 8px; margin-bottom: 20px; }
        .summary { background: #f8f9fa; padding: 15px; border-radius: 8px; margin-bottom: 20px; }
        .success { color: #198754; font-weight: bold; }
        .failure { color: #dc3545; font-weight: bold; }
        .info { color: #0dcaf0; }
        .timestamp { color: #6c757d; font-size: 0.9em; }
    </style>
</head>
<body>
    <div class="header">
        <h1>🏒 ParaHockey E2E Test Report</h1>
        <p>Development Environment Test Results</p>
    </div>
    
    <div class="summary">
        <h2>Test Summary</h2>
        <p><strong>Test Category:</strong> $TestCategory</p>
        <p><strong>Browser:</strong> $Browser</p>
        <p><strong>Execution Time:</strong> $($elapsed.ToString('mm\:ss'))</p>
        <p><strong>Result:</strong> $(if ($exitCode -eq 0) { '<span class="success">PASSED</span>' } else { '<span class="failure">FAILED</span>' })</p>
        <p class="timestamp">Generated: $(Get-Date -Format 'yyyy-MM-dd HH:mm:ss')</p>
    </div>
    
    <div class="details">
        <h2>Test Categories Covered</h2>
        <ul>
            <li>✅ Registration Form Validation</li>
            <li>✅ Registration Workflow (Junior & Adult)</li>
            <li>✅ Localization & Language Switching</li>
            <li>✅ Cross-Browser Compatibility</li>
            <li>✅ Responsive Design</li>
            <li>✅ Performance Metrics</li>
        </ul>
        
        <h2>Test Environment</h2>
        <ul>
            <li><strong>Base URL:</strong> http://localhost:5285</li>
            <li><strong>Database:</strong> SQL Server (Development)</li>
            <li><strong>Authentication:</strong> Disabled</li>
            <li><strong>Theme:</strong> Development (Info)</li>
        </ul>
    </div>
    
    <div class="footer" style="margin-top: 40px; padding-top: 20px; border-top: 1px solid #dee2e6; color: #6c757d; text-align: center;">
        <p>🤖 Generated by ParaHockey E2E Test Suite</p>
    </div>
</body>
</html>
"@

        $html | Out-File -FilePath $reportFile -Encoding UTF8
        Write-Host "📄 Test report generated: $reportFile" -ForegroundColor Green
        
        # Try to open the LIVE application test page with test category parameter
        $testUrl = "http://localhost:5285/Test?category=$($TestCategory.ToLower())"
        try {
            Start-Process $testUrl
            Write-Host "🌐 Opening live application test page: $testUrl" -ForegroundColor Green
        } catch {
            Write-Host "💡 Open $testUrl in your browser to view the live test page" -ForegroundColor Yellow
        }
    } else {
        Write-Host "⚠️  No test results file found for report generation" -ForegroundColor Yellow
    }
}

# Check for screenshots
$screenshots = Get-ChildItem -Path $screenshotDir -Filter "*.png" -ErrorAction SilentlyContinue
if ($screenshots) {
    Write-Host ""
    Write-Host "📸 Screenshots captured: $($screenshots.Count)" -ForegroundColor Cyan
    Write-Host "   Location: $screenshotDir" -ForegroundColor Gray
}

Write-Host ""
Write-Host "🎯 Test Categories Available:" -ForegroundColor Cyan
Write-Host "   - Validation: Form field validation and error handling" -ForegroundColor Gray
Write-Host "   - Workflow: Complete registration workflows and form submission" -ForegroundColor Gray  
Write-Host "   - Localization: Language switching and multilingual validation" -ForegroundColor Gray
Write-Host "   - CrossBrowser: Chrome, Firefox, Edge compatibility" -ForegroundColor Gray
Write-Host "   - Responsive: Mobile and tablet responsive design" -ForegroundColor Gray
Write-Host "   - Performance: Load times and UI responsiveness" -ForegroundColor Gray
Write-Host "   - Smoke: Quick essential functionality tests (recommended)" -ForegroundColor Gray
Write-Host "   - HTTP: Server-side tests without browser (fast)" -ForegroundColor Gray
Write-Host "   - Masking: Input masking for phone/postal code" -ForegroundColor Gray
Write-Host "   - Edge: Edge cases and error conditions" -ForegroundColor Gray
Write-Host "   - Critical: Mission-critical user journeys" -ForegroundColor Gray

Write-Host ""
Write-Host "💡 Usage Examples:" -ForegroundColor Cyan
Write-Host "   .\run-tests.ps1 -TestCategory Smoke -Browser Chrome" -ForegroundColor Gray
Write-Host "   .\run-tests.ps1 -TestCategory CrossBrowser -Parallel" -ForegroundColor Gray
Write-Host "   .\run-tests.ps1 -TestCategory Performance -Headless" -ForegroundColor Gray

exit $exitCode