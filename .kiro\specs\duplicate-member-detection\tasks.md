# Implementation Plan

-   [x] 1. Create duplicate detection service and data models

    -   [x] Create IDuplicateMemberService interface with CheckForDuplicatesAsync and MaskEmail methods
    -   [x] Implement DuplicateMemberService class with email and partial matching logic
    -   [x] Create DuplicateCheckResult and DuplicateResponseDto classes for data transfer
    -   [x] Create DuplicateType enum with NoDuplicate, ExactEmailMatch, and PartialMatch values
    -   [ ] Write unit tests for email masking functionality and duplicate detection logic
    -   _Requirements: 1.1, 2.1, 2.2, 5.1, 5.2_

-   [x] 2. Add localization resources for duplicate detection messages

    -   [x] Add French localization keys to SharedResourceMarker.resx for duplicate messages and button labels
    -   [x] Add English translations to SharedResourceMarker.en-CA.resx for all duplicate detection text
    -   [x] Create localized message templates that support dynamic email masking content
    -   [ ] Test localization switching with duplicate detection messages
    -   _Requirements: 4.1, 4.2, 4.3, 4.4_

-   [x] 3. Integrate duplicate detection service into dependency injection

    -   [x] Register IDuplicateMemberService and DuplicateMemberService in Program.cs
    -   [x] Inject IDuplicateMemberService into MembersController constructor
    -   [x] Update controller constructor and private fields to include duplicate service
    -   [x] Verify service registration works correctly with existing services
    -   _Requirements: 1.1, 2.1, 3.1_

-   [x] 4. Implement server-side duplicate checking in MembersController

    -   [x] Add duplicate checking logic to Register POST action before member creation
    -   [x] Create helper method to handle duplicate check results and generate appropriate responses
    -   [x] Implement different redirect logic for admin users vs regular users in duplicate scenarios
    -   [ ] Add duplicate checking to UpdateMember action for admin-created members
    -   [x] Create AJAX endpoint that returns JSON responses for duplicate detection
    -   _Requirements: 1.1, 1.2, 1.4, 1.5, 2.1, 2.4, 2.5, 3.1, 3.2_

-   [x] 5. Create client-side JavaScript for duplicate detection handling

    -   [x] Modify existing registration form JavaScript to intercept form submission
    -   [x] Add AJAX call to check for duplicates before normal form submission
    -   [x] Create modal dialog functions to display duplicate detection messages
    -   [x] Implement button click handlers for OK, Modify, Yes, and No actions
    -   [x] Add form clearing functionality when user chooses OK option
    -   [x] Add redirect functionality when user chooses Modify option
    -   _Requirements: 1.3, 1.4, 1.5, 2.3, 2.4, 2.5, 2.6, 6.1, 6.3, 6.4_

-   [x] 6. Implement email masking functionality with security best practices

    -   [x] Create email masking method that shows first 2 characters, asterisks, and domain
    -   [x] Add input validation to prevent malformed email addresses from causing errors
    -   [x] Ensure masked emails are properly formatted for display in localized messages
    -   [ ] Test email masking with various email formats and edge cases
    -   _Requirements: 2.2, 2.3, 5.1, 5.2_

-   [x] 7. Add comprehensive error handling and logging

    -   [x] Add try-catch blocks around duplicate detection service calls
    -   [x] Implement graceful degradation when duplicate service is unavailable
    -   [x] Add structured logging for duplicate detection events and results
    -   [x] Create error handling for database connection issues during duplicate checks
    -   [x] Ensure existing registration flow continues if duplicate detection fails
    -   _Requirements: 6.1, 6.2, 6.5_

-   [ ] 8. Create unit tests for duplicate detection service

    -   Write tests for exact email matching with case-insensitive comparison
    -   Create tests for partial matching using lastName and dateOfBirth combination
    -   Test email masking with various email formats and lengths
    -   Write tests for no duplicate scenarios and edge cases
    -   Create mock database context for isolated service testing
    -   _Requirements: 1.1, 2.1, 2.2, 5.1, 5.2, 5.3, 5.4_

-   [ ] 9. Create integration tests for controller duplicate detection

    -   Write tests for duplicate detection in complete registration flow
    -   Test AJAX response formatting and JSON structure
    -   Create tests for admin vs regular user redirect behavior
    -   Test integration with existing ModelState validation pipeline
    -   Verify localized messages are returned correctly in both languages
    -   _Requirements: 1.2, 1.3, 1.4, 1.5, 3.1, 3.2, 4.1, 4.2, 6.1, 6.2_

-   [ ] 10. Test end-to-end duplicate detection scenarios
    -   Test complete registration flow with no duplicates found
    -   Test registration attempt with exact email match and user interactions
    -   Test registration attempt with partial match and user decision flows
    -   Verify form clearing works correctly when user chooses OK
    -   Test redirect to login/edit page when user chooses Modify
    -   Test language switching maintains duplicate detection functionality
    -   _Requirements: 1.1, 1.2, 1.3, 1.4, 1.5, 2.1, 2.2, 2.3, 2.4, 2.5, 2.6, 4.1, 4.2, 4.3, 4.4, 6.3, 6.4, 6.5_
