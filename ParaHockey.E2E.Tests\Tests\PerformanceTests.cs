using Xunit;
using FluentAssertions;
using ParaHockey.E2E.Tests.Infrastructure;
using ParaHockey.E2E.Tests.PageObjects;
using System.Diagnostics;
using OpenQA.Selenium;

namespace ParaHockey.E2E.Tests.Tests
{
    public class PerformanceTests : BaseTest, IDisposable
    {
        private readonly RegistrationPage _registrationPage;
        private readonly HomePage _homePage;

        public PerformanceTests()
        {
            _registrationPage = new RegistrationPage(Driver, Wait);
            _homePage = new HomePage(Driver, Wait);
        }

        [Fact]
        public void HomePage_ShouldLoadWithinAcceptableTime()
        {
            try
            {
                // Arrange
                var stopwatch = Stopwatch.StartNew();

                // Act
                NavigateToHomePage();

                // Assert
                stopwatch.Stop();
                var loadTime = stopwatch.ElapsedMilliseconds;

                loadTime.Should().BeLessThan(3000, "Home page should load within 3 seconds");
                Console.WriteLine($"Home page loaded in {loadTime}ms");

                // Check if critical elements are visible
                _homePage.IsLogoDisplayed().Should().BeTrue("Logo should be displayed after page load");
                _homePage.IsRegisterButtonDisplayed().Should().BeTrue("Register button should be displayed after page load");
            }
            catch (Exception ex)
            {
                TakeScreenshot("HomePage_ShouldLoadWithinAcceptableTime");
                throw;
            }
        }

        [Fact]
        public void RegistrationPage_ShouldLoadWithinAcceptableTime()
        {
            try
            {
                // Arrange
                var stopwatch = Stopwatch.StartNew();

                // Act
                NavigateToRegistrationPage();

                // Assert
                stopwatch.Stop();
                var loadTime = stopwatch.ElapsedMilliseconds;

                loadTime.Should().BeLessThan(4000, "Registration page should load within 4 seconds");
                Console.WriteLine($"Registration page loaded in {loadTime}ms");

                // Check if form is rendered
                var firstNameField = Driver.FindElement(By.Name("FirstName"));
                firstNameField.Displayed.Should().BeTrue("Form should be rendered after page load");
            }
            catch (Exception ex)
            {
                TakeScreenshot("RegistrationPage_ShouldLoadWithinAcceptableTime");
                throw;
            }
        }

        [Fact]
        public void FormInteraction_ShouldBeResponsive()
        {
            try
            {
                // Arrange
                NavigateToRegistrationPage();

                // Act & Assert - Test input field responsiveness
                var inputFields = new[] { "FirstName", "LastName", "Email", "Phone" };

                foreach (var fieldName in inputFields)
                {
                    var stopwatch = Stopwatch.StartNew();
                    
                    var field = Driver.FindElement(By.Name(fieldName));
                    field.Clear();
                    field.SendKeys("Test");

                    stopwatch.Stop();
                    var responseTime = stopwatch.ElapsedMilliseconds;

                    responseTime.Should().BeLessThan(200, $"Input response for {fieldName} should be under 200ms");
                    Console.WriteLine($"Input response time for {fieldName}: {responseTime}ms");
                }
            }
            catch (Exception ex)
            {
                TakeScreenshot("FormInteraction_ShouldBeResponsive");
                throw;
            }
        }

        [Fact]
        public void LanguageSwitching_ShouldCompleteQuickly()
        {
            try
            {
                // Arrange
                NavigateToHomePage();
                var originalTitle = _homePage.GetWelcomeTitle();

                // Act & Assert - Test language switch performance
                var stopwatch = Stopwatch.StartNew();
                
                _homePage.SwitchToEnglish();
                WaitForPageLoad();

                stopwatch.Stop();
                var switchTime = stopwatch.ElapsedMilliseconds;

                switchTime.Should().BeLessThan(5000, "Language switching should complete within 5 seconds");
                Console.WriteLine($"Language switch completed in {switchTime}ms");

                // Verify the switch actually happened
                var newTitle = _homePage.GetWelcomeTitle();
                newTitle.Should().NotBe(originalTitle, "Title should change after language switch");
            }
            catch (Exception ex)
            {
                TakeScreenshot("LanguageSwitching_ShouldCompleteQuickly");
                throw;
            }
        }

        [Fact]
        public void DynamicFieldToggling_ShouldBeImmediate()
        {
            try
            {
                // Arrange
                NavigateToRegistrationPage();

                // Act & Assert - Test registration type switching speed
                var stopwatch = Stopwatch.StartNew();

                _registrationPage.SelectRegistrationType("Junior");
                Thread.Sleep(100); // Small delay to ensure DOM updates

                stopwatch.Stop();
                var toggleTime = stopwatch.ElapsedMilliseconds;

                toggleTime.Should().BeLessThan(1500, "Dynamic field toggling should be immediate (under 1.5 seconds)");
                Console.WriteLine($"Dynamic field toggle completed in {toggleTime}ms");

                // Verify the toggle worked
                _registrationPage.IsParentSectionVisible().Should().BeTrue("Parent section should be visible after selecting Junior");
            }
            catch (Exception ex)
            {
                TakeScreenshot("DynamicFieldToggling_ShouldBeImmediate");
                throw;
            }
        }

        [Fact]
        public void InputMasking_ShouldNotCauseDelay()
        {
            try
            {
                // Arrange
                NavigateToRegistrationPage();

                // Act & Assert - Test phone number masking performance
                var phoneField = Driver.FindElement(By.Name("Phone"));
                var testPhone = "5141234567";

                var stopwatch = Stopwatch.StartNew();

                foreach (char digit in testPhone)
                {
                    phoneField.SendKeys(digit.ToString());
                    Thread.Sleep(10); // Simulate realistic typing speed
                }

                Thread.Sleep(500); // Wait for masking to complete
                stopwatch.Stop();

                var maskingTime = stopwatch.ElapsedMilliseconds;
                maskingTime.Should().BeLessThan(2000, "Phone masking should not cause significant delay");

                // Verify masking worked
                var maskedValue = phoneField.GetAttribute("value");
                maskedValue.Should().Match("(*) *-*", "Phone should be properly masked");
                Console.WriteLine($"Phone masking completed in {maskingTime}ms, result: {maskedValue}");
            }
            catch (Exception ex)
            {
                TakeScreenshot("InputMasking_ShouldNotCauseDelay");
                throw;
            }
        }

        [Fact]
        public void FormValidation_ShouldExecuteQuickly()
        {
            try
            {
                // Arrange
                NavigateToRegistrationPage();

                // Act & Assert - Test validation speed
                var stopwatch = Stopwatch.StartNew();

                // Fill invalid data to trigger validation
                _registrationPage.FillContactInformation("invalid-phone", "mobile", "invalid-email");
                Thread.Sleep(1000); // Wait for validation

                stopwatch.Stop();
                var validationTime = stopwatch.ElapsedMilliseconds;

                validationTime.Should().BeLessThan(2000, "Client-side validation should execute quickly");
                Console.WriteLine($"Validation completed in {validationTime}ms");

                // Verify validation worked
                var phoneField = Driver.FindElement(By.Name("Phone"));
                var emailField = Driver.FindElement(By.Name("Email"));

                phoneField.GetAttribute("class").Should().Contain("is-invalid", "Phone field should show validation error");
                emailField.GetAttribute("class").Should().Contain("is-invalid", "Email field should show validation error");
            }
            catch (Exception ex)
            {
                TakeScreenshot("FormValidation_ShouldExecuteQuickly");
                throw;
            }
        }

        [Fact]
        public void PageSize_ShouldBeReasonable()
        {
            try
            {
                // Arrange & Act
                NavigateToRegistrationPage();

                // Check page size by measuring transferred data (approximate)
                var resourceSizes = ((IJavaScriptExecutor)Driver).ExecuteScript(@"
                    var resources = performance.getEntriesByType('resource');
                    var totalSize = 0;
                    resources.forEach(function(resource) {
                        if (resource.transferSize) {
                            totalSize += resource.transferSize;
                        }
                    });
                    return totalSize;
                ");

                var totalSizeKB = Convert.ToDouble(resourceSizes) / 1024;
                Console.WriteLine($"Total transferred size: {totalSizeKB:F2} KB");

                // Assert - Page size should be reasonable for fast loading
                totalSizeKB.Should().BeLessThan(5000, "Total page size should be under 5MB for reasonable load times");

                // Check number of HTTP requests
                var requestCount = (long)((IJavaScriptExecutor)Driver).ExecuteScript(@"
                    return performance.getEntriesByType('resource').length;
                ");

                Console.WriteLine($"Number of HTTP requests: {requestCount}");
                requestCount.Should().BeLessThan(50, "Number of HTTP requests should be reasonable");
            }
            catch (Exception ex)
            {
                TakeScreenshot("PageSize_ShouldBeReasonable");
                throw;
            }
        }

        [Fact]
        public void JavaScriptExecution_ShouldNotBlockUI()
        {
            try
            {
                // Arrange
                NavigateToRegistrationPage();

                // Act & Assert - Test that UI remains responsive during JavaScript operations
                var stopwatch = Stopwatch.StartNew();

                // Perform multiple operations that might trigger JavaScript
                _registrationPage.FillBasicInformation("Performance", "Test", "1990-01-01", "male");
                _registrationPage.FillAddress("123 Test St", "Montreal", "QC", "H3B 2Y5");
                _registrationPage.SelectRegistrationType("Junior");
                Thread.Sleep(1000);
                _registrationPage.SelectRegistrationType("Development");
                Thread.Sleep(1000);

                stopwatch.Stop();
                var operationTime = stopwatch.ElapsedMilliseconds;

                operationTime.Should().BeLessThan(5000, "Multiple JavaScript operations should not block UI for long");
                Console.WriteLine($"JavaScript operations completed in {operationTime}ms");

                // Verify UI is still responsive
                var firstNameField = Driver.FindElement(By.Name("FirstName"));
                firstNameField.Clear();
                firstNameField.SendKeys("Still Responsive");
                
                firstNameField.GetAttribute("value").Should().Be("Still Responsive", "UI should remain responsive after JavaScript operations");
            }
            catch (Exception ex)
            {
                TakeScreenshot("JavaScriptExecution_ShouldNotBlockUI");
                throw;
            }
        }

        [Fact]
        public void MemoryUsage_ShouldNotIncreaseExcessively()
        {
            try
            {
                // Arrange
                NavigateToRegistrationPage();

                // Get initial memory usage (approximate using JavaScript heap)
                var initialMemory = (long)((IJavaScriptExecutor)Driver).ExecuteScript(@"
                    if (performance.memory) {
                        return performance.memory.usedJSHeapSize;
                    }
                    return 0;
                ");

                // Act - Perform memory-intensive operations
                for (int i = 0; i < 10; i++)
                {
                    _registrationPage.FillBasicInformation($"Test{i}", $"User{i}", "1990-01-01", "male");
                    _registrationPage.SelectRegistrationType("Junior");
                    Thread.Sleep(200);
                    _registrationPage.SelectRegistrationType("Development");
                    Thread.Sleep(200);
                    _registrationPage.ResetForm();
                    Thread.Sleep(500);
                }

                // Get final memory usage
                var finalMemory = (long)((IJavaScriptExecutor)Driver).ExecuteScript(@"
                    if (performance.memory) {
                        return performance.memory.usedJSHeapSize;
                    }
                    return 0;
                ");

                // Assert
                if (initialMemory > 0 && finalMemory > 0)
                {
                    var memoryIncrease = finalMemory - initialMemory;
                    var memoryIncreaseKB = memoryIncrease / 1024.0;
                    
                    Console.WriteLine($"Memory usage increased by {memoryIncreaseKB:F2} KB");
                    memoryIncreaseKB.Should().BeLessThan(10240, "Memory usage should not increase by more than 10MB during normal operations");
                }
                else
                {
                    Console.WriteLine("Memory measurement not available in this browser");
                }
            }
            catch (Exception ex)
            {
                TakeScreenshot("MemoryUsage_ShouldNotIncreaseExcessively");
                throw;
            }
        }

        public new void Dispose()
        {
            base.Dispose();
        }
    }
}