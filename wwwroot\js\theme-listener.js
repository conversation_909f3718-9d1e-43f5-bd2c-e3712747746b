/*
 * ParaHockey Theme Listener
 * Listens for system theme changes and applies appropriate CSS classes
 * Supports prefers-color-scheme: dark and forced-colors media queries
 */

(function () {
    'use strict';

    // Check if browser supports matchMedia (graceful degradation)
    if (!window.matchMedia) {
        console.warn('ParaHockey Theme Listener: matchMedia not supported, theme switching disabled');
        return;
    }

    // Get references to document element
    const documentElement = document.documentElement;

    // Dark mode media query
    const darkModeQuery = window.matchMedia('(prefers-color-scheme: dark)');
    
    // Forced colors (high contrast) media query
    const forcedColorsQuery = window.matchMedia('(forced-colors: active)');

    /**
     * Updates theme classes based on current media query states
     */
    function updateThemeClasses() {
        // Dark mode handling
        if (darkModeQuery.matches) {
            documentElement.classList.add('ph-dark');
            console.log('ParaHockey Theme: Dark mode activated');
        } else {
            documentElement.classList.remove('ph-dark');
            console.log('ParaHockey Theme: Light mode activated');
        }

        // Forced colors (high contrast) handling
        if (forcedColorsQuery.matches) {
            documentElement.classList.add('ph-forced-colors');
            console.log('ParaHockey Theme: Forced colors/high contrast mode activated');
        } else {
            documentElement.classList.remove('ph-forced-colors');
        }
    }

    /**
     * Initialize theme listener
     */
    function initializeThemeListener() {
        // Set initial state
        updateThemeClasses();

        // Listen for dark mode changes
        try {
            darkModeQuery.addEventListener('change', updateThemeClasses);
        } catch (e) {
            // Fallback for older browsers
            darkModeQuery.addListener(updateThemeClasses);
        }

        // Listen for forced colors changes
        try {
            forcedColorsQuery.addEventListener('change', updateThemeClasses);
        } catch (e) {
            // Fallback for older browsers
            forcedColorsQuery.addListener(updateThemeClasses);
        }

        console.log('ParaHockey Theme Listener: Initialized successfully');
    }

    // Initialize when DOM is ready
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', initializeThemeListener);
    } else {
        // DOM is already ready
        initializeThemeListener();
    }

    // Expose theme utilities to global scope for debugging
    window.ParaHockeyTheme = {
        isDark: function() {
            return darkModeQuery.matches;
        },
        isForcedColors: function() {
            return forcedColorsQuery.matches;
        },
        refresh: function() {
            updateThemeClasses();
        },
        getSystemTheme: function() {
            return {
                dark: darkModeQuery.matches,
                forcedColors: forcedColorsQuery.matches,
                light: !darkModeQuery.matches && !forcedColorsQuery.matches
            };
        }
    };
})();