-- Add Test Data to ParaHockey Databases - CORRECTED VERSION
-- Run this script AFTER creating the tables

USE ParaHockeyDB_TEST;
GO

-- Check if TestEntries table exists
IF OBJECT_ID('TestEntries', 'U') IS NOT NULL
BEGIN
    PRINT 'TestEntries table found in TEST database. Adding test data...';
    
    -- Clear existing test data
    DELETE FROM TestEntries;
    
    -- Insert 15 test entries using CORRECT column names
    INSERT INTO TestEntries (Name, Email, Notes, DateCreation, Environment) VALUES
    ('<PERSON>', '<EMAIL>', 'Premier test de saisie pour Para Hockey', GETDATE(), 'TEST'),
    ('<PERSON>', '<EMAIL>', 'Test d''inscription pour l''équipe', DATEADD(MINUTE, -10, GETDATE()), 'TEST'),
    ('<PERSON>', '<EMAIL>', 'Demande d''information sur les horaires', DATEADD(MINUTE, -20, GETDATE()), 'TEST'),
    ('<PERSON>', '<EMAIL>', 'Intérêt pour rejoindre le club', DATEADD(MINUTE, -30, GETDATE()), 'TEST'),
    ('Luc Gagnon', '<EMAIL>', 'Question sur l''équipement nécessaire', DATEADD(MINUTE, -40, GETDATE()), 'TEST'),
    ('Anne Bouchard', '<EMAIL>', 'Inscription pour la saison prochaine', DATEADD(MINUTE, -50, GETDATE()), 'TEST'),
    ('Michel Roy', '<EMAIL>', 'Demande de renseignements généraux', DATEADD(HOUR, -1, GETDATE()), 'TEST'),
    ('Julie Lavoie', '<EMAIL>', 'Intérêt pour le para hockey', DATEADD(HOUR, -2, GETDATE()), 'TEST'),
    ('Robert Côté', '<EMAIL>', 'Question sur les entraînements', DATEADD(HOUR, -3, GETDATE()), 'TEST'),
    ('Isabelle Morin', '<EMAIL>', 'Demande d''adhésion au club', DATEADD(HOUR, -4, GETDATE()), 'TEST'),
    ('Daniel Fortin', '<EMAIL>', 'Information sur les compétitions', DATEADD(HOUR, -5, GETDATE()), 'TEST'),
    ('Sylvie Bélanger', '<EMAIL>', 'Question sur l''accessibilité', DATEADD(HOUR, -6, GETDATE()), 'TEST'),
    ('François Pelletier', '<EMAIL>', 'Demande de contact avec un entraîneur', DATEADD(HOUR, -7, GETDATE()), 'TEST'),
    ('Nathalie Bergeron', '<EMAIL>', 'Intérêt pour le bénévolat', DATEADD(HOUR, -8, GETDATE()), 'TEST'),
    ('Alain Girard', '<EMAIL>', 'Question sur les frais d''inscription', DATEADD(HOUR, -9, GETDATE()), 'TEST');
    
    PRINT 'Successfully added 15 test entries to TEST database.';
    
    -- Show the data
    SELECT COUNT(*) as 'Total Test Entries' FROM TestEntries;
    SELECT TOP 5 Id, Name, Email, DateCreation FROM TestEntries ORDER BY DateCreation DESC;
END
ELSE
BEGIN
    PRINT 'ERROR: TestEntries table not found in TEST database. Create the table first.';
END
GO

-- Also add a few entries to Production database (fewer entries)
USE ParaHockeyDB;
GO

IF OBJECT_ID('TestEntries', 'U') IS NOT NULL
BEGIN
    PRINT 'TestEntries table found in PRODUCTION database. Adding minimal test data...';
    
    -- Clear existing test data
    DELETE FROM TestEntries;
    
    -- Insert just 3 entries for production using CORRECT column names
    INSERT INTO TestEntries (Name, Email, Notes, DateCreation, Environment) VALUES
    ('Admin Test', '<EMAIL>', 'Test de fonctionnement du système', GETDATE(), 'PRODUCTION'),
    ('Demo User', '<EMAIL>', 'Entrée de démonstration', DATEADD(MINUTE, -30, GETDATE()), 'PRODUCTION'),
    ('System Check', '<EMAIL>', 'Vérification du système', DATEADD(HOUR, -1, GETDATE()), 'PRODUCTION');
    
    PRINT 'Successfully added 3 test entries to PRODUCTION database.';
    
    -- Show the data
    SELECT COUNT(*) as 'Total Production Entries' FROM TestEntries;
    SELECT Id, Name, Email, DateCreation FROM TestEntries ORDER BY DateCreation DESC;
END
ELSE
BEGIN
    PRINT 'ERROR: TestEntries table not found in PRODUCTION database. Create the table first.';
END
GO

PRINT 'Test data script completed successfully!';
PRINT 'You can now test the websites to see the data.'; 