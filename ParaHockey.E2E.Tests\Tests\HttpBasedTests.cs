using Xunit;
using FluentAssertions;
using System.Net.Http;
using System.Net;
using System.Text;
using Newtonsoft.Json;
using System.Collections.Generic;

namespace ParaHockey.E2E.Tests.Tests
{
    public class HttpBasedTests : IDisposable
    {
        private readonly HttpClient _client;
        private readonly string _baseUrl = "http://localhost:5285";

        public HttpBasedTests()
        {
            _client = new HttpClient
            {
                Timeout = TimeSpan.FromSeconds(30)
            };
        }

        [Fact]
        public async Task HomePage_ShouldReturnSuccessAndCorrectContent()
        {
            // Act
            var response = await _client.GetAsync(_baseUrl);
            var content = await response.Content.ReadAsStringAsync();

            // Assert
            response.StatusCode.Should().Be(HttpStatusCode.OK);
            content.Should().Contain("Parahockey", "Should contain organization name");
            content.Should().ContainAny("Bienvenue", "Welcome", "Should contain welcome message");
            content.Should().Contain("Members/Register", "Should contain registration link");
        }

        [Fact]
        public async Task RegistrationPage_ShouldLoadWithAllRequiredFields()
        {
            // Act
            var response = await _client.GetAsync($"{_baseUrl}/Members/Register");
            var content = await response.Content.ReadAsStringAsync();

            // Assert
            response.StatusCode.Should().Be(HttpStatusCode.OK);
            
            // Check for all required form fields
            var requiredFields = new[] 
            { 
                "FirstName", "LastName", "DateOfBirth", "Address", 
                "City", "PostalCode", "Phone", "Email", "ProvinceId"
            };

            foreach (var field in requiredFields)
            {
                content.Should().Contain($"name=\"{field}\"", $"Should contain {field} field");
            }

            // Check for registration types
            content.Should().Contain("reg-1", "Should contain Junior registration type");
            content.Should().Contain("reg-2", "Should contain Development registration type");
            content.Should().Contain("reg-3", "Should contain Elite registration type");
            content.Should().Contain("reg-4", "Should contain Coach registration type");
            content.Should().Contain("reg-5", "Should contain Volunteer registration type");
        }

        [Fact]
        public async Task RegistrationPage_ShouldIncludeValidationScripts()
        {
            // Act
            var response = await _client.GetAsync($"{_baseUrl}/Members/Register");
            var content = await response.Content.ReadAsStringAsync();

            // Assert
            response.StatusCode.Should().Be(HttpStatusCode.OK);
            
            // Check for validation scripts
            content.Should().Contain("jquery.validate", "Should include jQuery validation");
            content.Should().Contain("jquery.validate.unobtrusive", "Should include unobtrusive validation");
            
            // Check for masking scripts
            content.Should().ContainAny("jquery.mask", "cleave", "imask", "Should include input masking library");
        }

        [Fact]
        public async Task LanguageSwitch_ShouldReturnDifferentContent()
        {
            // Test French (default)
            var frenchResponse = await _client.GetAsync($"{_baseUrl}/Home/Index?culture=fr-CA");
            var frenchContent = await frenchResponse.Content.ReadAsStringAsync();

            // Test English
            var englishResponse = await _client.GetAsync($"{_baseUrl}/Home/Index?culture=en-CA");
            var englishContent = await englishResponse.Content.ReadAsStringAsync();

            // Assert
            frenchResponse.StatusCode.Should().Be(HttpStatusCode.OK);
            englishResponse.StatusCode.Should().Be(HttpStatusCode.OK);

            // Content should be different
            frenchContent.Should().NotBe(englishContent, "French and English content should be different");
            
            // Language-specific checks
            frenchContent.Should().Contain("Bienvenue", "French should contain 'Bienvenue'");
            englishContent.Should().Contain("Welcome", "English should contain 'Welcome'");
        }

        [Fact]
        public async Task StaticAssets_ShouldBeAccessible()
        {
            // Test critical static assets
            var assets = new[]
            {
                "/css/site.css",
                "/js/site.js",
                "/lib/bootstrap/dist/css/bootstrap.min.css",
                "/lib/jquery/dist/jquery.min.js"
            };

            foreach (var asset in assets)
            {
                var response = await _client.GetAsync($"{_baseUrl}{asset}");
                response.StatusCode.Should().Be(HttpStatusCode.OK, $"Asset {asset} should be accessible");
                
                var contentType = response.Content.Headers.ContentType?.MediaType;
                if (asset.EndsWith(".css"))
                {
                    contentType.Should().Be("text/css", $"CSS file {asset} should have correct content type");
                }
                else if (asset.EndsWith(".js"))
                {
                    contentType.Should().Be("application/javascript", $"JS file {asset} should have correct content type");
                }
            }
        }

        [Fact]
        public async Task EnvironmentSpecificAssets_ShouldExist()
        {
            // Check for environment-specific CSS files
            var envAssets = new[]
            {
                "/css/environment-test.css",
                "/css/environment-prod.css"
            };

            foreach (var asset in envAssets)
            {
                var response = await _client.GetAsync($"{_baseUrl}{asset}");
                response.StatusCode.Should().Be(HttpStatusCode.OK, $"Environment asset {asset} should exist");
            }
        }

        [Fact]
        public async Task FormSubmission_WithInvalidData_ShouldReturnValidationErrors()
        {
            // Prepare invalid form data
            var formData = new FormUrlEncodedContent(new[]
            {
                new KeyValuePair<string, string>("FirstName", ""),
                new KeyValuePair<string, string>("LastName", ""),
                new KeyValuePair<string, string>("Email", "invalid-email")
            });

            // Act
            var response = await _client.PostAsync($"{_baseUrl}/Members/Register", formData);
            var content = await response.Content.ReadAsStringAsync();

            // Assert
            response.StatusCode.Should().Be(HttpStatusCode.OK, "Should return OK with validation errors");
            content.Should().Contain("field-validation-error", "Should contain validation error markers");
            content.Should().Contain("validation-summary", "Should contain validation summary");
        }

        [Fact]
        public async Task ApiEndpoints_ShouldBeProtected()
        {
            // Test that API endpoints exist and respond appropriately
            var apiEndpoints = new[]
            {
                "/api/members",
                "/api/registration",
                "/api/validation"
            };

            foreach (var endpoint in apiEndpoints)
            {
                var response = await _client.GetAsync($"{_baseUrl}{endpoint}");
                
                // API endpoints might return 404, 401, or 405 depending on implementation
                var acceptableStatusCodes = new[] 
                { 
                    HttpStatusCode.NotFound, 
                    HttpStatusCode.Unauthorized, 
                    HttpStatusCode.MethodNotAllowed,
                    HttpStatusCode.OK
                };
                
                acceptableStatusCodes.Should().Contain(response.StatusCode, 
                    $"API endpoint {endpoint} should return an expected status code");
            }
        }

        [Fact]
        public async Task SecurityHeaders_ShouldBePresent()
        {
            // Act
            var response = await _client.GetAsync(_baseUrl);

            // Assert
            response.Headers.Should().ContainKey("X-Content-Type-Options", 
                "Should include X-Content-Type-Options header");
            response.Headers.Should().ContainKey("X-Frame-Options", 
                "Should include X-Frame-Options header");
            
            // Check for common security headers
            var headers = response.Headers.ToDictionary(h => h.Key, h => h.Value);
            
            if (headers.ContainsKey("X-Content-Type-Options"))
            {
                headers["X-Content-Type-Options"].Should().Contain("nosniff");
            }
            
            if (headers.ContainsKey("X-Frame-Options"))
            {
                var frameOptions = string.Join(",", headers["X-Frame-Options"]);
                frameOptions.Should().Match(x => x.Contains("DENY") || x.Contains("SAMEORIGIN"));
            }
        }

        [Fact]
        public async Task ErrorPage_ShouldBeCustomized()
        {
            // Test 404 error
            var response = await _client.GetAsync($"{_baseUrl}/non-existent-page");
            
            if (response.StatusCode == HttpStatusCode.NotFound)
            {
                var content = await response.Content.ReadAsStringAsync();
                content.Should().NotContain("This page isn't working", 
                    "Should not show default browser error page");
                content.Should().ContainAny("404", "not found", "introuvable", 
                    "Should show custom error message");
            }
        }

        [Fact]
        public async Task Localization_ResourceFiles_ShouldBeLoaded()
        {
            // Check that localization is working by examining page content
            var response = await _client.GetAsync($"{_baseUrl}/Members/Register");
            var content = await response.Content.ReadAsStringAsync();

            // Should contain localized labels (not raw resource keys)
            content.Should().NotContain("[[", "Should not contain unresolved resource keys");
            content.Should().NotContain("]]", "Should not contain unresolved resource keys");
            
            // Should have proper labels
            content.Should().ContainAny("First Name", "Prénom", "Should contain localized first name label");
            content.Should().ContainAny("Email", "Courriel", "Should contain localized email label");
        }

        [Fact]
        public async Task FormAntiForgeryToken_ShouldBePresent()
        {
            // Act
            var response = await _client.GetAsync($"{_baseUrl}/Members/Register");
            var content = await response.Content.ReadAsStringAsync();

            // Assert
            response.StatusCode.Should().Be(HttpStatusCode.OK);
            content.Should().Contain("__RequestVerificationToken", 
                "Should include anti-forgery token for CSRF protection");
            content.Should().Contain("type=\"hidden\"", 
                "Should have hidden input for anti-forgery token");
        }

        [Fact]
        public async Task HealthCheck_Endpoints_ShouldRespond()
        {
            // Common health check endpoints
            var healthEndpoints = new[]
            {
                "/health",
                "/healthz",
                "/ready",
                "/api/health"
            };

            var foundHealthEndpoint = false;
            foreach (var endpoint in healthEndpoints)
            {
                var response = await _client.GetAsync($"{_baseUrl}{endpoint}");
                if (response.StatusCode == HttpStatusCode.OK)
                {
                    foundHealthEndpoint = true;
                    var content = await response.Content.ReadAsStringAsync();
                    content.Should().ContainAny("healthy", "ok", "ready", 
                        "Health endpoint should indicate healthy status");
                    break;
                }
            }

            // It's okay if no health endpoint exists, but log it
            if (!foundHealthEndpoint)
            {
                Console.WriteLine("No health check endpoint found - consider adding one for monitoring");
            }
        }

        [Fact]
        public async Task ResponseTime_ShouldBeAcceptable()
        {
            // Measure response times for key pages
            var pagesToTest = new[]
            {
                ("/", "Home page"),
                ("/Members/Register", "Registration page")
            };

            foreach (var (url, pageName) in pagesToTest)
            {
                var sw = System.Diagnostics.Stopwatch.StartNew();
                var response = await _client.GetAsync($"{_baseUrl}{url}");
                sw.Stop();

                response.StatusCode.Should().Be(HttpStatusCode.OK);
                sw.ElapsedMilliseconds.Should().BeLessThan(5000, 
                    $"{pageName} should load within 5 seconds");
                
                Console.WriteLine($"{pageName} loaded in {sw.ElapsedMilliseconds}ms");
            }
        }

        [Fact]
        public async Task ContentEncoding_ShouldSupportCompression()
        {
            // Add Accept-Encoding header
            _client.DefaultRequestHeaders.Add("Accept-Encoding", "gzip, deflate");
            
            var response = await _client.GetAsync(_baseUrl);
            
            // Check if response is compressed
            if (response.Content.Headers.ContentEncoding.Any())
            {
                var encoding = string.Join(",", response.Content.Headers.ContentEncoding);
                encoding.Should().Match(x => x.Contains("gzip") || x.Contains("deflate"), 
                    "Should support compression for better performance");
            }
            else
            {
                Console.WriteLine("Warning: Response compression not enabled");
            }
        }

        public void Dispose()
        {
            _client?.Dispose();
        }
    }
}