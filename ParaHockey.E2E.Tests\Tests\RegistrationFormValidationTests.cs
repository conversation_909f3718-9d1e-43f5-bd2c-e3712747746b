using Xunit;
using FluentAssertions;
using ParaHockey.E2E.Tests.Infrastructure;
using ParaHockey.E2E.Tests.PageObjects;
using OpenQA.Selenium;

namespace ParaHockey.E2E.Tests.Tests
{
    public class RegistrationFormValidationTests : BaseTest, IDisposable
    {
        private readonly RegistrationPage _registrationPage;

        public RegistrationFormValidationTests()
        {
            _registrationPage = new RegistrationPage(Driver, Wait);
        }

        [Fact]
        public void SubmitEmptyForm_ShouldShowValidationErrors()
        {
            try
            {
                // Arrange
                NavigateToRegistrationPage();

                // Act
                _registrationPage.SubmitForm();
                Thread.Sleep(2000); // Wait for validation

                // Assert
                _registrationPage.HasValidationErrors().Should().BeTrue("Empty form should trigger validation errors");
                _registrationPage.ValidationErrorCount.Should().BeGreaterThan(0, "Should have at least one validation error");
            }
            catch (Exception ex)
            {
                TakeScreenshot("SubmitEmptyForm_ShouldShowValidationErrors");
                throw;
            }
        }

        [Theory]
        [InlineData("(*************", true)]
        [InlineData("5141234567", true)] // Should be formatted automatically
        [InlineData("invalid-phone", false)]
        [InlineData("123", false)]
        public void PhoneNumberValidation_ShouldHandleVariousFormats(string phoneInput, bool shouldBeValid)
        {
            try
            {
                // Arrange
                NavigateToRegistrationPage();

                // Act
                _registrationPage.FillContactInformation(phoneInput, "mobile", TestData.ValidEmail);
                Thread.Sleep(1000); // Wait for masking/validation

                // Assert
                if (shouldBeValid)
                {
                    _registrationPage.IsPhoneMasked().Should().BeTrue("Valid phone should be automatically masked");
                    _registrationPage.GetPhoneValue().Should().Match("(*) *-*", "Phone should follow (XXX) XXX-XXXX pattern");
                }
                else
                {
                    // For invalid phones, check if validation styling is applied
                    var phoneField = Driver.FindElement(By.Name("Phone"));
                    var classes = phoneField.GetAttribute("class");
                    classes.Should().Contain("is-invalid", "Invalid phone should have error styling");
                }
            }
            catch (Exception ex)
            {
                TakeScreenshot($"PhoneNumberValidation_{phoneInput.Replace(" ", "_").Replace("(", "").Replace(")", "")}");
                throw;
            }
        }

        [Theory]
        [InlineData("H3B 2Y5", true)]
        [InlineData("h3b2y5", true)] // Should be formatted automatically
        [InlineData("H3B2Y5", true)] // Should be formatted automatically
        [InlineData("12345", false)]
        [InlineData("INVALID", false)]
        [InlineData("A1A1A1", false)] // Missing space
        public void PostalCodeValidation_ShouldHandleVariousFormats(string postalInput, bool shouldBeValid)
        {
            try
            {
                // Arrange
                NavigateToRegistrationPage();

                // Act
                _registrationPage.FillAddress("123 Test St", "Montreal", "QC", postalInput);
                Thread.Sleep(1000); // Wait for masking/validation

                // Assert
                if (shouldBeValid)
                {
                    _registrationPage.IsPostalCodeMasked().Should().BeTrue("Valid postal code should be automatically masked");
                    _registrationPage.GetPostalCodeValue().Should().MatchRegex(@"^[A-Z]\d[A-Z] \d[A-Z]\d$", "Postal code should follow A1A 1A1 pattern");
                }
                else
                {
                    var postalField = Driver.FindElement(By.Name("PostalCode"));
                    var classes = postalField.GetAttribute("class");
                    classes.Should().Contain("is-invalid", "Invalid postal code should have error styling");
                }
            }
            catch (Exception ex)
            {
                TakeScreenshot($"PostalCodeValidation_{postalInput.Replace(" ", "_")}");
                throw;
            }
        }

        [Theory]
        [InlineData("<EMAIL>", true)]
        [InlineData("<EMAIL>", true)]
        [InlineData("invalid-email", false)]
        [InlineData("@domain.com", false)]
        [InlineData("test@", false)]
        [InlineData("<EMAIL>", false)]
        public void EmailValidation_ShouldHandleVariousFormats(string emailInput, bool shouldBeValid)
        {
            try
            {
                // Arrange
                NavigateToRegistrationPage();

                // Act
                _registrationPage.FillContactInformation(TestData.ValidPhone, "mobile", emailInput);
                Thread.Sleep(1000); // Wait for validation

                // Assert
                var emailField = Driver.FindElement(By.Name("Email"));
                var classes = emailField.GetAttribute("class");
                
                if (shouldBeValid)
                {
                    classes.Should().Contain("is-valid", "Valid email should have success styling");
                }
                else
                {
                    classes.Should().Contain("is-invalid", "Invalid email should have error styling");
                }
            }
            catch (Exception ex)
            {
                TakeScreenshot($"EmailValidation_{emailInput.Replace("@", "_at_").Replace(".", "_dot_")}");
                throw;
            }
        }

        [Fact]
        public void RequiredFields_ShouldBeMarkedAsRequired()
        {
            try
            {
                // Arrange
                NavigateToRegistrationPage();

                // Act & Assert
                var requiredFields = new[]
                {
                    "FirstName", "LastName", "DateOfBirth", "Address", 
                    "City", "PostalCode", "Phone", "Email"
                };

                foreach (var fieldName in requiredFields)
                {
                    var field = Driver.FindElement(By.Name(fieldName));
                    var isRequired = field.GetAttribute("required") == "true";
                    isRequired.Should().BeTrue($"Field {fieldName} should be marked as required");
                }
            }
            catch (Exception ex)
            {
                TakeScreenshot("RequiredFields_ShouldBeMarkedAsRequired");
                throw;
            }
        }

        [Theory]
        [InlineData("2010-05-15")] // Valid date
        [InlineData("1990-12-25")] // Valid date
        public void DateOfBirth_ValidDates_ShouldBeAccepted(string dateInput)
        {
            try
            {
                // Arrange
                NavigateToRegistrationPage();

                // Act
                _registrationPage.FillBasicInformation("John", "Doe", dateInput, "male");
                Thread.Sleep(1000);

                // Assert
                var dateField = Driver.FindElement(By.Name("DateOfBirth"));
                var classes = dateField.GetAttribute("class");
                classes.Should().Contain("is-valid", "Valid date should have success styling");
            }
            catch (Exception ex)
            {
                TakeScreenshot($"DateOfBirth_ValidDates_{dateInput.Replace("-", "_")}");
                throw;
            }
        }

        [Theory]
        [InlineData("2030-01-01")] // Future date
        [InlineData("1800-01-01")] // Too old
        [InlineData("invalid-date")] // Invalid format
        [InlineData("2023-13-01")] // Invalid month
        [InlineData("2023-02-30")] // Invalid day
        public void DateOfBirth_InvalidDates_ShouldShowError(string dateInput)
        {
            try
            {
                // Arrange
                NavigateToRegistrationPage();

                // Act
                _registrationPage.FillBasicInformation("John", "Doe", dateInput, "male");
                Thread.Sleep(1000);

                // Assert
                var dateField = Driver.FindElement(By.Name("DateOfBirth"));
                var classes = dateField.GetAttribute("class");
                classes.Should().Contain("is-invalid", "Invalid date should have error styling");
            }
            catch (Exception ex)
            {
                TakeScreenshot($"DateOfBirth_InvalidDates_{dateInput.Replace("-", "_")}");
                throw;
            }
        }

        [Fact]
        public void DatePicker_ShouldOpenWhenIconClicked()
        {
            try
            {
                // Arrange
                NavigateToRegistrationPage();

                // Act
                _registrationPage.ClickDatePicker();
                Thread.Sleep(1000);

                // Assert - Check if datepicker UI is visible
                var datepickerUI = Driver.FindElements(By.CssSelector(".ui-datepicker"));
                datepickerUI.Should().NotBeEmpty("Datepicker UI should be visible after clicking icon");
            }
            catch (Exception ex)
            {
                TakeScreenshot("DatePicker_ShouldOpenWhenIconClicked");
                throw;
            }
        }

        [Theory]
        [InlineData("A", false)] // Too short
        [InlineData("AB", true)]  // Minimum length
        [InlineData("This is a very long name that exceeds the normal length limit for names in most systems and databases", false)] // Too long
        [InlineData("John123", false)] // Contains numbers
        [InlineData("John@Smith", false)] // Contains special chars
        [InlineData("Jean-Pierre", true)] // Hyphenated names should be valid
        [InlineData("O'Connor", true)] // Names with apostrophes should be valid
        [InlineData("François", true)] // Names with accents should be valid
        [InlineData("María José", true)] // Names with spaces and accents should be valid
        public void NameFields_ShouldValidateLength(string nameInput, bool shouldBeValid)
        {
            try
            {
                // Arrange
                NavigateToRegistrationPage();

                // Act
                _registrationPage.FillBasicInformation(nameInput, "TestLastName", TestData.AdultBirthDate, "male");
                Thread.Sleep(1000);

                // Assert
                var firstNameField = Driver.FindElement(By.Name("FirstName"));
                var classes = firstNameField.GetAttribute("class");
                
                if (shouldBeValid)
                {
                    classes.Should().NotContain("is-invalid", "Valid name length should not have error styling");
                }
                else
                {
                    classes.Should().Contain("is-invalid", "Invalid name length should have error styling");
                }
            }
            catch (Exception ex)
            {
                TakeScreenshot($"NameFields_ShouldValidateLength_{nameInput.Length}chars");
                throw;
            }
        }

        [Fact]
        public void FormReset_ShouldClearAllFields()
        {
            try
            {
                // Arrange
                NavigateToRegistrationPage();
                
                // Fill form with test data
                _registrationPage.FillBasicInformation("John", "Doe", TestData.AdultBirthDate, "male");
                _registrationPage.FillAddress("123 Test St", "Montreal", "QC", TestData.ValidPostalCode);
                _registrationPage.FillContactInformation(TestData.ValidPhone, "mobile", TestData.ValidEmail);

                // Act
                _registrationPage.ResetForm();
                Thread.Sleep(1000);

                // Assert
                _registrationPage.GetFirstNameValue().Should().BeEmpty("First name should be cleared after reset");
                _registrationPage.GetLastNameValue().Should().BeEmpty("Last name should be cleared after reset");
                _registrationPage.GetEmailValue().Should().BeEmpty("Email should be cleared after reset");
                _registrationPage.GetPhoneValue().Should().BeEmpty("Phone should be cleared after reset");
                _registrationPage.GetPostalCodeValue().Should().BeEmpty("Postal code should be cleared after reset");
            }
            catch (Exception ex)
            {
                TakeScreenshot("FormReset_ShouldClearAllFields");
                throw;
            }
        }

        [Fact]
        public void PhoneField_ShouldBlockNonNumericInput()
        {
            try
            {
                // Arrange
                NavigateToRegistrationPage();
                var phoneField = Driver.FindElement(By.Name("Phone"));

                // Act - Try to type letters
                phoneField.SendKeys("abcdefgh");
                Thread.Sleep(500);

                // Assert - Field should be empty (letters blocked)
                phoneField.GetAttribute("value").Should().BeEmpty("Letters should be blocked from phone field");

                // Act - Type numbers
                phoneField.SendKeys("5141234567");
                Thread.Sleep(500);

                // Assert - Numbers should be accepted and formatted
                phoneField.GetAttribute("value").Should().Match("(*) *-*", "Numbers should be accepted and formatted");
            }
            catch (Exception ex)
            {
                TakeScreenshot("PhoneField_ShouldBlockNonNumericInput");
                throw;
            }
        }

        [Fact]
        public void PostalCode_ShouldAutoFormatAndUppercase()
        {
            try
            {
                // Arrange
                NavigateToRegistrationPage();
                var postalField = Driver.FindElement(By.Name("PostalCode"));

                // Act - Type lowercase without space
                postalField.SendKeys("h3b2y5");
                Thread.Sleep(1000);

                // Assert - Should be uppercase and formatted
                postalField.GetAttribute("value").Should().Be("H3B 2Y5", "Postal code should be auto-uppercase and formatted");
            }
            catch (Exception ex)
            {
                TakeScreenshot("PostalCode_ShouldAutoFormatAndUppercase");
                throw;
            }
        }

        [Fact]
        public void EmergencyContact_ShouldValidateWhenSectionVisible()
        {
            try
            {
                // Arrange
                NavigateToRegistrationPage();
                
                // Act - Select Junior to show emergency contact
                _registrationPage.SelectRegistrationType("junior");
                Thread.Sleep(1000);

                // Fill main form but leave emergency contact empty
                _registrationPage.FillBasicInformation("John", "Doe", TestData.MinorBirthDate, "male");
                _registrationPage.FillAddress("123 Test St", "Montreal", "QC", TestData.ValidPostalCode);
                _registrationPage.FillContactInformation(TestData.ValidPhone, "mobile", TestData.ValidEmail);
                
                // Submit form
                _registrationPage.SubmitForm();
                Thread.Sleep(2000);

                // Assert - Should have validation errors for emergency contact
                _registrationPage.HasValidationErrors().Should().BeTrue("Emergency contact fields should be required when visible");
                
                var emergencyErrors = Driver.FindElements(By.CssSelector("#emergency-contact-section .text-danger"));
                emergencyErrors.Should().NotBeEmpty("Should have validation errors in emergency contact section");
            }
            catch (Exception ex)
            {
                TakeScreenshot("EmergencyContact_ShouldValidateWhenSectionVisible");
                throw;
            }
        }

        [Fact]
        public void ParentFields_ShouldValidateWhenMinorSelected()
        {
            try
            {
                // Arrange
                NavigateToRegistrationPage();
                
                // Act - Fill form with minor date
                _registrationPage.FillBasicInformation("John", "Doe", TestData.MinorBirthDate, "male");
                _registrationPage.FillAddress("123 Test St", "Montreal", "QC", TestData.ValidPostalCode);
                _registrationPage.FillContactInformation(TestData.ValidPhone, "mobile", TestData.ValidEmail);
                _registrationPage.SelectRegistrationType("junior");
                Thread.Sleep(1000);
                
                // Submit without parent info
                _registrationPage.SubmitForm();
                Thread.Sleep(2000);

                // Assert - Should have parent validation errors
                _registrationPage.HasValidationErrors().Should().BeTrue("Parent fields should be required for minors");
                
                var parentErrors = Driver.FindElements(By.CssSelector("#parent-fields-section .text-danger"));
                parentErrors.Should().NotBeEmpty("Should have validation errors in parent section for minors");
            }
            catch (Exception ex)
            {
                TakeScreenshot("ParentFields_ShouldValidateWhenMinorSelected");
                throw;
            }
        }

        [Theory]
        [InlineData("Development", false)] // Adult doesn't need parent
        [InlineData("Elite", false)] // Adult doesn't need parent
        [InlineData("Coach", false)] // Coach doesn't need parent
        [InlineData("Volunteer", false)] // Volunteer doesn't need parent
        public void ParentFields_ShouldNotBeRequiredForAdults(string registrationType, bool shouldShowParent)
        {
            try
            {
                // Arrange
                NavigateToRegistrationPage();
                
                // Act - Fill form with adult date
                _registrationPage.FillBasicInformation("John", "Doe", TestData.AdultBirthDate, "male");
                _registrationPage.SelectRegistrationType(registrationType);
                Thread.Sleep(1000);

                // Assert
                _registrationPage.IsParentSectionVisible().Should().Be(shouldShowParent, 
                    $"Parent section should {(shouldShowParent ? "" : "not ")}be visible for {registrationType} with adult birthdate");
            }
            catch (Exception ex)
            {
                TakeScreenshot($"ParentFields_NotRequiredForAdults_{registrationType}");
                throw;
            }
        }

        [Fact]
        public void ValidationSummary_ShouldShowAllErrors()
        {
            try
            {
                // Arrange
                NavigateToRegistrationPage();
                
                // Act - Submit empty form
                _registrationPage.SubmitForm();
                Thread.Sleep(2000);

                // Assert - Validation summary should be visible
                var validationSummary = Driver.FindElement(By.CssSelector(".validation-summary-errors"));
                validationSummary.Should().NotBeNull("Validation summary should exist");
                validationSummary.Displayed.Should().BeTrue("Validation summary should be visible");
                
                var summaryErrors = validationSummary.FindElements(By.TagName("li"));
                summaryErrors.Count.Should().BeGreaterThan(5, "Should list multiple validation errors");
            }
            catch (Exception ex)
            {
                TakeScreenshot("ValidationSummary_ShouldShowAllErrors");
                throw;
            }
        }

        [Theory]
        [InlineData("************", "(*************")] // With dashes
        [InlineData("************", "(*************")] // With dots
        [InlineData("************", "(*************")] // With spaces
        [InlineData("(514)123-4567", "(*************")] // Already partially formatted
        [InlineData("15141234567", "(*************")] // With country code
        public void PhoneNumber_ShouldNormalizeFormats(string input, string expected)
        {
            try
            {
                // Arrange
                NavigateToRegistrationPage();
                var phoneField = Driver.FindElement(By.Name("Phone"));

                // Act
                phoneField.SendKeys(input);
                phoneField.SendKeys(Keys.Tab); // Trigger blur event
                Thread.Sleep(1000);

                // Assert
                phoneField.GetAttribute("value").Should().Be(expected, 
                    $"Phone number {input} should be normalized to {expected}");
            }
            catch (Exception ex)
            {
                TakeScreenshot($"PhoneNumber_NormalizeFormats_{input.Replace(" ", "_")}");
                throw;
            }
        }

        [Fact]
        public void CrossFieldValidation_MinorWithAdultRegistrationType_ShouldShowError()
        {
            try
            {
                // Arrange
                NavigateToRegistrationPage();
                
                // Act - Minor birthdate with adult registration type
                _registrationPage.FillBasicInformation("John", "Doe", TestData.MinorBirthDate, "male");
                _registrationPage.SelectRegistrationType("Elite"); // Elite is for adults
                Thread.Sleep(1000);
                
                _registrationPage.SubmitForm();
                Thread.Sleep(2000);

                // Assert - Should have validation error
                _registrationPage.HasValidationErrors().Should().BeTrue(
                    "Should have validation error for minor selecting adult registration type");
            }
            catch (Exception ex)
            {
                TakeScreenshot("CrossFieldValidation_MinorWithAdultRegistrationType");
                throw;
            }
        }

        [Fact]
        public void EmailField_ShouldShowRealTimeValidation()
        {
            try
            {
                // Arrange
                NavigateToRegistrationPage();
                var emailField = Driver.FindElement(By.Name("Email"));

                // Act - Type invalid email progressively
                emailField.SendKeys("test");
                Thread.Sleep(500);
                emailField.GetAttribute("class").Should().Contain("is-invalid", "Partial email should be invalid");

                emailField.SendKeys("@");
                Thread.Sleep(500);
                emailField.GetAttribute("class").Should().Contain("is-invalid", "Email with just @ should be invalid");

                emailField.SendKeys("example");
                Thread.Sleep(500);
                emailField.GetAttribute("class").Should().Contain("is-invalid", "Email without domain should be invalid");

                emailField.SendKeys(".com");
                Thread.Sleep(500);
                emailField.GetAttribute("class").Should().Contain("is-valid", "Complete email should be valid");
            }
            catch (Exception ex)
            {
                TakeScreenshot("EmailField_RealTimeValidation");
                throw;
            }
        }

        public new void Dispose()
        {
            base.Dispose();
        }
    }
}