using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Localization;
using ParaHockeyApp.Services;
using ParaHockeyApp.Configuration;
using System.Globalization;

namespace ParaHockeyApp.Controllers
{
    /// <summary>
    /// Controller for handling localization and configuration for the import system
    /// </summary>
    [ApiController]
    [Route("api/[controller]")]
    public class ImportConfigController : ControllerBase
    {
        private readonly IImportLocalizationService _localizationService;
        private readonly IImportConfigurationService _configurationService;
        private readonly ILogger<ImportConfigController> _logger;

        public ImportConfigController(
            IImportLocalizationService localizationService,
            IImportConfigurationService configurationService,
            ILogger<ImportConfigController> logger)
        {
            _localizationService = localizationService;
            _configurationService = configurationService;
            _logger = logger;
        }

        /// <summary>
        /// Gets localized strings for the import interface
        /// </summary>
        /// <param name="culture">Optional culture to use for localization</param>
        /// <returns>Dictionary of localized strings</returns>
        [HttpGet("localization")]
        public ActionResult<object> GetLocalization(string? culture = null)
        {
            try
            {
                if (!string.IsNullOrEmpty(culture))
                {
                    if (!_configurationService.IsCultureSupported(culture))
                    {
                        return BadRequest($"Culture '{culture}' is not supported");
                    }
                    _localizationService.SetCulture(culture);
                }

                var localization = new
                {
                    Culture = _localizationService.CurrentCulture.Name,
                    Import = new
                    {
                        Title = _localizationService.GetString("Import.Title"),
                        SelectFile = _localizationService.GetString("Import.SelectFile"),
                        FileFormat = _localizationService.GetString("Import.FileFormat"),
                        Upload = _localizationService.GetString("Import.Upload"),
                        Processing = _localizationService.GetString("Import.Processing"),
                        Success = _localizationService.GetString("Import.Success"),
                        Error = _localizationService.GetString("Import.Error"),
                        ValidationErrors = _localizationService.GetString("Import.ValidationErrors"),
                        DuplicatesFound = _localizationService.GetString("Import.DuplicatesFound"),
                        ReviewRequired = _localizationService.GetString("Import.ReviewRequired")
                    },
                    Validation = new
                    {
                        RequiredField = _localizationService.GetString("Validation.RequiredField"),
                        InvalidEmail = _localizationService.GetString("Validation.InvalidEmail"),
                        InvalidDate = _localizationService.GetString("Validation.InvalidDate"),
                        InvalidPhone = _localizationService.GetString("Validation.InvalidPhone"),
                        InvalidPostalCode = _localizationService.GetString("Validation.InvalidPostalCode"),
                        FutureDate = _localizationService.GetString("Validation.FutureDate"),
                        DuplicateEmail = _localizationService.GetString("Validation.DuplicateEmail"),
                        InvalidStatus = _localizationService.GetString("Validation.InvalidStatus")
                    },
                    Buttons = new
                    {
                        Cancel = _localizationService.GetString("Buttons.Cancel"),
                        Continue = _localizationService.GetString("Buttons.Continue"),
                        Retry = _localizationService.GetString("Buttons.Retry"),
                        Download = _localizationService.GetString("Buttons.Download"),
                        Back = _localizationService.GetString("Buttons.Back"),
                        Next = _localizationService.GetString("Buttons.Next"),
                        Finish = _localizationService.GetString("Buttons.Finish"),
                        Save = _localizationService.GetString("Buttons.Save"),
                        Delete = _localizationService.GetString("Buttons.Delete"),
                        Edit = _localizationService.GetString("Buttons.Edit"),
                        View = _localizationService.GetString("Buttons.View")
                    },
                    Messages = new
                    {
                        ConfirmDelete = _localizationService.GetString("Messages.ConfirmDelete"),
                        UnsavedChanges = _localizationService.GetString("Messages.UnsavedChanges"),
                        OperationComplete = _localizationService.GetString("Messages.OperationComplete"),
                        PleaseWait = _localizationService.GetString("Messages.PleaseWait"),
                        NoRecordsFound = _localizationService.GetString("Messages.NoRecordsFound"),
                        LoadingData = _localizationService.GetString("Messages.LoadingData")
                    }
                };

                return Ok(localization);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting localization data");
                return StatusCode(500, "An error occurred while retrieving localization data");
            }
        }

        /// <summary>
        /// Gets the current import configuration
        /// </summary>
        /// <returns>Import configuration settings</returns>
        [HttpGet("configuration")]
        public ActionResult<object> GetConfiguration()
        {
            try
            {
                var config = _configurationService.GetConfiguration();

                // Return a sanitized version without sensitive information
                var publicConfig = new
                {
                    MaxFileSizeMB = config.MaxFileSizeMB,
                    MaxRecordsPerImport = config.MaxRecordsPerImport,
                    SupportedFileExtensions = config.SupportedFileExtensions,
                    EnableDuplicateDetection = config.EnableDuplicateDetection,
                    DuplicateThreshold = config.DuplicateThreshold,
                    DefaultCulture = config.DefaultCulture,
                    SupportedCultures = config.SupportedCultures,
                    TimeoutMinutes = config.TimeoutMinutes
                };

                return Ok(publicConfig);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting configuration data");
                return StatusCode(500, "An error occurred while retrieving configuration data");
            }
        }

        /// <summary>
        /// Validates the current configuration
        /// </summary>
        /// <returns>Validation results</returns>
        [HttpGet("configuration/validate")]
        public ActionResult<object> ValidateConfiguration()
        {
            try
            {
                var validationResults = _configurationService.ValidateConfiguration();

                var response = new
                {
                    IsValid = !validationResults.Any(),
                    Errors = validationResults.Select(r => new
                    {
                        ErrorMessage = r.ErrorMessage,
                        MemberNames = r.MemberNames?.ToArray() ?? Array.Empty<string>()
                    }).ToArray()
                };

                return Ok(response);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error validating configuration");
                return StatusCode(500, "An error occurred while validating configuration");
            }
        }

        /// <summary>
        /// Sets the culture for the current session
        /// </summary>
        /// <param name="culture">The culture code to set</param>
        /// <returns>Success status</returns>
        [HttpPost("culture")]
        public ActionResult SetCulture([FromBody] string culture)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(culture))
                {
                    return BadRequest("Culture cannot be empty");
                }

                if (!_configurationService.IsCultureSupported(culture))
                {
                    return BadRequest($"Culture '{culture}' is not supported");
                }

                _localizationService.SetCulture(culture);

                // Set culture cookie for persistence
                Response.Cookies.Append("Culture", culture, new CookieOptions
                {
                    Expires = DateTimeOffset.UtcNow.AddYears(1),
                    HttpOnly = false,
                    Secure = Request.IsHttps,
                    SameSite = SameSiteMode.Strict
                });

                return Ok(new { Message = "Culture set successfully", Culture = culture });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error setting culture to {Culture}", culture);
                return StatusCode(500, "An error occurred while setting culture");
            }
        }

        /// <summary>
        /// Gets error messages for specific error types
        /// </summary>
        /// <param name="errorType">The type of error</param>
        /// <param name="culture">Optional culture to use</param>
        /// <returns>Localized error message</returns>
        [HttpGet("error/{errorType}")]
        public ActionResult<object> GetErrorMessage(string errorType, string? culture = null)
        {
            try
            {
                if (!string.IsNullOrEmpty(culture))
                {
                    if (!_configurationService.IsCultureSupported(culture))
                    {
                        return BadRequest($"Culture '{culture}' is not supported");
                    }
                    _localizationService.SetCulture(culture);
                }

                var errorMessage = _localizationService.GetErrorMessage(errorType);

                return Ok(new { ErrorType = errorType, Message = errorMessage });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting error message for type {ErrorType}", errorType);
                return StatusCode(500, "An error occurred while retrieving error message");
            }
        }

        /// <summary>
        /// Gets all supported cultures
        /// </summary>
        /// <returns>List of supported cultures with their display names</returns>
        [HttpGet("cultures")]
        public ActionResult<object> GetSupportedCultures()
        {
            try
            {
                var config = _configurationService.GetConfiguration();
                var cultures = config.SupportedCultures.Select(c => new
                {
                    Code = c,
                    DisplayName = new CultureInfo(c).DisplayName,
                    NativeName = new CultureInfo(c).NativeName,
                    IsDefault = c == config.DefaultCulture
                }).ToArray();

                return Ok(cultures);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting supported cultures");
                return StatusCode(500, "An error occurred while retrieving supported cultures");
            }
        }
    }
}
