@model ParaHockeyApp.Models.Entities.Member
@{
    ViewData["Title"] = $"Member Details - {Model.FirstName} {Model.LastName}";
    var parents = ViewBag.Parents as List<ParaHockeyApp.Models.Entities.Parent> ?? new List<ParaHockeyApp.Models.Entities.Parent>();
    var emergencyContact = ViewBag.EmergencyContact as ParaHockeyApp.Models.Entities.EmergencyContact;
    var auditLogs = ViewBag.AuditLogs as List<ParaHockeyApp.Models.Entities.AuditLog> ?? new List<ParaHockeyApp.Models.Entities.AuditLog>();
    var auditSummary = ViewBag.AuditSummary as ParaHockeyApp.Services.AuditSummary;
}

<!-- Skip navigation links for accessibility -->
<a href="#main-content" class="skip-link visually-hidden-focusable">@SharedLocalizer["SkipToMainContent"]</a>
<a href="#member-info" class="skip-link visually-hidden-focusable">@SharedLocalizer["SkipToMemberInfo"]</a>
<a href="#member-actions" class="skip-link visually-hidden-focusable">@SharedLocalizer["SkipToMemberActions"]</a>
<a href="#contact-info" class="skip-link visually-hidden-focusable">@SharedLocalizer["SkipToContactInfo"]</a>
<a href="#audit-history" class="skip-link visually-hidden-focusable">@SharedLocalizer["SkipToAuditHistory"]</a>

<main id="main-content" role="main" aria-label="@SharedLocalizer["MemberDetailsPage"]">
    <!-- ARIA live regions for dynamic feedback -->
    <div id="page-status" aria-live="polite" aria-atomic="true" class="visually-hidden"></div>
    <div id="page-errors" aria-live="assertive" aria-atomic="true" class="visually-hidden"></div>
    
    <div class="container-fluid mt-4">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col">
            <h1 class="h2 text-primary">
                <i class="fas fa-user"></i> @SharedLocalizer["MemberDetails"]
            </h1>
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="@Url.Action("Index", "Admin")">@SharedLocalizer["AdminDashboard"]</a></li>
                    <li class="breadcrumb-item"><a href="@Url.Action("Members", "Admin")">@SharedLocalizer["Members"]</a></li>
                    <li class="breadcrumb-item active" aria-current="page">@Model.FirstName @Model.LastName</li>
                </ol>
            </nav>
        </div>
    </div>

    <!-- Action Buttons -->
    <div class="row mb-4">
        <div class="col">
            <div class="btn-group" role="group">
                <a href="@Url.Action("Edit", "Members", new { memberId = Model.Id })" class="btn btn-primary">
                    <i class="fas fa-edit"></i> @SharedLocalizer["EditMember"]
                </a>
                <button type="button" class="btn btn-outline-info" onclick="exportMemberData()">
                    <i class="fas fa-download"></i> @SharedLocalizer["ExportData"]
                </button>
                @if (Model.IsActive)
                {
                    <button type="button" class="btn btn-outline-warning" onclick="confirmDisable(@Model.Id, '@Model.FirstName @Model.LastName')">
                        <i class="fas fa-ban"></i> @SharedLocalizer["DisableMember"]
                    </button>
                }
                else
                {
                    <button type="button" class="btn btn-outline-success" onclick="confirmEnable(@Model.Id, '@Model.FirstName @Model.LastName')">
                        <i class="fas fa-check"></i> @SharedLocalizer["EnableMember"]
                    </button>
                }
                <a href="@Url.Action("Members", "Admin")" class="btn btn-outline-secondary">
                    <i class="fas fa-arrow-left"></i> @SharedLocalizer["BackToMembersList"]
                </a>
            </div>
        </div>
    </div>

    <!-- Member Information -->
    <div class="row mb-4">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-user"></i> @SharedLocalizer["PersonalInformation"]
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <table class="table table-borderless">
                                <tr>
                                    <td><strong>@SharedLocalizer["MemberID"]:</strong></td>
                                    <td>
                                        @Model.Id
                                        @if (!string.IsNullOrEmpty(Model.HQc_Id))
                                        {
                                            <br><small class="text-muted">HQc ID: @Model.HQc_Id</small>
                                        }
                                    </td>
                                </tr>
                                <tr>
                                    <td><strong>@SharedLocalizer["FirstName"]:</strong></td>
                                    <td>@Model.FirstName</td>
                                </tr>
                                <tr>
                                    <td><strong>@SharedLocalizer["LastName"]:</strong></td>
                                    <td>@Model.LastName</td>
                                </tr>
                                <tr>
                                    <td><strong>@SharedLocalizer["DateOfBirth"]:</strong></td>
                                    <td>
                                        @Model.DateOfBirth.ToString("yyyy-MM-dd")
                                        <span class="text-muted">(@Model.Age @SharedLocalizer["YearsOld"])</span>
                                    </td>
                                </tr>
                                <tr>
                                    <td><strong>@SharedLocalizer["Gender"]:</strong></td>
                                    <td>
                                        @if (Model.Gender != null)
                                        {
                                            @SharedLocalizer[Model.Gender.DisplayNameKey]
                                        }
                                        else
                                        {
                                            <span class="text-muted">@SharedLocalizer["NotSpecified"]</span>
                                        }
                                    </td>
                                </tr>
                            </table>
                        </div>
                        <div class="col-md-6">
                            <table class="table table-borderless">
                                <tr>
                                    <td><strong>@SharedLocalizer["Email"]:</strong></td>
                                    <td><a href="mailto:@Model.Email">@Model.Email</a></td>
                                </tr>
                                <tr>
                                    <td><strong>@SharedLocalizer["Phone"]:</strong></td>
                                    <td>
                                        <a href="tel:@Model.Phone">@Model.Phone</a>
                                        @if (Model.PhoneType != null)
                                        {
                                            <span class="badge bg-info ms-2">@SharedLocalizer[Model.PhoneType.DisplayNameKey]</span>
                                        }
                                    </td>
                                </tr>
                                <tr>
                                    <td><strong>@SharedLocalizer["RegistrationType"]:</strong></td>
                                    <td>
                                        @if (Model.RegistrationType != null)
                                        {
                                            <span class="badge bg-success">@SharedLocalizer[Model.RegistrationType.DisplayNameKey]</span>
                                        }
                                        else
                                        {
                                            <span class="text-muted">@SharedLocalizer["NotSpecified"]</span>
                                        }
                                    </td>
                                </tr>
                                <tr>
                                    <td><strong>@SharedLocalizer["RegistrationDate"]:</strong></td>
                                    <td>@Model.DateCreated.ToString("yyyy-MM-dd HH:mm")</td>
                                </tr>
                                <tr>
                                    <td><strong>@SharedLocalizer["Status"]:</strong></td>
                                    <td>
                                        @if (Model.IsActive)
                                        {
                                            <span class="badge bg-success">@SharedLocalizer["Active"]</span>
                                        }
                                        else
                                        {
                                            <span class="badge bg-secondary">@SharedLocalizer["Inactive"]</span>
                                        }
                                        @if (Model.DateModified.HasValue)
                                        {
                                            <br><small class="text-muted">@SharedLocalizer["LastModified"]: @Model.DateModified.Value.ToString("yyyy-MM-dd HH:mm")</small>
                                        }
                                    </td>
                                </tr>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Quick Stats and Actions -->
        <div class="col-md-4">
            <div class="card mb-3">
                <div class="card-header bg-info text-white">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-chart-bar"></i> @SharedLocalizer["QuickStats"]
                    </h5>
                </div>
                <div class="card-body">
                    <div class="text-center">
                        <div class="mb-3">
                            <h4 class="text-primary">@parents.Count</h4>
                            <p class="text-muted mb-0">@SharedLocalizer["ParentsText"]</p>
                        </div>
                        <div class="mb-3">
                            <h4 class="text-warning">@(emergencyContact != null ? 1 : 0)</h4>
                            <p class="text-muted mb-0">@SharedLocalizer["EmergencyContact"]</p>
                        </div>
                        <div class="mb-3">
                            <h4 class="text-success">@auditLogs.Count</h4>
                            <p class="text-muted mb-0">@SharedLocalizer["AuditEntries"]</p>
                        </div>
                        <div>
                            <span class="badge bg-@(Model.RegistrationType?.DisplayNameKey == "Junior" ? "warning" : "info") fs-6">
                                @if (Model.RegistrationType != null)
                                {
                                    @SharedLocalizer[Model.RegistrationType.DisplayNameKey]
                                }
                                else
                                {
                                    <text>@SharedLocalizer["Unknown"]</text>
                                }
                            </span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="card">
                <div class="card-header bg-secondary text-white">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-bolt"></i> @SharedLocalizer["QuickActions"]
                    </h5>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <a href="@Url.Action("Edit", "Members", new { memberId = Model.Id })" class="btn btn-outline-primary btn-sm" target="_blank">
                            <i class="fas fa-external-link-alt"></i> @SharedLocalizer["FullEditForm"]
                        </a>
                        <button type="button" class="btn btn-outline-info btn-sm" onclick="printMemberCard()">
                            <i class="fas fa-print"></i> @SharedLocalizer["PrintMemberCard"]
                        </button>
                        <button type="button" class="btn btn-outline-success btn-sm" onclick="addToTeam()">
                            <i class="fas fa-users"></i> @SharedLocalizer["AddToTeam"]
                        </button>
                        <button type="button" class="btn btn-outline-warning btn-sm" onclick="viewEvents()">
                            <i class="fas fa-calendar"></i> @SharedLocalizer["ViewEvents"]
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Address Information -->
    <div class="row mb-4">
        <div class="col">
            <div class="card">
                <div class="card-header bg-secondary text-white">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-map-marker-alt"></i> @SharedLocalizer["AddressInformation"]
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <p><strong>@SharedLocalizer["StreetAddress"]:</strong><br>@Model.Address</p>
                        </div>
                        <div class="col-md-3">
                            <p><strong>@SharedLocalizer["City"]:</strong><br>@Model.City</p>
                        </div>
                        <div class="col-md-3">
                            <p>
                                <strong>@SharedLocalizer["Province"]:</strong><br>
                                @if (Model.Province != null)
                                {
                                    @SharedLocalizer[Model.Province.DisplayNameKey]
                                }
                                else
                                {
                                    <span class="text-muted">Not specified</span>
                                }
                                <br>
                                <strong>@SharedLocalizer["PostalCode"]:</strong><br>@Model.PostalCode
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Related Contacts -->
    <div class="row mb-4">
        <div class="col">
            <div class="card">
                <div class="card-header bg-success text-white">
                    <ul class="nav nav-tabs card-header-tabs" id="contactTabs" role="tablist">
                        <li class="nav-item" role="presentation">
                            <button class="nav-link active text-white" id="parents-tab" data-bs-toggle="tab" data-bs-target="#parents" type="button" role="tab">
                                <i class="fas fa-users"></i> @SharedLocalizer["ParentsGuardians"] (@parents.Count)
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link text-white" id="emergency-tab" data-bs-toggle="tab" data-bs-target="#emergency" type="button" role="tab">
                                <i class="fas fa-exclamation-triangle"></i> @SharedLocalizer["EmergencyContact"] (@(emergencyContact != null ? 1 : 0))
                            </button>
                        </li>
                    </ul>
                </div>
                <div class="card-body">
                    <div class="tab-content" id="contactTabContent">
                        <!-- Parents Tab -->
                        <div class="tab-pane fade show active" id="parents" role="tabpanel">
                            @if (parents.Any())
                            {
                                <div class="row">
                                    @foreach (var parent in parents)
                                    {
                                        <div class="col-md-6 mb-3">
                                            <div class="border rounded p-3">
                                                <h6 class="text-primary">@parent.FirstName @parent.LastName</h6>
                                                <p class="mb-1"><strong>@SharedLocalizer["Type"]:</strong> @parent.ParentType</p>
                                                <p class="mb-1"><strong>@SharedLocalizer["Phone"]:</strong> <a href="tel:@parent.Phone">@parent.Phone</a></p>
                                                @if (!string.IsNullOrEmpty(parent.Email))
                                                {
                                                    <p class="mb-0"><strong>@SharedLocalizer["Email"]:</strong> <a href="mailto:@parent.Email">@parent.Email</a></p>
                                                }
                                            </div>
                                        </div>
                                    }
                                </div>
                            }
                            else
                            {
                                <div class="text-center text-muted py-4">
                                    <i class="fas fa-users fa-3x mb-3"></i>
                                    <p>@SharedLocalizer["NoParentsRegistered"]</p>
                                </div>
                            }
                        </div>

                        <!-- Emergency Contact Tab -->
                        <div class="tab-pane fade" id="emergency" role="tabpanel">
                            @if (emergencyContact != null)
                            {
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="border rounded p-3">
                                            <h6 class="text-primary">@emergencyContact.FirstName @emergencyContact.LastName</h6>
                                            <p class="mb-1"><strong>@SharedLocalizer["Relation"]:</strong> @emergencyContact.RelationToUser</p>
                                            <p class="mb-1"><strong>@SharedLocalizer["Phone"]:</strong> <a href="tel:@emergencyContact.Phone">@emergencyContact.Phone</a></p>
                                            @if (!string.IsNullOrEmpty(emergencyContact.Email))
                                            {
                                                <p class="mb-0"><strong>@SharedLocalizer["Email"]:</strong> <a href="mailto:@emergencyContact.Email">@emergencyContact.Email</a></p>
                                            }
                                        </div>
                                    </div>
                                </div>
                            }
                            else
                            {
                                <div class="text-center text-muted py-4">
                                    <i class="fas fa-exclamation-triangle fa-3x mb-3"></i>
                                    <p>@SharedLocalizer["NoEmergencyContactRegistered"]</p>
                                </div>
                            }
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Audit History -->
    <div class="row mb-4">
        <div class="col">
            <div class="card">
                <div class="card-header bg-dark text-white">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-history"></i> @SharedLocalizer["AuditHistory"]
                    </h5>
                </div>
                <div class="card-body">
                    @if (auditSummary != null)
                    {
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <p class="mb-1">
                                    <strong>@SharedLocalizer["Created"]:</strong> 
                                    @if (auditSummary.DateCreated.HasValue)
                                    {
                                        @auditSummary.DateCreated.Value.ToString("yyyy-MM-dd HH:mm")
                                        <text> @SharedLocalizer["By"] </text>
                                        @if (auditSummary.CreatedBySource == ParaHockeyApp.Models.Entities.ActionSource.AdminPanel)
                                        {
                                            <span class="badge bg-danger">Admin: @auditSummary.CreatedBy</span>
                                        }
                                        else if (auditSummary.CreatedBySource == ParaHockeyApp.Models.Entities.ActionSource.SelfService)
                                        {
                                            <span class="badge bg-primary">Member: @auditSummary.CreatedBy</span>
                                        }
                                        else
                                        {
                                            <span class="badge bg-secondary">System</span>
                                        }
                                    }
                                    else
                                    {
                                        <span class="text-muted">@SharedLocalizer["Unknown"]</span>
                                    }
                                </p>
                            </div>
                            <div class="col-md-6">
                                <p class="mb-1">
                                    <strong>@SharedLocalizer["LastModified"]:</strong> 
                                    @if (auditSummary.DateModified.HasValue)
                                    {
                                        @auditSummary.DateModified.Value.ToString("yyyy-MM-dd HH:mm")
                                        <text> @SharedLocalizer["By"] </text>
                                        @if (auditSummary.ModifiedBySource == ParaHockeyApp.Models.Entities.ActionSource.AdminPanel)
                                        {
                                            <span class="badge bg-danger">Admin: @auditSummary.ModifiedBy</span>
                                        }
                                        else if (auditSummary.ModifiedBySource == ParaHockeyApp.Models.Entities.ActionSource.SelfService)
                                        {
                                            <span class="badge bg-primary">Member: @auditSummary.ModifiedBy</span>
                                        }
                                        else
                                        {
                                            <span class="badge bg-secondary">System</span>
                                        }
                                    }
                                    else
                                    {
                                        <span class="text-muted">@SharedLocalizer["NeverModified"]</span>
                                    }
                                </p>
                            </div>
                        </div>
                        <div class="mb-3">
                            <p class="mb-0"><strong>@SharedLocalizer["TotalChanges"]:</strong> @auditSummary.TotalChanges</p>
                        </div>
                    }

                    @if (auditLogs.Any())
                    {
                        <h6 class="mt-4 mb-3">
                            @SharedLocalizer["ChangeHistory"] 
                            <button class="btn btn-sm btn-outline-secondary ms-2" type="button" data-bs-toggle="collapse" data-bs-target="#auditDetails">
                                <i class="fas fa-chevron-down"></i> @SharedLocalizer["ShowDetails"]
                            </button>
                        </h6>
                        <div class="collapse" id="auditDetails">
                            <div class="table-responsive">
                                <table class="table table-sm table-hover">
                                    <thead>
                                        <tr>
                                            <th>@SharedLocalizer["AuditDateTime"]</th>
                                            <th>@SharedLocalizer["Action"]</th>
                                            <th>@SharedLocalizer["PerformedBy"]</th>
                                            <th>@SharedLocalizer["Description"]</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        @foreach (var log in auditLogs.Take(20))
                                        {
                                            <tr>
                                                <td>@log.Timestamp.ToLocalTime().ToString("yyyy-MM-dd HH:mm:ss")</td>
                                                <td>
                                                    @if (log.Action == "Create")
                                                    {
                                                        <span class="badge bg-success">@log.Action</span>
                                                    }
                                                    else if (log.Action == "Update")
                                                    {
                                                        <span class="badge bg-warning text-dark">@log.Action</span>
                                                    }
                                                    else
                                                    {
                                                        <span class="badge bg-secondary">@log.Action</span>
                                                    }
                                                </td>
                                                <td>
                                                    @if (log.PerformedBySource == ParaHockeyApp.Models.Entities.ActionSource.AdminPanel)
                                                    {
                                                        <span class="text-danger"><i class="fas fa-user-shield"></i> Admin: @log.PerformerName</span>
                                                    }
                                                    else if (log.PerformedBySource == ParaHockeyApp.Models.Entities.ActionSource.SelfService)
                                                    {
                                                        <span class="text-primary"><i class="fas fa-user"></i> Member: @log.PerformerName</span>
                                                    }
                                                    else
                                                    {
                                                        <span class="text-secondary"><i class="fas fa-cog"></i> System</span>
                                                    }
                                                </td>
                                                <td>
                                                    <small>@log.Description</small>
                                                    @if (!string.IsNullOrEmpty(log.IPAddress))
                                                    {
                                                        <br><small class="text-muted">@SharedLocalizer["IP"]: @log.IPAddress</small>
                                                    }
                                                </td>
                                            </tr>
                                        }
                                    </tbody>
                                </table>
                            </div>
                            @if (auditLogs.Count > 20)
                            {
                                <p class="text-muted text-center">@SharedLocalizer["ShowingFirst20Entries"] (@auditLogs.Count @SharedLocalizer["Total"])</p>
                            }
                        </div>
                    }
                    else
                    {
                        <p class="text-muted">@SharedLocalizer["NoAuditHistoryAvailable"]</p>
                    }
                </div>
            </div>
        </div>
    </div>
    </div>
</main>

<!-- Modals -->
<!-- Disable Confirmation Modal -->
<div class="modal fade" id="disableModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header bg-warning text-dark">
                <h5 class="modal-title"><i class="fas fa-ban"></i> @SharedLocalizer["ConfirmDisable"]</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>@SharedLocalizer["ConfirmDisableMessage"] <strong id="disableMemberName"></strong>?</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">@SharedLocalizer["Cancel"]</button>
                <form method="post" id="disableForm" style="display: inline;">
                    @Html.AntiForgeryToken()
                    <button type="submit" class="btn btn-warning">@SharedLocalizer["DisableMember"]</button>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Enable Confirmation Modal -->
<div class="modal fade" id="enableModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header bg-success text-white">
                <h5 class="modal-title"><i class="fas fa-check"></i> @SharedLocalizer["ConfirmEnable"]</h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>@SharedLocalizer["ConfirmEnableMessage"] <strong id="enableMemberName"></strong>?</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">@SharedLocalizer["Cancel"]</button>
                <form method="post" id="enableForm" style="display: inline;">
                    @Html.AntiForgeryToken()
                    <button type="submit" class="btn btn-success">@SharedLocalizer["EnableMember"]</button>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
// Member action functions
function confirmDisable(memberId, memberName) {
    document.getElementById('disableMemberName').textContent = memberName;
    document.getElementById('disableForm').action = '@Url.Action("DisableMember", "Admin")/' + memberId;
    new bootstrap.Modal(document.getElementById('disableModal')).show();
}

function confirmEnable(memberId, memberName) {
    document.getElementById('enableMemberName').textContent = memberName;
    document.getElementById('enableForm').action = '@Url.Action("EnableMember", "Admin")/' + memberId;
    new bootstrap.Modal(document.getElementById('enableModal')).show();
}

// Quick action functions
function exportMemberData() {
    const exportData = {
        memberIds: [@Model.Id],
        format: 'CSV'
    };
    
    const form = document.createElement('form');
    form.method = 'POST';
    form.action = '@Url.Action("ExportMembers", "Admin")';
    
    const tokenInput = document.createElement('input');
    tokenInput.type = 'hidden';
    tokenInput.name = '__RequestVerificationToken';
    tokenInput.value = document.querySelector('input[name="__RequestVerificationToken"]').value;
    form.appendChild(tokenInput);
    
    const dataInput = document.createElement('input');
    dataInput.type = 'hidden';
    dataInput.name = 'exportData';
    dataInput.value = JSON.stringify(exportData);
    form.appendChild(dataInput);
    
    document.body.appendChild(form);
    form.submit();
    document.body.removeChild(form);
}

function printMemberCard() {
    // TODO: Implement print functionality
    alert('@SharedLocalizer["PrintFeatureComingSoon"]');
}

function addToTeam() {
    // TODO: Implement team assignment
    alert('@SharedLocalizer["TeamFeatureComingSoon"]');
}

function viewEvents() {
    // TODO: Implement event viewing
    alert('@SharedLocalizer["EventFeatureComingSoon"]');
}

// Accessibility enhancements
document.addEventListener('DOMContentLoaded', function() {
    // Announce page load to screen readers
    const statusRegion = document.getElementById('page-status');
    if (statusRegion) {
        statusRegion.textContent = '@SharedLocalizer["MemberDetailsPageLoaded"]';
        setTimeout(() => {
            statusRegion.textContent = '';
        }, 2000);
    }
    
    // Add keyboard navigation for tabs
    const tabButtons = document.querySelectorAll('[data-bs-toggle="tab"]');
    tabButtons.forEach((tab, index) => {
        tab.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowRight' || e.key === 'ArrowLeft') {
                e.preventDefault();
                const nextIndex = e.key === 'ArrowRight' ? 
                    (index + 1) % tabButtons.length : 
                    (index - 1 + tabButtons.length) % tabButtons.length;
                tabButtons[nextIndex].focus();
                tabButtons[nextIndex].click();
            }
        });
    });
    
    // Enhance button accessibility with better focus management
    const actionButtons = document.querySelectorAll('.btn[onclick]');
    actionButtons.forEach(button => {
        button.addEventListener('click', function() {
            // Provide visual feedback
            this.style.transform = 'scale(0.98)';
            setTimeout(() => {
                this.style.transform = '';
            }, 100);
        });
    });
    
    // Add loading states for async operations
    const exportButton = document.querySelector('button[onclick="exportMemberData()"]');
    if (exportButton) {
        const originalExport = window.exportMemberData;
        window.exportMemberData = function() {
            exportButton.disabled = true;
            exportButton.innerHTML = '<i class="fas fa-spinner fa-spin" aria-hidden="true"></i> ' + '@SharedLocalizer["Exporting"]';
            
            try {
                originalExport();
            } finally {
                setTimeout(() => {
                    exportButton.disabled = false;
                    exportButton.innerHTML = '<i class="fas fa-download" aria-hidden="true"></i> @SharedLocalizer["ExportData"]';
                }, 2000);
            }
        };
    }
});
</script>