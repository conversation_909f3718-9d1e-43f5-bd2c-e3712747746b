using System;
using System.Globalization;
using System.Text.RegularExpressions;
using Microsoft.Extensions.Logging;

namespace ParaHockeyApp.Services
{
    /// <summary>
    /// Service for culture-aware formatting of dates, numbers, phone numbers, and other locale-specific data.
    /// Supports Canadian English (en-CA) and Canadian French (fr-CA) cultures.
    /// </summary>
    public interface ICultureAwareFormattingService
    {
        string FormatDate(DateTime date, string? culture = null);
        string FormatDate(DateTime? date, string? culture = null);
        string FormatTime(DateTime time, string? culture = null);
        string FormatDateTime(DateTime dateTime, string? culture = null);
        string FormatShortDate(DateTime date, string? culture = null);
        string FormatLongDate(DateTime date, string? culture = null);
        string FormatRelativeDate(DateTime date, string? culture = null);
        
        string FormatNumber(decimal number, string? culture = null);
        string FormatCurrency(decimal amount, string? culture = null);
        string FormatPercentage(decimal percentage, string? culture = null);
        
        string FormatPhoneNumber(string phoneNumber, string? culture = null);
        string FormatPostalCode(string postalCode, string? culture = null);
        
        string ParsePhoneNumber(string phoneNumber);
        string ParsePostalCode(string postalCode);
        
        bool IsValidPhoneNumber(string phoneNumber, string? culture = null);
        bool IsValidPostalCode(string postalCode, string? culture = null);
    }
    
    public class CultureAwareFormattingService : ICultureAwareFormattingService
    {
        private readonly ILogger<CultureAwareFormattingService> _logger;
        private const string DefaultCulture = "fr-CA"; // Default to French Canadian as per requirements
        
        // Canadian phone number patterns
        private static readonly Regex CanadianPhoneRegex = new(@"^(\+?1[-.\s]?)?(\(?[0-9]{3}\)?[-.\s]?[0-9]{3}[-.\s]?[0-9]{4})$", RegexOptions.Compiled);
        private static readonly Regex PhoneDigitsOnly = new(@"[^\d]", RegexOptions.Compiled);
        
        // Canadian postal code pattern
        private static readonly Regex CanadianPostalCodeRegex = new(@"^[A-Za-z]\d[A-Za-z][\s-]?\d[A-Za-z]\d$", RegexOptions.Compiled);
        
        public CultureAwareFormattingService(ILogger<CultureAwareFormattingService> logger)
        {
            _logger = logger;
        }
        
        #region Date and Time Formatting
        
        public string FormatDate(DateTime date, string? culture = null)
        {
            var cultureInfo = GetCultureInfo(culture);
            return date.ToString("d", cultureInfo);
        }
        
        public string FormatDate(DateTime? date, string? culture = null)
        {
            if (!date.HasValue) return string.Empty;
            return FormatDate(date.Value, culture);
        }
        
        public string FormatTime(DateTime time, string? culture = null)
        {
            var cultureInfo = GetCultureInfo(culture);
            return time.ToString("t", cultureInfo);
        }
        
        public string FormatDateTime(DateTime dateTime, string? culture = null)
        {
            var cultureInfo = GetCultureInfo(culture);
            return dateTime.ToString("g", cultureInfo);
        }
        
        public string FormatShortDate(DateTime date, string? culture = null)
        {
            var cultureInfo = GetCultureInfo(culture);
            return date.ToString("d", cultureInfo);
        }
        
        public string FormatLongDate(DateTime date, string? culture = null)
        {
            var cultureInfo = GetCultureInfo(culture);
            return date.ToString("D", cultureInfo);
        }
        
        public string FormatRelativeDate(DateTime date, string? culture = null)
        {
            var now = DateTime.Now;
            var timeSpan = now - date;
            var cultureInfo = GetCultureInfo(culture);
            
            if (timeSpan.TotalDays < 1)
            {
                if (timeSpan.TotalHours < 1)
                {
                    var minutes = (int)timeSpan.TotalMinutes;
                    return culture?.StartsWith("fr") == true 
                        ? $"il y a {minutes} minute{(minutes > 1 ? "s" : "")}"
                        : $"{minutes} minute{(minutes > 1 ? "s" : "")} ago";
                }
                else
                {
                    var hours = (int)timeSpan.TotalHours;
                    return culture?.StartsWith("fr") == true 
                        ? $"il y a {hours} heure{(hours > 1 ? "s" : "")}"
                        : $"{hours} hour{(hours > 1 ? "s" : "")} ago";
                }
            }
            else if (timeSpan.TotalDays < 7)
            {
                var days = (int)timeSpan.TotalDays;
                return culture?.StartsWith("fr") == true 
                    ? $"il y a {days} jour{(days > 1 ? "s" : "")}"
                    : $"{days} day{(days > 1 ? "s" : "")} ago";
            }
            else
            {
                return FormatShortDate(date, culture);
            }
        }
        
        #endregion
        
        #region Number Formatting
        
        public string FormatNumber(decimal number, string? culture = null)
        {
            var cultureInfo = GetCultureInfo(culture);
            return number.ToString("N", cultureInfo);
        }
        
        public string FormatCurrency(decimal amount, string? culture = null)
        {
            var cultureInfo = GetCultureInfo(culture);
            return amount.ToString("C", cultureInfo);
        }
        
        public string FormatPercentage(decimal percentage, string? culture = null)
        {
            var cultureInfo = GetCultureInfo(culture);
            return (percentage / 100).ToString("P", cultureInfo);
        }
        
        #endregion
        
        #region Phone Number Formatting
        
        public string FormatPhoneNumber(string phoneNumber, string? culture = null)
        {
            if (string.IsNullOrWhiteSpace(phoneNumber))
                return string.Empty;
            
            try
            {
                // Extract digits only
                var digitsOnly = PhoneDigitsOnly.Replace(phoneNumber, "");
                
                // Handle different phone number lengths
                if (digitsOnly.Length == 10)
                {
                    // Format as (XXX) XXX-XXXX
                    return $"({digitsOnly.Substring(0, 3)}) {digitsOnly.Substring(3, 3)}-{digitsOnly.Substring(6, 4)}";
                }
                else if (digitsOnly.Length == 11 && digitsOnly.StartsWith("1"))
                {
                    // Format as +1 (XXX) XXX-XXXX
                    return $"+1 ({digitsOnly.Substring(1, 3)}) {digitsOnly.Substring(4, 3)}-{digitsOnly.Substring(7, 4)}";
                }
                else
                {
                    // Return original if we can't format it
                    _logger.LogWarning("Unable to format phone number: {PhoneNumber}", phoneNumber);
                    return phoneNumber;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error formatting phone number: {PhoneNumber}", phoneNumber);
                return phoneNumber;
            }
        }
        
        public string ParsePhoneNumber(string phoneNumber)
        {
            if (string.IsNullOrWhiteSpace(phoneNumber))
                return string.Empty;
            
            // Extract digits only and normalize to E.164 format for storage
            var digitsOnly = PhoneDigitsOnly.Replace(phoneNumber, "");
            
            if (digitsOnly.Length == 10)
            {
                // Add Canadian country code
                return $"+1{digitsOnly}";
            }
            else if (digitsOnly.Length == 11 && digitsOnly.StartsWith("1"))
            {
                // Already has country code
                return $"+{digitsOnly}";
            }
            
            return phoneNumber; // Return original if we can't parse it
        }
        
        public bool IsValidPhoneNumber(string phoneNumber, string? culture = null)
        {
            if (string.IsNullOrWhiteSpace(phoneNumber))
                return false;
            
            // For Canadian phone numbers
            return CanadianPhoneRegex.IsMatch(phoneNumber);
        }
        
        #endregion
        
        #region Postal Code Formatting
        
        public string FormatPostalCode(string postalCode, string? culture = null)
        {
            if (string.IsNullOrWhiteSpace(postalCode))
                return string.Empty;
            
            try
            {
                // Remove spaces and convert to uppercase
                var cleaned = postalCode.Replace(" ", "").Replace("-", "").ToUpperInvariant();
                
                if (cleaned.Length == 6 && IsValidPostalCode(cleaned))
                {
                    // Format as A1A 1A1
                    return $"{cleaned.Substring(0, 3)} {cleaned.Substring(3, 3)}";
                }
                
                return postalCode; // Return original if we can't format it
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error formatting postal code: {PostalCode}", postalCode);
                return postalCode;
            }
        }
        
        public string ParsePostalCode(string postalCode)
        {
            if (string.IsNullOrWhiteSpace(postalCode))
                return string.Empty;
            
            // Remove spaces and convert to uppercase for storage
            return postalCode.Replace(" ", "").Replace("-", "").ToUpperInvariant();
        }
        
        public bool IsValidPostalCode(string postalCode, string? culture = null)
        {
            if (string.IsNullOrWhiteSpace(postalCode))
                return false;
            
            // For Canadian postal codes
            return CanadianPostalCodeRegex.IsMatch(postalCode);
        }
        
        #endregion
        
        #region Helper Methods
        
        private CultureInfo GetCultureInfo(string? culture)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(culture))
                    culture = DefaultCulture;
                
                return new CultureInfo(culture);
            }
            catch (CultureNotFoundException ex)
            {
                _logger.LogWarning(ex, "Invalid culture specified: {Culture}. Using default culture: {DefaultCulture}", 
                    culture, DefaultCulture);
                return new CultureInfo(DefaultCulture);
            }
        }
        
        #endregion
    }
}