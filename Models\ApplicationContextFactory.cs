using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Design;
using System;

namespace ParaHockeyApp.Models
{
    public class ApplicationContextFactory : IDesignTimeDbContextFactory<ApplicationContext>
    {
        public ApplicationContext CreateDbContext(string[] args)
        {
            // Hardcode the TEST connection string for design-time tools
            var connectionString = "Server=SIMBA\\SQLEXPRESS;User Id=ParaHockeyUser;Password=***************;Database=ParaHockeyDB_TEST;Encrypt=False;TrustServerCertificate=True;";

            if (string.IsNullOrEmpty(connectionString))
            {
                throw new InvalidOperationException("Hardcoded connection string is empty.");
            }

            var builder = new DbContextOptionsBuilder<ApplicationContext>();
            builder.UseSqlServer(connectionString);

            return new ApplicationContext(builder.Options);
        }
    }
}