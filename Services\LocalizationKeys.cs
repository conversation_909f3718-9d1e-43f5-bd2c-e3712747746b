using System;

namespace ParaHockeyApp.Services
{
    /// <summary>
    /// Centralized localization keys with semantic organization for maintainable translations.
    /// Provides strongly-typed access to resource keys to prevent typos and improve maintainability.
    /// </summary>
    public static class LocalizationKeys
    {
        /// <summary>
        /// Form-related localization keys organized by entity and field
        /// </summary>
        public static class Forms
        {
            public static class Member
            {
                // Personal Information
                public const string FirstNameLabel = "Form.Member.FirstName.Label";
                public const string FirstNamePlaceholder = "Form.Member.FirstName.Placeholder";
                public const string FirstNameHelp = "Form.Member.FirstName.Help";
                
                public const string LastNameLabel = "Form.Member.LastName.Label";
                public const string LastNamePlaceholder = "Form.Member.LastName.Placeholder";
                public const string LastNameHelp = "Form.Member.LastName.Help";
                
                public const string EmailLabel = "Form.Member.Email.Label";
                public const string EmailPlaceholder = "Form.Member.Email.Placeholder";
                public const string EmailHelp = "Form.Member.Email.Help";
                
                public const string PhoneLabel = "Form.Member.Phone.Label";
                public const string PhonePlaceholder = "Form.Member.Phone.Placeholder";
                public const string PhoneHelp = "Form.Member.Phone.Help";
                
                public const string BirthDateLabel = "Form.Member.BirthDate.Label";
                public const string BirthDatePlaceholder = "Form.Member.BirthDate.Placeholder";
                public const string BirthDateHelp = "Form.Member.BirthDate.Help";
                
                // Address Information
                public const string AddressLabel = "Form.Member.Address.Label";
                public const string AddressPlaceholder = "Form.Member.Address.Placeholder";
                
                public const string CityLabel = "Form.Member.City.Label";
                public const string CityPlaceholder = "Form.Member.City.Placeholder";
                
                public const string ProvinceLabel = "Form.Member.Province.Label";
                public const string ProvincePlaceholder = "Form.Member.Province.Placeholder";
                
                public const string PostalCodeLabel = "Form.Member.PostalCode.Label";
                public const string PostalCodePlaceholder = "Form.Member.PostalCode.Placeholder";
                public const string PostalCodeHelp = "Form.Member.PostalCode.Help";
                
                // Emergency Contact
                public const string EmergencyContactNameLabel = "Form.Member.EmergencyContactName.Label";
                public const string EmergencyContactPhoneLabel = "Form.Member.EmergencyContactPhone.Label";
                
                // Form Actions
                public const string SubmitButton = "Form.Member.Submit.Button";
                public const string CancelButton = "Form.Member.Cancel.Button";
                public const string SaveButton = "Form.Member.Save.Button";
                public const string EditButton = "Form.Member.Edit.Button";
            }
            
            public static class Admin
            {
                public const string SearchLabel = "Form.Admin.Search.Label";
                public const string SearchPlaceholder = "Form.Admin.Search.Placeholder";
                public const string FilterButton = "Form.Admin.Filter.Button";
                public const string ClearFiltersButton = "Form.Admin.ClearFilters.Button";
                public const string ExportButton = "Form.Admin.Export.Button";
                
                // Member Management
                public const string MemberStatusLabel = "Form.Admin.MemberStatus.Label";
                public const string MemberTypeLabel = "Form.Admin.MemberType.Label";
                public const string RegistrationDateLabel = "Form.Admin.RegistrationDate.Label";
            }
            
            public static class Event
            {
                public const string EventNameLabel = "Form.Event.EventName.Label";
                public const string EventDateLabel = "Form.Event.EventDate.Label";
                public const string EventTimeLabel = "Form.Event.EventTime.Label";
                public const string EventLocationLabel = "Form.Event.EventLocation.Label";
                public const string EventDescriptionLabel = "Form.Event.EventDescription.Label";
                public const string MaxParticipantsLabel = "Form.Event.MaxParticipants.Label";
                public const string RegistrationDeadlineLabel = "Form.Event.RegistrationDeadline.Label";
            }
            
            public static class Authentication
            {
                public const string UsernameLabel = "Form.Auth.Username.Label";
                public const string PasswordLabel = "Form.Auth.Password.Label";
                public const string RememberMeLabel = "Form.Auth.RememberMe.Label";
                public const string LoginButton = "Form.Auth.Login.Button";
                public const string LogoutButton = "Form.Auth.Logout.Button";
                public const string ForgotPasswordLink = "Form.Auth.ForgotPassword.Link";
            }
        }
        
        /// <summary>
        /// Validation error message keys organized by validation type
        /// </summary>
        public static class Validation
        {
            // Generic validation messages
            public const string Required = "Validation.Required";
            public const string StringLength = "Validation.StringLength";
            public const string Range = "Validation.Range";
            public const string Compare = "Validation.Compare";
            
            // Format-specific validation
            public const string EmailFormat = "Validation.Email.Format";
            public const string PhoneFormat = "Validation.Phone.Format";
            public const string PostalCodeFormat = "Validation.PostalCode.Format";
            public const string DateFormat = "Validation.Date.Format";
            public const string TimeFormat = "Validation.Time.Format";
            
            // Business rule validation
            public const string MinimumAge = "Validation.Member.MinimumAge";
            public const string DuplicateEmail = "Validation.Member.DuplicateEmail";
            public const string InvalidMembershipType = "Validation.Member.InvalidMembershipType";
            public const string EventCapacityExceeded = "Validation.Event.CapacityExceeded";
            public const string RegistrationDeadlinePassed = "Validation.Event.RegistrationDeadlinePassed";
            
            // Security validation
            public const string InvalidCredentials = "Validation.Auth.InvalidCredentials";
            public const string AccountLocked = "Validation.Auth.AccountLocked";
            public const string SessionExpired = "Validation.Auth.SessionExpired";
        }
        
        /// <summary>
        /// User interface text keys for buttons, labels, and navigation
        /// </summary>
        public static class UI
        {
            // Navigation
            public const string HomeNavigation = "UI.Navigation.Home";
            public const string MembersNavigation = "UI.Navigation.Members";
            public const string EventsNavigation = "UI.Navigation.Events";
            public const string AdminNavigation = "UI.Navigation.Admin";
            public const string ProfileNavigation = "UI.Navigation.Profile";
            
            // Common Actions
            public const string Save = "UI.Action.Save";
            public const string Cancel = "UI.Action.Cancel";
            public const string Delete = "UI.Action.Delete";
            public const string Edit = "UI.Action.Edit";
            public const string View = "UI.Action.View";
            public const string Search = "UI.Action.Search";
            public const string Filter = "UI.Action.Filter";
            public const string Export = "UI.Action.Export";
            public const string Import = "UI.Action.Import";
            public const string Print = "UI.Action.Print";
            
            // Status Messages
            public const string Loading = "UI.Status.Loading";
            public const string Saving = "UI.Status.Saving";
            public const string Success = "UI.Status.Success";
            public const string Error = "UI.Status.Error";
            public const string Warning = "UI.Status.Warning";
            public const string Info = "UI.Status.Info";
            
            // Empty States
            public const string NoResults = "UI.EmptyState.NoResults";
            public const string NoMembers = "UI.EmptyState.NoMembers";
            public const string NoEvents = "UI.EmptyState.NoEvents";
            public const string NoData = "UI.EmptyState.NoData";
            
            // Confirmation Messages
            public const string ConfirmDelete = "UI.Confirm.Delete";
            public const string ConfirmCancel = "UI.Confirm.Cancel";
            public const string ConfirmLogout = "UI.Confirm.Logout";
            public const string UnsavedChanges = "UI.Confirm.UnsavedChanges";
        }
        
        /// <summary>
        /// Page titles and headings organized by controller/action
        /// </summary>
        public static class Pages
        {
            public static class Home
            {
                public const string IndexTitle = "Page.Home.Index.Title";
                public const string IndexHeading = "Page.Home.Index.Heading";
                public const string WelcomeMessage = "Page.Home.Welcome.Message";
                public const string FeaturesHeading = "Page.Home.Features.Heading";
            }
            
            public static class Members
            {
                public const string RegisterTitle = "Page.Members.Register.Title";
                public const string RegisterHeading = "Page.Members.Register.Heading";
                public const string LoginTitle = "Page.Members.Login.Title";
                public const string LoginHeading = "Page.Members.Login.Heading";
                public const string DashboardTitle = "Page.Members.Dashboard.Title";
                public const string DashboardHeading = "Page.Members.Dashboard.Heading";
                public const string ProfileTitle = "Page.Members.Profile.Title";
                public const string ProfileHeading = "Page.Members.Profile.Heading";
            }
            
            public static class Admin
            {
                public const string DashboardTitle = "Page.Admin.Dashboard.Title";
                public const string DashboardHeading = "Page.Admin.Dashboard.Heading";
                public const string MembersTitle = "Page.Admin.Members.Title";
                public const string MembersHeading = "Page.Admin.Members.Heading";
                public const string MemberDetailsTitle = "Page.Admin.MemberDetails.Title";
                public const string MemberDetailsHeading = "Page.Admin.MemberDetails.Heading";
                public const string SystemInfoTitle = "Page.Admin.SystemInfo.Title";
                public const string SystemInfoHeading = "Page.Admin.SystemInfo.Heading";
            }
            
            public static class Events
            {
                public const string CalendarTitle = "Page.Events.Calendar.Title";
                public const string CalendarHeading = "Page.Events.Calendar.Heading";
                public const string SubscribeTitle = "Page.Events.Subscribe.Title";
                public const string SubscribeHeading = "Page.Events.Subscribe.Heading";
                public const string EventDetailsTitle = "Page.Events.Details.Title";
                public const string EventDetailsHeading = "Page.Events.Details.Heading";
            }
        }
        
        /// <summary>
        /// Error messages organized by category and severity
        /// </summary>
        public static class Errors
        {
            // System Errors
            public const string SystemError = "Error.System.Generic";
            public const string DatabaseError = "Error.System.Database";
            public const string NetworkError = "Error.System.Network";
            public const string TimeoutError = "Error.System.Timeout";
            
            // User Errors
            public const string NotFound = "Error.User.NotFound";
            public const string AccessDenied = "Error.User.AccessDenied";
            public const string InvalidOperation = "Error.User.InvalidOperation";
            public const string DuplicateEntry = "Error.User.DuplicateEntry";
            
            // Validation Errors
            public const string ValidationFailed = "Error.Validation.Failed";
            public const string RequiredFieldMissing = "Error.Validation.RequiredField";
            public const string InvalidFormat = "Error.Validation.InvalidFormat";
            public const string ValueOutOfRange = "Error.Validation.ValueOutOfRange";
            
            // Authentication Errors
            public const string LoginFailed = "Error.Auth.LoginFailed";
            public const string SessionExpired = "Error.Auth.SessionExpired";
            public const string InsufficientPermissions = "Error.Auth.InsufficientPermissions";
        }
        
        /// <summary>
        /// Success messages for user feedback
        /// </summary>
        public static class Success
        {
            // Member Operations
            public const string MemberRegistered = "Success.Member.Registered";
            public const string MemberUpdated = "Success.Member.Updated";
            public const string MemberDeleted = "Success.Member.Deleted";
            
            // Event Operations
            public const string EventCreated = "Success.Event.Created";
            public const string EventUpdated = "Success.Event.Updated";
            public const string EventRegistered = "Success.Event.Registered";
            public const string EventCancelled = "Success.Event.Cancelled";
            
            // System Operations
            public const string DataExported = "Success.System.DataExported";
            public const string DataImported = "Success.System.DataImported";
            public const string SettingsSaved = "Success.System.SettingsSaved";
            public const string EmailSent = "Success.System.EmailSent";
        }
        
        /// <summary>
        /// Accessibility-related text for screen readers and assistive technologies
        /// </summary>
        public static class Accessibility
        {
            // ARIA Labels
            public const string MainNavigation = "A11y.MainNavigation";
            public const string SearchForm = "A11y.SearchForm";
            public const string DataTable = "A11y.DataTable";
            public const string SortColumn = "A11y.SortColumn";
            public const string FilterPanel = "A11y.FilterPanel";
            
            // Screen Reader Text
            public const string SkipToContent = "A11y.SkipToContent";
            public const string SkipToNavigation = "A11y.SkipToNavigation";
            public const string CurrentPage = "A11y.CurrentPage";
            public const string ExternalLink = "A11y.ExternalLink";
            public const string NewWindow = "A11y.NewWindow";
            
            // Form Accessibility
            public const string RequiredField = "A11y.RequiredField";
            public const string OptionalField = "A11y.OptionalField";
            public const string ErrorMessage = "A11y.ErrorMessage";
            public const string HelpText = "A11y.HelpText";
            
            // Interactive Elements
            public const string ButtonExpanded = "A11y.Button.Expanded";
            public const string ButtonCollapsed = "A11y.Button.Collapsed";
            public const string MenuOpen = "A11y.Menu.Open";
            public const string MenuClosed = "A11y.Menu.Closed";
        }
        
        /// <summary>
        /// Date and time formatting keys for culture-specific display
        /// </summary>
        public static class DateTimeKeys
        {
            public const string ShortDate = "DateTime.Format.ShortDate";
            public const string LongDate = "DateTime.Format.LongDate";
            public const string ShortTime = "DateTime.Format.ShortTime";
            public const string LongTime = "DateTime.Format.LongTime";
            public const string DateTime = "DateTime.Format.DateTime";
            public const string RelativeTime = "DateTime.Format.Relative";
            
            // Date Range Labels
            public const string Today = "DateTime.Label.Today";
            public const string Yesterday = "DateTime.Label.Yesterday";
            public const string Tomorrow = "DateTime.Label.Tomorrow";
            public const string ThisWeek = "DateTime.Label.ThisWeek";
            public const string NextWeek = "DateTime.Label.NextWeek";
            public const string ThisMonth = "DateTime.Label.ThisMonth";
            public const string NextMonth = "DateTime.Label.NextMonth";
        }
    }
}