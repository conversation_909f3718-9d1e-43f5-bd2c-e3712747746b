/* ParaHockey Design System - Form Components */

/* ===== FORM CONTROLS ===== */
.ph-form-control {
    display: block;
    width: 100%;
    padding: var(--ph-input-padding-y) var(--ph-input-padding-x);
    font-family: var(--ph-font-family-base);
    font-size: var(--ph-font-size-base);
    font-weight: var(--ph-font-weight-normal);
    line-height: 1.5;
    color: var(--ph-gray-900);
    background-color: var(--ph-white);
    background-image: none;
    border: var(--ph-input-border-width) solid var(--ph-input-border-color);
    border-radius: var(--ph-input-border-radius);
    transition: border-color var(--ph-transition-fast), box-shadow var(--ph-transition-fast);
    appearance: none;
    min-height: var(--ph-input-height);
}

.ph-form-control:focus {
    color: var(--ph-gray-900);
    background-color: var(--ph-white);
    border-color: var(--ph-input-focus-border-color);
    outline: 0;
    box-shadow: var(--ph-input-focus-box-shadow);
}

.ph-form-control:disabled,
.ph-form-control[readonly] {
    background-color: var(--ph-gray-100);
    opacity: 1;
}

.ph-form-control:disabled {
    cursor: not-allowed;
}

.ph-form-control::placeholder {
    color: var(--ph-gray-500);
    opacity: 1;
}

/* Form Control Sizes */
.ph-form-control-sm {
    min-height: 2rem;
    padding: 0.25rem 0.5rem;
    font-size: var(--ph-font-size-sm);
    border-radius: var(--ph-radius-sm);
}

.ph-form-control-lg {
    min-height: 3rem;
    padding: 0.75rem 1rem;
    font-size: var(--ph-font-size-lg);
    border-radius: var(--ph-radius-lg);
}

/* ===== SELECT ===== */
.ph-form-select {
    display: block;
    width: 100%;
    padding: var(--ph-input-padding-y) calc(var(--ph-input-padding-x) * 3) var(--ph-input-padding-y) var(--ph-input-padding-x);
    font-family: var(--ph-font-family-base);
    font-size: var(--ph-font-size-base);
    font-weight: var(--ph-font-weight-normal);
    line-height: 1.5;
    color: var(--ph-gray-900);
    background-color: var(--ph-white);
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'%3e%3cpath fill='none' stroke='%23343a40' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='m1 6 7 7 7-7'/%3e%3c/svg%3e");
    background-repeat: no-repeat;
    background-position: right var(--ph-input-padding-x) center;
    background-size: 16px 12px;
    border: var(--ph-input-border-width) solid var(--ph-input-border-color);
    border-radius: var(--ph-input-border-radius);
    transition: border-color var(--ph-transition-fast), box-shadow var(--ph-transition-fast);
    appearance: none;
    min-height: var(--ph-input-height);
}

.ph-form-select:focus {
    border-color: var(--ph-input-focus-border-color);
    outline: 0;
    box-shadow: var(--ph-input-focus-box-shadow);
}

.ph-form-select:disabled {
    background-color: var(--ph-gray-100);
    cursor: not-allowed;
}

/* ===== FORM LABELS ===== */
.ph-form-label {
    margin-bottom: var(--ph-spacing-sm);
    font-size: var(--ph-font-size-base);
    font-weight: var(--ph-font-weight-medium);
    color: var(--ph-gray-900);
    display: block;
}

.ph-form-label.required::after {
    content: " *";
    color: var(--ph-danger);
    font-weight: var(--ph-font-weight-bold);
}

/* ===== FORM TEXT ===== */
.ph-form-text {
    margin-top: var(--ph-spacing-xs);
    font-size: var(--ph-font-size-sm);
    color: var(--ph-gray-600);
}

/* ===== FORM VALIDATION ===== */
.ph-form-control.is-valid {
    border-color: var(--ph-success);
    padding-right: calc(1.5em + 0.75rem);
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 8 8'%3e%3cpath fill='%23198754' d='m2.3 6.73.94-.94 1.38 1.38L7.7 4.08 6.76 3.14 4.25 5.65z'/%3e%3c/svg%3e");
    background-repeat: no-repeat;
    background-position: right calc(0.375em + 0.1875rem) center;
    background-size: calc(0.75em + 0.375rem) calc(0.75em + 0.375rem);
}

.ph-form-control.is-valid:focus {
    border-color: var(--ph-success);
    box-shadow: 0 0 0 var(--ph-focus-ring-width) rgba(25, 135, 84, 0.25);
}

.ph-form-control.is-invalid {
    border-color: var(--ph-danger);
    padding-right: calc(1.5em + 0.75rem);
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 12 12' width='12' height='12' fill='none' stroke='%23dc3545'%3e%3ccircle cx='6' cy='6' r='4.5'/%3e%3cpath d='m5.8 4.6 1.4 1.4-1.4 1.4'/%3e%3c/svg%3e");
    background-repeat: no-repeat;
    background-position: right calc(0.375em + 0.1875rem) center;
    background-size: calc(0.75em + 0.375rem) calc(0.75em + 0.375rem);
}

.ph-form-control.is-invalid:focus {
    border-color: var(--ph-danger);
    box-shadow: 0 0 0 var(--ph-focus-ring-width) rgba(220, 53, 69, 0.25);
}

.ph-valid-feedback {
    display: none;
    width: 100%;
    margin-top: var(--ph-spacing-xs);
    font-size: var(--ph-font-size-sm);
    color: var(--ph-success);
}

.ph-invalid-feedback {
    display: none;
    width: 100%;
    margin-top: var(--ph-spacing-xs);
    font-size: var(--ph-font-size-sm);
    color: var(--ph-danger);
}

.ph-form-control.is-valid ~ .ph-valid-feedback,
.ph-form-control.is-valid ~ .ph-valid-tooltip {
    display: block;
}

.ph-form-control.is-invalid ~ .ph-invalid-feedback,
.ph-form-control.is-invalid ~ .ph-invalid-tooltip {
    display: block;
}

/* ===== CHECKBOXES AND RADIOS ===== */
.ph-form-check {
    display: block;
    min-height: 1.5rem;
    padding-left: 1.5em;
    margin-bottom: var(--ph-spacing-xs);
}

.ph-form-check .ph-form-check-input {
    float: left;
    margin-left: -1.5em;
}

.ph-form-check-input {
    width: 1em;
    height: 1em;
    margin-top: 0.25em;
    vertical-align: top;
    background-color: var(--ph-white);
    background-repeat: no-repeat;
    background-position: center;
    background-size: contain;
    border: 1px solid var(--ph-gray-400);
    appearance: none;
    color-adjust: exact;
    transition: background-color var(--ph-transition-fast), border-color var(--ph-transition-fast), box-shadow var(--ph-transition-fast);
}

.ph-form-check-input[type="checkbox"] {
    border-radius: var(--ph-radius-sm);
}

.ph-form-check-input[type="radio"] {
    border-radius: 50%;
}

.ph-form-check-input:active {
    filter: brightness(90%);
}

.ph-form-check-input:focus {
    border-color: var(--ph-primary);
    outline: 0;
    box-shadow: 0 0 0 var(--ph-focus-ring-width) var(--ph-focus-ring-color);
}

.ph-form-check-input:checked {
    background-color: var(--ph-primary);
    border-color: var(--ph-primary);
}

.ph-form-check-input:checked[type="checkbox"] {
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 20 20'%3e%3cpath fill='none' stroke='%23fff' stroke-linecap='round' stroke-linejoin='round' stroke-width='3' d='m6 10 3 3 6-6'/%3e%3c/svg%3e");
}

.ph-form-check-input:checked[type="radio"] {
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'%3e%3ccircle r='2' fill='%23fff'/%3e%3c/svg%3e");
}

.ph-form-check-input[type="checkbox"]:indeterminate {
    background-color: var(--ph-primary);
    border-color: var(--ph-primary);
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 20 20'%3e%3cpath fill='none' stroke='%23fff' stroke-linecap='round' stroke-linejoin='round' stroke-width='3' d='M6 10h8'/%3e%3c/svg%3e");
}

.ph-form-check-input:disabled {
    pointer-events: none;
    filter: none;
    opacity: 0.5;
}

.ph-form-check-input:disabled ~ .ph-form-check-label,
.ph-form-check-input[disabled] ~ .ph-form-check-label {
    opacity: 0.5;
}

.ph-form-check-label {
    color: var(--ph-gray-900);
    cursor: pointer;
}

/* Inline checkboxes and radios */
.ph-form-check-inline {
    display: inline-block;
    margin-right: 1rem;
}

/* ===== FORM GROUPS ===== */
.ph-form-group {
    margin-bottom: var(--ph-spacing-base);
}

.ph-form-row {
    display: flex;
    flex-wrap: wrap;
    margin-right: -5px;
    margin-left: -5px;
}

.ph-form-row > .ph-col,
.ph-form-row > [class*="ph-col-"] {
    padding-right: 5px;
    padding-left: 5px;
}

/* ===== INPUT GROUPS ===== */
.ph-input-group {
    position: relative;
    display: flex;
    flex-wrap: wrap;
    align-items: stretch;
    width: 100%;
}

.ph-input-group > .ph-form-control,
.ph-input-group > .ph-form-select {
    position: relative;
    flex: 1 1 auto;
    width: 1%;
    min-width: 0;
}

.ph-input-group > .ph-form-control:focus,
.ph-input-group > .ph-form-select:focus {
    z-index: 3;
}

.ph-input-group-text {
    display: flex;
    align-items: center;
    padding: var(--ph-input-padding-y) var(--ph-input-padding-x);
    font-size: var(--ph-font-size-base);
    font-weight: var(--ph-font-weight-normal);
    line-height: 1.5;
    color: var(--ph-gray-700);
    text-align: center;
    white-space: nowrap;
    background-color: var(--ph-gray-100);
    border: var(--ph-input-border-width) solid var(--ph-input-border-color);
    border-radius: var(--ph-input-border-radius);
}

.ph-input-group-prepend {
    margin-right: -1px;
}

.ph-input-group-append {
    margin-left: -1px;
}

.ph-input-group-prepend .ph-input-group-text {
    border-top-right-radius: 0;
    border-bottom-right-radius: 0;
}

.ph-input-group-append .ph-input-group-text {
    border-top-left-radius: 0;
    border-bottom-left-radius: 0;
}

.ph-input-group-prepend + .ph-form-control,
.ph-input-group-prepend + .ph-form-select {
    border-top-left-radius: 0;
    border-bottom-left-radius: 0;
}

.ph-form-control + .ph-input-group-append .ph-input-group-text,
.ph-form-select + .ph-input-group-append .ph-input-group-text {
    border-top-left-radius: 0;
    border-bottom-left-radius: 0;
}

/* ===== FLOATING LABELS ===== */
.ph-form-floating {
    position: relative;
}

.ph-form-floating > .ph-form-control,
.ph-form-floating > .ph-form-select {
    height: calc(3.5rem + 2px);
    line-height: 1.25;
    padding: 1rem 0.75rem;
}

.ph-form-floating > label {
    position: absolute;
    top: 0;
    left: 0;
    height: 100%;
    padding: 1rem 0.75rem;
    pointer-events: none;
    border: 1px solid transparent;
    transform-origin: 0 0;
    transition: opacity var(--ph-transition-fast), transform var(--ph-transition-fast);
}

.ph-form-floating > .ph-form-control::placeholder {
    color: transparent;
}

.ph-form-floating > .ph-form-control:focus,
.ph-form-floating > .ph-form-control:not(:placeholder-shown),
.ph-form-floating > .ph-form-select {
    padding-top: 1.625rem;
    padding-bottom: 0.625rem;
}

.ph-form-floating > .ph-form-control:focus ~ label,
.ph-form-floating > .ph-form-control:not(:placeholder-shown) ~ label,
.ph-form-floating > .ph-form-select ~ label {
    opacity: 0.65;
    transform: scale(0.85) translateY(-0.5rem) translateX(0.15rem);
}

/* ===== SWITCHES ===== */
.ph-form-switch {
    padding-left: 2.5em;
}

.ph-form-switch .ph-form-check-input {
    width: 2em;
    margin-left: -2.5em;
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'%3e%3ccircle r='3' fill='rgba%2833, 37, 41, 0.25%29'/%3e%3c/svg%3e");
    background-position: left center;
    border-radius: 2em;
    transition: background-position var(--ph-transition-fast);
}

.ph-form-switch .ph-form-check-input:focus {
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'%3e%3ccircle r='3' fill='%2386b7fe'/%3e%3c/svg%3e");
}

.ph-form-switch .ph-form-check-input:checked {
    background-position: right center;
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'%3e%3ccircle r='3' fill='%23fff'/%3e%3c/svg%3e");
}

/* ===== FORM VALIDATION SUMMARY ===== */
.ph-validation-summary {
    background-color: var(--ph-danger-light);
    border: 1px solid var(--ph-danger);
    color: var(--ph-danger-dark);
    padding: var(--ph-spacing-base) var(--ph-spacing-lg);
    border-radius: var(--ph-radius-md);
    margin-bottom: var(--ph-spacing-base);
}

.ph-validation-summary h4 {
    color: var(--ph-danger-dark);
    font-size: var(--ph-font-size-base);
    font-weight: var(--ph-font-weight-semibold);
    margin-bottom: var(--ph-spacing-sm);
}

.ph-validation-summary ul {
    margin-bottom: 0;
    padding-left: var(--ph-spacing-lg);
}

.ph-validation-summary li {
    margin-bottom: var(--ph-spacing-xs);
}

/* ===== RESPONSIVE FORM STYLES ===== */
@media (max-width: 767.98px) {
    .ph-form-control,
    .ph-form-select {
        font-size: 16px; /* Prevents zoom on iOS */
        min-height: 2.75rem; /* Larger touch targets */
    }
    
    .ph-form-check-input {
        width: 1.25em;
        height: 1.25em;
    }
    
    .ph-form-check {
        padding-left: 1.75em;
    }
    
    .ph-form-check .ph-form-check-input {
        margin-left: -1.75em;
    }
    
    .ph-btn {
        min-height: 2.75rem;
        padding: 0.75rem 1rem;
    }
}

/* ===== ACCESSIBILITY IMPROVEMENTS ===== */
@media (prefers-reduced-motion: reduce) {
    .ph-form-control,
    .ph-form-select,
    .ph-form-check-input {
        transition: none;
    }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    .ph-form-control,
    .ph-form-select {
        border-width: 2px;
    }
    
    .ph-form-check-input {
        border-width: 2px;
    }
}