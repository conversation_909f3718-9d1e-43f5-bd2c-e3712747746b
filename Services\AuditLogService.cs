using Microsoft.EntityFrameworkCore;
using ParaHockeyApp.Models;
using ParaHockeyApp.Models.Entities;

namespace ParaHockeyApp.Services
{
    public class AuditLogService : IAuditLogService
    {
        private readonly ApplicationContext _context;

        public AuditLogService(ApplicationContext context)
        {
            _context = context;
        }

        public async Task<List<AuditLog>> GetEntityAuditLogsAsync(string entityType, int entityId)
        {
            return await _context.AuditLogs
                .Where(al => al.EntityType == entityType && al.EntityId == entityId)
                .OrderByDescending(al => al.Timestamp)
                .ToListAsync();
        }

        public async Task<List<AuditLog>> GetRecentAuditLogsAsync(int pageNumber = 1, int pageSize = 50)
        {
            return await _context.AuditLogs
                .OrderByDescending(al => al.Timestamp)
                .Skip((pageNumber - 1) * pageSize)
                .Take(pageSize)
                .ToListAsync();
        }

        public async Task<List<AuditLog>> GetAuditLogsByPerformerAsync(int? adminId, int? memberId, int pageNumber = 1, int pageSize = 50)
        {
            var query = _context.AuditLogs.AsQueryable();

            if (adminId.HasValue)
            {
                query = query.Where(al => al.PerformedByAdminId == adminId);
            }
            else if (memberId.HasValue)
            {
                query = query.Where(al => al.PerformedByMemberId == memberId);
            }

            return await query
                .OrderByDescending(al => al.Timestamp)
                .Skip((pageNumber - 1) * pageSize)
                .Take(pageSize)
                .ToListAsync();
        }

        public async Task<AuditSummary> GetEntityAuditSummaryAsync(string entityType, int entityId)
        {
            var logs = await GetEntityAuditLogsAsync(entityType, entityId);
            
            var createLog = logs.FirstOrDefault(l => l.Action == "Create");
            var lastUpdateLog = logs.FirstOrDefault(l => l.Action == "Update");

            return new AuditSummary
            {
                DateCreated = createLog?.Timestamp,
                DateModified = lastUpdateLog?.Timestamp,
                CreatedBy = createLog?.PerformerName,
                ModifiedBy = lastUpdateLog?.PerformerName,
                CreatedBySource = createLog?.PerformedBySource,
                ModifiedBySource = lastUpdateLog?.PerformedBySource,
                TotalChanges = logs.Count(l => l.Action == "Update")
            };
        }

        public async Task<List<AuditLog>> GetMemberComprehensiveAuditLogsAsync(int memberId)
        {
            var allLogs = new List<AuditLog>();

            // Get direct member audit logs
            var memberLogs = await _context.AuditLogs
                .Where(al => al.EntityType == "Member" && al.EntityId == memberId)
                .ToListAsync();
            allLogs.AddRange(memberLogs);

            // Get parent audit logs for this member
            var parentIds = await _context.Parents
                .Where(p => p.MemberId == memberId)
                .Select(p => p.Id)
                .ToListAsync();

            if (parentIds.Any())
            {
                var parentLogs = await _context.AuditLogs
                    .Where(al => al.EntityType == "Parent" && parentIds.Contains(al.EntityId))
                    .ToListAsync();
                allLogs.AddRange(parentLogs);
            }

            // Get emergency contact audit logs for this member
            var emergencyContactIds = await _context.EmergencyContacts
                .Where(ec => ec.MemberId == memberId)
                .Select(ec => ec.Id)
                .ToListAsync();

            if (emergencyContactIds.Any())
            {
                var emergencyContactLogs = await _context.AuditLogs
                    .Where(al => al.EntityType == "EmergencyContact" && emergencyContactIds.Contains(al.EntityId))
                    .ToListAsync();
                allLogs.AddRange(emergencyContactLogs);
            }

            // Return all logs ordered by timestamp (newest first)
            return allLogs.OrderByDescending(al => al.Timestamp).ToList();
        }

        public async Task LogActionAsync(string description, Member? member, ActionSource source, string performerName, string? ipAddress)
        {
            var auditLog = new AuditLog
            {
                EntityType = member != null ? "Member" : "System",
                EntityId = member?.Id ?? 0,
                Action = "Admin Action",
                Description = description,
                Timestamp = DateTime.UtcNow,
                PerformedBySource = source,
                PerformerName = performerName,
                IPAddress = ipAddress,
                // For admin actions, we'll set the admin info when we have user context
                PerformedByAdminId = null, // Could be enhanced with admin context
                PerformedByMemberId = null,
                OldValues = null,
                NewValues = null
            };

            _context.AuditLogs.Add(auditLog);
            await _context.SaveChangesAsync();
        }

        public async Task LogMemberMergeAsync(int memberId, Dictionary<string, object?> originalValues, Dictionary<string, object?> newValues, string performedBy)
        {
            var changesList = new List<string>();
            
            foreach (var field in originalValues.Keys)
            {
                var oldValue = originalValues[field]?.ToString() ?? "";
                var newValue = newValues.ContainsKey(field) ? newValues[field]?.ToString() ?? "" : "";
                
                if (oldValue != newValue)
                {
                    changesList.Add($"{field}: '{oldValue}' → '{newValue}'");
                }
            }

            if (changesList.Any())
            {
                var changesDescription = string.Join(", ", changesList);
                var auditLog = new AuditLog
                {
                    EntityType = "Member",
                    EntityId = memberId,
                    Action = "Merge",
                    Description = $"Member merged from import data. Changes: {changesDescription}",
                    Timestamp = DateTime.UtcNow,
                    PerformerName = performedBy,
                    PerformedBySource = ActionSource.AdminPanel,
                    IPAddress = null // Will be populated by middleware if available
                };

                _context.AuditLogs.Add(auditLog);
                await _context.SaveChangesAsync();
            }
        }

    }
}