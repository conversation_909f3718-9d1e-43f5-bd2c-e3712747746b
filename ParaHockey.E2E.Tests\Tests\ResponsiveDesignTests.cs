using Xunit;
using FluentAssertions;
using ParaHockey.E2E.Tests.Infrastructure;
using ParaHockey.E2E.Tests.PageObjects;
using Microsoft.Extensions.Configuration;

namespace ParaHockey.E2E.Tests.Tests
{
    public class ResponsiveDesignTests : IDisposable
    {
        private readonly TestConfiguration _config;
        private readonly WebDriverFactory _driverFactory;
        private readonly MobileViewport[] _mobileViewports;

        public ResponsiveDesignTests()
        {
            _config = TestConfiguration.Load();
            _driverFactory = new WebDriverFactory(_config);

            // Load mobile viewports from configuration
            var configuration = new ConfigurationBuilder()
                .AddJsonFile("appsettings.Test.json", optional: false)
                .Build();

            var browserSettings = new BrowserSettings();
            configuration.GetSection("BrowserSettings").Bind(browserSettings);
            _mobileViewports = browserSettings.MobileViewports;
        }

        [Theory]
        [InlineData(1920, 1080)] // Desktop
        [InlineData(1366, 768)]  // Laptop
        [InlineData(768, 1024)]  // Tablet Portrait
        [InlineData(1024, 768)]  // Tablet Landscape
        [InlineData(390, 844)]   // iPhone 12
        [InlineData(360, 640)]   // Samsung Galaxy
        public void HomePage_ShouldBeResponsive_AtDifferentScreenSizes(int width, int height)
        {
            using var driver = _driverFactory.CreateDriver("Chrome");
            var wait = new OpenQA.Selenium.Support.UI.WebDriverWait(driver, TimeSpan.FromSeconds(_config.ExplicitWaitSeconds));
            var homePage = new HomePage(driver, wait);

            try
            {
                // Arrange
                driver.Manage().Window.Size = new System.Drawing.Size(width, height);
                driver.Navigate().GoToUrl(_config.BaseUrl);
                wait.Until(driver => ((OpenQA.Selenium.IJavaScriptExecutor)driver).ExecuteScript("return document.readyState").Equals("complete"));

                // Act & Assert
                homePage.IsLogoDisplayed().Should().BeTrue($"Logo should be visible at {width}x{height}");
                homePage.IsRegisterButtonDisplayed().Should().BeTrue($"Register button should be visible at {width}x{height}");

                // Check if navigation is accessible (may be collapsed on mobile)
                var navigationElements = driver.FindElements(OpenQA.Selenium.By.CssSelector(".navbar"));
                navigationElements.Should().NotBeEmpty($"Navigation should be present at {width}x{height}");

                // Check if content is not overlapping
                var bodyElement = driver.FindElement(OpenQA.Selenium.By.TagName("body"));
                var bodyHeight = bodyElement.Size.Height;
                bodyHeight.Should().BeGreaterThan(0, $"Body should have positive height at {width}x{height}");

                // Check if horizontal scroll is not needed (except for very narrow screens)
                if (width >= 320)
                {
                    var documentWidth = (long)((OpenQA.Selenium.IJavaScriptExecutor)driver).ExecuteScript("return document.body.scrollWidth");
                    var viewportWidth = (long)((OpenQA.Selenium.IJavaScriptExecutor)driver).ExecuteScript("return window.innerWidth");
                    
                    documentWidth.Should().BeLessOrEqualTo(viewportWidth + 20, $"Should not require horizontal scrolling at {width}x{height} (allowing 20px tolerance)");
                }
            }
            catch (Exception ex)
            {
                TakeScreenshot(driver, $"HomePage_Responsive_{width}x{height}");
                throw new Exception($"Responsive test failed at {width}x{height}: {ex.Message}", ex);
            }
        }

        [Theory]
        [InlineData(1920, 1080)] // Desktop
        [InlineData(768, 1024)]  // Tablet
        [InlineData(390, 844)]   // Mobile
        public void RegistrationForm_ShouldBeUsable_OnAllScreenSizes(int width, int height)
        {
            using var driver = _driverFactory.CreateDriver("Chrome");
            var wait = new OpenQA.Selenium.Support.UI.WebDriverWait(driver, TimeSpan.FromSeconds(_config.ExplicitWaitSeconds));
            var registrationPage = new RegistrationPage(driver, wait);

            try
            {
                // Arrange
                driver.Manage().Window.Size = new System.Drawing.Size(width, height);
                driver.Navigate().GoToUrl($"{_config.BaseUrl}/Members/Register");
                wait.Until(driver => ((OpenQA.Selenium.IJavaScriptExecutor)driver).ExecuteScript("return document.readyState").Equals("complete"));

                // Act & Assert - Check form fields are accessible
                var formFields = new[]
                {
                    "FirstName", "LastName", "DateOfBirth", "Address", 
                    "City", "PostalCode", "Phone", "Email"
                };

                foreach (var fieldName in formFields)
                {
                    var field = driver.FindElement(OpenQA.Selenium.By.Name(fieldName));
                    field.Displayed.Should().BeTrue($"Field {fieldName} should be visible at {width}x{height}");
                    field.Enabled.Should().BeTrue($"Field {fieldName} should be enabled at {width}x{height}");

                    // Check if field is not cut off
                    var fieldLocation = field.Location;
                    var fieldSize = field.Size;
                    
                    fieldLocation.X.Should().BeGreaterOrEqualTo(0, $"Field {fieldName} should not be cut off on the left at {width}x{height}");
                    (fieldLocation.X + fieldSize.Width).Should().BeLessOrEqualTo(width, $"Field {fieldName} should not be cut off on the right at {width}x{height}");
                }

                // Test form interaction
                registrationPage.FillBasicInformation("Mobile", "Test", "1990-01-01", "male");
                registrationPage.GetFirstNameValue().Should().Be("Mobile", $"Form input should work at {width}x{height}");

                // Check submit button is accessible
                var submitButton = driver.FindElement(OpenQA.Selenium.By.CssSelector("button[type='submit']"));
                submitButton.Displayed.Should().BeTrue($"Submit button should be visible at {width}x{height}");
                
                // Scroll to submit button to ensure it's accessible
                ((OpenQA.Selenium.IJavaScriptExecutor)driver).ExecuteScript("arguments[0].scrollIntoView(true);", submitButton);
                Thread.Sleep(500);
                
                submitButton.Enabled.Should().BeTrue($"Submit button should be enabled at {width}x{height}");
            }
            catch (Exception ex)
            {
                TakeScreenshot(driver, $"RegistrationForm_Responsive_{width}x{height}");
                throw new Exception($"Registration form responsive test failed at {width}x{height}: {ex.Message}", ex);
            }
        }

        [Fact]
        public void MobileNavigation_ShouldWork_OnSmallScreens()
        {
            using var driver = _driverFactory.CreateDriver("Chrome");
            var wait = new OpenQA.Selenium.Support.UI.WebDriverWait(driver, TimeSpan.FromSeconds(_config.ExplicitWaitSeconds));

            try
            {
                // Arrange - Set mobile viewport
                driver.Manage().Window.Size = new System.Drawing.Size(390, 844);
                driver.Navigate().GoToUrl(_config.BaseUrl);
                wait.Until(driver => ((OpenQA.Selenium.IJavaScriptExecutor)driver).ExecuteScript("return document.readyState").Equals("complete"));

                // Act & Assert - Check mobile navigation
                var navbarToggler = driver.FindElements(OpenQA.Selenium.By.CssSelector(".navbar-toggler"));
                
                if (navbarToggler.Any() && navbarToggler.First().Displayed)
                {
                    // Mobile navigation with hamburger menu
                    navbarToggler.First().Click();
                    Thread.Sleep(1000);

                    // Check if navigation menu is now visible
                    var navMenu = driver.FindElement(OpenQA.Selenium.By.CssSelector(".navbar-collapse"));
                    var navMenuClasses = navMenu.GetAttribute("class");
                    
                    // Menu should be expanded or visible
                    (navMenuClasses.Contains("show") || navMenuClasses.Contains("in")).Should().BeTrue("Mobile navigation menu should be expanded when toggler is clicked");
                }

                // Check language dropdown accessibility on mobile
                var languageDropdown = driver.FindElements(OpenQA.Selenium.By.Id("languageDropdown"));
                if (languageDropdown.Any())
                {
                    languageDropdown.First().Displayed.Should().BeTrue("Language dropdown should be accessible on mobile");
                }
            }
            catch (Exception ex)
            {
                TakeScreenshot(driver, "MobileNavigation_ShouldWork");
                throw;
            }
        }

        [Fact]
        public void TouchInteractions_ShouldWork_OnMobileDevices()
        {
            using var driver = _driverFactory.CreateDriver("Chrome");
            var wait = new OpenQA.Selenium.Support.UI.WebDriverWait(driver, TimeSpan.FromSeconds(_config.ExplicitWaitSeconds));

            try
            {
                // Arrange - Set mobile viewport and enable touch events
                driver.Manage().Window.Size = new System.Drawing.Size(390, 844);
                
                // Enable mobile emulation
                var mobileEmulation = new Dictionary<string, object>
                {
                    ["deviceMetrics"] = new Dictionary<string, object>
                    {
                        ["width"] = 390,
                        ["height"] = 844,
                        ["pixelRatio"] = 3.0
                    },
                    ["userAgent"] = "Mozilla/5.0 (iPhone; CPU iPhone OS 14_7_1 like Mac OS X) AppleWebKit/605.1.15"
                };

                driver.Navigate().GoToUrl($"{_config.BaseUrl}/Members/Register");
                wait.Until(driver => ((OpenQA.Selenium.IJavaScriptExecutor)driver).ExecuteScript("return document.readyState").Equals("complete"));

                // Act & Assert - Test touch interactions
                
                // Test dropdown selection (Province)
                var provinceDropdown = driver.FindElement(OpenQA.Selenium.By.Name("ProvinceId"));
                provinceDropdown.Click();
                Thread.Sleep(500);
                
                var qcOption = driver.FindElement(OpenQA.Selenium.By.CssSelector("option[value='QC']"));
                qcOption.Click();
                
                provinceDropdown.GetAttribute("value").Should().Be("QC", "Dropdown selection should work with touch");

                // Test radio button selection
                var maleRadio = driver.FindElement(OpenQA.Selenium.By.Id("gender-1"));
                maleRadio.Click();
                Thread.Sleep(500);
                
                maleRadio.Selected.Should().BeTrue("Radio button selection should work with touch");

                // Test date picker interaction
                var dateField = driver.FindElement(OpenQA.Selenium.By.Name("DateOfBirth"));
                dateField.Click();
                dateField.SendKeys("1990-01-01");
                
                dateField.GetAttribute("value").Should().Be("1990-01-01", "Date input should work with touch");

                // Test registration type selection
                var juniorRegRadio = driver.FindElement(OpenQA.Selenium.By.Id("reg-1"));
                
                // Scroll to element to ensure it's in view
                ((OpenQA.Selenium.IJavaScriptExecutor)driver).ExecuteScript("arguments[0].scrollIntoView(true);", juniorRegRadio);
                Thread.Sleep(500);
                
                juniorRegRadio.Click();
                Thread.Sleep(2000);
                
                juniorRegRadio.Selected.Should().BeTrue("Registration type selection should work with touch");
            }
            catch (Exception ex)
            {
                TakeScreenshot(driver, "TouchInteractions_ShouldWork");
                throw;
            }
        }

        [Theory]
        [InlineData(320, 568)]   // iPhone 5
        [InlineData(375, 667)]   // iPhone 6/7/8
        [InlineData(390, 844)]   // iPhone 12
        [InlineData(414, 896)]   // iPhone 11 Pro Max
        public void RegistrationForm_ShouldNotRequireHorizontalScrolling_OnMobile(int width, int height)
        {
            using var driver = _driverFactory.CreateDriver("Chrome");
            var wait = new OpenQA.Selenium.Support.UI.WebDriverWait(driver, TimeSpan.FromSeconds(_config.ExplicitWaitSeconds));

            try
            {
                // Arrange
                driver.Manage().Window.Size = new System.Drawing.Size(width, height);
                driver.Navigate().GoToUrl($"{_config.BaseUrl}/Members/Register");
                wait.Until(driver => ((OpenQA.Selenium.IJavaScriptExecutor)driver).ExecuteScript("return document.readyState").Equals("complete"));

                // Act & Assert - Check for horizontal overflow
                var documentWidth = (long)((OpenQA.Selenium.IJavaScriptExecutor)driver).ExecuteScript("return Math.max(document.body.scrollWidth, document.documentElement.scrollWidth)");
                var viewportWidth = (long)((OpenQA.Selenium.IJavaScriptExecutor)driver).ExecuteScript("return window.innerWidth");

                documentWidth.Should().BeLessOrEqualTo(viewportWidth + 10, $"Page should not require horizontal scrolling at {width}x{height} (allowing 10px tolerance for browser differences)");

                // Check that all form sections fit within viewport
                var formSections = driver.FindElements(OpenQA.Selenium.By.CssSelector(".section-header, .card-body"));
                
                foreach (var section in formSections.Take(3)) // Check first few sections
                {
                    var sectionWidth = section.Size.Width;
                    sectionWidth.Should().BeLessOrEqualTo(width, $"Form section should fit within viewport width at {width}x{height}");
                }
            }
            catch (Exception ex)
            {
                TakeScreenshot(driver, $"NoHorizontalScrolling_{width}x{height}");
                throw;
            }
        }

        [Fact]
        public void OrientationChange_ShouldMaintainUsability()
        {
            using var driver = _driverFactory.CreateDriver("Chrome");
            var wait = new OpenQA.Selenium.Support.UI.WebDriverWait(driver, TimeSpan.FromSeconds(_config.ExplicitWaitSeconds));
            var registrationPage = new RegistrationPage(driver, wait);

            try
            {
                // Test Portrait mode first
                driver.Manage().Window.Size = new System.Drawing.Size(390, 844); // Portrait
                driver.Navigate().GoToUrl($"{_config.BaseUrl}/Members/Register");
                wait.Until(driver => ((OpenQA.Selenium.IJavaScriptExecutor)driver).ExecuteScript("return document.readyState").Equals("complete"));

                // Fill some form data in portrait
                registrationPage.FillBasicInformation("Orientation", "Test", "1990-01-01", "male");
                var portraitFirstName = registrationPage.GetFirstNameValue();

                // Switch to Landscape mode
                driver.Manage().Window.Size = new System.Drawing.Size(844, 390); // Landscape
                Thread.Sleep(1000);

                // Check that form data is preserved
                registrationPage.GetFirstNameValue().Should().Be(portraitFirstName, "Form data should be preserved during orientation change");

                // Check that form is still usable in landscape
                registrationPage.FillContactInformation("(*************", "mobile", "<EMAIL>");
                registrationPage.GetEmailValue().Should().Be("<EMAIL>", "Form should remain functional in landscape mode");

                // Check that submit button is still accessible
                var submitButton = driver.FindElement(OpenQA.Selenium.By.CssSelector("button[type='submit']"));
                ((OpenQA.Selenium.IJavaScriptExecutor)driver).ExecuteScript("arguments[0].scrollIntoView(true);", submitButton);
                Thread.Sleep(500);
                
                submitButton.Displayed.Should().BeTrue("Submit button should be accessible in landscape mode");
            }
            catch (Exception ex)
            {
                TakeScreenshot(driver, "OrientationChange_ShouldMaintainUsability");
                throw;
            }
        }

        [Fact]
        public void FormFields_ShouldHaveAppropriateSize_OnMobile()
        {
            using var driver = _driverFactory.CreateDriver("Chrome");
            var wait = new OpenQA.Selenium.Support.UI.WebDriverWait(driver, TimeSpan.FromSeconds(_config.ExplicitWaitSeconds));

            try
            {
                // Arrange
                driver.Manage().Window.Size = new System.Drawing.Size(390, 844);
                driver.Navigate().GoToUrl($"{_config.BaseUrl}/Members/Register");
                wait.Until(driver => ((OpenQA.Selenium.IJavaScriptExecutor)driver).ExecuteScript("return document.readyState").Equals("complete"));

                // Act & Assert - Check form field sizes
                var inputFields = driver.FindElements(OpenQA.Selenium.By.CssSelector("input[type='text'], input[type='email'], input[type='tel']"));

                foreach (var field in inputFields.Take(5)) // Test first 5 fields
                {
                    var fieldHeight = field.Size.Height;
                    var fieldWidth = field.Size.Width;

                    // Fields should be large enough for touch interaction (minimum 44px height recommended)
                    fieldHeight.Should().BeGreaterOrEqualTo(35, "Form fields should be large enough for touch interaction");
                    
                    // Fields should not be too narrow
                    fieldWidth.Should().BeGreaterThan(100, "Form fields should not be too narrow on mobile");
                    
                    // Fields should fit within viewport (with some margin)
                    fieldWidth.Should().BeLessOrEqualTo(350, "Form fields should fit within mobile viewport");
                }

                // Check button sizes
                var submitButton = driver.FindElement(OpenQA.Selenium.By.CssSelector("button[type='submit']"));
                ((OpenQA.Selenium.IJavaScriptExecutor)driver).ExecuteScript("arguments[0].scrollIntoView(true);", submitButton);
                Thread.Sleep(500);

                var buttonHeight = submitButton.Size.Height;
                buttonHeight.Should().BeGreaterOrEqualTo(40, "Submit button should be large enough for touch interaction");
            }
            catch (Exception ex)
            {
                TakeScreenshot(driver, "FormFields_ShouldHaveAppropriateSize_OnMobile");
                throw;
            }
        }

        private void TakeScreenshot(OpenQA.Selenium.IWebDriver driver, string testName)
        {
            try
            {
                var screenshot = ((OpenQA.Selenium.ITakesScreenshot)driver).GetScreenshot();
                var directory = _config.ScreenshotPath;
                Directory.CreateDirectory(directory);
                
                var fileName = $"{testName}_{DateTime.Now:yyyyMMdd_HHmmss}.png";
                var filePath = Path.Combine(directory, fileName);
                
                screenshot.SaveAsFile(filePath);
                Console.WriteLine($"Screenshot saved: {filePath}");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Failed to take screenshot: {ex.Message}");
            }
        }

        public void Dispose()
        {
            // Cleanup is handled by using statements in each test method
        }
    }
}