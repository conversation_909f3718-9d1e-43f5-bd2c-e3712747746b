namespace ParaHockeyApp.Models.Configuration
{
    /// <summary>
    /// Configuration class for error handling settings.
    /// Defines how errors should be processed, logged, and displayed based on the environment.
    /// </summary>
    public class ErrorHandlingConfiguration
    {
        /// <summary>
        /// Whether to show detailed error messages to users.
        /// Should be true in Development, false in Production for security.
        /// </summary>
        public bool ShowDetailedErrors { get; set; } = false;

        /// <summary>
        /// Whether to log full stack traces.
        /// Should be true in Development and Test, controlled in Production.
        /// </summary>
        public bool LogStackTraces { get; set; } = false;

        /// <summary>
        /// Whether to show user-friendly error messages instead of technical ones.
        /// Should typically be true in all environments.
        /// </summary>
        public bool ShowUserFriendlyMessages { get; set; } = true;

        /// <summary>
        /// Level of detail to include in error responses.
        /// </summary>
        public ErrorDetailLevel ErrorDetailLevel { get; set; } = ErrorDetailLevel.Minimal;

        /// <summary>
        /// Whether to enable error reporting to external services.
        /// Typically enabled in Test and Production for monitoring.
        /// </summary>
        public bool EnableErrorReporting { get; set; } = false;

        /// <summary>
        /// Whether to include error correlation IDs in responses.
        /// Useful for tracking errors across distributed systems.
        /// </summary>
        public bool IncludeCorrelationIds { get; set; } = true;

        /// <summary>
        /// Whether to log sensitive data in error messages.
        /// Should always be false in Production.
        /// </summary>
        public bool LogSensitiveData { get; set; } = false;

        /// <summary>
        /// Maximum length for error messages shown to users.
        /// Helps prevent information disclosure through overly verbose errors.
        /// </summary>
        public int MaxUserMessageLength { get; set; } = 500;

        /// <summary>
        /// Whether to automatically redirect to error pages for unhandled exceptions.
        /// </summary>
        public bool UseErrorPages { get; set; } = true;

        /// <summary>
        /// Custom error page paths for different error types.
        /// </summary>
        public Dictionary<int, string> ErrorPagePaths { get; set; } = new()
        {
            { 404, "/Home/NotFound" },
            { 500, "/Home/Error" },
            { 403, "/Home/Forbidden" }
        };
    }

    /// <summary>
    /// Enumeration defining levels of error detail to include in responses.
    /// </summary>
    public enum ErrorDetailLevel
    {
        /// <summary>
        /// Minimal error information - just basic user message.
        /// </summary>
        Minimal = 0,

        /// <summary>
        /// Basic error information - user message and error type.
        /// </summary>
        Basic = 1,

        /// <summary>
        /// Detailed error information - includes stack trace and context.
        /// Should only be used in Development.
        /// </summary>
        Detailed = 2,

        /// <summary>
        /// Verbose error information - includes all available error data.
        /// Should only be used for debugging.
        /// </summary>
        Verbose = 3
    }
}