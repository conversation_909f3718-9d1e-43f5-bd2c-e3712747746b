@using Microsoft.Extensions.Localization
@inject IStringLocalizer<ParaHockeyApp.Resources.SharedResourceMarker> Localizer

@{
    ViewData["Title"] = Localizer["PageAuditSystem"];
    var latestInventory = ViewBag.LatestInventory as ParaHockeyApp.Models.Entities.PageInventory;
    var auditSummary = ViewBag.AuditSummary as ParaHockeyApp.Services.AuditSummaryReport;
    var criticalIssues = ViewBag.CriticalIssues as List<ParaHockeyApp.Models.Entities.AuditFinding>;
    var highIssues = ViewBag.HighIssues as List<ParaHockeyApp.Models.Entities.AuditFinding>;
}

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1 class="h3 mb-0">@Localizer["PageAuditSystem"]</h1>
                <div>
                    <button type="button" class="btn btn-primary" id="generateInventoryBtn">
                        <i class="fas fa-sync-alt"></i> @Localizer["GenerateInventory"]
                    </button>
                    <button type="button" class="btn btn-info ms-2" id="generateReviewPlanBtn">
                        <i class="fas fa-clipboard-list"></i> Generate Review Plan
                    </button>
                    <button type="button" class="btn btn-warning ms-2" id="generateAuditReportsBtn">
                        <i class="fas fa-file-alt"></i> Generate Initial Audits
                    </button>
                </div>
                <div class="mt-2">
                    <button type="button" class="btn btn-danger" id="runSecurityScanBtn">
                        <i class="fas fa-shield-alt"></i> Run Security Scan
                    </button>
                    <button type="button" class="btn btn-success ms-2" id="runAccessibilityAuditBtn">
                        <i class="fas fa-universal-access"></i> Run Accessibility Audit
                    </button>
                    <button type="button" class="btn btn-outline-danger ms-2" id="scanCsrfBtn">
                        <i class="fas fa-lock"></i> Scan CSRF Protection
                    </button>
                    <button type="button" class="btn btn-outline-warning ms-2" id="analyzeAuthBtn">
                        <i class="fas fa-user-shield"></i> Analyze Authorization
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Summary Cards -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card bg-primary text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="card-title">@(latestInventory?.TotalPages ?? 0)</h4>
                            <p class="card-text">Total Pages</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-file-alt fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-danger text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="card-title">@(auditSummary?.CriticalIssues ?? 0)</h4>
                            <p class="card-text">@Localizer["CriticalIssues"]</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-exclamation-triangle fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-warning text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="card-title">@(auditSummary?.HighIssues ?? 0)</h4>
                            <p class="card-text">@Localizer["HighIssues"]</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-exclamation-circle fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-success text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="card-title">@(auditSummary?.PassingPages ?? 0)</h4>
                            <p class="card-text">Passing Pages</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-check-circle fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Page Inventory Section -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">@Localizer["PageInventory"]</h5>
                </div>
                <div class="card-body">
                    @if (latestInventory != null)
                    {
                        <div class="mb-3">
                            <small class="text-muted">
                                Version @latestInventory.Version - Generated by @latestInventory.GeneratedBy on @latestInventory.DateCreated.ToString("yyyy-MM-dd HH:mm")
                            </small>
                        </div>
                        
                        <div class="table-responsive">
                            <table class="table table-striped" id="pageInventoryTable">
                                <thead>
                                    <tr>
                                        <th>Page Name</th>
                                        <th>Controller</th>
                                        <th>Action</th>
                                        <th>@Localizer["Priority"]</th>
                                        <th>@Localizer["PageComplexity"]</th>
                                        <th>@Localizer["IsModernized"]</th>
                                        <th>@Localizer["LastAuditScore"]</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <!-- Will be populated by JavaScript -->
                                </tbody>
                            </table>
                        </div>
                    }
                    else
                    {
                        <div class="text-center py-4">
                            <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
                            <h5>@Localizer["NoInventoryFound"]</h5>
                            <p class="text-muted">Click "Generate Inventory" to scan all pages in the application.</p>
                        </div>
                    }
                </div>
            </div>
        </div>
    </div>

    <!-- Anti-forgery token for AJAX requests -->
    @Html.AntiForgeryToken()

    <!-- Critical Issues Section -->
    @if (criticalIssues != null && criticalIssues.Any())
    {
        <div class="row mt-4">
            <div class="col-12">
                <div class="card border-danger">
                    <div class="card-header bg-danger text-white">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-exclamation-triangle"></i> @Localizer["CriticalIssues"]
                        </h5>
                    </div>
                    <div class="card-body">
                        @foreach (var issue in criticalIssues)
                        {
                            <div class="alert alert-danger" role="alert">
                                <strong>@issue.Issue</strong><br>
                                <small class="text-muted">@issue.CodeLocation</small><br>
                                <small>@issue.Rationale</small>
                                @if (!string.IsNullOrEmpty(issue.FixPlan))
                                {
                                    <br><strong>Fix:</strong> @issue.FixPlan
                                }
                            </div>
                        }
                    </div>
                </div>
            </div>
        </div>
    }
</div>

@section Scripts {
    <script>
        $(document).ready(function() {
            // Load page inventory on page load
            loadPageInventory();

            // Generate inventory button click
            $('#generateInventoryBtn').click(function() {
                generateInventory();
            });

            // Generate review plan button click
            $('#generateReviewPlanBtn').click(function() {
                generateReviewPlan();
            });

            // Generate audit reports button click
            $('#generateAuditReportsBtn').click(function() {
                generateAuditReports();
            });

            // Security scan buttons
            $('#runSecurityScanBtn').click(function() {
                runSecurityScan();
            });

            // Accessibility audit button
            $('#runAccessibilityAuditBtn').click(function() {
                runAccessibilityAudit();
            });

            $('#scanCsrfBtn').click(function() {
                scanCsrfProtection();
            });

            $('#analyzeAuthBtn').click(function() {
                analyzeAuthorization();
            });
        });

        function loadPageInventory() {
            $.get('@Url.Action("GetPageInventory", "Admin")')
                .done(function(response) {
                    if (response.success) {
                        populateInventoryTable(response.inventory.pages);
                    } else if (response.needsGeneration) {
                        // Show empty state - already handled in the view
                    } else {
                        showError(response.message);
                    }
                })
                .fail(function() {
                    showError('Error loading page inventory');
                });
        }

        function populateInventoryTable(pages) {
            var tbody = $('#pageInventoryTable tbody');
            tbody.empty();

            pages.forEach(function(page) {
                var priorityBadge = getPriorityBadge(page.priority);
                var complexityBadge = getComplexityBadge(page.complexity);
                var modernizedBadge = page.isModernized ? 
                    '<span class="badge bg-success">Yes</span>' : 
                    '<span class="badge bg-secondary">No</span>';
                var auditScore = page.lastAuditScore ? 
                    '<span class="badge bg-' + getScoreBadgeClass(page.lastAuditScore) + '">' + page.lastAuditScore + '</span>' : 
                    '<span class="text-muted">Not audited</span>';

                var row = '<tr>' +
                    '<td><strong>' + page.name + '</strong></td>' +
                    '<td>' + page.controller + '</td>' +
                    '<td>' + page.action + '</td>' +
                    '<td>' + priorityBadge + '</td>' +
                    '<td>' + complexityBadge + '</td>' +
                    '<td>' + modernizedBadge + '</td>' +
                    '<td>' + auditScore + '</td>' +
                    '<td>' +
                        '<button class="btn btn-sm btn-outline-primary me-1" onclick="auditPage(\'' + page.name + '\')">' +
                            '<i class="fas fa-search"></i> @Localizer["AuditPage"]' +
                        '</button>' +
                        '<button class="btn btn-sm btn-outline-info" onclick="viewAuditHistory(\'' + page.name + '\')">' +
                            '<i class="fas fa-history"></i> History' +
                        '</button>' +
                    '</td>' +
                '</tr>';
                tbody.append(row);
            });
        }

        function getPriorityBadge(priority) {
            switch(priority) {
                case 1: return '<span class="badge bg-danger">High</span>';
                case 2: return '<span class="badge bg-warning">Medium</span>';
                case 3: return '<span class="badge bg-info">Low</span>';
                default: return '<span class="badge bg-secondary">Unknown</span>';
            }
        }

        function getComplexityBadge(complexity) {
            switch(complexity) {
                case 'Critical': return '<span class="badge bg-danger">Critical</span>';
                case 'High': return '<span class="badge bg-warning">High</span>';
                case 'Medium': return '<span class="badge bg-info">Medium</span>';
                case 'Low': return '<span class="badge bg-success">Low</span>';
                default: return '<span class="badge bg-secondary">' + complexity + '</span>';
            }
        }

        function getScoreBadgeClass(score) {
            if (score >= 90) return 'success';
            if (score >= 70) return 'warning';
            return 'danger';
        }

        function generateInventory() {
            var btn = $('#generateInventoryBtn');
            btn.prop('disabled', true).html('<i class="fas fa-spinner fa-spin"></i> Generating...');

            $.post('@Url.Action("GenerateInventory", "Admin")', {
                __RequestVerificationToken: $('input[name="__RequestVerificationToken"]').val()
            })
            .done(function(response) {
                if (response.success) {
                    showSuccess(response.message);
                    // Reload the page to show the new inventory
                    setTimeout(function() {
                        location.reload();
                    }, 1500);
                } else {
                    showError(response.message);
                }
            })
            .fail(function() {
                showError('Error generating inventory');
            })
            .always(function() {
                btn.prop('disabled', false).html('<i class="fas fa-sync-alt"></i> @Localizer["GenerateInventory"]');
            });
        }

        function auditPage(pageName) {
            $.post('@Url.Action("AuditPage", "Admin")', {
                pageName: pageName,
                __RequestVerificationToken: $('input[name="__RequestVerificationToken"]').val()
            })
            .done(function(response) {
                if (response.success) {
                    showSuccess(response.message);
                    // Reload inventory to show updated scores
                    loadPageInventory();
                } else {
                    showError(response.message);
                }
            })
            .fail(function() {
                showError('Error auditing page');
            });
        }

        function viewAuditHistory(pageName) {
            // This would open a modal or navigate to a detailed view
            alert('Audit history for ' + pageName + ' - Feature coming soon!');
        }

        function generateReviewPlan() {
            var btn = $('#generateReviewPlanBtn');
            btn.prop('disabled', true).html('<i class="fas fa-spinner fa-spin"></i> Generating...');

            $.post('@Url.Action("GeneratePageReviewPlan", "Admin")', {
                __RequestVerificationToken: $('input[name="__RequestVerificationToken"]').val()
            })
            .done(function(response) {
                if (response.success) {
                    showSuccess(response.message);
                    // Display the review plan in a modal or new section
                    displayReviewPlan(response.reviewPlan);
                } else {
                    showError(response.message);
                }
            })
            .fail(function() {
                showError('Error generating review plan');
            })
            .always(function() {
                btn.prop('disabled', false).html('<i class="fas fa-clipboard-list"></i> Generate Review Plan');
            });
        }

        function generateAuditReports() {
            var btn = $('#generateAuditReportsBtn');
            btn.prop('disabled', true).html('<i class="fas fa-spinner fa-spin"></i> Generating...');

            $.post('@Url.Action("GenerateInitialAuditReports", "Admin")', {
                __RequestVerificationToken: $('input[name="__RequestVerificationToken"]').val()
            })
            .done(function(response) {
                if (response.success) {
                    showSuccess(response.message + ' - ' + response.auditResults.length + ' pages audited');
                    // Display the audit summary
                    displayAuditSummary(response.summary);
                    // Reload inventory to show updated scores
                    loadPageInventory();
                } else {
                    showError(response.message);
                }
            })
            .fail(function() {
                showError('Error generating audit reports');
            })
            .always(function() {
                btn.prop('disabled', false).html('<i class="fas fa-file-alt"></i> Generate Initial Audits');
            });
        }

        function displayReviewPlan(reviewPlan) {
            // Simple display - you can enhance this with a proper modal
            var message = 'Review Plan Generated:\n\n';
            message += 'Total Pages: ' + reviewPlan.totalPages + '\n';
            message += 'Estimated Hours: ' + reviewPlan.estimatedHours + '\n\n';
            message += 'High Priority Pages: ' + reviewPlan.highPriorityPages.length + '\n';
            message += 'Medium Priority Pages: ' + reviewPlan.mediumPriorityPages.length + '\n';
            message += 'Low Priority Pages: ' + reviewPlan.lowPriorityPages.length + '\n\n';
            message += 'Recommended Order:\n' + reviewPlan.recommendedOrder.slice(0, 5).join('\n');
            if (reviewPlan.recommendedOrder.length > 5) {
                message += '\n... and ' + (reviewPlan.recommendedOrder.length - 5) + ' more';
            }
            alert(message);
        }

        function displayAuditSummary(summary) {
            // Simple display - you can enhance this with a proper modal
            var message = 'Audit Summary:\n\n';
            message += 'Total Pages: ' + summary.totalPages + '\n';
            message += 'Passing Pages: ' + summary.passingPages + '\n';
            message += 'Failing Pages: ' + summary.failingPages + '\n';
            message += 'Average Score: ' + summary.averageScore.toFixed(1) + '%\n\n';
            message += 'Issues Found:\n';
            message += 'Critical: ' + summary.totalCriticalIssues + '\n';
            message += 'High: ' + summary.totalHighIssues + '\n';
            message += 'Medium: ' + summary.totalMediumIssues + '\n';
            message += 'Low: ' + summary.totalLowIssues;
            alert(message);
        }

        function runSecurityScan() {
            var btn = $('#runSecurityScanBtn');
            btn.prop('disabled', true).html('<i class="fas fa-spinner fa-spin"></i> Scanning...');

            $.post('@Url.Action("RunSecurityScan", "Admin")', {
                __RequestVerificationToken: $('input[name="__RequestVerificationToken"]').val()
            })
            .done(function(response) {
                if (response.success) {
                    showSuccess(response.message);
                    displaySecurityReport(response.securityReport);
                } else {
                    showError(response.message);
                }
            })
            .fail(function() {
                showError('Error running security scan');
            })
            .always(function() {
                btn.prop('disabled', false).html('<i class="fas fa-shield-alt"></i> Run Security Scan');
            });
        }

        function displaySecurityReport(report) {
            var message = 'Security Scan Report:\n\n';
            message += 'Total Pages: ' + report.totalPagesAudited + '\n';
            message += 'Secure Pages: ' + report.securePagesCount + '\n';
            message += 'Vulnerable Pages: ' + report.vulnerablePagesCount + '\n';
            message += 'Average Security Score: ' + report.averageSecurityScore + '%\n\n';
            message += 'Security Issues Found:\n';
            message += 'Critical: ' + report.criticalIssuesCount + '\n';
            message += 'High Risk: ' + report.highRiskIssuesCount + '\n';
            message += 'Medium Risk: ' + report.mediumRiskIssuesCount + '\n';
            message += 'Low Risk: ' + report.lowRiskIssuesCount + '\n\n';
            
            if (report.topVulnerabilities && report.topVulnerabilities.length > 0) {
                message += 'Top Vulnerabilities:\n';
                for (var i = 0; i < Math.min(3, report.topVulnerabilities.length); i++) {
                    message += '- ' + report.topVulnerabilities[i] + '\n';
                }
            }
            
            if (report.recommendedActions && report.recommendedActions.length > 0) {
                message += '\nRecommended Actions:\n';
                for (var i = 0; i < Math.min(3, report.recommendedActions.length); i++) {
                    message += '- ' + report.recommendedActions[i] + '\n';
                }
            }
            
            alert(message);
        }

        function runAccessibilityAudit() {
            var btn = $('#runAccessibilityAuditBtn');
            btn.prop('disabled', true).html('<i class="fas fa-spinner fa-spin"></i> Auditing...');

            $.post('@Url.Action("RunAccessibilityAudit", "Admin")', {
                __RequestVerificationToken: $('input[name="__RequestVerificationToken"]').val()
            })
            .done(function(response) {
                if (response.success) {
                    showSuccess(response.message);
                    displayAccessibilityReport(response.report);
                } else {
                    showError(response.message);
                }
            })
            .fail(function() {
                showError('Error running accessibility audit');
            })
            .always(function() {
                btn.prop('disabled', false).html('<i class="fas fa-universal-access"></i> Run Accessibility Audit');
            });
        }

        function displayAccessibilityReport(report) {
            var message = 'Accessibility Audit Report:\n\n';
            message += 'Total Pages: ' + report.totalPages + '\n';
            message += 'Compliant Pages: ' + report.compliantPages + '\n';
            message += 'Non-Compliant Pages: ' + report.nonCompliantPages + '\n';
            message += 'Average Accessibility Score: ' + report.averageAccessibilityScore + '%\n';
            message += 'Overall Compliance: ' + report.overallComplianceLevel + '\n\n';
            message += 'Issues Found:\n';
            message += 'Critical: ' + report.criticalIssues + '\n';
            message += 'Serious: ' + report.seriousIssues + '\n';
            message += 'Moderate: ' + report.moderateIssues + '\n';
            message += 'Minor: ' + report.minorIssues + '\n\n';
            
            if (report.topIssues && report.topIssues.length > 0) {
                message += 'Top Accessibility Issues:\n';
                for (var i = 0; i < Math.min(3, report.topIssues.length); i++) {
                    var issue = report.topIssues[i];
                    message += '- ' + issue.pageName + ': ' + issue.issue + ' (' + issue.impact + ')\n';
                }
            }
            
            if (report.recommendedActions && report.recommendedActions.length > 0) {
                message += '\nRecommended Actions:\n';
                for (var i = 0; i < Math.min(3, report.recommendedActions.length); i++) {
                    message += '- ' + report.recommendedActions[i] + '\n';
                }
            }
            
            alert(message);
        }

        function showSuccess(message) {
            // Simple success notification - you can enhance this with a proper notification system
            alert('Success: ' + message);
        }

        function showError(message) {
            // Simple error notification - you can enhance this with a proper notification system
            alert('Error: ' + message);
        }

        function scanCsrfProtection() {
            var btn = $('#scanCsrfBtn');
            btn.prop('disabled', true).html('<i class="fas fa-spinner fa-spin"></i> Scanning...');

            $.post('@Url.Action("ScanCsrfProtection", "Admin")', {
                __RequestVerificationToken: $('input[name="__RequestVerificationToken"]').val()
            })
            .done(function(response) {
                if (response.success) {
                    showSuccess(response.message);
                    displayCsrfReport(response.report);
                } else {
                    showError(response.message);
                }
            })
            .fail(function() {
                showError('Error scanning CSRF protection');
            })
            .always(function() {
                btn.prop('disabled', false).html('<i class="fas fa-lock"></i> Scan CSRF Protection');
            });
        }

        function analyzeAuthorization() {
            var btn = $('#analyzeAuthBtn');
            btn.prop('disabled', true).html('<i class="fas fa-spinner fa-spin"></i> Analyzing...');

            $.post('@Url.Action("AnalyzeAuthorization", "Admin")', {
                __RequestVerificationToken: $('input[name="__RequestVerificationToken"]').val()
            })
            .done(function(response) {
                if (response.success) {
                    showSuccess(response.message);
                    displayAuthorizationReport(response.summary);
                } else {
                    showError(response.message);
                }
            })
            .fail(function() {
                showError('Error analyzing authorization');
            })
            .always(function() {
                btn.prop('disabled', false).html('<i class="fas fa-user-shield"></i> Analyze Authorization');
            });
        }

        function displayCsrfReport(report) {
            var message = 'CSRF Protection Scan Report:\n\n';
            message += 'Total Actions: ' + report.totalActions + '\n';
            message += 'Protected Actions: ' + report.protectedActions + '\n';
            message += 'Unprotected Actions: ' + report.unprotectedActions + '\n';
            message += 'Protection Rate: ' + report.protectionRate + '%\n\n';
            
            if (report.vulnerableActions && report.vulnerableActions.length > 0) {
                message += 'Vulnerable Actions:\n';
                for (var i = 0; i < Math.min(5, report.vulnerableActions.length); i++) {
                    var action = report.vulnerableActions[i];
                    message += '- ' + action.controller + '.' + action.action + ' (' + action.riskLevel + ')\n';
                }
                if (report.vulnerableActions.length > 5) {
                    message += '... and ' + (report.vulnerableActions.length - 5) + ' more\n';
                }
            }
            
            if (report.recommendations && report.recommendations.length > 0) {
                message += '\nRecommendations:\n';
                for (var i = 0; i < Math.min(3, report.recommendations.length); i++) {
                    message += '- ' + report.recommendations[i] + '\n';
                }
            }
            
            alert(message);
        }

        function displayAuthorizationReport(summary) {
            var message = 'Authorization Analysis Report:\n\n';
            message += 'Total Actions: ' + summary.totalActions + '\n';
            message += 'Authorized Actions: ' + summary.authorizedActions + '\n';
            message += 'Public Endpoints: ' + summary.publicEndpoints + '\n';
            message += 'Authorization Rate: ' + Math.round((summary.authorizedActions / summary.totalActions) * 100) + '%\n\n';
            message += 'Risk Breakdown:\n';
            message += 'Critical Risk: ' + summary.criticalRisk + '\n';
            message += 'High Risk: ' + summary.highRisk + '\n';
            message += 'Medium Risk: ' + summary.mediumRisk + '\n';
            message += 'Low Risk: ' + summary.lowRisk + '\n\n';
            
            if (summary.criticalRisk > 0 || summary.highRisk > 0) {
                message += 'Security Recommendations:\n';
                message += '- Review and add [Authorize] attributes to unprotected actions\n';
                message += '- Ensure sensitive operations require proper authorization\n';
                message += '- Consider implementing role-based access control\n';
            } else {
                message += 'Good news! No critical or high-risk authorization issues found.\n';
            }
            
            alert(message);
        }
    </script>
}