namespace ParaHockeyApp.Models.Configuration
{
    /// <summary>
    /// Configuration settings for different environments (Development, Test, Production)
    /// </summary>
    public class EnvironmentSettings
    {
        /// <summary>
        /// Name of the current environment (DEVELOPMENT, TEST, PRODUCTION)
        /// </summary>
        public string Name { get; set; } = string.Empty;

        /// <summary>
        /// Bootstrap theme class (primary, danger, info, etc.)
        /// </summary>
        public string Theme { get; set; } = "primary";

        /// <summary>
        /// Whether to show the environment warning banner
        /// </summary>
        public bool ShowBanner { get; set; } = false;

        /// <summary>
        /// Whether to use Azure AD authentication
        /// </summary>
        public bool UseAuthentication { get; set; } = true;

        /// <summary>
        /// Text to show in the environment banner
        /// </summary>
        public string BannerText { get; set; } = string.Empty;

        /// <summary>
        /// Whether to show development tools (form auto-fill, test data generators, etc.)
        /// Should be true for Development and Test environments, false for Production
        /// </summary>
        public bool ShowDevelopmentTools { get; set; } = false;

        /// <summary>
        /// Whether to enable detailed error logging with stack traces and technical details
        /// Should be true for Development and Test environments, configurable for Production
        /// </summary>
        public bool EnableDetailedErrorLogging { get; set; } = true;

        /// <summary>
        /// Color scheme for environment indicators (info, danger, primary)
        /// Used to visually distinguish between environments
        /// </summary>
        public string EnvironmentIndicatorColor { get; set; } = "primary";

        /// <summary>
        /// Whether to show user-friendly error messages instead of technical details
        /// Should be true for Production, configurable for other environments
        /// </summary>
        public bool ShowUserFriendlyErrors { get; set; } = true;

        /// <summary>
        /// Maximum level of error detail to expose to users
        /// Options: "minimal", "standard", "detailed"
        /// </summary>
        public string ErrorDetailLevel { get; set; } = "standard";

        /// <summary>
        /// Check if current environment is development
        /// </summary>
        public bool IsDevelopment => Name.Equals("DEVELOPMENT", StringComparison.OrdinalIgnoreCase);

        /// <summary>
        /// Check if current environment is test/staging
        /// </summary>
        public bool IsTest => Name.Equals("TEST", StringComparison.OrdinalIgnoreCase);

        /// <summary>
        /// Check if current environment is production
        /// </summary>
        public bool IsProduction => Name.Equals("PRODUCTION", StringComparison.OrdinalIgnoreCase);

        /// <summary>
        /// Helper property to get the current environment name
        /// </summary>
        public string CurrentEnvironment => Name ?? "DEV";

        /// <summary>
        /// Helper property to determine if banner should be shown and is not null/empty
        /// </summary>
        public bool ShouldShowBanner => ShowBanner && !string.IsNullOrWhiteSpace(BannerText);

        /// <summary>
        /// Helper property to determine if this is a non-production environment
        /// </summary>
        public bool IsNonProduction => IsDevelopment || IsTest;

        /// <summary>
        /// Validates the environment configuration to ensure consistency
        /// </summary>
        /// <returns>List of validation errors, empty if valid</returns>
        public List<string> ValidateConfiguration()
        {
            var errors = new List<string>();

            // Validate environment name
            if (string.IsNullOrWhiteSpace(Name))
            {
                errors.Add("Environment Name is required");
            }
            else if (!new[] { "DEVELOPMENT", "TEST", "PRODUCTION" }.Contains(Name.ToUpperInvariant()))
            {
                errors.Add($"Invalid environment name: {Name}. Must be DEVELOPMENT, TEST, or PRODUCTION");
            }

            // Validate production environment constraints
            if (IsProduction)
            {
                if (ShowDevelopmentTools)
                {
                    errors.Add("Development tools must be disabled in Production environment");
                }

                if (!ShowUserFriendlyErrors)
                {
                    errors.Add("User-friendly errors should be enabled in Production environment");
                }

                if (ShowBanner)
                {
                    errors.Add("Environment banner should be disabled in Production environment");
                }
            }

            // Validate non-production environment recommendations
            if (IsNonProduction)
            {
                if (!EnableDetailedErrorLogging)
                {
                    errors.Add("Detailed error logging should be enabled in non-production environments");
                }
            }

            // Validate theme/color consistency
            if (!string.IsNullOrWhiteSpace(Theme) && !string.IsNullOrWhiteSpace(EnvironmentIndicatorColor))
            {
                if (IsTest && !Theme.Equals("danger", StringComparison.OrdinalIgnoreCase))
                {
                    errors.Add("Test environment should use 'danger' theme for clear visual distinction");
                }

                if (IsDevelopment && !Theme.Equals("info", StringComparison.OrdinalIgnoreCase))
                {
                    errors.Add("Development environment should use 'info' theme for clear visual distinction");
                }
            }

            // Validate error detail level
            if (!string.IsNullOrWhiteSpace(ErrorDetailLevel))
            {
                var validLevels = new[] { "minimal", "standard", "detailed" };
                if (!validLevels.Contains(ErrorDetailLevel.ToLowerInvariant()))
                {
                    errors.Add($"Invalid ErrorDetailLevel: {ErrorDetailLevel}. Must be one of: {string.Join(", ", validLevels)}");
                }
            }

            return errors;
        }

        /// <summary>
        /// Gets the appropriate CSS class for environment indicators
        /// </summary>
        public string GetEnvironmentCssClass()
        {
            return Name?.ToUpperInvariant() switch
            {
                "DEVELOPMENT" => "alert-info",
                "TEST" => "alert-danger",
                "PRODUCTION" => "d-none", // Hidden in production
                _ => "alert-secondary"
            };
        }

        /// <summary>
        /// Gets the appropriate icon class for environment indicators
        /// </summary>
        public string GetEnvironmentIconClass()
        {
            return Name?.ToUpperInvariant() switch
            {
                "DEVELOPMENT" => "bi-code-slash",
                "TEST" => "bi-exclamation-triangle",
                "PRODUCTION" => "",
                _ => "bi-gear"
            };
        }

        /// <summary>
        /// The age at which a person is considered an adult for registration purposes
        /// Configurable per environment, defaults to 18
        /// </summary>
        public int AgeOfMajority { get; set; } = 18;
    }
}