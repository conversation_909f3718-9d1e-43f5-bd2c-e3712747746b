﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace ParaHockeyApp.Migrations
{
    /// <inheritdoc />
    public partial class AddPratiqueFirstShiftCategory : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            // Add Pratique First Shift category
            migrationBuilder.Sql(@"
                -- Add Pratique First Shift category with ID 13
                IF NOT EXISTS (SELECT 1 FROM EventCategories WHERE DisplayNameKey = 'EventCategory_PratiqueFirstShift')
                BEGIN
                    SET IDENTITY_INSERT EventCategories ON
                    INSERT INTO EventCategories (Id, DisplayNameKey, DescriptionKey, Color, IconClass, DisplayOrder, RequiresRegistration, MaxParticipants, DateCreated, IsActive, CreatedBySource)
                    VALUES (13, 'EventCategory_PratiqueFirstShift', 'EventCategory_PratiqueFirstShift_Desc', '#20c997', 'fas fa-skating', 13, 0, -1, GETUTCDATE(), 1, 0)
                    SET IDENTITY_INSERT EventCategories OFF
                    PRINT 'Pratique First Shift category added with ID 13'
                END
                ELSE
                BEGIN
                    PRINT 'Pratique First Shift category already exists'
                END
            ");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            // Remove Pratique First Shift category
            migrationBuilder.Sql(@"
                -- Remove any events that might have been assigned to Pratique First Shift
                UPDATE Events 
                SET EventCategoryId = (SELECT Id FROM EventCategories WHERE DisplayNameKey = 'EventCategory_Other')
                WHERE EventCategoryId = 13

                -- Remove the category
                DELETE FROM EventCategories WHERE DisplayNameKey = 'EventCategory_PratiqueFirstShift'
                
                PRINT 'Removed Pratique First Shift category'
            ");
        }
    }
}
