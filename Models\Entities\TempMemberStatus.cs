namespace ParaHockeyApp.Models.Entities
{
    /// <summary>
    /// Status of a temporary member record during the import process
    /// </summary>
    public enum TempMemberStatus
    {
        /// <summary>
        /// Just imported from file, not yet processed
        /// </summary>
        Imported = 0,

        /// <summary>
        /// Has validation errors that need to be fixed
        /// </summary>
        NeedsFix = 1,

        /// <summary>
        /// Duplicate member detected, needs resolution
        /// </summary>
        Duplicate = 2,

        /// <summary>
        /// Valid and ready to be created as a member
        /// </summary>
        ReadyToCreate = 3,

        /// <summary>
        /// Successfully merged with existing member
        /// </summary>
        Merged = 4,

        /// <summary>
        /// Successfully created as new member
        /// </summary>
        Created = 5,

        /// <summary>
        /// Rejected/discarded by admin
        /// </summary>
        Rejected = 6
    }
}