namespace ParaHockeyApp.Services
{
    public interface IEmailService
    {
        /// <summary>
        /// Sends a verification code email to the specified email address
        /// </summary>
        /// <param name="toEmail">The recipient's email address</param>
        /// <param name="code">The verification code to send</param>
        /// <param name="memberName">The member's name for personalization</param>
        /// <returns>True if email was sent successfully, false otherwise</returns>
        Task<bool> SendVerificationCodeAsync(string toEmail, string code, string memberName);
    }
}