﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace ParaHockeyApp.Migrations
{
    /// <inheritdoc />
    public partial class AddIndexesForFilterQueries : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateIndex(
                name: "IX_Members_City",
                table: "Members",
                column: "City");

            migrationBuilder.CreateIndex(
                name: "IX_Members_IsActive",
                table: "Members",
                column: "IsActive");

            migrationBuilder.CreateIndex(
                name: "IX_Members_IsActive_City",
                table: "Members",
                columns: new[] { "IsActive", "City" });

            migrationBuilder.CreateIndex(
                name: "IX_Members_IsActive_RegistrationTypeId",
                table: "Members",
                columns: new[] { "IsActive", "RegistrationTypeId" });
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropIndex(
                name: "IX_Members_City",
                table: "Members");

            migrationBuilder.DropIndex(
                name: "IX_Members_IsActive",
                table: "Members");

            migrationBuilder.DropIndex(
                name: "IX_Members_IsActive_City",
                table: "Members");

            migrationBuilder.DropIndex(
                name: "IX_Members_IsActive_RegistrationTypeId",
                table: "Members");
        }
    }
}
