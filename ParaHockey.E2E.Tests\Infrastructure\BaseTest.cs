using OpenQA.Selenium;
using OpenQA.Selenium.Support.UI;
using Microsoft.Extensions.Configuration;
using FluentAssertions;
using Selenium.Axe;
using Xunit;

namespace ParaHockey.E2E.Tests.Infrastructure
{
    public abstract class BaseTest : IDisposable
    {
        protected IWebDriver Driver { get; private set; }
        protected WebDriverWait Wait { get; private set; }
        protected TestConfiguration Config { get; private set; }
        protected TestData TestData { get; private set; }
        private readonly WebDriverFactory _driverFactory;
        private bool _disposed = false;

        protected BaseTest(string browserName = "Chrome")
        {
            Config = TestConfiguration.Load();
            _driverFactory = new WebDriverFactory(Config);
            Driver = _driverFactory.CreateDriver(browserName);
            Wait = new WebDriverWait(Driver, TimeSpan.FromSeconds(Config.ExplicitWaitSeconds));

            // Load test data
            var configuration = new ConfigurationBuilder()
                .AddJsonFile("appsettings.Test.json", optional: false)
                .Build();
            
            TestData = new TestData();
            configuration.GetSection("TestData").Bind(TestData);
        }

        protected void NavigateToHomePage()
        {
            Driver.Navigate().GoToUrl(Config.BaseUrl);
            WaitForPageLoad();
        }

        protected void NavigateToRegistrationPage()
        {
            Driver.Navigate().GoToUrl($"{Config.BaseUrl}/Members/Register");
            WaitForPageLoad();
        }

        protected void WaitForPageLoad()
        {
            Wait.Until(driver => 
                ((IJavaScriptExecutor)driver).ExecuteScript("return document.readyState").Equals("complete"));
        }

        protected void WaitForElement(By locator)
        {
            Wait.Until(driver => driver.FindElement(locator).Displayed);
        }

        protected void WaitForElementToBeClickable(By locator)
        {
            Wait.Until(SeleniumExtras.WaitHelpers.ExpectedConditions.ElementToBeClickable(locator));
        }

        protected void ScrollToElement(IWebElement element)
        {
            ((IJavaScriptExecutor)Driver).ExecuteScript("arguments[0].scrollIntoView(true);", element);
            Thread.Sleep(500); // Small delay for smooth scrolling
        }

        protected void TakeScreenshot(string testName)
        {
            if (!Config.ScreenshotOnFailure) return;

            try
            {
                var screenshot = ((ITakesScreenshot)Driver).GetScreenshot();
                var directory = Config.ScreenshotPath;
                Directory.CreateDirectory(directory);
                
                var fileName = $"{testName}_{DateTime.Now:yyyyMMdd_HHmmss}.png";
                var filePath = Path.Combine(directory, fileName);
                
                screenshot.SaveAsFile(filePath);
                Console.WriteLine($"Screenshot saved: {filePath}");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Failed to take screenshot: {ex.Message}");
            }
        }

        protected void ClearAndType(IWebElement element, string text)
        {
            element.Clear();
            element.SendKeys(text);
        }

        protected void SelectDropdownByValue(IWebElement selectElement, string value)
        {
            var select = new SelectElement(selectElement);
            select.SelectByValue(value);
        }

        protected void SelectDropdownByText(IWebElement selectElement, string text)
        {
            var select = new SelectElement(selectElement);
            select.SelectByText(text);
        }

        protected void ClickRadioButton(By locator, string value)
        {
            var radioButton = Driver.FindElement(By.CssSelector($"input[name='{locator}'][value='{value}']"));
            if (!radioButton.Selected)
            {
                radioButton.Click();
            }
        }

        protected void SwitchLanguage(string language)
        {
            var languageLink = Driver.FindElement(By.PartialLinkText(language));
            languageLink.Click();
            WaitForPageLoad();
        }

        protected void AssertElementIsVisible(By locator)
        {
            var element = Driver.FindElement(locator);
            element.Displayed.Should().BeTrue($"Element {locator} should be visible");
        }

        protected void AssertElementIsHidden(By locator)
        {
            try
            {
                var element = Driver.FindElement(locator);
                element.Displayed.Should().BeFalse($"Element {locator} should be hidden");
            }
            catch (NoSuchElementException)
            {
                // Element not found is also considered "hidden"
            }
        }

        protected void AssertFieldHasError(By fieldLocator)
        {
            var field = Driver.FindElement(fieldLocator);
            var classes = field.GetAttribute("class");
            classes.Should().Contain("is-invalid", "Field should have validation error styling");
        }

        protected void AssertFieldIsValid(By fieldLocator)
        {
            var field = Driver.FindElement(fieldLocator);
            var classes = field.GetAttribute("class");
            classes.Should().Contain("is-valid", "Field should have valid styling");
        }

        /// <summary>
        /// Runs axe-core accessibility tests on the current page
        /// </summary>
        /// <param name="testName">Name of the test for reporting purposes</param>
        /// <param name="theme">Theme context (light, dark, forced-colors)</param>
        protected void RunAccessibilityTest(string testName, string theme = "light")
        {
            WaitForPageLoad();
            
            var builder = new AxeBuilder(Driver)
                .WithTags("wcag2a", "wcag2aa", "wcag21aa")
                .WithRules("color-contrast"); // Removed enhanced contrast for more realistic standards

            // For forced colors mode, we might want to skip certain color contrast rules
            // as the system overrides them anyway
            if (theme == "forced-colors")
            {
                builder = builder.DisableRules("color-contrast", "color-contrast-enhanced");
            }

            var results = builder.Analyze();

            if (results.Violations.Length > 0)
            {
                var violationMessages = results.Violations.Select(v => 
                    $"Rule: {v.Id} - {v.Description}\n" +
                    $"Impact: {v.Impact}\n" +
                    $"Help: {v.Help}\n" +
                    $"Nodes: {v.Nodes.Length}\n" +
                    string.Join("\n", v.Nodes.Select(n => $"  - {n.Html}"))
                ).ToArray();

                var message = $"Accessibility violations found in {testName} ({theme} theme):\n\n" +
                             string.Join("\n\n", violationMessages);
                
                Console.WriteLine(message);
                Assert.True(false, message);
            }

            Console.WriteLine($"✅ Accessibility test passed for {testName} ({theme} theme): {results.Passes.Length} rules passed, 0 violations");
        }

        /// <summary>
        /// Sets the theme preference and runs accessibility test
        /// </summary>
        /// <param name="testName">Name of the test</param>
        /// <param name="theme">Theme to test (light, dark, forced-colors)</param>
        protected void RunAccessibilityTestWithTheme(string testName, string theme)
        {
            try
            {
                switch (theme.ToLower())
                {
                    case "dark":
                        // Inject CSS to simulate dark mode preference
                        ((IJavaScriptExecutor)Driver).ExecuteScript(@"
                            const style = document.createElement('style');
                            style.textContent = '@media (prefers-color-scheme: light) { :root { display: none; } }';
                            document.head.appendChild(style);
                            
                            const darkStyle = document.createElement('style');
                            darkStyle.textContent = ':root { color-scheme: dark; }';
                            document.head.appendChild(darkStyle);
                            
                            if (window.ParaHockeyTheme) {
                                window.ParaHockeyTheme.refresh();
                            }
                        ");
                        break;
                        
                    case "forced-colors":
                        // Inject CSS to simulate forced colors mode
                        ((IJavaScriptExecutor)Driver).ExecuteScript(@"
                            const style = document.createElement('style');
                            style.textContent = `
                                @media (forced-colors: active) {
                                    :root {
                                        --ph-bg-primary: Canvas;
                                        --ph-text-primary: CanvasText;
                                        --ph-link: LinkText;
                                        --ph-border: GrayText;
                                    }
                                }
                                html { forced-color-adjust: none; }
                            `;
                            document.head.appendChild(style);
                            document.documentElement.classList.add('ph-forced-colors');
                        ");
                        break;
                        
                    case "light":
                    default:
                        // Ensure light mode (default)
                        ((IJavaScriptExecutor)Driver).ExecuteScript(@"
                            document.documentElement.classList.remove('ph-dark', 'ph-forced-colors');
                        ");
                        break;
                }

                // Small delay for theme changes to take effect
                Thread.Sleep(500);
                
                RunAccessibilityTest(testName, theme);
            }
            finally
            {
                // Reset to light mode
                ((IJavaScriptExecutor)Driver).ExecuteScript(@"
                    document.documentElement.classList.remove('ph-dark', 'ph-forced-colors');
                    const injectedStyles = document.querySelectorAll('style');
                    injectedStyles.forEach(style => {
                        if (style.textContent.includes('prefers-color-scheme') || 
                            style.textContent.includes('forced-colors') ||
                            style.textContent.includes('color-scheme: dark')) {
                            style.remove();
                        }
                    });
                ");
            }
        }

        public virtual void Dispose()
        {
            if (!_disposed)
            {
                Driver?.Quit();
                Driver?.Dispose();
                _disposed = true;
            }
        }
    }
}