using Xunit;
using FluentAssertions;
using ParaHockey.E2E.Tests.Infrastructure;
using OpenQA.Selenium;
using OpenQA.Selenium.Support.UI;
using System.Threading;

namespace ParaHockey.E2E.Tests.Tests
{
    public class EventTimeOffsetTests : BaseTest, IDisposable
    {
        public EventTimeOffsetTests() : base()
        {
        }

        [Fact]
        public void CreateEvent_ShouldPreserveLocalTime_AfterReload()
        {
            try
            {
                // Arrange - Navigate to admin calendar page
                NavigateToAdminCalendar();
                
                // Act - Create a new event at 09:00
                var addEventBtn = Driver.FindElement(By.Id("addEventBtn"));
                addEventBtn.Click();
                Thread.Sleep(500); // Wait for modal
                
                // Fill event form
                var titleInput = Driver.FindElement(By.Id("eventTitle"));
                titleInput.SendKeys("Test Event Time Offset");
                
                var startDateInput = Driver.FindElement(By.Id("eventStartDate"));
                var endDateInput = Driver.FindElement(By.Id("eventEndDate"));
                
                // Set specific time (9:00 AM today)
                var today = DateTime.Now;
                var startTime = new DateTime(today.Year, today.Month, today.Day, 9, 0, 0);
                var endTime = startTime.AddHours(1);
                
                // Format for datetime-local input
                var startValue = startTime.ToString("yyyy-MM-ddTHH:mm");
                var endValue = endTime.ToString("yyyy-MM-ddTHH:mm");
                
                startDateInput.Clear();
                startDateInput.SendKeys(startValue);
                endDateInput.Clear();
                endDateInput.SendKeys(endValue);
                
                // Save event
                var saveBtn = Driver.FindElement(By.Id("saveEventBtn"));
                saveBtn.Click();
                Thread.Sleep(2000); // Wait for save
                
                // Reload page
                Driver.Navigate().Refresh();
                WaitForPageLoad();
                Thread.Sleep(2000); // Wait for calendar to load
                
                // Find and click the created event
                var eventElements = Driver.FindElements(By.CssSelector(".fc-event-title"));
                var testEvent = eventElements.FirstOrDefault(e => e.Text.Contains("Test Event Time Offset"));
                testEvent.Should().NotBeNull("Event should be visible on calendar");
                
                // Click to open event modal
                testEvent.Click();
                Thread.Sleep(1000); // Wait for modal
                
                // Assert - Verify time is still 09:00
                var startDateValue = Driver.FindElement(By.Id("eventStartDate")).GetAttribute("value");
                startDateValue.Should().Contain("09:00", "Start time should remain 09:00 after reload");
            }
            catch (Exception ex)
            {
                TakeScreenshot("CreateEvent_PreserveLocalTime");
                throw;
            }
        }

        [Fact]
        public void SaveEvent_WithoutChanges_ShouldShowNoChangesMessage()
        {
            try
            {
                // Arrange - Navigate to admin calendar and create an event first
                NavigateToAdminCalendar();
                CreateTestEvent("No Changes Test Event");
                
                // Act - Find and click the event to edit
                Thread.Sleep(2000); // Wait for calendar refresh
                var eventElements = Driver.FindElements(By.CssSelector(".fc-event-title"));
                var testEvent = eventElements.FirstOrDefault(e => e.Text.Contains("No Changes Test Event"));
                testEvent.Should().NotBeNull("Event should be visible on calendar");
                
                testEvent.Click();
                Thread.Sleep(1000); // Wait for modal
                
                // Click save without making changes
                var saveBtn = Driver.FindElement(By.Id("saveEventBtn"));
                saveBtn.Click();
                Thread.Sleep(500);
                
                // Assert - Check for "No changes detected" message
                var alerts = Driver.FindElements(By.CssSelector(".alert-info"));
                alerts.Should().NotBeEmpty("Info alert should be displayed");
                
                var alertText = alerts.First().Text;
                // Check for both English and French versions
                alertText.Should().Match(text => 
                    text.Contains("No changes detected") || 
                    text.Contains("Aucune modification détectée"),
                    "Alert should contain the no changes message");
                
                // Verify modal was closed
                var modalDisplay = Driver.FindElement(By.Id("eventModal")).GetCssValue("display");
                modalDisplay.Should().Be("none", "Modal should be closed");
            }
            catch (Exception ex)
            {
                TakeScreenshot("SaveEvent_NoChanges");
                throw;
            }
        }

        [Fact]
        public void EditEvent_ChangeOnlyDescription_ShouldPreserveTime()
        {
            try
            {
                // Arrange - Create event and reload
                NavigateToAdminCalendar();
                CreateTestEvent("Time Preservation Test");
                
                // Get the event and open edit modal
                Thread.Sleep(2000);
                var eventElements = Driver.FindElements(By.CssSelector(".fc-event-title"));
                var testEvent = eventElements.FirstOrDefault(e => e.Text.Contains("Time Preservation Test"));
                testEvent.Click();
                Thread.Sleep(1000);
                
                // Capture original time values
                var originalStartTime = Driver.FindElement(By.Id("eventStartDate")).GetAttribute("value");
                var originalEndTime = Driver.FindElement(By.Id("eventEndDate")).GetAttribute("value");
                
                // Act - Change only description
                var descriptionInput = Driver.FindElement(By.Id("eventDescription"));
                descriptionInput.Clear();
                descriptionInput.SendKeys("Updated description only");
                
                // Save
                var saveBtn = Driver.FindElement(By.Id("saveEventBtn"));
                saveBtn.Click();
                Thread.Sleep(2000);
                
                // Open event again
                eventElements = Driver.FindElements(By.CssSelector(".fc-event-title"));
                testEvent = eventElements.FirstOrDefault(e => e.Text.Contains("Time Preservation Test"));
                testEvent.Click();
                Thread.Sleep(1000);
                
                // Assert - Times should be unchanged
                var newStartTime = Driver.FindElement(By.Id("eventStartDate")).GetAttribute("value");
                var newEndTime = Driver.FindElement(By.Id("eventEndDate")).GetAttribute("value");
                
                newStartTime.Should().Be(originalStartTime, "Start time should remain unchanged");
                newEndTime.Should().Be(originalEndTime, "End time should remain unchanged");
                
                // Verify description was updated
                var description = Driver.FindElement(By.Id("eventDescription")).GetAttribute("value");
                description.Should().Be("Updated description only", "Description should be updated");
            }
            catch (Exception ex)
            {
                TakeScreenshot("EditEvent_PreserveTime");
                throw;
            }
        }

        private void NavigateToAdminCalendar()
        {
            // In development mode, authentication is bypassed
            Driver.Navigate().GoToUrl($"{Config.BaseUrl}/Admin/Calendar");
            WaitForPageLoad();
            Thread.Sleep(2000); // Wait for calendar to initialize
        }

        private void CreateTestEvent(string title)
        {
            // Click add event button
            var addEventBtn = Driver.FindElement(By.Id("addEventBtn"));
            addEventBtn.Click();
            Thread.Sleep(500);
            
            // Fill basic event details
            Driver.FindElement(By.Id("eventTitle")).SendKeys(title);
            
            // Set times to specific values for testing
            var today = DateTime.Now;
            var startTime = new DateTime(today.Year, today.Month, today.Day, 14, 0, 0);
            var endTime = startTime.AddHours(2);
            
            var startValue = startTime.ToString("yyyy-MM-ddTHH:mm");
            var endValue = endTime.ToString("yyyy-MM-ddTHH:mm");
            
            var startInput = Driver.FindElement(By.Id("eventStartDate"));
            var endInput = Driver.FindElement(By.Id("eventEndDate"));
            
            startInput.Clear();
            startInput.SendKeys(startValue);
            endInput.Clear();
            endInput.SendKeys(endValue);
            
            // Save
            Driver.FindElement(By.Id("saveEventBtn")).Click();
            Thread.Sleep(2000);
        }

        [Theory]
        [InlineData("America/Vancouver")] // Pacific Time
        [InlineData("America/New_York")]  // Eastern Time
        [InlineData("Europe/London")]     // GMT/BST
        public void CreateEvent_InDifferentTimezone_ShouldWorkCorrectly(string timezone)
        {
            // Note: This test simulates different timezones by setting browser preferences
            // In a real scenario, you might need to use browser-specific capabilities
            // or run tests in containers with different TZ environment variables
            
            try
            {
                // This is a placeholder for timezone testing
                // Actual implementation would require:
                // 1. For Chrome: Set chromeOptions.AddUserProfilePreference("intl.accept_languages", "en-US")
                // 2. For Firefox: Set firefoxProfile.SetPreference("intl.accept_languages", "en-US")
                // 3. Or run with TZ environment variable set
                
                NavigateToAdminCalendar();
                
                // Create event
                var addEventBtn = Driver.FindElement(By.Id("addEventBtn"));
                addEventBtn.Click();
                Thread.Sleep(500);
                
                Driver.FindElement(By.Id("eventTitle")).SendKeys($"TZ Test - {timezone}");
                
                // Set a specific time
                var testTime = new DateTime(2024, 1, 15, 15, 30, 0);
                var startValue = testTime.ToString("yyyy-MM-ddTHH:mm");
                var endValue = testTime.AddHours(1).ToString("yyyy-MM-ddTHH:mm");
                
                var startInput = Driver.FindElement(By.Id("eventStartDate"));
                var endInput = Driver.FindElement(By.Id("eventEndDate"));
                
                startInput.Clear();
                startInput.SendKeys(startValue);
                endInput.Clear();
                endInput.SendKeys(endValue);
                
                // Save
                Driver.FindElement(By.Id("saveEventBtn")).Click();
                Thread.Sleep(2000);
                
                // Reload and verify
                Driver.Navigate().Refresh();
                WaitForPageLoad();
                Thread.Sleep(2000);
                
                // Find and click event
                var eventElements = Driver.FindElements(By.CssSelector(".fc-event-title"));
                var testEvent = eventElements.FirstOrDefault(e => e.Text.Contains($"TZ Test - {timezone}"));
                testEvent.Should().NotBeNull($"Event should be visible for timezone {timezone}");
                
                testEvent.Click();
                Thread.Sleep(1000);
                
                // Verify time is preserved
                var savedStartTime = Driver.FindElement(By.Id("eventStartDate")).GetAttribute("value");
                savedStartTime.Should().Contain("15:30", $"Time should be preserved in {timezone}");
            }
            catch (Exception ex)
            {
                TakeScreenshot($"CreateEvent_Timezone_{timezone.Replace("/", "_")}");
                throw;
            }
        }

        public new void Dispose()
        {
            // Cleanup any test events if needed
            base.Dispose();
        }
    }
}