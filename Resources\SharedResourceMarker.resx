<?xml version="1.0" encoding="utf-8"?>
<root>
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <!-- Default: French -->
  <data name="Home" xml:space="preserve">
    <value>Accueil</value>
  </data>
  <data name="Privacy" xml:space="preserve">
    <value>ConfidentialitÃ©</value>
  </data>
  <data name="TestBackbone" xml:space="preserve">
    <value>Test Backbone</value>
  </data>
  <data name="Login" xml:space="preserve">
    <value>Connexion</value>
  </data>
  <data name="Logout" xml:space="preserve">
    <value>DÃ©connexion</value>
  </data>
  <data name="Hello" xml:space="preserve">
    <value>Bonjour</value>
  </data>
  <data name="DevelopmentMode" xml:space="preserve">
    <value>Mode DÃ©veloppement</value>
  </data>
  <data name="Language" xml:space="preserve">
    <value>Langue</value>
  </data>
  <data name="French" xml:space="preserve">
    <value>FranÃ§ais</value>
  </data>
  <data name="English" xml:space="preserve">
    <value>Anglais</value>
  </data>
  <data name="Environment" xml:space="preserve">
    <value>Environnement</value>
  </data>
  <data name="Copyright" xml:space="preserve">
    <value>Â© {0} - Parahockey</value>
  </data>
  <data name="DevelopmentAlert" xml:space="preserve">
    <value>Mode DÃ©veloppement: Base de donnÃ©es SQLite locale, authentification dÃ©sactivÃ©e pour un dÃ©veloppement rapide.</value>
  </data>
  <data name="TestVerification" xml:space="preserve">
    <value>Test de VÃ©rification - Parahockey</value>
  </data>
  <data name="BackboneVerification" xml:space="preserve">
    <value>Phase 1 - VÃ©rification de l'architecture de base</value>
  </data>
  <data name="Objective" xml:space="preserve">
    <value>Objectif</value>
  </data>
  <data name="VerifyArchitecture" xml:space="preserve">
    <value>VÃ©rifier que l'architecture fonctionne : Saisie â†’ Traitement â†’ Base de donnÃ©es â†’ Affichage</value>
  </data>
  <data name="TotalEntries" xml:space="preserve">
    <value>EntrÃ©es totales</value>
  </data>
  <data name="ActiveEntries" xml:space="preserve">
    <value>EntrÃ©es actives</value>
  </data>
  <data name="Database" xml:space="preserve">
    <value>Base de donnÃ©es</value>
  </data>
  <data name="CreateNewEntry" xml:space="preserve">
    <value>CrÃ©er une nouvelle entrÃ©e</value>
  </data>
  <data name="Notes" xml:space="preserve">
    <value>Notes</value>
  </data>
  <data name="Optional" xml:space="preserve">
    <value>optionnel</value>
  </data>
  <data name="Required" xml:space="preserve">
    <value>requis</value>
  </data>
  <data name="EnterYourName" xml:space="preserve">
    <value>Entrez votre nom</value>
  </data>
  <data name="CommentsOrNotes" xml:space="preserve">
    <value>Commentaires ou notes...</value>
  </data>
  <data name="CreateEntry" xml:space="preserve">
    <value>CrÃ©er l'entrÃ©e</value>
  </data>
  <data name="BaseArchitectureVerification" xml:space="preserve">
    <value>Phase 1 - VÃ©rification de l'architecture de base</value>
  </data>
  <data name="VerifyArchitectureFlow" xml:space="preserve">
    <value>VÃ©rifier que l'architecture fonctionne : Saisie â†’ Traitement â†’ Base de donnÃ©es â†’ Affichage</value>
  </data>
  <data name="TestEntries" xml:space="preserve">
    <value>EntrÃ©es de test</value>
  </data>
  <data name="SortedByCreationDate" xml:space="preserve">
    <value>TriÃ©es par date de crÃ©ation (plus rÃ©centes en premier)</value>
  </data>
  <data name="ID" xml:space="preserve">
    <value>ID</value>
  </data>
  <data name="CreatedOn" xml:space="preserve">
    <value>CrÃ©Ã© le</value>
  </data>
  <data name="ShowingFirst10" xml:space="preserve">
    <value>Affichage des 10 premiers de {0} entrÃ©es</value>
  </data>
  <data name="First10" xml:space="preserve">
    <value>Premiers 10</value>
  </data>
  <data name="Between2And100Characters" xml:space="preserve">
    <value>Entre 2 et 100 caractÃ¨res</value>
  </data>
  <data name="Maximum1000Characters" xml:space="preserve">
    <value>Maximum 1000 caractÃ¨res</value>
  </data>
  <data name="Administration" xml:space="preserve">
    <value>Administration</value>
  </data>

  <!-- Lookup Table Translations -->
  <!-- Gender Options -->
  <data name="Gender_Male" xml:space="preserve">
    <value>Masculin</value>
  </data>
  <data name="Gender_Female" xml:space="preserve">
    <value>FÃ©minin</value>
  </data>
  <data name="Gender_Other" xml:space="preserve">
    <value>Autre</value>
  </data>

  <!-- Phone Type Options -->
  <data name="PhoneType_Mobile" xml:space="preserve">
    <value>Mobile</value>
  </data>
  <data name="PhoneType_Other" xml:space="preserve">
    <value>Autre</value>
  </data>

  <!-- Province Options -->
  <data name="Province_AB" xml:space="preserve">
    <value>Alberta</value>
  </data>
  <data name="Province_BC" xml:space="preserve">
    <value>Colombie-Britannique</value>
  </data>
  <data name="Province_MB" xml:space="preserve">
    <value>Manitoba</value>
  </data>
  <data name="Province_NB" xml:space="preserve">
    <value>Nouveau-Brunswick</value>
  </data>
  <data name="Province_NL" xml:space="preserve">
    <value>Terre-Neuve-et-Labrador</value>
  </data>
  <data name="Province_NS" xml:space="preserve">
    <value>Nouvelle-Ã‰cosse</value>
  </data>
  <data name="Province_NT" xml:space="preserve">
    <value>Territoires du Nord-Ouest</value>
  </data>
  <data name="Province_NU" xml:space="preserve">
    <value>Nunavut</value>
  </data>
  <data name="Province_ON" xml:space="preserve">
    <value>Ontario</value>
  </data>
  <data name="Province_PE" xml:space="preserve">
    <value>ÃŽle-du-Prince-Ã‰douard</value>
  </data>
  <data name="Province_QC" xml:space="preserve">
    <value>QuÃ©bec</value>
  </data>
  <data name="Province_SK" xml:space="preserve">
    <value>Saskatchewan</value>
  </data>
  <data name="Province_YT" xml:space="preserve">
    <value>Yukon</value>
  </data>

  <!-- Registration Type Options -->
  <data name="RegType_Junior" xml:space="preserve">
    <value>Junior</value>
  </data>
  <data name="RegType_Development" xml:space="preserve">
    <value>DÃ©veloppement</value>
  </data>
  <data name="RegType_Elite" xml:space="preserve">
    <value>Ã‰lite</value>
  </data>
  <data name="RegType_Coach" xml:space="preserve">
    <value>EntraÃ®neur</value>
  </data>
  <data name="RegType_Volunteer" xml:space="preserve">
    <value>BÃ©nÃ©vole</value>
  </data>

  <!-- Form Labels -->
  <data name="ContactInformation" xml:space="preserve">
    <value>Informations de contact</value>
  </data>
  <data name="PhoneNumber" xml:space="preserve">
    <value>NumÃ©ro de tÃ©lÃ©phone</value>
  </data>

  <!-- Home Page -->
  <data name="HomePageTitle" xml:space="preserve">
    <value>Parahockey - Accueil</value>
  </data>
  <data name="WelcomeTitle" xml:space="preserve">
    <value>Bienvenue chez Parahockey</value>
  </data>
  <data name="WelcomeSubtitle" xml:space="preserve">
    <value>Rejoignez notre communautÃ© de passionnÃ©s de hockey adaptÃ©. Que vous soyez joueur, entraÃ®neur ou bÃ©nÃ©vole, votre place est ici.</value>
  </data>
  <data name="RegisterNow" xml:space="preserve">
    <value>S'inscrire maintenant</value>
  </data>
  <data name="AdminPanel" xml:space="preserve">
    <value>Panneau d'administration</value>
  </data>
  <data name="AdminLogin" xml:space="preserve">
    <value>Connexion Admin</value>
  </data>
  <data name="CommunityTitle" xml:space="preserve">
    <value>Notre CommunautÃ©</value>
  </data>
  <data name="ActivePlayers" xml:space="preserve">
    <value>Joueurs actifs</value>
  </data>
  <data name="Teams" xml:space="preserve">
    <value>Ã‰quipes</value>
  </data>
  <data name="Coaches" xml:space="preserve">
    <value>EntraÃ®neurs</value>
  </data>
  <data name="Volunteers" xml:space="preserve">
    <value>BÃ©nÃ©voles</value>
  </data>
  <data name="RegistrationTypesTitle" xml:space="preserve">
    <value>Types d'inscription disponibles</value>
  </data>
  <data name="RegistrationTypesSubtitle" xml:space="preserve">
    <value>Trouvez votre place dans notre Ã©quipe</value>
  </data>
  <data name="Junior" xml:space="preserve">
    <value>Junior</value>
  </data>
  <data name="JuniorDesc" xml:space="preserve">
    <value>Programme spÃ©cialement conÃ§u pour les jeunes joueurs qui dÃ©butent leur parcours dans le hockey adaptÃ©.</value>
  </data>
  <data name="Development" xml:space="preserve">
    <value>DÃ©veloppement</value>
  </data>
  <data name="DevelopmentDesc" xml:space="preserve">
    <value>Pour ceux qui souhaitent apprendre et progresser dans un environnement bienveillant et structurÃ©.</value>
  </data>
  <data name="Elite" xml:space="preserve">
    <value>Ã‰lite</value>
  </data>
  <data name="EliteDesc" xml:space="preserve">
    <value>Niveau compÃ©titif pour les joueurs expÃ©rimentÃ©s qui visent l'excellence sportive.</value>
  </data>
  <data name="Coach" xml:space="preserve">
    <value>EntraÃ®neur</value>
  </data>
  <data name="CoachDesc" xml:space="preserve">
    <value>Rejoignez notre Ã©quipe d'encadrement et partagez votre passion pour le hockey adaptÃ©.</value>
  </data>
  <data name="Volunteer" xml:space="preserve">
    <value>BÃ©nÃ©vole</value>
  </data>
  <data name="VolunteerDesc" xml:space="preserve">
    <value>Contribuez au succÃ¨s de notre organisation en offrant votre temps et vos compÃ©tences.</value>
  </data>
  <data name="FamilyFriends" xml:space="preserve">
    <value>Famille &amp; Amis</value>
  </data>
  <data name="FamilyFriendsDesc" xml:space="preserve">
    <value>Soutenez nos joueurs et dÃ©couvrez l'esprit unique du hockey adaptÃ©.</value>
  </data>
  <data name="CtaTitle" xml:space="preserve">
    <value>PrÃªt Ã  rejoindre l'aventure ?</value>
  </data>
  <data name="CtaSubtitle" xml:space="preserve">
    <value>L'inscription ne prend que quelques minutes. Commencez votre parcours avec Parahockey dÃ¨s aujourd'hui.</value>
  </data>
  <data name="CtaButton" xml:space="preserve">
    <value>DÃ©marrer mon inscription</value>
  </data>

  <!-- Registration Page -->
  <data name="RegistrationPageTitle" xml:space="preserve">
    <value>Inscription - Parahockey</value>
  </data>
  <data name="EditProfilePageTitle" xml:space="preserve">
    <value>Modifier le profil - Parahockey</value>
  </data>
  <data name="EditProfileTitle" xml:space="preserve">
    <value>Modifier le profil</value>
  </data>
  <data name="UpdateProfileButton" xml:space="preserve">
    <value>Mettre Ã  jour le profil</value>
  </data>
  <data name="RegistrationFormTitle" xml:space="preserve">
    <value>Formulaire d'Inscription</value>
  </data>
  <data name="AlreadyRegistered" xml:space="preserve">
    <value>DÃ©jÃ  inscrit?</value>
  </data>
  <data name="ClickHereLink" xml:space="preserve">
    <value>Cliquez ici</value>
  </data>
  <data name="LoginLink" xml:space="preserve">
    <value>Connexion</value>
  </data>
  <data name="ValidationErrors" xml:space="preserve">
    <value>Veuillez corriger les erreurs suivantes :</value>
  </data>
  <data name="BasicInformation" xml:space="preserve">
    <value>Information de base</value>
  </data>
  <data name="FirstName" xml:space="preserve">
    <value>PrÃ©nom</value>
  </data>
  <data name="FirstNamePlaceholder" xml:space="preserve">
    <value>Votre prÃ©nom</value>
  </data>
  <data name="LastName" xml:space="preserve">
    <value>Nom</value>
  </data>
  <data name="LastNamePlaceholder" xml:space="preserve">
    <value>Votre nom de famille</value>
  </data>
  <data name="ThirdPersonFirstNamePlaceholder" xml:space="preserve">
    <value>PrÃ©nom du contact</value>
  </data>
  <data name="ThirdPersonLastNamePlaceholder" xml:space="preserve">
    <value>Nom de famille du contact</value>
  </data>
  <data name="ThirdPersonEmailPlaceholder" xml:space="preserve">
    <value>Adresse courriel du contact</value>
  </data>
  <data name="DateOfBirth" xml:space="preserve">
    <value>Date de naissance</value>
  </data>
  <data name="DateFormat" xml:space="preserve">
    <value>Format: AAAA-MM-JJ</value>
  </data>
  <data name="Gender" xml:space="preserve">
    <value>Genre</value>
  </data>
  <data name="Male" xml:space="preserve">
    <value>Masculin</value>
  </data>
  <data name="Female" xml:space="preserve">
    <value>FÃ©minin</value>
  </data>
  <data name="Other" xml:space="preserve">
    <value>Autres</value>
  </data>
  <data name="Address" xml:space="preserve">
    <value>Adresse</value>
  </data>
  <data name="StreetAddress" xml:space="preserve">
    <value>Adresse</value>
  </data>
  <data name="StreetAddressPlaceholder" xml:space="preserve">
    <value>123 Rue de la Patinoire</value>
  </data>
  <data name="City" xml:space="preserve">
    <value>Ville</value>
  </data>
  <data name="CityPlaceholder" xml:space="preserve">
    <value>MontrÃ©al</value>
  </data>
  <data name="Province" xml:space="preserve">
    <value>Province</value>
  </data>
  <data name="SelectProvince" xml:space="preserve">
    <value>SÃ©lectionnez une province</value>
  </data>
  <data name="PostalCode" xml:space="preserve">
    <value>Code postal</value>
  </data>
  <data name="PostalCodeFormat" xml:space="preserve">
    <value>Format: H1H 1H1</value>
  </data>
  <data name="Contact" xml:space="preserve">
    <value>Contact</value>
  </data>
  <data name="Phone" xml:space="preserve">
    <value>TÃ©lÃ©phone</value>
  </data>
  <data name="PhonePlaceholder" xml:space="preserve">
    <value>(*************</value>
  </data>
  <data name="PhoneType" xml:space="preserve">
    <value>Type</value>
  </data>
  <data name="Mobile" xml:space="preserve">
    <value>Cellulaire</value>
  </data>
  <data name="Email" xml:space="preserve">
    <value>Courriel</value>
  </data>
  <data name="EmailPlaceholder" xml:space="preserve">
    <value><EMAIL></value>
  </data>
  <data name="RegistrationType" xml:space="preserve">
    <value>Type d'inscription</value>
  </data>
  <data name="JuniorSubtext" xml:space="preserve">
    <value>Pour les jeunes joueurs</value>
  </data>
  <data name="DevelopmentSubtext" xml:space="preserve">
    <value>Pour apprendre et progresser</value>
  </data>
  <data name="EliteSubtext" xml:space="preserve">
    <value>Pour les joueurs expÃ©rimentÃ©s</value>
  </data>
  <data name="CoachSubtext" xml:space="preserve">
    <value>Pour encadrer les Ã©quipes</value>
  </data>
  <data name="VolunteerSubtext" xml:space="preserve">
    <value>Pour aider l'organisation</value>
  </data>
  <data name="RegisterButton" xml:space="preserve">
    <value>S'inscrire</value>
  </data>
  <data name="ClearFormButton" xml:space="preserve">
    <value>Effacer le formulaire</value>
  </data>
  <data name="ConfirmReset" xml:space="preserve">
    <value>Voulez-vous vraiment effacer tous les champs du formulaire ?</value>
  </data>

  <!-- Validation Messages -->
  <data name="ValidationRequired" xml:space="preserve">
    <value>Le champ {0} est requis.</value>
  </data>
  <data name="ValidationStringLength" xml:space="preserve">
    <value>Le champ {0} doit Ãªtre une chaÃ®ne d'une longueur maximale de {1}.</value>
  </data>
  <data name="ValidationPostalCode" xml:space="preserve">
    <value>Format de code postal invalide (ex: H1H 1H1).</value>
  </data>
  <data name="ValidationPhone" xml:space="preserve">
    <value>Format de numÃ©ro de tÃ©lÃ©phone invalide.</value>
  </data>
  <data name="ValidationEmail" xml:space="preserve">
    <value>Format d'adresse de courriel invalide.</value>
  </data>
  <data name="ValidationEmailFormat" xml:space="preserve">
    <value>Le format du courriel n'est pas valide. Exemple: <EMAIL></value>
  </data>

  <!-- Modern Validation Messages -->
  <data name="ValidationEmailSpecific" xml:space="preserve">
    <value>Veuillez entrer une adresse courriel valide (ex: <EMAIL>)</value>
  </data>
  <data name="ValidationPhoneSpecific" xml:space="preserve">
    <value>Veuillez entrer un numÃ©ro de tÃ©lÃ©phone Ã  10 chiffres</value>
  </data>
  <data name="ValidationPostalCodeSpecific" xml:space="preserve">
    <value>Veuillez entrer un code postal canadien valide (ex: H1H 1H1)</value>
  </data>
  <data name="ValidationDateSpecific" xml:space="preserve">
    <value>Veuillez entrer une date valide au format AAAA-MM-JJ</value>
  </data>
  <data name="ValidationDateFuture" xml:space="preserve">
    <value>La date de naissance ne peut pas Ãªtre dans le futur</value>
  </data>
  <data name="ValidationDateTooOld" xml:space="preserve">
    <value>Veuillez entrer une date de naissance valide</value>
  </data>
  <data name="ValidationNameTooShort" xml:space="preserve">
    <value>Le nom doit contenir au moins 2 caractÃ¨res</value>
  </data>
  <data name="ValidationNameTooLong" xml:space="preserve">
    <value>Le nom ne peut pas dÃ©passer 50 caractÃ¨res</value>
  </data>
  <data name="ValidationAddressTooShort" xml:space="preserve">
    <value>L'adresse doit contenir au moins 5 caractÃ¨res</value>
  </data>
  <data name="ValidationCityTooShort" xml:space="preserve">
    <value>La ville doit contenir au moins 2 caractÃ¨res</value>
  </data>
  <data name="ValidationProvinceRequired" xml:space="preserve">
    <value>Veuillez sÃ©lectionner une province</value>
  </data>
  <data name="ValidationGenderRequired" xml:space="preserve">
    <value>Veuillez sÃ©lectionner un genre</value>
  </data>
  <data name="ValidationRegistrationTypeRequired" xml:space="preserve">
    <value>Veuillez sÃ©lectionner un type d'inscription</value>
  </data>
  <data name="ValidationPhoneTypeRequired" xml:space="preserve">
    <value>Veuillez sÃ©lectionner un type de tÃ©lÃ©phone</value>
  </data>
  
  <!-- Debug and Test Status Section -->
  <data name="DebugUrlGeneration" xml:space="preserve">
    <value>DEBUG: GÃ©nÃ©ration d'URL</value>
  </data>
  <data name="TestPostUrl" xml:space="preserve">
    <value>URL TestPost</value>
  </data>
  <data name="CreateUrl" xml:space="preserve">
    <value>URL CrÃ©er</value>
  </data>
  <data name="GetFirst10Url" xml:space="preserve">
    <value>URL GetFirst10</value>
  </data>
  <data name="CurrentUrl" xml:space="preserve">
    <value>URL actuelle</value>
  </data>
  <data name="BaseUrl" xml:space="preserve">
    <value>URL de base</value>
  </data>
  <data name="DebugTestPostRouting" xml:space="preserve">
    <value>DEBUG: Routage POST de test</value>
  </data>
  <data name="TestBasicPost" xml:space="preserve">
    <value>Test POST basique</value>
  </data>
  <data name="FormActionWillBe" xml:space="preserve">
    <value>L'action du formulaire sera</value>
  </data>
  <data name="EmergencyDirectHtmlPost" xml:space="preserve">
    <value>URGENCE: POST HTML direct</value>
  </data>
  <data name="DirectHtmlFormAction" xml:space="preserve">
    <value>Action du formulaire HTML direct</value>
  </data>
  <data name="DebugTestGetFunctionality" xml:space="preserve">
    <value>DEBUG: Test de fonctionnalitÃ© GET</value>
  </data>
  <data name="TestThatGetOperationsWork" xml:space="preserve">
    <value>Tester que les opÃ©rations GET fonctionnent en chargeant les 10 premiÃ¨res entrÃ©es de la base de donnÃ©es</value>
  </data>
  <data name="GetFirst10Entries" xml:space="preserve">
    <value>Obtenir les 10 premiÃ¨res entrÃ©es</value>
  </data>
  <data name="ThisWillCall" xml:space="preserve">
    <value>Ceci appellera</value>
  </data>
  <data name="UrlWillBe" xml:space="preserve">
    <value>L'URL sera</value>
  </data>
  <data name="GetTestActive" xml:space="preserve">
    <value>Test GET actif!</value>
  </data>
  <data name="CurrentlyShowingFirst" xml:space="preserve">
    <value>Affichage actuellement des {0} premiÃ¨res entrÃ©es.</value>
  </data>
  <data name="ShowAllEntries" xml:space="preserve">
    <value>Afficher toutes les entrÃ©es</value>
  </data>
  <data name="Actions" xml:space="preserve">
    <value>Actions</value>
  </data>
  <data name="Anonymous" xml:space="preserve">
    <value>Anonyme</value>
  </data>
  <data name="NoTestEntries" xml:space="preserve">
    <value>Aucune entrÃ©e de test</value>
  </data>
  <data name="CreateFirstEntry" xml:space="preserve">
    <value>CrÃ©ez votre premiÃ¨re entrÃ©e en utilisant le formulaire ci-dessus.</value>
  </data>
  <data name="TestStatus" xml:space="preserve">
    <value>Statut des Tests</value>
  </data>
  <data name="TestedFeatures" xml:space="preserve">
    <value>âœ… FonctionnalitÃ©s testÃ©es :</value>
  </data>
  <data name="DatabaseConnection" xml:space="preserve">
    <value>âœ… Connexion Ã  la base de donnÃ©es</value>
  </data>
  <data name="EntityCreationPost" xml:space="preserve">
    <value>âœ… CrÃ©ation d'entitÃ©s (POST)</value>
  </data>
  <data name="EntityReadingGet" xml:space="preserve">
    <value>âœ… Lecture d'entitÃ©s (GET)</value>
  </data>
  <data name="DataDisplay" xml:space="preserve">
    <value>âœ… Affichage des donnÃ©es</value>
  </data>
  <data name="SoftDelete" xml:space="preserve">
    <value>âœ… Suppression (soft delete)</value>
  </data>
  <data name="DataValidation" xml:space="preserve">
    <value>âœ… Validation des donnÃ©es</value>
  </data>
  <data name="AutoMapperMapping" xml:space="preserve">
    <value>âœ… Mapping AutoMapper</value>
  </data>
  <data name="UserInterface" xml:space="preserve">
    <value>âœ… Interface utilisateur</value>
  </data>
  <data name="NextSteps" xml:space="preserve">
    <value>ðŸ”„ Prochaines Ã©tapes :</value>
  </data>
  <data name="AzureAdAuthentication" xml:space="preserve">
    <value>ðŸ”„ Authentification Azure AD</value>
  </data>
  <data name="SqlServerDatabase" xml:space="preserve">
    <value>ðŸ”„ Base de donnÃ©es SQL Server</value>
  </data>
  <data name="RealParaHockeyEntities" xml:space="preserve">
    <value>ðŸ”„ EntitÃ©s Parahockey rÃ©elles</value>
  </data>
  <data name="AutomatedTests" xml:space="preserve">
    <value>ðŸ”„ Tests automatisÃ©s</value>
  </data>
  <data name="CiCdPipeline" xml:space="preserve">
    <value>ðŸ”„ Pipeline CI/CD</value>
  </data>

  <!-- Provinces -->
  <data name="ProvinceQC" xml:space="preserve">
    <value>QuÃ©bec (QC)</value>
  </data>
  <data name="ProvinceON" xml:space="preserve">
    <value>Ontario (ON)</value>
  </data>
  <data name="ProvinceBC" xml:space="preserve">
    <value>Colombie-Britannique (BC)</value>
  </data>
  <data name="ProvinceAB" xml:space="preserve">
    <value>Alberta (AB)</value>
  </data>
  <data name="ProvinceMB" xml:space="preserve">
    <value>Manitoba (MB)</value>
  </data>
  <data name="ProvinceSK" xml:space="preserve">
    <value>Saskatchewan (SK)</value>
  </data>
  <data name="ProvinceNS" xml:space="preserve">
    <value>Nouvelle-Ã‰cosse (NS)</value>
  </data>
  <data name="ProvinceNB" xml:space="preserve">
    <value>Nouveau-Brunswick (NB)</value>
  </data>
  <data name="ProvinceNL" xml:space="preserve">
    <value>Terre-Neuve-et-Labrador (NL)</value>
  </data>
  <data name="ProvincePE" xml:space="preserve">
    <value>ÃŽle-du-Prince-Ã‰douard (PE)</value>
  </data>
  <data name="ProvinceYT" xml:space="preserve">
    <value>Yukon (YT)</value>
  </data>
  <data name="ProvinceNT" xml:space="preserve">
    <value>Territoires du Nord-Ouest (NT)</value>
  </data>
  <data name="ProvinceNU" xml:space="preserve">
    <value>Nunavut (NU)</value>
  </data>
  <data name="DateFormatHelper" xml:space="preserve">
    <value>Format: AAAA-MM-JJ (ex: 1995-03-15)</value>
  </data>
  <data name="DatePlaceholder" xml:space="preserve">
    <value>AAAA-MM-JJ</value>
  </data>

  <data name="NoDataForReport" xml:space="preserve">
    <value>Aucune donnÃ©e disponible pour ce rapport</value>
  </data>

  <!-- Parent Information -->
  <data name="ParentInformation" xml:space="preserve">
    <value>Informations du parent/tuteur</value>
  </data>
  <data name="ParentGuardianDetails" xml:space="preserve">
    <value>DÃ©tails du parent/tuteur</value>
  </data>
  <data name="SecondParentGuardianDetails" xml:space="preserve">
    <value>DÃ©tails du deuxiÃ¨me parent/tuteur</value>
  </data>
  <data name="ParentType" xml:space="preserve">
    <value>Type de parent</value>
  </data>
  <data name="SelectParentType" xml:space="preserve">
    <value>SÃ©lectionner le type de parent</value>
  </data>
  <data name="Mother" xml:space="preserve">
    <value>MÃ¨re</value>
  </data>
  <data name="Father" xml:space="preserve">
    <value>PÃ¨re</value>
  </data>
  <data name="Guardian" xml:space="preserve">
    <value>Tuteur/Tutrice</value>
  </data>
  <data name="OtherRelation" xml:space="preserve">
    <value>Autre</value>
  </data>

  <!-- Emergency Contact Information -->
  <data name="EmergencyContactInformation" xml:space="preserve">
    <value>Contact d'urgence</value>
  </data>
  <data name="EmergencyContactDetails" xml:space="preserve">
    <value>DÃ©tails du contact d'urgence</value>
  </data>
  <data name="RelationToUser" xml:space="preserve">
    <value>Relation avec le membre</value>
  </data>
  <data name="SelectRelation" xml:space="preserve">
    <value>SÃ©lectionner la relation</value>
  </data>
  <data name="Spouse" xml:space="preserve">
    <value>Conjoint(e)</value>
  </data>
  <data name="Parent" xml:space="preserve">
    <value>Parent</value>
  </data>
  <data name="Sibling" xml:space="preserve">
    <value>FrÃ¨re/SÅ“ur</value>
  </data>
  <data name="Friend" xml:space="preserve">
    <value>Ami(e)</value>
  </data>

  <!-- Login Page -->
  <data name="LoginPageTitle" xml:space="preserve">
    <value>Recherche de membre - Parahockey</value>
  </data>
  <data name="SearchForMember" xml:space="preserve">
    <value>Rechercher un membre</value>
  </data>
  <data name="SearchButton" xml:space="preserve">
    <value>Rechercher</value>
  </data>
  <data name="SearchResults" xml:space="preserve">
    <value>RÃ©sultats de la recherche</value>
  </data>
  <data name="NoResultsFound" xml:space="preserve">
    <value>Aucun membre ne correspond Ã  vos critÃ¨res. Si votre compte a Ã©tÃ© dÃ©sactivÃ©, veuillez contacter l'administration.</value>
  </data>
  <data name="WrongEmail" xml:space="preserve">
    <value>Courriel incorrect</value>
  </data>
  <data name="SendCodeButton" xml:space="preserve">
    <value>Envoyer le code</value>
  </data>
  <data name="EmailVerification" xml:space="preserve">
    <value>VÃ©rification par courriel</value>
  </data>
  <data name="VerificationCodeSent" xml:space="preserve">
    <value>Un code de vÃ©rification a Ã©tÃ© envoyÃ© Ã  votre adresse de courriel.</value>
  </data>
  <data name="EnterCode" xml:space="preserve">
    <value>Entrez le code</value>
  </data>
  <data name="VerifyButton" xml:space="preserve">
    <value>VÃ©rifier</value>
  </data>
  
  <!-- Azure AD Admin Users -->
  <data name="AdminUsers" xml:space="preserve">
    <value>Utilisateurs administrateurs</value>
  </data>
  <data name="AzureADInfo" xml:space="preserve">
    <value>Informations sur Azure AD</value>
  </data>
  <data name="AzureADDescription" xml:space="preserve">
    <value>L'accÃ¨s administrateur utilise l'authentification Azure AD. Seuls les utilisateurs membres du groupe d'administration configurÃ© peuvent accÃ©der Ã  ces fonctionnalitÃ©s.</value>
  </data>
  <data name="CurrentUserInfo" xml:space="preserve">
    <value>Informations sur l'utilisateur actuel</value>
  </data>
  <data name="Username" xml:space="preserve">
    <value>Nom d'utilisateur</value>
  </data>
  <data name="AdminGroupId" xml:space="preserve">
    <value>ID du groupe d'administration</value>
  </data>
  <data name="UserGroups" xml:space="preserve">
    <value>Groupes de l'utilisateur</value>
  </data>
  <data name="AdminAccess" xml:space="preserve">
    <value>AccÃ¨s administrateur</value>
  </data>
  <data name="NoGroups" xml:space="preserve">
    <value>Aucun groupe</value>
  </data>
  <data name="ConfigurationSteps" xml:space="preserve">
    <value>Ã‰tapes de configuration</value>
  </data>
  <data name="AzureADStep1" xml:space="preserve">
    <value>Inscrire l'application dans Azure AD</value>
  </data>
  <data name="AzureADStep2" xml:space="preserve">
    <value>Configurer les valeurs dans appsettings.json</value>
  </data>
  <data name="AzureADStep3" xml:space="preserve">
    <value>CrÃ©er un groupe d'administration Azure AD</value>
  </data>
  <data name="AzureADStep4" xml:space="preserve">
    <value>Ajouter des utilisateurs au groupe d'administration</value>
  </data>
  <data name="AzureADStep5" xml:space="preserve">
    <value>Configurer l'ID du groupe dans appsettings.json</value>
  </data>
  <data name="AzureADWarning" xml:space="preserve">
    <value>Avertissement : Les valeurs Azure AD dans appsettings.json sont actuellement des valeurs par dÃ©faut. Elles doivent Ãªtre configurÃ©es avec vos valeurs Azure AD rÃ©elles.</value>
  </data>
  <data name="BackToDashboard" xml:space="preserve">
    <value>Retour au tableau de bord</value>
  </data>

  <!-- Calendar & Events -->
  <data name="CalendarEvents" xml:space="preserve">
    <value>Calendrier et Ã©vÃ©nements</value>
  </data>
  <data name="CalendarEventsDescription" xml:space="preserve">
    <value>GÃ©rer les Ã©vÃ©nements et les inscriptions des membres</value>
  </data>
  <data name="ComingSoon" xml:space="preserve">
    <value>BientÃ´t disponible</value>
  </data>
  <data name="PlannedFeatures" xml:space="preserve">
    <value>FonctionnalitÃ©s prÃ©vues :</value>
  </data>
  <data name="CreateManageEvents" xml:space="preserve">
    <value>CrÃ©er et gÃ©rer des Ã©vÃ©nements</value>
  </data>
  <data name="MemberEventSubscriptions" xml:space="preserve">
    <value>Inscriptions des membres aux Ã©vÃ©nements</value>
  </data>
  <data name="SeasonBasedEventOrganization" xml:space="preserve">
    <value>Organisation d'Ã©vÃ©nements par saison</value>
  </data>
  <data name="EventCapacityManagement" xml:space="preserve">
    <value>Gestion de la capacitÃ© des Ã©vÃ©nements</value>
  </data>
  <data name="CalendarFunctionalityMessage" xml:space="preserve">
    <value>Les fonctionnalitÃ©s de calendrier et d'Ã©vÃ©nements seront implÃ©mentÃ©es ici.</value>
  </data>

  <!-- Teams Management -->
  <data name="TeamsManagement" xml:space="preserve">
    <value>Gestion des Ã©quipes</value>
  </data>
  <data name="TeamsManagementDescription" xml:space="preserve">
    <value>GÃ©rer les Ã©quipes, les joueurs et les entraÃ®neurs</value>
  </data>
  <data name="CreateManageTeams" xml:space="preserve">
    <value>CrÃ©er et gÃ©rer des Ã©quipes</value>
  </data>
  <data name="AssignPlayersCoaches" xml:space="preserve">
    <value>Assigner des joueurs et des entraÃ®neurs</value>
  </data>
  <data name="TeamRosterManagement" xml:space="preserve">
    <value>Gestion des alignements d'Ã©quipe</value>
  </data>
  <data name="SeasonBasedTeamOrganization" xml:space="preserve">
    <value>Organisation d'Ã©quipes par saison</value>
  </data>
  <data name="TeamsManagementMessage" xml:space="preserve">
    <value>Les fonctionnalitÃ©s de gestion d'Ã©quipes seront implÃ©mentÃ©es ici.</value>
  </data>

  <!-- Rankings -->
  <data name="Rankings" xml:space="preserve">
    <value>Classements</value>
  </data>
  <data name="RankingsDescription" xml:space="preserve">
    <value>Voir les classements et les standings des Ã©quipes</value>
  </data>
  <data name="TeamStandingsRankings" xml:space="preserve">
    <value>Standings et classements des Ã©quipes</value>
  </data>
  <data name="WinLossTracking" xml:space="preserve">
    <value>Suivi des victoires/dÃ©faites</value>
  </data>
  <data name="PointsCalculation" xml:space="preserve">
    <value>Calcul des points</value>
  </data>
  <data name="SeasonBasedRankings" xml:space="preserve">
    <value>Classements par saison</value>
  </data>
  <data name="RankingsMessage" xml:space="preserve">
    <value>Les fonctionnalitÃ©s de classements seront implÃ©mentÃ©es ici.</value>
  </data>

  <!-- Statistics -->
  <data name="Statistics" xml:space="preserve">
    <value>Statistiques</value>
  </data>
  <data name="StatisticsDescription" xml:space="preserve">
    <value>Voir les statistiques de performance des joueurs et des Ã©quipes</value>
  </data>
  <data name="PlayerPerformanceStatistics" xml:space="preserve">
    <value>Statistiques de performance des joueurs</value>
  </data>
  <data name="TeamPerformanceMetrics" xml:space="preserve">
    <value>MÃ©triques de performance des Ã©quipes</value>
  </data>
  <data name="SeasonComparisonReports" xml:space="preserve">
    <value>Rapports de comparaison des saisons</value>
  </data>
  <data name="ManualStatisticsEntry" xml:space="preserve">
    <value>Saisie manuelle des statistiques</value>
  </data>
  <data name="StatisticsMessage" xml:space="preserve">
    <value>Les fonctionnalitÃ©s de statistiques seront implÃ©mentÃ©es ici.</value>
  </data>

  <!-- Admin Management -->
  <data name="ManageAdmins" xml:space="preserve">
    <value>GÃ©rer les administrateurs</value>
  </data>
  <data name="CurrentAdministrators" xml:space="preserve">
    <value>Administrateurs actuels</value>
  </data>
  <data name="AddNewAdministrator" xml:space="preserve">
    <value>Ajouter un nouvel administrateur</value>
  </data>
  <data name="YourAccount" xml:space="preserve">
    <value>Votre compte</value>
  </data>
  <data name="LoggedInAs" xml:space="preserve">
    <value>ConnectÃ© en tant que :</value>
  </data>
  <data name="Authentication" xml:space="preserve">
    <value>Authentification :</value>
  </data>
  <data name="MicrosoftAzureAD" xml:space="preserve">
    <value>Microsoft/Azure AD</value>
  </data>
  <data name="Name" xml:space="preserve">
    <value>Nom</value>
  </data>
  <data name="Role" xml:space="preserve">
    <value>RÃ´le</value>
  </data>
  <data name="Added" xml:space="preserve">
    <value>AjoutÃ©</value>
  </data>
  <data name="MasterAdmin" xml:space="preserve">
    <value>Administrateur maÃ®tre</value>
  </data>
  <data name="NormalAdmin" xml:space="preserve">
    <value>Administrateur normal</value>
  </data>
  <data name="Disabled" xml:space="preserve">
    <value>DÃ©sactivÃ©</value>
  </data>
  <data name="CannotRemove" xml:space="preserve">
    <value>Ne peut pas Ãªtre supprimÃ©</value>
  </data>
  <data name="Remove" xml:space="preserve">
    <value>Supprimer</value>
  </data>
  <data name="EmailAddress" xml:space="preserve">
    <value>Adresse courriel</value>
  </data>
  <data name="FullName" xml:space="preserve">
    <value>Nom complet</value>
  </data>
  <data name="AddAdmin" xml:space="preserve">
    <value>Ajouter administrateur</value>
  </data>
  <data name="NoAdministratorsFound" xml:space="preserve">
    <value>Aucun administrateur trouvÃ©.</value>
  </data>

  <!-- Quick Actions -->
  <data name="QuickActions" xml:space="preserve">
    <value>Actions rapides</value>
  </data>
  <data name="ViewAllMembers" xml:space="preserve">
    <value>GÃ©rer les membres</value>
  </data>
  <data name="AddNewMember" xml:space="preserve">
    <value>Ajouter un nouveau membre</value>
  </data>
  <data name="SystemInformation" xml:space="preserve">
    <value>Informations systÃ¨me</value>
  </data>
  <data name="BackToSite" xml:space="preserve">
    <value>Retour au site</value>
  </data>

  <!-- Recent Audit Activity -->
  <data name="RecentMemberAudit" xml:space="preserve">
    <value>ActivitÃ© d'audit rÃ©cente</value>
  </data>
  <data name="RecentChanges" xml:space="preserve">
    <value>Changements rÃ©cents</value>
  </data>
  <data name="ViewAllAuditHistory" xml:space="preserve">
    <value>Voir tout l'historique d'audit</value>
  </data>
  <data name="NoRecentActivity" xml:space="preserve">
    <value>Aucune activitÃ© rÃ©cente.</value>
  </data>
  <data name="AuditDateTime" xml:space="preserve">
    <value>Date/Heure</value>
  </data>
  <data name="Action" xml:space="preserve">
    <value>Action</value>
  </data>
  <data name="PerformedBy" xml:space="preserve">
    <value>EffectuÃ© par</value>
  </data>
  <data name="Description" xml:space="preserve">
    <value>Description</value>
  </data>
  <data name="Create" xml:space="preserve">
    <value>CrÃ©er</value>
  </data>
  <data name="Update" xml:space="preserve">
    <value>Modifier</value>
  </data>
  <data name="Delete" xml:space="preserve">
    <value>Supprimer</value>
  </data>
  <data name="Admin" xml:space="preserve">
    <value>Admin</value>
  </data>
  <data name="Member" xml:space="preserve">
    <value>Membre</value>
  </data>
  <data name="System" xml:space="preserve">
    <value>SystÃ¨me</value>
  </data>
  <data name="RecentMemberRegistrations" xml:space="preserve">
    <value>Inscriptions rÃ©centes de membres</value>
  </data>
  <data name="DateCreated" xml:space="preserve">
    <value>Date de crÃ©ation</value>
  </data>
  <data name="NoMembersRegistered" xml:space="preserve">
    <value>Aucun membre inscrit encore.</value>
  </data>
  <data name="EntityType" xml:space="preserve">
    <value>Type d'entitÃ©</value>
  </data>
  <data name="Filters" xml:space="preserve">
    <value>Filtres</value>
  </data>
  <data name="AllEntityTypes" xml:space="preserve">
    <value>Tous les types d'entitÃ©s</value>
  </data>
  <data name="AllActions" xml:space="preserve">
    <value>Toutes les actions</value>
  </data>
  <data name="Filter" xml:space="preserve">
    <value>Filtrer</value>
  </data>
  <data name="Clear" xml:space="preserve">
    <value>Effacer</value>
  </data>
  <data name="ShowingResults" xml:space="preserve">
    <value>Affichage des rÃ©sultats {0} Ã  {1} sur {2}</value>
  </data>
  <data name="Source" xml:space="preserve">
    <value>Source</value>
  </data>
  <data name="NoDescriptionAvailable" xml:space="preserve">
    <value>Aucune description disponible</value>
  </data>
  <data name="Previous" xml:space="preserve">
    <value>PrÃ©cÃ©dent</value>
  </data>
  <data name="Next" xml:space="preserve">
    <value>Suivant</value>
  </data>
  <data name="NoAuditLogsFound" xml:space="preserve">
    <value>Aucun journal d'audit trouvÃ©</value>
  </data>
  <data name="NoAuditLogsFoundDesc" xml:space="preserve">
    <value>Aucune activitÃ© d'audit ne correspond aux critÃ¨res de filtre sÃ©lectionnÃ©s.</value>
  </data>
  <data name="ViewAllRecords" xml:space="preserve">
    <value>Voir tous les enregistrements</value>
  </data>

  <!-- Admin Dashboard -->
  <data name="AdminDashboard" xml:space="preserve">
    <value>Tableau de bord administrateur</value>
  </data>
  <data name="Welcome" xml:space="preserve">
    <value>Bienvenue</value>
  </data>
  <data name="TotalMembers" xml:space="preserve">
    <value>Total des membres</value>
  </data>
  <data name="TotalParents" xml:space="preserve">
    <value>Total des parents</value>
  </data>
  <data name="EmergencyContacts" xml:space="preserve">
    <value>Contacts d'urgence</value>
  </data>
  <data name="ViewAll" xml:space="preserve">
    <value>Voir tout</value>
  </data>
  <data name="SystemInfo" xml:space="preserve">
    <value>Informations systÃ¨me</value>
  </data>

  <!-- Access Denied -->
  <data name="AccessDenied" xml:space="preserve">
    <value>AccÃ¨s refusÃ©</value>
  </data>
  <data name="NoPermissionMessage" xml:space="preserve">
    <value>Vous n'avez pas la permission d'accÃ©der Ã  cette zone</value>
  </data>
  <data name="OnlyAdminsMessage" xml:space="preserve">
    <value>Seuls les administrateurs peuvent accÃ©der au panneau d'administration.</value>
  </data>
  <data name="ContactAdminMessage" xml:space="preserve">
    <value>Si vous pensez qu'il s'agit d'une erreur, veuillez contacter l'administrateur systÃ¨me.</value>
  </data>
  <data name="PleaseLoginMessage" xml:space="preserve">
    <value>Veuillez vous connecter pour accÃ©der au panneau d'administration.</value>
  </data>
  <data name="ReturnToHome" xml:space="preserve">
    <value>Retour Ã  l'accueil</value>
  </data>

  <!-- System Information -->
  <data name="EnvironmentConfiguration" xml:space="preserve">
    <value>Configuration de l'environnement</value>
  </data>
  <data name="Theme" xml:space="preserve">
    <value>ThÃ¨me</value>
  </data>
  <data name="ShowBanner" xml:space="preserve">
    <value>Afficher la banniÃ¨re</value>
  </data>
  <data name="DatabaseType" xml:space="preserve">
    <value>Type de base de donnÃ©es</value>
  </data>
  <data name="DatabaseStatus" xml:space="preserve">
    <value>Statut de la base de donnÃ©es</value>
  </data>
  <data name="DatabaseStatistics" xml:space="preserve">
    <value>Statistiques de la base de donnÃ©es</value>
  </data>
  <data name="Connected" xml:space="preserve">
    <value>ConnectÃ©</value>
  </data>
  <data name="NotConnected" xml:space="preserve">
    <value>Non connectÃ©</value>
  </data>
  <data name="Enabled" xml:space="preserve">
    <value>ActivÃ©</value>
  </data>
  <data name="DisabledDevelopment" xml:space="preserve">
    <value>DÃ©sactivÃ© (DÃ©veloppement)</value>
  </data>
  <data name="Yes" xml:space="preserve">
    <value>Oui</value>
  </data>
  <data name="No" xml:space="preserve">
    <value>Non</value>
  </data>
  <data name="QuickActionsText" xml:space="preserve">
    <value>Actions rapides :</value>
  </data>
  <data name="AuthenticationStatus" xml:space="preserve">
    <value>Statut d'authentification</value>
  </data>
  <data name="UserAuthenticated" xml:space="preserve">
    <value>Utilisateur authentifiÃ©</value>
  </data>
  <data name="CurrentUser" xml:space="preserve">
    <value>Utilisateur actuel</value>
  </data>
  <data name="ServerTime" xml:space="preserve">
    <value>Heure du serveur</value>
  </data>
  <data name="DevelopmentModeMessage" xml:space="preserve">
    <value>L'authentification est dÃ©sactivÃ©e pour un dÃ©veloppement plus rapide. Dans les environnements de test/production, l'authentification Microsoft sera requise pour accÃ©der Ã  ce panneau d'administration.</value>
  </data>
  <data name="AuthenticationRequired" xml:space="preserve">
    <value>Authentification requise</value>
  </data>
  <data name="AuthenticationError" xml:space="preserve">
    <value>Vous ne devriez pas pouvoir voir cette page sans authentification. VÃ©rifiez votre configuration d'authentification.</value>
  </data>
  <data name="AuthenticatedAccess" xml:space="preserve">
    <value>AccÃ¨s authentifiÃ©</value>
  </data>
  <data name="AuthenticatedMessage" xml:space="preserve">
    <value>Vous Ãªtes authentifiÃ© avec succÃ¨s via un compte Microsoft.</value>
  </data>
  <data name="BackToAdminDashboard" xml:space="preserve">
    <value>Retour au tableau de bord administrateur</value>
  </data>

  <!-- Members Management -->
  <data name="ManageMembers" xml:space="preserve">
    <value>GÃ©rer les membres</value>
  </data>
  <data name="SearchByNameOrEmail" xml:space="preserve">
    <value>Rechercher par nom ou email...</value>
  </data>
  <data name="Search" xml:space="preserve">
    <value>Rechercher</value>
  </data>
  <data name="Showing" xml:space="preserve">
    <value>Affichage</value>
  </data>
  <data name="Of" xml:space="preserve">
    <value>de</value>
  </data>
  <data name="Members" xml:space="preserve">
    <value>membres</value>
  </data>
  <data name="Matching" xml:space="preserve">
    <value>correspondant Ã </value>
  </data>
  <data name="Page" xml:space="preserve">
    <value>Page</value>
  </data>
  <data name="RegistrationDate" xml:space="preserve">
    <value>Date d'inscription</value>
  </data>
  <data name="YearsOld" xml:space="preserve">
    <value>ans</value>
  </data>
  <data name="Unknown" xml:space="preserve">
    <value>Inconnu</value>
  </data>
  <data name="ViewDetails" xml:space="preserve">
    <value>Voir les dÃ©tails</value>
  </data>
  <data name="Edit" xml:space="preserve">
    <value>Modifier</value>
  </data>
  <data name="NoMembersFound" xml:space="preserve">
    <value>Aucun membre trouvÃ© correspondant Ã </value>
  </data>
  <data name="TryDifferentSearch" xml:space="preserve">
    <value>Essayez un terme de recherche diffÃ©rent ou</value>
  </data>
  <data name="ViewAllMembersLink" xml:space="preserve">
    <value>voir tous les membres</value>
  </data>
  <data name="NoMembersRegisteredYet" xml:space="preserve">
    <value>Aucun membre inscrit encore</value>
  </data>
  <data name="StartByRegistering" xml:space="preserve">
    <value>Commencez par inscrire le premier membre.</value>
  </data>
  <data name="RegisterFirstMember" xml:space="preserve">
    <value>Inscrire le premier membre</value>
  </data>
  <data name="ConfirmDelete" xml:space="preserve">
    <value>Confirmer la suppression</value>
  </data>
  <data name="ConfirmDeleteMessage" xml:space="preserve">
    <value>ÃŠtes-vous sÃ»r de vouloir supprimer le membre</value>
  </data>
  <data name="DeleteWarning" xml:space="preserve">
    <value>Cette action ne peut pas Ãªtre annulÃ©e et supprimera Ã©galement toutes les informations associÃ©es du parent et du contact d'urgence.</value>
  </data>
  <data name="Cancel" xml:space="preserve">
    <value>Annuler</value>
  </data>
  <data name="DeleteMember" xml:space="preserve">
    <value>Supprimer le membre</value>
  </data>

  <!-- Member Details -->
  <data name="MemberDetails" xml:space="preserve">
    <value>DÃ©tails du membre</value>
  </data>
  <data name="EditMember" xml:space="preserve">
    <value>Modifier le membre</value>
  </data>
  <data name="BackToMembersList" xml:space="preserve">
    <value>Retour Ã  la liste des membres</value>
  </data>
  <data name="PersonalInformation" xml:space="preserve">
    <value>Informations personnelles</value>
  </data>
  <data name="MemberID" xml:space="preserve">
    <value>ID du membre</value>
  </data>
  <data name="NotSpecified" xml:space="preserve">
    <value>Non spÃ©cifiÃ©</value>
  </data>
  <data name="QuickStats" xml:space="preserve">
    <value>Statistiques rapides</value>
  </data>
  <data name="ParentsText" xml:space="preserve">
    <value>Parent(s)</value>
  </data>
  <data name="EmergencyContact" xml:space="preserve">
    <value>Contact d'urgence</value>
  </data>
  <data name="AddressInformation" xml:space="preserve">
    <value>Informations d'adresse</value>
  </data>
  <data name="ParentGuardianInformation" xml:space="preserve">
    <value>Informations du parent/tuteur</value>
  </data>
  <data name="Type" xml:space="preserve">
    <value>Type</value>
  </data>
  <data name="Relation" xml:space="preserve">
    <value>Relation</value>
  </data>
  <data name="AuditHistory" xml:space="preserve">
    <value>Historique d'audit</value>
  </data>
  <data name="Created" xml:space="preserve">
    <value>CrÃ©Ã©</value>
  </data>
  <data name="By" xml:space="preserve">
    <value>par</value>
  </data>
  <data name="LastModified" xml:space="preserve">
    <value>DerniÃ¨re modification</value>
  </data>
  <data name="NeverModified" xml:space="preserve">
    <value>Jamais modifiÃ©</value>
  </data>
  <data name="TotalChanges" xml:space="preserve">
    <value>Total des modifications</value>
  </data>
  <data name="ChangeHistory" xml:space="preserve">
    <value>Historique des modifications</value>
  </data>
  <data name="NoAuditHistoryAvailable" xml:space="preserve">
    <value>Aucun historique d'audit disponible.</value>
  </data>
  <data name="IP" xml:space="preserve">
    <value>IP</value>
  </data>
  <data name="Active" xml:space="preserve">
    <value>Actif</value>
  </data>
  <data name="Inactive" xml:space="preserve">
    <value>Inactif</value>
  </data>
  <data name="Status" xml:space="preserve">
    <value>Statut</value>
  </data>

  <!-- Setup Master Admin -->
  <data name="SetupMasterAdmin" xml:space="preserve">
    <value>Configurer l'administrateur maÃ®tre</value>
  </data>
  <data name="FirstTimeSetup" xml:space="preserve">
    <value>Configuration initiale :</value>
  </data>
  <data name="NoAdminsExist" xml:space="preserve">
    <value>Aucun administrateur n'existe encore. Vous deviendrez l'administrateur maÃ®tre.</value>
  </data>
  <data name="YouAreLoggedInAs" xml:space="preserve">
    <value>Vous Ãªtes connectÃ© en tant que :</value>
  </data>
  <data name="Important" xml:space="preserve">
    <value>Important :</value>
  </data>
  <data name="MasterAdminCannotBeRemoved" xml:space="preserve">
    <value>En tant qu'administrateur maÃ®tre, vous ne pouvez pas Ãªtre supprimÃ© du systÃ¨me. Vous pourrez ajouter et supprimer d'autres administrateurs.</value>
  </data>
  <data name="SetupMasterAdminAccount" xml:space="preserve">
    <value>Configurer le compte d'administrateur maÃ®tre</value>
  </data>

  <!-- Teams, Calendar, Rankings, Statistics coming soon messages -->
  <data name="ManageEventsAndSubscriptions" xml:space="preserve">
    <value>GÃ©rer les Ã©vÃ©nements et les inscriptions des membres</value>
  </data>
  <data name="ManageTeamsPlayersCoaches" xml:space="preserve">
    <value>GÃ©rer les Ã©quipes, les joueurs et les entraÃ®neurs</value>
  </data>
  <data name="ViewTeamStandings" xml:space="preserve">
    <value>Voir les classements et les standings des Ã©quipes</value>
  </data>
  <data name="ViewPlayerTeamStats" xml:space="preserve">
    <value>Voir les statistiques de performance des joueurs et des Ã©quipes</value>
  </data>

  <!-- Confirmation messages -->
  <data name="AreYouSureRemoveAdmin" xml:space="preserve">
    <value>ÃŠtes-vous sÃ»r de vouloir supprimer cet administrateur ?</value>
  </data>

  <!-- Controller messages -->
  <data name="MemberNotFound" xml:space="preserve">
    <value>Membre non trouvÃ©.</value>
  </data>
  <data name="MemberDeletedSuccessfully" xml:space="preserve">
    <value>Le membre {0} {1} a Ã©tÃ© supprimÃ© avec succÃ¨s.</value>
  </data>
  <data name="ErrorDeletingMember" xml:space="preserve">
    <value>Une erreur s'est produite lors de la suppression du membre.</value>
  </data>
  <data name="MasterAdminCreatedSuccessfully" xml:space="preserve">
    <value>Compte d'administrateur maÃ®tre crÃ©Ã© avec succÃ¨s !</value>
  </data>
  <data name="OnlyMasterAdminsCanCreate" xml:space="preserve">
    <value>Seuls les administrateurs maÃ®tres peuvent crÃ©er d'autres administrateurs maÃ®tres.</value>
  </data>
  <data name="EmailAndNameRequired" xml:space="preserve">
    <value>Le courriel et le nom sont requis.</value>
  </data>
  <data name="AdminAlreadyExists" xml:space="preserve">
    <value>L'administrateur existe dÃ©jÃ .</value>
  </data>
  <data name="AdminAddedSuccessfully" xml:space="preserve">
    <value>{0} {1} ajoutÃ© avec succÃ¨s !</value>
  </data>
  <data name="AdminNotFound" xml:space="preserve">
    <value>Administrateur non trouvÃ©.</value>
  </data>
  <data name="CannotRemoveMasterAdmin" xml:space="preserve">
    <value>Impossible de supprimer l'administrateur maÃ®tre.</value>
  </data>
  <data name="AdminRemovedSuccessfully" xml:space="preserve">
    <value>L'administrateur {0} a Ã©tÃ© supprimÃ© avec succÃ¨s !</value>
  </data>
  
  <!-- Disable/Enable Actions -->
  <data name="Disable" xml:space="preserve">
    <value>DÃ©sactiver</value>
  </data>
  <data name="Enable" xml:space="preserve">
    <value>Activer</value>
  </data>
  <data name="DisableMember" xml:space="preserve">
    <value>DÃ©sactiver le membre</value>
  </data>
  <data name="EnableMember" xml:space="preserve">
    <value>Activer le membre</value>
  </data>
  <data name="ConfirmDisable" xml:space="preserve">
    <value>Confirmer la dÃ©sactivation</value>
  </data>
  <data name="ConfirmEnable" xml:space="preserve">
    <value>Confirmer l'activation</value>
  </data>
  <data name="ConfirmDisableMessage" xml:space="preserve">
    <value>ÃŠtes-vous sÃ»r de vouloir dÃ©sactiver</value>
  </data>
  <data name="ConfirmEnableMessage" xml:space="preserve">
    <value>ÃŠtes-vous sÃ»r de vouloir activer</value>
  </data>
  <data name="DisableWarning" xml:space="preserve">
    <value>Le membre ne pourra plus se connecter ou Ãªtre affichÃ© dans les listes actives.</value>
  </data>
  <data name="EnableWarning" xml:space="preserve">
    <value>Le membre pourra Ã  nouveau se connecter et Ãªtre affichÃ© dans les listes actives.</value>
  </data>
  <data name="MemberDisabledSuccessfully" xml:space="preserve">
    <value>Le membre {0} {1} a Ã©tÃ© dÃ©sactivÃ© avec succÃ¨s.</value>
  </data>
  <data name="MemberEnabledSuccessfully" xml:space="preserve">
    <value>Le membre {0} {1} a Ã©tÃ© activÃ© avec succÃ¨s.</value>
  </data>
  <data name="ErrorDisablingMember" xml:space="preserve">
    <value>Erreur lors de la dÃ©sactivation du membre.</value>
  </data>
  <data name="ErrorEnablingMember" xml:space="preserve">
    <value>Erreur lors de l'activation du membre.</value>
  </data>
  <data name="EventCalendar" xml:space="preserve">
    <value>Calendrier des Ã©vÃ©nements</value>
  </data>
  <data name="AddEvent" xml:space="preserve">
    <value>Ajouter un Ã©vÃ©nement</value>
  </data>
  <data name="AllEvents" xml:space="preserve">
    <value>Tous les Ã©vÃ©nements</value>
  </data>
  <data name="TotalEvents" xml:space="preserve">
    <value>Total Ã©vÃ©nements</value>
  </data>
  <data name="PublishedEvents" xml:space="preserve">
    <value>Ã‰vÃ©nements publiÃ©s</value>
  </data>
  <data name="UpcomingEvents" xml:space="preserve">
    <value>Ã‰vÃ©nements Ã  venir</value>
  </data>
  <data name="NoUpcomingEvents" xml:space="preserve">
    <value>Aucun Ã©vÃ©nement Ã  venir</value>
  </data>
  <data name="EventCategories" xml:space="preserve">
    <value>CatÃ©gories d'Ã©vÃ©nements</value>
  </data>
  <data name="EventDetails" xml:space="preserve">
    <value>DÃ©tails de l'Ã©vÃ©nement</value>
  </data>
  <data name="ViewEventDetails" xml:space="preserve">
    <value>Voir les dÃ©tails de l'Ã©vÃ©nement</value>
  </data>
  <data name="StartDate" xml:space="preserve">
    <value>Date de dÃ©but</value>
  </data>
  <data name="EndDate" xml:space="preserve">
    <value>Date de fin</value>
  </data>
  <data name="MaxParticipants" xml:space="preserve">
    <value>Participants max</value>
  </data>
  <data name="MaxParticipantsHelp" xml:space="preserve">
    <value>Laissez vide si aucune limite</value>
  </data>
  <data name="UnlimitedParticipants" xml:space="preserve">
    <value>Participants illimitÃ©s</value>
  </data>
  <data name="AllDayEvent" xml:space="preserve">
    <value>Ã‰vÃ©nement toute la journÃ©e</value>
  </data>
  <data name="RequiresRegistration" xml:space="preserve">
    <value>Inscription requise</value>
  </data>
  <data name="ContactPerson" xml:space="preserve">
    <value>Personne-contact</value>
  </data>
  <data name="ContactEmail" xml:space="preserve">
    <value>Courriel de contact</value>
  </data>
  <data name="ContactPhone" xml:space="preserve">
    <value>TÃ©lÃ©phone de contact</value>
  </data>
  <data name="OrganizerNotes" xml:space="preserve">
    <value>Notes de l'organisateur</value>
  </data>
  <data name="PublishEvent" xml:space="preserve">
    <value>Publier l'Ã©vÃ©nement</value>
  </data>
  <data name="ConfirmDeleteEvent" xml:space="preserve">
    <value>ÃŠtes-vous sÃ»r de vouloir supprimer cet Ã©vÃ©nement ?</value>
  </data>
  <data name="EventCategory_Practice" xml:space="preserve">
    <value>Pratique</value>
  </data>
  <data name="EventCategory_Practice_Desc" xml:space="preserve">
    <value>SÃ©ances d'entraÃ®nement et de pratique</value>
  </data>
  <data name="EventCategory_Game" xml:space="preserve">
    <value>Match</value>
  </data>
  <data name="EventCategory_Game_Desc" xml:space="preserve">
    <value>SÃ©ances de match et compÃ©titions</value>
  </data>
  <data name="EventCategory_Tournament" xml:space="preserve">
    <value>Tournoi</value>
  </data>
  <data name="EventCategory_Tournament_Desc" xml:space="preserve">
    <value>Tournois et championnats</value>
  </data>
  <data name="EventCategory_Training" xml:space="preserve">
    <value>Formation</value>
  </data>
  <data name="EventCategory_Training_Desc" xml:space="preserve">
    <value>Formations et ateliers</value>
  </data>
  <data name="EventCategory_Meeting" xml:space="preserve">
    <value>RÃ©union</value>
  </data>
  <data name="EventCategory_Meeting_Desc" xml:space="preserve">
    <value>RÃ©unions et assemblÃ©es</value>
  </data>
  <data name="EventCategory_Social" xml:space="preserve">
    <value>Social</value>
  </data>
  <data name="EventCategory_Social_Desc" xml:space="preserve">
    <value>Ã‰vÃ©nements sociaux et cÃ©lÃ©brations</value>
  </data>
  <data name="EventCategory_Fundraiser" xml:space="preserve">
    <value>Collecte de fonds</value>
  </data>
  <data name="EventCategory_Fundraiser_Desc" xml:space="preserve">
    <value>Ã‰vÃ©nements de collecte de fonds</value>
  </data>
  <data name="EventCategory_Other" xml:space="preserve">
    <value>Autre</value>
  </data>
  <data name="EventCategory_Other_Desc" xml:space="preserve">
    <value>Autres Ã©vÃ©nements</value>
  </data>
  <data name="EventCategory_Tentative" xml:space="preserve">
    <value>Tentatif</value>
  </data>
  <data name="EventCategory_Tentative_Desc" xml:space="preserve">
    <value>Ã‰vÃ©nements tentatives et Ã  confirmer</value>
  </data>
  <data name="EventCategory_FirstShift" xml:space="preserve">
    <value>First Shift</value>
  </data>
  <data name="EventCategory_FirstShift_Desc" xml:space="preserve">
    <value>Ã‰vÃ©nements First Shift</value>
  </data>
  <data name="EventCategory_Camp" xml:space="preserve">
    <value>Camp</value>
  </data>
  <data name="EventCategory_Camp_Desc" xml:space="preserve">
    <value>Camps d'entraÃ®nement et de dÃ©veloppement</value>
  </data>
  <data name="EventCategory_Serie" xml:space="preserve">
    <value>SÃ©rie</value>
  </data>
  <data name="EventCategory_Serie_Desc" xml:space="preserve">
    <value>SÃ©rie de matchs ou d'Ã©vÃ©nements</value>
  </data>
  <data name="EventCategory_Tentatif" xml:space="preserve">
    <value>Tentatif</value>
  </data>
  <data name="EventCategory_Tentatif_Desc" xml:space="preserve">
    <value>Ã‰vÃ©nements tentatives et Ã  confirmer</value>
  </data>
  <data name="EventCategory_PratiqueFirstShift" xml:space="preserve">
    <value>Pratique First Shift</value>
  </data>
  <data name="EventCategory_PratiqueFirstShift_Desc" xml:space="preserve">
    <value>Sessions de pratique dÃ©diÃ©es au programme First Shift</value>
  </data>
  
  <!-- Public Calendar Keys -->
  <data name="ViewCalendar" xml:space="preserve">
    <value>Voir le calendrier</value>
  </data>
  <data name="ViewPublicEventsAndSchedule" xml:space="preserve">
    <value>Consultez les Ã©vÃ©nements publics et les horaires.</value>
  </data>
  <data name="BackToHome" xml:space="preserve">
    <value>Retour Ã  l'accueil</value>
  </data>
  <data name="LoginRequired" xml:space="preserve">
    <value>Connexion requise</value>
  </data>
  <data name="MembershipRequired" xml:space="preserve">
    <value>AdhÃ©sion requise</value>
  </data>
  <data name="LoginRequiredMessage" xml:space="preserve">
    <value>Pour voir les dÃ©tails des Ã©vÃ©nements et vous y inscrire, vous devez Ãªtre connectÃ© en tant que membre.</value>
  </data>
  <data name="MemberBenefitsText" xml:space="preserve">
    <value>Les membres peuvent s'inscrire aux Ã©vÃ©nements, gÃ©rer leur profil et accÃ©der aux fonctionnalitÃ©s exclusives.</value>
  </data>
  <data name="ErrorLoadingCalendar" xml:space="preserve">
    <value>Erreur lors du chargement du calendrier</value>
  </data>

  <!-- Import/Export Calendar -->
  <data name="ImportExport" xml:space="preserve">
    <value>Importer/Exporter</value>
  </data>
  <data name="ImportEvents" xml:space="preserve">
    <value>Importer des Ã©vÃ©nements</value>
  </data>
  <data name="ExportEvents" xml:space="preserve">
    <value>Exporter des Ã©vÃ©nements</value>
  </data>
  <data name="SelectFile" xml:space="preserve">
    <value>SÃ©lectionner un fichier</value>
  </data>
  <data name="ImportFileHelp" xml:space="preserve">
    <value>SÃ©lectionnez un fichier CSV ou Excel contenant les Ã©vÃ©nements Ã  importer</value>
  </data>
  <data name="ImportInstructions" xml:space="preserve">
    <value>Instructions d'importation</value>
  </data>
  <data name="ImportSupportedFormats" xml:space="preserve">
    <value>Formats supportÃ©s</value>
  </data>
  <data name="ImportExpectedColumns" xml:space="preserve">
    <value>Colonnes attendues</value>
  </data>
  <data name="ExportInformation" xml:space="preserve">
    <value>Informations d'exportation</value>
  </data>
  <data name="ExportDescription" xml:space="preserve">
    <value>Exportez les Ã©vÃ©nements du mois sÃ©lectionnÃ© dans le format de votre choix</value>
  </data>
  <data name="FileFormat" xml:space="preserve">
    <value>Format de fichier</value>
  </data>
  <data name="Year" xml:space="preserve">
    <value>AnnÃ©e</value>
  </data>
  <data name="Month" xml:space="preserve">
    <value>Mois</value>
  </data>
  <data name="PleaseSelectFile" xml:space="preserve">
    <value>Veuillez sÃ©lectionner un fichier</value>
  </data>
  <data name="FileTooLarge" xml:space="preserve">
    <value>Le fichier est trop volumineux (max 10 MB)</value>
  </data>
  <data name="UnsupportedFileFormat" xml:space="preserve">
    <value>Format de fichier non supportÃ©</value>
  </data>
  <data name="EventsImportedSuccessfully" xml:space="preserve">
    <value>{0} Ã©vÃ©nements importÃ©s avec succÃ¨s</value>
  </data>
  <data name="ImportWarnings" xml:space="preserve">
    <value>avec {0} avertissements</value>
  </data>
  <data name="NoEventsToImport" xml:space="preserve">
    <value>Aucun Ã©vÃ©nement Ã  importer</value>
  </data>
  <data name="ImportError" xml:space="preserve">
    <value>Erreur d'importation</value>
  </data>
  <data name="ExportError" xml:space="preserve">
    <value>Erreur d'exportation</value>
  </data>
  
  <!-- Audit Log Localization -->
  <data name="NewMember" xml:space="preserve">
    <value>Nouveau membre</value>
  </data>
  <data name="Event" xml:space="preserve">
    <value>Ã‰vÃ©nement</value>
  </data>
  <data name="edited" xml:space="preserve">
    <value>modifiÃ©</value>
  </data>
  <data name="deleted" xml:space="preserve">
    <value>supprimÃ©</value>
  </data>
  <data name="Start" xml:space="preserve">
    <value>DÃ©but</value>
  </data>
  <data name="End" xml:space="preserve">
    <value>Fin</value>
  </data>
  
  <!-- Duplicate Detection Messages -->
  <data name="DuplicateEmailMessage" xml:space="preserve">
    <value>Cette adresse courriel est dÃ©jÃ  utilisÃ©e par un membre existant.</value>
  </data>
  <data name="DuplicatePartialMessage" xml:space="preserve">
    <value>Un membre avec ce nom et cette date de naissance existe dÃ©jÃ  avec l'adresse courriel {0}. Est-ce votre adresse courriel?</value>
  </data>
  <data name="DuplicateOkButton" xml:space="preserve">
    <value>OK</value>
  </data>
  <data name="DuplicateModifyButton" xml:space="preserve">
    <value>Modifier le membre</value>
  </data>
  <data name="DuplicateYesButton" xml:space="preserve">
    <value>Oui</value>
  </data>
  <data name="DuplicateNoButton" xml:space="preserve">
    <value>Non</value>
  </data>
  <data name="Attention" xml:space="preserve">
    <value>Attention</value>
  </data>
  <data name="DuplicateEmailUpdateError" xml:space="preserve">
    <value>Cette adresse courriel est dÃ©jÃ  utilisÃ©e par un autre membre.</value>
  </data>
  <data name="HQc_Id" xml:space="preserve">
    <value>ID Hockey QuÃ©bec</value>
  </data>
  <data name="HQc_IdPlaceholder" xml:space="preserve">
    <value>ID Hockey QuÃ©bec (optionnel)</value>
  </data>
  <data name="DuplicateHQcIdUpdateError" xml:space="preserve">
    <value>Cet ID Hockey QuÃ©bec est dÃ©jÃ  utilisÃ© par un autre membre.</value>
  </data>
  <data name="AdminOnly" xml:space="preserve">
    <value>Champ administrateur seulement</value>
  </data>
  <data name="AccountDeactivated" xml:space="preserve">
    <value>Votre compte a Ã©tÃ© dÃ©sactivÃ©. Veuillez contacter l'administration.</value>
  </data>
  <data name="DeactivatedAccountLogin" xml:space="preserve">
    <value>Ce compte est dÃ©sactivÃ© et ne peut pas se connecter.</value>
  </data>

  <!-- Export and Filter UI -->
  <data name="Export" xml:space="preserve">
    <value>Exporter</value>
  </data>
  <data name="ExportMembers" xml:space="preserve">
    <value>Exporter les membres</value>
  </data>
  <data name="ExportMembersDescription" xml:space="preserve">
    <value>Choisissez le format d'exportation pour les membres actuellement affichÃ©s.</value>
  </data>
  <data name="SelectExportFormat" xml:space="preserve">
    <value>SÃ©lectionnez le format d'exportation</value>
  </data>
  <data name="ExportWillIncludeAllResults" xml:space="preserve">
    <value>L'exportation inclura tous les rÃ©sultats correspondant aux critÃ¨res de recherche et de filtrage actuels :</value>
  </data>
  <data name="SelectFilter" xml:space="preserve">
    <value>SÃ©lectionner un filtre</value>
  </data>
  <data name="SelectOption" xml:space="preserve">
    <value>SÃ©lectionner une option</value>
  </data>
  <data name="TypeToSearchCities" xml:space="preserve">
    <value>Tapez pour rechercher des villes</value>
  </data>
  <data name="Apply" xml:space="preserve">
    <value>Appliquer</value>
  </data>
  <data name="ClearFilters" xml:space="preserve">
    <value>Effacer les filtres</value>
  </data>
  <data name="ActiveFilters" xml:space="preserve">
    <value>Filtres actifs</value>
  </data>
  <data name="RemoveFilter" xml:space="preserve">
    <value>Supprimer</value>
  </data>
  <data name="Loading" xml:space="preserve">
    <value>Chargement...</value>
  </data>
  <data name="NoCitiesFound" xml:space="preserve">
    <value>Aucune ville trouvÃ©e</value>
  </data>
  <data name="InvalidExportParameters" xml:space="preserve">
    <value>ParamÃ¨tres d'exportation invalides</value>
  </data>
  <data name="ErrorExportingMembers" xml:space="preserve">
    <value>Erreur lors de l'exportation des membres</value>
  </data>

  <!-- Empty State Messages -->
  <data name="EmptyState_NoMembers" xml:space="preserve">
    <value>Aucun membre trouvÃ©</value>
  </data>
  <data name="EmptyState_NoEvents" xml:space="preserve">
    <value>Aucun Ã©vÃ©nement trouvÃ©</value>
  </data>
  <data name="EmptyState_NoAdminData" xml:space="preserve">
    <value>Aucune donnÃ©e administrative trouvÃ©e</value>
  </data>
  <data name="EmptyState_NoLogs" xml:space="preserve">
    <value>Aucun journal trouvÃ©</value>
  </data>
  <data name="EmptyState_NoExports" xml:space="preserve">
    <value>Aucune exportation trouvÃ©e</value>
  </data>
  <data name="EmptyState_NoSavedSearches" xml:space="preserve">
    <value>Aucune recherche sauvegardÃ©e trouvÃ©e</value>
  </data>
  <data name="EmptyState_NoData" xml:space="preserve">
    <value>Aucune donnÃ©e trouvÃ©e</value>
  </data>

  <!-- Empty State Titles -->
  <data name="EmptyState_NoMembersTitle" xml:space="preserve">
    <value>Aucun membre</value>
  </data>
  <data name="EmptyState_NoEventsTitle" xml:space="preserve">
    <value>Aucun Ã©vÃ©nement</value>
  </data>
  <data name="EmptyState_NoAdminDataTitle" xml:space="preserve">
    <value>Aucune donnÃ©e administrative</value>
  </data>
  <data name="EmptyState_NoLogsTitle" xml:space="preserve">
    <value>Aucun journal</value>
  </data>
  <data name="EmptyState_NoExportsTitle" xml:space="preserve">
    <value>Aucune exportation</value>
  </data>
  <data name="EmptyState_NoSavedSearchesTitle" xml:space="preserve">
    <value>Aucune recherche sauvegardÃ©e</value>
  </data>
  <data name="EmptyState_NoDataTitle" xml:space="preserve">
    <value>Aucune donnÃ©e</value>
  </data>

  <!-- Search Empty State Messages -->
  <data name="EmptyState_SearchNoResults" xml:space="preserve">
    <value>Aucun rÃ©sultat trouvÃ©</value>
  </data>
  <data name="EmptyState_SearchNoMembers" xml:space="preserve">
    <value>Aucun membre trouvÃ© pour "{0}"</value>
  </data>
  <data name="EmptyState_SearchNoEvents" xml:space="preserve">
    <value>Aucun Ã©vÃ©nement trouvÃ© pour "{0}"</value>
  </data>
  <data name="EmptyState_SearchNoAdminData" xml:space="preserve">
    <value>Aucune donnÃ©e administrative trouvÃ©e pour "{0}"</value>
  </data>
  <data name="EmptyState_SearchNoLogs" xml:space="preserve">
    <value>Aucun journal trouvÃ© pour "{0}"</value>
  </data>
  <data name="EmptyState_SearchNoExports" xml:space="preserve">
    <value>Aucune exportation trouvÃ©e pour "{0}"</value>
  </data>
  <data name="EmptyState_SearchNoSavedSearches" xml:space="preserve">
    <value>Aucune recherche sauvegardÃ©e trouvÃ©e pour "{0}"</value>
  </data>
  <data name="EmptyState_SearchNoData" xml:space="preserve">
    <value>Aucune donnÃ©e trouvÃ©e pour "{0}"</value>
  </data>

  <!-- Search Suggestions -->
  <data name="EmptyState_SearchSuggestions" xml:space="preserve">
    <value>Essayez : {0}</value>
  </data>

  <!-- Action Messages -->
  <data name="EmptyState_AddFirstMember" xml:space="preserve">
    <value>Ajouter le premier membre</value>
  </data>
  <data name="EmptyState_CreateFirstEvent" xml:space="preserve">
    <value>CrÃ©er le premier Ã©vÃ©nement</value>
  </data>
  <data name="EmptyState_ConfigureSystem" xml:space="preserve">
    <value>Configurer le systÃ¨me</value>
  </data>
  <data name="EmptyState_ViewAllLogs" xml:space="preserve">
    <value>Voir tous les journaux</value>
  </data>
  <data name="EmptyState_CreateExport" xml:space="preserve">
    <value>CrÃ©er une exportation</value>
  </data>
  <data name="EmptyState_CreateSearch" xml:space="preserve">
    <value>CrÃ©er une recherche</value>
  </data>
  <data name="EmptyState_GetStarted" xml:space="preserve">
    <value>Commencer</value>
  </data>
  <data name="EmptyState_SearchTryDifferent" xml:space="preserve">
    <value>Essayez une recherche diffÃ©rente ou modifiez vos filtres</value>
  </data>
  <data name="EmptyState_ErrorTitle" xml:space="preserve">
    <value>Une erreur est survenue</value>
  </data>
  <data name="EmptyState_RetryAction" xml:space="preserve">
    <value>RÃ©essayer</value>
  </data>
  
  <!-- Context-specific suggestions -->
  <data name="EmptyState_members_filterSuggestion" xml:space="preserve">
    <value>Essayez de supprimer certains filtres pour voir plus de rÃ©sultats</value>
  </data>
  <data name="EmptyState_members_newSuggestion" xml:space="preserve">
    <value>Vous pouvez ajouter un nouveau membre en utilisant le bouton ci-dessous</value>
  </data>
  <data name="EmptyState_events_filterSuggestion" xml:space="preserve">
    <value>Essayez de modifier la plage de dates ou de supprimer des filtres</value>
  </data>
  <data name="EmptyState_events_newSuggestion" xml:space="preserve">
    <value>Vous pouvez crÃ©er un nouvel Ã©vÃ©nement en utilisant le bouton ci-dessous</value>
  </data>
  
  <!-- Error-specific messages -->
  <data name="EmptyState_ErrorDatabaseMembers" xml:space="preserve">
    <value>Impossible de charger les membres en raison d'une erreur de base de donnÃ©es</value>
  </data>
  <data name="EmptyState_ErrorNetworkMembers" xml:space="preserve">
    <value>Impossible de charger les membres en raison d'un problÃ¨me de rÃ©seau</value>
  </data>
  <data name="EmptyState_ErrorPermissionMembers" xml:space="preserve">
    <value>Vous n'avez pas les permissions nÃ©cessaires pour voir ces membres</value>
  </data>
  <data name="EmptyState_ErrorGeneralMembers" xml:space="preserve">
    <value>Une erreur est survenue lors du chargement des membres</value>
  </data>
  <data name="EmptyState_ErrorDatabaseEvents" xml:space="preserve">
    <value>Impossible de charger les Ã©vÃ©nements en raison d'une erreur de base de donnÃ©es</value>
  </data>
  <data name="EmptyState_ErrorNetworkEvents" xml:space="preserve">
    <value>Impossible de charger les Ã©vÃ©nements en raison d'un problÃ¨me de rÃ©seau</value>
  </data>
  <data name="EmptyState_ErrorPermissionEvents" xml:space="preserve">
    <value>Vous n'avez pas les permissions nÃ©cessaires pour voir ces Ã©vÃ©nements</value>
  </data>
  <data name="EmptyState_ErrorGeneralEvents" xml:space="preserve">
    <value>Une erreur est survenue lors du chargement des Ã©vÃ©nements</value>
  </data>
  <data name="EmptyState_ErrorDatabaseData" xml:space="preserve">
    <value>Impossible de charger les donnÃ©es en raison d'une erreur de base de donnÃ©es</value>
  </data>
  <data name="EmptyState_ErrorNetworkData" xml:space="preserve">
    <value>Impossible de charger les donnÃ©es en raison d'un problÃ¨me de rÃ©seau</value>
  </data>
  <data name="EmptyState_ErrorPermissionData" xml:space="preserve">
    <value>Vous n'avez pas les permissions nÃ©cessaires pour voir ces donnÃ©es</value>
  </data>
  <data name="EmptyState_ErrorGeneralData" xml:space="preserve">
    <value>Une erreur est survenue lors du chargement des donnÃ©es</value>
  </data>
  <!-- Environment Indicator Messages -->
  <data name="Environment_Development" xml:space="preserve">
    <value>Environnement de DÃ©veloppement</value>
  </data>
  <data name="Environment_Test" xml:space="preserve">
    <value>Environnement de Test</value>
  </data>
  <data name="Environment_SqlServer" xml:space="preserve">
    <value>Base de donnÃ©es SQL Server</value>
  </data>
  <data name="Environment_DevToolsAvailable" xml:space="preserve">
    <value>Outils de dÃ©veloppement disponibles</value>
  </data>
  <!-- Development Tools Messages -->
  <data name="DevTools_Title" xml:space="preserve">
    <value>Outils de DÃ©veloppement</value>
  </data>
  <data name="DevTools_Registration" xml:space="preserve">
    <value>Inscription</value>
  </data>
  <data name="DevTools_FillJunior" xml:space="preserve">
    <value>Remplir Junior</value>
  </data>
  <data name="DevTools_FillAdult" xml:space="preserve">
    <value>Remplir Adulte</value>
  </data>
  <data name="DevTools_FillCoach" xml:space="preserve">
    <value>Remplir EntraÃ®neur</value>
  </data>
  <data name="DevTools_Admin" xml:space="preserve">
    <value>Administration</value>
  </data>
  <data name="DevTools_GenerateTestData" xml:space="preserve">
    <value>GÃ©nÃ©rer des donnÃ©es de test</value>
  </data>
  <data name="DevTools_ClearTestData" xml:space="preserve">
    <value>Effacer les donnÃ©es de test</value>
  </data>
  <data name="DevTools_Debug" xml:space="preserve">
    <value>DÃ©bogage</value>
  </data>
  <data name="DevTools_ShowDebugInfo" xml:space="preserve">
    <value>Afficher les infos de dÃ©bogage</value>
  </data>
  <data name="DevTools_ShowEnvInfo" xml:space="preserve">
    <value>Infos environnement</value>
  </data>
  <data name="DevTools_ShowUserInfo" xml:space="preserve">
    <value>Infos utilisateur</value>
  </data>
  <data name="DevTools_Performance" xml:space="preserve">
    <value>Performance</value>
  </data>
  <data name="DevTools_MeasurePageLoad" xml:space="preserve">
    <value>Mesurer le chargement</value>
  </data>
  <data name="DevTools_ClearCache" xml:space="preserve">
    <value>Vider le cache</value>
  </data>
  <data name="DevTools_Api" xml:space="preserve">
    <value>API</value>
  </data>
  <data name="DevTools_TestApi" xml:space="preserve">
    <value>Tester l'API</value>
  </data>
  <data name="DevTools_ApiDocs" xml:space="preserve">
    <value>Documentation API</value>
  </data>
  <data name="DevTools_Environment" xml:space="preserve">
    <value>Environnement</value>
  </data>
  <data name="DevTools_Theme" xml:space="preserve">
    <value>ThÃ¨me</value>
  </data>
  <data name="DevTools_Auth" xml:space="preserve">
    <value>Authentification</value>
  </data>
  <data name="DevTools_Enabled" xml:space="preserve">
    <value>ActivÃ©</value>
  </data>
  <data name="DevTools_Disabled" xml:space="preserve">
    <value>DÃ©sactivÃ©</value>
  </data>
  <data name="DevTools_DebugInformation" xml:space="preserve">
    <value>Informations de DÃ©bogage</value>
  </data>
  <data name="DevTools_FormFilled" xml:space="preserve">
    <value>Formulaire rempli avec succÃ¨s</value>
  </data>
  <data name="DevTools_ConfirmGenerateData" xml:space="preserve">
    <value>ÃŠtes-vous sÃ»r de vouloir gÃ©nÃ©rer des donnÃ©es de test ?</value>
  </data>
  <data name="DevTools_TestDataGenerated" xml:space="preserve">
    <value>DonnÃ©es de test gÃ©nÃ©rÃ©es</value>
  </data>
  <data name="DevTools_ConfirmClearData" xml:space="preserve">
    <value>ÃŠtes-vous sÃ»r de vouloir effacer les donnÃ©es de test ?</value>
  </data>
  <data name="DevTools_TestDataCleared" xml:space="preserve">
    <value>DonnÃ©es de test effacÃ©es</value>
  </data>
  <data name="DevTools_PageInfo" xml:space="preserve">
    <value>Informations de la page</value>
  </data>
  <data name="DevTools_UserAgent" xml:space="preserve">
    <value>Agent utilisateur</value>
  </data>
  <data name="DevTools_Viewport" xml:space="preserve">
    <value>Viewport</value>
  </data>
  <data name="DevTools_LoadTime" xml:space="preserve">
    <value>Temps de chargement</value>
  </data>
  <data name="DevTools_Browser" xml:space="preserve">
    <value>Navigateur</value>
  </data>
  <data name="DevTools_Language" xml:space="preserve">
    <value>Langue</value>
  </data>
  <data name="DevTools_CookiesEnabled" xml:space="preserve">
    <value>Cookies activÃ©s</value>
  </data>
  <data name="DevTools_OnLine" xml:space="preserve">
    <value>En ligne</value>
  </data>
  <data name="DevTools_CheckConsole" xml:space="preserve">
    <value>VÃ©rifiez la console pour les dÃ©tails</value>
  </data>
  <data name="DevTools_PageLoadTime" xml:space="preserve">
    <value>Temps de chargement de la page</value>
  </data>
  <data name="DevTools_CacheCleared" xml:space="preserve">
    <value>Cache vidÃ© avec succÃ¨s</value>
  </data>
  <data name="DevTools_CacheNotSupported" xml:space="preserve">
    <value>Cache non pris en charge par ce navigateur</value>
  </data>
  <data name="DevTools_TestingApi" xml:space="preserve">
    <value>Test de l'API en cours...</value>
  </data>
  <!-- Layout Messages -->
  <data name="PoweredBy" xml:space="preserve">
    <value>PropulsÃ© par</value>
  </data>
  <!-- Validation Messages for Custom ValidationResult -->
  <data name="ValidationParentRequired" xml:space="preserve">
    <value>Au moins un parent est requis pour l'inscription Junior</value>
  </data>
  <data name="ValidationEmergencyContactRequired" xml:space="preserve">
    <value>Un contact d'urgence est requis</value>
  </data>
  <!-- Client-side validation messages -->
  <data name="ClientValidationEmailInvalid" xml:space="preserve">
    <value>Veuillez entrer une adresse e-mail valide (ex: <EMAIL>)</value>
  </data>
  <data name="ClientValidationPhoneInvalid" xml:space="preserve">
    <value>Veuillez entrer un numÃ©ro de tÃ©lÃ©phone Ã  10 chiffres</value>
  </data>
  <data name="ClientValidationPostalCodeInvalid" xml:space="preserve">
    <value>Veuillez entrer un code postal canadien valide (ex: A1A 1A1)</value>
  </data>
  <data name="ClientValidationDateInvalid" xml:space="preserve">
    <value>Veuillez entrer une date valide au format AAAA-MM-JJ</value>
  </data>
  <data name="ClientValidationDateFuture" xml:space="preserve">
    <value>La date de naissance ne peut pas Ãªtre dans le futur</value>
  </data>
  <data name="ClientValidationDateGeneral" xml:space="preserve">
    <value>Veuillez entrer une date de naissance valide</value>
  </data>
  <data name="ClientValidationNameTooShort" xml:space="preserve">
    <value>Le nom doit contenir au moins 2 caractÃ¨res</value>
  </data>
  <data name="ClientValidationAddressTooShort" xml:space="preserve">
    <value>L'adresse doit contenir au moins 5 caractÃ¨res</value>
  </data>
  <data name="ClientValidationCityTooShort" xml:space="preserve">
    <value>La ville doit contenir au moins 2 caractÃ¨res</value>
  </data>
  <data name="ClientValidationRequired" xml:space="preserve">
    <value>Ce champ est requis</value>
  </data>
  <!-- jQuery validation default messages -->
  <data name="JQueryValidationRequired" xml:space="preserve">
    <value>Ce champ est requis.</value>
  </data>
  <data name="JQueryValidationEmail" xml:space="preserve">
    <value>Veuillez entrer une adresse e-mail valide.</value>
  </data>
  <data name="JQueryValidationNumber" xml:space="preserve">
    <value>Veuillez entrer un nombre valide.</value>
  </data>
  <data name="JQueryValidationDigits" xml:space="preserve">
    <value>Veuillez entrer seulement des chiffres.</value>
  </data>
  <data name="JQueryValidationMinLength" xml:space="preserve">
    <value>Veuillez entrer au moins {0} caractÃ¨res.</value>
  </data>
  <data name="JQueryValidationMaxLength" xml:space="preserve">
    <value>Veuillez entrer au plus {0} caractÃ¨res.</value>
  </data>
  <data name="JQueryValidationRangeLength" xml:space="preserve">
    <value>Veuillez entrer une valeur entre {0} et {1} caractÃ¨res.</value>
  </data>
  <data name="JQueryValidationMin" xml:space="preserve">
    <value>Veuillez entrer une valeur supÃ©rieure ou Ã©gale Ã  {0}.</value>
  </data>
  <data name="JQueryValidationMax" xml:space="preserve">
    <value>Veuillez entrer une valeur infÃ©rieure ou Ã©gale Ã  {0}.</value>
  </data>
  <data name="JQueryValidationRange" xml:space="preserve">
    <value>Veuillez entrer une valeur entre {0} et {1}.</value>
  </data>
  <!-- API Error Messages -->
  <data name="ApiAccessDenied" xml:space="preserve">
    <value>AccÃ¨s refusÃ©</value>
  </data>
  <data name="ApiFilterTypeRequired" xml:space="preserve">
    <value>Le type de filtre est requis</value>
  </data>
  <data name="ApiErrorRetrievingFilterOptions" xml:space="preserve">
    <value>Erreur lors de la rÃ©cupÃ©ration des options de filtre</value>
  </data>
  <data name="ApiInvalidRequestData" xml:space="preserve">
    <value>DonnÃ©es de requÃªte invalides</value>
  </data>
  <data name="ApiUserNotFound" xml:space="preserve">
    <value>Utilisateur non trouvÃ©</value>
  </data>
  <data name="ApiCodeSentEmail" xml:space="preserve">
    <value>Code envoyÃ© par email</value>
  </data>
  <data name="ApiEmailSendingError" xml:space="preserve">
    <value>Erreur d'envoi d'email</value>
  </data>
  <data name="ApiValidationBad" xml:space="preserve">
    <value>Validation Ã©chouÃ©e</value>
  </data>
  <data name="ApiValidationGood" xml:space="preserve">
    <value>Validation rÃ©ussie</value>
  </data>
  <data name="ApiUnauthorizedAccess" xml:space="preserve">
    <value>AccÃ¨s non autorisÃ©</value>
  </data>
  <data name="ApiSessionExpired" xml:space="preserve">
    <value>Session expirÃ©e</value>
  </data>
  <data name="ApiFailedToLoadEvents" xml:space="preserve">
    <value>Ã‰chec du chargement des Ã©vÃ©nements</value>
  </data>
  <data name="ApiValidationFailed" xml:space="preserve">
    <value>Ã‰chec de la validation</value>
  </data>
  <data name="ApiEventDataRequired" xml:space="preserve">
    <value>Les donnÃ©es d'Ã©vÃ©nement sont requises</value>
  </data>
  <data name="ApiEventTitleRequired" xml:space="preserve">
    <value>Le titre de l'Ã©vÃ©nement est requis</value>
  </data>
  <data name="ApiEventCategoryRequired" xml:space="preserve">
    <value>La catÃ©gorie d'Ã©vÃ©nement est requise</value>
  </data>
  <!-- JavaScript Error Messages -->
  <data name="JsVerificationCodeMessage" xml:space="preserve">
    <value>Code de vÃ©rification: {0}\n\nEntrez ce code dans le champ de vÃ©rification ci-dessous.</value>
  </data>
  <data name="JsErrorSendingCode" xml:space="preserve">
    <value>Erreur lors de l'envoi du code de vÃ©rification. Consultez la console pour plus de dÃ©tails.</value>
  </data>
  <data name="JsFormResetConfirm" xml:space="preserve">
    <value>Voulez-vous effacer tous les champs du formulaire?</value>
  </data>
  <!-- System Error Page Messages -->
  <data name="SystemErrorTitle" xml:space="preserve">
    <value>Erreur</value>
  </data>
  <data name="SystemErrorMessage" xml:space="preserve">
    <value>Une erreur s'est produite lors du traitement de votre demande.</value>
  </data>
  <data name="SystemErrorRequestId" xml:space="preserve">
    <value>ID de demande:</value>
  </data>
  <data name="SystemErrorDevelopmentMode" xml:space="preserve">
    <value>Mode DÃ©veloppement</value>
  </data>
  <data name="SystemErrorDevelopmentInfo" xml:space="preserve">
    <value>Le passage Ã  l'environnement de <strong>DÃ©veloppement</strong> affiche des informations dÃ©taillÃ©es sur l'erreur qui s'est produite.</value>
  </data>
  <data name="SystemErrorDevelopmentWarning" xml:space="preserve">
    <value><strong>L'environnement de dÃ©veloppement ne doit pas Ãªtre activÃ© pour les applications dÃ©ployÃ©es.</strong> Il peut entraÃ®ner l'affichage d'informations sensibles d'exceptions aux utilisateurs finaux. Pour le dÃ©bogage local, activez l'environnement de <strong>DÃ©veloppement</strong> en dÃ©finissant la variable d'environnement <strong>ASPNETCORE_ENVIRONMENT</strong> sur <strong>Development</strong> et en redÃ©marrant l'application.</value>
  </data>
  <data name="ApiValidationTypeRequired" xml:space="preserve">
    <value>Le type est requis.</value>
  </data>
  <data name="NoUpcomingEventsMessage" xml:space="preserve">
    <value>Il n'y a aucun Ã©vÃ©nement Ã  venir pour le moment. VÃ©rifiez le calendrier principal pour voir tous les Ã©vÃ©nements.</value>
  </data>
  <data name="NoRecentEventsMessage" xml:space="preserve">
    <value>Aucun Ã©vÃ©nement rÃ©cent n'est disponible pour le moment.</value>
  </data>
  <data name="ExploreAllEvents" xml:space="preserve">
    <value>Explorer tous les Ã©vÃ©nements</value>
  </data>
  <data name="SetupFirstAdmin" xml:space="preserve">
    <value>Configurer le premier administrateur</value>
  </data>
  <data name="Error_ValidationFailed" xml:space="preserve">
    <value>Erreur de validation des donnÃ©es</value>
  </data>
  <data name="Error_Unauthorized" xml:space="preserve">
    <value>AccÃ¨s non autorisÃ©</value>
  </data>
  <data name="Error_ResourceNotFound" xml:space="preserve">
    <value>Ressource introuvable</value>
  </data>
  <data name="Error_RequestTimeout" xml:space="preserve">
    <value>DÃ©lai d'attente de la requÃªte dÃ©passÃ©</value>
  </data>
  <data name="Error_InvalidOperation" xml:space="preserve">
    <value>OpÃ©ration invalide</value>
  </data>
  <data name="Error_OperationNotSupported" xml:space="preserve">
    <value>OpÃ©ration non supportÃ©e</value>
  </data>
  <data name="Error_DatabaseError" xml:space="preserve">
    <value>Erreur de base de donnÃ©es</value>
  </data>
  <data name="Error_UnexpectedError" xml:space="preserve">
    <value>Une erreur inattendue s'est produite</value>
  </data>
  <data name="PageNotFoundTitle" xml:space="preserve">
    <value>Page introuvable</value>
  </data>
  <data name="PageNotFoundHeading" xml:space="preserve">
    <value>Oups! Page non trouvÃ©e</value>
  </data>
  <data name="PageNotFoundMessage" xml:space="preserve">
    <value>La page que vous recherchez n'existe pas ou a Ã©tÃ© dÃ©placÃ©e.</value>
  </data>
  <data name="SystemErrorHeading" xml:space="preserve">
    <value>Erreur systÃ¨me</value>
  </data>
  <data name="SuggestedLinks" xml:space="preserve">
    <value>Liens suggÃ©rÃ©s</value>
  </data>
  <data name="HomePage" xml:space="preserve">
    <value>Page d'accueil</value>
  </data>
  <data name="GoBack" xml:space="preserve">
    <value>Retour</value>
  </data>
  <data name="MemberRegistration" xml:space="preserve">
    <value>Inscription des membres</value>
  </data>
  <data name="MemberLogin" xml:space="preserve">
    <value>Connexion des membres</value>
  </data>
  
  <!-- Event Subscription Page -->
  <data name="SubscribeToEvents" xml:space="preserve">
    <value>S'inscrire aux événements</value>
  </data>
  <data name="BrowseAndSubscribeToUpcomingEvents" xml:space="preserve">
    <value>Parcourez et inscrivez-vous aux Ã©vÃ©nements Ã  venir</value>
  </data>
  <data name="Subscribe" xml:space="preserve">
    <value>S'inscrire</value>
  </data>
  <data name="Registered" xml:space="preserve">
    <value>Inscrit</value>
  </data>
  <data name="Full" xml:space="preserve">
    <value>Complet</value>
  </data>
  <data name="SpotsAvailable" xml:space="preserve">
    <value>places disponibles</value>
  </data>
  <data name="RegistrationDeadline" xml:space="preserve">
    <value>Date limite d'inscription</value>
  </data>
  <data name="NoRegistrationRequired" xml:space="preserve">
    <value>Aucune inscription requise</value>
  </data>
  <data name="RegistrationClosed" xml:space="preserve">
    <value>Inscription fermÃ©e</value>
  </data>
  <data name="CheckBackLaterForNewEvents" xml:space="preserve">
    <value>Revenez plus tard pour voir les nouveaux Ã©vÃ©nements</value>
  </data>
  <data name="EventSubscriptionSuccess" xml:space="preserve">
    <value>Vous avez Ã©tÃ© inscrit avec succÃ¨s Ã  l'Ã©vÃ©nement!</value>
  </data>
  <data name="EventSubscriptionError" xml:space="preserve">
    <value>Une erreur s'est produite lors de l'inscription Ã  l'Ã©vÃ©nement.</value>
  </data>
  
  <!-- Enhanced Form Validation Messages -->
  <data name="SelectGender" xml:space="preserve">
    <value>SÃ©lectionner le genre</value>
  </data>
  <data name="SelectPhoneType" xml:space="preserve">
    <value>SÃ©lectionner le type de tÃ©lÃ©phone</value>
  </data>
  <data name="SelectRegistrationType" xml:space="preserve">
    <value>SÃ©lectionner le type d'inscription</value>
  </data>
  <data name="ParentInfoRequired" xml:space="preserve">
    <value>Les informations du parent/tuteur sont requises pour les membres de moins de 18 ans.</value>
  </data>
  <data name="EmergencyContactOptional" xml:space="preserve">
    <value>Les informations de contact d'urgence sont optionnelles mais recommandÃ©es.</value>
  </data>
  <data name="MemberRegistrationSuccess" xml:space="preserve">
    <value>Inscription rÃ©ussie pour {0} {1}!</value>
  </data>
  <data name="RegistrationSuccess" xml:space="preserve">
    <value>Inscription RÃ©ussie</value>
  </data>
  <data name="RegistrationSuccessTitle" xml:space="preserve">
    <value>Bienvenue chez Parahockey!</value>
  </data>
  <data name="RegistrationSuccessMessage" xml:space="preserve">
    <value>Merci {0} {1}, votre inscription a Ã©tÃ© complÃ©tÃ©e avec succÃ¨s.</value>
  </data>
  <data name="NextStepEmail" xml:space="preserve">
    <value>VÃ©rifiez votre courriel pour un message de bienvenue et des informations importantes</value>
  </data>
  <data name="NextStepEvents" xml:space="preserve">
    <value>Parcourez les Ã©vÃ©nements Ã  venir et inscrivez-vous aux activitÃ©s</value>
  </data>
  <data name="NextStepProfile" xml:space="preserve">
    <value>ComplÃ©tez votre profil avec des informations supplÃ©mentaires</value>
  </data>
  <data name="ViewEvents" xml:space="preserve">
    <value>Voir les Ã©vÃ©nements</value>
  </data>
  <data name="AutoRedirectConfirm" xml:space="preserve">
    <value>Souhaitez-vous retourner Ã  la page d'accueil?</value>
  </data>
  <data name="RegistrationFormSubtitle" xml:space="preserve">
    <value>Rejoignez notre communautÃ© de passionnÃ©s de hockey adaptÃ©</value>
  </data>
  <data name="AlreadyRegisteredForEvent" xml:space="preserve">
    <value>Vous Ãªtes dÃ©jÃ  inscrit Ã  cet Ã©vÃ©nement.</value>
  </data>
  <data name="RegisterForUpcomingEvents" xml:space="preserve">
    <value>Inscrivez-vous aux Ã©vÃ©nements Ã  venir</value>
  </data>
  <data name="ErrorLoadingEvents" xml:space="preserve">
    <value>Erreur lors du chargement des Ã©vÃ©nements</value>
  </data>
  <data name="MemberAccountRequired" xml:space="preserve">
    <value>Un compte membre est requis pour s'inscrire aux Ã©vÃ©nements</value>
  </data>
  
  <!-- Event Details Modal -->
  <data name="Date" xml:space="preserve">
    <value>Date</value>
  </data>
  <data name="Time" xml:space="preserve">
    <value>Heure</value>
  </data>
  <data name="RegisterForEvent" xml:space="preserve">
    <value>S'inscrire Ã  l'Ã©vÃ©nement</value>
  </data>
  <data name="AlreadyFull" xml:space="preserve">
    <value>Cet Ã©vÃ©nement est dÃ©jÃ  complet</value>
  </data>
  <data name="AllDay" xml:space="preserve">
    <value>Toute la journÃ©e</value>
  </data>
  <data name="LocationTBA" xml:space="preserve">
    <value>Lieu Ã  confirmer</value>
  </data>
  <data name="ErrorLoadingEventDetails" xml:space="preserve">
    <value>Erreur lors du chargement des dÃ©tails de l'Ã©vÃ©nement</value>
  </data>
  <data name="NoChangesDetected" xml:space="preserve">
    <value>Aucune modification dÃ©tectÃ©e.</value>
  </data>
  <data name="Close" xml:space="preserve">
    <value>Fermer</value>
  </data>
  <data name="Location" xml:space="preserve">
    <value>Lieu</value>
  </data>
  <data name="YouAreRegistered" xml:space="preserve">
    <value>Vous Ãªtes inscrit Ã  cet Ã©vÃ©nement</value>
  </data>
  <data name="Unregister" xml:space="preserve">
    <value>Se dÃ©sinscrire</value>
  </data>
  <data name="EventUnregistrationSuccess" xml:space="preserve">
    <value>Vous avez Ã©tÃ© dÃ©sinscrit de l'Ã©vÃ©nement avec succÃ¨s</value>
  </data>
  <data name="EventUnregistrationError" xml:space="preserve">
    <value>Une erreur s'est produite lors de la dÃ©sinscription de l'Ã©vÃ©nement</value>
  </data>
  <data name="MemberBenefitsTitle" xml:space="preserve">
    <value>Avantages de l'adhÃ©sion :</value>
  </data>
  <data name="MemberBenefitEventRegistration" xml:space="preserve">
    <value>Inscription aux Ã©vÃ©nements et compÃ©titions</value>
  </data>
  <data name="MemberBenefitPriorityAccess" xml:space="preserve">
    <value>AccÃ¨s prioritaire aux Ã©vÃ©nements populaires</value>
  </data>
  <data name="MemberBenefitUpdates" xml:space="preserve">
    <value>Mises Ã  jour et nouvelles du club</value>
  </data>
  <data name="MemberBenefitCommunity" xml:space="preserve">
    <value>AccÃ¨s Ã  la communautÃ© parahockey</value>
  </data>
  <data name="MembershipIsFree" xml:space="preserve">
    <value>L'adhÃ©sion est gratuite et prend seulement quelques minutes!</value>
  </data>
  <data name="EventRegistrationAfterSignup" xml:space="preserve">
    <value>AprÃ¨s votre inscription, vous serez automatiquement redirigÃ© pour vous inscrire Ã  l'Ã©vÃ©nement sÃ©lectionnÃ©.</value>
  </data>
  <data name="Available" xml:space="preserve">
    <value>Disponible</value>
  </data>
  <data name="EventFull" xml:space="preserve">
    <value>Complet</value>
  </data>
  <data name="Registering" xml:space="preserve">
    <value>Inscription</value>
  </data>
  <data name="Unregistering" xml:space="preserve">
    <value>DÃ©sinscription</value>
  </data>
  <data name="EventRegistrationSuccess" xml:space="preserve">
    <value>Inscription rÃ©ussie!</value>
  </data>
  <data name="RegistrationError" xml:space="preserve">
    <value>Erreur d'inscription</value>
  </data>
  <data name="UnregisterConfirm" xml:space="preserve">
    <value>ÃŠtes-vous sÃ»r de vouloir vous dÃ©sinscrire de cet Ã©vÃ©nement?</value>
  </data>
  <data name="ManageRegistration" xml:space="preserve">
    <value>GÃ©rer l'inscription</value>
  </data>
  <data name="JuniorMembershipNotAvailable18Plus" xml:space="preserve">
    <value>L'adhÃ©sion Junior n'est pas disponible pour les personnes de 18 ans et plus</value>
  </data>

  <!-- Page Audit System -->
  <data name="InventoryGeneratedSuccessfully" xml:space="preserve">
    <value>Inventaire des pages gÃ©nÃ©rÃ© avec succÃ¨s</value>
  </data>
  <data name="ErrorGeneratingInventory" xml:space="preserve">
    <value>Erreur lors de la gÃ©nÃ©ration de l'inventaire des pages</value>
  </data>
  <data name="PageNameRequired" xml:space="preserve">
    <value>Le nom de la page est requis</value>
  </data>
  <data name="PageAuditCompleted" xml:space="preserve">
    <value>Audit de la page terminÃ©</value>
  </data>
  <data name="ErrorAuditingPage" xml:space="preserve">
    <value>Erreur lors de l'audit de la page</value>
  </data>
  <data name="NoInventoryFound" xml:space="preserve">
    <value>Aucun inventaire trouvÃ©. Veuillez gÃ©nÃ©rer un inventaire d'abord.</value>
  </data>
  <data name="ErrorRetrievingInventory" xml:space="preserve">
    <value>Erreur lors de la rÃ©cupÃ©ration de l'inventaire</value>
  </data>
  <data name="ErrorRetrievingAuditHistory" xml:space="preserve">
    <value>Erreur lors de la rÃ©cupÃ©ration de l'historique d'audit</value>
  </data>
  <data name="InvalidFindingId" xml:space="preserve">
    <value>ID de constatation invalide</value>
  </data>
  <data name="ResolutionNotesRequired" xml:space="preserve">
    <value>Les notes de rÃ©solution sont requises</value>
  </data>
  <data name="FindingResolvedSuccessfully" xml:space="preserve">
    <value>Constatation rÃ©solue avec succÃ¨s</value>
  </data>
  <data name="ErrorResolvingFinding" xml:space="preserve">
    <value>Erreur lors de la rÃ©solution de la constatation</value>
  </data>
  <data name="PageAuditSystem" xml:space="preserve">
    <value>SystÃ¨me d'audit des pages</value>
  </data>
  <data name="GenerateInventory" xml:space="preserve">
    <value>GÃ©nÃ©rer l'inventaire</value>
  </data>
  <data name="AuditPage" xml:space="preserve">
    <value>Auditer la page</value>
  </data>
  <data name="PageInventory" xml:space="preserve">
    <value>Inventaire des pages</value>
  </data>
  <data name="AuditResults" xml:space="preserve">
    <value>RÃ©sultats d'audit</value>
  </data>
  <data name="SecurityScore" xml:space="preserve">
    <value>Score de sÃ©curitÃ©</value>
  </data>
  <data name="AccessibilityScore" xml:space="preserve">
    <value>Score d'accessibilitÃ©</value>
  </data>
  <data name="PerformanceScore" xml:space="preserve">
    <value>Score de performance</value>
  </data>
  <data name="LocalizationScore" xml:space="preserve">
    <value>Score de localisation</value>
  </data>
  <data name="OverallScore" xml:space="preserve">
    <value>Score global</value>
  </data>
  <data name="CriticalIssues" xml:space="preserve">
    <value>ProblÃ¨mes critiques</value>
  </data>
  <data name="HighIssues" xml:space="preserve">
    <value>ProblÃ¨mes Ã©levÃ©s</value>
  </data>
  <data name="MediumIssues" xml:space="preserve">
    <value>ProblÃ¨mes moyens</value>
  </data>
  <data name="LowIssues" xml:space="preserve">
    <value>ProblÃ¨mes faibles</value>
  </data>
  <data name="TotalIssues" xml:space="preserve">
    <value>Total des problÃ¨mes</value>
  </data>
  <data name="PageComplexity" xml:space="preserve">
    <value>ComplexitÃ© de la page</value>
  </data>
  <data name="Priority" xml:space="preserve">
    <value>PrioritÃ©</value>
  </data>
  <data name="IsModernized" xml:space="preserve">
    <value>ModernisÃ©e</value>
  </data>
  <data name="LastAuditScore" xml:space="preserve">
    <value>Dernier score d'audit</value>
  </data>
  <data name="ResolveFinding" xml:space="preserve">
    <value>RÃ©soudre la constatation</value>
  </data>
  <data name="ResolutionNotes" xml:space="preserve">
    <value>Notes de rÃ©solution</value>
  </data>
  
  <!-- Page Audit System -->
  <data name="PageReviewPlanGenerated" xml:space="preserve">
    <value>Plan de rÃ©vision des pages gÃ©nÃ©rÃ© avec succÃ¨s</value>
  </data>
  <data name="InitialAuditReportsGenerated" xml:space="preserve">
    <value>Rapports d'audit initiaux gÃ©nÃ©rÃ©s avec succÃ¨s</value>
  </data>
  <data name="ErrorGeneratingReviewPlan" xml:space="preserve">
    <value>Erreur lors de la gÃ©nÃ©ration du plan de rÃ©vision</value>
  </data>
  <data name="ErrorGeneratingAuditReports" xml:space="preserve">
    <value>Erreur lors de la gÃ©nÃ©ration des rapports d'audit</value>
  </data>

  <!-- Error Handling System -->

  <!-- Validation Error Messages -->
  <data name="ValidationError_Generic" xml:space="preserve">
    <value>Erreur de validation</value>
  </data>
  <data name="ValidationError_Required" xml:space="preserve">
    <value>Ce champ est requis</value>
  </data>
  <data name="ValidationError_EmailFormat" xml:space="preserve">
    <value>Format d'email invalide</value>
  </data>
  <data name="ValidationError_PhoneFormat" xml:space="preserve">
    <value>Format de tÃ©lÃ©phone invalide</value>
  </data>
  <data name="ValidationError_PostalCodeFormat" xml:space="preserve">
    <value>Format de code postal invalide</value>
  </data>
  <data name="ValidationError_StringLength" xml:space="preserve">
    <value>La longueur du texte est invalide</value>
  </data>
  <data name="ValidationError_Range" xml:space="preserve">
    <value>La valeur est hors de la plage autorisÃ©e</value>
  </data>
  <data name="ValidationError_DateFormat" xml:space="preserve">
    <value>Format de date invalide</value>
  </data>

  <!-- Validation Summary Messages -->
  <data name="ValidationSummary_SingleError" xml:space="preserve">
    <value>Veuillez corriger l'erreur ci-dessous</value>
  </data>
  <data name="ValidationSummary_MultipleErrors" xml:space="preserve">
    <value>Veuillez corriger les {0} erreurs ci-dessous</value>
  </data>

  <!-- Error Page Messages -->
  <data name="RequestedUrl" xml:space="preserve">
    <value>URL DemandÃ©e</value>
  </data>
  <data name="Calendar" xml:space="preserve">
    <value>Calendrier</value>
  </data>
  <data name="NeedHelp" xml:space="preserve">
    <value>Besoin d'aide?</value>
  </data>
  
  <!-- Home Page Accessibility Keys -->
  <data name="SkipToMainContent" xml:space="preserve">
    <value>Aller au contenu principal</value>
  </data>
  <data name="MainContentArea" xml:space="preserve">
    <value>Zone de contenu principal</value>
  </data>
  <data name="ParahockeyLogoAlt" xml:space="preserve">
    <value>Logo Parahockey QuÃ©bec - Association de hockey sur luge</value>
  </data>
  <data name="PrimaryActions" xml:space="preserve">
    <value>Actions principales</value>
  </data>
  <data name="RegisterButtonDesc" xml:space="preserve">
    <value>Commencer votre inscription comme membre</value>
  </data>
  <data name="ViewCalendarDesc" xml:space="preserve">
    <value>Consulter le calendrier public des Ã©vÃ©nements</value>
  </data>
  <data name="SubscribeEventsDesc" xml:space="preserve">
    <value>S'inscrire aux Ã©vÃ©nements et activitÃ©s</value>
  </data>
  <data name="CommunityStatsLabel" xml:space="preserve">
    <value>Statistiques de la communautÃ©</value>
  </data>
  <data name="PlayersCount" xml:space="preserve">
    <value>Plus de 150 joueurs actifs</value>
  </data>
  <data name="TeamsCount" xml:space="preserve">
    <value>12 Ã©quipes</value>
  </data>
  <data name="CoachesCount" xml:space="preserve">
    <value>25 entraÃ®neurs</value>
  </data>
  <data name="VolunteersCount" xml:space="preserve">
    <value>Plus de 50 bÃ©nÃ©voles</value>
  </data>
  <data name="RegistrationTypesList" xml:space="preserve">
    <value>Types d'inscription disponibles</value>
  </data>
  <data name="CtaButtonDesc" xml:space="preserve">
    <value>Commencer votre inscription maintenant</value>
  </data>
  
  <!-- Home Page Content Keys (if missing) -->
  
  <!-- Members Registration Page Accessibility Keys -->
  <data name="SkipToRegistrationForm" xml:space="preserve">
    <value>Aller au formulaire d'inscription</value>
  </data>
  <data name="RegistrationMainContent" xml:space="preserve">
    <value>Contenu principal de l'inscription</value>
  </data>
  <data name="LoginLinkDescription" xml:space="preserve">
    <value>Navigue vers la page de connexion pour les membres existants</value>
  </data>
  <data name="RegistrationFormDescription" xml:space="preserve">
    <value>Formulaire d'inscription complÃ¨te pour devenir membre de Parahockey QuÃ©bec</value>
  </data>
  <data name="FirstNameHelp" xml:space="preserve">
    <value>Votre prÃ©nom tel qu'il apparaÃ®t sur vos documents officiels</value>
  </data>
  <data name="LastNameHelp" xml:space="preserve">
    <value>Votre nom de famille tel qu'il apparaÃ®t sur vos documents officiels</value>
  </data>
  <data name="DateOfBirthHelp" xml:space="preserve">
    <value>Format requis: AAAA-MM-JJ (ex: 1990-12-25)</value>
  </data>
  <data name="GenderFieldsetLabel" xml:space="preserve">
    <value>SÃ©lection du genre</value>
  </data>
  <data name="AddressFieldsetLabel" xml:space="preserve">
    <value>Informations d'adresse</value>
  </data>
  <data name="ContactFieldsetLabel" xml:space="preserve">
    <value>Informations de contact</value>
  </data>
  <data name="RegistrationTypeFieldsetLabel" xml:space="preserve">
    <value>Type d'inscription</value>
  </data>
  <data name="FormSubmissionStatus" xml:space="preserve">
    <value>Traitement de votre inscription en cours...</value>
  </data>
  <data name="FormValidationError" xml:space="preserve">
    <value>Erreur de validation dÃ©tectÃ©e dans le formulaire</value>
  </data>

  <!-- Login Page Accessibility -->
  <data name="SkipToLoginForm" xml:space="preserve">
    <value>Aller au formulaire de connexion</value>
  </data>
  <data name="LoginMainContent" xml:space="preserve">
    <value>Contenu principal de la page de connexion</value>
  </data>
  <data name="LoginFormDescription" xml:space="preserve">
    <value>Formulaire de recherche et de connexion des membres</value>
  </data>
  <data name="EmailHelp" xml:space="preserve">
    <value>Entrez l'adresse courriel associÃ©e Ã  votre compte</value>
  </data>
  <data name="OpenDatePicker" xml:space="preserve">
    <value>Ouvrir le sÃ©lecteur de date</value>
  </data>
  <data name="SearchButtonHelp" xml:space="preserve">
    <value>Rechercher votre profil membre dans la base de donnÃ©es</value>
  </data>
  <data name="SearchResultsTableDescription" xml:space="preserve">
    <value>RÃ©sultats de recherche des membres correspondants</value>
  </data>
  <data name="SendCodeButtonHelp" xml:space="preserve">
    <value>Envoyer un code de vÃ©rification par courriel</value>
  </data>
  <data name="VerificationCodeEntry" xml:space="preserve">
    <value>Saisie du code de vÃ©rification</value>
  </data>
  <data name="VerificationCode" xml:space="preserve">
    <value>Code de vÃ©rification</value>
  </data>
  <data name="VerificationCodeHelp" xml:space="preserve">
    <value>Entrez le code Ã  6 chiffres reÃ§u par courriel</value>
  </data>
  <data name="VerifyButtonHelp" xml:space="preserve">
    <value>VÃ©rifier le code et se connecter</value>
  </data>
  <data name="CloseAlert" xml:space="preserve">
    <value>Fermer l'alerte</value>
  </data>
  <data name="InfoMessage" xml:space="preserve">
    <value>Message d'information</value>
  </data>
  <data name="SendingCode" xml:space="preserve">
    <value>Envoi en cours...</value>
  </data>
  <data name="VerifyingCode" xml:space="preserve">
    <value>VÃ©rification...</value>
  </data>
  <data name="VerificationSuccessful" xml:space="preserve">
    <value>VÃ©rification rÃ©ussie!</value>
  </data>
  <data name="VerificationFailed" xml:space="preserve">
    <value>Code de vÃ©rification invalide</value>
  </data>
  <data name="VerificationError" xml:space="preserve">
    <value>Erreur lors de la vÃ©rification</value>
  </data>
  <data name="LogoutSuccessful" xml:space="preserve">
    <value>DÃ©connexion rÃ©ussie. Ã€ bientÃ´t!</value>
  </data>
  <data name="LogoutCompleted" xml:space="preserve">
    <value>DÃ©connexion terminÃ©e</value>
  </data>

  <!-- Admin Dashboard Accessibility -->
  <data name="SkipToStatistics" xml:space="preserve">
    <value>Aller aux statistiques</value>
  </data>
  <data name="SkipToQuickActions" xml:space="preserve">
    <value>Aller aux actions rapides</value>
  </data>
  <data name="AdminDashboardMainContent" xml:space="preserve">
    <value>Contenu principal du tableau de bord administrateur</value>
  </data>
  <data name="AdminWelcomeMessage" xml:space="preserve">
    <value>Bienvenue, {0} | Environnement: {1}</value>
  </data>
  <data name="SystemStatistics" xml:space="preserve">
    <value>Statistiques du systÃ¨me</value>
  </data>
  <data name="CurrentEnvironment" xml:space="preserve">
    <value>Environnement actuel</value>
  </data>
  <data name="ViewSystemInfo" xml:space="preserve">
    <value>Infos systÃ¨me</value>
  </data>
  <data name="QuickActionsDescription" xml:space="preserve">
    <value>AccÃ¨s rapide aux fonctions principales du systÃ¨me d'administration</value>
  </data>
  <data name="AdminQuickActions" xml:space="preserve">
    <value>Actions rapides d'administration</value>
  </data>
  <data name="ViewAllMembersDescription" xml:space="preserve">
    <value>Consulter et gÃ©rer tous les membres inscrits</value>
  </data>
  <data name="AddNewMemberDescription" xml:space="preserve">
    <value>Inscrire un nouveau membre au systÃ¨me</value>
  </data>
  <data name="ManageCalendar" xml:space="preserve">
    <value>GÃ©rer le calendrier</value>
  </data>
  <data name="ManageCalendarDescription" xml:space="preserve">
    <value>CrÃ©er et modifier les Ã©vÃ©nements du calendrier</value>
  </data>
  <data name="ManageAdminsDescription" xml:space="preserve">
    <value>Administrer les comptes et permissions d'administrateurs</value>
  </data>
  <data name="PageAuditDescription" xml:space="preserve">
    <value>Analyser la performance et l'accessibilitÃ© des pages</value>
  </data>
  <data name="SystemInfoDescription" xml:space="preserve">
    <value>Consulter l'Ã©tat et la configuration du systÃ¨me</value>
  </data>
  <data name="TeamsDescription" xml:space="preserve">
    <value>Organiser et gÃ©rer les Ã©quipes de hockey</value>
  </data>
  <data name="ViewStatistics" xml:space="preserve">
    <value>Voir les statistiques</value>
  </data>
  <data name="BackToSiteDescription" xml:space="preserve">
    <value>Revenir Ã  la page d'accueil publique</value>
  </data>
  <data name="RecentAuditDescription" xml:space="preserve">
    <value>ActivitÃ©s rÃ©centes et modifications apportÃ©es au systÃ¨me</value>
  </data>
  <data name="AuditTableDescription" xml:space="preserve">
    <value>Tableau des activitÃ©s d'audit rÃ©centes</value>
  </data>
  <data name="AdminAction" xml:space="preserve">
    <value>Action d'administrateur</value>
  </data>
  <data name="SystemAction" xml:space="preserve">
    <value>Action systÃ¨me</value>
  </data>
  <data name="ViewAuditDetails" xml:space="preserve">
    <value>Voir les dÃ©tails de l'audit pour {0}</value>
  </data>
  <data name="NoAuditActivityDescription" xml:space="preserve">
    <value>Aucune activitÃ© d'audit n'a Ã©tÃ© enregistrÃ©e rÃ©cemment</value>
  </data>
  <data name="RecentMembersDescription" xml:space="preserve">
    <value>Nouveaux membres inscrits rÃ©cemment dans le systÃ¨me</value>
  </data>
  <data name="MembersTableDescription" xml:space="preserve">
    <value>Tableau des membres rÃ©cemment inscrits</value>
  </data>
  <data name="SendEmailTo" xml:space="preserve">
    <value>Envoyer un courriel Ã  {0}</value>
  </data>
  <data name="ViewMemberDetails" xml:space="preserve">
    <value>Voir les dÃ©tails de {0}</value>
  </data>
  <data name="NoMembersDescription" xml:space="preserve">
    <value>Aucun membre n'est encore inscrit dans le systÃ¨me</value>
  </data>
  <data name="DashboardLoaded" xml:space="preserve">
    <value>Tableau de bord chargÃ© avec succÃ¨s</value>
  </data>

  <!-- Admin Members Page Accessibility -->
  <data name="SkipToSearchFilters" xml:space="preserve">
    <value>Aller aux filtres de recherche</value>
  </data>
  <data name="SkipToMemberActions" xml:space="preserve">
    <value>Aller aux actions membres</value>
  </data>
  <data name="SkipToMembersTable" xml:space="preserve">
    <value>Aller au tableau des membres</value>
  </data>
  <data name="MemberManagementPage" xml:space="preserve">
    <value>Page de gestion des membres</value>
  </data>
  <data name="Breadcrumb" xml:space="preserve">
    <value>Fil d'Ariane</value>
  </data>
  <data name="SearchAndFilters" xml:space="preserve">
    <value>Recherche et filtres</value>
  </data>
  <data name="SearchHelpText" xml:space="preserve">
    <value>Rechercher par nom, prÃ©nom ou adresse email</value>
  </data>
  <data name="ClearSearchResults" xml:space="preserve">
    <value>Effacer les rÃ©sultats de recherche</value>
  </data>
  <data name="AdvancedFilters" xml:space="preserve">
    <value>Filtres avancÃ©s</value>
  </data>
  <data name="FilterBy" xml:space="preserve">
    <value>Filtrer par</value>
  </data>
  <data name="SelectFilterType" xml:space="preserve">
    <value>SÃ©lectionner le type de filtre</value>
  </data>
  <data name="SelectFilterTypeHelp" xml:space="preserve">
    <value>Choisir le critÃ¨re de filtrage</value>
  </data>
  <data name="FilterValue" xml:space="preserve">
    <value>Valeur du filtre</value>
  </data>
  <data name="SelectFilterValueHelp" xml:space="preserve">
    <value>SÃ©lectionner la valeur Ã  filtrer</value>
  </data>
  <data name="CitySearchHelp" xml:space="preserve">
    <value>Taper au moins 2 caractÃ¨res pour rechercher une ville</value>
  </data>
  <data name="ApplyFilter" xml:space="preserve">
    <value>Appliquer le filtre</value>
  </data>
  <data name="ApplyFilterHelp" xml:space="preserve">
    <value>Appliquer le filtre sÃ©lectionnÃ© Ã  la liste</value>
  </data>
  <data name="ClearFiltersHelp" xml:space="preserve">
    <value>Supprimer tous les filtres actifs</value>
  </data>
  <data name="MemberActions" xml:space="preserve">
    <value>Actions membres</value>
  </data>
  <data name="ExportMembersHelp" xml:space="preserve">
    <value>Exporter la liste des membres en Excel ou CSV</value>
  </data>
  <data name="AddNewMemberHelp" xml:space="preserve">
    <value>Ajouter un nouveau membre au systÃ¨me</value>
  </data>
  <data name="ImportMembers" xml:space="preserve">
    <value>Importer des membres</value>
  </data>
  <data name="ImportMembersHelp" xml:space="preserve">
    <value>Importer des membres depuis des fichiers Excel/CSV</value>
  </data>
  <data name="ReviewImportErrors" xml:space="preserve">
    <value>RÃ©viser les erreurs d'importation</value>
  </data>
  <data name="ReviewImportErrorsDescription" xml:space="preserve">
    <value>Examiner et corriger les membres avec erreurs d'importation</value>
  </data>
  <data name="SearchResultsSummary" xml:space="preserve">
    <value>RÃ©sumÃ© des rÃ©sultats de recherche</value>
  </data>
  <data name="MembersTable" xml:space="preserve">
    <value>Tableau des membres</value>
  </data>
  <data name="MembersPageLoaded" xml:space="preserve">
    <value>Page des membres chargÃ©e avec succÃ¨s</value>
  </data>
  
  <!-- Page de dÃ©tails de membre admin - AccessibilitÃ© -->
  <data name="SkipToMemberInfo" xml:space="preserve">
    <value>Aller aux informations du membre</value>
  </data>
  <data name="SkipToContactInfo" xml:space="preserve">
    <value>Aller aux informations de contact</value>
  </data>
  <data name="SkipToAuditHistory" xml:space="preserve">
    <value>Aller Ã  l'historique d'audit</value>
  </data>
  <data name="MemberDetailsPage" xml:space="preserve">
    <value>Page de dÃ©tails du membre</value>
  </data>
  <data name="MemberDetailsPageLoaded" xml:space="preserve">
    <value>Page de dÃ©tails du membre chargÃ©e avec succÃ¨s</value>
  </data>
  <data name="MemberActionButtons" xml:space="preserve">
    <value>Boutons d'actions du membre</value>
  </data>
  <data name="Exporting" xml:space="preserve">
    <value>Exportation...</value>
  </data>
  
  <!-- Page d'abonnement aux Ã©vÃ©nements - AccessibilitÃ© -->
  <data name="SkipToUpcomingEvents" xml:space="preserve">
    <value>Aller aux Ã©vÃ©nements Ã  venir</value>
  </data>
  <data name="SkipToEventCalendar" xml:space="preserve">
    <value>Aller au calendrier des Ã©vÃ©nements</value>
  </data>
  <data name="EventSubscriptionPage" xml:space="preserve">
    <value>Page d'abonnement aux Ã©vÃ©nements</value>
  </data>
  <data name="EventSubscriptionPageLoaded" xml:space="preserve">
    <value>Page d'abonnement aux Ã©vÃ©nements chargÃ©e avec succÃ¨s</value>
  </data>
  <data name="BackToHomePage" xml:space="preserve">
    <value>Retourner Ã  la page d'accueil</value>
  </data>
  <data name="MustBeLoggedInToRegister" xml:space="preserve">
    <value>Vous devez Ãªtre connectÃ© pour vous inscrire aux Ã©vÃ©nements</value>
  </data>
  <data name="LoadingEventDetails" xml:space="preserve">
    <value>Chargement des dÃ©tails de l'Ã©vÃ©nement...</value>
  </data>
</root>
