<?xml version="1.0" encoding="utf-8"?>
<root>
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <!-- Default: French -->
  <data name="Home" xml:space="preserve">
    <value>Accueil</value>
  </data>
  <data name="Privacy" xml:space="preserve">
    <value>Confidentialité</value>
  </data>
  <data name="TestBackbone" xml:space="preserve">
    <value>Test Backbone</value>
  </data>
  <data name="Login" xml:space="preserve">
    <value>Connexion</value>
  </data>
  <data name="Logout" xml:space="preserve">
    <value>Déconnexion</value>
  </data>
  <data name="Hello" xml:space="preserve">
    <value>Bonjour</value>
  </data>
  <data name="DevelopmentMode" xml:space="preserve">
    <value>Mode Développement</value>
  </data>
  <data name="Language" xml:space="preserve">
    <value>Langue</value>
  </data>
  <data name="French" xml:space="preserve">
    <value>Français</value>
  </data>
  <data name="English" xml:space="preserve">
    <value>Anglais</value>
  </data>
  <data name="Environment" xml:space="preserve">
    <value>Environnement</value>
  </data>
  <data name="Copyright" xml:space="preserve">
    <value>© {0} - Parahockey</value>
  </data>
  <data name="DevelopmentAlert" xml:space="preserve">
    <value>Mode Développement: Base de données SQLite locale, authentification désactivée pour un développement rapide.</value>
  </data>
  <data name="TestVerification" xml:space="preserve">
    <value>Test de Vérification - Parahockey</value>
  </data>
  <data name="BackboneVerification" xml:space="preserve">
    <value>Phase 1 - Vérification de l'architecture de base</value>
  </data>
  <data name="Objective" xml:space="preserve">
    <value>Objectif</value>
  </data>
  <data name="VerifyArchitecture" xml:space="preserve">
    <value>Vérifier que l'architecture fonctionne : Saisie → Traitement → Base de données → Affichage</value>
  </data>
  <data name="TotalEntries" xml:space="preserve">
    <value>Entrées totales</value>
  </data>
  <data name="ActiveEntries" xml:space="preserve">
    <value>Entrées actives</value>
  </data>
  <data name="Database" xml:space="preserve">
    <value>Base de données</value>
  </data>
  <data name="CreateNewEntry" xml:space="preserve">
    <value>Créer une nouvelle entrée</value>
  </data>
  <data name="Notes" xml:space="preserve">
    <value>Notes</value>
  </data>
  <data name="Optional" xml:space="preserve">
    <value>optionnel</value>
  </data>
  <data name="Required" xml:space="preserve">
    <value>requis</value>
  </data>
  <data name="EnterYourName" xml:space="preserve">
    <value>Entrez votre nom</value>
  </data>
  <data name="CommentsOrNotes" xml:space="preserve">
    <value>Commentaires ou notes...</value>
  </data>
  <data name="CreateEntry" xml:space="preserve">
    <value>Créer l'entrée</value>
  </data>
  <data name="BaseArchitectureVerification" xml:space="preserve">
    <value>Phase 1 - Vérification de l'architecture de base</value>
  </data>
  <data name="VerifyArchitectureFlow" xml:space="preserve">
    <value>Vérifier que l'architecture fonctionne : Saisie → Traitement → Base de données → Affichage</value>
  </data>
  <data name="TestEntries" xml:space="preserve">
    <value>Entrées de test</value>
  </data>
  <data name="SortedByCreationDate" xml:space="preserve">
    <value>Triées par date de création (plus récentes en premier)</value>
  </data>
  <data name="ID" xml:space="preserve">
    <value>ID</value>
  </data>
  <data name="CreatedOn" xml:space="preserve">
    <value>Créé le</value>
  </data>
  <data name="ShowingFirst10" xml:space="preserve">
    <value>Affichage des 10 premiers de {0} entrées</value>
  </data>
  <data name="First10" xml:space="preserve">
    <value>Premiers 10</value>
  </data>
  <data name="Between2And100Characters" xml:space="preserve">
    <value>Entre 2 et 100 caractères</value>
  </data>
  <data name="Maximum1000Characters" xml:space="preserve">
    <value>Maximum 1000 caractères</value>
  </data>
  <data name="Administration" xml:space="preserve">
    <value>Administration</value>
  </data>

  <!-- Lookup Table Translations -->
  <!-- Gender Options -->
  <data name="Gender_Male" xml:space="preserve">
    <value>Masculin</value>
  </data>
  <data name="Gender_Female" xml:space="preserve">
    <value>Féminin</value>
  </data>
  <data name="Gender_Other" xml:space="preserve">
    <value>Autre</value>
  </data>

  <!-- Phone Type Options -->
  <data name="PhoneType_Mobile" xml:space="preserve">
    <value>Mobile</value>
  </data>
  <data name="PhoneType_Other" xml:space="preserve">
    <value>Autre</value>
  </data>

  <!-- Province Options -->
  <data name="Province_AB" xml:space="preserve">
    <value>Alberta</value>
  </data>
  <data name="Province_BC" xml:space="preserve">
    <value>Colombie-Britannique</value>
  </data>
  <data name="Province_MB" xml:space="preserve">
    <value>Manitoba</value>
  </data>
  <data name="Province_NB" xml:space="preserve">
    <value>Nouveau-Brunswick</value>
  </data>
  <data name="Province_NL" xml:space="preserve">
    <value>Terre-Neuve-et-Labrador</value>
  </data>
  <data name="Province_NS" xml:space="preserve">
    <value>Nouvelle-Écosse</value>
  </data>
  <data name="Province_NT" xml:space="preserve">
    <value>Territoires du Nord-Ouest</value>
  </data>
  <data name="Province_NU" xml:space="preserve">
    <value>Nunavut</value>
  </data>
  <data name="Province_ON" xml:space="preserve">
    <value>Ontario</value>
  </data>
  <data name="Province_PE" xml:space="preserve">
    <value>Île-du-Prince-Édouard</value>
  </data>
  <data name="Province_QC" xml:space="preserve">
    <value>Québec</value>
  </data>
  <data name="Province_SK" xml:space="preserve">
    <value>Saskatchewan</value>
  </data>
  <data name="Province_YT" xml:space="preserve">
    <value>Yukon</value>
  </data>

  <!-- Registration Type Options -->
  <data name="RegType_Junior" xml:space="preserve">
    <value>Junior</value>
  </data>
  <data name="RegType_Development" xml:space="preserve">
    <value>Développement</value>
  </data>
  <data name="RegType_Elite" xml:space="preserve">
    <value>Élite</value>
  </data>
  <data name="RegType_Coach" xml:space="preserve">
    <value>Entraîneur</value>
  </data>
  <data name="RegType_Volunteer" xml:space="preserve">
    <value>Bénévole</value>
  </data>

  <!-- Form Labels -->
  <data name="ContactInformation" xml:space="preserve">
    <value>Informations de contact</value>
  </data>
  <data name="PhoneNumber" xml:space="preserve">
    <value>Numéro de téléphone</value>
  </data>

  <!-- Home Page -->
  <data name="HomePageTitle" xml:space="preserve">
    <value>Parahockey - Accueil</value>
  </data>
  <data name="WelcomeTitle" xml:space="preserve">
    <value>Bienvenue chez Parahockey</value>
  </data>
  <data name="WelcomeSubtitle" xml:space="preserve">
    <value>Rejoignez notre communauté de passionnés de hockey adapté. Que vous soyez joueur, entraîneur ou bénévole, votre place est ici.</value>
  </data>
  <data name="RegisterNow" xml:space="preserve">
    <value>S'inscrire maintenant</value>
  </data>
  <data name="AdminPanel" xml:space="preserve">
    <value>Panneau d'administration</value>
  </data>
  <data name="AdminLogin" xml:space="preserve">
    <value>Connexion Admin</value>
  </data>
  <data name="CommunityTitle" xml:space="preserve">
    <value>Notre Communauté</value>
  </data>
  <data name="ActivePlayers" xml:space="preserve">
    <value>Joueurs actifs</value>
  </data>
  <data name="Teams" xml:space="preserve">
    <value>Équipes</value>
  </data>
  <data name="Coaches" xml:space="preserve">
    <value>Entraîneurs</value>
  </data>
  <data name="Volunteers" xml:space="preserve">
    <value>Bénévoles</value>
  </data>
  <data name="RegistrationTypesTitle" xml:space="preserve">
    <value>Types d'inscription disponibles</value>
  </data>
  <data name="RegistrationTypesSubtitle" xml:space="preserve">
    <value>Trouvez votre place dans notre équipe</value>
  </data>
  <data name="Junior" xml:space="preserve">
    <value>Junior</value>
  </data>
  <data name="JuniorDesc" xml:space="preserve">
    <value>Programme spécialement conçu pour les jeunes joueurs qui débutent leur parcours dans le hockey adapté.</value>
  </data>
  <data name="Development" xml:space="preserve">
    <value>Développement</value>
  </data>
  <data name="DevelopmentDesc" xml:space="preserve">
    <value>Pour ceux qui souhaitent apprendre et progresser dans un environnement bienveillant et structuré.</value>
  </data>
  <data name="Elite" xml:space="preserve">
    <value>Élite</value>
  </data>
  <data name="EliteDesc" xml:space="preserve">
    <value>Niveau compétitif pour les joueurs expérimentés qui visent l'excellence sportive.</value>
  </data>
  <data name="Coach" xml:space="preserve">
    <value>Entraîneur</value>
  </data>
  <data name="CoachDesc" xml:space="preserve">
    <value>Rejoignez notre équipe d'encadrement et partagez votre passion pour le hockey adapté.</value>
  </data>
  <data name="Volunteer" xml:space="preserve">
    <value>Bénévole</value>
  </data>
  <data name="VolunteerDesc" xml:space="preserve">
    <value>Contribuez au succès de notre organisation en offrant votre temps et vos compétences.</value>
  </data>
  <data name="FamilyFriends" xml:space="preserve">
    <value>Famille &amp; Amis</value>
  </data>
  <data name="FamilyFriendsDesc" xml:space="preserve">
    <value>Soutenez nos joueurs et découvrez l'esprit unique du hockey adapté.</value>
  </data>
  <data name="CtaTitle" xml:space="preserve">
    <value>Prêt à rejoindre l'aventure ?</value>
  </data>
  <data name="CtaSubtitle" xml:space="preserve">
    <value>L'inscription ne prend que quelques minutes. Commencez votre parcours avec Parahockey dès aujourd'hui.</value>
  </data>
  <data name="CtaButton" xml:space="preserve">
    <value>Démarrer mon inscription</value>
  </data>

  <!-- Registration Page -->
  <data name="RegistrationPageTitle" xml:space="preserve">
    <value>Inscription - Parahockey</value>
  </data>
  <data name="EditProfilePageTitle" xml:space="preserve">
    <value>Modifier le profil - Parahockey</value>
  </data>
  <data name="EditProfileTitle" xml:space="preserve">
    <value>Modifier le profil</value>
  </data>
  <data name="UpdateProfileButton" xml:space="preserve">
    <value>Mettre à jour le profil</value>
  </data>
  <data name="RegistrationFormTitle" xml:space="preserve">
    <value>Formulaire d'Inscription</value>
  </data>
  <data name="AlreadyRegistered" xml:space="preserve">
    <value>Déjà inscrit?</value>
  </data>
  <data name="ClickHereLink" xml:space="preserve">
    <value>Cliquez ici</value>
  </data>
  <data name="LoginLink" xml:space="preserve">
    <value>Connexion</value>
  </data>
  <data name="ValidationErrors" xml:space="preserve">
    <value>Veuillez corriger les erreurs suivantes :</value>
  </data>
  <data name="BasicInformation" xml:space="preserve">
    <value>Information de base</value>
  </data>
  <data name="FirstName" xml:space="preserve">
    <value>Prénom</value>
  </data>
  <data name="FirstNamePlaceholder" xml:space="preserve">
    <value>Votre prénom</value>
  </data>
  <data name="LastName" xml:space="preserve">
    <value>Nom</value>
  </data>
  <data name="LastNamePlaceholder" xml:space="preserve">
    <value>Votre nom de famille</value>
  </data>
  <data name="ThirdPersonFirstNamePlaceholder" xml:space="preserve">
    <value>Prénom du contact</value>
  </data>
  <data name="ThirdPersonLastNamePlaceholder" xml:space="preserve">
    <value>Nom de famille du contact</value>
  </data>
  <data name="ThirdPersonEmailPlaceholder" xml:space="preserve">
    <value>Adresse courriel du contact</value>
  </data>
  <data name="DateOfBirth" xml:space="preserve">
    <value>Date de naissance</value>
  </data>
  <data name="DateFormat" xml:space="preserve">
    <value>Format: AAAA-MM-JJ</value>
  </data>
  <data name="Gender" xml:space="preserve">
    <value>Genre</value>
  </data>
  <data name="Male" xml:space="preserve">
    <value>Masculin</value>
  </data>
  <data name="Female" xml:space="preserve">
    <value>Féminin</value>
  </data>
  <data name="Other" xml:space="preserve">
    <value>Autres</value>
  </data>
  <data name="Address" xml:space="preserve">
    <value>Adresse</value>
  </data>
  <data name="StreetAddress" xml:space="preserve">
    <value>Adresse</value>
  </data>
  <data name="StreetAddressPlaceholder" xml:space="preserve">
    <value>123 Rue de la Patinoire</value>
  </data>
  <data name="City" xml:space="preserve">
    <value>Ville</value>
  </data>
  <data name="CityPlaceholder" xml:space="preserve">
    <value>Montréal</value>
  </data>
  <data name="Province" xml:space="preserve">
    <value>Province</value>
  </data>
  <data name="SelectProvince" xml:space="preserve">
    <value>Sélectionnez une province</value>
  </data>
  <data name="PostalCode" xml:space="preserve">
    <value>Code postal</value>
  </data>
  <data name="PostalCodeFormat" xml:space="preserve">
    <value>Format: H1H 1H1</value>
  </data>
  <data name="Contact" xml:space="preserve">
    <value>Contact</value>
  </data>
  <data name="Phone" xml:space="preserve">
    <value>Téléphone</value>
  </data>
  <data name="PhonePlaceholder" xml:space="preserve">
    <value>(*************</value>
  </data>
  <data name="PhoneType" xml:space="preserve">
    <value>Type</value>
  </data>
  <data name="Mobile" xml:space="preserve">
    <value>Cellulaire</value>
  </data>
  <data name="Email" xml:space="preserve">
    <value>Courriel</value>
  </data>
  <data name="EmailPlaceholder" xml:space="preserve">
    <value><EMAIL></value>
  </data>
  <data name="RegistrationType" xml:space="preserve">
    <value>Type d'inscription</value>
  </data>
  <data name="JuniorSubtext" xml:space="preserve">
    <value>Pour les jeunes joueurs</value>
  </data>
  <data name="DevelopmentSubtext" xml:space="preserve">
    <value>Pour apprendre et progresser</value>
  </data>
  <data name="EliteSubtext" xml:space="preserve">
    <value>Pour les joueurs expérimentés</value>
  </data>
  <data name="CoachSubtext" xml:space="preserve">
    <value>Pour encadrer les équipes</value>
  </data>
  <data name="VolunteerSubtext" xml:space="preserve">
    <value>Pour aider l'organisation</value>
  </data>
  <data name="RegisterButton" xml:space="preserve">
    <value>S'inscrire</value>
  </data>
  <data name="ClearFormButton" xml:space="preserve">
    <value>Effacer le formulaire</value>
  </data>
  <data name="ConfirmReset" xml:space="preserve">
    <value>Voulez-vous vraiment effacer tous les champs du formulaire ?</value>
  </data>

  <!-- Validation Messages -->
  <data name="ValidationRequired" xml:space="preserve">
    <value>Le champ {0} est requis.</value>
  </data>
  <data name="ValidationStringLength" xml:space="preserve">
    <value>Le champ {0} doit être une chaîne d'une longueur maximale de {1}.</value>
  </data>
  <data name="ValidationPostalCode" xml:space="preserve">
    <value>Format de code postal invalide (ex: H1H 1H1).</value>
  </data>
  <data name="ValidationPhone" xml:space="preserve">
    <value>Format de numéro de téléphone invalide.</value>
  </data>
  <data name="ValidationEmail" xml:space="preserve">
    <value>Format d'adresse de courriel invalide.</value>
  </data>
  <data name="ValidationEmailFormat" xml:space="preserve">
    <value>Le format du courriel n'est pas valide. Exemple: <EMAIL></value>
  </data>

  <!-- Modern Validation Messages -->
  <data name="ValidationEmailSpecific" xml:space="preserve">
    <value>Veuillez entrer une adresse courriel valide (ex: <EMAIL>)</value>
  </data>
  <data name="ValidationPhoneSpecific" xml:space="preserve">
    <value>Veuillez entrer un numéro de téléphone à 10 chiffres</value>
  </data>
  <data name="ValidationPostalCodeSpecific" xml:space="preserve">
    <value>Veuillez entrer un code postal canadien valide (ex: H1H 1H1)</value>
  </data>
  <data name="ValidationDateSpecific" xml:space="preserve">
    <value>Veuillez entrer une date valide au format AAAA-MM-JJ</value>
  </data>
  <data name="ValidationDateFuture" xml:space="preserve">
    <value>La date de naissance ne peut pas être dans le futur</value>
  </data>
  <data name="ValidationDateTooOld" xml:space="preserve">
    <value>Veuillez entrer une date de naissance valide</value>
  </data>
  <data name="ValidationNameTooShort" xml:space="preserve">
    <value>Le nom doit contenir au moins 2 caractères</value>
  </data>
  <data name="ValidationNameTooLong" xml:space="preserve">
    <value>Le nom ne peut pas dépasser 50 caractères</value>
  </data>
  <data name="ValidationAddressTooShort" xml:space="preserve">
    <value>L'adresse doit contenir au moins 5 caractères</value>
  </data>
  <data name="ValidationCityTooShort" xml:space="preserve">
    <value>La ville doit contenir au moins 2 caractères</value>
  </data>
  <data name="ValidationProvinceRequired" xml:space="preserve">
    <value>Veuillez sélectionner une province</value>
  </data>
  <data name="ValidationGenderRequired" xml:space="preserve">
    <value>Veuillez sélectionner un genre</value>
  </data>
  <data name="ValidationRegistrationTypeRequired" xml:space="preserve">
    <value>Veuillez sélectionner un type d'inscription</value>
  </data>
  <data name="ValidationPhoneTypeRequired" xml:space="preserve">
    <value>Veuillez sélectionner un type de téléphone</value>
  </data>
  
  <!-- Debug and Test Status Section -->
  <data name="DebugUrlGeneration" xml:space="preserve">
    <value>DEBUG: Génération d'URL</value>
  </data>
  <data name="TestPostUrl" xml:space="preserve">
    <value>URL TestPost</value>
  </data>
  <data name="CreateUrl" xml:space="preserve">
    <value>URL Créer</value>
  </data>
  <data name="GetFirst10Url" xml:space="preserve">
    <value>URL GetFirst10</value>
  </data>
  <data name="CurrentUrl" xml:space="preserve">
    <value>URL actuelle</value>
  </data>
  <data name="BaseUrl" xml:space="preserve">
    <value>URL de base</value>
  </data>
  <data name="DebugTestPostRouting" xml:space="preserve">
    <value>DEBUG: Routage POST de test</value>
  </data>
  <data name="TestBasicPost" xml:space="preserve">
    <value>Test POST basique</value>
  </data>
  <data name="FormActionWillBe" xml:space="preserve">
    <value>L'action du formulaire sera</value>
  </data>
  <data name="EmergencyDirectHtmlPost" xml:space="preserve">
    <value>URGENCE: POST HTML direct</value>
  </data>
  <data name="DirectHtmlFormAction" xml:space="preserve">
    <value>Action du formulaire HTML direct</value>
  </data>
  <data name="DebugTestGetFunctionality" xml:space="preserve">
    <value>DEBUG: Test de fonctionnalité GET</value>
  </data>
  <data name="TestThatGetOperationsWork" xml:space="preserve">
    <value>Tester que les opérations GET fonctionnent en chargeant les 10 premières entrées de la base de données</value>
  </data>
  <data name="GetFirst10Entries" xml:space="preserve">
    <value>Obtenir les 10 premières entrées</value>
  </data>
  <data name="ThisWillCall" xml:space="preserve">
    <value>Ceci appellera</value>
  </data>
  <data name="UrlWillBe" xml:space="preserve">
    <value>L'URL sera</value>
  </data>
  <data name="GetTestActive" xml:space="preserve">
    <value>Test GET actif!</value>
  </data>
  <data name="CurrentlyShowingFirst" xml:space="preserve">
    <value>Affichage actuellement des {0} premières entrées.</value>
  </data>
  <data name="ShowAllEntries" xml:space="preserve">
    <value>Afficher toutes les entrées</value>
  </data>
  <data name="Actions" xml:space="preserve">
    <value>Actions</value>
  </data>
  <data name="Anonymous" xml:space="preserve">
    <value>Anonyme</value>
  </data>
  <data name="NoTestEntries" xml:space="preserve">
    <value>Aucune entrée de test</value>
  </data>
  <data name="CreateFirstEntry" xml:space="preserve">
    <value>Créez votre première entrée en utilisant le formulaire ci-dessus.</value>
  </data>
  <data name="TestStatus" xml:space="preserve">
    <value>Statut des Tests</value>
  </data>
  <data name="TestedFeatures" xml:space="preserve">
    <value>✅ Fonctionnalités testées :</value>
  </data>
  <data name="DatabaseConnection" xml:space="preserve">
    <value>✅ Connexion à la base de données</value>
  </data>
  <data name="EntityCreationPost" xml:space="preserve">
    <value>✅ Création d'entités (POST)</value>
  </data>
  <data name="EntityReadingGet" xml:space="preserve">
    <value>✅ Lecture d'entités (GET)</value>
  </data>
  <data name="DataDisplay" xml:space="preserve">
    <value>✅ Affichage des données</value>
  </data>
  <data name="SoftDelete" xml:space="preserve">
    <value>✅ Suppression (soft delete)</value>
  </data>
  <data name="DataValidation" xml:space="preserve">
    <value>✅ Validation des données</value>
  </data>
  <data name="AutoMapperMapping" xml:space="preserve">
    <value>✅ Mapping AutoMapper</value>
  </data>
  <data name="UserInterface" xml:space="preserve">
    <value>✅ Interface utilisateur</value>
  </data>
  <data name="NextSteps" xml:space="preserve">
    <value>🔄 Prochaines étapes :</value>
  </data>
  <data name="AzureAdAuthentication" xml:space="preserve">
    <value>🔄 Authentification Azure AD</value>
  </data>
  <data name="SqlServerDatabase" xml:space="preserve">
    <value>🔄 Base de données SQL Server</value>
  </data>
  <data name="RealParaHockeyEntities" xml:space="preserve">
    <value>🔄 Entités Parahockey réelles</value>
  </data>
  <data name="AutomatedTests" xml:space="preserve">
    <value>🔄 Tests automatisés</value>
  </data>
  <data name="CiCdPipeline" xml:space="preserve">
    <value>🔄 Pipeline CI/CD</value>
  </data>

  <!-- Provinces -->
  <data name="ProvinceQC" xml:space="preserve">
    <value>Québec (QC)</value>
  </data>
  <data name="ProvinceON" xml:space="preserve">
    <value>Ontario (ON)</value>
  </data>
  <data name="ProvinceBC" xml:space="preserve">
    <value>Colombie-Britannique (BC)</value>
  </data>
  <data name="ProvinceAB" xml:space="preserve">
    <value>Alberta (AB)</value>
  </data>
  <data name="ProvinceMB" xml:space="preserve">
    <value>Manitoba (MB)</value>
  </data>
  <data name="ProvinceSK" xml:space="preserve">
    <value>Saskatchewan (SK)</value>
  </data>
  <data name="ProvinceNS" xml:space="preserve">
    <value>Nouvelle-Écosse (NS)</value>
  </data>
  <data name="ProvinceNB" xml:space="preserve">
    <value>Nouveau-Brunswick (NB)</value>
  </data>
  <data name="ProvinceNL" xml:space="preserve">
    <value>Terre-Neuve-et-Labrador (NL)</value>
  </data>
  <data name="ProvincePE" xml:space="preserve">
    <value>Île-du-Prince-Édouard (PE)</value>
  </data>
  <data name="ProvinceYT" xml:space="preserve">
    <value>Yukon (YT)</value>
  </data>
  <data name="ProvinceNT" xml:space="preserve">
    <value>Territoires du Nord-Ouest (NT)</value>
  </data>
  <data name="ProvinceNU" xml:space="preserve">
    <value>Nunavut (NU)</value>
  </data>
  <data name="DateFormatHelper" xml:space="preserve">
    <value>Format: AAAA-MM-JJ (ex: 1995-03-15)</value>
  </data>
  <data name="DatePlaceholder" xml:space="preserve">
    <value>AAAA-MM-JJ</value>
  </data>

  <data name="NoDataForReport" xml:space="preserve">
    <value>Aucune donnée disponible pour ce rapport</value>
  </data>

  <!-- Parent Information -->
  <data name="ParentInformation" xml:space="preserve">
    <value>Informations du parent/tuteur</value>
  </data>
  <data name="ParentGuardianDetails" xml:space="preserve">
    <value>Détails du parent/tuteur</value>
  </data>
  <data name="SecondParentGuardianDetails" xml:space="preserve">
    <value>Détails du deuxième parent/tuteur</value>
  </data>
  <data name="ParentType" xml:space="preserve">
    <value>Type de parent</value>
  </data>
  <data name="SelectParentType" xml:space="preserve">
    <value>Sélectionner le type de parent</value>
  </data>
  <data name="Mother" xml:space="preserve">
    <value>Mère</value>
  </data>
  <data name="Father" xml:space="preserve">
    <value>Père</value>
  </data>
  <data name="Guardian" xml:space="preserve">
    <value>Tuteur/Tutrice</value>
  </data>
  <data name="OtherRelation" xml:space="preserve">
    <value>Autre</value>
  </data>

  <!-- Emergency Contact Information -->
  <data name="EmergencyContactInformation" xml:space="preserve">
    <value>Contact d'urgence</value>
  </data>
  <data name="EmergencyContactDetails" xml:space="preserve">
    <value>Détails du contact d'urgence</value>
  </data>
  <data name="RelationToUser" xml:space="preserve">
    <value>Relation avec le membre</value>
  </data>
  <data name="SelectRelation" xml:space="preserve">
    <value>Sélectionner la relation</value>
  </data>
  <data name="Spouse" xml:space="preserve">
    <value>Conjoint(e)</value>
  </data>
  <data name="Parent" xml:space="preserve">
    <value>Parent</value>
  </data>
  <data name="Sibling" xml:space="preserve">
    <value>Frère/Sœur</value>
  </data>
  <data name="Friend" xml:space="preserve">
    <value>Ami(e)</value>
  </data>

  <!-- Login Page -->
  <data name="LoginPageTitle" xml:space="preserve">
    <value>Recherche de membre - Parahockey</value>
  </data>
  <data name="SearchForMember" xml:space="preserve">
    <value>Rechercher un membre</value>
  </data>
  <data name="SearchButton" xml:space="preserve">
    <value>Rechercher</value>
  </data>
  <data name="SearchResults" xml:space="preserve">
    <value>Résultats de la recherche</value>
  </data>
  <data name="NoResultsFound" xml:space="preserve">
    <value>Aucun membre ne correspond à vos critères. Si votre compte a été désactivé, veuillez contacter l'administration.</value>
  </data>
  <data name="WrongEmail" xml:space="preserve">
    <value>Courriel incorrect</value>
  </data>
  <data name="SendCodeButton" xml:space="preserve">
    <value>Envoyer le code</value>
  </data>
  <data name="EmailVerification" xml:space="preserve">
    <value>Vérification par courriel</value>
  </data>
  <data name="VerificationCodeSent" xml:space="preserve">
    <value>Un code de vérification a été envoyé à votre adresse de courriel.</value>
  </data>
  <data name="EnterCode" xml:space="preserve">
    <value>Entrez le code</value>
  </data>
  <data name="VerifyButton" xml:space="preserve">
    <value>Vérifier</value>
  </data>
  
  <!-- Azure AD Admin Users -->
  <data name="AdminUsers" xml:space="preserve">
    <value>Utilisateurs administrateurs</value>
  </data>
  <data name="AzureADInfo" xml:space="preserve">
    <value>Informations sur Azure AD</value>
  </data>
  <data name="AzureADDescription" xml:space="preserve">
    <value>L'accès administrateur utilise l'authentification Azure AD. Seuls les utilisateurs membres du groupe d'administration configuré peuvent accéder à ces fonctionnalités.</value>
  </data>
  <data name="CurrentUserInfo" xml:space="preserve">
    <value>Informations sur l'utilisateur actuel</value>
  </data>
  <data name="Username" xml:space="preserve">
    <value>Nom d'utilisateur</value>
  </data>
  <data name="AdminGroupId" xml:space="preserve">
    <value>ID du groupe d'administration</value>
  </data>
  <data name="UserGroups" xml:space="preserve">
    <value>Groupes de l'utilisateur</value>
  </data>
  <data name="AdminAccess" xml:space="preserve">
    <value>Accès administrateur</value>
  </data>
  <data name="NoGroups" xml:space="preserve">
    <value>Aucun groupe</value>
  </data>
  <data name="ConfigurationSteps" xml:space="preserve">
    <value>Étapes de configuration</value>
  </data>
  <data name="AzureADStep1" xml:space="preserve">
    <value>Inscrire l'application dans Azure AD</value>
  </data>
  <data name="AzureADStep2" xml:space="preserve">
    <value>Configurer les valeurs dans appsettings.json</value>
  </data>
  <data name="AzureADStep3" xml:space="preserve">
    <value>Créer un groupe d'administration Azure AD</value>
  </data>
  <data name="AzureADStep4" xml:space="preserve">
    <value>Ajouter des utilisateurs au groupe d'administration</value>
  </data>
  <data name="AzureADStep5" xml:space="preserve">
    <value>Configurer l'ID du groupe dans appsettings.json</value>
  </data>
  <data name="AzureADWarning" xml:space="preserve">
    <value>Avertissement : Les valeurs Azure AD dans appsettings.json sont actuellement des valeurs par défaut. Elles doivent être configurées avec vos valeurs Azure AD réelles.</value>
  </data>
  <data name="BackToDashboard" xml:space="preserve">
    <value>Retour au tableau de bord</value>
  </data>

  <!-- Calendar & Events -->
  <data name="CalendarEvents" xml:space="preserve">
    <value>Calendrier et événements</value>
  </data>
  <data name="CalendarEventsDescription" xml:space="preserve">
    <value>Gérer les événements et les inscriptions des membres</value>
  </data>
  <data name="ComingSoon" xml:space="preserve">
    <value>Bientôt disponible</value>
  </data>
  <data name="PlannedFeatures" xml:space="preserve">
    <value>Fonctionnalités prévues :</value>
  </data>
  <data name="CreateManageEvents" xml:space="preserve">
    <value>Créer et gérer des événements</value>
  </data>
  <data name="MemberEventSubscriptions" xml:space="preserve">
    <value>Inscriptions des membres aux événements</value>
  </data>
  <data name="SeasonBasedEventOrganization" xml:space="preserve">
    <value>Organisation d'événements par saison</value>
  </data>
  <data name="EventCapacityManagement" xml:space="preserve">
    <value>Gestion de la capacité des événements</value>
  </data>
  <data name="CalendarFunctionalityMessage" xml:space="preserve">
    <value>Les fonctionnalités de calendrier et d'événements seront implémentées ici.</value>
  </data>

  <!-- Teams Management -->
  <data name="TeamsManagement" xml:space="preserve">
    <value>Gestion des équipes</value>
  </data>
  <data name="TeamsManagementDescription" xml:space="preserve">
    <value>Gérer les équipes, les joueurs et les entraîneurs</value>
  </data>
  <data name="CreateManageTeams" xml:space="preserve">
    <value>Créer et gérer des équipes</value>
  </data>
  <data name="AssignPlayersCoaches" xml:space="preserve">
    <value>Assigner des joueurs et des entraîneurs</value>
  </data>
  <data name="TeamRosterManagement" xml:space="preserve">
    <value>Gestion des alignements d'équipe</value>
  </data>
  <data name="SeasonBasedTeamOrganization" xml:space="preserve">
    <value>Organisation d'équipes par saison</value>
  </data>
  <data name="TeamsManagementMessage" xml:space="preserve">
    <value>Les fonctionnalités de gestion d'équipes seront implémentées ici.</value>
  </data>

  <!-- Rankings -->
  <data name="Rankings" xml:space="preserve">
    <value>Classements</value>
  </data>
  <data name="RankingsDescription" xml:space="preserve">
    <value>Voir les classements et les standings des équipes</value>
  </data>
  <data name="TeamStandingsRankings" xml:space="preserve">
    <value>Standings et classements des équipes</value>
  </data>
  <data name="WinLossTracking" xml:space="preserve">
    <value>Suivi des victoires/défaites</value>
  </data>
  <data name="PointsCalculation" xml:space="preserve">
    <value>Calcul des points</value>
  </data>
  <data name="SeasonBasedRankings" xml:space="preserve">
    <value>Classements par saison</value>
  </data>
  <data name="RankingsMessage" xml:space="preserve">
    <value>Les fonctionnalités de classements seront implémentées ici.</value>
  </data>

  <!-- Statistics -->
  <data name="Statistics" xml:space="preserve">
    <value>Statistiques</value>
  </data>
  <data name="StatisticsDescription" xml:space="preserve">
    <value>Voir les statistiques de performance des joueurs et des équipes</value>
  </data>
  <data name="PlayerPerformanceStatistics" xml:space="preserve">
    <value>Statistiques de performance des joueurs</value>
  </data>
  <data name="TeamPerformanceMetrics" xml:space="preserve">
    <value>Métriques de performance des équipes</value>
  </data>
  <data name="SeasonComparisonReports" xml:space="preserve">
    <value>Rapports de comparaison des saisons</value>
  </data>
  <data name="ManualStatisticsEntry" xml:space="preserve">
    <value>Saisie manuelle des statistiques</value>
  </data>
  <data name="StatisticsMessage" xml:space="preserve">
    <value>Les fonctionnalités de statistiques seront implémentées ici.</value>
  </data>

  <!-- Admin Management -->
  <data name="ManageAdmins" xml:space="preserve">
    <value>Gérer les administrateurs</value>
  </data>
  <data name="CurrentAdministrators" xml:space="preserve">
    <value>Administrateurs actuels</value>
  </data>
  <data name="AddNewAdministrator" xml:space="preserve">
    <value>Ajouter un nouvel administrateur</value>
  </data>
  <data name="YourAccount" xml:space="preserve">
    <value>Votre compte</value>
  </data>
  <data name="LoggedInAs" xml:space="preserve">
    <value>Connecté en tant que :</value>
  </data>
  <data name="Authentication" xml:space="preserve">
    <value>Authentification :</value>
  </data>
  <data name="MicrosoftAzureAD" xml:space="preserve">
    <value>Microsoft/Azure AD</value>
  </data>
  <data name="Name" xml:space="preserve">
    <value>Nom</value>
  </data>
  <data name="Role" xml:space="preserve">
    <value>Rôle</value>
  </data>
  <data name="Added" xml:space="preserve">
    <value>Ajouté</value>
  </data>
  <data name="MasterAdmin" xml:space="preserve">
    <value>Administrateur maître</value>
  </data>
  <data name="NormalAdmin" xml:space="preserve">
    <value>Administrateur normal</value>
  </data>
  <data name="Disabled" xml:space="preserve">
    <value>Désactivé</value>
  </data>
  <data name="CannotRemove" xml:space="preserve">
    <value>Ne peut pas être supprimé</value>
  </data>
  <data name="Remove" xml:space="preserve">
    <value>Supprimer</value>
  </data>
  <data name="EmailAddress" xml:space="preserve">
    <value>Adresse courriel</value>
  </data>
  <data name="FullName" xml:space="preserve">
    <value>Nom complet</value>
  </data>
  <data name="AddAdmin" xml:space="preserve">
    <value>Ajouter administrateur</value>
  </data>
  <data name="NoAdministratorsFound" xml:space="preserve">
    <value>Aucun administrateur trouvé.</value>
  </data>

  <!-- Quick Actions -->
  <data name="QuickActions" xml:space="preserve">
    <value>Actions rapides</value>
  </data>
  <data name="ViewAllMembers" xml:space="preserve">
    <value>Voir tous les membres</value>
  </data>
  <data name="AddNewMember" xml:space="preserve">
    <value>Ajouter un nouveau membre</value>
  </data>
  <data name="SystemInformation" xml:space="preserve">
    <value>Informations système</value>
  </data>
  <data name="BackToSite" xml:space="preserve">
    <value>Retour au site</value>
  </data>

  <!-- Recent Audit Activity -->
  <data name="RecentMemberAudit" xml:space="preserve">
    <value>Activité d'audit récente</value>
  </data>
  <data name="RecentChanges" xml:space="preserve">
    <value>Changements récents</value>
  </data>
  <data name="ViewAllAuditHistory" xml:space="preserve">
    <value>Voir tout l'historique d'audit</value>
  </data>
  <data name="NoRecentActivity" xml:space="preserve">
    <value>Aucune activité récente.</value>
  </data>
  <data name="AuditDateTime" xml:space="preserve">
    <value>Date/Heure</value>
  </data>
  <data name="Action" xml:space="preserve">
    <value>Action</value>
  </data>
  <data name="PerformedBy" xml:space="preserve">
    <value>Effectué par</value>
  </data>
  <data name="Description" xml:space="preserve">
    <value>Description</value>
  </data>
  <data name="Create" xml:space="preserve">
    <value>Créer</value>
  </data>
  <data name="Update" xml:space="preserve">
    <value>Modifier</value>
  </data>
  <data name="Delete" xml:space="preserve">
    <value>Supprimer</value>
  </data>
  <data name="Admin" xml:space="preserve">
    <value>Admin</value>
  </data>
  <data name="Member" xml:space="preserve">
    <value>Membre</value>
  </data>
  <data name="System" xml:space="preserve">
    <value>Système</value>
  </data>
  <data name="RecentMemberRegistrations" xml:space="preserve">
    <value>Inscriptions récentes de membres</value>
  </data>
  <data name="DateCreated" xml:space="preserve">
    <value>Date de création</value>
  </data>
  <data name="NoMembersRegistered" xml:space="preserve">
    <value>Aucun membre inscrit encore.</value>
  </data>
  <data name="EntityType" xml:space="preserve">
    <value>Type d'entité</value>
  </data>
  <data name="Filters" xml:space="preserve">
    <value>Filtres</value>
  </data>
  <data name="AllEntityTypes" xml:space="preserve">
    <value>Tous les types d'entités</value>
  </data>
  <data name="AllActions" xml:space="preserve">
    <value>Toutes les actions</value>
  </data>
  <data name="Filter" xml:space="preserve">
    <value>Filtrer</value>
  </data>
  <data name="Clear" xml:space="preserve">
    <value>Effacer</value>
  </data>
  <data name="ShowingResults" xml:space="preserve">
    <value>Affichage des résultats {0} à {1} sur {2}</value>
  </data>
  <data name="Source" xml:space="preserve">
    <value>Source</value>
  </data>
  <data name="NoDescriptionAvailable" xml:space="preserve">
    <value>Aucune description disponible</value>
  </data>
  <data name="Previous" xml:space="preserve">
    <value>Précédent</value>
  </data>
  <data name="Next" xml:space="preserve">
    <value>Suivant</value>
  </data>
  <data name="NoAuditLogsFound" xml:space="preserve">
    <value>Aucun journal d'audit trouvé</value>
  </data>
  <data name="NoAuditLogsFoundDesc" xml:space="preserve">
    <value>Aucune activité d'audit ne correspond aux critères de filtre sélectionnés.</value>
  </data>
  <data name="ViewAllRecords" xml:space="preserve">
    <value>Voir tous les enregistrements</value>
  </data>

  <!-- Admin Dashboard -->
  <data name="AdminDashboard" xml:space="preserve">
    <value>Tableau de bord administrateur</value>
  </data>
  <data name="Welcome" xml:space="preserve">
    <value>Bienvenue</value>
  </data>
  <data name="TotalMembers" xml:space="preserve">
    <value>Total des membres</value>
  </data>
  <data name="TotalParents" xml:space="preserve">
    <value>Total des parents</value>
  </data>
  <data name="EmergencyContacts" xml:space="preserve">
    <value>Contacts d'urgence</value>
  </data>
  <data name="ViewAll" xml:space="preserve">
    <value>Voir tout</value>
  </data>
  <data name="SystemInfo" xml:space="preserve">
    <value>Informations système</value>
  </data>

  <!-- Access Denied -->
  <data name="AccessDenied" xml:space="preserve">
    <value>Accès refusé</value>
  </data>
  <data name="NoPermissionMessage" xml:space="preserve">
    <value>Vous n'avez pas la permission d'accéder à cette zone</value>
  </data>
  <data name="OnlyAdminsMessage" xml:space="preserve">
    <value>Seuls les administrateurs peuvent accéder au panneau d'administration.</value>
  </data>
  <data name="ContactAdminMessage" xml:space="preserve">
    <value>Si vous pensez qu'il s'agit d'une erreur, veuillez contacter l'administrateur système.</value>
  </data>
  <data name="PleaseLoginMessage" xml:space="preserve">
    <value>Veuillez vous connecter pour accéder au panneau d'administration.</value>
  </data>
  <data name="ReturnToHome" xml:space="preserve">
    <value>Retour à l'accueil</value>
  </data>

  <!-- System Information -->
  <data name="EnvironmentConfiguration" xml:space="preserve">
    <value>Configuration de l'environnement</value>
  </data>
  <data name="Theme" xml:space="preserve">
    <value>Thème</value>
  </data>
  <data name="ShowBanner" xml:space="preserve">
    <value>Afficher la bannière</value>
  </data>
  <data name="DatabaseType" xml:space="preserve">
    <value>Type de base de données</value>
  </data>
  <data name="DatabaseStatus" xml:space="preserve">
    <value>Statut de la base de données</value>
  </data>
  <data name="DatabaseStatistics" xml:space="preserve">
    <value>Statistiques de la base de données</value>
  </data>
  <data name="Connected" xml:space="preserve">
    <value>Connecté</value>
  </data>
  <data name="NotConnected" xml:space="preserve">
    <value>Non connecté</value>
  </data>
  <data name="Enabled" xml:space="preserve">
    <value>Activé</value>
  </data>
  <data name="DisabledDevelopment" xml:space="preserve">
    <value>Désactivé (Développement)</value>
  </data>
  <data name="Yes" xml:space="preserve">
    <value>Oui</value>
  </data>
  <data name="No" xml:space="preserve">
    <value>Non</value>
  </data>
  <data name="QuickActionsText" xml:space="preserve">
    <value>Actions rapides :</value>
  </data>
  <data name="AuthenticationStatus" xml:space="preserve">
    <value>Statut d'authentification</value>
  </data>
  <data name="UserAuthenticated" xml:space="preserve">
    <value>Utilisateur authentifié</value>
  </data>
  <data name="CurrentUser" xml:space="preserve">
    <value>Utilisateur actuel</value>
  </data>
  <data name="ServerTime" xml:space="preserve">
    <value>Heure du serveur</value>
  </data>
  <data name="DevelopmentModeMessage" xml:space="preserve">
    <value>L'authentification est désactivée pour un développement plus rapide. Dans les environnements de test/production, l'authentification Microsoft sera requise pour accéder à ce panneau d'administration.</value>
  </data>
  <data name="AuthenticationRequired" xml:space="preserve">
    <value>Authentification requise</value>
  </data>
  <data name="AuthenticationError" xml:space="preserve">
    <value>Vous ne devriez pas pouvoir voir cette page sans authentification. Vérifiez votre configuration d'authentification.</value>
  </data>
  <data name="AuthenticatedAccess" xml:space="preserve">
    <value>Accès authentifié</value>
  </data>
  <data name="AuthenticatedMessage" xml:space="preserve">
    <value>Vous êtes authentifié avec succès via un compte Microsoft.</value>
  </data>
  <data name="BackToAdminDashboard" xml:space="preserve">
    <value>Retour au tableau de bord administrateur</value>
  </data>

  <!-- Members Management -->
  <data name="ManageMembers" xml:space="preserve">
    <value>Gérer les membres</value>
  </data>
  <data name="SearchByNameOrEmail" xml:space="preserve">
    <value>Rechercher par nom ou email...</value>
  </data>
  <data name="Search" xml:space="preserve">
    <value>Rechercher</value>
  </data>
  <data name="Showing" xml:space="preserve">
    <value>Affichage</value>
  </data>
  <data name="Of" xml:space="preserve">
    <value>de</value>
  </data>
  <data name="Members" xml:space="preserve">
    <value>membres</value>
  </data>
  <data name="Matching" xml:space="preserve">
    <value>correspondant à</value>
  </data>
  <data name="Page" xml:space="preserve">
    <value>Page</value>
  </data>
  <data name="RegistrationDate" xml:space="preserve">
    <value>Date d'inscription</value>
  </data>
  <data name="YearsOld" xml:space="preserve">
    <value>ans</value>
  </data>
  <data name="Unknown" xml:space="preserve">
    <value>Inconnu</value>
  </data>
  <data name="ViewDetails" xml:space="preserve">
    <value>Voir les détails</value>
  </data>
  <data name="Edit" xml:space="preserve">
    <value>Modifier</value>
  </data>
  <data name="NoMembersFound" xml:space="preserve">
    <value>Aucun membre trouvé correspondant à</value>
  </data>
  <data name="TryDifferentSearch" xml:space="preserve">
    <value>Essayez un terme de recherche différent ou</value>
  </data>
  <data name="ViewAllMembersLink" xml:space="preserve">
    <value>voir tous les membres</value>
  </data>
  <data name="NoMembersRegisteredYet" xml:space="preserve">
    <value>Aucun membre inscrit encore</value>
  </data>
  <data name="StartByRegistering" xml:space="preserve">
    <value>Commencez par inscrire le premier membre.</value>
  </data>
  <data name="RegisterFirstMember" xml:space="preserve">
    <value>Inscrire le premier membre</value>
  </data>
  <data name="ConfirmDelete" xml:space="preserve">
    <value>Confirmer la suppression</value>
  </data>
  <data name="ConfirmDeleteMessage" xml:space="preserve">
    <value>Êtes-vous sûr de vouloir supprimer le membre</value>
  </data>
  <data name="DeleteWarning" xml:space="preserve">
    <value>Cette action ne peut pas être annulée et supprimera également toutes les informations associées du parent et du contact d'urgence.</value>
  </data>
  <data name="Cancel" xml:space="preserve">
    <value>Annuler</value>
  </data>
  <data name="DeleteMember" xml:space="preserve">
    <value>Supprimer le membre</value>
  </data>

  <!-- Member Details -->
  <data name="MemberDetails" xml:space="preserve">
    <value>Détails du membre</value>
  </data>
  <data name="EditMember" xml:space="preserve">
    <value>Modifier le membre</value>
  </data>
  <data name="BackToMembersList" xml:space="preserve">
    <value>Retour à la liste des membres</value>
  </data>
  <data name="PersonalInformation" xml:space="preserve">
    <value>Informations personnelles</value>
  </data>
  <data name="MemberID" xml:space="preserve">
    <value>ID du membre</value>
  </data>
  <data name="NotSpecified" xml:space="preserve">
    <value>Non spécifié</value>
  </data>
  <data name="QuickStats" xml:space="preserve">
    <value>Statistiques rapides</value>
  </data>
  <data name="ParentsText" xml:space="preserve">
    <value>Parent(s)</value>
  </data>
  <data name="EmergencyContact" xml:space="preserve">
    <value>Contact d'urgence</value>
  </data>
  <data name="AddressInformation" xml:space="preserve">
    <value>Informations d'adresse</value>
  </data>
  <data name="ParentGuardianInformation" xml:space="preserve">
    <value>Informations du parent/tuteur</value>
  </data>
  <data name="Type" xml:space="preserve">
    <value>Type</value>
  </data>
  <data name="Relation" xml:space="preserve">
    <value>Relation</value>
  </data>
  <data name="AuditHistory" xml:space="preserve">
    <value>Historique d'audit</value>
  </data>
  <data name="Created" xml:space="preserve">
    <value>Créé</value>
  </data>
  <data name="By" xml:space="preserve">
    <value>par</value>
  </data>
  <data name="LastModified" xml:space="preserve">
    <value>Dernière modification</value>
  </data>
  <data name="NeverModified" xml:space="preserve">
    <value>Jamais modifié</value>
  </data>
  <data name="TotalChanges" xml:space="preserve">
    <value>Total des modifications</value>
  </data>
  <data name="ChangeHistory" xml:space="preserve">
    <value>Historique des modifications</value>
  </data>
  <data name="NoAuditHistoryAvailable" xml:space="preserve">
    <value>Aucun historique d'audit disponible.</value>
  </data>
  <data name="IP" xml:space="preserve">
    <value>IP</value>
  </data>
  <data name="Active" xml:space="preserve">
    <value>Actif</value>
  </data>
  <data name="Inactive" xml:space="preserve">
    <value>Inactif</value>
  </data>
  <data name="Status" xml:space="preserve">
    <value>Statut</value>
  </data>

  <!-- Setup Master Admin -->
  <data name="SetupMasterAdmin" xml:space="preserve">
    <value>Configurer l'administrateur maître</value>
  </data>
  <data name="FirstTimeSetup" xml:space="preserve">
    <value>Configuration initiale :</value>
  </data>
  <data name="NoAdminsExist" xml:space="preserve">
    <value>Aucun administrateur n'existe encore. Vous deviendrez l'administrateur maître.</value>
  </data>
  <data name="YouAreLoggedInAs" xml:space="preserve">
    <value>Vous êtes connecté en tant que :</value>
  </data>
  <data name="Important" xml:space="preserve">
    <value>Important :</value>
  </data>
  <data name="MasterAdminCannotBeRemoved" xml:space="preserve">
    <value>En tant qu'administrateur maître, vous ne pouvez pas être supprimé du système. Vous pourrez ajouter et supprimer d'autres administrateurs.</value>
  </data>
  <data name="SetupMasterAdminAccount" xml:space="preserve">
    <value>Configurer le compte d'administrateur maître</value>
  </data>

  <!-- Teams, Calendar, Rankings, Statistics coming soon messages -->
  <data name="ManageEventsAndSubscriptions" xml:space="preserve">
    <value>Gérer les événements et les inscriptions des membres</value>
  </data>
  <data name="ManageTeamsPlayersCoaches" xml:space="preserve">
    <value>Gérer les équipes, les joueurs et les entraîneurs</value>
  </data>
  <data name="ViewTeamStandings" xml:space="preserve">
    <value>Voir les classements et les standings des équipes</value>
  </data>
  <data name="ViewPlayerTeamStats" xml:space="preserve">
    <value>Voir les statistiques de performance des joueurs et des équipes</value>
  </data>

  <!-- Confirmation messages -->
  <data name="AreYouSureRemoveAdmin" xml:space="preserve">
    <value>Êtes-vous sûr de vouloir supprimer cet administrateur ?</value>
  </data>

  <!-- Controller messages -->
  <data name="MemberNotFound" xml:space="preserve">
    <value>Membre non trouvé.</value>
  </data>
  <data name="MemberDeletedSuccessfully" xml:space="preserve">
    <value>Le membre {0} {1} a été supprimé avec succès.</value>
  </data>
  <data name="ErrorDeletingMember" xml:space="preserve">
    <value>Une erreur s'est produite lors de la suppression du membre.</value>
  </data>
  <data name="MasterAdminCreatedSuccessfully" xml:space="preserve">
    <value>Compte d'administrateur maître créé avec succès !</value>
  </data>
  <data name="OnlyMasterAdminsCanCreate" xml:space="preserve">
    <value>Seuls les administrateurs maîtres peuvent créer d'autres administrateurs maîtres.</value>
  </data>
  <data name="EmailAndNameRequired" xml:space="preserve">
    <value>Le courriel et le nom sont requis.</value>
  </data>
  <data name="AdminAlreadyExists" xml:space="preserve">
    <value>L'administrateur existe déjà.</value>
  </data>
  <data name="AdminAddedSuccessfully" xml:space="preserve">
    <value>{0} {1} ajouté avec succès !</value>
  </data>
  <data name="AdminNotFound" xml:space="preserve">
    <value>Administrateur non trouvé.</value>
  </data>
  <data name="CannotRemoveMasterAdmin" xml:space="preserve">
    <value>Impossible de supprimer l'administrateur maître.</value>
  </data>
  <data name="AdminRemovedSuccessfully" xml:space="preserve">
    <value>L'administrateur {0} a été supprimé avec succès !</value>
  </data>
  
  <!-- Disable/Enable Actions -->
  <data name="Disable" xml:space="preserve">
    <value>Désactiver</value>
  </data>
  <data name="Enable" xml:space="preserve">
    <value>Activer</value>
  </data>
  <data name="DisableMember" xml:space="preserve">
    <value>Désactiver le membre</value>
  </data>
  <data name="EnableMember" xml:space="preserve">
    <value>Activer le membre</value>
  </data>
  <data name="ConfirmDisable" xml:space="preserve">
    <value>Confirmer la désactivation</value>
  </data>
  <data name="ConfirmEnable" xml:space="preserve">
    <value>Confirmer l'activation</value>
  </data>
  <data name="ConfirmDisableMessage" xml:space="preserve">
    <value>Êtes-vous sûr de vouloir désactiver</value>
  </data>
  <data name="ConfirmEnableMessage" xml:space="preserve">
    <value>Êtes-vous sûr de vouloir activer</value>
  </data>
  <data name="DisableWarning" xml:space="preserve">
    <value>Le membre ne pourra plus se connecter ou être affiché dans les listes actives.</value>
  </data>
  <data name="EnableWarning" xml:space="preserve">
    <value>Le membre pourra à nouveau se connecter et être affiché dans les listes actives.</value>
  </data>
  <data name="MemberDisabledSuccessfully" xml:space="preserve">
    <value>Le membre {0} {1} a été désactivé avec succès.</value>
  </data>
  <data name="MemberEnabledSuccessfully" xml:space="preserve">
    <value>Le membre {0} {1} a été activé avec succès.</value>
  </data>
  <data name="ErrorDisablingMember" xml:space="preserve">
    <value>Erreur lors de la désactivation du membre.</value>
  </data>
  <data name="ErrorEnablingMember" xml:space="preserve">
    <value>Erreur lors de l'activation du membre.</value>
  </data>
  <data name="EventCalendar" xml:space="preserve">
    <value>Calendrier des événements</value>
  </data>
  <data name="AddEvent" xml:space="preserve">
    <value>Ajouter un événement</value>
  </data>
  <data name="AllEvents" xml:space="preserve">
    <value>Tous les événements</value>
  </data>
  <data name="TotalEvents" xml:space="preserve">
    <value>Total événements</value>
  </data>
  <data name="PublishedEvents" xml:space="preserve">
    <value>Événements publiés</value>
  </data>
  <data name="UpcomingEvents" xml:space="preserve">
    <value>Événements à venir</value>
  </data>
  <data name="NoUpcomingEvents" xml:space="preserve">
    <value>Aucun événement à venir</value>
  </data>
  <data name="EventCategories" xml:space="preserve">
    <value>Catégories d'événements</value>
  </data>
  <data name="EventDetails" xml:space="preserve">
    <value>Détails de l'événement</value>
  </data>
  <data name="ViewEventDetails" xml:space="preserve">
    <value>Voir les détails de l'événement</value>
  </data>
  <data name="StartDate" xml:space="preserve">
    <value>Date de début</value>
  </data>
  <data name="EndDate" xml:space="preserve">
    <value>Date de fin</value>
  </data>
  <data name="MaxParticipants" xml:space="preserve">
    <value>Participants max</value>
  </data>
  <data name="MaxParticipantsHelp" xml:space="preserve">
    <value>Laissez vide si aucune limite</value>
  </data>
  <data name="UnlimitedParticipants" xml:space="preserve">
    <value>Participants illimités</value>
  </data>
  <data name="AllDayEvent" xml:space="preserve">
    <value>Événement toute la journée</value>
  </data>
  <data name="RequiresRegistration" xml:space="preserve">
    <value>Inscription requise</value>
  </data>
  <data name="ContactPerson" xml:space="preserve">
    <value>Personne-contact</value>
  </data>
  <data name="ContactEmail" xml:space="preserve">
    <value>Courriel de contact</value>
  </data>
  <data name="ContactPhone" xml:space="preserve">
    <value>Téléphone de contact</value>
  </data>
  <data name="OrganizerNotes" xml:space="preserve">
    <value>Notes de l'organisateur</value>
  </data>
  <data name="PublishEvent" xml:space="preserve">
    <value>Publier l'événement</value>
  </data>
  <data name="ConfirmDeleteEvent" xml:space="preserve">
    <value>Êtes-vous sûr de vouloir supprimer cet événement ?</value>
  </data>
  <data name="EventCategory_Practice" xml:space="preserve">
    <value>Pratique</value>
  </data>
  <data name="EventCategory_Practice_Desc" xml:space="preserve">
    <value>Séances d'entraînement et de pratique</value>
  </data>
  <data name="EventCategory_Game" xml:space="preserve">
    <value>Match</value>
  </data>
  <data name="EventCategory_Game_Desc" xml:space="preserve">
    <value>Séances de match et compétitions</value>
  </data>
  <data name="EventCategory_Tournament" xml:space="preserve">
    <value>Tournoi</value>
  </data>
  <data name="EventCategory_Tournament_Desc" xml:space="preserve">
    <value>Tournois et championnats</value>
  </data>
  <data name="EventCategory_Training" xml:space="preserve">
    <value>Formation</value>
  </data>
  <data name="EventCategory_Training_Desc" xml:space="preserve">
    <value>Formations et ateliers</value>
  </data>
  <data name="EventCategory_Meeting" xml:space="preserve">
    <value>Réunion</value>
  </data>
  <data name="EventCategory_Meeting_Desc" xml:space="preserve">
    <value>Réunions et assemblées</value>
  </data>
  <data name="EventCategory_Social" xml:space="preserve">
    <value>Social</value>
  </data>
  <data name="EventCategory_Social_Desc" xml:space="preserve">
    <value>Événements sociaux et célébrations</value>
  </data>
  <data name="EventCategory_Fundraiser" xml:space="preserve">
    <value>Collecte de fonds</value>
  </data>
  <data name="EventCategory_Fundraiser_Desc" xml:space="preserve">
    <value>Événements de collecte de fonds</value>
  </data>
  <data name="EventCategory_Other" xml:space="preserve">
    <value>Autre</value>
  </data>
  <data name="EventCategory_Other_Desc" xml:space="preserve">
    <value>Autres événements</value>
  </data>
  <data name="EventCategory_Tentative" xml:space="preserve">
    <value>Tentatif</value>
  </data>
  <data name="EventCategory_Tentative_Desc" xml:space="preserve">
    <value>Événements tentatives et à confirmer</value>
  </data>
  <data name="EventCategory_FirstShift" xml:space="preserve">
    <value>First Shift</value>
  </data>
  <data name="EventCategory_FirstShift_Desc" xml:space="preserve">
    <value>Événements First Shift</value>
  </data>
  <data name="EventCategory_Camp" xml:space="preserve">
    <value>Camp</value>
  </data>
  <data name="EventCategory_Camp_Desc" xml:space="preserve">
    <value>Camps d'entraînement et de développement</value>
  </data>
  <data name="EventCategory_Serie" xml:space="preserve">
    <value>Série</value>
  </data>
  <data name="EventCategory_Serie_Desc" xml:space="preserve">
    <value>Série de matchs ou d'événements</value>
  </data>
  <data name="EventCategory_Tentatif" xml:space="preserve">
    <value>Tentatif</value>
  </data>
  <data name="EventCategory_Tentatif_Desc" xml:space="preserve">
    <value>Événements tentatives et à confirmer</value>
  </data>
  <data name="EventCategory_PratiqueFirstShift" xml:space="preserve">
    <value>Pratique First Shift</value>
  </data>
  <data name="EventCategory_PratiqueFirstShift_Desc" xml:space="preserve">
    <value>Sessions de pratique dédiées au programme First Shift</value>
  </data>
  
  <!-- Public Calendar Keys -->
  <data name="ViewCalendar" xml:space="preserve">
    <value>Voir le calendrier</value>
  </data>
  <data name="ViewPublicEventsAndSchedule" xml:space="preserve">
    <value>Consultez les événements publics et les horaires.</value>
  </data>
  <data name="BackToHome" xml:space="preserve">
    <value>Retour à l'accueil</value>
  </data>
  <data name="LoginRequired" xml:space="preserve">
    <value>Connexion requise</value>
  </data>
  <data name="MembershipRequired" xml:space="preserve">
    <value>Adhésion requise</value>
  </data>
  <data name="LoginRequiredMessage" xml:space="preserve">
    <value>Pour voir les détails des événements et vous y inscrire, vous devez être connecté en tant que membre.</value>
  </data>
  <data name="MemberBenefitsText" xml:space="preserve">
    <value>Les membres peuvent s'inscrire aux événements, gérer leur profil et accéder aux fonctionnalités exclusives.</value>
  </data>
  <data name="ErrorLoadingCalendar" xml:space="preserve">
    <value>Erreur lors du chargement du calendrier</value>
  </data>

  <!-- Import/Export Calendar -->
  <data name="ImportExport" xml:space="preserve">
    <value>Importer/Exporter</value>
  </data>
  <data name="ImportEvents" xml:space="preserve">
    <value>Importer des événements</value>
  </data>
  <data name="ExportEvents" xml:space="preserve">
    <value>Exporter des événements</value>
  </data>
  <data name="SelectFile" xml:space="preserve">
    <value>Sélectionner un fichier</value>
  </data>
  <data name="ImportFileHelp" xml:space="preserve">
    <value>Sélectionnez un fichier CSV ou Excel contenant les événements à importer</value>
  </data>
  <data name="ImportInstructions" xml:space="preserve">
    <value>Instructions d'importation</value>
  </data>
  <data name="ImportSupportedFormats" xml:space="preserve">
    <value>Formats supportés</value>
  </data>
  <data name="ImportExpectedColumns" xml:space="preserve">
    <value>Colonnes attendues</value>
  </data>
  <data name="ExportInformation" xml:space="preserve">
    <value>Informations d'exportation</value>
  </data>
  <data name="ExportDescription" xml:space="preserve">
    <value>Exportez les événements du mois sélectionné dans le format de votre choix</value>
  </data>
  <data name="FileFormat" xml:space="preserve">
    <value>Format de fichier</value>
  </data>
  <data name="Year" xml:space="preserve">
    <value>Année</value>
  </data>
  <data name="Month" xml:space="preserve">
    <value>Mois</value>
  </data>
  <data name="PleaseSelectFile" xml:space="preserve">
    <value>Veuillez sélectionner un fichier</value>
  </data>
  <data name="FileTooLarge" xml:space="preserve">
    <value>Le fichier est trop volumineux (max 10 MB)</value>
  </data>
  <data name="UnsupportedFileFormat" xml:space="preserve">
    <value>Format de fichier non supporté</value>
  </data>
  <data name="EventsImportedSuccessfully" xml:space="preserve">
    <value>{0} événements importés avec succès</value>
  </data>
  <data name="ImportWarnings" xml:space="preserve">
    <value>avec {0} avertissements</value>
  </data>
  <data name="NoEventsToImport" xml:space="preserve">
    <value>Aucun événement à importer</value>
  </data>
  <data name="ImportError" xml:space="preserve">
    <value>Erreur d'importation</value>
  </data>
  <data name="ExportError" xml:space="preserve">
    <value>Erreur d'exportation</value>
  </data>
  
  <!-- Audit Log Localization -->
  <data name="NewMember" xml:space="preserve">
    <value>Nouveau membre</value>
  </data>
  <data name="Event" xml:space="preserve">
    <value>Événement</value>
  </data>
  <data name="edited" xml:space="preserve">
    <value>modifié</value>
  </data>
  <data name="deleted" xml:space="preserve">
    <value>supprimé</value>
  </data>
  <data name="Start" xml:space="preserve">
    <value>Début</value>
  </data>
  <data name="End" xml:space="preserve">
    <value>Fin</value>
  </data>
  
  <!-- Duplicate Detection Messages -->
  <data name="DuplicateEmailMessage" xml:space="preserve">
    <value>Cette adresse courriel est déjà utilisée par un membre existant.</value>
  </data>
  <data name="DuplicatePartialMessage" xml:space="preserve">
    <value>Un membre avec ce nom et cette date de naissance existe déjà avec l'adresse courriel {0}. Est-ce votre adresse courriel?</value>
  </data>
  <data name="DuplicateOkButton" xml:space="preserve">
    <value>OK</value>
  </data>
  <data name="DuplicateModifyButton" xml:space="preserve">
    <value>Modifier le membre</value>
  </data>
  <data name="DuplicateYesButton" xml:space="preserve">
    <value>Oui</value>
  </data>
  <data name="DuplicateNoButton" xml:space="preserve">
    <value>Non</value>
  </data>
  <data name="Attention" xml:space="preserve">
    <value>Attention</value>
  </data>
  <data name="DuplicateEmailUpdateError" xml:space="preserve">
    <value>Cette adresse courriel est déjà utilisée par un autre membre.</value>
  </data>
  <data name="HQc_Id" xml:space="preserve">
    <value>ID Hockey Québec</value>
  </data>
  <data name="HQc_IdPlaceholder" xml:space="preserve">
    <value>ID Hockey Québec (optionnel)</value>
  </data>
  <data name="DuplicateHQcIdUpdateError" xml:space="preserve">
    <value>Cet ID Hockey Québec est déjà utilisé par un autre membre.</value>
  </data>
  <data name="AdminOnly" xml:space="preserve">
    <value>Champ administrateur seulement</value>
  </data>
  <data name="AccountDeactivated" xml:space="preserve">
    <value>Votre compte a été désactivé. Veuillez contacter l'administration.</value>
  </data>
  <data name="DeactivatedAccountLogin" xml:space="preserve">
    <value>Ce compte est désactivé et ne peut pas se connecter.</value>
  </data>

  <!-- Export and Filter UI -->
  <data name="Export" xml:space="preserve">
    <value>Exporter</value>
  </data>
  <data name="ExportMembers" xml:space="preserve">
    <value>Exporter les membres</value>
  </data>
  <data name="ExportMembersDescription" xml:space="preserve">
    <value>Choisissez le format d'exportation pour les membres actuellement affichés.</value>
  </data>
  <data name="SelectExportFormat" xml:space="preserve">
    <value>Sélectionnez le format d'exportation</value>
  </data>
  <data name="ExportWillIncludeAllResults" xml:space="preserve">
    <value>L'exportation inclura tous les résultats correspondant aux critères de recherche et de filtrage actuels :</value>
  </data>
  <data name="SelectFilter" xml:space="preserve">
    <value>Sélectionner un filtre</value>
  </data>
  <data name="SelectOption" xml:space="preserve">
    <value>Sélectionner une option</value>
  </data>
  <data name="TypeToSearchCities" xml:space="preserve">
    <value>Tapez pour rechercher des villes</value>
  </data>
  <data name="Apply" xml:space="preserve">
    <value>Appliquer</value>
  </data>
  <data name="ClearFilters" xml:space="preserve">
    <value>Effacer les filtres</value>
  </data>
  <data name="ActiveFilters" xml:space="preserve">
    <value>Filtres actifs</value>
  </data>
  <data name="RemoveFilter" xml:space="preserve">
    <value>Supprimer</value>
  </data>
  <data name="Loading" xml:space="preserve">
    <value>Chargement...</value>
  </data>
  <data name="NoCitiesFound" xml:space="preserve">
    <value>Aucune ville trouvée</value>
  </data>
  <data name="InvalidExportParameters" xml:space="preserve">
    <value>Paramètres d'exportation invalides</value>
  </data>
  <data name="ErrorExportingMembers" xml:space="preserve">
    <value>Erreur lors de l'exportation des membres</value>
  </data>

  <!-- Empty State Messages -->
  <data name="EmptyState_NoMembers" xml:space="preserve">
    <value>Aucun membre trouvé</value>
  </data>
  <data name="EmptyState_NoEvents" xml:space="preserve">
    <value>Aucun événement trouvé</value>
  </data>
  <data name="EmptyState_NoAdminData" xml:space="preserve">
    <value>Aucune donnée administrative trouvée</value>
  </data>
  <data name="EmptyState_NoLogs" xml:space="preserve">
    <value>Aucun journal trouvé</value>
  </data>
  <data name="EmptyState_NoExports" xml:space="preserve">
    <value>Aucune exportation trouvée</value>
  </data>
  <data name="EmptyState_NoSavedSearches" xml:space="preserve">
    <value>Aucune recherche sauvegardée trouvée</value>
  </data>
  <data name="EmptyState_NoData" xml:space="preserve">
    <value>Aucune donnée trouvée</value>
  </data>

  <!-- Empty State Titles -->
  <data name="EmptyState_NoMembersTitle" xml:space="preserve">
    <value>Aucun membre</value>
  </data>
  <data name="EmptyState_NoEventsTitle" xml:space="preserve">
    <value>Aucun événement</value>
  </data>
  <data name="EmptyState_NoAdminDataTitle" xml:space="preserve">
    <value>Aucune donnée administrative</value>
  </data>
  <data name="EmptyState_NoLogsTitle" xml:space="preserve">
    <value>Aucun journal</value>
  </data>
  <data name="EmptyState_NoExportsTitle" xml:space="preserve">
    <value>Aucune exportation</value>
  </data>
  <data name="EmptyState_NoSavedSearchesTitle" xml:space="preserve">
    <value>Aucune recherche sauvegardée</value>
  </data>
  <data name="EmptyState_NoDataTitle" xml:space="preserve">
    <value>Aucune donnée</value>
  </data>

  <!-- Search Empty State Messages -->
  <data name="EmptyState_SearchNoResults" xml:space="preserve">
    <value>Aucun résultat trouvé</value>
  </data>
  <data name="EmptyState_SearchNoMembers" xml:space="preserve">
    <value>Aucun membre trouvé pour "{0}"</value>
  </data>
  <data name="EmptyState_SearchNoEvents" xml:space="preserve">
    <value>Aucun événement trouvé pour "{0}"</value>
  </data>
  <data name="EmptyState_SearchNoAdminData" xml:space="preserve">
    <value>Aucune donnée administrative trouvée pour "{0}"</value>
  </data>
  <data name="EmptyState_SearchNoLogs" xml:space="preserve">
    <value>Aucun journal trouvé pour "{0}"</value>
  </data>
  <data name="EmptyState_SearchNoExports" xml:space="preserve">
    <value>Aucune exportation trouvée pour "{0}"</value>
  </data>
  <data name="EmptyState_SearchNoSavedSearches" xml:space="preserve">
    <value>Aucune recherche sauvegardée trouvée pour "{0}"</value>
  </data>
  <data name="EmptyState_SearchNoData" xml:space="preserve">
    <value>Aucune donnée trouvée pour "{0}"</value>
  </data>

  <!-- Search Suggestions -->
  <data name="EmptyState_SearchSuggestions" xml:space="preserve">
    <value>Essayez : {0}</value>
  </data>

  <!-- Action Messages -->
  <data name="EmptyState_AddFirstMember" xml:space="preserve">
    <value>Ajouter le premier membre</value>
  </data>
  <data name="EmptyState_CreateFirstEvent" xml:space="preserve">
    <value>Créer le premier événement</value>
  </data>
  <data name="EmptyState_ConfigureSystem" xml:space="preserve">
    <value>Configurer le système</value>
  </data>
  <data name="EmptyState_ViewAllLogs" xml:space="preserve">
    <value>Voir tous les journaux</value>
  </data>
  <data name="EmptyState_CreateExport" xml:space="preserve">
    <value>Créer une exportation</value>
  </data>
  <data name="EmptyState_CreateSearch" xml:space="preserve">
    <value>Créer une recherche</value>
  </data>
  <data name="EmptyState_GetStarted" xml:space="preserve">
    <value>Commencer</value>
  </data>
  <data name="EmptyState_SearchTryDifferent" xml:space="preserve">
    <value>Essayez une recherche différente ou modifiez vos filtres</value>
  </data>
  <data name="EmptyState_ErrorTitle" xml:space="preserve">
    <value>Une erreur est survenue</value>
  </data>
  <data name="EmptyState_RetryAction" xml:space="preserve">
    <value>Réessayer</value>
  </data>
  
  <!-- Context-specific suggestions -->
  <data name="EmptyState_members_filterSuggestion" xml:space="preserve">
    <value>Essayez de supprimer certains filtres pour voir plus de résultats</value>
  </data>
  <data name="EmptyState_members_newSuggestion" xml:space="preserve">
    <value>Vous pouvez ajouter un nouveau membre en utilisant le bouton ci-dessous</value>
  </data>
  <data name="EmptyState_events_filterSuggestion" xml:space="preserve">
    <value>Essayez de modifier la plage de dates ou de supprimer des filtres</value>
  </data>
  <data name="EmptyState_events_newSuggestion" xml:space="preserve">
    <value>Vous pouvez créer un nouvel événement en utilisant le bouton ci-dessous</value>
  </data>
  
  <!-- Error-specific messages -->
  <data name="EmptyState_ErrorDatabaseMembers" xml:space="preserve">
    <value>Impossible de charger les membres en raison d'une erreur de base de données</value>
  </data>
  <data name="EmptyState_ErrorNetworkMembers" xml:space="preserve">
    <value>Impossible de charger les membres en raison d'un problème de réseau</value>
  </data>
  <data name="EmptyState_ErrorPermissionMembers" xml:space="preserve">
    <value>Vous n'avez pas les permissions nécessaires pour voir ces membres</value>
  </data>
  <data name="EmptyState_ErrorGeneralMembers" xml:space="preserve">
    <value>Une erreur est survenue lors du chargement des membres</value>
  </data>
  <data name="EmptyState_ErrorDatabaseEvents" xml:space="preserve">
    <value>Impossible de charger les événements en raison d'une erreur de base de données</value>
  </data>
  <data name="EmptyState_ErrorNetworkEvents" xml:space="preserve">
    <value>Impossible de charger les événements en raison d'un problème de réseau</value>
  </data>
  <data name="EmptyState_ErrorPermissionEvents" xml:space="preserve">
    <value>Vous n'avez pas les permissions nécessaires pour voir ces événements</value>
  </data>
  <data name="EmptyState_ErrorGeneralEvents" xml:space="preserve">
    <value>Une erreur est survenue lors du chargement des événements</value>
  </data>
  <data name="EmptyState_ErrorDatabaseData" xml:space="preserve">
    <value>Impossible de charger les données en raison d'une erreur de base de données</value>
  </data>
  <data name="EmptyState_ErrorNetworkData" xml:space="preserve">
    <value>Impossible de charger les données en raison d'un problème de réseau</value>
  </data>
  <data name="EmptyState_ErrorPermissionData" xml:space="preserve">
    <value>Vous n'avez pas les permissions nécessaires pour voir ces données</value>
  </data>
  <data name="EmptyState_ErrorGeneralData" xml:space="preserve">
    <value>Une erreur est survenue lors du chargement des données</value>
  </data>
  <!-- Environment Indicator Messages -->
  <data name="Environment_Development" xml:space="preserve">
    <value>Environnement de Développement</value>
  </data>
  <data name="Environment_Test" xml:space="preserve">
    <value>Environnement de Test</value>
  </data>
  <data name="Environment_SqlServer" xml:space="preserve">
    <value>Base de données SQL Server</value>
  </data>
  <data name="Environment_DevToolsAvailable" xml:space="preserve">
    <value>Outils de développement disponibles</value>
  </data>
  <!-- Development Tools Messages -->
  <data name="DevTools_Title" xml:space="preserve">
    <value>Outils de Développement</value>
  </data>
  <data name="DevTools_Registration" xml:space="preserve">
    <value>Inscription</value>
  </data>
  <data name="DevTools_FillJunior" xml:space="preserve">
    <value>Remplir Junior</value>
  </data>
  <data name="DevTools_FillAdult" xml:space="preserve">
    <value>Remplir Adulte</value>
  </data>
  <data name="DevTools_FillCoach" xml:space="preserve">
    <value>Remplir Entraîneur</value>
  </data>
  <data name="DevTools_Admin" xml:space="preserve">
    <value>Administration</value>
  </data>
  <data name="DevTools_GenerateTestData" xml:space="preserve">
    <value>Générer des données de test</value>
  </data>
  <data name="DevTools_ClearTestData" xml:space="preserve">
    <value>Effacer les données de test</value>
  </data>
  <data name="DevTools_Debug" xml:space="preserve">
    <value>Débogage</value>
  </data>
  <data name="DevTools_ShowDebugInfo" xml:space="preserve">
    <value>Afficher les infos de débogage</value>
  </data>
  <data name="DevTools_ShowEnvInfo" xml:space="preserve">
    <value>Infos environnement</value>
  </data>
  <data name="DevTools_ShowUserInfo" xml:space="preserve">
    <value>Infos utilisateur</value>
  </data>
  <data name="DevTools_Performance" xml:space="preserve">
    <value>Performance</value>
  </data>
  <data name="DevTools_MeasurePageLoad" xml:space="preserve">
    <value>Mesurer le chargement</value>
  </data>
  <data name="DevTools_ClearCache" xml:space="preserve">
    <value>Vider le cache</value>
  </data>
  <data name="DevTools_Api" xml:space="preserve">
    <value>API</value>
  </data>
  <data name="DevTools_TestApi" xml:space="preserve">
    <value>Tester l'API</value>
  </data>
  <data name="DevTools_ApiDocs" xml:space="preserve">
    <value>Documentation API</value>
  </data>
  <data name="DevTools_Environment" xml:space="preserve">
    <value>Environnement</value>
  </data>
  <data name="DevTools_Theme" xml:space="preserve">
    <value>Thème</value>
  </data>
  <data name="DevTools_Auth" xml:space="preserve">
    <value>Authentification</value>
  </data>
  <data name="DevTools_Enabled" xml:space="preserve">
    <value>Activé</value>
  </data>
  <data name="DevTools_Disabled" xml:space="preserve">
    <value>Désactivé</value>
  </data>
  <data name="DevTools_DebugInformation" xml:space="preserve">
    <value>Informations de Débogage</value>
  </data>
  <data name="DevTools_FormFilled" xml:space="preserve">
    <value>Formulaire rempli avec succès</value>
  </data>
  <data name="DevTools_ConfirmGenerateData" xml:space="preserve">
    <value>Êtes-vous sûr de vouloir générer des données de test ?</value>
  </data>
  <data name="DevTools_TestDataGenerated" xml:space="preserve">
    <value>Données de test générées</value>
  </data>
  <data name="DevTools_ConfirmClearData" xml:space="preserve">
    <value>Êtes-vous sûr de vouloir effacer les données de test ?</value>
  </data>
  <data name="DevTools_TestDataCleared" xml:space="preserve">
    <value>Données de test effacées</value>
  </data>
  <data name="DevTools_PageInfo" xml:space="preserve">
    <value>Informations de la page</value>
  </data>
  <data name="DevTools_UserAgent" xml:space="preserve">
    <value>Agent utilisateur</value>
  </data>
  <data name="DevTools_Viewport" xml:space="preserve">
    <value>Viewport</value>
  </data>
  <data name="DevTools_LoadTime" xml:space="preserve">
    <value>Temps de chargement</value>
  </data>
  <data name="DevTools_Browser" xml:space="preserve">
    <value>Navigateur</value>
  </data>
  <data name="DevTools_Language" xml:space="preserve">
    <value>Langue</value>
  </data>
  <data name="DevTools_CookiesEnabled" xml:space="preserve">
    <value>Cookies activés</value>
  </data>
  <data name="DevTools_OnLine" xml:space="preserve">
    <value>En ligne</value>
  </data>
  <data name="DevTools_CheckConsole" xml:space="preserve">
    <value>Vérifiez la console pour les détails</value>
  </data>
  <data name="DevTools_PageLoadTime" xml:space="preserve">
    <value>Temps de chargement de la page</value>
  </data>
  <data name="DevTools_CacheCleared" xml:space="preserve">
    <value>Cache vidé avec succès</value>
  </data>
  <data name="DevTools_CacheNotSupported" xml:space="preserve">
    <value>Cache non pris en charge par ce navigateur</value>
  </data>
  <data name="DevTools_TestingApi" xml:space="preserve">
    <value>Test de l'API en cours...</value>
  </data>
  <!-- Layout Messages -->
  <data name="PoweredBy" xml:space="preserve">
    <value>Propulsé par</value>
  </data>
  <!-- Validation Messages for Custom ValidationResult -->
  <data name="ValidationParentRequired" xml:space="preserve">
    <value>Au moins un parent est requis pour l'inscription Junior</value>
  </data>
  <data name="ValidationEmergencyContactRequired" xml:space="preserve">
    <value>Un contact d'urgence est requis</value>
  </data>
  <!-- Client-side validation messages -->
  <data name="ClientValidationEmailInvalid" xml:space="preserve">
    <value>Veuillez entrer une adresse e-mail valide (ex: <EMAIL>)</value>
  </data>
  <data name="ClientValidationPhoneInvalid" xml:space="preserve">
    <value>Veuillez entrer un numéro de téléphone à 10 chiffres</value>
  </data>
  <data name="ClientValidationPostalCodeInvalid" xml:space="preserve">
    <value>Veuillez entrer un code postal canadien valide (ex: A1A 1A1)</value>
  </data>
  <data name="ClientValidationDateInvalid" xml:space="preserve">
    <value>Veuillez entrer une date valide au format AAAA-MM-JJ</value>
  </data>
  <data name="ClientValidationDateFuture" xml:space="preserve">
    <value>La date de naissance ne peut pas être dans le futur</value>
  </data>
  <data name="ClientValidationDateGeneral" xml:space="preserve">
    <value>Veuillez entrer une date de naissance valide</value>
  </data>
  <data name="ClientValidationNameTooShort" xml:space="preserve">
    <value>Le nom doit contenir au moins 2 caractères</value>
  </data>
  <data name="ClientValidationAddressTooShort" xml:space="preserve">
    <value>L'adresse doit contenir au moins 5 caractères</value>
  </data>
  <data name="ClientValidationCityTooShort" xml:space="preserve">
    <value>La ville doit contenir au moins 2 caractères</value>
  </data>
  <data name="ClientValidationRequired" xml:space="preserve">
    <value>Ce champ est requis</value>
  </data>
  <!-- jQuery validation default messages -->
  <data name="JQueryValidationRequired" xml:space="preserve">
    <value>Ce champ est requis.</value>
  </data>
  <data name="JQueryValidationEmail" xml:space="preserve">
    <value>Veuillez entrer une adresse e-mail valide.</value>
  </data>
  <data name="JQueryValidationNumber" xml:space="preserve">
    <value>Veuillez entrer un nombre valide.</value>
  </data>
  <data name="JQueryValidationDigits" xml:space="preserve">
    <value>Veuillez entrer seulement des chiffres.</value>
  </data>
  <data name="JQueryValidationMinLength" xml:space="preserve">
    <value>Veuillez entrer au moins {0} caractères.</value>
  </data>
  <data name="JQueryValidationMaxLength" xml:space="preserve">
    <value>Veuillez entrer au plus {0} caractères.</value>
  </data>
  <data name="JQueryValidationRangeLength" xml:space="preserve">
    <value>Veuillez entrer une valeur entre {0} et {1} caractères.</value>
  </data>
  <data name="JQueryValidationMin" xml:space="preserve">
    <value>Veuillez entrer une valeur supérieure ou égale à {0}.</value>
  </data>
  <data name="JQueryValidationMax" xml:space="preserve">
    <value>Veuillez entrer une valeur inférieure ou égale à {0}.</value>
  </data>
  <data name="JQueryValidationRange" xml:space="preserve">
    <value>Veuillez entrer une valeur entre {0} et {1}.</value>
  </data>
  <!-- API Error Messages -->
  <data name="ApiAccessDenied" xml:space="preserve">
    <value>Accès refusé</value>
  </data>
  <data name="ApiFilterTypeRequired" xml:space="preserve">
    <value>Le type de filtre est requis</value>
  </data>
  <data name="ApiErrorRetrievingFilterOptions" xml:space="preserve">
    <value>Erreur lors de la récupération des options de filtre</value>
  </data>
  <data name="ApiInvalidRequestData" xml:space="preserve">
    <value>Données de requête invalides</value>
  </data>
  <data name="ApiUserNotFound" xml:space="preserve">
    <value>Utilisateur non trouvé</value>
  </data>
  <data name="ApiCodeSentEmail" xml:space="preserve">
    <value>Code envoyé par email</value>
  </data>
  <data name="ApiEmailSendingError" xml:space="preserve">
    <value>Erreur d'envoi d'email</value>
  </data>
  <data name="ApiValidationBad" xml:space="preserve">
    <value>Validation échouée</value>
  </data>
  <data name="ApiValidationGood" xml:space="preserve">
    <value>Validation réussie</value>
  </data>
  <data name="ApiUnauthorizedAccess" xml:space="preserve">
    <value>Accès non autorisé</value>
  </data>
  <data name="ApiSessionExpired" xml:space="preserve">
    <value>Session expirée</value>
  </data>
  <data name="ApiFailedToLoadEvents" xml:space="preserve">
    <value>Échec du chargement des événements</value>
  </data>
  <data name="ApiValidationFailed" xml:space="preserve">
    <value>Échec de la validation</value>
  </data>
  <data name="ApiEventDataRequired" xml:space="preserve">
    <value>Les données d'événement sont requises</value>
  </data>
  <data name="ApiEventTitleRequired" xml:space="preserve">
    <value>Le titre de l'événement est requis</value>
  </data>
  <data name="ApiEventCategoryRequired" xml:space="preserve">
    <value>La catégorie d'événement est requise</value>
  </data>
  <!-- JavaScript Error Messages -->
  <data name="JsVerificationCodeMessage" xml:space="preserve">
    <value>Code de vérification: {0}\n\nEntrez ce code dans le champ de vérification ci-dessous.</value>
  </data>
  <data name="JsErrorSendingCode" xml:space="preserve">
    <value>Erreur lors de l'envoi du code de vérification. Consultez la console pour plus de détails.</value>
  </data>
  <data name="JsFormResetConfirm" xml:space="preserve">
    <value>Voulez-vous effacer tous les champs du formulaire?</value>
  </data>
  <!-- System Error Page Messages -->
  <data name="SystemErrorTitle" xml:space="preserve">
    <value>Erreur</value>
  </data>
  <data name="SystemErrorMessage" xml:space="preserve">
    <value>Une erreur s'est produite lors du traitement de votre demande.</value>
  </data>
  <data name="SystemErrorRequestId" xml:space="preserve">
    <value>ID de demande:</value>
  </data>
  <data name="SystemErrorDevelopmentMode" xml:space="preserve">
    <value>Mode Développement</value>
  </data>
  <data name="SystemErrorDevelopmentInfo" xml:space="preserve">
    <value>Le passage à l'environnement de <strong>Développement</strong> affiche des informations détaillées sur l'erreur qui s'est produite.</value>
  </data>
  <data name="SystemErrorDevelopmentWarning" xml:space="preserve">
    <value><strong>L'environnement de développement ne doit pas être activé pour les applications déployées.</strong> Il peut entraîner l'affichage d'informations sensibles d'exceptions aux utilisateurs finaux. Pour le débogage local, activez l'environnement de <strong>Développement</strong> en définissant la variable d'environnement <strong>ASPNETCORE_ENVIRONMENT</strong> sur <strong>Development</strong> et en redémarrant l'application.</value>
  </data>
  <data name="ApiValidationTypeRequired" xml:space="preserve">
    <value>Le type est requis.</value>
  </data>
  <data name="NoUpcomingEventsMessage" xml:space="preserve">
    <value>Il n'y a aucun événement à venir pour le moment. Vérifiez le calendrier principal pour voir tous les événements.</value>
  </data>
  <data name="NoRecentEventsMessage" xml:space="preserve">
    <value>Aucun événement récent n'est disponible pour le moment.</value>
  </data>
  <data name="ExploreAllEvents" xml:space="preserve">
    <value>Explorer tous les événements</value>
  </data>
  <data name="SetupFirstAdmin" xml:space="preserve">
    <value>Configurer le premier administrateur</value>
  </data>
  <data name="Error_ValidationFailed" xml:space="preserve">
    <value>Erreur de validation des données</value>
  </data>
  <data name="Error_Unauthorized" xml:space="preserve">
    <value>Accès non autorisé</value>
  </data>
  <data name="Error_ResourceNotFound" xml:space="preserve">
    <value>Ressource introuvable</value>
  </data>
  <data name="Error_RequestTimeout" xml:space="preserve">
    <value>Délai d'attente de la requête dépassé</value>
  </data>
  <data name="Error_InvalidOperation" xml:space="preserve">
    <value>Opération invalide</value>
  </data>
  <data name="Error_OperationNotSupported" xml:space="preserve">
    <value>Opération non supportée</value>
  </data>
  <data name="Error_DatabaseError" xml:space="preserve">
    <value>Erreur de base de données</value>
  </data>
  <data name="Error_UnexpectedError" xml:space="preserve">
    <value>Une erreur inattendue s'est produite</value>
  </data>
  <data name="PageNotFoundTitle" xml:space="preserve">
    <value>Page introuvable</value>
  </data>
  <data name="PageNotFoundHeading" xml:space="preserve">
    <value>Oups! Page non trouvée</value>
  </data>
  <data name="PageNotFoundMessage" xml:space="preserve">
    <value>La page que vous recherchez n'existe pas ou a été déplacée.</value>
  </data>
  <data name="SystemErrorHeading" xml:space="preserve">
    <value>Erreur système</value>
  </data>
  <data name="SuggestedLinks" xml:space="preserve">
    <value>Liens suggérés</value>
  </data>
  <data name="HomePage" xml:space="preserve">
    <value>Page d'accueil</value>
  </data>
  <data name="GoBack" xml:space="preserve">
    <value>Retour</value>
  </data>
  <data name="MemberRegistration" xml:space="preserve">
    <value>Inscription des membres</value>
  </data>
  <data name="MemberLogin" xml:space="preserve">
    <value>Connexion des membres</value>
  </data>
  
  <!-- Event Subscription Page -->
  <data name="SubscribeToEvents" xml:space="preserve">
    <value>S'inscrire aux événements</value>
  </data>
  <data name="BrowseAndSubscribeToUpcomingEvents" xml:space="preserve">
    <value>Parcourez et inscrivez-vous aux événements à venir</value>
  </data>
  <data name="Subscribe" xml:space="preserve">
    <value>S'inscrire</value>
  </data>
  <data name="Registered" xml:space="preserve">
    <value>Inscrit</value>
  </data>
  <data name="Full" xml:space="preserve">
    <value>Complet</value>
  </data>
  <data name="SpotsAvailable" xml:space="preserve">
    <value>places disponibles</value>
  </data>
  <data name="RegistrationDeadline" xml:space="preserve">
    <value>Date limite d'inscription</value>
  </data>
  <data name="NoRegistrationRequired" xml:space="preserve">
    <value>Aucune inscription requise</value>
  </data>
  <data name="RegistrationClosed" xml:space="preserve">
    <value>Inscription fermée</value>
  </data>
  <data name="CheckBackLaterForNewEvents" xml:space="preserve">
    <value>Revenez plus tard pour voir les nouveaux événements</value>
  </data>
  <data name="EventSubscriptionSuccess" xml:space="preserve">
    <value>Vous avez été inscrit avec succès à l'événement!</value>
  </data>
  <data name="EventSubscriptionError" xml:space="preserve">
    <value>Une erreur s'est produite lors de l'inscription à l'événement.</value>
  </data>
  
  <!-- Enhanced Form Validation Messages -->
  <data name="SelectGender" xml:space="preserve">
    <value>Sélectionner le genre</value>
  </data>
  <data name="SelectPhoneType" xml:space="preserve">
    <value>Sélectionner le type de téléphone</value>
  </data>
  <data name="SelectRegistrationType" xml:space="preserve">
    <value>Sélectionner le type d'inscription</value>
  </data>
  <data name="ParentInfoRequired" xml:space="preserve">
    <value>Les informations du parent/tuteur sont requises pour les membres de moins de 18 ans.</value>
  </data>
  <data name="EmergencyContactOptional" xml:space="preserve">
    <value>Les informations de contact d'urgence sont optionnelles mais recommandées.</value>
  </data>
  <data name="MemberRegistrationSuccess" xml:space="preserve">
    <value>Inscription réussie pour {0} {1}!</value>
  </data>
  <data name="RegistrationSuccess" xml:space="preserve">
    <value>Inscription Réussie</value>
  </data>
  <data name="RegistrationSuccessTitle" xml:space="preserve">
    <value>Bienvenue chez Parahockey!</value>
  </data>
  <data name="RegistrationSuccessMessage" xml:space="preserve">
    <value>Merci {0} {1}, votre inscription a été complétée avec succès.</value>
  </data>
  <data name="NextStepEmail" xml:space="preserve">
    <value>Vérifiez votre courriel pour un message de bienvenue et des informations importantes</value>
  </data>
  <data name="NextStepEvents" xml:space="preserve">
    <value>Parcourez les événements à venir et inscrivez-vous aux activités</value>
  </data>
  <data name="NextStepProfile" xml:space="preserve">
    <value>Complétez votre profil avec des informations supplémentaires</value>
  </data>
  <data name="ViewEvents" xml:space="preserve">
    <value>Voir les événements</value>
  </data>
  <data name="AutoRedirectConfirm" xml:space="preserve">
    <value>Souhaitez-vous retourner à la page d'accueil?</value>
  </data>
  <data name="RegistrationFormSubtitle" xml:space="preserve">
    <value>Rejoignez notre communauté de passionnés de hockey adapté</value>
  </data>
  <data name="AlreadyRegisteredForEvent" xml:space="preserve">
    <value>Vous êtes déjà inscrit à cet événement.</value>
  </data>
  <data name="RegisterForUpcomingEvents" xml:space="preserve">
    <value>Inscrivez-vous aux événements à venir</value>
  </data>
  <data name="ErrorLoadingEvents" xml:space="preserve">
    <value>Erreur lors du chargement des événements</value>
  </data>
  <data name="MemberAccountRequired" xml:space="preserve">
    <value>Un compte membre est requis pour s'inscrire aux événements</value>
  </data>
  
  <!-- Event Details Modal -->
  <data name="Date" xml:space="preserve">
    <value>Date</value>
  </data>
  <data name="Time" xml:space="preserve">
    <value>Heure</value>
  </data>
  <data name="RegisterForEvent" xml:space="preserve">
    <value>S'inscrire à l'événement</value>
  </data>
  <data name="AlreadyFull" xml:space="preserve">
    <value>Cet événement est déjà complet</value>
  </data>
  <data name="AllDay" xml:space="preserve">
    <value>Toute la journée</value>
  </data>
  <data name="LocationTBA" xml:space="preserve">
    <value>Lieu à confirmer</value>
  </data>
  <data name="ErrorLoadingEventDetails" xml:space="preserve">
    <value>Erreur lors du chargement des détails de l'événement</value>
  </data>
  <data name="NoChangesDetected" xml:space="preserve">
    <value>Aucune modification détectée.</value>
  </data>
  <data name="Close" xml:space="preserve">
    <value>Fermer</value>
  </data>
  <data name="Location" xml:space="preserve">
    <value>Lieu</value>
  </data>
  <data name="YouAreRegistered" xml:space="preserve">
    <value>Vous êtes inscrit à cet événement</value>
  </data>
  <data name="Unregister" xml:space="preserve">
    <value>Se désinscrire</value>
  </data>
  <data name="EventUnregistrationSuccess" xml:space="preserve">
    <value>Vous avez été désinscrit de l'événement avec succès</value>
  </data>
  <data name="EventUnregistrationError" xml:space="preserve">
    <value>Une erreur s'est produite lors de la désinscription de l'événement</value>
  </data>
  <data name="MemberBenefitsTitle" xml:space="preserve">
    <value>Avantages de l'adhésion :</value>
  </data>
  <data name="MemberBenefitEventRegistration" xml:space="preserve">
    <value>Inscription aux événements et compétitions</value>
  </data>
  <data name="MemberBenefitPriorityAccess" xml:space="preserve">
    <value>Accès prioritaire aux événements populaires</value>
  </data>
  <data name="MemberBenefitUpdates" xml:space="preserve">
    <value>Mises à jour et nouvelles du club</value>
  </data>
  <data name="MemberBenefitCommunity" xml:space="preserve">
    <value>Accès à la communauté parahockey</value>
  </data>
  <data name="MembershipIsFree" xml:space="preserve">
    <value>L'adhésion est gratuite et prend seulement quelques minutes!</value>
  </data>
  <data name="EventRegistrationAfterSignup" xml:space="preserve">
    <value>Après votre inscription, vous serez automatiquement redirigé pour vous inscrire à l'événement sélectionné.</value>
  </data>
  <data name="Available" xml:space="preserve">
    <value>Disponible</value>
  </data>
  <data name="EventFull" xml:space="preserve">
    <value>Complet</value>
  </data>
  <data name="Registering" xml:space="preserve">
    <value>Inscription</value>
  </data>
  <data name="Unregistering" xml:space="preserve">
    <value>Désinscription</value>
  </data>
  <data name="EventRegistrationSuccess" xml:space="preserve">
    <value>Inscription réussie!</value>
  </data>
  <data name="RegistrationError" xml:space="preserve">
    <value>Erreur d'inscription</value>
  </data>
  <data name="UnregisterConfirm" xml:space="preserve">
    <value>Êtes-vous sûr de vouloir vous désinscrire de cet événement?</value>
  </data>
  <data name="ViewDetails" xml:space="preserve">
    <value>Voir les détails</value>
  </data>
  <data name="ManageRegistration" xml:space="preserve">
    <value>Gérer l'inscription</value>
  </data>
  <data name="JuniorMembershipNotAvailable18Plus" xml:space="preserve">
    <value>L'adhésion Junior n'est pas disponible pour les personnes de 18 ans et plus</value>
  </data>

  <!-- Page Audit System -->
  <data name="InventoryGeneratedSuccessfully" xml:space="preserve">
    <value>Inventaire des pages généré avec succès</value>
  </data>
  <data name="ErrorGeneratingInventory" xml:space="preserve">
    <value>Erreur lors de la génération de l'inventaire des pages</value>
  </data>
  <data name="PageNameRequired" xml:space="preserve">
    <value>Le nom de la page est requis</value>
  </data>
  <data name="PageAuditCompleted" xml:space="preserve">
    <value>Audit de la page terminé</value>
  </data>
  <data name="ErrorAuditingPage" xml:space="preserve">
    <value>Erreur lors de l'audit de la page</value>
  </data>
  <data name="NoInventoryFound" xml:space="preserve">
    <value>Aucun inventaire trouvé. Veuillez générer un inventaire d'abord.</value>
  </data>
  <data name="ErrorRetrievingInventory" xml:space="preserve">
    <value>Erreur lors de la récupération de l'inventaire</value>
  </data>
  <data name="ErrorRetrievingAuditHistory" xml:space="preserve">
    <value>Erreur lors de la récupération de l'historique d'audit</value>
  </data>
  <data name="InvalidFindingId" xml:space="preserve">
    <value>ID de constatation invalide</value>
  </data>
  <data name="ResolutionNotesRequired" xml:space="preserve">
    <value>Les notes de résolution sont requises</value>
  </data>
  <data name="FindingResolvedSuccessfully" xml:space="preserve">
    <value>Constatation résolue avec succès</value>
  </data>
  <data name="ErrorResolvingFinding" xml:space="preserve">
    <value>Erreur lors de la résolution de la constatation</value>
  </data>
  <data name="PageAuditSystem" xml:space="preserve">
    <value>Système d'audit des pages</value>
  </data>
  <data name="GenerateInventory" xml:space="preserve">
    <value>Générer l'inventaire</value>
  </data>
  <data name="AuditPage" xml:space="preserve">
    <value>Auditer la page</value>
  </data>
  <data name="PageInventory" xml:space="preserve">
    <value>Inventaire des pages</value>
  </data>
  <data name="AuditResults" xml:space="preserve">
    <value>Résultats d'audit</value>
  </data>
  <data name="SecurityScore" xml:space="preserve">
    <value>Score de sécurité</value>
  </data>
  <data name="AccessibilityScore" xml:space="preserve">
    <value>Score d'accessibilité</value>
  </data>
  <data name="PerformanceScore" xml:space="preserve">
    <value>Score de performance</value>
  </data>
  <data name="LocalizationScore" xml:space="preserve">
    <value>Score de localisation</value>
  </data>
  <data name="OverallScore" xml:space="preserve">
    <value>Score global</value>
  </data>
  <data name="CriticalIssues" xml:space="preserve">
    <value>Problèmes critiques</value>
  </data>
  <data name="HighIssues" xml:space="preserve">
    <value>Problèmes élevés</value>
  </data>
  <data name="MediumIssues" xml:space="preserve">
    <value>Problèmes moyens</value>
  </data>
  <data name="LowIssues" xml:space="preserve">
    <value>Problèmes faibles</value>
  </data>
  <data name="TotalIssues" xml:space="preserve">
    <value>Total des problèmes</value>
  </data>
  <data name="PageComplexity" xml:space="preserve">
    <value>Complexité de la page</value>
  </data>
  <data name="Priority" xml:space="preserve">
    <value>Priorité</value>
  </data>
  <data name="IsModernized" xml:space="preserve">
    <value>Modernisée</value>
  </data>
  <data name="LastAuditScore" xml:space="preserve">
    <value>Dernier score d'audit</value>
  </data>
  <data name="AuditHistory" xml:space="preserve">
    <value>Historique d'audit</value>
  </data>
  <data name="ResolveFinding" xml:space="preserve">
    <value>Résoudre la constatation</value>
  </data>
  <data name="ResolutionNotes" xml:space="preserve">
    <value>Notes de résolution</value>
  </data>
  
  <!-- Page Audit System -->
  <data name="PageReviewPlanGenerated" xml:space="preserve">
    <value>Plan de révision des pages généré avec succès</value>
  </data>
  <data name="InitialAuditReportsGenerated" xml:space="preserve">
    <value>Rapports d'audit initiaux générés avec succès</value>
  </data>
  <data name="ErrorGeneratingReviewPlan" xml:space="preserve">
    <value>Erreur lors de la génération du plan de révision</value>
  </data>
  <data name="ErrorGeneratingAuditReports" xml:space="preserve">
    <value>Erreur lors de la génération des rapports d'audit</value>
  </data>

  <!-- Error Handling System -->
  <data name="Error_ValidationFailed" xml:space="preserve">
    <value>La validation a échoué</value>
  </data>
  <data name="Error_Unauthorized" xml:space="preserve">
    <value>Accès non autorisé</value>
  </data>
  <data name="Error_ResourceNotFound" xml:space="preserve">
    <value>Ressource non trouvée</value>
  </data>
  <data name="Error_RequestTimeout" xml:space="preserve">
    <value>Délai d'attente de la requête dépassé</value>
  </data>
  <data name="Error_InvalidOperation" xml:space="preserve">
    <value>Opération invalide</value>
  </data>
  <data name="Error_OperationNotSupported" xml:space="preserve">
    <value>Opération non supportée</value>
  </data>
  <data name="Error_DatabaseError" xml:space="preserve">
    <value>Erreur de base de données</value>
  </data>
  <data name="Error_UnexpectedError" xml:space="preserve">
    <value>Une erreur inattendue s'est produite</value>
  </data>

  <!-- Validation Error Messages -->
  <data name="ValidationError_Generic" xml:space="preserve">
    <value>Erreur de validation</value>
  </data>
  <data name="ValidationError_Required" xml:space="preserve">
    <value>Ce champ est requis</value>
  </data>
  <data name="ValidationError_EmailFormat" xml:space="preserve">
    <value>Format d'email invalide</value>
  </data>
  <data name="ValidationError_PhoneFormat" xml:space="preserve">
    <value>Format de téléphone invalide</value>
  </data>
  <data name="ValidationError_PostalCodeFormat" xml:space="preserve">
    <value>Format de code postal invalide</value>
  </data>
  <data name="ValidationError_StringLength" xml:space="preserve">
    <value>La longueur du texte est invalide</value>
  </data>
  <data name="ValidationError_Range" xml:space="preserve">
    <value>La valeur est hors de la plage autorisée</value>
  </data>
  <data name="ValidationError_DateFormat" xml:space="preserve">
    <value>Format de date invalide</value>
  </data>

  <!-- Validation Summary Messages -->
  <data name="ValidationSummary_SingleError" xml:space="preserve">
    <value>Veuillez corriger l'erreur ci-dessous</value>
  </data>
  <data name="ValidationSummary_MultipleErrors" xml:space="preserve">
    <value>Veuillez corriger les {0} erreurs ci-dessous</value>
  </data>

  <!-- Error Page Messages -->
  <data name="SystemErrorTitle" xml:space="preserve">
    <value>Erreur Système</value>
  </data>
  <data name="SystemErrorHeading" xml:space="preserve">
    <value>Une erreur inattendue s'est produite</value>
  </data>
  <data name="SystemErrorRequestId" xml:space="preserve">
    <value>ID de la Requête</value>
  </data>
  <data name="PageNotFoundTitle" xml:space="preserve">
    <value>Page Non Trouvée</value>
  </data>
  <data name="PageNotFoundHeading" xml:space="preserve">
    <value>Cette page n'existe pas</value>
  </data>
  <data name="PageNotFoundMessage" xml:space="preserve">
    <value>La page que vous cherchez n'a pu être trouvée. Elle a peut-être été déplacée, supprimée ou vous avez tapé une mauvaise adresse.</value>
  </data>
  <data name="RequestedUrl" xml:space="preserve">
    <value>URL Demandée</value>
  </data>
  <data name="ReturnToHome" xml:space="preserve">
    <value>Retour à l'Accueil</value>
  </data>
  <data name="GoBack" xml:space="preserve">
    <value>Retour</value>
  </data>
  <data name="ViewCalendar" xml:space="preserve">
    <value>Voir le Calendrier</value>
  </data>
  <data name="SuggestedLinks" xml:space="preserve">
    <value>Liens Suggérés</value>
  </data>
  <data name="HomePage" xml:space="preserve">
    <value>Page d'Accueil</value>
  </data>
  <data name="Calendar" xml:space="preserve">
    <value>Calendrier</value>
  </data>
  <data name="MemberRegistration" xml:space="preserve">
    <value>Inscription Membre</value>
  </data>
  <data name="MemberLogin" xml:space="preserve">
    <value>Connexion Membre</value>
  </data>
  <data name="NeedHelp" xml:space="preserve">
    <value>Besoin d'aide?</value>
  </data>
  <data name="AutoRedirectConfirm" xml:space="preserve">
    <value>Voulez-vous être redirigé vers la page d'accueil?</value>
  </data>
  
  <!-- Home Page Accessibility Keys -->
  <data name="SkipToMainContent" xml:space="preserve">
    <value>Aller au contenu principal</value>
  </data>
  <data name="MainContentArea" xml:space="preserve">
    <value>Zone de contenu principal</value>
  </data>
  <data name="ParahockeyLogoAlt" xml:space="preserve">
    <value>Logo Parahockey Québec - Association de hockey sur luge</value>
  </data>
  <data name="PrimaryActions" xml:space="preserve">
    <value>Actions principales</value>
  </data>
  <data name="RegisterButtonDesc" xml:space="preserve">
    <value>Commencer votre inscription comme membre</value>
  </data>
  <data name="ViewCalendarDesc" xml:space="preserve">
    <value>Consulter le calendrier public des événements</value>
  </data>
  <data name="SubscribeEventsDesc" xml:space="preserve">
    <value>S'inscrire aux événements et activités</value>
  </data>
  <data name="CommunityStatsLabel" xml:space="preserve">
    <value>Statistiques de la communauté</value>
  </data>
  <data name="PlayersCount" xml:space="preserve">
    <value>Plus de 150 joueurs actifs</value>
  </data>
  <data name="TeamsCount" xml:space="preserve">
    <value>12 équipes</value>
  </data>
  <data name="CoachesCount" xml:space="preserve">
    <value>25 entraîneurs</value>
  </data>
  <data name="VolunteersCount" xml:space="preserve">
    <value>Plus de 50 bénévoles</value>
  </data>
  <data name="RegistrationTypesList" xml:space="preserve">
    <value>Types d'inscription disponibles</value>
  </data>
  <data name="CtaButtonDesc" xml:space="preserve">
    <value>Commencer votre inscription maintenant</value>
  </data>
  
  <!-- Home Page Content Keys (if missing) -->
  <data name="HomePageTitle" xml:space="preserve">
    <value>Parahockey Québec - Page d'Accueil</value>
  </data>
  <data name="WelcomeTitle" xml:space="preserve">
    <value>Bienvenue chez Parahockey Québec</value>
  </data>
  <data name="WelcomeSubtitle" xml:space="preserve">
    <value>Rejoignez notre communauté passionnée de hockey sur luge</value>
  </data>
  <data name="RegisterNow" xml:space="preserve">
    <value>S'inscrire maintenant</value>
  </data>
  <data name="ViewCalendar" xml:space="preserve">
    <value>Voir le calendrier</value>
  </data>
  <data name="SubscribeToEvents" xml:space="preserve">
    <value>S'inscrire aux événements</value>
  </data>
  <data name="CommunityTitle" xml:space="preserve">
    <value>Notre Communauté</value>
  </data>
  <data name="ActivePlayers" xml:space="preserve">
    <value>Joueurs Actifs</value>
  </data>
  <data name="Teams" xml:space="preserve">
    <value>Équipes</value>
  </data>
  <data name="Coaches" xml:space="preserve">
    <value>Entraîneurs</value>
  </data>
  <data name="Volunteers" xml:space="preserve">
    <value>Bénévoles</value>
  </data>
  <data name="RegistrationTypesTitle" xml:space="preserve">
    <value>Types d'Inscription</value>
  </data>
  <data name="RegistrationTypesSubtitle" xml:space="preserve">
    <value>Choisissez le type d'inscription qui vous convient</value>
  </data>
  <data name="Junior" xml:space="preserve">
    <value>Junior</value>
  </data>
  <data name="JuniorDesc" xml:space="preserve">
    <value>Pour les jeunes joueurs âgés de 6 à 17 ans</value>
  </data>
  <data name="Development" xml:space="preserve">
    <value>Développement</value>
  </data>
  <data name="DevelopmentDesc" xml:space="preserve">
    <value>Programme de formation pour nouveaux joueurs</value>
  </data>
  <data name="Elite" xml:space="preserve">
    <value>Élite</value>
  </data>
  <data name="EliteDesc" xml:space="preserve">
    <value>Pour les joueurs expérimentés et compétitifs</value>
  </data>
  <data name="Coach" xml:space="preserve">
    <value>Entraîneur</value>
  </data>
  <data name="CoachDesc" xml:space="preserve">
    <value>Rejoignez notre équipe d'entraîneurs certifiés</value>
  </data>
  <data name="Volunteer" xml:space="preserve">
    <value>Bénévole</value>
  </data>
  <data name="VolunteerDesc" xml:space="preserve">
    <value>Aidez à organiser événements et activités</value>
  </data>
  <data name="FamilyFriends" xml:space="preserve">
    <value>Famille &amp; Amis</value>
  </data>
  <data name="FamilyFriendsDesc" xml:space="preserve">
    <value>Soutenez nos joueurs et participez aux événements</value>
  </data>
  <data name="CtaTitle" xml:space="preserve">
    <value>Prêt à Rejoindre Notre Équipe?</value>
  </data>
  <data name="CtaSubtitle" xml:space="preserve">
    <value>Inscrivez-vous dès aujourd'hui et découvrez la passion du hockey sur luge</value>
  </data>
  <data name="CtaButton" xml:space="preserve">
    <value>Commencer Mon Inscription</value>
  </data>
  
  <!-- Members Registration Page Accessibility Keys -->
  <data name="SkipToRegistrationForm" xml:space="preserve">
    <value>Aller au formulaire d'inscription</value>
  </data>
  <data name="RegistrationMainContent" xml:space="preserve">
    <value>Contenu principal de l'inscription</value>
  </data>
  <data name="LoginLinkDescription" xml:space="preserve">
    <value>Navigue vers la page de connexion pour les membres existants</value>
  </data>
  <data name="RegistrationFormDescription" xml:space="preserve">
    <value>Formulaire d'inscription complète pour devenir membre de Parahockey Québec</value>
  </data>
  <data name="FirstNameHelp" xml:space="preserve">
    <value>Votre prénom tel qu'il apparaît sur vos documents officiels</value>
  </data>
  <data name="LastNameHelp" xml:space="preserve">
    <value>Votre nom de famille tel qu'il apparaît sur vos documents officiels</value>
  </data>
  <data name="DateOfBirthHelp" xml:space="preserve">
    <value>Format requis: AAAA-MM-JJ (ex: 1990-12-25)</value>
  </data>
  <data name="GenderFieldsetLabel" xml:space="preserve">
    <value>Sélection du genre</value>
  </data>
  <data name="AddressFieldsetLabel" xml:space="preserve">
    <value>Informations d'adresse</value>
  </data>
  <data name="ContactFieldsetLabel" xml:space="preserve">
    <value>Informations de contact</value>
  </data>
  <data name="RegistrationTypeFieldsetLabel" xml:space="preserve">
    <value>Type d'inscription</value>
  </data>
  <data name="FormSubmissionStatus" xml:space="preserve">
    <value>Traitement de votre inscription en cours...</value>
  </data>
  <data name="FormValidationError" xml:space="preserve">
    <value>Erreur de validation détectée dans le formulaire</value>
  </data>

  <!-- Login Page Accessibility -->
  <data name="SkipToLoginForm" xml:space="preserve">
    <value>Aller au formulaire de connexion</value>
  </data>
  <data name="LoginMainContent" xml:space="preserve">
    <value>Contenu principal de la page de connexion</value>
  </data>
  <data name="LoginFormDescription" xml:space="preserve">
    <value>Formulaire de recherche et de connexion des membres</value>
  </data>
  <data name="LastNameHelp" xml:space="preserve">
    <value>Entrez votre nom de famille tel qu'inscrit</value>
  </data>
  <data name="EmailHelp" xml:space="preserve">
    <value>Entrez l'adresse courriel associée à votre compte</value>
  </data>
  <data name="OpenDatePicker" xml:space="preserve">
    <value>Ouvrir le sélecteur de date</value>
  </data>
  <data name="SearchButtonHelp" xml:space="preserve">
    <value>Rechercher votre profil membre dans la base de données</value>
  </data>
  <data name="SearchResultsTableDescription" xml:space="preserve">
    <value>Résultats de recherche des membres correspondants</value>
  </data>
  <data name="Actions" xml:space="preserve">
    <value>Actions</value>
  </data>
  <data name="SendCodeButtonHelp" xml:space="preserve">
    <value>Envoyer un code de vérification par courriel</value>
  </data>
  <data name="VerificationCodeEntry" xml:space="preserve">
    <value>Saisie du code de vérification</value>
  </data>
  <data name="VerificationCode" xml:space="preserve">
    <value>Code de vérification</value>
  </data>
  <data name="VerificationCodeHelp" xml:space="preserve">
    <value>Entrez le code à 6 chiffres reçu par courriel</value>
  </data>
  <data name="VerifyButtonHelp" xml:space="preserve">
    <value>Vérifier le code et se connecter</value>
  </data>
  <data name="CloseAlert" xml:space="preserve">
    <value>Fermer l'alerte</value>
  </data>
  <data name="InfoMessage" xml:space="preserve">
    <value>Message d'information</value>
  </data>
  <data name="SendingCode" xml:space="preserve">
    <value>Envoi en cours...</value>
  </data>
  <data name="VerifyingCode" xml:space="preserve">
    <value>Vérification...</value>
  </data>
  <data name="VerificationSuccessful" xml:space="preserve">
    <value>Vérification réussie!</value>
  </data>
  <data name="VerificationFailed" xml:space="preserve">
    <value>Code de vérification invalide</value>
  </data>
  <data name="VerificationError" xml:space="preserve">
    <value>Erreur lors de la vérification</value>
  </data>
  <data name="LogoutSuccessful" xml:space="preserve">
    <value>Déconnexion réussie. À bientôt!</value>
  </data>
  <data name="LogoutCompleted" xml:space="preserve">
    <value>Déconnexion terminée</value>
  </data>

  <!-- Admin Dashboard Accessibility -->
  <data name="SkipToMainContent" xml:space="preserve">
    <value>Aller au contenu principal</value>
  </data>
  <data name="SkipToStatistics" xml:space="preserve">
    <value>Aller aux statistiques</value>
  </data>
  <data name="SkipToQuickActions" xml:space="preserve">
    <value>Aller aux actions rapides</value>
  </data>
  <data name="AdminDashboardMainContent" xml:space="preserve">
    <value>Contenu principal du tableau de bord administrateur</value>
  </data>
  <data name="AdminDashboard" xml:space="preserve">
    <value>Tableau de bord administrateur</value>
  </data>
  <data name="AdminWelcomeMessage" xml:space="preserve">
    <value>Bienvenue, {0} | Environnement: {1}</value>
  </data>
  <data name="SystemStatistics" xml:space="preserve">
    <value>Statistiques du système</value>
  </data>
  <data name="TotalMembers" xml:space="preserve">
    <value>Membres totaux</value>
  </data>
  <data name="TotalParents" xml:space="preserve">
    <value>Parents totaux</value>
  </data>
  <data name="EmergencyContacts" xml:space="preserve">
    <value>Contacts d'urgence</value>
  </data>
  <data name="CurrentEnvironment" xml:space="preserve">
    <value>Environnement actuel</value>
  </data>
  <data name="ViewAllMembers" xml:space="preserve">
    <value>Voir tous les membres</value>
  </data>
  <data name="ViewSystemInfo" xml:space="preserve">
    <value>Infos système</value>
  </data>
  <data name="QuickActions" xml:space="preserve">
    <value>Actions rapides</value>
  </data>
  <data name="QuickActionsDescription" xml:space="preserve">
    <value>Accès rapide aux fonctions principales du système d'administration</value>
  </data>
  <data name="AdminQuickActions" xml:space="preserve">
    <value>Actions rapides d'administration</value>
  </data>
  <data name="ViewAllMembersDescription" xml:space="preserve">
    <value>Consulter et gérer tous les membres inscrits</value>
  </data>
  <data name="AddNewMember" xml:space="preserve">
    <value>Ajouter nouveau membre</value>
  </data>
  <data name="AddNewMemberDescription" xml:space="preserve">
    <value>Inscrire un nouveau membre au système</value>
  </data>
  <data name="ManageCalendar" xml:space="preserve">
    <value>Gérer le calendrier</value>
  </data>
  <data name="ManageCalendarDescription" xml:space="preserve">
    <value>Créer et modifier les événements du calendrier</value>
  </data>
  <data name="ManageAdmins" xml:space="preserve">
    <value>Gérer les administrateurs</value>
  </data>
  <data name="ManageAdminsDescription" xml:space="preserve">
    <value>Administrer les comptes et permissions d'administrateurs</value>
  </data>
  <data name="PageAuditSystem" xml:space="preserve">
    <value>Système d'audit des pages</value>
  </data>
  <data name="PageAuditDescription" xml:space="preserve">
    <value>Analyser la performance et l'accessibilité des pages</value>
  </data>
  <data name="SystemInformation" xml:space="preserve">
    <value>Informations système</value>
  </data>
  <data name="SystemInfoDescription" xml:space="preserve">
    <value>Consulter l'état et la configuration du système</value>
  </data>
  <data name="TeamsManagement" xml:space="preserve">
    <value>Gestion des équipes</value>
  </data>
  <data name="TeamsDescription" xml:space="preserve">
    <value>Organiser et gérer les équipes de hockey</value>
  </data>
  <data name="ViewStatistics" xml:space="preserve">
    <value>Voir les statistiques</value>
  </data>
  <data name="StatisticsDescription" xml:space="preserve">
    <value>Consulter les rapports et analyses du système</value>
  </data>
  <data name="BackToSite" xml:space="preserve">
    <value>Retour au site</value>
  </data>
  <data name="BackToSiteDescription" xml:space="preserve">
    <value>Revenir à la page d'accueil publique</value>
  </data>
  <data name="RecentMemberAudit" xml:space="preserve">
    <value>Audit récent des membres</value>
  </data>
  <data name="RecentAuditDescription" xml:space="preserve">
    <value>Activités récentes et modifications apportées au système</value>
  </data>
  <data name="AuditTableDescription" xml:space="preserve">
    <value>Tableau des activités d'audit récentes</value>
  </data>
  <data name="AuditDateTime" xml:space="preserve">
    <value>Date et heure</value>
  </data>
  <data name="Description" xml:space="preserve">
    <value>Description</value>
  </data>
  <data name="PerformedBy" xml:space="preserve">
    <value>Effectué par</value>
  </data>
  <data name="Actions" xml:space="preserve">
    <value>Actions</value>
  </data>
  <data name="AdminAction" xml:space="preserve">
    <value>Action d'administrateur</value>
  </data>
  <data name="Admin" xml:space="preserve">
    <value>Admin</value>
  </data>
  <data name="SystemAction" xml:space="preserve">
    <value>Action système</value>
  </data>
  <data name="ViewAuditDetails" xml:space="preserve">
    <value>Voir les détails de l'audit pour {0}</value>
  </data>
  <data name="ViewDetails" xml:space="preserve">
    <value>Voir les détails</value>
  </data>
  <data name="ViewAllAuditHistory" xml:space="preserve">
    <value>Voir tout l'historique d'audit</value>
  </data>
  <data name="NoRecentActivity" xml:space="preserve">
    <value>Aucune activité récente</value>
  </data>
  <data name="NoAuditActivityDescription" xml:space="preserve">
    <value>Aucune activité d'audit n'a été enregistrée récemment</value>
  </data>
  <data name="RecentMemberRegistrations" xml:space="preserve">
    <value>Inscriptions récentes de membres</value>
  </data>
  <data name="RecentMembersDescription" xml:space="preserve">
    <value>Nouveaux membres inscrits récemment dans le système</value>
  </data>
  <data name="MembersTableDescription" xml:space="preserve">
    <value>Tableau des membres récemment inscrits</value>
  </data>
  <data name="MemberID" xml:space="preserve">
    <value>ID Membre</value>
  </data>
  <data name="FullName" xml:space="preserve">
    <value>Nom complet</value>
  </data>
  <data name="RegistrationDate" xml:space="preserve">
    <value>Date d'inscription</value>
  </data>
  <data name="SendEmailTo" xml:space="preserve">
    <value>Envoyer un courriel à {0}</value>
  </data>
  <data name="ViewMemberDetails" xml:space="preserve">
    <value>Voir les détails de {0}</value>
  </data>
  <data name="NoMembersRegistered" xml:space="preserve">
    <value>Aucun membre inscrit</value>
  </data>
  <data name="NoMembersDescription" xml:space="preserve">
    <value>Aucun membre n'est encore inscrit dans le système</value>
  </data>
  <data name="RegisterFirstMember" xml:space="preserve">
    <value>Inscrire le premier membre</value>
  </data>
  <data name="DashboardLoaded" xml:space="preserve">
    <value>Tableau de bord chargé avec succès</value>
  </data>

  <!-- Admin Members Page Accessibility -->
  <data name="SkipToSearchFilters" xml:space="preserve">
    <value>Aller aux filtres de recherche</value>
  </data>
  <data name="SkipToMemberActions" xml:space="preserve">
    <value>Aller aux actions membres</value>
  </data>
  <data name="SkipToMembersTable" xml:space="preserve">
    <value>Aller au tableau des membres</value>
  </data>
  <data name="MemberManagementPage" xml:space="preserve">
    <value>Page de gestion des membres</value>
  </data>
  <data name="BackToAdminDashboard" xml:space="preserve">
    <value>Retour au tableau de bord administrateur</value>
  </data>
  <data name="Breadcrumb" xml:space="preserve">
    <value>Fil d'Ariane</value>
  </data>
  <data name="SearchAndFilters" xml:space="preserve">
    <value>Recherche et filtres</value>
  </data>
  <data name="SearchHelpText" xml:space="preserve">
    <value>Rechercher par nom, prénom ou adresse email</value>
  </data>
  <data name="ClearSearchResults" xml:space="preserve">
    <value>Effacer les résultats de recherche</value>
  </data>
  <data name="AdvancedFilters" xml:space="preserve">
    <value>Filtres avancés</value>
  </data>
  <data name="FilterBy" xml:space="preserve">
    <value>Filtrer par</value>
  </data>
  <data name="SelectFilterType" xml:space="preserve">
    <value>Sélectionner le type de filtre</value>
  </data>
  <data name="SelectFilterTypeHelp" xml:space="preserve">
    <value>Choisir le critère de filtrage</value>
  </data>
  <data name="FilterValue" xml:space="preserve">
    <value>Valeur du filtre</value>
  </data>
  <data name="SelectFilterValueHelp" xml:space="preserve">
    <value>Sélectionner la valeur à filtrer</value>
  </data>
  <data name="CitySearchHelp" xml:space="preserve">
    <value>Taper au moins 2 caractères pour rechercher une ville</value>
  </data>
  <data name="ApplyFilter" xml:space="preserve">
    <value>Appliquer le filtre</value>
  </data>
  <data name="ApplyFilterHelp" xml:space="preserve">
    <value>Appliquer le filtre sélectionné à la liste</value>
  </data>
  <data name="ClearFiltersHelp" xml:space="preserve">
    <value>Supprimer tous les filtres actifs</value>
  </data>
  <data name="MemberActions" xml:space="preserve">
    <value>Actions membres</value>
  </data>
  <data name="ExportMembersHelp" xml:space="preserve">
    <value>Exporter la liste des membres en Excel ou CSV</value>
  </data>
  <data name="AddNewMemberHelp" xml:space="preserve">
    <value>Ajouter un nouveau membre au système</value>
  </data>
  <data name="SearchResultsSummary" xml:space="preserve">
    <value>Résumé des résultats de recherche</value>
  </data>
  <data name="MembersTable" xml:space="preserve">
    <value>Tableau des membres</value>
  </data>
  <data name="MembersTableDescription" xml:space="preserve">
    <value>Tableau listant tous les membres avec leurs informations et actions disponibles</value>
  </data>
  <data name="MembersPageLoaded" xml:space="preserve">
    <value>Page des membres chargée avec succès</value>
  </data>
  
  <!-- Page de détails de membre admin - Accessibilité -->
  <data name="SkipToMemberInfo" xml:space="preserve">
    <value>Aller aux informations du membre</value>
  </data>
  <data name="SkipToContactInfo" xml:space="preserve">
    <value>Aller aux informations de contact</value>
  </data>
  <data name="SkipToAuditHistory" xml:space="preserve">
    <value>Aller à l'historique d'audit</value>
  </data>
  <data name="MemberDetailsPage" xml:space="preserve">
    <value>Page de détails du membre</value>
  </data>
  <data name="MemberDetailsPageLoaded" xml:space="preserve">
    <value>Page de détails du membre chargée avec succès</value>
  </data>
  <data name="MemberActionButtons" xml:space="preserve">
    <value>Boutons d'actions du membre</value>
  </data>
  <data name="Exporting" xml:space="preserve">
    <value>Exportation...</value>
  </data>
  
  <!-- Page d'abonnement aux événements - Accessibilité -->
  <data name="SkipToUpcomingEvents" xml:space="preserve">
    <value>Aller aux événements à venir</value>
  </data>
  <data name="SkipToEventCalendar" xml:space="preserve">
    <value>Aller au calendrier des événements</value>
  </data>
  <data name="EventSubscriptionPage" xml:space="preserve">
    <value>Page d'abonnement aux événements</value>
  </data>
  <data name="EventSubscriptionPageLoaded" xml:space="preserve">
    <value>Page d'abonnement aux événements chargée avec succès</value>
  </data>
  <data name="BackToHomePage" xml:space="preserve">
    <value>Retourner à la page d'accueil</value>
  </data>
  <data name="CloseAlert" xml:space="preserve">
    <value>Fermer l'alerte</value>
  </data>
  <data name="MustBeLoggedInToRegister" xml:space="preserve">
    <value>Vous devez être connecté pour vous inscrire aux événements</value>
  </data>
  <data name="LoadingEventDetails" xml:space="preserve">
    <value>Chargement des détails de l'événement...</value>
  </data>
</root>