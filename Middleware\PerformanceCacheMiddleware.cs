using Microsoft.Extensions.Caching.Memory;
using ParaHockeyApp.Services;
using System.Text;

namespace ParaHockeyApp.Middleware;

/// <summary>
/// Middleware for implementing performance-focused caching strategies
/// with appropriate cache headers and invalidation logic.
/// </summary>
public class PerformanceCacheMiddleware
{
    private readonly RequestDelegate _next;
    private readonly IServiceProvider _serviceProvider;
    private readonly IMemoryCache _cache;
    private readonly ILogger<PerformanceCacheMiddleware> _logger;
    private readonly IWebHostEnvironment _environment;

    public PerformanceCacheMiddleware(
        RequestDelegate next,
        IServiceProvider serviceProvider,
        IMemoryCache cache,
        ILogger<PerformanceCacheMiddleware> logger,
        IWebHostEnvironment environment)
    {
        _next = next;
        _serviceProvider = serviceProvider;
        _cache = cache;
        _logger = logger;
        _environment = environment;
    }

    public async Task InvokeAsync(HttpContext context)
    {
        var request = context.Request;
        var response = context.Response;

        // Skip caching for certain requests
        if (ShouldSkipCaching(request))
        {
            await _next(context);
            return;
        }

        // Resolve the scoped service from the service provider
        using var scope = _serviceProvider.CreateScope();
        var performanceService = scope.ServiceProvider.GetRequiredService<IPerformanceOptimizationService>();

        // Determine resource type
        var resourceType = GetResourceType(request.Path);
        var cacheConfig = performanceService.GetCacheHeaders(resourceType, request.Path);

        // Check if we have a cached response
        var cacheKey = GenerateCacheKey(request);
        var cachedResponse = await GetCachedResponseAsync(cacheKey);

        // Handle conditional requests (If-None-Match, If-Modified-Since)
        if (HandleConditionalRequest(context, cacheConfig, cachedResponse))
        {
            return;
        }

        // If we have a valid cached response, serve it
        if (cachedResponse != null && IsValidCachedResponse(cachedResponse, cacheConfig))
        {
            await ServeCachedResponse(context, cachedResponse, cacheConfig);
            return;
        }

        // Capture the response for caching
        var originalBodyStream = response.Body;
        using var responseBodyStream = new MemoryStream();
        response.Body = responseBodyStream;

        try
        {
            await _next(context);

            // Only cache successful responses
            if (response.StatusCode == 200 && ShouldCacheResponse(request, response))
            {
                var responseBody = responseBodyStream.ToArray();
                var responseData = new CachedResponse
                {
                    StatusCode = response.StatusCode,
                    ContentType = response.ContentType ?? "text/html",
                    Body = responseBody,
                    Headers = response.Headers.ToDictionary(h => h.Key, h => h.Value.ToString()),
                    CachedAt = DateTime.UtcNow,
                    ETag = cacheConfig.ETag
                };

                await CacheResponseAsync(cacheKey, responseData, cacheConfig);
            }

            // Apply cache headers
            ApplyCacheHeaders(response, cacheConfig);

            // Copy the response back to the original stream
            responseBodyStream.Seek(0, SeekOrigin.Begin);
            await responseBodyStream.CopyToAsync(originalBodyStream);
        }
        finally
        {
            response.Body = originalBodyStream;
        }
    }

    /// <summary>
    /// Determine if caching should be skipped for this request
    /// </summary>
    private bool ShouldSkipCaching(HttpRequest request)
    {
        // Skip caching for:
        // - Non-GET requests
        // - Requests with query parameters that indicate dynamic content
        // - Admin pages (for security)
        // - API endpoints that return user-specific data

        if (request.Method != "GET")
            return true;

        var path = request.Path.Value?.ToLowerInvariant() ?? "";

        // Skip admin pages
        if (path.StartsWith("/admin"))
            return true;

        // Skip API endpoints with user-specific data
        if (path.StartsWith("/api/members") || path.StartsWith("/api/events/subscribe"))
            return true;

        // Skip pages with authentication requirements
        if (request.Headers.ContainsKey("Authorization"))
            return true;

        // Skip if there are cache-busting query parameters
        if (request.Query.ContainsKey("nocache") || request.Query.ContainsKey("t"))
            return true;

        return false;
    }

    /// <summary>
    /// Determine the resource type based on the request path
    /// </summary>
    private string GetResourceType(PathString path)
    {
        var pathValue = path.Value?.ToLowerInvariant() ?? "";

        if (pathValue.EndsWith(".css"))
            return "css";
        if (pathValue.EndsWith(".js"))
            return "js";
        if (pathValue.EndsWith(".jpg") || pathValue.EndsWith(".jpeg") || 
            pathValue.EndsWith(".png") || pathValue.EndsWith(".gif") || 
            pathValue.EndsWith(".webp") || pathValue.EndsWith(".avif"))
            return "image";
        if (pathValue.EndsWith(".woff") || pathValue.EndsWith(".woff2") || 
            pathValue.EndsWith(".ttf") || pathValue.EndsWith(".eot"))
            return "font";
        if (pathValue.StartsWith("/api/"))
            return "api";

        return "html";
    }

    /// <summary>
    /// Generate a cache key for the request
    /// </summary>
    private string GenerateCacheKey(HttpRequest request)
    {
        var keyBuilder = new StringBuilder();
        keyBuilder.Append(request.Path.Value);

        // Include relevant query parameters
        var relevantParams = request.Query
            .Where(q => !q.Key.Equals("t", StringComparison.OrdinalIgnoreCase) && 
                       !q.Key.Equals("nocache", StringComparison.OrdinalIgnoreCase))
            .OrderBy(q => q.Key);

        foreach (var param in relevantParams)
        {
            keyBuilder.Append($"&{param.Key}={param.Value}");
        }

        // Include culture for localized content
        var culture = request.Headers["Accept-Language"].FirstOrDefault() ?? "en-CA";
        keyBuilder.Append($"&culture={culture}");

        return Convert.ToBase64String(Encoding.UTF8.GetBytes(keyBuilder.ToString()));
    }

    /// <summary>
    /// Get cached response if available
    /// </summary>
    private async Task<CachedResponse?> GetCachedResponseAsync(string cacheKey)
    {
        try
        {
            return _cache.Get<CachedResponse>(cacheKey);
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Failed to retrieve cached response for key: {CacheKey}", cacheKey);
            return null;
        }
    }

    /// <summary>
    /// Check if cached response is still valid
    /// </summary>
    private bool IsValidCachedResponse(CachedResponse cachedResponse, Models.Entities.CacheHeaderConfiguration cacheConfig)
    {
        if (cachedResponse == null)
            return false;

        // Check if cache has expired
        var age = DateTime.UtcNow - cachedResponse.CachedAt;
        if (age > cacheConfig.MaxAge)
            return false;

        return true;
    }

    /// <summary>
    /// Handle conditional requests (304 Not Modified)
    /// </summary>
    private bool HandleConditionalRequest(HttpContext context, Models.Entities.CacheHeaderConfiguration cacheConfig, CachedResponse? cachedResponse)
    {
        var request = context.Request;
        var response = context.Response;

        // Handle If-None-Match (ETag)
        var ifNoneMatch = request.Headers["If-None-Match"].FirstOrDefault();
        if (!string.IsNullOrEmpty(ifNoneMatch) && ifNoneMatch == cacheConfig.ETag)
        {
            response.StatusCode = 304;
            response.Headers["ETag"] = cacheConfig.ETag;
            response.Headers["Cache-Control"] = cacheConfig.CacheControl;
            return true;
        }

        // Handle If-Modified-Since
        var ifModifiedSince = request.Headers["If-Modified-Since"].FirstOrDefault();
        if (!string.IsNullOrEmpty(ifModifiedSince) && 
            DateTime.TryParse(ifModifiedSince, out var modifiedSince) &&
            cacheConfig.LastModified.HasValue &&
            cacheConfig.LastModified.Value <= modifiedSince)
        {
            response.StatusCode = 304;
            response.Headers["Last-Modified"] = cacheConfig.LastModified.Value.ToString("R");
            response.Headers["Cache-Control"] = cacheConfig.CacheControl;
            return true;
        }

        return false;
    }

    /// <summary>
    /// Serve cached response
    /// </summary>
    private async Task ServeCachedResponse(HttpContext context, CachedResponse cachedResponse, Models.Entities.CacheHeaderConfiguration cacheConfig)
    {
        var response = context.Response;

        response.StatusCode = cachedResponse.StatusCode;
        response.ContentType = cachedResponse.ContentType;

        // Apply cached headers
        foreach (var header in cachedResponse.Headers)
        {
            if (!response.Headers.ContainsKey(header.Key))
            {
                response.Headers[header.Key] = header.Value;
            }
        }

        // Apply cache headers
        ApplyCacheHeaders(response, cacheConfig);

        // Add cache hit header for debugging
        if (_environment.IsDevelopment())
        {
            response.Headers["X-Cache"] = "HIT";
        }

        await response.Body.WriteAsync(cachedResponse.Body);

        _logger.LogDebug("Served cached response for {Path}", context.Request.Path);
    }

    /// <summary>
    /// Determine if response should be cached
    /// </summary>
    private bool ShouldCacheResponse(HttpRequest request, HttpResponse response)
    {
        // Only cache successful responses
        if (response.StatusCode != 200)
            return false;

        // Don't cache responses with Set-Cookie headers
        if (response.Headers.ContainsKey("Set-Cookie"))
            return false;

        // Don't cache responses that are already marked as no-cache
        var cacheControl = response.Headers["Cache-Control"].FirstOrDefault();
        if (!string.IsNullOrEmpty(cacheControl) && 
            (cacheControl.Contains("no-cache") || cacheControl.Contains("no-store")))
            return false;

        return true;
    }

    /// <summary>
    /// Cache the response
    /// </summary>
    private async Task CacheResponseAsync(string cacheKey, CachedResponse responseData, Models.Entities.CacheHeaderConfiguration cacheConfig)
    {
        try
        {
            var cacheOptions = new MemoryCacheEntryOptions
            {
                AbsoluteExpirationRelativeToNow = cacheConfig.MaxAge,
                Priority = CacheItemPriority.Normal
            };

            // Set sliding expiration for frequently accessed items
            if (cacheConfig.MaxAge > TimeSpan.FromHours(1))
            {
                cacheOptions.SlidingExpiration = TimeSpan.FromMinutes(30);
            }

            _cache.Set(cacheKey, responseData, cacheOptions);

            _logger.LogDebug("Cached response for key: {CacheKey}, expires in: {MaxAge}", 
                cacheKey, cacheConfig.MaxAge);
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Failed to cache response for key: {CacheKey}", cacheKey);
        }
    }

    /// <summary>
    /// Apply cache headers to response
    /// </summary>
    private void ApplyCacheHeaders(HttpResponse response, Models.Entities.CacheHeaderConfiguration cacheConfig)
    {
        // Cache-Control header
        if (!response.Headers.ContainsKey("Cache-Control"))
        {
            response.Headers["Cache-Control"] = cacheConfig.CacheControl;
        }

        // ETag header
        if (!string.IsNullOrEmpty(cacheConfig.ETag) && !response.Headers.ContainsKey("ETag"))
        {
            response.Headers["ETag"] = cacheConfig.ETag;
        }

        // Last-Modified header
        if (cacheConfig.LastModified.HasValue && !response.Headers.ContainsKey("Last-Modified"))
        {
            response.Headers["Last-Modified"] = cacheConfig.LastModified.Value.ToString("R");
        }

        // Vary headers
        if (cacheConfig.VaryHeaders.Any())
        {
            var existingVary = response.Headers["Vary"].FirstOrDefault();
            var varyHeaders = existingVary != null 
                ? existingVary.Split(',').Select(h => h.Trim()).ToList()
                : new List<string>();

            foreach (var varyHeader in cacheConfig.VaryHeaders)
            {
                if (!varyHeaders.Contains(varyHeader, StringComparer.OrdinalIgnoreCase))
                {
                    varyHeaders.Add(varyHeader);
                }
            }

            response.Headers["Vary"] = string.Join(", ", varyHeaders);
        }

        // Add cache miss header for debugging
        if (_environment.IsDevelopment() && !response.Headers.ContainsKey("X-Cache"))
        {
            response.Headers["X-Cache"] = "MISS";
        }
    }
}

/// <summary>
/// Represents a cached HTTP response
/// </summary>
public class CachedResponse
{
    public int StatusCode { get; set; }
    public string ContentType { get; set; } = string.Empty;
    public byte[] Body { get; set; } = Array.Empty<byte>();
    public Dictionary<string, string> Headers { get; set; } = new();
    public DateTime CachedAt { get; set; }
    public string ETag { get; set; } = string.Empty;
}