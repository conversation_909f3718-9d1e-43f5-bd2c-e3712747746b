Feature: admin-event-registrations-viewer. Docs path: Plan/Specs/admin-event-registrations-viewer. Please open Requirements.md, Design.md, and Tasks.md, read them fully, and keep their contents in memory. Starting with the first unchecked item in Tasks.md, execute each task one by one. After completing a task, mark its checkbox as "[x]". Continue until all tasks are complete. Then notify me and provide a concise manual test procedure to verify the implementation.

## Feature Overview

This feature enhances the existing Admin Calendar event modal with a comprehensive registration management system. Instead of creating a separate page, it integrates directly into the modal where admins already manage events, providing:

**Key Capabilities:**

-   **Registration Viewing**: See all event registrations with member details, status, and notes within the event modal
-   **Status Management**: Change registration status (Confirmed/Pending/Rejected) with immediate UI feedback
-   **Administrative Tools**: Add admin notes, mark attendance, export registration lists to CSV
-   **Seamless Integration**: New "Registrations" tab in existing Admin Calendar modal preserves all current functionality

**Why This Approach:**

-   **Context-Aware**: Admins view registrations while managing the event itself
-   **Single Interface**: No navigation between different pages required
-   **Existing Infrastructure**: Leverages the already-built `AdminController.GetEventRegistrations` endpoint
-   **Workflow Integration**: See registrations → manage capacity → update event details in one place

**Technical Foundation:**

-   Uses existing Admin Calendar modal with enhanced tab system
-   Lazy loading - registration data loads only when tab is accessed
-   Real-time updates via AJAX with comprehensive error handling
-   Maintains all existing event editing functionality without changes
-   Integrates with existing audit logging and security systems

The solution provides a professional event management interface that feels native to the existing admin experience while adding powerful registration oversight capabilities.
