using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.Extensions.Localization;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using System.Collections.Concurrent;
using System.Text.Json;
using ParaHockeyApp.Resources;

namespace ParaHockeyApp.Services
{
    /// <summary>
    /// Service for detecting missing localization keys at runtime and providing fallback behavior.
    /// Logs missing keys for translation team follow-up and maintains runtime stability.
    /// </summary>
    public interface ILocalizationKeyDetectionService
    {
        /// <summary>
        /// Gets a localized string and logs if the key is missing
        /// </summary>
        string GetLocalizedString(string key, string? culture = null, params object[] arguments);
        
        /// <summary>
        /// Gets all missing keys detected during runtime
        /// </summary>
        Task<List<MissingLocalizationKey>> GetMissingKeysAsync();
        
        /// <summary>
        /// Clears the missing keys cache
        /// </summary>
        Task ClearMissingKeysAsync();
        
        /// <summary>
        /// Exports missing keys to JSON for translation team
        /// </summary>
        Task<string> ExportMissingKeysAsync();
        
        /// <summary>
        /// Validates that all keys in LocalizationKeys class have corresponding resources
        /// </summary>
        Task<KeyValidationResult> ValidateAllKeysAsync();
    }
    
    public class LocalizationKeyDetectionService : ILocalizationKeyDetectionService
    {
        private readonly IStringLocalizer<SharedResourceMarker> _localizer;
        private readonly ILogger<LocalizationKeyDetectionService> _logger;
        private readonly LocalizationDetectionOptions _options;
        
        // Thread-safe collection to store missing keys
        private readonly ConcurrentDictionary<string, MissingLocalizationKey> _missingKeys = new();
        
        public LocalizationKeyDetectionService(
            IStringLocalizer<SharedResourceMarker> localizer,
            ILogger<LocalizationKeyDetectionService> logger,
            IOptions<LocalizationDetectionOptions> options)
        {
            _localizer = localizer;
            _logger = logger;
            _options = options.Value;
        }
        
        public string GetLocalizedString(string key, string? culture = null, params object[] arguments)
        {
            if (string.IsNullOrWhiteSpace(key))
            {
                _logger.LogWarning("Attempted to localize empty or null key");
                return "[EMPTY_KEY]";
            }
            
            try
            {
                var localizedString = _localizer[key, arguments];
                
                // Check if the key was found (ResourceNotFound indicates missing key)
                if (localizedString.ResourceNotFound)
                {
                    LogMissingKey(key, culture);
                    return GetFallbackText(key, arguments);
                }
                
                return localizedString.Value;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving localized string for key: {Key}", key);
                LogMissingKey(key, culture, ex.Message);
                return GetFallbackText(key, arguments);
            }
        }
        
        public Task<List<MissingLocalizationKey>> GetMissingKeysAsync()
        {
            var missingKeys = _missingKeys.Values
                .OrderBy(k => k.Key)
                .ToList();
            
            return Task.FromResult(missingKeys);
        }
        
        public Task ClearMissingKeysAsync()
        {
            _missingKeys.Clear();
            _logger.LogInformation("Missing localization keys cache cleared");
            return Task.CompletedTask;
        }
        
        public async Task<string> ExportMissingKeysAsync()
        {
            var missingKeys = await GetMissingKeysAsync();
            
            var exportData = new
            {
                ExportedAt = DateTime.UtcNow,
                TotalMissingKeys = missingKeys.Count,
                MissingKeys = missingKeys.Select(k => new
                {
                    k.Key,
                    k.FirstDetectedAt,
                    k.LastDetectedAt,
                    k.DetectionCount,
                    k.Culture,
                    k.ErrorMessage,
                    SuggestedFallback = GetFallbackText(k.Key),
                    Category = GetKeyCategory(k.Key)
                }).ToList()
            };
            
            var json = JsonSerializer.Serialize(exportData, new JsonSerializerOptions
            {
                WriteIndented = true,
                PropertyNamingPolicy = JsonNamingPolicy.CamelCase
            });
            
            _logger.LogInformation("Exported {Count} missing localization keys", missingKeys.Count);
            return json;
        }
        
        public async Task<KeyValidationResult> ValidateAllKeysAsync()
        {
            var result = new KeyValidationResult();
            var allKeys = GetAllLocalizationKeys();
            
            _logger.LogInformation("Validating {Count} localization keys", allKeys.Count);
            
            foreach (var key in allKeys)
            {
                try
                {
                    var localizedString = _localizer[key];
                    
                    if (localizedString.ResourceNotFound)
                    {
                        result.MissingKeys.Add(new MissingLocalizationKey
                        {
                            Key = key,
                            FirstDetectedAt = DateTime.UtcNow,
                            LastDetectedAt = DateTime.UtcNow,
                            DetectionCount = 1,
                            Culture = "validation",
                            ErrorMessage = "Key not found during validation"
                        });
                    }
                    else
                    {
                        result.ValidKeys.Add(key);
                    }
                }
                catch (Exception ex)
                {
                    result.ErrorKeys.Add(key, ex.Message);
                    _logger.LogError(ex, "Error validating localization key: {Key}", key);
                }
            }
            
            result.ValidationCompletedAt = DateTime.UtcNow;
            
            _logger.LogInformation("Localization validation completed. Valid: {Valid}, Missing: {Missing}, Errors: {Errors}",
                result.ValidKeys.Count, result.MissingKeys.Count, result.ErrorKeys.Count);
            
            return result;
        }
        
        private void LogMissingKey(string key, string? culture = null, string? errorMessage = null)
        {
            if (!_options.EnableMissingKeyDetection)
                return;
            
            var missingKey = _missingKeys.AddOrUpdate(key, 
                new MissingLocalizationKey
                {
                    Key = key,
                    FirstDetectedAt = DateTime.UtcNow,
                    LastDetectedAt = DateTime.UtcNow,
                    DetectionCount = 1,
                    Culture = culture ?? "unknown",
                    ErrorMessage = errorMessage
                },
                (existingKey, existing) =>
                {
                    existing.LastDetectedAt = DateTime.UtcNow;
                    existing.DetectionCount++;
                    if (!string.IsNullOrEmpty(errorMessage))
                        existing.ErrorMessage = errorMessage;
                    return existing;
                });
            
            if (_options.LogMissingKeys)
            {
                _logger.LogWarning("Missing localization key detected: {Key} (Culture: {Culture}, Count: {Count})", 
                    key, culture ?? "unknown", missingKey.DetectionCount);
            }
        }
        
        private string GetFallbackText(string key, params object[] arguments)
        {
            if (!_options.EnableFallbackText)
                return key;
            
            try
            {
                // Generate user-friendly fallback text from key
                var fallback = key.Split('.').LastOrDefault() ?? key;
                
                // Convert PascalCase to readable text
                fallback = System.Text.RegularExpressions.Regex.Replace(fallback, "([a-z])([A-Z])", "$1 $2");
                
                // Apply arguments if provided
                if (arguments?.Length > 0)
                {
                    try
                    {
                        fallback = string.Format(fallback, arguments);
                    }
                    catch
                    {
                        // If formatting fails, just return the fallback text
                    }
                }
                
                return _options.ShowFallbackIndicator ? $"[{fallback}]" : fallback;
            }
            catch
            {
                return _options.ShowFallbackIndicator ? $"[{key}]" : key;
            }
        }
        
        private string GetKeyCategory(string key)
        {
            if (key.StartsWith("Form.")) return "Forms";
            if (key.StartsWith("Validation.")) return "Validation";
            if (key.StartsWith("UI.")) return "User Interface";
            if (key.StartsWith("Page.")) return "Pages";
            if (key.StartsWith("Error.")) return "Errors";
            if (key.StartsWith("Success.")) return "Success Messages";
            if (key.StartsWith("A11y.")) return "Accessibility";
            if (key.StartsWith("DateTime.")) return "Date/Time";
            
            return "Other";
        }
        
        private List<string> GetAllLocalizationKeys()
        {
            var keys = new List<string>();
            
            // Use reflection to get all constants from LocalizationKeys class
            var localizationKeysType = typeof(LocalizationKeys);
            var nestedTypes = localizationKeysType.GetNestedTypes();
            
            foreach (var nestedType in nestedTypes)
            {
                var fields = nestedType.GetFields(System.Reflection.BindingFlags.Public | System.Reflection.BindingFlags.Static);
                foreach (var field in fields)
                {
                    if (field.FieldType == typeof(string) && field.IsLiteral)
                    {
                        var value = field.GetValue(null)?.ToString();
                        if (!string.IsNullOrEmpty(value))
                        {
                            keys.Add(value);
                        }
                    }
                }
            }
            
            return keys;
        }
    }
    
    /// <summary>
    /// Configuration options for localization key detection
    /// </summary>
    public class LocalizationDetectionOptions
    {
        public bool EnableMissingKeyDetection { get; set; } = true;
        public bool LogMissingKeys { get; set; } = true;
        public bool EnableFallbackText { get; set; } = true;
        public bool ShowFallbackIndicator { get; set; } = false; // Set to true in development
    }
    
    /// <summary>
    /// Represents a missing localization key detected at runtime
    /// </summary>
    public class MissingLocalizationKey
    {
        public string Key { get; set; } = string.Empty;
        public DateTime FirstDetectedAt { get; set; }
        public DateTime LastDetectedAt { get; set; }
        public int DetectionCount { get; set; }
        public string Culture { get; set; } = string.Empty;
        public string? ErrorMessage { get; set; }
    }
    
    /// <summary>
    /// Result of localization key validation
    /// </summary>
    public class KeyValidationResult
    {
        public List<string> ValidKeys { get; set; } = new();
        public List<MissingLocalizationKey> MissingKeys { get; set; } = new();
        public Dictionary<string, string> ErrorKeys { get; set; } = new();
        public DateTime ValidationCompletedAt { get; set; }
        
        public int TotalKeys => ValidKeys.Count + MissingKeys.Count + ErrorKeys.Count;
        public double ValidationSuccessRate => TotalKeys > 0 ? (double)ValidKeys.Count / TotalKeys * 100 : 0;
    }
}