using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Localization;
using ParaHockeyApp.DTOs;
using ParaHockeyApp.Resources;

namespace ParaHockeyApp.Controllers;

/// <summary>
/// Base controller for MVC controllers providing consistent error handling patterns
/// </summary>
public abstract class BaseMvcController : Controller
{
    protected readonly ILogger _logger;
    protected readonly IStringLocalizer<SharedResourceMarker> _localizer;

    protected BaseMvcController(ILogger logger, IStringLocalizer<SharedResourceMarker> localizer)
    {
        _logger = logger;
        _localizer = localizer;
    }

    /// <summary>
    /// Sets a localized error message in TempData for display to the user
    /// </summary>
    /// <param name="messageKey">The localization key for the error message</param>
    /// <param name="args">Optional arguments for string formatting</param>
    protected void SetErrorMessage(string messageKey, params object[] args)
    {
        TempData["ErrorMessage"] = _localizer[messageKey, args].Value;
    }

    /// <summary>
    /// Sets a localized success message in TempData for display to the user
    /// </summary>
    /// <param name="messageKey">The localization key for the success message</param>
    /// <param name="args">Optional arguments for string formatting</param>
    protected void SetSuccessMessage(string messageKey, params object[] args)
    {
        TempData["SuccessMessage"] = _localizer[messageKey, args].Value;
    }

    /// <summary>
    /// Handles exceptions consistently for MVC controllers
    /// </summary>
    /// <param name="ex">The exception to handle</param>
    /// <param name="operation">The operation that failed</param>
    /// <param name="redirectAction">Optional action to redirect to on error</param>
    /// <param name="redirectController">Optional controller to redirect to on error</param>
    /// <returns>ActionResult for error handling</returns>
    protected IActionResult HandleMvcError(Exception ex, string operation, string redirectAction = "Index", string redirectController = null)
    {
        var errorId = Guid.NewGuid().ToString();
        var controller = redirectController ?? ControllerContext.ActionDescriptor.ControllerName;
        
        _logger.LogError(ex, "Error in {Controller}.{Operation} for User {User}. ErrorId: {ErrorId}", 
            ControllerContext.ActionDescriptor.ControllerName, operation, User.Identity?.Name, errorId);

        SetErrorMessage("Error_UnexpectedError");

        return RedirectToAction(redirectAction, controller);
    }

    /// <summary>
    /// Handles exceptions for AJAX/JSON responses in MVC controllers
    /// </summary>
    /// <param name="ex">The exception to handle</param>
    /// <param name="operation">The operation that failed</param>
    /// <returns>JSON error response</returns>
    protected IActionResult HandleAjaxError(Exception ex, string operation)
    {
        var errorId = Guid.NewGuid().ToString();
        
        _logger.LogError(ex, "Error in {Controller}.{Operation} for User {User}. ErrorId: {ErrorId}", 
            ControllerContext.ActionDescriptor.ControllerName, operation, User.Identity?.Name, errorId);

        return Json(new { 
            success = false, 
            error = _localizer["Error_UnexpectedError"].Value,
            errorId = errorId
        });
    }
}

/// <summary>
/// Base controller for API controllers providing consistent error handling patterns
/// </summary>
[ApiController]
public abstract class BaseApiController : ControllerBase
{
    protected readonly ILogger _logger;
    protected readonly IStringLocalizer<SharedResourceMarker> _localizer;

    protected BaseApiController(ILogger logger, IStringLocalizer<SharedResourceMarker> localizer)
    {
        _logger = logger;
        _localizer = localizer;
    }

    /// <summary>
    /// Handles exceptions consistently for API controllers
    /// </summary>
    /// <param name="ex">The exception to handle</param>
    /// <param name="operation">The operation that failed</param>
    /// <returns>Standardized API error response</returns>
    protected IActionResult HandleApiError(Exception ex, string operation)
    {
        var errorId = Guid.NewGuid().ToString();
        
        _logger.LogError(ex, "Error in {Controller}.{Operation} for User {User}. ErrorId: {ErrorId}", 
            ControllerContext.ActionDescriptor.ControllerName, operation, User.Identity?.Name, errorId);

        var errorResponse = ErrorResponseModel.Create(
            _localizer["Error_UnexpectedError"].Value, 
            false, 
            500);
        errorResponse.ErrorId = errorId;

        return StatusCode(500, errorResponse);
    }

    /// <summary>
    /// Returns a standardized validation error response
    /// </summary>
    /// <param name="message">The validation error message</param>
    /// <returns>BadRequest with ErrorResponseModel</returns>
    protected IActionResult ValidationError(string message)
    {
        return BadRequest(ErrorResponseModel.CreateValidationError(message));
    }

    /// <summary>
    /// Returns a standardized validation error response using localization
    /// </summary>
    /// <param name="messageKey">The localization key for the validation error</param>
    /// <param name="args">Optional arguments for string formatting</param>
    /// <returns>BadRequest with ErrorResponseModel</returns>
    protected IActionResult ValidationError(string messageKey, params object[] args)
    {
        var message = _localizer[messageKey, args].Value;
        return BadRequest(ErrorResponseModel.CreateValidationError(message));
    }

    /// <summary>
    /// Returns a standardized success response
    /// </summary>
    /// <param name="data">The data to return</param>
    /// <returns>Ok response with consistent format</returns>
    protected IActionResult SuccessResponse(object data = null)
    {
        return Ok(new { success = true, data = data });
    }

    /// <summary>
    /// Returns a standardized success response with a message
    /// </summary>
    /// <param name="messageKey">The localization key for the success message</param>
    /// <param name="data">Optional data to include</param>
    /// <param name="args">Optional arguments for string formatting</param>
    /// <returns>Ok response with message and data</returns>
    protected IActionResult SuccessResponse(string messageKey, object data = null, params object[] args)
    {
        var message = _localizer[messageKey, args].Value;
        return Ok(new { 
            success = true, 
            message = message,
            data = data 
        });
    }
}