# ===============================================
# Setup AdminTI Master Admin in All Environments
# Runs the SQL scripts on Dev, Test, and Production
# ===============================================

Write-Host "🚀 Setting up AdminTI Master Admin in all environments..." -ForegroundColor Green

# Database connection strings (update these as needed)
$environments = @{
    "Development" = "Server=localhost;Database=ParaHockeyDev;Integrated Security=true;TrustServerCertificate=true;"
    "Test" = "Server=your-test-server;Database=ParaHockeyTest;Integrated Security=true;TrustServerCertificate=true;"
    "Production" = "Server=your-prod-server;Database=ParaHockeyProd;Integrated Security=true;TrustServerCertificate=true;"
}

$sqlFiles = @(
    "Ensure-AdminTI-Master-Admin.sql",
    "Update-Migration-History-AdminType.sql"
)

foreach ($env in $environments.Keys) {
    Write-Host "`n🌍 Processing $env environment..." -ForegroundColor Yellow
    
    $connectionString = $environments[$env]
    
    foreach ($sqlFile in $sqlFiles) {
        if (Test-Path $sqlFile) {
            Write-Host "  📋 Executing $sqlFile..." -ForegroundColor Cyan
            
            try {
                sqlcmd -S $connectionString -i $sqlFile -v
                Write-Host "  ✅ $sqlFile completed successfully" -ForegroundColor Green
            }
            catch {
                Write-Host "  ❌ Error executing $sqlFile`: $($_.Exception.Message)" -ForegroundColor Red
            }
        } else {
            Write-Host "  ⚠️ $sqlFile not found" -ForegroundColor Yellow
        }
    }
}

Write-Host "`n🎉 AdminTI Master Admin setup completed for all environments!" -ForegroundColor Green
Write-Host "`n📝 What was done:" -ForegroundColor White
Write-Host "   ✅ Added AdminType column (0=Disabled, 3=Normal, 9=Master)" -ForegroundColor White
Write-Host "   ✅ Ensured <EMAIL> has AdminType = 9" -ForegroundColor White
Write-Host "   ✅ Updated migration history" -ForegroundColor White
Write-Host "`n⚠️ Note: Update connection strings in this script for your actual servers" -ForegroundColor Yellow