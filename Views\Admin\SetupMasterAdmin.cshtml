@{
    ViewData["Title"] = Localizer["SetupMasterAdmin"];
}

<div class="container mt-4">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h3 class="mb-0">
                        <i class="fas fa-user-shield"></i> @SharedLocalizer["SetupMasterAdmin"]
                    </h3>
                </div>
                <div class="card-body">
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle"></i>
                        <strong>@SharedLocalizer["FirstTimeSetup"]</strong> @SharedLocalizer["NoAdminsExist"]
                    </div>
                    
                    <p>@SharedLocalizer["YouAreLoggedInAs"] <strong>@ViewBag.CurrentUser</strong></p>
                    <p>@SharedLocalizer["Email"]: <strong>@ViewBag.CurrentEmail</strong></p>
                    
                    <div class="alert alert-warning">
                        <i class="fas fa-exclamation-triangle"></i>
                        <strong>@SharedLocalizer["Important"]</strong> @SharedLocalizer["MasterAdminCannotBeRemoved"]
                    </div>
                    
                    <form method="post" asp-action="SetupMasterAdmin">
                        @Html.AntiForgeryToken()
                        <input type="hidden" name="confirm" value="true" />
                        
                        <div class="d-grid gap-2">
                            <button type="submit" class="btn btn-primary btn-lg">
                                <i class="fas fa-user-plus"></i> @SharedLocalizer["SetupMasterAdminAccount"]
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>