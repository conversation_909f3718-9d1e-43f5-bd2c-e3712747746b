# ParaHockey Registration Auto-Fill Script
# This script generates realistic test data for member registration forms

param(
    [Parameter(Mandatory=$true)]
    [ValidateSet("<PERSON>", "Development", "Elite", "Coach", "Volunteer")]
    [string]$MemberType,
    
    [Parameter(Mandatory=$false)]
    [int]$Count = 1
)

# Counter file to track incremental numbers
$counterFile = "registration-counter.txt"
$baseCounter = 1

# Read existing counter or start at 1
if (Test-Path $counterFile) {
    $baseCounter = [int](Get-Content $counterFile)
}

# Sample data pools
$firstNames = @("<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>")
$lastNames = @("Tremblay", "Gagnon", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Beaulieu")

$cities = @("Montréal", "Québec", "Laval", "Gatineau", "Longueuil", "Sherbrooke", "Saguenay", "Trois-Rivières", "Terrebonne", "Saint-Jean-sur-Richelieu")
$addresses = @("123 rue Principale", "456 avenue des Érables", "789 boulevard Saint-Laurent", "321 rue de la Paix", "654 avenue du Parc", "987 rue Notre-Dame", "147 boulevard René-Lévesque", "258 rue Sainte-Catherine", "369 avenue Papineau", "741 rue Saint-Denis")

function Get-RandomPostalCode {
    $letters = "ABCDEFGHIJKLMNOPQRSTUVWXYZ"
    $l1 = $letters[(Get-Random -Maximum 26)]
    $n1 = Get-Random -Maximum 10
    $l2 = $letters[(Get-Random -Maximum 26)]
    $n2 = Get-Random -Maximum 10
    $l3 = $letters[(Get-Random -Maximum 26)]
    $n3 = Get-Random -Maximum 10
    return "$l1$n1$l2 $n2$l3$n3"
}

function Get-RandomPhone {
    $area = Get-Random -Minimum 400 -Maximum 999
    $exchange = Get-Random -Minimum 200 -Maximum 999
    $number = Get-Random -Minimum 1000 -Maximum 9999
    return "($area) $exchange-$number"
}

function Get-RandomEmail {
    param($firstName, $lastName, $counter)
    $domains = @("gmail.com", "outlook.com", "yahoo.ca", "hotmail.com", "videotron.ca")
    $domain = $domains[(Get-Random -Maximum $domains.Count)]
    return "$firstName.$lastName$counter@$domain".ToLower()
}

function Get-RandomBirthDate {
    param($memberType)
    $today = Get-Date
    switch ($memberType) {
        "Junior" { 
            # Age 8-17
            $yearsAgo = Get-Random -Minimum 8 -Maximum 18
            return $today.AddYears(-$yearsAgo).ToString("yyyy-MM-dd")
        }
        "Development" { 
            # Age 16-25
            $yearsAgo = Get-Random -Minimum 16 -Maximum 26
            return $today.AddYears(-$yearsAgo).ToString("yyyy-MM-dd")
        }
        "Elite" { 
            # Age 18-35
            $yearsAgo = Get-Random -Minimum 18 -Maximum 36
            return $today.AddYears(-$yearsAgo).ToString("yyyy-MM-dd")
        }
        "Coach" { 
            # Age 25-55
            $yearsAgo = Get-Random -Minimum 25 -Maximum 56
            return $today.AddYears(-$yearsAgo).ToString("yyyy-MM-dd")
        }
        "Volunteer" { 
            # Age 18-70
            $yearsAgo = Get-Random -Minimum 18 -Maximum 71
            return $today.AddYears(-$yearsAgo).ToString("yyyy-MM-dd")
        }
    }
}

function Get-RegistrationTypeId {
    param($memberType)
    switch ($memberType) {
        "Junior" { return 1 }
        "Development" { return 2 }
        "Elite" { return 3 }
        "Coach" { return 4 }
        "Volunteer" { return 5 }
    }
}

Write-Host "🏒 ParaHockey Registration Auto-Fill Generator" -ForegroundColor Cyan
Write-Host "=====================================================" -ForegroundColor Cyan
Write-Host ""
Write-Host "Member Type: $MemberType" -ForegroundColor Yellow
Write-Host "Count: $Count" -ForegroundColor Yellow
Write-Host ""

for ($i = 0; $i -lt $Count; $i++) {
    $currentCounter = $baseCounter + $i
    
    # Generate random data
    $firstName = ($firstNames | Get-Random) + $currentCounter
    $lastName = ($lastNames | Get-Random) + $currentCounter
    $email = Get-RandomEmail -firstName $firstName -lastName $lastName -counter $currentCounter
    $phone = Get-RandomPhone
    $birthDate = Get-RandomBirthDate -memberType $MemberType
    $address = $addresses | Get-Random
    $city = $cities | Get-Random
    $postalCode = Get-RandomPostalCode
    $registrationTypeId = Get-RegistrationTypeId -memberType $MemberType
    
    # Gender (1=Male, 2=Female, 3=Other)
    $genderId = Get-Random -Minimum 1 -Maximum 4
    
    # Province (1=QC is most common)
    $provinceId = if ((Get-Random -Maximum 10) -lt 7) { 1 } else { Get-Random -Minimum 1 -Maximum 14 }
    
    # Phone Type (1=Mobile, 2=Other)
    $phoneTypeId = if ((Get-Random -Maximum 10) -lt 8) { 1 } else { 2 }
    
    Write-Host "👤 Member #$currentCounter ($MemberType)" -ForegroundColor Green
    Write-Host "   First Name: $firstName" -ForegroundColor White
    Write-Host "   Last Name: $lastName" -ForegroundColor White
    Write-Host "   Email: $email" -ForegroundColor White
    Write-Host "   Phone: $phone" -ForegroundColor White
    Write-Host "   Birth Date: $birthDate" -ForegroundColor White
    Write-Host "   Address: $address" -ForegroundColor White
    Write-Host "   City: $city" -ForegroundColor White
    Write-Host "   Postal Code: $postalCode" -ForegroundColor White
    Write-Host "   Registration Type ID: $registrationTypeId" -ForegroundColor White
    Write-Host "   Gender ID: $genderId" -ForegroundColor White
    Write-Host "   Province ID: $provinceId" -ForegroundColor White
    Write-Host "   Phone Type ID: $phoneTypeId" -ForegroundColor White
    
    # Generate parent data for Junior members
    if ($MemberType -eq "Junior") {
        Write-Host "   👨‍👩‍👧‍👦 Parent Information:" -ForegroundColor Cyan
        $parentFirstName = ($firstNames | Get-Random) + "_Parent$currentCounter"
        $parentLastName = $lastName  # Same last name as child
        $parentEmail = Get-RandomEmail -firstName $parentFirstName -lastName $parentLastName -counter $currentCounter
        $parentPhone = Get-RandomPhone
        
        Write-Host "      Parent Name: $parentFirstName $parentLastName" -ForegroundColor White
        Write-Host "      Parent Email: $parentEmail" -ForegroundColor White
        Write-Host "      Parent Phone: $parentPhone" -ForegroundColor White
    }
    
    # Generate emergency contact for non-Junior members
    if ($MemberType -ne "Junior") {
        Write-Host "   🚨 Emergency Contact:" -ForegroundColor Cyan
        $emergencyFirstName = ($firstNames | Get-Random) + "_Emergency$currentCounter"
        $emergencyLastName = $lastName  # Often same family
        $emergencyEmail = Get-RandomEmail -firstName $emergencyFirstName -lastName $emergencyLastName -counter $currentCounter
        $emergencyPhone = Get-RandomPhone
        
        Write-Host "      Emergency Name: $emergencyFirstName $emergencyLastName" -ForegroundColor White
        Write-Host "      Emergency Email: $emergencyEmail" -ForegroundColor White
        Write-Host "      Emergency Phone: $emergencyPhone" -ForegroundColor White
    }
    
    Write-Host ""
    
    # Create JavaScript snippet for auto-filling the form
    $jsSnippet = @"
// Auto-fill registration form for $firstName $lastName ($MemberType)
document.getElementById('FirstName').value = '$firstName';
document.getElementById('LastName').value = '$lastName';
document.getElementById('Email').value = '$email';
document.getElementById('Phone').value = '$phone';
document.getElementById('DateOfBirth').value = '$birthDate';
document.getElementById('Address').value = '$address';
document.getElementById('City').value = '$city';
document.getElementById('PostalCode').value = '$postalCode';
document.getElementById('RegistrationTypeId').value = '$registrationTypeId';
document.getElementById('GenderId').value = '$genderId';
document.getElementById('ProvinceId').value = '$provinceId';
document.getElementById('PhoneTypeId').value = '$phoneTypeId';
"@

    if ($MemberType -eq "Junior") {
        $jsSnippet += @"

// Parent information for Junior
document.getElementById('Parents_0__FirstName').value = '$parentFirstName';
document.getElementById('Parents_0__LastName').value = '$parentLastName';
document.getElementById('Parents_0__Email').value = '$parentEmail';
document.getElementById('Parents_0__Phone').value = '$parentPhone';
"@
    } else {
        $jsSnippet += @"

// Emergency contact for non-Junior
document.getElementById('EmergencyContact_FirstName').value = '$emergencyFirstName';
document.getElementById('EmergencyContact_LastName').value = '$emergencyLastName';
document.getElementById('EmergencyContact_Email').value = '$emergencyEmail';
document.getElementById('EmergencyContact_Phone').value = '$emergencyPhone';
"@
    }
    
    # Save JavaScript snippet to file
    $jsFileName = "autofill-$MemberType-$currentCounter.js"
    $jsSnippet | Out-File -FilePath $jsFileName -Encoding UTF8
    
    Write-Host "💾 JavaScript file saved: $jsFileName" -ForegroundColor Magenta
    Write-Host "   Copy and paste this JavaScript in browser console to auto-fill the form" -ForegroundColor Gray
    Write-Host ""
}

# Update counter file
$newCounter = $baseCounter + $Count
$newCounter | Out-File -FilePath $counterFile -Encoding UTF8

Write-Host "✅ Generated $Count member(s) of type '$MemberType'" -ForegroundColor Green
Write-Host "📊 Next counter will start at: $newCounter" -ForegroundColor Yellow
Write-Host ""
Write-Host "🌐 To use:" -ForegroundColor Cyan
Write-Host "   1. Open http://localhost:5285/Members/Register" -ForegroundColor White
Write-Host "   2. Open browser developer tools (F12)" -ForegroundColor White
Write-Host "   3. Go to Console tab" -ForegroundColor White
Write-Host "   4. Copy and paste the JavaScript from the .js file" -ForegroundColor White
Write-Host "   5. Press Enter to auto-fill the form" -ForegroundColor White
Write-Host "   6. Click Register to submit" -ForegroundColor White