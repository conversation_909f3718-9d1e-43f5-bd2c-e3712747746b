# Design Document

## Overview

This feature introduces a new member options page that serves as an intermediate dashboard between member login validation and specific actions (edit profile, view calendar). The design leverages existing admin member detail view components while creating a member-specific interface that maintains security, usability, and consistency with the current application architecture.

## Architecture

### Component Structure

```
MembersController
├── Enhanced Login Action (existing)
├── New Options Action
├── Enhanced Edit Action (existing)
├── New Calendar View Action
└── Enhanced UI Components
    ├── Member Options Page
    ├── Read-Only Member Details Component
    ├── Member Navigation Menu
    └── Read-Only Calendar Component
```

### Data Flow

1. **Member Login**: Member enters validation code → Validation succeeds → Redirect to Options page
2. **Options Page Load**: Load member data → Display in read-only format → Show navigation options
3. **Edit Navigation**: Click Edit → Navigate to existing edit page → Maintain session
4. **Calendar Navigation**: Click Calendar → Navigate to read-only calendar → Show events without edit capabilities

## Components and Interfaces

### 1. New Controller Actions

#### MembersController.Options

```csharp
[HttpGet]
public async Task<IActionResult> Options()
{
    // Verify member session and validation
    var memberSession = GetCurrentMemberSession();
    if (memberSession == null || !IsValidSession(memberSession))
    {
        return RedirectToAction("Login", "Members");
    }

    // Get member details
    var member = await _memberService.GetMemberWithDetailsAsync(memberSession.MemberId);
    var memberDetailsViewModel = _mapper.Map<MemberDetailsViewModel>(member);

    return View(memberDetailsViewModel);
}
```

#### MembersController.CalendarReadOnly

```csharp
[HttpGet]
public async Task<IActionResult> CalendarReadOnly()
{
    // Verify member session
    var memberSession = GetCurrentMemberSession();
    if (memberSession == null || !IsValidSession(memberSession))
    {
        return RedirectToAction("Login", "Members");
    }

    // Get calendar events (read-only)
    var events = await _eventService.GetUpcomingEventsAsync();
    var calendarViewModel = new CalendarReadOnlyViewModel
    {
        Events = events,
        IsReadOnly = true,
        MemberName = memberSession.MemberName
    };

    return View(calendarViewModel);
}
```

### 2. Enhanced Session Management

#### MemberSession Model Enhancement

```csharp
public class MemberSession
{
    public int MemberId { get; set; }
    public string MemberName { get; set; }
    public string Email { get; set; }
    public DateTime ValidationTime { get; set; }
    public DateTime ExpiryTime { get; set; }
    public bool IsActive => DateTime.Now < ExpiryTime;
    public string SessionToken { get; set; }
}
```

### 3. View Models

#### MemberDetailsViewModel

```csharp
public class MemberDetailsViewModel
{
    public int Id { get; set; }
    public string FirstName { get; set; }
    public string LastName { get; set; }
    public string FullName => $"{FirstName} {LastName}";
    public string Email { get; set; }
    public string Phone { get; set; }
    public DateTime DateOfBirth { get; set; }
    public int Age => DateTime.Now.Year - DateOfBirth.Year;
    public string Address { get; set; }
    public string City { get; set; }
    public string Province { get; set; }
    public string PostalCode { get; set; }
    public string RegistrationTypeName { get; set; }
    public DateTime DateCreated { get; set; }
    public string Status { get; set; }
    public bool IsActive { get; set; }
}
```

#### CalendarReadOnlyViewModel

```csharp
public class CalendarReadOnlyViewModel
{
    public IEnumerable<EventSummaryDto> Events { get; set; }
    public bool IsReadOnly { get; set; } = true;
    public string MemberName { get; set; }
    public DateTime CurrentDate { get; set; } = DateTime.Now;
}
```

## User Interface Design

### 1. Member Options Page Layout

```html
<div class="container-fluid mt-4">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col">
            <h1 class="h2 text-primary">
                <i class="fas fa-user-circle"></i> @Model.FullName - Member Dashboard
            </h1>
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item">Member Portal</li>
                    <li class="breadcrumb-item active">Dashboard</li>
                </ol>
            </nav>
        </div>
    </div>

    <!-- Navigation Menu -->
    <div class="row mb-4">
        <div class="col">
            <div class="card">
                <div class="card-body">
                    <h5 class="card-title">What would you like to do?</h5>
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <a href="@Url.Action("Edit", "Members")" class="btn btn-primary btn-lg w-100">
                                <i class="fas fa-edit"></i> Edit My Profile
                            </a>
                        </div>
                        <div class="col-md-6 mb-3">
                            <a href="@Url.Action("CalendarReadOnly", "Members")" class="btn btn-info btn-lg w-100">
                                <i class="fas fa-calendar"></i> View Calendar
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Member Information Display -->
    <div class="row">
        <div class="col-md-8">
            <!-- Personal Information Card -->
            <div class="card mb-4">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0"><i class="fas fa-user"></i> Personal Information</h5>
                </div>
                <div class="card-body">
                    <!-- Member details in read-only format -->
                </div>
            </div>
        </div>
        <div class="col-md-4">
            <!-- Quick Info Sidebar -->
            <div class="card">
                <div class="card-header bg-info text-white">
                    <h5 class="mb-0"><i class="fas fa-info-circle"></i> Quick Info</h5>
                </div>
                <div class="card-body">
                    <!-- Status, registration date, etc. -->
                </div>
            </div>
        </div>
    </div>
</div>
```

### 2. Read-Only Calendar Design

- Use existing calendar component but disable all edit functionality
- Hide admin-only buttons and actions
- Show events with read-only event details
- Include navigation back to Options page

### 3. Responsive Considerations

#### Mobile Layout (< 768px)
- Stack navigation buttons vertically
- Simplify member information display
- Use collapsible sections for detailed information
- Ensure touch-friendly button sizing (minimum 44px height)

#### Tablet Layout (768px - 1024px)
- Maintain card-based layout
- Adjust button sizes for touch interaction
- Optimize spacing for tablet screens

#### Desktop Layout (> 1024px)
- Full multi-column layout
- Larger buttons and cards
- Utilize full screen real estate

## Security Design

### 1. Session Validation

```csharp
private MemberSession GetCurrentMemberSession()
{
    var sessionToken = HttpContext.Session.GetString("MemberSessionToken");
    if (string.IsNullOrEmpty(sessionToken))
        return null;

    var session = _sessionService.ValidateSession(sessionToken);
    return session?.IsActive == true ? session : null;
}
```

### 2. Data Access Control

- Only show data for the authenticated member
- Prevent access to other members' information
- Log all access attempts for audit purposes
- Implement proper error handling for unauthorized access

### 3. Session Timeout Handling

- Implement JavaScript-based session timeout warnings
- Graceful redirect to login on session expiry
- Clear session data on logout or timeout

## Navigation Flow

```
Member Login Page
    ↓ (Successful validation)
Member Options Page ←─┐
    ├─→ Edit Profile   │
    │   ↓              │
    │   Edit Page ─────┘
    │
    ├─→ View Calendar
    │   ↓
    │   Read-Only Calendar ─────┐
    │                           │
    └───────────────────────────┘
```

## Implementation Considerations

### 1. Code Reusability

- Leverage existing admin member detail partial views
- Create reusable read-only components
- Share styling and layout patterns with admin interface

### 2. Performance

- Implement proper caching for member data
- Optimize database queries for member information retrieval
- Use lazy loading for calendar events

### 3. Accessibility

- Ensure proper ARIA labels and roles
- Maintain keyboard navigation support
- Use semantic HTML elements
- Provide alternative text for icons and images

### 4. Localization

- Support both French and English
- Use consistent localization patterns with existing application
- Ensure all user-facing text is localized

## Testing Strategy

### 1. Unit Tests

- Session validation logic
- Member data retrieval and mapping
- Security access control methods

### 2. Integration Tests

- End-to-end member login to options flow
- Navigation between options, edit, and calendar
- Session timeout and security scenarios

### 3. UI Tests

- Responsive design across devices
- Accessibility compliance
- Cross-browser compatibility

### 4. Security Tests

- Unauthorized access attempts
- Session hijacking prevention
- Data exposure validation