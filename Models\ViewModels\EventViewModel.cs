using System;
using System.ComponentModel.DataAnnotations;

namespace ParaHockey.Models.ViewModels
{
    public class EventViewModel
    {
        public int Id { get; set; }
        
        [Display(Name = "EventTitle")]
        public string Title { get; set; } = string.Empty;
        
        [Display(Name = "EventDescription")]
        public string? Description { get; set; }
        
        [Display(Name = "StartDate")]
        public DateTime StartDate { get; set; }
        
        [Display(Name = "EndDate")]
        public DateTime EndDate { get; set; }
        
        [Display(Name = "Location")]
        public string? Location { get; set; }
        
        public bool IsAllDay { get; set; }
        
        public bool RequiresRegistration { get; set; }
        
        public int MaxParticipants { get; set; }
        
        public DateTime? RegistrationDeadline { get; set; }
        
        public bool IsPublished { get; set; }
        
        public int CurrentRegistrations { get; set; }
        
        public int AvailableSpots { get; set; }
        
        public bool IsFull { get; set; }
        
        public bool IsRegistrationOpen { get; set; }
        
        public string DateRangeDisplay { get; set; } = string.Empty;
        
        public string? CategoryName { get; set; }
        
        public string? CategoryColor { get; set; }
        
        public string? ContactPerson { get; set; }
        
        public string? ContactEmail { get; set; }
        
        public string? ContactPhone { get; set; }
        
        // For tracking if current user is registered
        public bool IsUserRegistered { get; set; }
    }
}