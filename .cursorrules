# ParaHockey Project - Cursor AI Instructions

## Project Overview

This is a ParaHockey member registration web application built with ASP.NET Core 8.0 MVC. The user is a beginner developer who prefers simple, clear explanations and robust, reusable solutions.

## Critical Project Requirements

### Multilingual Support (MANDATORY)

-   ALL user-facing text in Views (.cshtml) MUST use localization keys: `@Localizer["MyText"]`
-   ALL validation messages in DTOs MUST use localization keys: `ErrorMessageResourceName = "MyValidationError"`
-   Resource files are in /Resources/ folder (SharedResource.resx, SharedResource.en-CA.resx)
-   This is non-negotiable for the entire application

### Architecture Requirements

-   Follow ASP.NET Core MVC pattern strictly
-   Use Views/\_ViewStart.cshtml to point to correct MVC layout
-   All navigation links must use MVC routing (asp-controller/asp-action) NOT Razor Pages routing
-   MVC views use ~/Views/Shared/\_Layout.cshtml NOT ~/Pages/Shared/\_Layout.cshtml
-   Entity Framework for data access
-   AutoMapper for DTO mapping

## Development Approach

### Problem-Solving Hierarchy

1. Check what libraries/tools are ALREADY loaded in the project
2. Use the simplest, most proven solution first
3. For common problems (input masking, validation), use established libraries
4. Only write custom code as a last resort
5. If jQuery is loaded, prefer jQuery plugins over vanilla JS libraries

### Input Masking/Formatting Rules

-   Canadian Postal Code: Use jQuery Mask Plugin with pattern 'L0L 0L0' (jQuery already loaded)
-   Phone Numbers: Use jQuery Mask Plugin with pattern '(*************'
-   DON'T write custom keypress handlers or complex validation
-   DON'T use experimental libraries when proven ones exist

### File Organization (ASP.NET Core MVC Standard)

```
/Controllers/          # MVC Controllers
/Views/               # Razor Views
  /Shared/            # Shared layouts and partials
  /_ViewStart.cshtml  # REQUIRED for MVC layout
/Models/              # Domain models and ViewModels
  /Entities/          # Entity Framework entities
  /Configuration/     # Configuration classes
/DTOs/                # Data Transfer Objects
/Services/            # Business logic services
/Resources/           # Localization resource files
/wwwroot/            # Static files (CSS, JS, images)
  /css/              # Stylesheets
  /js/               # JavaScript files
  /lib/              # Third-party libraries
```

### Coding Best Practices

#### Controllers

-   Keep controllers thin - delegate business logic to services
-   Use dependency injection for services
-   Return appropriate ActionResults
-   Use async/await for database operations

#### Views

-   Always use strongly-typed views with models
-   Use Tag Helpers (asp-for, asp-controller, asp-action)
-   Include proper validation spans: `<span asp-validation-for="Property" class="text-danger"></span>`
-   Use Bootstrap classes for styling
-   All text must use localization: `@SharedLocalizer["Key"]`

#### Models/DTOs

-   Use Data Annotations for validation
-   Include localized error messages: `[Required(ErrorMessageResourceName = "RequiredField")]`
-   Follow naming conventions (PascalCase for properties)

#### Services

-   Use interfaces for dependency injection
-   Keep methods focused and single-purpose
-   Use async/await for database operations

#### JavaScript

-   Place scripts in @section Scripts { } at bottom of views
-   Use jQuery if already loaded (common in ASP.NET Core)
-   Prefer established libraries over custom solutions
-   Test thoroughly in browser before considering it "done"

## Common Pitfalls to Avoid

### Layout Issues

-   Missing Views/\_ViewStart.cshtml causes CSS loading problems
-   Mixing MVC and Razor Pages routing breaks navigation
-   Always check layout path in \_ViewStart.cshtml

### Validation Issues

-   Don't forget client-side validation scripts: `@{await Html.RenderPartialAsync("_ValidationScriptsPartial");}`
-   Always include validation spans in forms
-   Use proper input types (type="email", type="tel", etc.)

### Localization Issues

-   Never hardcode user-facing text
-   Always add new keys to both .resx files (default and en-CA)
-   Test in both languages

### JavaScript Issues

-   Don't write complex custom input masking - use proven libraries
-   Check browser console for errors before declaring success
-   Test on actual devices, not just desktop

## Communication Style

-   Explain concepts in simple, non-technical terms
-   Provide step-by-step instructions
-   Always explain WHY a solution is chosen
-   Ask for confirmation before making significant changes
-   Flag potential security or performance concerns

### Testing Instructions Format (MANDATORY)

After implementing any feature or fix, ALWAYS provide a "🧪 Test Now" section with:

-   Clear emoji header: "🧪 **Test Now**"
-   Specific step-by-step testing instructions
-   Expected behaviors with checkmarks ✅ for what should work
-   Cross marks ❌ for what should be blocked/prevented
-   Visual cues like "→ should format to..." or "→ should show red border"
-   Console debugging steps when applicable
-   Multiple test scenarios covering different use cases
-   Clear pass/fail criteria for each test

Example format:

```
## 🧪 **Test Now**

Please refresh the page and test:

### **Phone Number** 📞
- Type "abc" → should be blocked completely ❌
- Type "1234567890" → should format to "(*************" ✅

### **Email** 📧
- Type "test" → red border ❌
- Type "<EMAIL>" → green border ✅
```

## Deployment Notes

-   Azure DevOps pipeline triggers on MAIN branch pushes
-   Development happens on BackendDev branch
-   Pipeline file: /azure-pipelines.yml
-   Deployment target: SIMBA server

## Testing Requirements

-   Always test forms completely before considering them done
-   Test validation (both client and server-side)
-   Test responsive design on mobile
-   Test localization in both languages
-   Verify all navigation links work

## Remember

-   The user is learning - provide educational explanations
-   Prefer robust, reusable solutions over quick fixes
-   When in doubt, use the simplest solution that works
-   Don't reinvent the wheel for common problems
-   Always consider the user's skill level in explanations
