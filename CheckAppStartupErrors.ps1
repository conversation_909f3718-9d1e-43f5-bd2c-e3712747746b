# PowerShell script to diagnose ParaHockey startup issues
Write-Host "🔍 CHECKING PARAHOCKEY APP STARTUP ERRORS" -ForegroundColor Yellow
Write-Host "=========================================" -ForegroundColor Yellow

# Check Event Logs
Write-Host "`n📋 Checking Windows Event Logs..." -ForegroundColor Cyan
$events = Get-EventLog -LogName Application -Newest 50 | Where-Object {
    $_.Source -like "*ASP.NET*" -or 
    $_.Source -like "*IIS*" -or 
    $_.Source -like "*.NET Runtime*" -or
    $_.EntryType -eq "Error"
}

if ($events) {
    Write-Host "Found $($events.Count) relevant events:" -ForegroundColor Yellow
    $events | Select-Object TimeGenerated, Source, Message | Format-List
} else {
    Write-Host "No recent error events found" -ForegroundColor Green
}

# Check IIS Application Pool Status
Write-Host "`n🏊 Checking IIS Application Pools..." -ForegroundColor Cyan
Import-Module WebAdministration -ErrorAction SilentlyContinue
$pools = Get-ChildItem IIS:\AppPools | Where-Object { $_.Name -like "*ParaHockey*" }
foreach ($pool in $pools) {
    Write-Host "Pool: $($pool.Name) - State: $($pool.State)" -ForegroundColor Yellow
}

# Check stdout logs
Write-Host "`n📄 Checking stdout logs..." -ForegroundColor Cyan
$logPaths = @(
    "C:\inetpub\ParaHockey\Test\logs",
    "C:\inetpub\ParaHockey\Production\logs",
    "C:\inetpub\ParaHockey\Test",
    "C:\inetpub\ParaHockey\Production"
)

foreach ($path in $logPaths) {
    if (Test-Path $path) {
        $stdoutLogs = Get-ChildItem "$path\*stdout*.log" -ErrorAction SilentlyContinue | Sort-Object LastWriteTime -Descending | Select-Object -First 5
        if ($stdoutLogs) {
            Write-Host "`nFound stdout logs in $path:" -ForegroundColor Yellow
            foreach ($log in $stdoutLogs) {
                Write-Host "`n--- $($log.Name) ---" -ForegroundColor Magenta
                Get-Content $log.FullName -Tail 50 | Write-Host
            }
        }
    }
}

# Check web.config
Write-Host "`n⚙️ Checking web.config settings..." -ForegroundColor Cyan
$webConfigs = @(
    "C:\inetpub\ParaHockey\Test\web.config",
    "C:\inetpub\ParaHockey\Production\web.config"
)

foreach ($config in $webConfigs) {
    if (Test-Path $config) {
        Write-Host "`nFound: $config" -ForegroundColor Yellow
        [xml]$xml = Get-Content $config
        $aspNetCore = $xml.configuration.system.webServer.aspNetCore
        Write-Host "stdoutLogEnabled: $($aspNetCore.stdoutLogEnabled)"
        Write-Host "stdoutLogFile: $($aspNetCore.stdoutLogFile)"
        Write-Host "processPath: $($aspNetCore.processPath)"
        Write-Host "arguments: $($aspNetCore.arguments)"
        Write-Host "hostingModel: $($aspNetCore.hostingModel)"
    }
}

Write-Host "`n✅ Diagnostic check complete!" -ForegroundColor Green
Write-Host "Look for any error messages above, especially in the Event Logs and stdout logs" -ForegroundColor Yellow