namespace ParaHockeyApp.Services
{
    /// <summary>
    /// Service for handling and translating errors into user-friendly messages
    /// </summary>
    public interface IErrorHandlingService
    {
        /// <summary>
        /// Handles an exception and returns a user-friendly error response
        /// </summary>
        /// <param name="exception">The exception to handle</param>
        /// <param name="context">Additional context about where the error occurred</param>
        /// <returns>User-friendly error information</returns>
        ErrorResponse HandleException(Exception exception, string context = "");

        /// <summary>
        /// Handles multiple exceptions and consolidates them into a single response
        /// </summary>
        /// <param name="exceptions">List of exceptions to handle</param>
        /// <param name="context">Additional context</param>
        /// <returns>Consolidated error response</returns>
        ErrorResponse HandleMultipleExceptions(List<Exception> exceptions, string context = "");

        /// <summary>
        /// Validates import data and returns validation errors
        /// </summary>
        /// <param name="data">Data to validate</param>
        /// <returns>Validation error response if invalid, null if valid</returns>
        ValidationErrorResponse? ValidateImportData(Dictionary<string, object?> data);

        /// <summary>
        /// Gets recovery suggestions for common error scenarios
        /// </summary>
        /// <param name="errorType">Type of error that occurred</param>
        /// <returns>List of suggested recovery actions</returns>
        List<RecoveryAction> GetRecoveryActions(ErrorType errorType);

        /// <summary>
        /// Logs error with appropriate level based on severity
        /// </summary>
        /// <param name="exception">The exception to log</param>
        /// <param name="context">Context information</param>
        /// <param name="additionalData">Additional data to include in log</param>
        void LogError(Exception exception, string context, Dictionary<string, object>? additionalData = null);
    }

    /// <summary>
    /// User-friendly error response
    /// </summary>
    public class ErrorResponse
    {
        public string Title { get; set; } = string.Empty;
        public string Message { get; set; } = string.Empty;
        public ErrorType Type { get; set; }
        public ErrorSeverity Severity { get; set; }
        public List<string> Details { get; set; } = new();
        public List<RecoveryAction> RecoveryActions { get; set; } = new();
        public string? TechnicalDetails { get; set; }
        public string? CorrelationId { get; set; }
        public DateTime OccurredAt { get; set; } = DateTime.UtcNow;
        public bool IsRetryable { get; set; }
        public int? RetryAfterSeconds { get; set; }
    }

    /// <summary>
    /// Validation error response for import data
    /// </summary>
    public class ValidationErrorResponse
    {
        public string Title { get; set; } = "Validation Errors";
        public string Message { get; set; } = string.Empty;
        public Dictionary<string, List<string>> FieldErrors { get; set; } = new();
        public List<string> GeneralErrors { get; set; } = new();
        public int ErrorCount => FieldErrors.Values.Sum(v => v.Count) + GeneralErrors.Count;
        public bool HasErrors => ErrorCount > 0;
    }

    /// <summary>
    /// Recovery action that users can take
    /// </summary>
    public class RecoveryAction
    {
        public string Title { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public string ActionType { get; set; } = string.Empty; // "retry", "contact", "fix", "ignore"
        public string? ActionUrl { get; set; }
        public Dictionary<string, string> Parameters { get; set; } = new();
        public bool IsPrimary { get; set; }
    }

    /// <summary>
    /// Types of errors that can occur
    /// </summary>
    public enum ErrorType
    {
        FileProcessing,
        DataValidation,
        DatabaseError,
        NetworkError,
        Authentication,
        Authorization,
        Business,
        System,
        Concurrency,
        Performance
    }

    /// <summary>
    /// Error severity levels
    /// </summary>
    public enum ErrorSeverity
    {
        Low,        // User can continue with warnings
        Medium,     // User should address but can proceed
        High,       // User must address before proceeding
        Critical    // System-level error requiring intervention
    }
}