# Requirements: Admin Event Registration Viewer

## Functional Requirements

### FR1: Registration Viewing Interface

-   **FR1.1** <PERSON><PERSON> can view all registrations for an event within the Admin Calendar event modal
-   **FR1.2** Registration list displays member name, email, registration status, and dates
-   **FR1.3** Registration details include guest count, member notes, and attendance status
-   **FR1.4** Interface shows total confirmed participants vs. event capacity

### FR2: Registration Management Actions

-   **FR2.1** <PERSON><PERSON> can mark registrations as confirmed, pending, or rejected
-   **FR2.2** <PERSON><PERSON> can add administrative notes to registrations
-   **FR2.3** <PERSON><PERSON> can mark attendance for past events
-   **FR2.4** <PERSON><PERSON> can view member contact details for event coordination

### FR3: Modal Integration

-   **FR3.1** Registration viewer integrates seamlessly into existing Admin Calendar event modal
-   **FR3.2** <PERSON>dal maintains existing event editing functionality while adding registration tab
-   **FR3.3** Registration data loads only when registration tab is accessed (performance)
-   **FR3.4** Modal adapts to events with no registrations vs. events with many registrations

### FR4: Data Display & Filtering

-   **FR4.1** Registrations grouped by status (Confirmed, Pending, Waitlisted, Cancelled)
-   **FR4.2** Option to filter registrations by status or registration date
-   **FR4.3** Export registration list to CSV for external communication
-   **FR4.4** Real-time registration count updates as admins manage registrations

## Non-Functional Requirements

### NFR1: Performance & Scalability

-   **NFR1.1** Registration data loads within 2 seconds for events with up to 100 registrations
-   **NFR1.2** Modal remains responsive when toggling between event details and registrations
-   **NFR1.3** AJAX operations for status updates complete within 1 second
-   **NFR1.4** Lazy loading prevents unnecessary API calls when tab not accessed

### NFR2: User Experience

-   **NFR2.1** Interface maintains consistency with existing Admin Calendar modal design
-   **NFR2.2** Registration actions provide immediate visual feedback
-   **NFR2.3** Mobile-responsive design for tablet-based event management
-   **NFR2.4** Clear empty states when events have no registrations

### NFR3: Security & Authorization

-   **NFR3.1** Only authenticated admin users can access registration viewer
-   **NFR3.2** All registration management actions are logged in audit trail
-   **NFR3.3** Admin actions respect existing role-based access control
-   **NFR3.4** Registration data is properly sanitized before display

### NFR4: Integration & Compatibility

-   **NFR4.1** Integration preserves existing Admin Calendar functionality
-   **NFR4.2** Uses existing EventService and AdminController infrastructure
-   **NFR4.3** Maintains localization support for French/English
-   **NFR4.4** Compatible with environment-specific configurations

## Acceptance Criteria

### AC1: Basic Registration Viewing

-   **AC1.1** When admin opens event modal, a "Registrations" tab is visible for events that require registration
-   **AC1.2** Clicking "Registrations" tab loads and displays all event registrations grouped by status
-   **AC1.3** Each registration shows member name, email, status badge, registration date, and guest count
-   **AC1.4** Registration count matches actual participant numbers

### AC2: Registration Management

-   **AC2.1** Admin can change registration status (Confirmed ↔ Pending ↔ Rejected) with immediate visual update
-   **AC2.2** Admin can add/edit administrative notes for any registration
-   **AC2.3** Admin can mark attendance for past events with checkbox toggle
-   **AC2.4** All management actions create audit log entries

### AC3: Modal Integration & Performance

-   **AC3.1** Event modal opens normally with existing event details in primary tab
-   **AC3.2** Registration tab loads data only when clicked (lazy loading)
-   **AC3.3** Switching between tabs is seamless with no page refresh
-   **AC3.4** Modal can be closed and reopened without losing registration management state

### AC4: Data Export & Communication

-   **AC4.1** "Export Registrations" button generates CSV with all registration details
-   **AC4.2** CSV includes member contact info, registration status, notes, and guest details
-   **AC4.3** Export respects current filter settings (e.g., only confirmed registrations)
-   **AC4.4** Email links in registration list open default email client with member address

## User Stories

### US1: Event Coordination

> **As an** admin managing an upcoming tournament
> **I want to** see all registered participants in the event modal
> **So that** I can coordinate logistics and communicate with attendees

### US2: Registration Management

> **As an** admin reviewing event sign-ups
> **I want to** confirm pending registrations and add notes
> **So that** I can manage event capacity and track special requirements

### US3: Attendance Tracking

> **As an** admin after an event
> **I want to** mark who actually attended from the registration list
> **So that** I can maintain accurate participation records

### US4: Communication Facilitation

> **As an** admin coordinating event details
> **I want to** export registration lists and access member contact info
> **So that** I can send updates and important information to participants

## Business Rules

### BR1: Registration Status Management

-   Only confirmed registrations count toward event capacity
-   Pending registrations can be bulk-confirmed if space allows
-   Rejected registrations remain visible for audit purposes
-   Cancelled registrations show cancellation date and reason

### BR2: Data Access & Privacy

-   Admin notes are internal and not visible to members
-   Member notes from registration are always visible to admins
-   Contact information is displayed but not editable from this interface
-   Export functionality respects data privacy requirements

### BR3: Event Capacity Integration

-   Registration viewer shows real-time capacity: "45/50 confirmed participants"
-   Capacity warnings appear when approaching event limits
-   Waitlisted registrations are clearly distinguished from confirmed
-   Admins can override capacity for special circumstances

### BR4: Audit & Compliance

-   All registration status changes create audit log entries
-   Administrative notes are tracked with timestamp and admin name
-   Attendance marking creates permanent attendance records
-   Export actions are logged for data access compliance
