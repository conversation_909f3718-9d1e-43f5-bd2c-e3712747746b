# Implementation Tasks – Admin Upcoming Events Modal Fix

> **Phase 0 – Preparation**
> - [ ] Confirm exact modal ID and JS function names in `Views/Admin/Calendar.cshtml` and `site.js`.

## Phase 1 – Front-End Markup
1. [ ] Update `Views/Admin/Calendar.cshtml` "Upcoming Events" loop:
   - Wrap each `.event-item` in an anchor with class `upcoming-event-link` and `data-event-id` attribute.
   - Ensure inner layout/CSS remains unchanged.
2. [ ] Add `role="button"`, `tabindex="0"`, and `aria-label` localisation key to anchor for accessibility.

## Phase 2 – JavaScript Interaction
3. [ ] In `wwwroot/js/site.js` (or relevant file):
   - Add delegated click listener on `.upcoming-event-link` (see design).
   - On click, call existing `showAdminEventModal(eventId)` function; if not present, refactor eventClick handler to expose reusable function.
4. [ ] Add keydown listener for `Enter` and `Space` keys to trigger click programmatically.

## Phase 3 – Localization & Resources
5. [ ] Add any new ARIA or tooltip text to `SharedResource.resx` and `SharedResource.en-CA.resx`.

## Phase 4 – Testing
6. [ ] Manual UI tests across latest Chrome, Firefox, Edge; desktop + mobile viewport.
7. [ ] Keyboard navigation & screen reader test (NVDA/VoiceOver).
8. [ ] Regression test: clicking calendar grid events still opens modal.
9. [ ] Write/Update unit or integration tests (e.g., Selenium) that verify sidebar click opens modal and modal content matches expected event.

## Phase 5 – Code Review & Deployment
10. [ ] Submit PR; ensure CI passes.
11. [ ] QA on staging; run full regression including localisation.
12. [ ] Merge to `main` → triggers Azure pipeline for production deployment.

---

### Estimate
* **Dev:** 2–3 hrs
* **QA:** 1 hr

---

### Dependencies
* FullCalendar initialisation script
* `/Admin/GetEventDetails/{id}` API endpoint (confirm exists) 

---

### Implementation Note for Coding AI
Please mark each task checkbox as completed (`[x]`) in this `tasks.md` file **immediately after finishing that task**. This ensures progress is transparently tracked throughout implementation. 