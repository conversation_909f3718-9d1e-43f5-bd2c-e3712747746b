using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Localization;
using System.Globalization;
using ParaHockeyApp.Models;
using System.Diagnostics;
using Microsoft.AspNetCore.Authentication;
using Microsoft.AspNetCore.Authentication.OpenIdConnect;
using Microsoft.AspNetCore.Authentication.Cookies;
using Microsoft.Identity.Web;
using ParaHockeyApp.Services;

namespace ParaHockeyApp.Controllers
{
    /// <summary>
    /// Home controller - redirects to Test controller for backbone verification
    /// </summary>
    public class HomeController : BaseMvcController
    {
        private readonly IEventService _eventService;
        private readonly IEmptyStateService _emptyStateService;
        private readonly IUserContextService _userContextService;

        public HomeController(ILogger<HomeController> logger, IStringLocalizer<ParaHockeyApp.Resources.SharedResourceMarker> localizer, IEventService eventService, IEmptyStateService emptyStateService, IUserContextService userContextService)
            : base(logger, localizer)
        {
            _eventService = eventService;
            _emptyStateService = emptyStateService;
            _userContextService = userContextService;
        }

        /// <summary>
        /// Default home page - redirect to Test controller
        /// </summary>
        public IActionResult Index()
        {
            // The home page should just display the welcome view.
            // The redirect to the test controller has been removed.
            return View();
        }

        /// <summary>
        /// Privacy page (required by default template)
        /// </summary>
        public IActionResult Privacy()
        {
            return View();
        }

        /// <summary>
        /// Public calendar page - accessible without authentication
        /// </summary>
        [HttpGet]
        public async Task<IActionResult> PublicCalendar()
        {
            try
            {
                // Get event categories for the filter dropdown (sorted alphabetically with Other at end)
                var eventCategories = await _eventService.GetAllEventCategoriesSortedAsync();
                
                // Get upcoming events for the sidebar (published only)
                var upcomingEvents = await _eventService.GetUpcomingEventsAsync(5);
                
                // Get recent events for the sidebar (published only)
                var recentEvents = await _eventService.GetRecentEventsAsync(5);
                
                // Get statistics for published events only
                var publishedEvents = await _eventService.GetEventCountAsync(true);
                
                ViewBag.EventCategories = eventCategories;
                ViewBag.UpcomingEvents = upcomingEvents;
                ViewBag.RecentEvents = recentEvents;
                ViewBag.PublishedEvents = publishedEvents;
                ViewBag.IsPublicView = true; // Flag to control behavior differences
                
                // Add empty state support for public calendar events
                ViewBag.UpcomingEventsEmptyState = _emptyStateService.GetEmptyState("events", "upcoming");
                ViewBag.RecentEventsEmptyState = _emptyStateService.GetEmptyState("events", "recent");
                
                _logger.LogInformation("Public calendar page accessed");
                
                return View();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error loading public calendar");
                SetErrorMessage("ErrorLoadingCalendar");
                return RedirectToAction(nameof(Index));
            }
        }

        /// <summary>
        /// Get calendar events for public access (published events only, no authentication required)
        /// </summary>
        [HttpGet]
        public async Task<IActionResult> GetPublicCalendarEvents(DateTime? start = null, DateTime? end = null, int? categoryId = null)
        {
            try
            {
                var startDate = start ?? DateTime.Now.AddMonths(-1);
                var endDate = end ?? DateTime.Now.AddMonths(6);

                // Get only published events for public access
                var events = await _eventService.GetCalendarEventsJsonAsync(startDate, endDate, true, categoryId); // true = published only
                return Json(events);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error loading public calendar events");
                return Json(new { error = _localizer["ApiFailedToLoadEvents"].Value });
            }
        }

        /// <summary>
        /// Get subscribable calendar events (excludes Practice and Tentative events)
        /// Used by the Events/Subscribe page calendar
        /// </summary>
        [HttpGet]
        public async Task<IActionResult> GetSubscribableCalendarEvents(DateTime? start = null, DateTime? end = null, int? categoryId = null)
        {
            try
            {
                var startDate = start ?? DateTime.Now.AddMonths(-1);
                var endDate = end ?? DateTime.Now.AddMonths(6);

                // Get only subscribable events (requires registration, excludes Practice/Tentative)
                var events = await _eventService.GetSubscribableCalendarEventsJsonAsync(startDate, endDate, categoryId);
                return Json(events);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error loading subscribable calendar events");
                return Json(new { error = _localizer["ApiFailedToLoadEvents"].Value });
            }
        }

        /// <summary>
        /// Get public event details for display in modal (no authentication required)
        /// Enhanced to include user context for smart UI rendering
        /// </summary>
        [HttpGet]
        [ResponseCache(NoStore = true, Location = ResponseCacheLocation.None, Duration = 0)]
        public async Task<IActionResult> GetPublicEventDetails(int id)
        {
            try
            {
                // CRITICAL: Add aggressive cache-busting headers to prevent stale authentication state
                Response.Headers["Cache-Control"] = "no-cache, no-store, must-revalidate, max-age=0";
                Response.Headers["Pragma"] = "no-cache";
                Response.Headers["Expires"] = "Thu, 01 Jan 1970 00:00:00 GMT";
                
                // DEBUG: Log authentication state and request details
                _logger.LogInformation("🔍 GetPublicEventDetails - Event ID: {EventId}", id);
                _logger.LogInformation("🔍 Request Headers: {Headers}", string.Join(", ", Request.Headers.Select(h => $"{h.Key}={h.Value}")));
                _logger.LogInformation("🔍 Request Cookies: {Cookies}", string.Join(", ", Request.Cookies.Select(c => $"{c.Key}={c.Value.Substring(0, Math.Min(c.Value.Length, 20))}...")));
                _logger.LogInformation("🔍 Session ID: {SessionId}", HttpContext.Session.Id ?? "NULL");
                _logger.LogInformation("🔍 Session IsAvailable: {SessionAvailable}", HttpContext.Session.IsAvailable);
                _logger.LogInformation("🔍 User.Identity.IsAuthenticated: {IsAuthenticated}", User.Identity.IsAuthenticated);
                _logger.LogInformation("🔍 User.Identity.Name: {UserName}", User.Identity.Name ?? "NULL");
                _logger.LogInformation("🔍 User.Identity.AuthenticationType: {AuthType}", User.Identity.AuthenticationType ?? "NULL");
                
                // Get current user context (handles both authenticated and anonymous users)
                var userContext = _userContextService.GetCurrentUser();
                
                // DEBUG: Log user context details
                _logger.LogInformation("🔍 UserContext.IsAdmin: {IsAdmin}", userContext.IsAdmin);
                _logger.LogInformation("🔍 UserContext.IsSystem: {IsSystem}", userContext.IsSystem);
                _logger.LogInformation("🔍 UserContext.MemberId: {MemberId}", userContext.MemberId?.ToString() ?? "NULL");
                _logger.LogInformation("🔍 UserContext.AdminId: {AdminId}", userContext.AdminId?.ToString() ?? "NULL");
                _logger.LogInformation("🔍 UserContext.Email: {Email}", userContext.Email ?? "NULL");
                
                // Use enhanced service method that includes user registration state
                var eventDetails = await _eventService.GetEventDetailsWithUserContextAsync(id, userContext);
                
                // DEBUG: Log the returned event details
                if (eventDetails != null)
                {
                    _logger.LogInformation("🔍 EventDetails.UserState: {UserState}", eventDetails.UserState);
                    _logger.LogInformation("🔍 EventDetails.IsUserRegistered: {IsUserRegistered}", eventDetails.IsUserRegistered);
                    _logger.LogInformation("🔍 EventDetails.RequiresRegistration: {RequiresRegistration}", eventDetails.RequiresRegistration);
                }
                
                // Return 404 if event not found or not published
                if (eventDetails == null)
                {
                    var eventEntity = await _eventService.GetEventByIdAsync(id);
                    if (eventEntity == null || !eventEntity.IsPublished || !eventEntity.IsActive)
                    {
                        return NotFound();
                    }
                }

                return Json(eventDetails);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error loading public event details for event {EventId}", id);
                return StatusCode(500, new { error = _localizer["ErrorLoadingEventDetails"].Value });
            }
        }

        /// <summary>
        /// 404 Not Found error page
        /// </summary>
        public new IActionResult NotFound()
        {
            Response.StatusCode = 404;
            return View();
        }

        /// <summary>
        /// General error page
        /// </summary>
        public IActionResult Error()
        {
            var errorResponse = HttpContext.Items["ErrorResponse"] as ParaHockeyApp.DTOs.ErrorResponseModel;
            
            if (errorResponse != null)
            {
                ViewBag.ErrorMessage = errorResponse.UserMessage;
                ViewBag.ErrorCode = errorResponse.StatusCode.ToString();
                ViewBag.ErrorId = errorResponse.ErrorId;
            }
            else
            {
                ViewBag.ErrorMessage = _localizer["Error_UnexpectedError"];
                ViewBag.ErrorCode = "500";
            }
            
            return View();
        }

        /// <summary>
        /// Logout action - clears authentication and redirects home
        /// </summary>
        public IActionResult Logout()
        {
            _logger.LogInformation("User {User} logging out", User.Identity?.Name ?? "Unknown");
            
            // Clear all sessions and cache
            HttpContext.Session.Clear();
            
            // Add cache-busting headers
            Response.Headers.Add("Cache-Control", "no-cache, no-store, must-revalidate");
            Response.Headers.Add("Pragma", "no-cache");
            Response.Headers.Add("Expires", "0");
            
            // Sign out from both the cookie scheme and Azure AD
            return SignOut(
                new AuthenticationProperties 
                { 
                    RedirectUri = Url.Action("Index", "Home") + "?logged_out=true"
                },
                CookieAuthenticationDefaults.AuthenticationScheme,
                OpenIdConnectDefaults.AuthenticationScheme);
        }
    }
}