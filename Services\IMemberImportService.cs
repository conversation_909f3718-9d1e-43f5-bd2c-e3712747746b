using Microsoft.AspNetCore.Http;
using ParaHockeyApp.Models.Entities;
using ParaHockeyApp.DTOs;

namespace ParaHockeyApp.Services
{
    /// <summary>
    /// Service for importing member data from Excel files
    /// </summary>
    public interface IMemberImportService
    {
        /// <summary>
        /// Validates an uploaded Excel file before processing
        /// </summary>
        /// <param name="file">The uploaded Excel file</param>
        /// <returns>True if file is valid for processing</returns>
        Task<FileValidationResult> ValidateFileAsync(IFormFile file);

        /// <summary>
        /// Parses and stages member data from Excel file into TempMembers table
        /// </summary>
        /// <param name="file">The uploaded Excel file</param>
        /// <param name="uploadedBy">Username of the person uploading</param>
        /// <returns>The import batch ID for tracking</returns>
        Task<int> ParseAndStageAsync(IFormFile file, string uploadedBy);

        /// <summary>
        /// Gets summary information for an import batch
        /// </summary>
        /// <param name="batchId">The import batch ID</param>
        /// <returns>Summary of import batch statistics</returns>
        Task<ImportBatchSummary> GetBatchSummaryAsync(int batchId);

        /// <summary>
        /// Gets temp members by status for queue processing
        /// </summary>
        /// <param name="batchId">The import batch ID</param>
        /// <param name="status">The temp member status to filter by</param>
        /// <returns>List of temp members with the specified status</returns>
        Task<List<TempMember>> GetQueueAsync(int batchId, TempMemberStatus status);

        /// <summary>
        /// Updates batch statistics after processing temp members
        /// </summary>
        /// <param name="batchId">The import batch ID</param>
        /// <returns>Task for async operation</returns>
        Task UpdateBatchStatisticsAsync(int batchId);

        /// <summary>
        /// Processes multi-file import with relationship matching
        /// </summary>
        /// <param name="request">Multi-file import request</param>
        /// <param name="uploadedBy">Username of the person uploading</param>
        /// <returns>The import batch ID and relationship matching results</returns>
        Task<(int batchId, RelationshipMatchingResult matchingResult)> ProcessMultiFileImportAsync(MultiFileImportRequest request, string uploadedBy);

        /// <summary>
        /// Validates multiple files for multi-file import
        /// </summary>
        /// <param name="request">Multi-file import request</param>
        /// <returns>Validation results for all files</returns>
        Task<MultiFileValidationResult> ValidateMultiFileImportAsync(MultiFileImportRequest request);

        /// <summary>
        /// Gets unmatched relationship records for manual review
        /// </summary>
        /// <param name="batchId">The import batch ID</param>
        /// <returns>List of unmatched records that need manual review</returns>
        Task<List<UnmatchedRelationshipRecord>> GetUnmatchedRelationshipRecordsAsync(int batchId);
    }

    /// <summary>
    /// Result of file validation
    /// </summary>
    public class FileValidationResult
    {
        public bool IsValid { get; set; }
        public List<string> Errors { get; set; } = new();
        public string? FileName { get; set; }
        public long FileSizeBytes { get; set; }
        public int EstimatedRowCount { get; set; }
    }

    /// <summary>
    /// Summary of an import batch
    /// </summary>
    public class ImportBatchSummary
    {
        public int ImportBatchId { get; set; }
        public string FileName { get; set; } = string.Empty;
        public DateTime UploadedAtUtc { get; set; }
        public string UploadedBy { get; set; } = string.Empty;
        public int TotalRows { get; set; }
        public int CreatedCount { get; set; }
        public int DuplicateCount { get; set; }
        public int NeedsFixCount { get; set; }
        public int MergedCount { get; set; }
        public int RejectedCount { get; set; }
        public int ReadyToCreateCount { get; set; }
        public string Status { get; set; } = string.Empty;
        public string? ErrorMessage { get; set; }
    }

    /// <summary>
    /// Configuration settings for member import
    /// </summary>
    public class MemberImportConfig
    {
        public long MaxFileSizeBytes { get; set; } = 10 * 1024 * 1024; // 10 MB
        public int MaxRowCount { get; set; } = 10000;
        public int BatchSize { get; set; } = 500; // Process in batches for performance
        public bool ValidateHeaders { get; set; } = true;
        public List<string> RequiredHeaders { get; set; } = new()
        {
            "Prénom", "Nom", "Numéro HCR"
        };
        public List<string> OptionalHeaders { get; set; } = new()
        {
            "Courriel", "Numéro de téléphone", "Date de naissance", "Numéro d'unité", "Numéro de rue", "Rue", 
            "Ville", "Code Postal", "Identité de genre", "Province", "Statut du membre", "Actif", "Pays"
        };
    }

    /// <summary>
    /// Result of multi-file validation
    /// </summary>
    public class MultiFileValidationResult
    {
        public bool IsValid { get; set; }
        public FileValidationResult? MemberFileResult { get; set; }
        public FileValidationResult? ParentFileResult { get; set; }
        public FileValidationResult? EmergencyContactFileResult { get; set; }
        public List<string> GeneralErrors { get; set; } = new();

        public List<string> AllErrors
        {
            get
            {
                var errors = new List<string>(GeneralErrors);
                if (MemberFileResult != null) errors.AddRange(MemberFileResult.Errors);
                if (ParentFileResult != null) errors.AddRange(ParentFileResult.Errors);
                if (EmergencyContactFileResult != null) errors.AddRange(EmergencyContactFileResult.Errors);
                return errors;
            }
        }
    }

    /// <summary>
    /// Represents an unmatched relationship record for manual review
    /// </summary>
    public class UnmatchedRelationshipRecord
    {
        public string HcrNumber { get; set; } = string.Empty;
        public ImportFileType FileType { get; set; }
        public string FirstName { get; set; } = string.Empty;
        public string LastName { get; set; } = string.Empty;
        public string? Email { get; set; }
        public string? Phone { get; set; }
        public string? Relationship { get; set; }
        public Dictionary<string, string> RawData { get; set; } = new();
        public List<TempMember> PotentialMatches { get; set; } = new();
    }
}