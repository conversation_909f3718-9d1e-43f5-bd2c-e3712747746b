using ParaHockeyApp.Models.Entities;

namespace ParaHockeyApp.Services
{
    /// <summary>
    /// Service for processing member import files (CSV/Excel)
    /// </summary>
    public interface IMemberImportService
    {
        /// <summary>
        /// Parses Excel/CSV file and stages data in TempMembers table
        /// </summary>
        /// <param name="file">Uploaded file</param>
        /// <param name="uploadedBy">Admin email who uploaded the file</param>
        /// <returns>Import batch ID</returns>
        Task<Guid> ParseAndStageAsync(IFormFile file, string uploadedBy);

        /// <summary>
        /// Gets summary statistics for an import batch
        /// </summary>
        /// <param name="batchId">Import batch ID</param>
        /// <returns>Batch summary with statistics</returns>
        Task<ImportBatchSummary> GetBatchSummaryAsync(int batchId);

        /// <summary>
        /// Gets temp members in a specific queue (status)
        /// </summary>
        /// <param name="batchId">Import batch ID</param>
        /// <param name="status">Status to filter by</param>
        /// <returns>List of temp members in the queue</returns>
        Task<List<TempMember>> GetQueueAsync(int batchId, TempMemberStatus status);

        /// <summary>
        /// Validates file format and size before processing
        /// </summary>
        /// <param name="file">File to validate</param>
        /// <returns>True if valid, false otherwise</returns>
        Task<bool> ValidateFileAsync(IFormFile file);

        /// <summary>
        /// Gets list of all import batches for admin overview
        /// </summary>
        /// <returns>List of import batch summaries</returns>
        Task<List<ImportBatchSummary>> GetAllBatchesAsync();

        /// <summary>
        /// Updates batch statistics after processing operations
        /// </summary>
        /// <param name="batchId">Batch ID to update</param>
        Task UpdateBatchStatisticsAsync(int batchId);
    }

    /// <summary>
    /// Summary information for an import batch
    /// </summary>
    public class ImportBatchSummary
    {
        public int ImportBatchId { get; set; }
        public Guid ImportBatchGuid { get; set; }
        public string FileName { get; set; } = string.Empty;
        public DateTime UploadedAtUtc { get; set; }
        public string UploadedBy { get; set; } = string.Empty;
        public int TotalRows { get; set; }
        public int CreatedCount { get; set; }
        public int DuplicateCount { get; set; }
        public int NeedsFixCount { get; set; }
        public int MergedCount { get; set; }
        public int RejectedCount { get; set; }
        public int ReadyToCreateCount { get; set; }
        public string Status { get; set; } = string.Empty;
        public string? ErrorMessage { get; set; }
        
        public int ProcessedCount => CreatedCount + MergedCount + RejectedCount;
        public int PendingCount => TotalRows - ProcessedCount;
        public bool IsCompleted => Status == "Completed";
        public bool HasErrors => !string.IsNullOrEmpty(ErrorMessage);
    }
}