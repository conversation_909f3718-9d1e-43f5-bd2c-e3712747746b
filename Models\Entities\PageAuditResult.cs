using System.ComponentModel.DataAnnotations;

namespace ParaHockeyApp.Models.Entities
{
    /// <summary>
    /// Represents the results of a comprehensive audit for a specific page
    /// Tracks security, accessibility, performance, and localization findings
    /// </summary>
    public class PageAuditResult : BaseEntity
    {
        /// <summary>
        /// Foreign key to the page this audit result belongs to
        /// </summary>
        [Required]
        public int PageInfoId { get; set; }

        /// <summary>
        /// Version of the audit (incremented on each audit run)
        /// </summary>
        [Required]
        public int AuditVersion { get; set; }

        /// <summary>
        /// User who performed the audit
        /// </summary>
        [Required]
        [StringLength(100)]
        public string AuditedBy { get; set; } = string.Empty;

        /// <summary>
        /// Overall audit status
        /// </summary>
        [Required]
        public AuditStatus Status { get; set; }

        /// <summary>
        /// Overall audit score (0-100)
        /// </summary>
        [Required]
        [Range(0, 100)]
        public int OverallScore { get; set; }

        /// <summary>
        /// Security audit score (0-100)
        /// </summary>
        [Required]
        [Range(0, 100)]
        public int SecurityScore { get; set; }

        /// <summary>
        /// Accessibility audit score (0-100)
        /// </summary>
        [Required]
        [Range(0, 100)]
        public int AccessibilityScore { get; set; }

        /// <summary>
        /// Performance audit score (0-100)
        /// </summary>
        [Required]
        [Range(0, 100)]
        public int PerformanceScore { get; set; }

        /// <summary>
        /// Localization audit score (0-100)
        /// </summary>
        [Required]
        [Range(0, 100)]
        public int LocalizationScore { get; set; }

        /// <summary>
        /// Number of critical issues found
        /// </summary>
        [Required]
        public int CriticalIssues { get; set; }

        /// <summary>
        /// Number of high priority issues found
        /// </summary>
        [Required]
        public int HighIssues { get; set; }

        /// <summary>
        /// Number of medium priority issues found
        /// </summary>
        [Required]
        public int MediumIssues { get; set; }

        /// <summary>
        /// Number of low priority issues found
        /// </summary>
        [Required]
        public int LowIssues { get; set; }

        /// <summary>
        /// Detailed audit notes and recommendations
        /// </summary>
        [StringLength(4000)]
        public string Notes { get; set; } = string.Empty;

        /// <summary>
        /// Navigation property to the page this audit belongs to
        /// </summary>
        public virtual PageInfo PageInfo { get; set; } = null!;

        /// <summary>
        /// Navigation property to individual audit findings
        /// </summary>
        public virtual ICollection<AuditFinding> Findings { get; set; } = new List<AuditFinding>();

        /// <summary>
        /// Total number of issues found
        /// </summary>
        public int TotalIssues => CriticalIssues + HighIssues + MediumIssues + LowIssues;

        /// <summary>
        /// Whether this audit passed (no critical or high issues)
        /// </summary>
        public bool IsPassing => CriticalIssues == 0 && HighIssues == 0;
    }

    /// <summary>
    /// Status of a page audit
    /// </summary>
    public enum AuditStatus
    {
        /// <summary>
        /// Audit is in progress
        /// </summary>
        InProgress = 0,

        /// <summary>
        /// Audit completed successfully
        /// </summary>
        Completed = 1,

        /// <summary>
        /// Audit failed due to errors
        /// </summary>
        Failed = 2,

        /// <summary>
        /// Audit was cancelled
        /// </summary>
        Cancelled = 3
    }
}