using ParaHockeyApp.Models.Entities;

namespace ParaHockeyApp.Services
{
    /// <summary>
    /// Service interface for systematic page auditing and modernization tracking
    /// Provides comprehensive analysis of security, accessibility, performance, and localization
    /// </summary>
    public interface IPageAuditService
    {
        /// <summary>
        /// Generates a complete inventory of all pages in the application
        /// Scans controllers, actions, and associated files
        /// </summary>
        /// <param name="generatedBy">User performing the inventory generation</param>
        /// <returns>Complete page inventory with all discovered pages</returns>
        Task<PageInventory> GenerateInventoryAsync(string generatedBy);

        /// <summary>
        /// Performs a comprehensive audit of a specific page
        /// Includes security, accessibility, performance, and localization checks
        /// </summary>
        /// <param name="pageName">Name of the page to audit (e.g., "Home/Index")</param>
        /// <param name="auditedBy">User performing the audit</param>
        /// <returns>Complete audit results with findings and scores</returns>
        Task<PageAuditResult> AuditPageAsync(string pageName, string auditedBy);

        /// <summary>
        /// Validates security aspects of a page
        /// Checks for anti-forgery tokens, authorization, input validation, etc.
        /// </summary>
        /// <param name="pageName">Name of the page to validate</param>
        /// <returns>Security validation results</returns>
        Task<SecurityValidationResult> ValidateSecurityAsync(string pageName);

        /// <summary>
        /// Validates accessibility compliance of a page
        /// Checks for WCAG 2.2 AA compliance, semantic HTML, ARIA attributes, etc.
        /// </summary>
        /// <param name="pageName">Name of the page to validate</param>
        /// <returns>Accessibility validation results</returns>
        Task<AccessibilityValidationResult> ValidateAccessibilityAsync(string pageName);

        /// <summary>
        /// Validates performance aspects of a page
        /// Checks for Core Web Vitals, bundle sizes, image optimization, etc.
        /// </summary>
        /// <param name="pageName">Name of the page to validate</param>
        /// <returns>Performance validation results</returns>
        Task<PerformanceValidationResult> ValidatePerformanceAsync(string pageName);

        /// <summary>
        /// Validates localization implementation of a page
        /// Checks for proper resource usage, missing translations, culture formatting, etc.
        /// </summary>
        /// <param name="pageName">Name of the page to validate</param>
        /// <returns>Localization validation results</returns>
        Task<LocalizationValidationResult> ValidateLocalizationAsync(string pageName);

        /// <summary>
        /// Gets the latest inventory from the database
        /// </summary>
        /// <returns>Most recent page inventory or null if none exists</returns>
        Task<PageInventory?> GetLatestInventoryAsync();

        /// <summary>
        /// Gets audit history for a specific page
        /// </summary>
        /// <param name="pageName">Name of the page</param>
        /// <returns>List of audit results ordered by date (newest first)</returns>
        Task<List<PageAuditResult>> GetPageAuditHistoryAsync(string pageName);

        /// <summary>
        /// Gets all unresolved findings across all pages
        /// </summary>
        /// <param name="severity">Optional severity filter</param>
        /// <returns>List of unresolved findings</returns>
        Task<List<AuditFinding>> GetUnresolvedFindingsAsync(Severity? severity = null);

        /// <summary>
        /// Marks a finding as resolved
        /// </summary>
        /// <param name="findingId">ID of the finding to resolve</param>
        /// <param name="resolutionNotes">Notes about how it was resolved</param>
        /// <returns>Updated finding</returns>
        Task<AuditFinding> ResolveFindingAsync(int findingId, string resolutionNotes);

        /// <summary>
        /// Generates a comprehensive audit report for all pages
        /// </summary>
        /// <returns>Summary report with statistics and recommendations</returns>
        Task<AuditSummaryReport> GenerateAuditSummaryAsync();

        /// <summary>
        /// Generates a Page Review Plan that prioritizes pages by risk and impact
        /// </summary>
        /// <returns>Prioritized list of pages with modernization recommendations</returns>
        Task<PageReviewPlan> GeneratePageReviewPlanAsync();

        /// <summary>
        /// Generates initial audit reports for all identified pages with current state analysis
        /// </summary>
        /// <param name="auditedBy">User performing the bulk audit</param>
        /// <returns>List of audit results for all pages</returns>
        Task<List<PageAuditResult>> GenerateInitialAuditReportsAsync(string auditedBy);
    }

    /// <summary>
    /// Result of security validation for a page
    /// </summary>
    public class SecurityValidationResult
    {
        public bool HasAntiForgeryTokens { get; set; }
        public bool HasProperAuthorization { get; set; }
        public bool UsesViewModels { get; set; }
        public bool HasServerSideValidation { get; set; }
        public bool HasProperOutputEncoding { get; set; }
        public bool HasSecureCookies { get; set; }
        public bool HasCSPHeaders { get; set; }
        public List<SecurityFinding> Findings { get; set; } = new();
        public int Score { get; set; }
    }

    /// <summary>
    /// Individual security finding
    /// </summary>
    public class SecurityFinding
    {
        public string Issue { get; set; } = string.Empty;
        public SecurityRisk Risk { get; set; }
        public string Recommendation { get; set; } = string.Empty;
        public string CodeLocation { get; set; } = string.Empty;
    }

    /// <summary>
    /// Security risk levels
    /// </summary>
    public enum SecurityRisk
    {
        Low,
        Medium,
        High,
        Critical
    }

    /// <summary>
    /// Result of accessibility validation for a page
    /// </summary>
    public class AccessibilityValidationResult
    {
        public bool HasSemanticHTML { get; set; }
        public bool HasProperLabels { get; set; }
        public bool HasKeyboardNavigation { get; set; }
        public bool HasARIAAttributes { get; set; }
        public bool HasSkipLinks { get; set; }
        public bool HasColorContrast { get; set; }
        public List<AccessibilityFinding> Findings { get; set; } = new();
        public int Score { get; set; }
    }

    /// <summary>
    /// Individual accessibility finding
    /// </summary>
    public class AccessibilityFinding
    {
        public string Issue { get; set; } = string.Empty;
        public string WCAGCriterion { get; set; } = string.Empty;
        public string Recommendation { get; set; } = string.Empty;
        public string CodeLocation { get; set; } = string.Empty;
    }

    /// <summary>
    /// Result of performance validation for a page
    /// </summary>
    public class PerformanceValidationResult
    {
        public double LoadTime { get; set; }
        public double FirstContentfulPaint { get; set; }
        public double LargestContentfulPaint { get; set; }
        public double CumulativeLayoutShift { get; set; }
        public int BundleSize { get; set; }
        public int ImageCount { get; set; }
        public bool HasLazyLoading { get; set; }
        public List<PerformanceFinding> Findings { get; set; } = new();
        public int Score { get; set; }
    }

    /// <summary>
    /// Individual performance finding
    /// </summary>
    public class PerformanceFinding
    {
        public string Issue { get; set; } = string.Empty;
        public string Metric { get; set; } = string.Empty;
        public string Recommendation { get; set; } = string.Empty;
        public string CodeLocation { get; set; } = string.Empty;
    }

    /// <summary>
    /// Result of localization validation for a page
    /// </summary>
    public class LocalizationValidationResult
    {
        public bool HasResourceKeys { get; set; }
        public bool HasMissingTranslations { get; set; }
        public bool HasCultureFormatting { get; set; }
        public bool HasLocalizedValidation { get; set; }
        public List<string> MissingKeys { get; set; } = new();
        public List<LocalizationFinding> Findings { get; set; } = new();
        public int Score { get; set; }
    }

    /// <summary>
    /// Individual localization finding
    /// </summary>
    public class LocalizationFinding
    {
        public string Issue { get; set; } = string.Empty;
        public string Culture { get; set; } = string.Empty;
        public string Recommendation { get; set; } = string.Empty;
        public string CodeLocation { get; set; } = string.Empty;
    }

    /// <summary>
    /// Summary report of all audit results
    /// </summary>
    public class AuditSummaryReport
    {
        public int TotalPages { get; set; }
        public int AuditedPages { get; set; }
        public int PassingPages { get; set; }
        public int FailingPages { get; set; }
        public int CriticalIssues { get; set; }
        public int HighIssues { get; set; }
        public int MediumIssues { get; set; }
        public int LowIssues { get; set; }
        public double AverageSecurityScore { get; set; }
        public double AverageAccessibilityScore { get; set; }
        public double AveragePerformanceScore { get; set; }
        public double AverageLocalizationScore { get; set; }
        public List<string> TopIssues { get; set; } = new();
        public List<string> Recommendations { get; set; } = new();
        public DateTime GeneratedAt { get; set; }
    }

    /// <summary>
    /// Page Review Plan that prioritizes pages by risk and impact
    /// </summary>
    public class PageReviewPlan
    {
        public List<PageReviewItem> HighPriorityPages { get; set; } = new();
        public List<PageReviewItem> MediumPriorityPages { get; set; } = new();
        public List<PageReviewItem> LowPriorityPages { get; set; } = new();
        public int TotalPages { get; set; }
        public int EstimatedHours { get; set; }
        public List<string> RecommendedOrder { get; set; } = new();
        public DateTime GeneratedAt { get; set; }
    }

    /// <summary>
    /// Individual page review item with risk and impact assessment
    /// </summary>
    public class PageReviewItem
    {
        public string PageName { get; set; } = string.Empty;
        public string Controller { get; set; } = string.Empty;
        public string Action { get; set; } = string.Empty;
        public ComplexityLevel Complexity { get; set; }
        public int Priority { get; set; }
        public int RiskScore { get; set; }
        public int ImpactScore { get; set; }
        public int EstimatedHours { get; set; }
        public List<string> RiskFactors { get; set; } = new();
        public List<string> ImpactFactors { get; set; } = new();
        public List<string> Recommendations { get; set; } = new();
    }
}