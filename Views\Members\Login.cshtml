@model ParaHockeyApp.Models.ViewModels.MemberSearchViewModel
@using Microsoft.AspNetCore.Mvc.Localization
@inject IHtmlLocalizer<ParaHockeyApp.Resources.SharedResourceMarker> SharedLocalizer

@{
    ViewData["Title"] = SharedLocalizer["LoginPageTitle"];
}

<!-- Skip navigation links for accessibility -->
<a href="#login-form" class="skip-link visually-hidden-focusable">@SharedLocalizer["SkipToLoginForm"]</a>

<main id="main-content" role="main" aria-label="@SharedLocalizer["LoginMainContent"]">
    <div class="container mt-4">
    <div class="row justify-content-center">
        <div class="col-lg-8 col-md-10">
            <div class="card shadow">
                <header class="card-header bg-primary text-white text-center py-4">
                    <div class="mb-3">
                        <img src="~/assets/logos/parahockey-01.jpg" 
                             alt="@SharedLocalizer["ParahockeyLogoAlt"]" 
                             class="logo-img mb-3"
                             style="max-height: 80px;"
                             loading="lazy"
                             decoding="async"
                             width="200"
                             height="80">
                    </div>
                    <h1 class="mb-0">@SharedLocalizer["SearchForMember"]</h1>
                    <p class="mb-0">Parahockey</p>
                </header>

                <div class="card-body p-4">
                    <!-- ARIA live regions for dynamic feedback -->
                    <div id="form-status" aria-live="polite" aria-atomic="true" class="visually-hidden"></div>
                    <div id="form-errors" aria-live="assertive" aria-atomic="true" class="visually-hidden"></div>
                    
                    @* Only show appropriate messages for login page *@
                    @if (TempData["InfoMessage"] != null)
                    {
                        <div class="alert alert-info alert-dismissible fade show" role="alert" aria-labelledby="info-message-title">
                            <h6 id="info-message-title" class="visually-hidden">@SharedLocalizer["InfoMessage"]</h6>
                            <i class="fas fa-info-circle" aria-hidden="true"></i> @TempData["InfoMessage"]
                            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="@SharedLocalizer["CloseAlert"]"></button>
                        </div>
                    }

                    @if (!Model.EnvironmentSettings.IsProduction)
                    {
                        <div class="mb-3">
                            <button type="button" class="btn btn-info me-2" id="fill-daniel-btn">
                                Fill with Daniel Germain
                            </button>
                            <button type="button" class="btn btn-secondary" id="fill-bad-btn">
                                Fill with Bad Email
                            </button>
                        </div>
                    }
                    <form asp-controller="Members" 
                          asp-action="Login" 
                          method="post" 
                          id="login-form"
                          aria-labelledby="form-title"
                          aria-describedby="form-description"
                          novalidate>
                        @Html.AntiForgeryToken()
                        
                        <p id="form-description" class="visually-hidden">
                            @SharedLocalizer["LoginFormDescription"]
                        </p>

                        <fieldset class="mb-4">
                            <legend class="section-header">
                                <h2 id="form-title" class="text-primary border-bottom pb-2 h4">
                                    <i class="fas fa-user" aria-hidden="true"></i> 
                                    @SharedLocalizer["BasicInformation"]
                                </h2>
                            </legend>

                            <div class="row mb-3">
                                <div class="col-md-6">
                                    <label asp-for="LastName" 
                                           class="ph-form-label required">
                                        @SharedLocalizer["LastName"]
                                    </label>
                                    <input asp-for="LastName" 
                                           name="LastName" 
                                           type="text"
                                           class="ph-form-control" 
                                           required
                                           aria-required="true"
                                           aria-describedby="LastName-error LastName-help"
                                           placeholder="@SharedLocalizer["LastNamePlaceholder"]" 
                                           autocomplete="family-name"
                                           inputmode="text"
                                           minlength="2"
                                           maxlength="50" />
                                    <small id="LastName-help" class="form-text text-muted visually-hidden">
                                        @SharedLocalizer["LastNameHelp"]
                                    </small>
                                    <span id="LastName-error" asp-validation-for="LastName" 
                                          class="text-danger small" role="alert" aria-live="polite"></span>
                                </div>
                                <div class="col-md-6">
                                    <div class="d-flex align-items-center mb-2">
                                        <label asp-for="DateOfBirth"
                                               class="ph-form-label required me-2">
                                            @SharedLocalizer["DateOfBirth"]
                                        </label>
                                        <button type="button" 
                                                class="btn btn-sm btn-outline-secondary datepicker-icon" 
                                                aria-label="@SharedLocalizer["OpenDatePicker"]"
                                                tabindex="-1">
                                            <i class="fas fa-calendar-alt" aria-hidden="true"></i>
                                        </button>
                                    </div>
                                    <input asp-for="DateOfBirth" 
                                           name="DateOfBirth" 
                                           type="date"
                                           class="ph-form-control datepicker-input"
                                           required
                                           aria-required="true"
                                           aria-describedby="DateOfBirth-error DateOfBirth-help"
                                           placeholder="@SharedLocalizer["DatePlaceholder"]" 
                                           autocomplete="bday"
                                           inputmode="numeric" />
                                    <small id="DateOfBirth-help" class="form-text text-muted">
                                        @SharedLocalizer["DateFormatHelper"]
                                    </small>
                                    <span id="DateOfBirth-error" asp-validation-for="DateOfBirth" 
                                          class="text-danger small" role="alert" aria-live="polite"></span>
                                </div>
                            </div>

                            <div class="row mb-3">
                                <div class="col-md-8">
                                    <label asp-for="Email" 
                                           class="ph-form-label required">
                                        @SharedLocalizer["Email"]
                                    </label>
                                    <input asp-for="Email" 
                                           name="Email" 
                                           type="email" 
                                           class="ph-form-control" 
                                           required
                                           aria-required="true"
                                           aria-describedby="Email-error Email-help"
                                           placeholder="@SharedLocalizer["EmailPlaceholder"]" 
                                           autocomplete="email"
                                           inputmode="email"
                                           pattern="[a-zA-Z0-9._%+-]+@@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}"
                                           maxlength="100" />
                                    <small id="Email-help" class="form-text text-muted visually-hidden">
                                        @SharedLocalizer["EmailHelp"]
                                    </small>
                                    <span id="Email-error" asp-validation-for="Email" 
                                          class="text-danger small" role="alert" aria-live="polite"></span>
                                </div>
                            </div>
                        </fieldset>

                        <div class="d-grid gap-2 mt-4">
                            <button type="submit" class="btn btn-primary btn-lg" aria-describedby="search-button-help">
                                <i class="fas fa-search" aria-hidden="true"></i> 
                                @SharedLocalizer["SearchButton"]
                            </button>
                            <small id="search-button-help" class="form-text text-muted text-center">
                                @SharedLocalizer["SearchButtonHelp"]
                            </small>
                        </div>
                    </form>

                    @if (Model.SearchResults != null)
                    {
                        <section class="mt-5" aria-labelledby="search-results-title">
                            <h3 id="search-results-title" class="text-primary border-bottom pb-2 h4">
                                <i class="fas fa-users" aria-hidden="true"></i> 
                                @SharedLocalizer["SearchResults"]
                            </h3>
                            @if (Model.SearchResults.Any())
                            {
                                <div class="table-responsive">
                                    <table class="table table-striped" 
                                           role="table" 
                                           aria-labelledby="search-results-title"
                                           aria-describedby="search-results-description">
                                        <caption id="search-results-description" class="visually-hidden">
                                            @SharedLocalizer["SearchResultsTableDescription"]
                                        </caption>
                                        <thead>
                                            <tr role="row">
                                                <th scope="col">@SharedLocalizer["LastName"]</th>
                                                <th scope="col">@SharedLocalizer["DateOfBirth"]</th>
                                                <th scope="col">@SharedLocalizer["Email"]</th>
                                                <th scope="col">@SharedLocalizer["Actions"]</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            @foreach (var member in Model.SearchResults)
                                            {
                                                <tr role="row">
                                                    <td>@member.LastName</td>
                                                    <td>@member.DateOfBirth.ToShortDateString()</td>
                                                    <td>
                                                        @if (member.Email.Equals(Model.Email, StringComparison.OrdinalIgnoreCase))
                                                        {
                                                            <span class="text-success">
                                                                <i class="fas fa-check-circle" aria-hidden="true"></i>
                                                                @member.Email
                                                            </span>
                                                        }
                                                        else
                                                        {
                                                            <span class="text-danger" role="alert">
                                                                <i class="fas fa-times-circle" aria-hidden="true"></i>
                                                                @SharedLocalizer["WrongEmail"]
                                                            </span>
                                                        }
                                                    </td>
                                                    <td>
                                                        @if (member.Email.Equals(Model.Email, StringComparison.OrdinalIgnoreCase))
                                                        {
                                                            <button type="button" 
                                                                    class="btn btn-secondary btn-sm send-code-button"
                                                                    data-email="@member.Email"
                                                                    aria-describedby="send-code-help">
                                                                <i class="fas fa-envelope" aria-hidden="true"></i>
                                                                @SharedLocalizer["SendCodeButton"]
                                                            </button>
                                                            <small id="send-code-help" class="visually-hidden">
                                                                @SharedLocalizer["SendCodeButtonHelp"]
                                                            </small>
                                                        }
                                                        else
                                                        {
                                                            <span class="text-muted">—</span>
                                                        }
                                                    </td>
                                                </tr>
                                            }
                                        </tbody>
                                    </table>
                                </div>
                            }
                            else
                            {
                                <div class="alert alert-info" role="status" aria-live="polite">
                                    <i class="fas fa-info-circle" aria-hidden="true"></i>
                                    @SharedLocalizer["NoResultsFound"]
                                </div>
                            }
                        </section>
                    }

                    <section id="verification-section" 
                             class="mt-4" 
                             style="display: none;" 
                             aria-labelledby="verification-title"
                             role="region">
                        <hr aria-hidden="true" />
                        <h3 id="verification-title" class="h4">@SharedLocalizer["EmailVerification"]</h3>
                        <p class="mb-3">@SharedLocalizer["VerificationCodeSent"]</p>
                        
                        <form id="verifyCodeForm" 
                              asp-controller="Members" 
                              asp-action="VerifyCode" 
                              method="post"
                              aria-labelledby="verification-title"
                              novalidate>
                            @Html.AntiForgeryToken()
                            <fieldset>
                                <legend class="visually-hidden">@SharedLocalizer["VerificationCodeEntry"]</legend>
                                <div class="row">
                                    <div class="col-md-6">
                                        <label for="verification-code" class="form-label required">
                                            @SharedLocalizer["VerificationCode"]
                                        </label>
                                        <input type="text" 
                                               id="verification-code"
                                               name="code" 
                                               class="form-control"
                                               required
                                               aria-required="true"
                                               aria-describedby="verification-code-help"
                                               placeholder="@SharedLocalizer["EnterCode"]"
                                               inputmode="numeric"
                                               pattern="[0-9]{6}"
                                               maxlength="6"
                                               autocomplete="one-time-code" />
                                        <small id="verification-code-help" class="form-text text-muted">
                                            @SharedLocalizer["VerificationCodeHelp"]
                                        </small>
                                        <input type="hidden" name="email" id="verification-email" />
                                    </div>
                                    <div class="col-md-6 d-flex align-items-end">
                                        <button type="submit"
                                                class="btn btn-primary"
                                                aria-describedby="verify-button-help">
                                            <i class="fas fa-check" aria-hidden="true"></i>
                                            @SharedLocalizer["VerifyButton"]
                                        </button>
                                        <small id="verify-button-help" class="visually-hidden">
                                            @SharedLocalizer["VerifyButtonHelp"]
                                        </small>
                                    </div>
                                </div>
                            </fieldset>
                        </form>
                        <div id="verification-result" 
                             class="mt-2" 
                             role="status" 
                             aria-live="polite" 
                             aria-atomic="true"></div>
                    </section>
                </div>
            </div>
        </div>
    </div>
</main>

@section Scripts {
    @{
        await Html.RenderPartialAsync("_ValidationScriptsPartial");
    }
    <script src="~/js/jquery.mask.min.js"></script>
    <script>
        $(document).ready(function () {
            console.log('🟢 Login form loaded');
            
            // ARIA live region announcement function
            function announceToScreenReader(message, isError = false) {
                const liveRegion = isError ? '#form-errors' : '#form-status';
                $(liveRegion).text(message);
                // Clear after 5 seconds to avoid repetitive announcements
                setTimeout(() => $(liveRegion).text(''), 5000);
            }
            
            // Pre-fill email from URL parameters (from "Modifier le membre" button)
            var urlParams = new URLSearchParams(window.location.search);
            var email = urlParams.get('email');
            
            if (email) {
                $('input[name="Email"]').val(email);
                announceToScreenReader('Email address pre-filled from navigation');
            }
            var datepickerOptions = {
                dateFormat: 'yy-mm-dd',
                changeMonth: true,
                changeYear: true,
                yearRange: 'c-100:c',
                onSelect: function (dateText, inst) {
                    $(this).trigger('change');
                }
            };

            var currentLang = $('html').attr('lang');

            if (currentLang === 'fr' && $.datepicker.regional['fr']) {
                $.datepicker.setDefaults($.datepicker.regional['fr']);
            }

            $('.datepicker-input').datepicker(datepickerOptions);

            $('.datepicker-icon').on('click', function () {
                $(this).closest('.col-md-6').find('.datepicker-input').datepicker('show');
            });

            var maskPlaceholder = currentLang === 'fr' ? 'AAAA-MM-JJ' : 'YYYY-MM-DD';
            $('.datepicker-input').mask('0000-00-00', {
                'translation': {
                    '0': { pattern: /[0-9]/ }
                },
                placeholder: maskPlaceholder
            });

            // Handle Send Code button click
            $('.send-code-button').on('click', function (e) {
                e.preventDefault();
                var $button = $(this);
                var email = $button.data('email');
                var originalText = $button.html();
                
                // Show loading state
                $button.prop('disabled', true)
                       .html('<i class="fas fa-spinner fa-spin" aria-hidden="true"></i> @SharedLocalizer["SendingCode"]');
                
                announceToScreenReader('Sending verification code to ' + email);
                
                $('#verification-email').val(email);

                // Submit the form via AJAX to avoid page refresh
                $.ajax({
                    url: '@Url.Action("SendVerificationCode", "Members")',
                    type: 'POST',
                    data: {
                        email: email,
                        __RequestVerificationToken: $('input[name="__RequestVerificationToken"]').val()
                    },
                    success: function (response) {
                        console.log("🟢 Code sent successfully:", response);
                        if (response.success) {
                            $('#verification-section').show();
                            $('#verification-code').focus(); // Focus on verification input
                            announceToScreenReader('Verification code sent successfully. Please check your email.');
                        } else {
                            announceToScreenReader('Error sending code: ' + response.message, true);
                        }
                    },
                    error: function (xhr, status, error) {
                        console.error("❌ Error sending code:", error);
                        console.error("Response:", xhr.responseText);
                        announceToScreenReader('Error sending verification code. Please try again.', true);
                    },
                    complete: function() {
                        // Restore button state
                        $button.prop('disabled', false).html(originalText);
                    }
                });
            });

            // Handle verify code form submission via AJAX
            $('#verifyCodeForm').on('submit', function (e) {
                e.preventDefault();
                var form = $(this);
                var formData = form.serialize();
                var $submitButton = form.find('button[type="submit"]');
                var originalButtonText = $submitButton.html();
                
                // Show loading state
                $submitButton.prop('disabled', true)
                            .html('<i class="fas fa-spinner fa-spin" aria-hidden="true"></i> @SharedLocalizer["VerifyingCode"]');
                
                announceToScreenReader('Verifying code...');

                $.ajax({
                    url: '@Url.Action("VerifyCode", "Members")',
                    type: 'POST',
                    data: formData,
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest'
                    },
                    success: function (response) {
                        var resultDiv = $('#verification-result');
                        if (response.success) {
                            resultDiv.html('<div class="alert alert-success" role="alert"><i class="fas fa-check-circle" aria-hidden="true"></i> @SharedLocalizer["VerificationSuccessful"]</div>');
                            announceToScreenReader('Verification successful! Redirecting...', false);
                            if (response.redirectUrl) {
                                setTimeout(() => window.location.href = response.redirectUrl, 1500);
                            }
                        } else {
                            resultDiv.html('<div class="alert alert-danger" role="alert"><i class="fas fa-times-circle" aria-hidden="true"></i> @SharedLocalizer["VerificationFailed"]</div>');
                            announceToScreenReader('Verification failed. Please check your code and try again.', true);
                            // Focus back on code input for retry
                            $('#verification-code').focus().select();
                        }
                    },
                    error: function (xhr, status, error) {
                        console.error('❌ Verification error:', error);
                        $('#verification-result').html('<div class="alert alert-danger" role="alert"><i class="fas fa-exclamation-triangle" aria-hidden="true"></i> @SharedLocalizer["VerificationError"]</div>');
                        announceToScreenReader('Error verifying code. Please try again.', true);
                    },
                    complete: function() {
                        // Restore button state
                        $submitButton.prop('disabled', false).html(originalButtonText);
                    }
                });
            });

            // Display verification result if available
            var verificationResult = '@TempData["VerificationResult"]';
            var verificationEmail = '@TempData["VerificationEmail"]';
            console.log('Verification result from server:', verificationResult);
            if (verificationResult && verificationResult !== '') {
                var resultDiv = $('#verification-result');
                if (verificationResult === 'GOOD') {
                    resultDiv.html('<h5 class="text-success">✅ GOOD CODE - Verification successful!</h5>');
                } else {
                    resultDiv.html('<h5 class="text-danger">❌ BAD CODE - Invalid verification code</h5>');
                }
                $('#verification-email').val(verificationEmail);
                $('#verification-section').show();
            }

            // Show verification section if code was just sent (only for non-AJAX requests)
            var codeSent = '@TempData["CodeSent"]';
            console.log('Code sent flag:', codeSent);
            if (codeSent === 'True') {
                $('#verification-section').show();
                var email = '@TempData["VerificationEmail"]';
                $('#verification-email').val(email);
                console.log('Showing verification section due to CodeSent flag');
            }

            $('#fill-daniel-btn').on('click', function () {
                $('input[name="LastName"]').val('Test-VolunteerTest_01');
                $('input[name="DateOfBirth"]').val('1965-05-04');
                $('input[name="Email"]').val('<EMAIL>');
            });
            $('#fill-bad-btn').on('click', function () {
                $('input[name="LastName"]').val('Test-VolunteerTest_01');
                $('input[name="DateOfBirth"]').val('1965-05-04');
                $('input[name="Email"]').val('<EMAIL>');
            });
        });
    </script>
}
