using System.ComponentModel.DataAnnotations;

namespace ParaHockeyApp.DTOs
{
    /// <summary>
    /// Test result data from E2E test execution
    /// </summary>
    public class TestResultDto
    {
        public string TestName { get; set; } = string.Empty;
        public bool Passed { get; set; }
        public string? ErrorMessage { get; set; }
        public TimeSpan Duration { get; set; }
        public DateTime ExecutedAt { get; set; }
    }

    /// <summary>
    /// Complete test run results
    /// </summary>
    public class TestRunResultDto
    {
        public string TestCategory { get; set; } = string.Empty;
        public DateTime StartedAt { get; set; }
        public DateTime CompletedAt { get; set; }
        public TimeSpan TotalDuration { get; set; }
        public int TotalTests { get; set; }
        public int PassedTests { get; set; }
        public int FailedTests { get; set; }
        public List<TestResultDto> TestResults { get; set; } = new();
        public string Environment { get; set; } = string.Empty;
        public string Browser { get; set; } = string.Empty;
    }
}