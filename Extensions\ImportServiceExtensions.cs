using Microsoft.AspNetCore.Localization;
using ParaHockeyApp.Configuration;
using ParaHockeyApp.Services;
using System.Globalization;

namespace ParaHockeyApp.Extensions
{
    /// <summary>
    /// Extension methods for configuring import localization and configuration services
    /// </summary>
    public static class ImportServiceExtensions
    {
        /// <summary>
        /// Adds import configuration and localization services to the service collection
        /// </summary>
        /// <param name="services">The service collection</param>
        /// <param name="configuration">The configuration</param>
        /// <returns>The service collection for chaining</returns>
        public static IServiceCollection AddImportServices(this IServiceCollection services, IConfiguration configuration)
        {
            // Register configuration services
            services.Configure<ImportConfiguration>(configuration.GetSection(ImportConfiguration.SectionName));
            services.AddSingleton<IImportConfigurationService, ImportConfigurationService>();

            // Register localization services
            services.AddSingleton<IImportLocalizationService, ImportLocalizationService>();

            // Configure request localization
            var importConfig = configuration.GetSection(ImportConfiguration.SectionName).Get<ImportConfiguration>() ?? new ImportConfiguration();

            services.Configure<RequestLocalizationOptions>(options =>
            {
                var supportedCultures = importConfig.SupportedCultures
                    .Select(c => new CultureInfo(c))
                    .ToArray();

                options.DefaultRequestCulture = new RequestCulture(importConfig.DefaultCulture);
                options.SupportedCultures = supportedCultures;
                options.SupportedUICultures = supportedCultures;

                // Configure request culture providers
                options.RequestCultureProviders = new List<IRequestCultureProvider>
                {
                    // Check for culture in cookie
                    new CookieRequestCultureProvider(),
                    // Check for culture in query string
                    new QueryStringRequestCultureProvider(),
                    // Check for culture in Accept-Language header
                    new AcceptLanguageHeaderRequestCultureProvider()
                };
            });

            return services;
        }

        /// <summary>
        /// Configures the application to use import localization middleware
        /// </summary>
        /// <param name="app">The application builder</param>
        /// <returns>The application builder for chaining</returns>
        public static IApplicationBuilder UseImportLocalization(this IApplicationBuilder app)
        {
            // Add request localization middleware
            app.UseRequestLocalization();

            // Add custom middleware to handle culture switching
            app.UseMiddleware<CultureSwitchMiddleware>();

            return app;
        }
    }

    /// <summary>
    /// Middleware for handling culture switching in import operations
    /// </summary>
    public class CultureSwitchMiddleware
    {
        private readonly RequestDelegate _next;
        private readonly ILogger<CultureSwitchMiddleware> _logger;

        public CultureSwitchMiddleware(RequestDelegate next, ILogger<CultureSwitchMiddleware> logger)
        {
            _next = next;
            _logger = logger;
        }

        public async Task InvokeAsync(HttpContext context, IImportConfigurationService configService)
        {
            // Check if culture is specified in the request
            string? culture = null;

            // Priority order: Query parameter, Cookie, Header
            if (context.Request.Query.ContainsKey("culture"))
            {
                culture = context.Request.Query["culture"];
            }
            else if (context.Request.Cookies.ContainsKey("Culture"))
            {
                culture = context.Request.Cookies["Culture"];
            }

            // Validate and set culture if specified
            if (!string.IsNullOrEmpty(culture) && configService.IsCultureSupported(culture))
            {
                try
                {
                    var cultureInfo = new CultureInfo(culture);
                    CultureInfo.CurrentCulture = cultureInfo;
                    CultureInfo.CurrentUICulture = cultureInfo;

                    // Store in HttpContext for use by other services
                    context.Items["RequestCulture"] = culture;
                }
                catch (CultureNotFoundException ex)
                {
                    _logger.LogWarning(ex, "Invalid culture specified: {Culture}", culture);
                }
            }

            await _next(context);
        }
    }
}
