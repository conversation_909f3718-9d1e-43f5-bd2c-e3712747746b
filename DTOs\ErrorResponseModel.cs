namespace ParaHockeyApp.DTOs
{
    /// <summary>
    /// Model for consistent error responses across the application.
    /// Provides standardized error information for API responses and error handling.
    /// </summary>
    public class ErrorResponseModel
    {
        /// <summary>
        /// User-friendly error message that can be displayed to the end user.
        /// </summary>
        public string UserMessage { get; set; } = string.Empty;

        /// <summary>
        /// Unique identifier for this error instance for tracking and debugging.
        /// </summary>
        public string ErrorId { get; set; } = Guid.NewGuid().ToString();

        /// <summary>
        /// Timestamp when the error occurred.
        /// </summary>
        public DateTime Timestamp { get; set; } = DateTime.UtcNow;

        /// <summary>
        /// Indicates whether the user can retry the operation that caused the error.
        /// </summary>
        public bool IsRetryable { get; set; } = false;

        /// <summary>
        /// HTTP status code associated with this error.
        /// </summary>
        public int StatusCode { get; set; } = 500;

        /// <summary>
        /// Optional error code for categorizing different types of errors.
        /// </summary>
        public string? ErrorCode { get; set; }

        /// <summary>
        /// Optional additional details about the error (for debugging, not shown to users).
        /// </summary>
        public string? Details { get; set; }

        /// <summary>
        /// Optional URL or action the user can take to resolve the error.
        /// </summary>
        public string? HelpUrl { get; set; }

        /// <summary>
        /// Creates a new error response with basic information.
        /// </summary>
        /// <param name="userMessage">User-friendly error message</param>
        /// <param name="isRetryable">Whether the operation can be retried</param>
        /// <param name="statusCode">HTTP status code</param>
        /// <returns>New ErrorResponseModel instance</returns>
        public static ErrorResponseModel Create(string userMessage, bool isRetryable = false, int statusCode = 500)
        {
            return new ErrorResponseModel
            {
                UserMessage = userMessage,
                IsRetryable = isRetryable,
                StatusCode = statusCode
            };
        }

        /// <summary>
        /// Creates a new error response for validation errors.
        /// </summary>
        /// <param name="userMessage">User-friendly validation error message</param>
        /// <returns>New ErrorResponseModel instance for validation errors</returns>
        public static ErrorResponseModel CreateValidationError(string userMessage)
        {
            return new ErrorResponseModel
            {
                UserMessage = userMessage,
                IsRetryable = true,
                StatusCode = 400,
                ErrorCode = "VALIDATION_ERROR"
            };
        }

        /// <summary>
        /// Creates a new error response for not found errors.
        /// </summary>
        /// <param name="resourceType">Type of resource that was not found</param>
        /// <returns>New ErrorResponseModel instance for not found errors</returns>
        public static ErrorResponseModel CreateNotFoundError(string resourceType)
        {
            return new ErrorResponseModel
            {
                UserMessage = $"The requested {resourceType} was not found.",
                IsRetryable = false,
                StatusCode = 404,
                ErrorCode = "NOT_FOUND"
            };
        }

        /// <summary>
        /// Creates a new error response for database errors.
        /// </summary>
        /// <param name="userMessage">User-friendly database error message</param>
        /// <returns>New ErrorResponseModel instance for database errors</returns>
        public static ErrorResponseModel CreateDatabaseError(string userMessage = "A database error occurred. Please try again later.")
        {
            return new ErrorResponseModel
            {
                UserMessage = userMessage,
                IsRetryable = true,
                StatusCode = 500,
                ErrorCode = "DATABASE_ERROR"
            };
        }
    }
}