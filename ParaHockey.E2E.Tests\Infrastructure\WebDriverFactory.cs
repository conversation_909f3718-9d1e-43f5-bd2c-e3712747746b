using OpenQA.Selenium;
using OpenQA.Selenium.Chrome;
using OpenQA.Selenium.Firefox;
using OpenQA.Selenium.Edge;
using WebDriverManager;
using WebDriverManager.DriverConfigs.Impl;

namespace ParaHockey.E2E.Tests.Infrastructure
{
    public class WebDriverFactory
    {
        private readonly TestConfiguration _config;

        public WebDriverFactory(TestConfiguration config)
        {
            _config = config;
        }

        public IWebDriver CreateDriver(string browserName = "Chrome")
        {
            return browserName.ToLower() switch
            {
                "chrome" => CreateChromeDriver(),
                "firefox" => CreateFirefoxDriver(),
                "edge" => CreateEdgeDriver(),
                _ => throw new ArgumentException($"Browser '{browserName}' is not supported")
            };
        }

        public IWebDriver CreateMobileDriver(MobileViewport viewport)
        {
            var options = new ChromeOptions();
            
            if (_config.HeadlessMode)
            {
                options.AddArgument("--headless");
            }

            // Mobile emulation
            options.AddArgument($"--window-size={viewport.Width},{viewport.Height}");
            options.AddArgument("--user-agent=Mozilla/5.0 (iPhone; CPU iPhone OS 14_7_1 like Mac OS X) AppleWebKit/605.1.15");
            
            // Performance optimizations
            options.AddArguments(
                "--no-sandbox",
                "--disable-dev-shm-usage",
                "--disable-gpu",
                "--disable-web-security",
                "--allow-running-insecure-content"
            );

            var driver = new ChromeDriver(options);
            driver.Manage().Timeouts().ImplicitWait = TimeSpan.FromSeconds(_config.ImplicitWaitSeconds);
            
            return driver;
        }

        private IWebDriver CreateChromeDriver()
        {
            try
            {
                // Auto-download and setup correct ChromeDriver version
                new DriverManager().SetUpDriver(new ChromeConfig());
            }
            catch (Exception ex)
            {
                Console.WriteLine($"WebDriverManager setup warning: {ex.Message}");
                // Continue anyway - Selenium Manager might handle it
            }
            
            var options = new ChromeOptions();
            
            if (_config.HeadlessMode)
            {
                options.AddArgument("--headless");
            }

            // Stability and performance
            options.AddArguments(
                "--no-sandbox",
                "--disable-dev-shm-usage",
                "--disable-gpu",
                "--disable-web-security",
                "--allow-running-insecure-content",
                "--disable-extensions",
                "--disable-logging",
                "--silent",
                "--disable-blink-features=AutomationControlled"
            );

            var driver = new ChromeDriver(options);
            driver.Manage().Window.Size = new System.Drawing.Size(1920, 1080);
            driver.Manage().Timeouts().ImplicitWait = TimeSpan.FromSeconds(_config.ImplicitWaitSeconds);
            
            return driver;
        }

        private IWebDriver CreateFirefoxDriver()
        {
            var options = new FirefoxOptions();
            
            if (_config.HeadlessMode)
            {
                options.AddArgument("--headless");
            }

            var driver = new FirefoxDriver(options);
            driver.Manage().Window.Size = new System.Drawing.Size(1920, 1080);
            driver.Manage().Timeouts().ImplicitWait = TimeSpan.FromSeconds(_config.ImplicitWaitSeconds);
            
            return driver;
        }

        private IWebDriver CreateEdgeDriver()
        {
            var options = new EdgeOptions();
            
            if (_config.HeadlessMode)
            {
                options.AddArgument("--headless");
            }

            options.AddArguments(
                "--no-sandbox",
                "--disable-dev-shm-usage"
            );

            var driver = new EdgeDriver(options);
            driver.Manage().Window.Size = new System.Drawing.Size(1920, 1080);
            driver.Manage().Timeouts().ImplicitWait = TimeSpan.FromSeconds(_config.ImplicitWaitSeconds);
            
            return driver;
        }
    }
}