# Copy member data from Test to Production
Write-Host "=== Copy Test Data to Production ===" -ForegroundColor Cyan

$testConn = "Server=SIMBA\SQLEXPRESS;Database=ParaHockeyDB_TEST;User Id=ParaHockeyUser;Password=***************;TrustServerCertificate=True;"
$prodConn = "Server=SIMBA\SQLEXPRESS;Database=ParaHockeyDB;User Id=ParaHockeyUser;Password=***************;TrustServerCertificate=True;"

Write-Host "WARNING: This will copy ALL member data from Test to Production!" -ForegroundColor Red
Write-Host "Test database has real member data that should NOT be in production." -ForegroundColor Red
Write-Host ""
Write-Host "Instead, let's create minimal seed data for Production testing." -ForegroundColor Yellow

try {
    $conn = New-Object System.Data.SqlClient.SqlConnection($prodConn)
    $conn.Open()
    
    # Check current member count
    $cmd = $conn.CreateCommand()
    $cmd.CommandText = "SELECT COUNT(*) FROM Members"
    $currentCount = $cmd.ExecuteScalar()
    
    Write-Host "Current Production Members: $currentCount" -ForegroundColor Green
    
    if ($currentCount -eq 0) {
        Write-Host "Creating test member for Production..." -ForegroundColor Yellow
        
        # Insert a test member
        $cmd.CommandText = @"
INSERT INTO Members (
    FirstName, LastName, Email, Phone, DateOfBirth, Address, City, PostalCode, ProvinceId, RegistrationTypeId,
    IsActive, DateCreated, DateModified, CreatedBy, ModifiedBy
) VALUES (
    'Test', 'User', '<EMAIL>', '(*************', '1990-01-01', '123 Test St', 'Montreal', 'H1A 1A1', 
    (SELECT Id FROM Provinces WHERE Code = 'QC'), 
    (SELECT TOP 1 Id FROM RegistrationTypes),
    1, GETUTCDATE(), GETUTCDATE(), 'SYSTEM', 'SYSTEM'
)
"@
        $cmd.ExecuteNonQuery()
        Write-Host "✓ Test member created" -ForegroundColor Green
        
        # Verify
        $cmd.CommandText = "SELECT COUNT(*) FROM Members"
        $newCount = $cmd.ExecuteScalar()
        Write-Host "New Production Members: $newCount" -ForegroundColor Green
    } else {
        Write-Host "Production already has $currentCount members - no action needed" -ForegroundColor Green
    }
    
    $conn.Close()
    Write-Host "✓ Complete" -ForegroundColor Green
}
catch {
    Write-Host "Error: $_" -ForegroundColor Red
}