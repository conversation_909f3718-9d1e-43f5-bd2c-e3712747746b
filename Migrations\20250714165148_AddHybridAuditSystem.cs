using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace ParaHockeyApp.Migrations
{
    /// <inheritdoc />
    public partial class AddHybridAuditSystem : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            // Create AuditLogs table first
            migrationBuilder.Sql(@"
                IF NOT EXISTS (SELECT * FROM sys.tables WHERE name = 'AuditLogs')
                BEGIN
                    CREATE TABLE AuditLogs (
                        Id int IDENTITY(1,1) PRIMARY KEY,
                        EntityType nvarchar(50) NOT NULL,
                        EntityId int NOT NULL,
                        Action nvarchar(20) NOT NULL,
                        Timestamp datetime2 NOT NULL,
                        PerformedByMemberId int NULL,
                        PerformedByAdminId int NULL,
                        PerformedBySource int NOT NULL,
                        PerformerName nvarchar(101) NOT NULL,
                        IPAddress nvarchar(45) NULL,
                        OldValues nvarchar(max) NULL,
                        NewValues nvarchar(max) NULL,
                        Description nvarchar(1000) NULL
                    )
                    
                    -- Create indexes for performance
                    CREATE INDEX IX_AuditLogs_Entity ON AuditLogs (EntityType, EntityId)
                    CREATE INDEX IX_AuditLogs_Timestamp ON AuditLogs (Timestamp)
                    CREATE INDEX IX_AuditLogs_PerformedByMember ON AuditLogs (PerformedByMemberId)
                    CREATE INDEX IX_AuditLogs_PerformedByAdmin ON AuditLogs (PerformedByAdminId)
                END");

            // Add audit fields to all BaseEntity tables (Members, AdminUsers, Parents, EmergencyContacts, etc.)
            var baseEntityTables = new[] { "Members", "AdminUsers", "Parents", "EmergencyContacts", "AdminTypes", "TestEntries" };

            foreach (var table in baseEntityTables)
            {
                migrationBuilder.Sql($@"
                    -- Add DateModified column
                    IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('{table}') AND name = 'DateModified')
                    BEGIN
                        ALTER TABLE {table} ADD DateModified datetime2 NULL
                    END

                    -- Add CreatedByMemberId column  
                    IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('{table}') AND name = 'CreatedByMemberId')
                    BEGIN
                        ALTER TABLE {table} ADD CreatedByMemberId int NULL
                    END

                    -- Add CreatedByAdminId column
                    IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('{table}') AND name = 'CreatedByAdminId')
                    BEGIN
                        ALTER TABLE {table} ADD CreatedByAdminId int NULL
                    END

                    -- Add ModifiedByMemberId column
                    IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('{table}') AND name = 'ModifiedByMemberId')
                    BEGIN
                        ALTER TABLE {table} ADD ModifiedByMemberId int NULL
                    END

                    -- Add ModifiedByAdminId column
                    IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('{table}') AND name = 'ModifiedByAdminId')
                    BEGIN
                        ALTER TABLE {table} ADD ModifiedByAdminId int NULL
                    END

                    -- Add CreatedBySource column (default to System = 0)
                    IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('{table}') AND name = 'CreatedBySource')
                    BEGIN
                        ALTER TABLE {table} ADD CreatedBySource int NOT NULL DEFAULT 0
                    END

                    -- Add ModifiedBySource column
                    IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('{table}') AND name = 'ModifiedBySource')
                    BEGIN
                        ALTER TABLE {table} ADD ModifiedBySource int NULL
                    END");
            }

            // Add foreign key constraints for AuditLogs (with defensive checks)
            migrationBuilder.Sql(@"
                -- Foreign key for AuditLogs.PerformedByMemberId
                IF NOT EXISTS (SELECT * FROM sys.foreign_keys WHERE name = 'FK_AuditLogs_Members_PerformedByMemberId')
                BEGIN
                    ALTER TABLE AuditLogs 
                    ADD CONSTRAINT FK_AuditLogs_Members_PerformedByMemberId 
                    FOREIGN KEY (PerformedByMemberId) REFERENCES Members(Id) ON DELETE NO ACTION
                END

                -- Foreign key for AuditLogs.PerformedByAdminId  
                IF NOT EXISTS (SELECT * FROM sys.foreign_keys WHERE name = 'FK_AuditLogs_AdminUsers_PerformedByAdminId')
                BEGIN
                    ALTER TABLE AuditLogs 
                    ADD CONSTRAINT FK_AuditLogs_AdminUsers_PerformedByAdminId 
                    FOREIGN KEY (PerformedByAdminId) REFERENCES AdminUsers(Id) ON DELETE NO ACTION
                END");

            // Note: We'll add BaseEntity foreign key constraints in a separate migration to avoid circular dependencies
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            // Remove foreign key constraints first
            migrationBuilder.Sql(@"
                IF EXISTS (SELECT * FROM sys.foreign_keys WHERE name = 'FK_AuditLogs_Members_PerformedByMemberId')
                    ALTER TABLE AuditLogs DROP CONSTRAINT FK_AuditLogs_Members_PerformedByMemberId

                IF EXISTS (SELECT * FROM sys.foreign_keys WHERE name = 'FK_AuditLogs_AdminUsers_PerformedByAdminId')
                    ALTER TABLE AuditLogs DROP CONSTRAINT FK_AuditLogs_AdminUsers_PerformedByAdminId");

            // Remove audit columns from BaseEntity tables
            var baseEntityTables = new[] { "Members", "AdminUsers", "Parents", "EmergencyContacts", "AdminTypes", "TestEntries" };

            foreach (var table in baseEntityTables)
            {
                migrationBuilder.Sql($@"
                    IF EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('{table}') AND name = 'ModifiedBySource')
                        ALTER TABLE {table} DROP COLUMN ModifiedBySource
                    
                    IF EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('{table}') AND name = 'CreatedBySource')
                        ALTER TABLE {table} DROP COLUMN CreatedBySource
                    
                    IF EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('{table}') AND name = 'ModifiedByAdminId')
                        ALTER TABLE {table} DROP COLUMN ModifiedByAdminId
                    
                    IF EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('{table}') AND name = 'ModifiedByMemberId')
                        ALTER TABLE {table} DROP COLUMN ModifiedByMemberId
                    
                    IF EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('{table}') AND name = 'CreatedByAdminId')
                        ALTER TABLE {table} DROP COLUMN CreatedByAdminId
                    
                    IF EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('{table}') AND name = 'CreatedByMemberId')
                        ALTER TABLE {table} DROP COLUMN CreatedByMemberId
                    
                    IF EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('{table}') AND name = 'DateModified')
                        ALTER TABLE {table} DROP COLUMN DateModified");
            }

            // Drop AuditLogs table
            migrationBuilder.Sql(@"
                IF EXISTS (SELECT * FROM sys.tables WHERE name = 'AuditLogs')
                    DROP TABLE AuditLogs");
        }
    }
}