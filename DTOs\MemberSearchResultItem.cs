namespace ParaHockeyApp.DTOs
{
    /// <summary>
    /// Individual member result item containing member data and search match information
    /// Used to display search results with highlighting and context about matched fields
    /// </summary>
    public class MemberSearchResultItem
    {
        /// <summary>
        /// Unique identifier for the member
        /// </summary>
        public int Id { get; set; }

        /// <summary>
        /// Member's first name
        /// </summary>
        public string FirstName { get; set; } = string.Empty;

        /// <summary>
        /// Member's last name
        /// </summary>
        public string LastName { get; set; } = string.Empty;

        /// <summary>
        /// Member's full name (computed property for display)
        /// </summary>
        public string FullName => $"{FirstName} {LastName}".Trim();

        /// <summary>
        /// Member's email address
        /// </summary>
        public string Email { get; set; } = string.Empty;

        /// <summary>
        /// Member's phone number
        /// </summary>
        public string Phone { get; set; } = string.Empty;

        /// <summary>
        /// Member's date of birth
        /// </summary>
        public DateTime DateOfBirth { get; set; }

        /// <summary>
        /// Member's current age (calculated from date of birth)
        /// </summary>
        public int Age => DateTime.Today.Year - DateOfBirth.Year - 
                         (DateTime.Today.DayOfYear < DateOfBirth.DayOfYear ? 1 : 0);

        /// <summary>
        /// Member's street address
        /// </summary>
        public string Address { get; set; } = string.Empty;

        /// <summary>
        /// Member's city
        /// </summary>
        public string City { get; set; } = string.Empty;

        /// <summary>
        /// Member's province/state
        /// </summary>
        public string Province { get; set; } = string.Empty;

        /// <summary>
        /// Member's postal/zip code
        /// </summary>
        public string PostalCode { get; set; } = string.Empty;

        /// <summary>
        /// Member's full address (computed property for display)
        /// </summary>
        public string FullAddress
        {
            get
            {
                var parts = new List<string>();
                if (!string.IsNullOrWhiteSpace(Address)) parts.Add(Address);
                if (!string.IsNullOrWhiteSpace(City)) parts.Add(City);
                if (!string.IsNullOrWhiteSpace(Province)) parts.Add(Province);
                if (!string.IsNullOrWhiteSpace(PostalCode)) parts.Add(PostalCode);
                return string.Join(", ", parts);
            }
        }

        /// <summary>
        /// Registration type ID
        /// </summary>
        public int RegistrationTypeId { get; set; }

        /// <summary>
        /// Registration type name (e.g., "Junior", "Coach", "Volunteer")
        /// </summary>
        public string RegistrationTypeName { get; set; } = string.Empty;

        /// <summary>
        /// Whether the member is currently active
        /// </summary>
        public bool IsActive { get; set; }

        /// <summary>
        /// Status display text for UI
        /// </summary>
        public string StatusText => IsActive ? "Active" : "Inactive";

        /// <summary>
        /// When the member was originally created/registered
        /// </summary>
        public DateTime DateCreated { get; set; }

        /// <summary>
        /// When the member record was last modified
        /// </summary>
        public DateTime? DateModified { get; set; }

        /// <summary>
        /// List of fields that matched the search criteria (for highlighting)
        /// </summary>
        public List<SearchMatchInfo> MatchedFields { get; set; } = new List<SearchMatchInfo>();

        /// <summary>
        /// Whether this member has any search matches (for UI styling)
        /// </summary>
        public bool HasMatches => MatchedFields.Any();

        /// <summary>
        /// Primary match information (first/best match for display)
        /// </summary>
        public SearchMatchInfo? PrimaryMatch => MatchedFields.FirstOrDefault();

        /// <summary>
        /// Count of how many fields matched the search
        /// </summary>
        public int MatchCount => MatchedFields.Count;
    }
}