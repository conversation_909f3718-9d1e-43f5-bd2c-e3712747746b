param (
    [Parameter(Mandatory=$true)]
    [ValidateSet("Staging", "Production")]
    [string]$Environment,
    
    [Parameter(Mandatory=$true)]
    [string]$WebsitePath
)

Write-Host "=== Running Database Migration for $Environment ===" -ForegroundColor Green

# Set connection string based on environment
if ($Environment -eq "Staging") {
    $connectionString = "Server=SIMBA\SQLEXPRESS;User Id=ParaHockeyUser;Password=***************;Database=ParaHockeyDB_TEST;Encrypt=False;"
    Write-Host "Target Database: ParaHockeyDB_TEST" -ForegroundColor Cyan
} else {
    $connectionString = "Server=SIMBA\SQLEXPRESS;User Id=ParaHockeyUser;Password=***************;Database=ParaHockeyDB;Encrypt=False;"
    Write-Host "Target Database: ParaHockeyDB" -ForegroundColor Cyan
}

Write-Host "Connection String: $connectionString" -ForegroundColor Cyan

# Check if .NET EF tools are installed
Write-Host "`nChecking .NET EF tools..." -ForegroundColor Yellow
$efToolCheck = dotnet tool list -g | Select-String "dotnet-ef"

if (-not $efToolCheck) {
    Write-Host "Installing .NET EF tools..." -ForegroundColor Yellow
    dotnet tool install --global dotnet-ef
    
    # Verify installation
    $efToolCheck = dotnet tool list -g | Select-String "dotnet-ef"
    if (-not $efToolCheck) {
        Write-Host "❌ Failed to install dotnet-ef tools!" -ForegroundColor Red
        exit 1
    }
    Write-Host "✅ .NET EF tools installed successfully" -ForegroundColor Green
} else {
    Write-Host "✅ .NET EF tools already installed" -ForegroundColor Green
}

# Navigate to the correct directory
Set-Location $WebsitePath
Write-Host "`nCurrent directory: $(Get-Location)" -ForegroundColor Cyan

# Verify we have the necessary files
if (-not (Test-Path "ParaHockey.dll")) {
    Write-Host "❌ ParaHockey.dll not found in $WebsitePath" -ForegroundColor Red
    exit 1
}

# List key files to verify deployment
Write-Host "`nKey files in directory:" -ForegroundColor Cyan
Get-ChildItem -Name "*.dll" | Where-Object { $_ -like "ParaHockey*" -or $_ -like "Microsoft.EntityFrameworkCore*" } | Select-Object -First 10

# Run migrations with detailed output
Write-Host "`nRunning Entity Framework migrations..." -ForegroundColor Yellow
try {
    # Execute migrations
    $output = dotnet ef database update --connection $connectionString --verbose 2>&1
    
    # Output the results
    $output | ForEach-Object { Write-Host $_ }
    
    # Check if we hit the IsMasterAdmin rename error
    if ($LASTEXITCODE -ne 0 -and ($output -join " ") -like "*IsMasterAdmin*IsActive*") {
        Write-Host "`n⚠️ Detected IsMasterAdmin rename issue. Applying fix..." -ForegroundColor Yellow
        
        # Run SQL to skip the problematic migration
        $sqlCmd = @"
IF NOT EXISTS (SELECT 1 FROM [__EFMigrationsHistory] WHERE [MigrationId] = '20250710211205_ReplaceAdminTypeWithLookupTable')
BEGIN
    INSERT INTO [__EFMigrationsHistory] ([MigrationId], [ProductVersion]) 
    VALUES ('20250710211205_ReplaceAdminTypeWithLookupTable', '8.0.0');
END
"@
        
        # Execute the fix using sqlcmd
        $sqlResult = sqlcmd -S "SIMBA\SQLEXPRESS" -d $(if ($Environment -eq 'Staging') { 'ParaHockeyDB_TEST' } else { 'ParaHockeyDB' }) -U ParaHockeyUser -P "***************" -Q $sqlCmd 2>&1
        
        if ($LASTEXITCODE -eq 0) {
            Write-Host "✅ Migration fix applied. Retrying migrations..." -ForegroundColor Green
            
            # Retry the migration
            $output = dotnet ef database update --connection $connectionString --verbose 2>&1
            $output | ForEach-Object { Write-Host $_ }
        }
    }
    
    if ($LASTEXITCODE -eq 0) {
        Write-Host "`n✅ $Environment database migration completed successfully!" -ForegroundColor Green
        
        # Log migration success details
        Write-Host "Migration Summary:" -ForegroundColor Cyan
        Write-Host "- Environment: $Environment" -ForegroundColor White
        Write-Host "- Database: $(if ($Environment -eq 'Staging') { 'ParaHockeyDB_TEST' } else { 'ParaHockeyDB' })" -ForegroundColor White
        Write-Host "- Path: $WebsitePath" -ForegroundColor White
        Write-Host "- Timestamp: $(Get-Date -Format 'yyyy-MM-dd HH:mm:ss')" -ForegroundColor White
    } else {
        Write-Host "`n❌ $Environment database migration failed with exit code: $LASTEXITCODE" -ForegroundColor Red
        exit 1
    }
} catch {
    Write-Host "`n❌ $Environment database migration failed with error: $_" -ForegroundColor Red
    exit 1
}

# Final status
Write-Host "`n=== Migration Process Complete ===" -ForegroundColor Green
exit 0