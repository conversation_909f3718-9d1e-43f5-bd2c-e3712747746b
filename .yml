[33m9d4f3ef[m[33m ([m[1;31morigin/main[m[33m, [m[1;31morigin/HEAD[m[33m)[m  CRITICAL FIX: Correct Package path for Production deployment
[33m0a9c51c[m Implement Windows PowerShell 5.1 TLS bypass for all health checks
[33ma4043a7[m Fix Test stage Web Deploy package path to avoid "No package found" error
[33mdffb275[m Replace Test stage PowerShell deployment with Web Deploy task
[33m3741d68[m Complete Task 4: Improve health-check scripts with proper TLS and module handling
[33m1248be7[m Fix pipeline access errors: Add health endpoint and update pipeline scripts
[33mef04938[m Still fixing the pipeline
[33m1850ec5[m Fixing the pipeline
[33m9fceaad[m[33m ([m[1;31morigin/Font-colour-depending-of-browser[m[33m, [m[1;32mFont-colour-depending-of-browser[m[33m)[m Small changes
[33mfce8710[m Add automated accessibility testing to Azure DevOps pipeline
