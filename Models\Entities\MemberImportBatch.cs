using System.ComponentModel.DataAnnotations;

namespace ParaHockeyApp.Models.Entities
{
    /// <summary>
    /// Tracks import operations for member data from Excel files
    /// </summary>
    public class MemberImportBatch : BaseEntity
    {
        /// <summary>
        /// Unique identifier for this import batch (using GUID for uniqueness across environments)
        /// </summary>
        public Guid ImportBatchId { get; set; } = Guid.NewGuid();

        /// <summary>
        /// Original filename of the uploaded Excel file
        /// </summary>
        [Required]
        [StringLength(255)]
        public string FileName { get; set; } = string.Empty;

        /// <summary>
        /// Email of the administrator who uploaded the file
        /// </summary>
        [Required]
        [StringLength(100)]
        public string UploadedBy { get; set; } = string.Empty;

        /// <summary>
        /// When the file was uploaded (UTC)
        /// </summary>
        [Required]
        public DateTime UploadedAtUtc { get; set; } = DateTime.UtcNow;

        /// <summary>
        /// Total number of rows processed from the file
        /// </summary>
        [Required]
        public int TotalRows { get; set; } = 0;

        /// <summary>
        /// Number of members successfully created
        /// </summary>
        [Required]
        public int CreatedCount { get; set; } = 0;

        /// <summary>
        /// Number of duplicates detected
        /// </summary>
        [Required]
        public int DuplicateCount { get; set; } = 0;

        /// <summary>
        /// Number of records that need fixes
        /// </summary>
        [Required]
        public int NeedsFixCount { get; set; } = 0;

        /// <summary>
        /// Number of duplicates that were merged
        /// </summary>
        [Required]
        public int MergedCount { get; set; } = 0;

        /// <summary>
        /// Number of records rejected
        /// </summary>
        [Required]
        public int RejectedCount { get; set; } = 0;

        /// <summary>
        /// Configuration used for this import (JSON)
        /// </summary>
        public string? ConfigurationJson { get; set; }

        /// <summary>
        /// Current status of the import batch
        /// </summary>
        [Required]
        [StringLength(20)]
        public string Status { get; set; } = "Processing";

        /// <summary>
        /// Error message if processing failed
        /// </summary>
        public string? ErrorMessage { get; set; }

        /// <summary>
        /// Navigation property to temp members in this batch
        /// </summary>
        public virtual ICollection<TempMember> TempMembers { get; set; } = new List<TempMember>();
    }
}