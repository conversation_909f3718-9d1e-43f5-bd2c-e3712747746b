﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace ParaHockeyApp.Migrations
{
    /// <inheritdoc />
    public partial class RemovePageInfoNameUniqueConstraint : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropIndex(
                name: "IX_PageInfos_Name",
                table: "PageInfos");

            migrationBuilder.CreateIndex(
                name: "IX_PageInfos_Name",
                table: "PageInfos",
                column: "Name");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropIndex(
                name: "IX_PageInfos_Name",
                table: "PageInfos");

            migrationBuilder.CreateIndex(
                name: "IX_PageInfos_Name",
                table: "PageInfos",
                column: "Name",
                unique: true);
        }
    }
}
