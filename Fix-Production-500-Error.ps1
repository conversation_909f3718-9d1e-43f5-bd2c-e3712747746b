# Quick Fix for Production HTTP 500.30 Error
# Run this on the SIMBA server to fix common startup issues

Write-Host "🔧 FIXING PRODUCTION HTTP 500.30 ERROR" -ForegroundColor Yellow
Write-Host "======================================" -ForegroundColor Yellow

$prodPath = "C:\inetpub\ParaHockey\Production"

# 1. Fix web.config environment
Write-Host "`n1. Fixing web.config environment..." -ForegroundColor Cyan
$webConfigPath = "$prodPath\web.config"
if (Test-Path $webConfigPath) {
    $webConfigContent = Get-Content $webConfigPath -Raw
    if ($webConfigContent -notmatch 'ASPNETCORE_ENVIRONMENT.*Production') {
        Write-Host "⚠️ Fixing web.config environment variable" -ForegroundColor Yellow
        # Fix the environment variable
        if ($webConfigContent -match '<environmentVariable name="ASPNETCORE_ENVIRONMENT" value="[^"]*"') {
            $webConfigContent = $webConfigContent -replace '<environmentVariable name="ASPNETCORE_ENVIRONMENT" value="[^"]*"', '<environmentVariable name="ASPNETCORE_ENVIRONMENT" value="Production"'
        } else {
            # Add environment variables section if not present
            if ($webConfigContent -notmatch '<environmentVariables>') {
                $webConfigContent = $webConfigContent -replace '(<aspNetCore[^>]*>)', '$1<environmentVariables><environmentVariable name="ASPNETCORE_ENVIRONMENT" value="Production" /></environmentVariables>'
            }
        }
        Set-Content -Path $webConfigPath -Value $webConfigContent -Force
        Write-Host "✅ web.config updated with ASPNETCORE_ENVIRONMENT=Production" -ForegroundColor Green
    } else {
        Write-Host "✅ web.config already has correct environment" -ForegroundColor Green
    }
} else {
    Write-Host "❌ web.config not found" -ForegroundColor Red
}

# 2. Create/restore appsettings.Production.json if missing
Write-Host "`n2. Ensuring appsettings.Production.json exists..." -ForegroundColor Cyan
$prodConfigPath = "$prodPath\appsettings.Production.json"
if (!(Test-Path $prodConfigPath)) {
    Write-Host "⚠️ Creating missing appsettings.Production.json" -ForegroundColor Yellow
    $defaultProdConfig = @{
        "DetailedErrors" = $false
        "Logging" = @{
            "LogLevel" = @{
                "Default" = "Warning"
                "Microsoft.AspNetCore" = "Warning"
            }
        }
        "ConnectionStrings" = @{
            "DefaultConnection" = "Server=SIMBA\\SQLEXPRESS;User Id=ParaHockeyUser;Password=***************;Database=ParaHockeyDB;Encrypt=False;TrustServerCertificate=True;"
        }
        "Environment" = @{
            "Name" = "PRODUCTION"
            "Theme" = "primary"
            "ShowBanner" = $false
            "UseAuthentication" = $true
            "BannerText" = ""
            "ShowDevelopmentTools" = $false
            "EnableDetailedErrorLogging" = $false
            "EnvironmentIndicatorColor" = "primary"
            "ShowUserFriendlyErrors" = $true
            "ErrorDetailLevel" = "minimal"
        }
        "Email" = @{
            "SmtpHost" = "smtp.office365.com"
            "SmtpPort" = "587"
            "Username" = "<EMAIL>"
            "Password" = "L@535539113654on"
            "FromEmail" = "<EMAIL>"
            "FromName" = "Parahockey Verification"
        }
    }
    $defaultProdConfig | ConvertTo-Json -Depth 10 | Set-Content -Path $prodConfigPath -Force
    Write-Host "✅ Created appsettings.Production.json" -ForegroundColor Green
} else {
    Write-Host "✅ appsettings.Production.json already exists" -ForegroundColor Green
}

# 3. Restart IIS App Pool
Write-Host "`n3. Restarting Production app pool..." -ForegroundColor Cyan
Import-Module WebAdministration -ErrorAction SilentlyContinue
try {
    $appPool = Get-IISAppPool -Name "ParaHockey-Production" -ErrorAction SilentlyContinue
    if ($appPool) {
        if ($appPool.State -eq "Started") {
            Stop-WebAppPool -Name "ParaHockey-Production"
            Write-Host "⏸️ App pool stopped" -ForegroundColor Yellow
            Start-Sleep -Seconds 5
        }
        Start-WebAppPool -Name "ParaHockey-Production"
        Write-Host "▶️ App pool started" -ForegroundColor Green
        Start-Sleep -Seconds 10
        
        # Check final state
        $finalState = (Get-IISAppPool -Name "ParaHockey-Production").State
        Write-Host "Final app pool state: $finalState" -ForegroundColor Cyan
    } else {
        Write-Host "❌ App pool 'ParaHockey-Production' not found" -ForegroundColor Red
    }
} catch {
    Write-Host "❌ Error restarting app pool: $_" -ForegroundColor Red
}

# 4. Test the site
Write-Host "`n4. Testing Production site..." -ForegroundColor Cyan
try {
    # TLS bypass for testing
    add-type @"
    using System.Net;
    using System.Security.Cryptography.X509Certificates;
    public class TrustAllCertsPolicy : ICertificatePolicy {
        public bool CheckValidationResult(ServicePoint sp, X509Certificate cert,
                                        WebRequest req, int problem) { return true; }
    }
"@
    [System.Net.ServicePointManager]::CertificatePolicy = New-Object TrustAllCertsPolicy
    [Net.ServicePointManager]::SecurityProtocol = [Net.SecurityProtocolType]::Tls12
    
    $response = Invoke-WebRequest -Uri "https://parahockey.complys.com/" -TimeoutSec 30 -UseBasicParsing
    if ($response.StatusCode -eq 200) {
        Write-Host "✅ Production site is responding! Status: $($response.StatusCode)" -ForegroundColor Green
    } else {
        Write-Host "⚠️ Production site responded with status: $($response.StatusCode)" -ForegroundColor Yellow
    }
} catch {
    Write-Host "❌ Production site test failed: $_" -ForegroundColor Red
    Write-Host "Check the steps above and try running the diagnostic script." -ForegroundColor Yellow
}

Write-Host "`n🎉 PRODUCTION FIX COMPLETED" -ForegroundColor Green
Write-Host "If the site is still not working, run Diagnose-Production-500-Error.ps1 for detailed analysis." -ForegroundColor White