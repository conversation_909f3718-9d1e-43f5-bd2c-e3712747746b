using Microsoft.EntityFrameworkCore;
using ParaHockeyApp.Models;
using ParaHockeyApp.Models.Entities;

namespace ParaHockeyApp.Services
{
    /// <summary>
    /// DEPRECATED: This service is being phased out in favor of the new AuditLog system.
    /// Historical data has been migrated to AuditLog table.
    /// This service is kept temporarily for backward compatibility.
    /// </summary>
    public class MemberLogService : IMemberLogService
    {
        private readonly ApplicationContext _context;

        public MemberLogService(ApplicationContext context)
        {
            _context = context;
        }

        public async Task LogMemberCreatedAsync(Member member, string userEmail)
        {
            var memberLog = new MemberLog
            {
                MemberId = member.Id,
                LogDate = DateTime.UtcNow,
                Description = "New member registration",
                EditorId = member.Id, // For new registration, editor is the member themselves
                EditorName = $"{member.FirstName} {member.LastName}" // Member's own name for new registration
            };

            _context.MemberLogs.Add(memberLog);
            await _context.SaveChangesAsync();
        }

        public async Task LogMemberModifiedAsync(Member originalMember, Member updatedMember, int editorId)
        {
            var changes = GetMemberChanges(originalMember, updatedMember);
            
            if (changes.Any())
            {
                // Get editor name by querying Members table using EditorId
                var editor = await _context.Members.FindAsync(editorId);
                var editorName = editor != null ? $"{editor.FirstName} {editor.LastName}" : "Unknown Editor";
                
                var memberLog = new MemberLog
                {
                    MemberId = updatedMember.Id,
                    LogDate = DateTime.UtcNow,
                    Description = string.Join(", ", changes),
                    EditorId = editorId,
                    EditorName = editorName
                };

                _context.MemberLogs.Add(memberLog);
                await _context.SaveChangesAsync();
            }
        }

        public async Task<List<MemberLog>> GetMemberLogsAsync(int memberId)
        {
            return await _context.MemberLogs
                .Where(ml => ml.MemberId == memberId)
                .Include(ml => ml.Member)
                .OrderByDescending(ml => ml.LogDate)
                .ToListAsync();
        }

        public async Task<List<MemberLog>> GetAllLogsAsync(int pageNumber = 1, int pageSize = 50)
        {
            return await _context.MemberLogs
                .Include(ml => ml.Member)
                .OrderByDescending(ml => ml.LogDate)
                .Skip((pageNumber - 1) * pageSize)
                .Take(pageSize)
                .ToListAsync();
        }

        private List<string> GetMemberChanges(Member original, Member updated)
        {
            var changes = new List<string>();

            if (original.FirstName != updated.FirstName)
                changes.Add($"FirstName: '{original.FirstName}' → '{updated.FirstName}'");

            if (original.LastName != updated.LastName)
                changes.Add($"LastName: '{original.LastName}' → '{updated.LastName}'");

            if (original.Email != updated.Email)
                changes.Add($"Email: '{original.Email}' → '{updated.Email}'");

            if (original.Phone != updated.Phone)
                changes.Add($"Phone: '{original.Phone}' → '{updated.Phone}'");

            if (original.Address != updated.Address)
                changes.Add($"Address: '{original.Address}' → '{updated.Address}'");

            if (original.City != updated.City)
                changes.Add($"City: '{original.City}' → '{updated.City}'");

            if (original.PostalCode != updated.PostalCode)
                changes.Add($"PostalCode: '{original.PostalCode}' → '{updated.PostalCode}'");

            if (original.DateOfBirth != updated.DateOfBirth)
                changes.Add($"DateOfBirth: '{original.DateOfBirth.ToString("yyyy-MM-dd")}' → '{updated.DateOfBirth.ToString("yyyy-MM-dd")}'");

            if (original.GenderId != updated.GenderId)
                changes.Add($"Gender: '{original.GenderId}' → '{updated.GenderId}'");

            if (original.ProvinceId != updated.ProvinceId)
                changes.Add($"Province: '{original.ProvinceId}' → '{updated.ProvinceId}'");

            if (original.PhoneTypeId != updated.PhoneTypeId)
                changes.Add($"PhoneType: '{original.PhoneTypeId}' → '{updated.PhoneTypeId}'");

            if (original.RegistrationTypeId != updated.RegistrationTypeId)
                changes.Add($"RegistrationType: '{original.RegistrationTypeId}' → '{updated.RegistrationTypeId}'");

            if (original.HQc_Id != updated.HQc_Id)
                changes.Add($"HQc_Id: '{original.HQc_Id ?? "null"}' → '{updated.HQc_Id ?? "null"}'");

            if (original.IsActive != updated.IsActive)
                changes.Add($"IsActive: '{original.IsActive}' → '{updated.IsActive}'");

            return changes;
        }
    }
}