#!/usr/bin/env pwsh

Write-Host "Testing Modern Form Validation Framework" -ForegroundColor Cyan
Write-Host "=========================================" -ForegroundColor Cyan

# Test 1: Check FormValidationService
Write-Host "`nTest 1: FormValidationService" -ForegroundColor Yellow
$serviceFile = "Services/FormValidationService.cs"
if (Test-Path $serviceFile) {
    $content = Get-Content $serviceFile -Raw
    if ($content -match "GetClientValidationRules" -and $content -match "GetHtml5ValidationAttributes") {
        Write-Host "✅ FormValidationService methods found" -ForegroundColor Green
    } else {
        Write-Host "❌ FormValidationService methods missing" -ForegroundColor Red
    }
} else {
    Write-Host "❌ FormValidationService file not found" -ForegroundColor Red
}

# Test 2: Check BaseFormViewModel
Write-Host "`nTest 2: BaseFormViewModel" -ForegroundColor Yellow
$viewModelFile = "ViewModels/Forms/BaseFormViewModel.cs"
if (Test-Path $viewModelFile) {
    $content = Get-Content $viewModelFile -Raw
    if ($content -match "GetHtml5ValidationAttributes" -and $content -match "GetPlaceholder") {
        Write-Host "✅ BaseFormViewModel enhancements found" -ForegroundColor Green
    } else {
        Write-Host "❌ BaseFormViewModel enhancements missing" -ForegroundColor Red
    }
} else {
    Write-Host "❌ BaseFormViewModel file not found" -ForegroundColor Red
}

# Test 3: Check JavaScript validation
Write-Host "`nTest 3: JavaScript Validation" -ForegroundColor Yellow
$jsFile = "wwwroot/js/enhanced-form-validation.js"
if (Test-Path $jsFile) {
    $content = Get-Content $jsFile -Raw
    if ($content -match "formatPhoneNumber" -and $content -match "formatPostalCode") {
        Write-Host "✅ JavaScript formatting functions found" -ForegroundColor Green
    } else {
        Write-Host "❌ JavaScript formatting functions missing" -ForegroundColor Red
    }
} else {
    Write-Host "❌ JavaScript validation file not found" -ForegroundColor Red
}

# Test 4: Check Tag Helpers
Write-Host "`nTest 4: Tag Helpers" -ForegroundColor Yellow
$tagHelperFile = "TagHelpers/EnhancedFormTagHelper.cs"
if (Test-Path $tagHelperFile) {
    $content = Get-Content $tagHelperFile -Raw
    if ($content -match "GetHtml5ValidationAttributes" -and $content -match "aria-describedby") {
        Write-Host "✅ Enhanced Tag Helpers found" -ForegroundColor Green
    } else {
        Write-Host "❌ Enhanced Tag Helpers missing features" -ForegroundColor Red
    }
} else {
    Write-Host "❌ Tag Helper file not found" -ForegroundColor Red
}

# Test 5: Check Validation Attributes
Write-Host "`nTest 5: Validation Attributes" -ForegroundColor Yellow
$attributesFile = "Attributes/LocalizedValidationAttributes.cs"
if (Test-Path $attributesFile) {
    $content = Get-Content $attributesFile -Raw
    if ($content -match "EnhancedCanadianPostalCodeAttribute" -and $content -match "ComprehensiveValidationAttribute") {
        Write-Host "✅ Enhanced validation attributes found" -ForegroundColor Green
    } else {
        Write-Host "❌ Enhanced validation attributes missing" -ForegroundColor Red
    }
} else {
    Write-Host "❌ Validation attributes file not found" -ForegroundColor Red
}

Write-Host "`nModern Form Validation Framework Test Complete!" -ForegroundColor Green