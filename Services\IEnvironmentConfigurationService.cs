using ParaHockeyApp.Models.Configuration;

namespace ParaHockeyApp.Services
{
    /// <summary>
    /// Service for validating and managing environment configuration consistency
    /// </summary>
    public interface IEnvironmentConfigurationService
    {
        /// <summary>
        /// Validates the current environment configuration
        /// </summary>
        /// <returns>Validation result with any errors found</returns>
        Task<EnvironmentValidationResult> ValidateConfigurationAsync();

        /// <summary>
        /// Gets the current environment settings
        /// </summary>
        /// <returns>Current environment settings</returns>
        EnvironmentSettings GetCurrentEnvironmentSettings();

        /// <summary>
        /// Checks if the current configuration allows the specified feature
        /// </summary>
        /// <param name="feature">Feature to check</param>
        /// <returns>True if feature is allowed in current environment</returns>
        bool IsFeatureAllowed(string feature);

        /// <summary>
        /// Gets environment-specific error handling configuration
        /// </summary>
        /// <returns>Error handling configuration for current environment</returns>
        ErrorHandlingConfiguration GetErrorHandlingConfiguration();

        /// <summary>
        /// Gets the configured age of majority for registration purposes
        /// </summary>
        /// <returns>Age of majority (default: 18)</returns>
        int GetAgeOfMajority();
    }

    /// <summary>
    /// Result of environment configuration validation
    /// </summary>
    public class EnvironmentValidationResult
    {
        public bool IsValid { get; set; }
        public List<string> Errors { get; set; } = new List<string>();
        public List<string> Warnings { get; set; } = new List<string>();
        public string EnvironmentName { get; set; } = string.Empty;
    }

    /// <summary>
    /// Error handling configuration for different environments
    /// </summary>
    public class ErrorHandlingConfiguration
    {
        public bool ShowDetailedErrors { get; set; }
        public bool LogStackTraces { get; set; }
        public bool ShowUserFriendlyMessages { get; set; }
        public string ErrorDetailLevel { get; set; } = "standard";
        public bool EnableErrorReporting { get; set; }
    }
}