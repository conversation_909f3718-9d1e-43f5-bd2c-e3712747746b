using ParaHockeyApp.DTOs;
using ParaHockeyApp.Models.Entities;
using System.Threading.Tasks;
using System.Collections.Generic;

namespace ParaHockeyApp.Services
{
    /// <summary>
    /// Service interface for advanced member search functionality
    /// Provides comprehensive search, filtering, export, and saved search capabilities
    /// </summary>
    public interface IMemberSearchService
    {
        /// <summary>
        /// Performs advanced member search with multiple criteria and filters
        /// </summary>
        /// <param name="request">Search request containing all search criteria and pagination parameters</param>
        /// <returns>Search results with pagination metadata and matched field information</returns>
        Task<MemberSearchResult> SearchMembersAsync(MemberSearchRequest request);

        /// <summary>
        /// Retrieves all available registration types for filtering
        /// </summary>
        /// <returns>List of active registration types</returns>
        Task<List<RegistrationType>> GetRegistrationTypesAsync();

        /// <summary>
        /// Exports member search results to CSV format
        /// </summary>
        /// <param name="request">Search request to filter members for export</param>
        /// <returns>CSV file content as byte array</returns>
        Task<byte[]> ExportMembersAsync(MemberSearchRequest request);

        /// <summary>
        /// Saves a search configuration for future use
        /// </summary>
        /// <param name="request">Search criteria to save</param>
        /// <param name="searchName">User-friendly name for the saved search</param>
        /// <param name="userId">ID of the user saving the search</param>
        /// <returns>The saved search entity</returns>
        Task<SavedSearch> SaveSearchAsync(MemberSearchRequest request, string searchName, string userId);

        /// <summary>
        /// Retrieves all saved searches for a specific user
        /// </summary>
        /// <param name="userId">ID of the user whose searches to retrieve</param>
        /// <returns>List of saved searches ordered by last used date</returns>
        Task<List<SavedSearch>> GetSavedSearchesAsync(string userId);

        /// <summary>
        /// Loads a saved search configuration
        /// </summary>
        /// <param name="searchId">ID of the saved search to load</param>
        /// <param name="userId">ID of the user (for authorization)</param>
        /// <returns>The search request configuration or null if not found/unauthorized</returns>
        Task<MemberSearchRequest?> LoadSavedSearchAsync(int searchId, string userId);

        /// <summary>
        /// Deletes a saved search
        /// </summary>
        /// <param name="searchId">ID of the saved search to delete</param>
        /// <param name="userId">ID of the user (for authorization)</param>
        /// <returns>True if deleted successfully, false if not found or unauthorized</returns>
        Task<bool> DeleteSavedSearchAsync(int searchId, string userId);

        /// <summary>
        /// Updates the last used timestamp for a saved search
        /// </summary>
        /// <param name="searchId">ID of the saved search</param>
        /// <param name="userId">ID of the user</param>
        Task UpdateSavedSearchLastUsedAsync(int searchId, string userId);

        /// <summary>
        /// Normalizes a phone number for searching (removes formatting characters)
        /// </summary>
        /// <param name="phoneNumber">Phone number to normalize</param>
        /// <returns>Normalized phone number suitable for searching</returns>
        string NormalizePhoneNumber(string phoneNumber);

        /// <summary>
        /// Retrieves distinct cities from the member database with optional search term filtering
        /// </summary>
        /// <param name="searchTerm">Optional search term to filter cities (case-insensitive)</param>
        /// <returns>List of distinct cities ordered alphabetically</returns>
        Task<List<string>> GetDistinctCitiesAsync(string? searchTerm = null);

        /// <summary>
        /// Retrieves distinct provinces from the member database for filter options
        /// </summary>
        /// <returns>List of distinct provinces ordered alphabetically</returns>
        Task<List<string>> GetDistinctProvincesAsync();

        /// <summary>
        /// Retrieves registration type counts for filter options with member counts
        /// </summary>
        /// <returns>Dictionary with registration type display names as keys and member counts as values</returns>
        Task<Dictionary<string, int>> GetRegistrationTypeCountsAsync();
    }
}