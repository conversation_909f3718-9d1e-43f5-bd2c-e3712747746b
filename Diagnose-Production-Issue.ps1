# Diagnose Production Site Issues
# Run this script ON THE SIMBA SERVER as Administrator

Write-Host "=== Production Site Diagnostics ===" -ForegroundColor Green

$prodPath = "C:\inetpub\ParaHockey\Production"

# Check what files exist in production directory
Write-Host "`n📁 Checking Production directory contents..." -ForegroundColor Yellow
if (Test-Path $prodPath) {
    Write-Host "✅ Production directory exists: $prodPath" -ForegroundColor Green
    
    $files = Get-ChildItem $prodPath | Select-Object Name, Length, LastWriteTime
    Write-Host "`n📄 Files in Production directory:" -ForegroundColor Cyan
    $files | Format-Table -AutoSize | Out-String | Write-Host
    
    # Check for key files
    $keyFiles = @("ParaHockeyApp.dll", "web.config", "appsettings.json", "appsettings.Production.json")
    foreach ($file in $keyFiles) {
        $filePath = Join-Path $prodPath $file
        if (Test-Path $filePath) {
            Write-Host "✅ Found: $file" -ForegroundColor Green
        } else {
            Write-Host "❌ Missing: $file" -ForegroundColor Red
        }
    }
} else {
    Write-Host "❌ Production directory not found: $prodPath" -ForegroundColor Red
    exit
}

# Check web.config content
Write-Host "`n📋 Checking web.config..." -ForegroundColor Yellow
$webConfigPath = Join-Path $prodPath "web.config"
if (Test-Path $webConfigPath) {
    try {
        $webConfigContent = Get-Content $webConfigPath -Raw
        Write-Host "✅ web.config content:" -ForegroundColor Green
        Write-Host $webConfigContent -ForegroundColor White
    } catch {
        Write-Host "❌ Could not read web.config: $_" -ForegroundColor Red
    }
} else {
    Write-Host "❌ web.config not found" -ForegroundColor Red
}

# Check appsettings.Production.json
Write-Host "`n📋 Checking appsettings.Production.json..." -ForegroundColor Yellow
$prodSettingsPath = Join-Path $prodPath "appsettings.Production.json"
if (Test-Path $prodSettingsPath) {
    try {
        $prodSettings = Get-Content $prodSettingsPath -Raw
        Write-Host "✅ appsettings.Production.json content:" -ForegroundColor Green
        Write-Host $prodSettings -ForegroundColor White
    } catch {
        Write-Host "❌ Could not read appsettings.Production.json: $_" -ForegroundColor Red
    }
} else {
    Write-Host "❌ appsettings.Production.json not found" -ForegroundColor Red
}

# Check IIS application pool status
Write-Host "`n📋 Checking IIS Application Pool status..." -ForegroundColor Yellow
try {
    $appcmd = "${env:windir}\system32\inetsrv\appcmd.exe"
    if (Test-Path $appcmd) {
        Write-Host "App Pool Status:" -ForegroundColor Cyan
        & $appcmd list apppool "ParaHockey-Production" /text:* | Write-Host
        
        Write-Host "`nWebsite Status:" -ForegroundColor Cyan  
        & $appcmd list site "ParaHockey-Production" /text:* | Write-Host
    }
} catch {
    Write-Warning "Could not check IIS status: $_"
}

# Try to manually test the application startup
Write-Host "`n📋 Testing manual application startup..." -ForegroundColor Yellow
try {
    $dllPath = Join-Path $prodPath "ParaHockeyApp.dll"
    if (Test-Path $dllPath) {
        Write-Host "Attempting to run dotnet ParaHockeyApp.dll..." -ForegroundColor Cyan
        
        # Set environment variable for Production
        $env:ASPNETCORE_ENVIRONMENT = "Production"
        
        # Try to start the application and capture output
        $processInfo = New-Object System.Diagnostics.ProcessStartInfo
        $processInfo.FileName = "dotnet"
        $processInfo.Arguments = "`"$dllPath`""
        $processInfo.WorkingDirectory = $prodPath
        $processInfo.RedirectStandardOutput = $true
        $processInfo.RedirectStandardError = $true
        $processInfo.UseShellExecute = $false
        $processInfo.CreateNoWindow = $true
        
        $process = New-Object System.Diagnostics.Process
        $process.StartInfo = $processInfo
        
        Write-Host "Starting process..." -ForegroundColor Cyan
        $process.Start() | Out-Null
        
        # Wait a few seconds for startup
        Start-Sleep -Seconds 5
        
        if (!$process.HasExited) {
            Write-Host "✅ Application started successfully (process still running)" -ForegroundColor Green
            $process.Kill()
        } else {
            Write-Host "❌ Application exited during startup" -ForegroundColor Red
            Write-Host "Exit Code: $($process.ExitCode)" -ForegroundColor Red
        }
        
        # Get any output
        $stdout = $process.StandardOutput.ReadToEnd()
        $stderr = $process.StandardError.ReadToEnd()
        
        if ($stdout) {
            Write-Host "`n📤 Standard Output:" -ForegroundColor Green
            Write-Host $stdout -ForegroundColor White
        }
        
        if ($stderr) {
            Write-Host "`n🔴 Error Output:" -ForegroundColor Red
            Write-Host $stderr -ForegroundColor White
        }
        
    } else {
        Write-Host "❌ ParaHockeyApp.dll not found" -ForegroundColor Red
    }
} catch {
    Write-Host "❌ Could not test manual startup: $_" -ForegroundColor Red
}

# Check .NET runtime
Write-Host "`n📋 Checking .NET Runtime..." -ForegroundColor Yellow
try {
    $dotnetInfo = dotnet --info 2>&1
    Write-Host "✅ .NET Info:" -ForegroundColor Green
    Write-Host $dotnetInfo -ForegroundColor White
} catch {
    Write-Host "❌ Could not get .NET info: $_" -ForegroundColor Red
}

Write-Host "`n=== Diagnostics Complete ===" -ForegroundColor Green
Write-Host "Please send me the output above, especially:" -ForegroundColor White
Write-Host "1. 📄 Contents of web.config and appsettings.Production.json" -ForegroundColor White
Write-Host "2. 🔴 Any error output from manual startup test" -ForegroundColor White
Write-Host "3. ℹ️ .NET runtime information" -ForegroundColor White

Write-Host "`n" -ForegroundColor Yellow
Write-Host "PRESS ANY KEY TO EXIT..." -ForegroundColor Yellow -BackgroundColor Red
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")