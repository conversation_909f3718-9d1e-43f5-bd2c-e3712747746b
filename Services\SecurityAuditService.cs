using Microsoft.EntityFrameworkCore;
using ParaHockeyApp.Models;
using System.Reflection;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using System.Text.RegularExpressions;
using System.ComponentModel.DataAnnotations;

namespace ParaHockeyApp.Services
{
    /// <summary>
    /// Service for comprehensive security auditing and enhancement
    /// Provides automated scanning and validation of security best practices
    /// </summary>
    public class SecurityAuditService : ISecurityAuditService
    {
        private readonly ApplicationContext _context;
        private readonly ILogger<SecurityAuditService> _logger;
        private readonly IWebHostEnvironment _environment;
        private readonly IConfiguration _configuration;

        public SecurityAuditService(
            ApplicationContext context,
            ILogger<SecurityAuditService> logger,
            IWebHostEnvironment environment,
            IConfiguration configuration)
        {
            _context = context;
            _logger = logger;
            _environment = environment;
            _configuration = configuration;
        }

        public async Task<SecurityAuditResult> AuditPageSecurityAsync(string pageName)
        {
            _logger.LogInformation("Starting security audit for page {PageName}", pageName);

            var result = new SecurityAuditResult
            {
                PageName = pageName,
                AuditDate = DateTime.UtcNow
            };

            try
            {
                // Parse controller and action from page name
                var parts = pageName.Split('/');
                if (parts.Length != 2)
                {
                    throw new ArgumentException($"Invalid page name format: {pageName}. Expected format: Controller/Action");
                }

                var controllerName = parts[0];
                var actionName = parts[1];

                // Audit anti-forgery tokens
                result.HasAntiForgeryTokens = await CheckAntiForgeryTokensAsync(controllerName, actionName);

                // Audit authorization
                result.HasProperAuthorization = await CheckAuthorizationAsync(controllerName, actionName);

                // Audit view models usage
                result.UsesViewModels = await CheckViewModelsUsageAsync(controllerName, actionName);

                // Audit server-side validation
                result.HasServerSideValidation = await CheckServerSideValidationAsync(controllerName, actionName);

                // Audit output encoding
                result.HasProperOutputEncoding = await CheckOutputEncodingAsync(controllerName, actionName);

                // Audit cookie security
                var cookieAudit = await CheckCookieSecurityAsync();
                result.HasSecureCookies = cookieAudit.UsesSecureDefaults;

                // Audit CSP headers
                result.HasCSPHeaders = CheckCSPHeaders();

                // Calculate security score
                result.SecurityScore = CalculateSecurityScore(result);

                // Generate issues and recommendations
                await GenerateSecurityIssuesAsync(result);
                GenerateSecurityRecommendations(result);

                _logger.LogInformation("Security audit completed for page {PageName}. Score: {Score}%", 
                    pageName, result.SecurityScore);

                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error during security audit for page {PageName}", pageName);
                result.Issues.Add(new SecurityIssue
                {
                    Title = "Audit Error",
                    Description = $"Failed to complete security audit: {ex.Message}",
                    RiskLevel = SecurityRiskLevel.Medium,
                    Location = pageName,
                    Impact = "Unable to assess security posture",
                    CanAutoFix = false
                });
                return result;
            }
        }

        public async Task<List<AntiForgeryAuditResult>> ScanAntiForgeryTokensAsync()
        {
            _logger.LogInformation("Scanning for anti-forgery token usage across all forms");

            var results = new List<AntiForgeryAuditResult>();

            try
            {
                // Get all controller types
                var controllerTypes = GetAllControllerTypes();

                foreach (var controllerType in controllerTypes)
                {
                    var controllerName = controllerType.Name.Replace("Controller", "");
                    var actions = GetControllerActions(controllerType);

                    foreach (var action in actions)
                    {
                        // Check if action accepts POST requests
                        if (IsPostAction(action))
                        {
                            var result = new AntiForgeryAuditResult
                            {
                                ActionMethod = $"{controllerName}/{action.Name}",
                                ViewPath = $"Views/{controllerName}/{action.Name}.cshtml",
                                FormLocation = $"{controllerName}Controller.{action.Name}",
                                HasValidateAntiForgeryAttribute = HasValidateAntiForgeryAttribute(action),
                                HasAntiForgeryToken = await CheckViewForAntiForgeryTokenAsync(controllerName, action.Name)
                            };

                            // Determine risk level
                            if (!result.HasValidateAntiForgeryAttribute && !result.HasAntiForgeryToken)
                            {
                                result.RiskLevel = SecurityRiskLevel.Critical;
                                result.Recommendation = "Add [ValidateAntiForgeryToken] attribute and @Html.AntiForgeryToken() in view";
                            }
                            else if (!result.HasValidateAntiForgeryAttribute)
                            {
                                result.RiskLevel = SecurityRiskLevel.High;
                                result.Recommendation = "Add [ValidateAntiForgeryToken] attribute to action method";
                            }
                            else if (!result.HasAntiForgeryToken)
                            {
                                result.RiskLevel = SecurityRiskLevel.High;
                                result.Recommendation = "Add @Html.AntiForgeryToken() to the form in the view";
                            }
                            else
                            {
                                result.RiskLevel = SecurityRiskLevel.Low;
                                result.Recommendation = "CSRF protection properly implemented";
                            }

                            results.Add(result);
                        }
                    }
                }

                _logger.LogInformation("Anti-forgery token scan completed. Found {Count} POST actions", results.Count);
                return results;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error during anti-forgery token scan");
                return results;
            }
        }

        public async Task<List<AuthorizationAuditResult>> AnalyzeAuthorizationAsync()
        {
            _logger.LogInformation("Analyzing authorization attributes across all controller actions");

            var results = new List<AuthorizationAuditResult>();

            try
            {
                var controllerTypes = GetAllControllerTypes();

                foreach (var controllerType in controllerTypes)
                {
                    var controllerName = controllerType.Name.Replace("Controller", "");
                    var controllerAuthorize = controllerType.GetCustomAttribute<AuthorizeAttribute>();
                    var controllerAllowAnonymous = controllerType.GetCustomAttribute<AllowAnonymousAttribute>();

                    var actions = GetControllerActions(controllerType);

                    foreach (var action in actions)
                    {
                        var actionAuthorize = action.GetCustomAttribute<AuthorizeAttribute>();
                        var actionAllowAnonymous = action.GetCustomAttribute<AllowAnonymousAttribute>();

                        var result = new AuthorizationAuditResult
                        {
                            ControllerName = controllerName,
                            ActionName = action.Name,
                            FullActionPath = $"{controllerName}/{action.Name}",
                            HasAuthorizeAttribute = controllerAuthorize != null || actionAuthorize != null,
                            HasAllowAnonymousAttribute = controllerAllowAnonymous != null || actionAllowAnonymous != null,
                            IsPublicEndpoint = IsPublicEndpoint(controllerName, action.Name)
                        };

                        // Extract roles and policies
                        var authorizeAttr = actionAuthorize ?? controllerAuthorize;
                        if (authorizeAttr != null)
                        {
                            result.RequiredRoles = string.IsNullOrEmpty(authorizeAttr.Roles) 
                                ? Array.Empty<string>() 
                                : authorizeAttr.Roles.Split(',').Select(r => r.Trim()).ToArray();
                            
                            result.RequiredPolicies = string.IsNullOrEmpty(authorizeAttr.Policy) 
                                ? Array.Empty<string>() 
                                : new[] { authorizeAttr.Policy };
                        }

                        // Determine risk level and recommendations
                        DetermineAuthorizationRisk(result);

                        results.Add(result);
                    }
                }

                _logger.LogInformation("Authorization analysis completed. Analyzed {Count} actions", results.Count);
                return results;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error during authorization analysis");
                return results;
            }
        }

        public async Task<List<CsrfProtectionAuditResult>> ScanCsrfProtectionAsync()
        {
            _logger.LogInformation("Scanning CSRF protection across all controller actions");

            var results = new List<CsrfProtectionAuditResult>();

            try
            {
                var controllerTypes = GetAllControllerTypes();

                foreach (var controllerType in controllerTypes)
                {
                    var controllerName = controllerType.Name.Replace("Controller", "");
                    var actions = GetControllerActions(controllerType);

                    foreach (var action in actions)
                    {
                        var result = new CsrfProtectionAuditResult
                        {
                            ControllerName = controllerName,
                            ActionName = action.Name,
                            IsProtected = HasCsrfProtection(action),
                            RiskLevel = DetermineCsrfRiskLevel(action)
                        };

                        if (result.IsProtected)
                        {
                            result.ProtectionMethods.Add("ValidateAntiForgeryToken");
                        }

                        // Add recommendations based on risk level
                        if (!result.IsProtected && result.RiskLevel >= SecurityRiskLevel.High)
                        {
                            result.Recommendation = "Add [ValidateAntiForgeryToken] attribute to this action";
                        }

                        results.Add(result);
                    }
                }

                _logger.LogInformation("CSRF protection scan completed. Analyzed {Count} actions", results.Count);
                return results;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error during CSRF protection scan");
                return results;
            }
        }

        public async Task<List<OverPostingAuditResult>> ScanOverPostingVulnerabilitiesAsync()
        {
            _logger.LogInformation("Scanning for over-posting vulnerabilities in model binding");

            var results = new List<OverPostingAuditResult>();

            try
            {
                var controllerTypes = GetAllControllerTypes();

                foreach (var controllerType in controllerTypes)
                {
                    var controllerName = controllerType.Name.Replace("Controller", "");
                    var actions = GetControllerActions(controllerType);

                    foreach (var action in actions)
                    {
                        if (IsPostAction(action))
                        {
                            var parameters = action.GetParameters();

                            foreach (var parameter in parameters)
                            {
                                // Check if parameter is a complex type (potential model)
                                if (IsComplexType(parameter.ParameterType))
                                {
                                    var result = new OverPostingAuditResult
                                    {
                                        ControllerName = controllerName,
                                        ActionName = action.Name,
                                        ParameterName = parameter.Name ?? "unknown",
                                        ParameterType = parameter.ParameterType.Name,
                                        UsesBindAttribute = parameter.GetCustomAttribute<BindAttribute>() != null,
                                        UsesViewModel = IsViewModel(parameter.ParameterType)
                                    };

                                    // Determine vulnerability and risk
                                    DetermineOverPostingRisk(result, parameter);

                                    results.Add(result);
                                }
                            }
                        }
                    }
                }

                _logger.LogInformation("Over-posting vulnerability scan completed. Found {Count} potential issues", results.Count);
                return results;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error during over-posting vulnerability scan");
                return results;
            }
        }

        public async Task<List<OutputEncodingAuditResult>> ValidateOutputEncodingAsync()
        {
            _logger.LogInformation("Validating output encoding in Razor views");

            var results = new List<OutputEncodingAuditResult>();

            try
            {
                var viewsPath = Path.Combine(_environment.ContentRootPath, "Views");
                if (Directory.Exists(viewsPath))
                {
                    var viewFiles = Directory.GetFiles(viewsPath, "*.cshtml", SearchOption.AllDirectories);

                    foreach (var viewFile in viewFiles)
                    {
                        await ScanViewForOutputEncodingIssuesAsync(viewFile, results);
                    }
                }

                _logger.LogInformation("Output encoding validation completed. Found {Count} potential issues", results.Count);
                return results;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error during output encoding validation");
                return results;
            }
        }

        public async Task<CookieSecurityAuditResult> CheckCookieSecurityAsync()
        {
            _logger.LogInformation("Checking cookie security configuration");

            var result = new CookieSecurityAuditResult();

            try
            {
                // Check cookie policy configuration
                var cookiePolicy = _configuration.GetSection("CookiePolicy");
                
                result.HasHttpOnlyFlag = cookiePolicy.GetValue<bool>("HttpOnly", false);
                result.HasSecureFlag = cookiePolicy.GetValue<bool>("Secure", false);
                result.SameSitePolicy = cookiePolicy.GetValue<string>("SameSite", "None") ?? "None";
                result.HasProperExpiration = cookiePolicy.GetValue<bool>("HasExpiration", false);

                // Check if using secure defaults
                result.UsesSecureDefaults = result.HasHttpOnlyFlag && 
                                          result.HasSecureFlag && 
                                          result.SameSitePolicy != "None";

                // Generate issues and recommendations
                GenerateCookieSecurityIssues(result);

                // Calculate security score
                result.SecurityScore = CalculateCookieSecurityScore(result);

                _logger.LogInformation("Cookie security check completed. Score: {Score}%", result.SecurityScore);
                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error during cookie security check");
                result.Issues.Add(new CookieSecurityIssue
                {
                    CookieName = "Configuration",
                    Issue = $"Failed to check cookie security: {ex.Message}",
                    RiskLevel = SecurityRiskLevel.Medium,
                    Recommendation = "Review cookie security configuration"
                });
                return result;
            }
        }

        public async Task<SecuritySummaryReport> GenerateSecurityReportAsync()
        {
            _logger.LogInformation("Generating comprehensive security report");

            var report = new SecuritySummaryReport();

            try
            {
                // Get all pages from inventory
                var inventory = await _context.PageInventories
                    .Include(pi => pi.Pages)
                    .OrderByDescending(pi => pi.Version)
                    .FirstOrDefaultAsync();

                if (inventory?.Pages != null)
                {
                    report.TotalPagesAudited = inventory.Pages.Count;
                    var securityScores = new List<int>();

                    foreach (var page in inventory.Pages)
                    {
                        var auditResult = await AuditPageSecurityAsync(page.Name);
                        securityScores.Add(auditResult.SecurityScore);

                        if (auditResult.SecurityScore >= 70)
                            report.SecurePagesCount++;
                        else
                            report.VulnerablePagesCount++;

                        // Count issues by severity
                        foreach (var issue in auditResult.Issues)
                        {
                            switch (issue.RiskLevel)
                            {
                                case SecurityRiskLevel.Critical:
                                    report.CriticalIssuesCount++;
                                    break;
                                case SecurityRiskLevel.High:
                                    report.HighRiskIssuesCount++;
                                    break;
                                case SecurityRiskLevel.Medium:
                                    report.MediumRiskIssuesCount++;
                                    break;
                                case SecurityRiskLevel.Low:
                                    report.LowRiskIssuesCount++;
                                    break;
                            }
                        }
                    }

                    report.AverageSecurityScore = securityScores.Any() ? securityScores.Average() : 0;
                }

                // Generate top vulnerabilities and recommendations
                GenerateTopVulnerabilities(report);
                GenerateRecommendedActions(report);

                _logger.LogInformation("Security report generated. {SecurePages}/{TotalPages} pages are secure", 
                    report.SecurePagesCount, report.TotalPagesAudited);

                return report;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error generating security report");
                return report;
            }
        }

        public async Task<SecurityFixResult> AutoFixSecurityIssuesAsync(string? pageName = null, SecurityFixType fixTypes = SecurityFixType.All)
        {
            _logger.LogInformation("Starting automated security fixes. Page: {PageName}, FixTypes: {FixTypes}", 
                pageName ?? "All", fixTypes);

            var result = new SecurityFixResult();

            try
            {
                // This is a placeholder for automated fixes
                // In a real implementation, this would:
                // 1. Add [ValidateAntiForgeryToken] attributes where missing
                // 2. Add [Authorize] attributes to sensitive actions
                // 3. Fix output encoding issues in views
                // 4. Update cookie configuration

                result.ManualFixesRequired.Add("Add [ValidateAntiForgeryToken] to POST actions");
                result.ManualFixesRequired.Add("Add [Authorize] attributes to admin actions");
                result.ManualFixesRequired.Add("Review and fix output encoding in views");
                result.ManualFixesRequired.Add("Update cookie security configuration");

                result.Success = true;
                result.Message = "Security analysis completed. Manual fixes required.";

                _logger.LogInformation("Automated security fix analysis completed");
                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error during automated security fixes");
                result.Success = false;
                result.Message = $"Error during security fixes: {ex.Message}";
                return result;
            }
        }

        // Helper methods continue in next part... 
       // Helper methods

        private List<Type> GetAllControllerTypes()
        {
            return Assembly.GetExecutingAssembly()
                .GetTypes()
                .Where(type => type.IsSubclassOf(typeof(Controller)) && !type.IsAbstract)
                .ToList();
        }

        private List<MethodInfo> GetControllerActions(Type controllerType)
        {
            return controllerType
                .GetMethods(BindingFlags.Public | BindingFlags.Instance)
                .Where(method => method.IsPublic && 
                               !method.IsSpecialName && 
                               method.DeclaringType == controllerType &&
                               (method.ReturnType == typeof(IActionResult) || 
                                method.ReturnType == typeof(Task<IActionResult>) ||
                                method.ReturnType.IsSubclassOf(typeof(ActionResult))))
                .ToList();
        }

        private bool IsPostAction(MethodInfo action)
        {
            return action.GetCustomAttribute<HttpPostAttribute>() != null ||
                   action.Name.StartsWith("Create", StringComparison.OrdinalIgnoreCase) ||
                   action.Name.StartsWith("Edit", StringComparison.OrdinalIgnoreCase) ||
                   action.Name.StartsWith("Update", StringComparison.OrdinalIgnoreCase) ||
                   action.Name.StartsWith("Delete", StringComparison.OrdinalIgnoreCase) ||
                   action.Name.StartsWith("Register", StringComparison.OrdinalIgnoreCase) ||
                   action.Name.StartsWith("Login", StringComparison.OrdinalIgnoreCase);
        }

        private bool HasValidateAntiForgeryAttribute(MethodInfo action)
        {
            return action.GetCustomAttribute<ValidateAntiForgeryTokenAttribute>() != null;
        }

        private async Task<bool> CheckViewForAntiForgeryTokenAsync(string controllerName, string actionName)
        {
            try
            {
                var viewPath = Path.Combine(_environment.ContentRootPath, "Views", controllerName, $"{actionName}.cshtml");
                if (File.Exists(viewPath))
                {
                    var content = await File.ReadAllTextAsync(viewPath);
                    return content.Contains("@Html.AntiForgeryToken()") || 
                           content.Contains("Html.AntiForgeryToken()") ||
                           content.Contains("antiforgerytoken");
                }
                return false;
            }
            catch
            {
                return false;
            }
        }

        private async Task<bool> CheckAntiForgeryTokensAsync(string controllerName, string actionName)
        {
            // Check if the action has ValidateAntiForgeryToken attribute
            var controllerType = GetAllControllerTypes()
                .FirstOrDefault(t => t.Name.Equals($"{controllerName}Controller", StringComparison.OrdinalIgnoreCase));

            if (controllerType != null)
            {
                var action = GetControllerActions(controllerType)
                    .FirstOrDefault(a => a.Name.Equals(actionName, StringComparison.OrdinalIgnoreCase));

                if (action != null && IsPostAction(action))
                {
                    var hasAttribute = HasValidateAntiForgeryAttribute(action);
                    var hasToken = await CheckViewForAntiForgeryTokenAsync(controllerName, actionName);
                    return hasAttribute && hasToken;
                }
            }

            return true; // Non-POST actions don't need CSRF protection
        }

        private async Task<bool> CheckAuthorizationAsync(string controllerName, string actionName)
        {
            var controllerType = GetAllControllerTypes()
                .FirstOrDefault(t => t.Name.Equals($"{controllerName}Controller", StringComparison.OrdinalIgnoreCase));

            if (controllerType != null)
            {
                var controllerAuthorize = controllerType.GetCustomAttribute<AuthorizeAttribute>();
                
                var action = GetControllerActions(controllerType)
                    .FirstOrDefault(a => a.Name.Equals(actionName, StringComparison.OrdinalIgnoreCase));

                if (action != null)
                {
                    var actionAuthorize = action.GetCustomAttribute<AuthorizeAttribute>();
                    var actionAllowAnonymous = action.GetCustomAttribute<AllowAnonymousAttribute>();

                    // If action has AllowAnonymous, it's intentionally public
                    if (actionAllowAnonymous != null)
                        return true;

                    // Check if it's a public endpoint that doesn't need authorization
                    if (IsPublicEndpoint(controllerName, actionName))
                        return true;

                    // Admin actions should have authorization
                    if (controllerName.Equals("Admin", StringComparison.OrdinalIgnoreCase))
                        return controllerAuthorize != null || actionAuthorize != null;

                    // Other sensitive actions should have authorization
                    if (IsPostAction(action.GetType().GetMethod(actionName) ?? action))
                        return controllerAuthorize != null || actionAuthorize != null;
                }
            }

            return true; // Default to secure for unknown actions
        }

        private bool IsPublicEndpoint(string controllerName, string actionName)
        {
            var publicEndpoints = new[]
            {
                "Home/Index", "Home/Privacy", "Home/Error",
                "Members/Register", "Members/Login",
                "Events/Index", "Events/View"
            };

            return publicEndpoints.Contains($"{controllerName}/{actionName}", StringComparer.OrdinalIgnoreCase);
        }

        private async Task<bool> CheckViewModelsUsageAsync(string controllerName, string actionName)
        {
            var controllerType = GetAllControllerTypes()
                .FirstOrDefault(t => t.Name.Equals($"{controllerName}Controller", StringComparison.OrdinalIgnoreCase));

            if (controllerType != null)
            {
                var action = GetControllerActions(controllerType)
                    .FirstOrDefault(a => a.Name.Equals(actionName, StringComparison.OrdinalIgnoreCase));

                if (action != null && IsPostAction(action))
                {
                    var parameters = action.GetParameters();
                    return parameters.Any(p => IsViewModel(p.ParameterType));
                }
            }

            return true; // Non-POST actions don't need ViewModels
        }

        private bool IsViewModel(Type type)
        {
            return type.Name.EndsWith("ViewModel", StringComparison.OrdinalIgnoreCase) ||
                   type.Name.EndsWith("Model", StringComparison.OrdinalIgnoreCase) ||
                   type.Namespace?.Contains("ViewModels") == true ||
                   type.Namespace?.Contains("DTOs") == true;
        }

        private bool IsComplexType(Type type)
        {
            return !type.IsPrimitive && 
                   type != typeof(string) && 
                   type != typeof(DateTime) && 
                   type != typeof(decimal) && 
                   type != typeof(Guid) &&
                   !type.IsEnum;
        }

        private async Task<bool> CheckServerSideValidationAsync(string controllerName, string actionName)
        {
            var controllerType = GetAllControllerTypes()
                .FirstOrDefault(t => t.Name.Equals($"{controllerName}Controller", StringComparison.OrdinalIgnoreCase));

            if (controllerType != null)
            {
                var action = GetControllerActions(controllerType)
                    .FirstOrDefault(a => a.Name.Equals(actionName, StringComparison.OrdinalIgnoreCase));

                if (action != null && IsPostAction(action))
                {
                    // Check if parameters have validation attributes
                    var parameters = action.GetParameters();
                    foreach (var param in parameters)
                    {
                        if (IsComplexType(param.ParameterType))
                        {
                            var properties = param.ParameterType.GetProperties();
                            var hasValidation = properties.Any(p => 
                                p.GetCustomAttributes<ValidationAttribute>().Any());
                            
                            if (hasValidation)
                                return true;
                        }
                    }
                    return false;
                }
            }

            return true; // Non-POST actions don't need validation
        }

        private async Task<bool> CheckOutputEncodingAsync(string controllerName, string actionName)
        {
            try
            {
                var viewPath = Path.Combine(_environment.ContentRootPath, "Views", controllerName, $"{actionName}.cshtml");
                if (File.Exists(viewPath))
                {
                    var content = await File.ReadAllTextAsync(viewPath);
                    
                    // Check for potentially unsafe output
                    var unsafePatterns = new[]
                    {
                        @"@Html\.Raw\(",
                        @"@\{[^}]*Html\.Raw",
                        @"innerHTML\s*=",
                        @"document\.write\("
                    };

                    foreach (var pattern in unsafePatterns)
                    {
                        if (Regex.IsMatch(content, pattern, RegexOptions.IgnoreCase))
                            return false;
                    }
                }
                return true;
            }
            catch
            {
                return false;
            }
        }



        private bool CheckCSPHeaders()
        {
            // Check if CSP headers are configured
            var securityHeaders = _configuration.GetSection("SecurityHeaders");
            var csp = securityHeaders.GetValue<string>("ContentSecurityPolicy");
            return !string.IsNullOrEmpty(csp);
        }

        private int CalculateSecurityScore(SecurityAuditResult result)
        {
            var score = 0;
            var maxScore = 7;

            if (result.HasAntiForgeryTokens) score++;
            if (result.HasProperAuthorization) score++;
            if (result.UsesViewModels) score++;
            if (result.HasServerSideValidation) score++;
            if (result.HasProperOutputEncoding) score++;
            if (result.HasSecureCookies) score++;
            if (result.HasCSPHeaders) score++;

            return (int)Math.Round((double)score / maxScore * 100);
        }

        private async Task GenerateSecurityIssuesAsync(SecurityAuditResult result)
        {
            if (!result.HasAntiForgeryTokens)
            {
                result.Issues.Add(new SecurityIssue
                {
                    Title = "Missing CSRF Protection",
                    Description = "Form submissions are not protected against Cross-Site Request Forgery attacks",
                    RiskLevel = SecurityRiskLevel.Critical,
                    Location = result.PageName,
                    Impact = "Attackers can perform unauthorized actions on behalf of users",
                    CanAutoFix = true
                });
            }

            if (!result.HasProperAuthorization)
            {
                result.Issues.Add(new SecurityIssue
                {
                    Title = "Missing Authorization",
                    Description = "Action does not have proper authorization controls",
                    RiskLevel = SecurityRiskLevel.High,
                    Location = result.PageName,
                    Impact = "Unauthorized users may access sensitive functionality",
                    CanAutoFix = true
                });
            }

            if (!result.UsesViewModels)
            {
                result.Issues.Add(new SecurityIssue
                {
                    Title = "Direct Model Binding",
                    Description = "Action uses direct entity model binding instead of ViewModels",
                    RiskLevel = SecurityRiskLevel.Medium,
                    Location = result.PageName,
                    Impact = "Potential for over-posting attacks",
                    CanAutoFix = false
                });
            }

            if (!result.HasServerSideValidation)
            {
                result.Issues.Add(new SecurityIssue
                {
                    Title = "Missing Server-Side Validation",
                    Description = "Input validation is not properly implemented",
                    RiskLevel = SecurityRiskLevel.High,
                    Location = result.PageName,
                    Impact = "Invalid or malicious data may be processed",
                    CanAutoFix = false
                });
            }

            if (!result.HasProperOutputEncoding)
            {
                result.Issues.Add(new SecurityIssue
                {
                    Title = "Potential XSS Vulnerability",
                    Description = "Output may not be properly encoded",
                    RiskLevel = SecurityRiskLevel.High,
                    Location = result.PageName,
                    Impact = "Cross-site scripting attacks possible",
                    CanAutoFix = true
                });
            }

            if (!result.HasSecureCookies)
            {
                result.Issues.Add(new SecurityIssue
                {
                    Title = "Insecure Cookie Configuration",
                    Description = "Cookies are not configured with security flags",
                    RiskLevel = SecurityRiskLevel.Medium,
                    Location = "Application Configuration",
                    Impact = "Session hijacking and cookie theft possible",
                    CanAutoFix = true
                });
            }

            if (!result.HasCSPHeaders)
            {
                result.Issues.Add(new SecurityIssue
                {
                    Title = "Missing Content Security Policy",
                    Description = "CSP headers are not configured",
                    RiskLevel = SecurityRiskLevel.Medium,
                    Location = "Application Configuration",
                    Impact = "XSS and code injection attacks not mitigated",
                    CanAutoFix = true
                });
            }
        }

        private void GenerateSecurityRecommendations(SecurityAuditResult result)
        {
            result.Recommendations.Add(new SecurityRecommendation
            {
                Title = "Implement CSRF Protection",
                Description = "Add anti-forgery tokens to all forms and validate them in POST actions",
                Implementation = "Add [ValidateAntiForgeryToken] attribute and @Html.AntiForgeryToken() in views",
                Priority = 1,
                References = new[] { "https://docs.microsoft.com/en-us/aspnet/core/security/anti-request-forgery" }
            });

            result.Recommendations.Add(new SecurityRecommendation
            {
                Title = "Implement Proper Authorization",
                Description = "Add authorization attributes to protect sensitive actions",
                Implementation = "Add [Authorize] attributes with appropriate roles or policies",
                Priority = 1,
                References = new[] { "https://docs.microsoft.com/en-us/aspnet/core/security/authorization/" }
            });

            result.Recommendations.Add(new SecurityRecommendation
            {
                Title = "Use ViewModels for Model Binding",
                Description = "Create specific ViewModels instead of binding directly to entity models",
                Implementation = "Create ViewModel classes with only the properties needed for the view",
                Priority = 2,
                References = new[] { "https://docs.microsoft.com/en-us/aspnet/core/mvc/models/model-binding" }
            });

            result.Recommendations.Add(new SecurityRecommendation
            {
                Title = "Implement Server-Side Validation",
                Description = "Add validation attributes and check ModelState in actions",
                Implementation = "Use DataAnnotations and validate ModelState.IsValid",
                Priority = 1,
                References = new[] { "https://docs.microsoft.com/en-us/aspnet/core/mvc/models/validation" }
            });

            result.Recommendations.Add(new SecurityRecommendation
            {
                Title = "Configure Secure Cookies",
                Description = "Set HttpOnly, Secure, and SameSite flags on cookies",
                Implementation = "Configure cookie policy in Startup.cs",
                Priority = 2,
                References = new[] { "https://docs.microsoft.com/en-us/aspnet/core/security/gdpr" }
            });
        }

        private void DetermineAuthorizationRisk(AuthorizationAuditResult result)
        {
            if (result.ControllerName.Equals("Admin", StringComparison.OrdinalIgnoreCase))
            {
                if (!result.HasAuthorizeAttribute)
                {
                    result.RiskLevel = SecurityRiskLevel.Critical;
                    result.Recommendation = "Admin actions must have [Authorize] attribute with appropriate roles";
                }
                else
                {
                    result.RiskLevel = SecurityRiskLevel.Low;
                    result.Recommendation = "Authorization properly configured";
                }
            }
            else if (result.IsPublicEndpoint)
            {
                result.RiskLevel = SecurityRiskLevel.Low;
                result.Recommendation = "Public endpoint - no authorization required";
            }
            else if (!result.HasAuthorizeAttribute && !result.HasAllowAnonymousAttribute)
            {
                result.RiskLevel = SecurityRiskLevel.Medium;
                result.Recommendation = "Consider adding [Authorize] or [AllowAnonymous] attribute for clarity";
            }
            else
            {
                result.RiskLevel = SecurityRiskLevel.Low;
                result.Recommendation = "Authorization appropriately configured";
            }
        }

        private void DetermineOverPostingRisk(OverPostingAuditResult result, ParameterInfo parameter)
        {
            if (!result.UsesViewModel && !result.UsesBindAttribute)
            {
                result.RiskLevel = SecurityRiskLevel.High;
                result.Vulnerability = "Direct entity model binding without restrictions";
                result.Recommendation = "Use ViewModel or [Bind] attribute to limit bindable properties";
            }
            else if (!result.UsesViewModel)
            {
                result.RiskLevel = SecurityRiskLevel.Medium;
                result.Vulnerability = "Entity model binding with [Bind] attribute";
                result.Recommendation = "Consider using ViewModel for better security and maintainability";
            }
            else
            {
                result.RiskLevel = SecurityRiskLevel.Low;
                result.Vulnerability = "None - using ViewModel";
                result.Recommendation = "Good practice - continue using ViewModels";
            }
        }

        private bool HasCsrfProtection(MethodInfo action)
        {
            // Check for ValidateAntiForgeryToken attribute on action or controller
            return action.GetCustomAttribute<ValidateAntiForgeryTokenAttribute>() != null ||
                   action.DeclaringType?.GetCustomAttribute<ValidateAntiForgeryTokenAttribute>() != null;
        }

        private List<string> GetHttpMethods(MethodInfo action)
        {
            var methods = new List<string>();

            if (action.GetCustomAttribute<HttpGetAttribute>() != null)
                methods.Add("GET");
            if (action.GetCustomAttribute<HttpPostAttribute>() != null)
                methods.Add("POST");
            if (action.GetCustomAttribute<HttpPutAttribute>() != null)
                methods.Add("PUT");
            if (action.GetCustomAttribute<HttpDeleteAttribute>() != null)
                methods.Add("DELETE");
            if (action.GetCustomAttribute<HttpPatchAttribute>() != null)
                methods.Add("PATCH");

            // If no explicit HTTP method attributes, assume GET for most actions
            if (methods.Count == 0)
            {
                // Check action name patterns to infer HTTP method
                var actionName = action.Name.ToLowerInvariant();
                if (actionName.StartsWith("create") || actionName.StartsWith("add") || 
                    actionName.StartsWith("post") || actionName.StartsWith("insert"))
                {
                    methods.Add("POST");
                }
                else if (actionName.StartsWith("edit") || actionName.StartsWith("update") || 
                         actionName.StartsWith("modify"))
                {
                    methods.Add("POST"); // Edit actions are typically POST
                }
                else if (actionName.StartsWith("delete") || actionName.StartsWith("remove"))
                {
                    methods.Add("POST"); // Delete actions are typically POST in MVC
                }
                else
                {
                    methods.Add("GET"); // Default to GET
                }
            }

            return methods;
        }

        private SecurityRiskLevel DetermineCsrfRiskLevel(MethodInfo action)
        {
            var httpMethods = GetHttpMethods(action);
            var hasProtection = HasCsrfProtection(action);

            if (!hasProtection)
            {
                // POST, PUT, DELETE actions without CSRF protection are critical risk
                if (httpMethods.Contains("POST") || httpMethods.Contains("PUT") || httpMethods.Contains("DELETE"))
                {
                    return SecurityRiskLevel.Critical;
                }
                // GET requests don't typically need CSRF protection
                return SecurityRiskLevel.Low;
            }

            return SecurityRiskLevel.Low;
        }

        private async Task ScanViewForOutputEncodingIssuesAsync(string viewPath, List<OutputEncodingAuditResult> results)
        {
            try
            {
                var content = await File.ReadAllTextAsync(viewPath);
                var lines = content.Split('\n');

                for (int i = 0; i < lines.Length; i++)
                {
                    var line = lines[i];
                    
                    // Check for Html.Raw usage
                    if (Regex.IsMatch(line, @"@Html\.Raw\(", RegexOptions.IgnoreCase))
                    {
                        results.Add(new OutputEncodingAuditResult
                        {
                            ViewPath = Path.GetRelativePath(_environment.ContentRootPath, viewPath),
                            LineNumber = i + 1,
                            CodeSnippet = line.Trim(),
                            UsesRawOutput = true,
                            HasProperEncoding = false,
                            RiskLevel = SecurityRiskLevel.High,
                            Vulnerability = "Raw HTML output without encoding",
                            Recommendation = "Ensure content is properly sanitized or use encoded output"
                        });
                    }

                    // Check for JavaScript innerHTML usage
                    if (Regex.IsMatch(line, @"innerHTML\s*=", RegexOptions.IgnoreCase))
                    {
                        results.Add(new OutputEncodingAuditResult
                        {
                            ViewPath = Path.GetRelativePath(_environment.ContentRootPath, viewPath),
                            LineNumber = i + 1,
                            CodeSnippet = line.Trim(),
                            UsesRawOutput = true,
                            HasProperEncoding = false,
                            RiskLevel = SecurityRiskLevel.Medium,
                            Vulnerability = "Direct innerHTML assignment",
                            Recommendation = "Use textContent or properly sanitize HTML content"
                        });
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Error scanning view file {ViewPath}", viewPath);
            }
        }

        private void GenerateCookieSecurityIssues(CookieSecurityAuditResult result)
        {
            if (!result.HasHttpOnlyFlag)
            {
                result.Issues.Add(new CookieSecurityIssue
                {
                    CookieName = "Session Cookies",
                    Issue = "HttpOnly flag not set",
                    RiskLevel = SecurityRiskLevel.Medium,
                    Recommendation = "Set HttpOnly flag to prevent JavaScript access to cookies"
                });
            }

            if (!result.HasSecureFlag)
            {
                result.Issues.Add(new CookieSecurityIssue
                {
                    CookieName = "Session Cookies",
                    Issue = "Secure flag not set",
                    RiskLevel = SecurityRiskLevel.High,
                    Recommendation = "Set Secure flag to ensure cookies are only sent over HTTPS"
                });
            }

            if (result.SameSitePolicy == "None")
            {
                result.Issues.Add(new CookieSecurityIssue
                {
                    CookieName = "Session Cookies",
                    Issue = "SameSite policy not configured",
                    RiskLevel = SecurityRiskLevel.Medium,
                    Recommendation = "Set SameSite policy to 'Strict' or 'Lax' to prevent CSRF attacks"
                });
            }

            // Generate recommendations
            result.Recommendations.Add("Configure cookie policy in Startup.cs with secure defaults");
            result.Recommendations.Add("Set HttpOnly=true, Secure=true, SameSite=Strict for session cookies");
            result.Recommendations.Add("Implement proper cookie expiration policies");
        }

        private int CalculateCookieSecurityScore(CookieSecurityAuditResult result)
        {
            var score = 0;
            var maxScore = 4;

            if (result.HasHttpOnlyFlag) score++;
            if (result.HasSecureFlag) score++;
            if (result.SameSitePolicy != "None") score++;
            if (result.HasProperExpiration) score++;

            return (int)Math.Round((double)score / maxScore * 100);
        }

        private void GenerateTopVulnerabilities(SecuritySummaryReport report)
        {
            report.TopVulnerabilities.AddRange(new[]
            {
                "Missing CSRF protection on forms",
                "Insufficient authorization controls",
                "Potential XSS vulnerabilities",
                "Insecure cookie configuration",
                "Missing server-side validation",
                "Direct model binding vulnerabilities"
            });
        }

        private void GenerateRecommendedActions(SecuritySummaryReport report)
        {
            report.RecommendedActions.AddRange(new[]
            {
                "Implement anti-forgery tokens on all POST forms",
                "Add [Authorize] attributes to admin and sensitive actions",
                "Review and fix output encoding in views",
                "Configure secure cookie policies",
                "Implement comprehensive server-side validation",
                "Use ViewModels instead of direct entity binding",
                "Add Content Security Policy headers",
                "Implement security headers middleware"
            });
        }
    }
}