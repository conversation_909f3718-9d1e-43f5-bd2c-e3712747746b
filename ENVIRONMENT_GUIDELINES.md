# Environment Guidelines for ParaHockey Application

## Core Principle
**ALL ENVIRONMENTS MUST WORK EXACTLY THE SAME** - The application's functionality must be identical across Development, Test/Staging, and Production environments. Any differences must be cosmetic only and controlled through configuration files.

## Approved Environment Differences

The following differences between environments are approved and expected:

### 1. Visual Theme and Indicators
- **Development**: Info theme (blue) with no banner
- **Test/Staging**: Danger theme (red) with "TEST" banner
- **Production**: Primary theme (blue) with no banner
- **Configuration**: Controlled by `Environment.Theme` and `Environment.ShowBanner` in appsettings

### 2. Development Tools
- **Test Buttons**: Auto-fill buttons on registration and login forms
  - Shown in Development and Test/Staging environments
  - Hidden in Production
  - **Configuration**: Controlled by `Environment.ShowDevelopmentTools` setting
  - **Implementation**: Conditionally rendered in views based on `Model.EnvironmentSettings.IsProduction`

### 3. Logging Levels
- **Development/Test**: Information level logging
- **Production**: Warning level logging
- **Configuration**: Set in `Logging.LogLevel.Default` in appsettings

### 4. Error Details
- **Development/Test**: Detailed error messages for debugging
- **Production**: User-friendly error messages without technical details
- **Configuration**: Controlled by `Environment.EnableDetailedErrorLogging` and `Environment.ErrorDetailLevel`

## Prohibited Practices

### ❌ NEVER DO THIS:
1. **Hard-code environment checks in business logic**
   ```csharp
   // BAD - Never do this
   if (Environment.IsProduction())
   {
       // Different business logic for production
   }
   ```

2. **Change functionality based on environment**
   ```csharp
   // BAD - Features must work the same everywhere
   if (!IsProduction)
   {
       EnableSpecialFeature();
   }
   ```

3. **Use different validation rules per environment**
   ```csharp
   // BAD - Validation must be consistent
   var maxLength = IsProduction ? 100 : 500;
   ```

### ✅ DO THIS INSTEAD:
1. **Use configuration for feature flags**
   ```csharp
   // GOOD - Controlled by configuration
   if (_environmentSettings.ShowDevelopmentTools)
   {
       // Show test buttons
   }
   ```

2. **Keep functionality identical**
   ```csharp
   // GOOD - Same behavior everywhere
   ProcessRegistration(model);
   ```

3. **Use consistent validation**
   ```csharp
   // GOOD - Same rules everywhere
   [MaxLength(100)]
   public string Field { get; set; }
   ```

## Configuration Management

### Adding New Settings
When adding new configuration settings:

1. Add to `appsettings.json` (base configuration)
2. Override in `appsettings.Staging.json` if needed
3. Override in `appsettings.Production.json` if needed
4. Document the setting in this file

### Environment Detection
- Environment is determined by `ASPNETCORE_ENVIRONMENT` variable
- Settings are loaded from `EnvironmentSettings` class
- Access in views via `Model.EnvironmentSettings`
- Access in controllers via dependency injection

## Testing Requirements

Before deploying any changes:

1. **Test in Development** - Verify basic functionality
2. **Test in Staging** - Verify with production-like data
3. **Verify consistency** - Ensure behavior is identical (except approved differences)
4. **Check configuration** - Ensure all environments have required settings

## Common Issues and Solutions

### Issue: Form works in Test but not Production
**Solution**: Check for missing configuration settings or hard-coded environment checks

### Issue: Feature visible in Development but not Production
**Solution**: Verify `ShowDevelopmentTools` setting and conditional rendering

### Issue: Different error messages between environments
**Solution**: This is expected - controlled by error detail settings

## Maintenance

This document must be updated when:
- New environment-specific settings are added
- Approved exceptions are modified
- New environments are introduced

Last Updated: [Current Date]
Version: 1.0