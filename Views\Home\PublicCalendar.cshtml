@using Microsoft.AspNetCore.Mvc.Localization
@inject IHtmlLocalizer<ParaHockeyApp.Resources.SharedResourceMarker> SharedLocalizer
@{
    ViewData["Title"] = SharedLocalizer["EventCalendar"];
    var isPublicView = ViewBag.IsPublicView ?? false;
}

<div class="container-fluid mt-4">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col">
            <h1 class="h2 text-primary">
                <i class="fas fa-calendar-alt"></i> @SharedLocalizer["EventCalendar"]
            </h1>
            <p class="text-muted">@SharedLocalizer["ViewPublicEventsAndSchedule"]</p>
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="@Url.Action("Index", "Home")">@SharedLocalizer["Home"]</a></li>
                    <li class="breadcrumb-item active" aria-current="page">@SharedLocalizer["Calendar"]</li>
                </ol>
            </nav>
        </div>
    </div>

    <!-- Navigation -->
    <div class="row mb-4">
        <div class="col">
            <a href="@Url.Action("Index", "Home")" class="btn btn-outline-primary">
                <i class="fas fa-arrow-left"></i> @SharedLocalizer["BackToHome"]
            </a>
            <div class="float-end">
                <a href="@Url.Action("Login", "Members")" class="btn btn-success me-2">
                    <i class="fas fa-sign-in-alt"></i> @SharedLocalizer["Login"]
                </a>
                <a href="@Url.Action("Register", "Members")" class="btn btn-primary">
                    <i class="fas fa-user-plus"></i> @SharedLocalizer["Register"]
                </a>
            </div>
        </div>
    </div>

    <div class="row mb-4">
        <!-- Calendar Controls -->
        <div class="col-md-8">
            <div class="card">
                <!-- Desktop Header -->
                <div class="card-header d-flex justify-content-between align-items-center calendar-desktop-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-calendar"></i> @SharedLocalizer["EventCalendar"]
                    </h5>
                    <div>
                        <!-- Read-only filter only -->
                        <div class="dropdown d-inline-block">
                            <button class="btn btn-outline-secondary btn-sm dropdown-toggle" type="button" id="filterDropdown" data-bs-toggle="dropdown">
                                <i class="fas fa-filter"></i> @SharedLocalizer["Filter"]
                            </button>
                            <ul class="dropdown-menu" aria-labelledby="filterDropdown">
                                <li><a class="dropdown-item" href="#" data-filter="all">@SharedLocalizer["AllEvents"]</a></li>
                                @if (ViewBag.EventCategories != null)
                                {
                                    @foreach (var category in ViewBag.EventCategories)
                                    {
                                        <li><a class="dropdown-item" href="#" data-filter="@category.Id">@SharedLocalizer[category.DisplayNameKey]</a></li>
                                    }
                                }
                            </ul>
                        </div>
                    </div>
                </div>

                <!-- Mobile Header -->
                <div class="card-header calendar-mobile-header">
                    <div class="d-flex justify-content-between align-items-center mb-2">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-calendar"></i> @SharedLocalizer["EventCalendar"]
                        </h5>
                    </div>
                    <div class="d-flex gap-2 flex-wrap">
                        <!-- Mobile Filter -->
                        <div class="dropdown">
                            <button class="btn btn-outline-secondary btn-sm dropdown-toggle" type="button" data-bs-toggle="dropdown">
                                <i class="fas fa-filter"></i> @SharedLocalizer["Filter"]
                            </button>
                            <ul class="dropdown-menu">
                                <li><a class="dropdown-item" href="#" data-filter="all">@SharedLocalizer["AllEvents"]</a></li>
                                @if (ViewBag.EventCategories != null)
                                {
                                    @foreach (var category in ViewBag.EventCategories)
                                    {
                                        <li><a class="dropdown-item" href="#" data-filter="@category.Id">@SharedLocalizer[category.DisplayNameKey]</a></li>
                                    }
                                }
                            </ul>
                        </div>
                    </div>
                </div>

                <div class="card-body p-0">
                    <!-- Calendar Container -->
                    <div id="calendar"></div>
                </div>
            </div>
        </div>

        <!-- Sidebar -->
        <div class="col-md-4">
            <!-- Event Statistics Card -->
            <div class="card mb-4">
                <div class="card-header bg-info text-white">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-chart-bar"></i> @SharedLocalizer["EventStatistics"]
                    </h5>
                </div>
                <div class="card-body">
                    <div class="text-center">
                        <div class="mb-3">
                            <h4 class="text-primary">@ViewBag.PublishedEvents</h4>
                            <p class="text-muted mb-0">@SharedLocalizer["PublishedEvents"]</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Upcoming Events Card -->
            <div class="card mb-4">
                <div class="card-header bg-warning text-dark">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-clock"></i> @SharedLocalizer["UpcomingEvents"]
                    </h5>
                </div>
                <div class="card-body">
                    @if (ViewBag.UpcomingEvents != null && ((List<ParaHockeyApp.Models.Entities.Event>)ViewBag.UpcomingEvents).Any())
                    {
                        @foreach (var evt in (List<ParaHockeyApp.Models.Entities.Event>)ViewBag.UpcomingEvents)
                        {
                            <div class="mb-3 pb-3 border-bottom upcoming-event-item" data-event-id="@evt.Id" role="button" tabindex="0">
                                <h6 class="mb-1">@evt.Title</h6>
                                <small class="text-muted">
                                    <i class="fas fa-calendar-day"></i> @evt.StartDate.ToString("MMM dd, yyyy")
                                    @if (!evt.IsAllDay)
                                    {
                                        <br><i class="fas fa-clock"></i> @evt.StartDate.ToString("HH:mm")
                                    }
                                    @if (!string.IsNullOrEmpty(evt.Location))
                                    {
                                        <br><i class="fas fa-map-marker-alt"></i> @evt.Location
                                    }
                                </small>
                            </div>
                        }
                    }
                    else
                    {
                        var upcomingEmptyState = ViewBag.UpcomingEventsEmptyState as ParaHockeyApp.Models.ViewModels.EmptyStateViewModel;
                        @if (upcomingEmptyState != null)
                        {
                            <div class="text-center py-3">
                                <i class="@upcomingEmptyState.IconClass fa-2x mb-3"></i>
                                <h6 class="text-muted">@upcomingEmptyState.Title</h6>
                                <p class="text-muted small">@upcomingEmptyState.Message</p>
                            </div>
                        }
                        else
                        {
                            <p class="text-muted text-center">@SharedLocalizer["NoUpcomingEvents"]</p>
                        }
                    }
                </div>
            </div>

            <!-- Recent Events Card -->
            <div class="card">
                <div class="card-header bg-secondary text-white">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-history"></i> @SharedLocalizer["RecentEvents"]
                    </h5>
                </div>
                <div class="card-body">
                    @if (ViewBag.RecentEvents != null && ((List<ParaHockeyApp.Models.Entities.Event>)ViewBag.RecentEvents).Any())
                    {
                        @foreach (var evt in (List<ParaHockeyApp.Models.Entities.Event>)ViewBag.RecentEvents)
                        {
                            <div class="mb-3 pb-3 border-bottom recent-event-item" data-event-id="@evt.Id" role="button" tabindex="0">
                                <h6 class="mb-1">@evt.Title</h6>
                                <small class="text-muted">
                                    <i class="fas fa-calendar-day"></i> @evt.StartDate.ToString("MMM dd, yyyy")
                                    @if (!evt.IsAllDay)
                                    {
                                        <br><i class="fas fa-clock"></i> @evt.StartDate.ToString("HH:mm")
                                    }
                                    @if (!string.IsNullOrEmpty(evt.Location))
                                    {
                                        <br><i class="fas fa-map-marker-alt"></i> @evt.Location
                                    }
                                </small>
                            </div>
                        }
                    }
                    else
                    {
                        var recentEmptyState = ViewBag.RecentEventsEmptyState as ParaHockeyApp.Models.ViewModels.EmptyStateViewModel;
                        @if (recentEmptyState != null)
                        {
                            <div class="text-center py-3">
                                <i class="@recentEmptyState.IconClass fa-2x mb-3"></i>
                                <h6 class="text-muted">@recentEmptyState.Title</h6>
                                <p class="text-muted small">@recentEmptyState.Message</p>
                            </div>
                        }
                        else
                        {
                            <p class="text-muted text-center">@SharedLocalizer["NoRecentEvents"]</p>
                        }
                    }
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Login Required Modal -->
<div class="modal fade" id="loginRequiredModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header bg-warning text-dark">
                <h5 class="modal-title">
                    <i class="fas fa-lock"></i> @SharedLocalizer["LoginRequired"]
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="text-center py-4">
                    <i class="fas fa-user-lock fa-4x text-warning mb-3"></i>
                    <h4>@SharedLocalizer["MembershipRequired"]</h4>
                    <p class="mb-4">@SharedLocalizer["LoginRequiredMessage"]</p>
                    <div class="d-flex justify-content-center gap-3">
                        <a href="@Url.Action("Login", "Members")" class="btn btn-success btn-lg">
                            <i class="fas fa-sign-in-alt"></i> @SharedLocalizer["Login"]
                        </a>
                        <a href="@Url.Action("Register", "Members")" class="btn btn-primary btn-lg">
                            <i class="fas fa-user-plus"></i> @SharedLocalizer["Register"]
                        </a>
                    </div>
                    <div class="mt-4">
                        <h6 class="text-primary">@SharedLocalizer["MemberBenefitsTitle"]</h6>
                        <ul class="text-start text-muted">
                            <li>@SharedLocalizer["MemberBenefitEventRegistration"]</li>
                            <li>@SharedLocalizer["MemberBenefitPriorityAccess"]</li>
                            <li>@SharedLocalizer["MemberBenefitUpdates"]</li>
                            <li>@SharedLocalizer["MemberBenefitCommunity"]</li>
                        </ul>
                        <small class="text-muted">@SharedLocalizer["MembershipIsFree"]</small>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">@SharedLocalizer["Close"]</button>
            </div>
        </div>
    </div>
</div>

<!-- Include shared Event Details Modal -->
@await Html.PartialAsync("_EventDetailsModal")

@section Scripts {
    <!-- FullCalendar CSS and JS -->
    <link href="https://cdn.jsdelivr.net/npm/fullcalendar@6.1.8/index.global.min.css" rel="stylesheet" />
    <script src="https://cdn.jsdelivr.net/npm/fullcalendar@6.1.8/index.global.min.js"></script>
    
    <!-- Event Details Modal JS -->
    <script src="~/js/event-details-modal.js"></script>

    <script>
        // Authentication flag
        const isAuthenticated = @Json.Serialize(User.Identity.IsAuthenticated);
        
        document.addEventListener('DOMContentLoaded', function() {
            var calendarEl = document.getElementById('calendar');
            var calendar = new FullCalendar.Calendar(calendarEl, {
                initialView: 'dayGridMonth',
                headerToolbar: {
                    left: 'prev,next today',
                    center: 'title',
                    right: 'dayGridMonth,timeGridWeek,timeGridDay'
                },
                events: {
                    url: '@Url.Action("GetPublicCalendarEvents", "Home")',
                    method: 'GET',
                    failure: function() {
                        alert('@SharedLocalizer["ErrorLoadingEvents"]');
                    }
                },
                eventClick: function(info) {
                    // Prevent navigation
                    info.jsEvent.preventDefault();
                    
                    // Show event details modal using shared function with public calendar config
                    showEventDetailsModal(info.event.id, isAuthenticated, {
                        calendarType: 'public'
                    });
                },
                height: 'auto',
                contentHeight: 500,
                aspectRatio: 1.35,
                eventDisplay: 'block',
                displayEventTime: true,
                eventTimeFormat: {
                    hour: '2-digit',
                    minute: '2-digit',
                    meridiem: false
                },
                locale: '@(System.Globalization.CultureInfo.CurrentCulture.TwoLetterISOLanguageName)'
            });

            calendar.render();

            // Filter functionality
            document.querySelectorAll('[data-filter]').forEach(function(filterBtn) {
                filterBtn.addEventListener('click', function(e) {
                    e.preventDefault();
                    var filter = this.dataset.filter;
                    
                    // Remove existing filter sources
                    calendar.getEventSources().forEach(function(eventSource) {
                        eventSource.remove();
                    });
                    
                    // Add new event source with filter
                    var url = '@Url.Action("GetPublicCalendarEvents", "Home")';
                    if (filter !== 'all') {
                        url += '?categoryId=' + filter;
                    }
                    
                    calendar.addEventSource({
                        url: url,
                        method: 'GET',
                        failure: function() {
                            alert('@SharedLocalizer["ErrorLoadingEvents"]');
                        }
                    });
                });
            });

            // Login required function for public access
            window.showLoginRequiredModal = function() {
                var modal = new bootstrap.Modal(document.getElementById('loginRequiredModal'));
                modal.show();
            };
            
            // Add click handlers for upcoming and recent events
            document.querySelectorAll('.upcoming-event-item, .recent-event-item').forEach(function(item) {
                item.addEventListener('click', function() {
                    var eventId = this.dataset.eventId;
                    if (eventId) {
                        showEventDetailsModal(eventId, isAuthenticated, {
                            calendarType: 'public'
                        });
                    }
                });
                
                // Handle keyboard navigation
                item.addEventListener('keypress', function(e) {
                    if (e.key === 'Enter' || e.key === ' ') {
                        e.preventDefault();
                        var eventId = this.dataset.eventId;
                        if (eventId) {
                            showEventDetailsModal(eventId, isAuthenticated, {
                                calendarType: 'public'
                            });
                        }
                    }
                });
            });
        });
    </script>
}

<!-- Custom Styles -->
<style>
    .calendar-desktop-header {
        display: block;
    }
    
    .calendar-mobile-header {
        display: none;
    }
    
    @@media (max-width: 768px) {
        .calendar-desktop-header {
            display: none;
        }
        
        .calendar-mobile-header {
            display: block;
        }
    }
    
    #calendar {
        min-height: 500px;
    }
    
    .fc-event {
        cursor: pointer;
    }
    
    .fc-event:hover {
        opacity: 0.8;
    }
    
    .card-title {
        font-weight: 600;
    }
    
    /* Clickable event items */
    .upcoming-event-item,
    .recent-event-item {
        cursor: pointer;
        transition: background-color 0.2s ease;
    }
    
    .upcoming-event-item:hover,
    .recent-event-item:hover {
        background-color: #f8f9fa;
    }
    
    .upcoming-event-item:focus,
    .recent-event-item:focus {
        outline: 2px solid #007bff;
        outline-offset: -2px;
    }
    
    /* Event Details Modal Responsiveness */
    #eventDetailsModal .modal-body {
        max-height: 90vh;
        overflow-y: auto;
    }
    
    @@media (max-width: 768px) {
        #eventDetailsModal .modal-dialog {
            margin: 0.5rem;
        }
        
        #eventDetailsModal .modal-body {
            max-height: 80vh;
            padding: 1rem;
        }
        
        #eventDetailsModal .row {
            margin: 0;
        }
        
        #eventDetailsModal .col-md-6 {
            padding: 0;
            margin-bottom: 1rem;
        }
    }
    
    /* Modal content styling */
    #eventDetailsModal h3 {
        color: #007bff;
        word-wrap: break-word;
    }
    
    #eventDetailsModal .alert {
        margin-bottom: 1rem;
    }
    
    #eventDetailsModal p {
        margin-bottom: 0.5rem;
    }
</style>