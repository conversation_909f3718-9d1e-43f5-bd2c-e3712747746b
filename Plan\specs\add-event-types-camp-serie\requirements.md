# Add Camp & Série Event Categories and Fix Category Mapping

## Functional Requirements

| ID | Description |
|----|-------------|
| FR-1 | System shall support new event categories Camp and Série available for selection during event creation and import |
| FR-2 | System shall localize category names using resource keys (`EventCategory_Camp`, `EventCategory_Serie`, etc.) |
| FR-3 | All calendar views (Admin, Public, Member, Registration) shall display events using the localized category name and specified color/icon |
| FR-4 | When an event is created/imported whose title or status contains “Tentatif”, the system shall automatically assign the Tentatif category |
| FR-5 | When an event is created/imported whose title contains “Défi Sportif” the system shall automatically assign the Tournoi category |
| FR-6 | A background data-migration shall update existing events so that Tentatif and Défi Sportif entries comply with FR-4 and FR-5 |

## Non-Functional Requirements

| ID | Description |
|----|-------------|
| NFR-1 | Solution must not degrade event retrieval performance by more than 5 % |
| NFR-2 | Must follow existing localization, seeding and MVC architecture patterns |
| NFR-3 | All text is localized; no hard-coded strings visible to end users |
| NFR-4 | Code changes must include automated E2E tests and unit tests where feasible |

## Acceptance Criteria

1. AC-1 ✅ New rows for Camp (Id=9) and Série (Id=10) exist in EventCategories table with DisplayNameKey & DescriptionKey populated.  
2. AC-2 ✅ Resource files contain both English and French translations for new keys.  
3. AC-3 ✅ Event creation UI lists Camp and Série options in both languages.  
4. AC-4 ✅ Calendars label and colour Camp & Série events correctly.  
5. AC-5 ✅ Creating or importing an event titled “Match Tentatif” auto-assigns Tentatif category.  
6. AC-6 ✅ Creating or importing an event titled “Défi Sportif” auto-assigns Tournoi category.  
7. AC-7 ✅ Existing Tentatif / Défi Sportif events appear in correct categories after migration.  

## Key User Stories

* US-1: “As an admin I want to mark a training Camp so that members can differentiate it from a regular practice.”  
* US-2: “As a visitor I want to see Série events highlighted so I can follow an entire series.”  
* US-3: “As a schedule coordinator I don’t want to manually correct Tentatif events after import.” 