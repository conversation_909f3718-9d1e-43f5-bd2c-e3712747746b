using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace ParaHockeyApp.Models.Entities
{
    [Table("Provinces")]
    public class Province
    {
        [Key]
        public int Id { get; set; }

        [Required]
        [StringLength(2)]
        public string Code { get; set; } = string.Empty;

        [Required]
        [StringLength(100)]
        public string DisplayNameKey { get; set; } = string.Empty;
    }
}