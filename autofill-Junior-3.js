﻿// Auto-fill registration form for Marie3 Beaulieu3 (Junior)
document.getElementById('FirstName').value = 'Marie3';
document.getElementById('LastName').value = 'Beaulieu3';
document.getElementById('Email').value = '<EMAIL>';
document.getElementById('Phone').value = '(*************';
document.getElementById('DateOfBirth').value = '2013-07-04';
document.getElementById('Address').value = '321 rue de la Paix';
document.getElementById('City').value = 'Saint-Jean-sur-Richelieu';
document.getElementById('PostalCode').value = 'H6S 8S2';
document.getElementById('RegistrationTypeId').value = '1';
document.getElementById('GenderId').value = '2';
document.getElementById('ProvinceId').value = '1';
document.getElementById('PhoneTypeId').value = '1';
// Parent information for Junior
document.getElementById('Parents_0__FirstName').value = 'Michel_Parent3';
document.getElementById('Parents_0__LastName').value = 'Beaulieu3';
document.getElementById('Parents_0__Email').value = '<EMAIL>';
document.getElementById('Parents_0__Phone').value = '(*************';
