namespace ParaHockeyApp.Services
{
    /// <summary>
    /// Service for handling concurrency and preventing conflicts during import processing
    /// </summary>
    public interface IConcurrencyService
    {
        /// <summary>
        /// Attempts to acquire a lock on a temp member for editing
        /// </summary>
        /// <param name="tempMemberId">The temp member ID to lock</param>
        /// <param name="userId">The user attempting to acquire the lock</param>
        /// <param name="lockDurationMinutes">How long to hold the lock (default 30 minutes)</param>
        /// <returns>Lock acquisition result</returns>
        Task<LockResult> TryAcquireLockAsync(Guid tempMemberId, string userId, int lockDurationMinutes = 30);

        /// <summary>
        /// Releases a lock on a temp member
        /// </summary>
        /// <param name="tempMemberId">The temp member ID to unlock</param>
        /// <param name="userId">The user releasing the lock</param>
        /// <returns>True if lock was successfully released</returns>
        Task<bool> ReleaseLockAsync(Guid tempMemberId, string userId);

        /// <summary>
        /// Extends an existing lock for additional time
        /// </summary>
        /// <param name="tempMemberId">The temp member ID</param>
        /// <param name="userId">The user extending the lock</param>
        /// <param name="additionalMinutes">Additional minutes to extend</param>
        /// <returns>True if lock was successfully extended</returns>
        Task<bool> ExtendLockAsync(Guid tempMemberId, string userId, int additionalMinutes = 30);

        /// <summary>
        /// Gets information about who has a lock on a temp member
        /// </summary>
        /// <param name="tempMemberId">The temp member ID</param>
        /// <returns>Lock information if locked, null if available</returns>
        Task<LockInfo?> GetLockInfoAsync(Guid tempMemberId);

        /// <summary>
        /// Cleans up expired locks
        /// </summary>
        /// <returns>Number of expired locks cleaned up</returns>
        Task<int> CleanupExpiredLocksAsync();

        /// <summary>
        /// Attempts to acquire locks on multiple temp members (for bulk operations)
        /// </summary>
        /// <param name="tempMemberIds">List of temp member IDs to lock</param>
        /// <param name="userId">The user attempting to acquire locks</param>
        /// <param name="lockDurationMinutes">How long to hold the locks</param>
        /// <returns>Bulk lock acquisition result</returns>
        Task<BulkLockResult> TryAcquireBulkLocksAsync(List<Guid> tempMemberIds, string userId, int lockDurationMinutes = 30);

        /// <summary>
        /// Releases multiple locks at once
        /// </summary>
        /// <param name="tempMemberIds">List of temp member IDs to unlock</param>
        /// <param name="userId">The user releasing the locks</param>
        /// <returns>Number of locks successfully released</returns>
        Task<int> ReleaseBulkLocksAsync(List<Guid> tempMemberIds, string userId);
    }

    /// <summary>
    /// Result of attempting to acquire a lock
    /// </summary>
    public class LockResult
    {
        public bool Success { get; set; }
        public string? FailureReason { get; set; }
        public LockInfo? ExistingLock { get; set; }
        public DateTime? LockExpiration { get; set; }
    }

    /// <summary>
    /// Information about an active lock
    /// </summary>
    public class LockInfo
    {
        public Guid TempMemberId { get; set; }
        public string UserId { get; set; } = string.Empty;
        public string UserDisplayName { get; set; } = string.Empty;
        public DateTime AcquiredAt { get; set; }
        public DateTime ExpiresAt { get; set; }
        public bool IsExpired => DateTime.UtcNow > ExpiresAt;
        public TimeSpan TimeRemaining => IsExpired ? TimeSpan.Zero : ExpiresAt - DateTime.UtcNow;
    }

    /// <summary>
    /// Result of attempting to acquire bulk locks
    /// </summary>
    public class BulkLockResult
    {
        public bool AllSuccessful { get; set; }
        public List<Guid> SuccessfulLocks { get; set; } = new();
        public List<Guid> FailedLocks { get; set; } = new();
        public Dictionary<Guid, LockInfo> ConflictingLocks { get; set; } = new();
        public string? ErrorMessage { get; set; }
    }
}