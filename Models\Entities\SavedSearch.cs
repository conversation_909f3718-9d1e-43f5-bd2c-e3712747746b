using System;
using System.ComponentModel.DataAnnotations;

namespace ParaHockeyApp.Models.Entities
{
    /// <summary>
    /// Entity for storing saved member search configurations
    /// Allows users to save and reuse frequently used search criteria
    /// </summary>
    public class SavedSearch : BaseEntity
    {
        /// <summary>
        /// User-friendly name for the saved search
        /// </summary>
        [Required]
        [StringLength(100)]
        public string Name { get; set; } = string.Empty;

        /// <summary>
        /// ID of the user who created this saved search
        /// </summary>
        [Required]
        [StringLength(450)]
        public string UserId { get; set; } = string.Empty;

        /// <summary>
        /// JSON serialized search criteria (MemberSearchRequest)
        /// </summary>
        [Required]
        public string SearchCriteriaJson { get; set; } = string.Empty;

        /// <summary>
        /// Last time this saved search was used
        /// </summary>
        public DateTime LastUsed { get; set; }

        /// <summary>
        /// Optional description of what this search is for
        /// </summary>
        [StringLength(500)]
        public string? Description { get; set; }

        /// <summary>
        /// Number of times this search has been used
        /// </summary>
        public int UsageCount { get; set; }
    }
}