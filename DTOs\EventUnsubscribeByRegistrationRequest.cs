using System.ComponentModel.DataAnnotations;

namespace ParaHockeyApp.DTOs
{
    /// <summary>
    /// Request model for unsubscribing from an event using registration ID
    /// </summary>
    public class EventUnsubscribeByRegistrationRequest
    {
        /// <summary>
        /// Registration ID to cancel
        /// </summary>
        [Required]
        public int RegistrationId { get; set; }
    }
}