/**
 * Image Optimization - Handles responsive images, lazy loading, and WebP support
 */
class ImageOptimizer {
    constructor() {
        this.supportsWebP = null;
        this.supportsAvif = null;
        this.intersectionObserver = null;
        this.lazyImages = new Set();
        
        this.initializeFormatSupport();
        this.initializeLazyLoading();
    }

    /**
     * Initialize format support detection
     */
    async initializeFormatSupport() {
        this.supportsWebP = await this.checkFormatSupport('webp');
        this.supportsAvif = await this.checkFormatSupport('avif');
        
        console.log('Image format support:', {
            webp: this.supportsWebP,
            avif: this.supportsAvif
        });
    }

    /**
     * Check if browser supports a specific image format
     */
    checkFormatSupport(format) {
        return new Promise((resolve) => {
            const canvas = document.createElement('canvas');
            canvas.width = 1;
            canvas.height = 1;
            
            const ctx = canvas.getContext('2d');
            ctx.fillStyle = 'rgba(0,0,0,0.5)';
            ctx.fillRect(0, 0, 1, 1);
            
            const dataURL = canvas.toDataURL(`image/${format}`);
            const img = new Image();
            
            img.onload = () => resolve(true);
            img.onerror = () => resolve(false);
            img.src = dataURL;
        });
    }

    /**
     * Initialize lazy loading with Intersection Observer
     */
    initializeLazyLoading() {
        if ('IntersectionObserver' in window) {
            this.intersectionObserver = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        this.loadImage(entry.target);
                        this.intersectionObserver.unobserve(entry.target);
                        this.lazyImages.delete(entry.target);
                    }
                });
            }, {
                rootMargin: '50px 0px',
                threshold: 0.01
            });

            // Observe existing lazy images
            this.observeLazyImages();
        } else {
            // Fallback for browsers without Intersection Observer
            this.loadAllImages();
        }
    }

    /**
     * Observe all images marked for lazy loading
     */
    observeLazyImages() {
        const lazyImages = document.querySelectorAll('img[data-src], img[loading="lazy"]');
        lazyImages.forEach(img => {
            this.lazyImages.add(img);
            this.intersectionObserver.observe(img);
        });
    }

    /**
     * Load a specific image with optimization
     */
    loadImage(img) {
        const dataSrc = img.getAttribute('data-src');
        const dataSrcset = img.getAttribute('data-srcset');
        
        if (dataSrc) {
            // Apply format optimization
            const optimizedSrc = this.getOptimizedImageSrc(dataSrc);
            img.src = optimizedSrc;
            img.removeAttribute('data-src');
        }
        
        if (dataSrcset) {
            const optimizedSrcset = this.getOptimizedImageSrcset(dataSrcset);
            img.srcset = optimizedSrcset;
            img.removeAttribute('data-srcset');
        }

        // Add loading class for fade-in effect
        img.classList.add('loading');
        
        img.onload = () => {
            img.classList.remove('loading');
            img.classList.add('loaded');
        };

        img.onerror = () => {
            img.classList.remove('loading');
            img.classList.add('error');
            console.warn('Failed to load image:', img.src);
        };
    }

    /**
     * Get optimized image source based on format support
     */
    getOptimizedImageSrc(originalSrc) {
        if (this.supportsAvif && this.hasAvifVariant(originalSrc)) {
            return this.convertToFormat(originalSrc, 'avif');
        } else if (this.supportsWebP && this.hasWebPVariant(originalSrc)) {
            return this.convertToFormat(originalSrc, 'webp');
        }
        return originalSrc;
    }

    /**
     * Get optimized srcset with format support
     */
    getOptimizedImageSrcset(originalSrcset) {
        const sources = originalSrcset.split(',').map(source => source.trim());
        
        return sources.map(source => {
            const [url, descriptor] = source.split(' ');
            const optimizedUrl = this.getOptimizedImageSrc(url);
            return descriptor ? `${optimizedUrl} ${descriptor}` : optimizedUrl;
        }).join(', ');
    }

    /**
     * Check if AVIF variant exists
     */
    hasAvifVariant(src) {
        // In a real implementation, this would check if the AVIF version exists
        // For now, assume it exists for demonstration
        return src.includes('/optimized/') || src.includes('hero') || src.includes('logo');
    }

    /**
     * Check if WebP variant exists
     */
    hasWebPVariant(src) {
        // In a real implementation, this would check if the WebP version exists
        // For now, assume it exists for most images
        return !src.endsWith('.svg');
    }

    /**
     * Convert image path to specific format
     */
    convertToFormat(src, format) {
        const extension = `.${format}`;
        const lastDotIndex = src.lastIndexOf('.');
        
        if (lastDotIndex === -1) return src;
        
        const basePath = src.substring(0, lastDotIndex);
        return `${basePath}${extension}`;
    }

    /**
     * Generate responsive image HTML
     */
    generateResponsiveImage(options) {
        const {
            src,
            alt,
            sizes = '100vw',
            widths = [576, 768, 992, 1200],
            className = '',
            loading = 'lazy'
        } = options;

        // Generate srcset
        const srcset = widths.map(width => {
            const optimizedSrc = this.getResponsiveImageSrc(src, width);
            return `${optimizedSrc} ${width}w`;
        }).join(', ');

        // Create picture element with format fallbacks
        const picture = document.createElement('picture');

        // Add AVIF source if supported
        if (this.supportsAvif) {
            const avifSource = document.createElement('source');
            avifSource.srcset = this.convertSrcsetToFormat(srcset, 'avif');
            avifSource.type = 'image/avif';
            avifSource.sizes = sizes;
            picture.appendChild(avifSource);
        }

        // Add WebP source if supported
        if (this.supportsWebP) {
            const webpSource = document.createElement('source');
            webpSource.srcset = this.convertSrcsetToFormat(srcset, 'webp');
            webpSource.type = 'image/webp';
            webpSource.sizes = sizes;
            picture.appendChild(webpSource);
        }

        // Add fallback img element
        const img = document.createElement('img');
        img.src = src;
        img.srcset = srcset;
        img.sizes = sizes;
        img.alt = alt;
        img.className = className;
        img.loading = loading;

        picture.appendChild(img);

        return picture;
    }

    /**
     * Get responsive image source for specific width
     */
    getResponsiveImageSrc(src, width) {
        const lastDotIndex = src.lastIndexOf('.');
        const extension = src.substring(lastDotIndex);
        const basePath = src.substring(0, lastDotIndex);
        
        return `${basePath}-${width}w${extension}`;
    }

    /**
     * Convert entire srcset to specific format
     */
    convertSrcsetToFormat(srcset, format) {
        return srcset.split(',').map(source => {
            const [url, descriptor] = source.trim().split(' ');
            const convertedUrl = this.convertToFormat(url, format);
            return descriptor ? `${convertedUrl} ${descriptor}` : convertedUrl;
        }).join(', ');
    }

    /**
     * Load all images immediately (fallback)
     */
    loadAllImages() {
        const lazyImages = document.querySelectorAll('img[data-src], img[loading="lazy"]');
        lazyImages.forEach(img => this.loadImage(img));
    }

    /**
     * Add new images to lazy loading observer
     */
    observeNewImages(container = document) {
        const newLazyImages = container.querySelectorAll('img[data-src]:not(.observed), img[loading="lazy"]:not(.observed)');
        
        newLazyImages.forEach(img => {
            img.classList.add('observed');
            this.lazyImages.add(img);
            
            if (this.intersectionObserver) {
                this.intersectionObserver.observe(img);
            } else {
                this.loadImage(img);
            }
        });
    }

    /**
     * Preload critical images
     */
    preloadCriticalImages() {
        const criticalImages = document.querySelectorAll('img[data-critical="true"]');
        
        criticalImages.forEach(img => {
            const src = img.getAttribute('data-src') || img.src;
            if (src) {
                const link = document.createElement('link');
                link.rel = 'preload';
                link.as = 'image';
                link.href = this.getOptimizedImageSrc(src);
                document.head.appendChild(link);
            }
        });
    }

    /**
     * Get image loading performance metrics
     */
    getImageMetrics() {
        const imageEntries = performance.getEntriesByType('resource')
            .filter(entry => entry.initiatorType === 'img');

        return {
            totalImages: imageEntries.length,
            totalLoadTime: imageEntries.reduce((sum, entry) => sum + entry.duration, 0),
            averageLoadTime: imageEntries.length > 0 
                ? imageEntries.reduce((sum, entry) => sum + entry.duration, 0) / imageEntries.length 
                : 0,
            largestImage: imageEntries.reduce((largest, entry) => 
                entry.transferSize > (largest?.transferSize || 0) ? entry : largest, null),
            images: imageEntries.map(entry => ({
                name: entry.name,
                loadTime: entry.duration,
                size: entry.transferSize
            }))
        };
    }
}

// Initialize image optimizer
const imageOptimizer = new ImageOptimizer();

// Auto-observe images when DOM changes
if ('MutationObserver' in window) {
    const mutationObserver = new MutationObserver((mutations) => {
        mutations.forEach(mutation => {
            if (mutation.type === 'childList') {
                mutation.addedNodes.forEach(node => {
                    if (node.nodeType === Node.ELEMENT_NODE) {
                        imageOptimizer.observeNewImages(node);
                    }
                });
            }
        });
    });

    mutationObserver.observe(document.body, {
        childList: true,
        subtree: true
    });
}

// Preload critical images when page loads
document.addEventListener('DOMContentLoaded', () => {
    imageOptimizer.preloadCriticalImages();
});

// Global access
window.imageOptimizer = imageOptimizer;

// Export for module systems
if (typeof module !== 'undefined' && module.exports) {
    module.exports = ImageOptimizer;
}