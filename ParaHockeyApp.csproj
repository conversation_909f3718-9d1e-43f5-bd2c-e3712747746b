﻿<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
    <GenerateAssemblyInfo>false</GenerateAssemblyInfo>
    <GenerateTargetFrameworkAttribute>false</GenerateTargetFrameworkAttribute>
    <UserSecretsId>543bc7c7-1f29-43ae-9c64-6da10354e8f8</UserSecretsId>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="AutoMapper.Extensions.Microsoft.DependencyInjection" Version="12.0.1" />
    <PackageReference Include="ClosedXML" Version="0.105.0" />
    <PackageReference Include="CsvHelper" Version="33.1.0" />
    <PackageReference Include="EPPlus" Version="6.2.10" />
    <PackageReference Include="HtmlAgilityPack" Version="1.12.2" />
    <PackageReference Include="Microsoft.AspNetCore.Identity.EntityFrameworkCore" Version="8.0.0" />
    <PackageReference Include="Microsoft.AspNetCore.Localization" Version="2.2.0" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.SqlServer" Version="8.0.0" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Tools" Version="8.0.0">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
    <PackageReference Include="Microsoft.Identity.Web" Version="2.15.2" />
    <PackageReference Include="Microsoft.Identity.Web.UI" Version="2.15.2" />
    <PackageReference Include="Swashbuckle.AspNetCore" Version="6.4.0" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Design" Version="8.0.0" />
    <PackageReference Include="Serilog.AspNetCore" Version="8.0.0" />
    <PackageReference Include="Microsoft.Extensions.Diagnostics.HealthChecks.EntityFrameworkCore" Version="8.0.0" />
    <PackageReference Include="Microsoft.AspNetCore.Diagnostics.HealthChecks" Version="2.2.0" />
    <PackageReference Include="Microsoft.AspNetCore.Mvc.Razor.RuntimeCompilation" Version="8.0.4" />
    <PackageReference Include="MailKit" Version="4.3.0" />
  </ItemGroup>

  <ItemGroup>
    <EmbeddedResource Update="Resources\SharedResourceMarker.resx">
      <Generator>PublicResXFileCodeGenerator</Generator>
      <LastGenOutput>SharedResourceMarker.Designer.cs</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Update="Resources\SharedResourceMarker.en-CA.resx">
      <Generator>PublicResXFileCodeGenerator</Generator>
      <LastGenOutput>SharedResourceMarker.en-CA.Designer.cs</LastGenOutput>
    </EmbeddedResource>
  </ItemGroup>

  <ItemGroup>
    <Compile Remove=".history\**" />
    <Content Remove=".history\**" />
    <EmbeddedResource Remove=".history\**" />
    <None Remove=".history\**" />
    <Compile Remove="ParaHockey.E2E.Tests\**" />
    <Content Remove="ParaHockey.E2E.Tests\**" />
    <EmbeddedResource Remove="ParaHockey.E2E.Tests\**" />
    <None Remove="ParaHockey.E2E.Tests\**" />
  </ItemGroup>

</Project>
