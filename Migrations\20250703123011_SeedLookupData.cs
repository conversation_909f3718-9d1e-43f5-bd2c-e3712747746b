﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace ParaHockeyApp.Migrations
{
    /// <inheritdoc />
    public partial class SeedLookupData : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            // Seed Genders
            migrationBuilder.InsertData(
                table: "Genders",
                columns: new[] { "Id", "DisplayNameKey" },
                values: new object[,]
                {
                    { 1, "Gender_Male" },
                    { 2, "Gender_Female" },
                    { 3, "Gender_Other" }
                });

            // Seed PhoneTypes
            migrationBuilder.InsertData(
                table: "PhoneTypes",
                columns: new[] { "Id", "DisplayNameKey" },
                values: new object[,]
                {
                    { 1, "PhoneType_Mobile" },
                    { 2, "PhoneType_Other" }
                });

            // Seed Provinces (Quebec FIRST, Ontario SECOND, New Brunswick THIRD, then alphabetical)
            migrationBuilder.InsertData(
                table: "Provinces",
                columns: new[] { "Id", "Code", "DisplayNameKey" },
                values: new object[,]
                {
                    { 1, "QC", "Province_QC" },
                    { 2, "ON", "Province_ON" },
                    { 3, "NB", "Province_NB" },
                    { 4, "AB", "Province_AB" },
                    { 5, "BC", "Province_BC" },
                    { 6, "MB", "Province_MB" },
                    { 7, "NL", "Province_NL" },
                    { 8, "NS", "Province_NS" },
                    { 9, "NT", "Province_NT" },
                    { 10, "NU", "Province_NU" },
                    { 11, "PE", "Province_PE" },
                    { 12, "SK", "Province_SK" },
                    { 13, "YT", "Province_YT" }
                });

            // Seed RegistrationTypes
            migrationBuilder.InsertData(
                table: "RegistrationTypes",
                columns: new[] { "Id", "DisplayNameKey", "DescriptionKey" },
                values: new object[,]
                {
                    { 1, "RegType_Junior", "JuniorSubtext" },
                    { 2, "RegType_Development", "DevelopmentSubtext" },
                    { 3, "RegType_Elite", "EliteSubtext" },
                    { 4, "RegType_Coach", "CoachSubtext" },
                    { 5, "RegType_Volunteer", "VolunteerSubtext" }
                });
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            // Remove seeded data in reverse order
            migrationBuilder.DeleteData(
                table: "RegistrationTypes",
                keyColumn: "Id",
                keyValues: new object[] { 1, 2, 3, 4, 5 });

            migrationBuilder.DeleteData(
                table: "Provinces",
                keyColumn: "Id",
                keyValues: new object[] { 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13 });

            migrationBuilder.DeleteData(
                table: "PhoneTypes",
                keyColumn: "Id",
                keyValues: new object[] { 1, 2 });

            migrationBuilder.DeleteData(
                table: "Genders",
                keyColumn: "Id",
                keyValues: new object[] { 1, 2, 3 });
        }
    }
}
