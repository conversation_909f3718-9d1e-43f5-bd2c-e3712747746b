using System.Globalization;
using System.Resources;

namespace ParaHockeyApp.Resources
{
    /// <summary>
    /// Anchor class for shared resources localization
    /// This class is used by IStringLocalizer to find the correct resource files
    /// </summary>
    public partial class SharedResourceMarker
    {
        // This class is intentionally empty - it's just used as an anchor for resource location
    }

    /// <summary>
    /// Resource accessor class for validation attributes
    /// </summary>
    public partial class SharedResource
    {
        // Access the ResX at runtime using the ACTUAL embedded resource name
        private static readonly ResourceManager _rm =
            new ResourceManager("ParaHockeyApp.Resources.SharedResourceMarker",
                                typeof(SharedResourceMarker).Assembly);

        public static string ValidationRequired =>
            _rm.GetString("ValidationRequired", CultureInfo.CurrentUICulture) ?? "Required field";

        public static string ValidationStringLength =>
            _rm.GetString("ValidationStringLength", CultureInfo.CurrentUICulture) ?? "String length validation";

        public static string ValidationPhone =>
            _rm.GetString("ValidationPhone", CultureInfo.CurrentUICulture) ?? "Phone validation";

        public static string ValidationEmail =>
            _rm.GetString("ValidationEmail", CultureInfo.CurrentUICulture) ?? "Email validation";

        public static string ValidationPostalCode =>
            _rm.GetString("ValidationPostalCode", CultureInfo.CurrentUICulture) ?? "Postal code validation";

        public static string ValidationNameTooShort =>
            _rm.GetString("ValidationNameTooShort", CultureInfo.CurrentUICulture) ?? "Name too short";

        public static string ValidationNameTooLong =>
            _rm.GetString("ValidationNameTooLong", CultureInfo.CurrentUICulture) ?? "Name too long";

        public static string ValidationEmailSpecific =>
            _rm.GetString("ValidationEmailSpecific", CultureInfo.CurrentUICulture) ?? "Invalid email format";

        public static string ValidationPhoneSpecific =>
            _rm.GetString("ValidationPhoneSpecific", CultureInfo.CurrentUICulture) ?? "Invalid phone format";

        public static string ValidationPostalCodeSpecific =>
            _rm.GetString("ValidationPostalCodeSpecific", CultureInfo.CurrentUICulture) ?? "Invalid postal code format";

        public static string ValidationDateSpecific =>
            _rm.GetString("ValidationDateSpecific", CultureInfo.CurrentUICulture) ?? "Invalid date format";

        public static string ValidationDateFuture =>
            _rm.GetString("ValidationDateFuture", CultureInfo.CurrentUICulture) ?? "Date cannot be in future";

        public static string ValidationDateTooOld =>
            _rm.GetString("ValidationDateTooOld", CultureInfo.CurrentUICulture) ?? "Date too old";

        public static string ValidationAddressTooShort =>
            _rm.GetString("ValidationAddressTooShort", CultureInfo.CurrentUICulture) ?? "Address too short";

        public static string ValidationCityTooShort =>
            _rm.GetString("ValidationCityTooShort", CultureInfo.CurrentUICulture) ?? "City name too short";

        public static string ValidationProvinceRequired =>
            _rm.GetString("ValidationProvinceRequired", CultureInfo.CurrentUICulture) ?? "Province required";

        public static string ValidationGenderRequired =>
            _rm.GetString("ValidationGenderRequired", CultureInfo.CurrentUICulture) ?? "Gender required";

        public static string ValidationRegistrationTypeRequired =>
            _rm.GetString("ValidationRegistrationTypeRequired", CultureInfo.CurrentUICulture) ?? "Registration type required";

        public static string ValidationPhoneTypeRequired =>
            _rm.GetString("ValidationPhoneTypeRequired", CultureInfo.CurrentUICulture) ?? "Phone type required";
    }
}