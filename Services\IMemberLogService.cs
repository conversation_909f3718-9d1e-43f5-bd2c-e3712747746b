using ParaHockeyApp.Models.Entities;

namespace ParaHockeyApp.Services
{
    public interface IMemberLogService
    {
        Task LogMemberCreatedAsync(Member member, string userEmail);
        Task LogMemberModifiedAsync(Member originalMember, Member updatedMember, int editorId);
        Task<List<MemberLog>> GetMemberLogsAsync(int memberId);
        Task<List<MemberLog>> GetAllLogsAsync(int pageNumber = 1, int pageSize = 50);
    }
}