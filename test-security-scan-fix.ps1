#!/usr/bin/env pwsh

# Test script to verify security scan fixes
Write-Host "Testing Security Scan Fixes..." -ForegroundColor Cyan

# Test 1: Check if _securityAuditService field exists
Write-Host "`n1. Checking AdminController field declarations..." -ForegroundColor Yellow
$adminContent = Get-Content "Controllers/AdminController.cs" -Raw
if ($adminContent -match "_securityAuditService") {
    Write-Host "✅ _securityAuditService field found!" -ForegroundColor Green
} else {
    Write-Host "❌ _securityAuditService field missing!" -ForegroundColor Red
    exit 1
}

# Test 2: Check if constructor parameter exists
Write-Host "`n2. Checking constructor parameter..." -ForegroundColor Yellow
if ($adminContent -match "ISecurityAuditService securityAuditService") {
    Write-Host "✅ Constructor parameter found!" -ForegroundColor Green
} else {
    Write-Host "❌ Constructor parameter missing!" -ForegroundColor Red
    exit 1
}

# Test 3: Check if field is assigned in constructor
Write-Host "`n3. Checking field assignment..." -ForegroundColor Yellow
if ($adminContent -match "_securityAuditService = securityAuditService") {
    Write-Host "✅ Field assignment found!" -ForegroundColor Green
} else {
    Write-Host "❌ Field assignment missing!" -ForegroundColor Red
    exit 1
}

# Test 4: Check JavaScript response handling
Write-Host "`n4. Checking JavaScript response handling..." -ForegroundColor Yellow
$viewContent = Get-Content "Views/Admin/PageAudit.cshtml" -Raw
if ($viewContent -match "response.securityReport") {
    Write-Host "✅ JavaScript uses correct response property!" -ForegroundColor Green
} else {
    Write-Host "❌ JavaScript response property incorrect!" -ForegroundColor Red
    exit 1
}

# Test 5: Check display function properties
Write-Host "`n5. Checking display function properties..." -ForegroundColor Yellow
if ($viewContent -match "totalPagesAudited" -and $viewContent -match "securePagesCount") {
    Write-Host "✅ Display function uses correct properties!" -ForegroundColor Green
} else {
    Write-Host "❌ Display function properties incorrect!" -ForegroundColor Red
    exit 1
}

Write-Host "`n🎉 All security scan fixes verified!" -ForegroundColor Green
Write-Host "📋 Summary:" -ForegroundColor Cyan
Write-Host "  • AdminController properly configured with SecurityAuditService" -ForegroundColor White
Write-Host "  • JavaScript response handling fixed" -ForegroundColor White
Write-Host "  • Display function properties corrected" -ForegroundColor White
Write-Host "  • Security scan should now work properly" -ForegroundColor White

Write-Host "`n🚀 Next steps:" -ForegroundColor Cyan
Write-Host "  1. Stop any running application instances" -ForegroundColor White
Write-Host "  2. Run: dotnet run" -ForegroundColor White
Write-Host "  3. Navigate to Admin > Page Audit" -ForegroundColor White
Write-Host "  4. Click Run Security Scan - it should work now!" -ForegroundColor White