using System.ComponentModel.DataAnnotations;

namespace ParaHockeyApp.DTOs
{
    /// <summary>
    /// Enhanced request DTO for member search with comprehensive filtering options
    /// Supports multi-field search, registration type filtering, status filtering, and date range filtering
    /// </summary>
    public class MemberSearchRequest
    {
        /// <summary>
        /// General search term that searches across name, email, phone, and address fields
        /// </summary>
        [StringLength(100, ErrorMessage = "Search term cannot exceed 100 characters")]
        public string? SearchTerm { get; set; }

        /// <summary>
        /// List of registration type IDs to filter by (supports multiple selections)
        /// </summary>
        public List<int>? RegistrationTypeIds { get; set; }

        /// <summary>
        /// Filter by active/inactive status. Null means show all members
        /// </summary>
        public bool? IsActive { get; set; }

        /// <summary>
        /// Start date for date of birth range filtering
        /// </summary>
        [DataType(DataType.Date)]
        public DateTime? DateOfBirthFrom { get; set; }

        /// <summary>
        /// End date for date of birth range filtering
        /// </summary>
        [DataType(DataType.Date)]
        public DateTime? DateOfBirthTo { get; set; }

        /// <summary>
        /// Minimum age for age range filtering
        /// </summary>
        [Range(0, 120, ErrorMessage = "Age must be between 0 and 120")]
        public int? AgeFrom { get; set; }

        /// <summary>
        /// Maximum age for age range filtering
        /// </summary>
        [Range(0, 120, ErrorMessage = "Age must be between 0 and 120")]
        public int? AgeTo { get; set; }

        /// <summary>
        /// Filter by city name
        /// </summary>
        [StringLength(100, ErrorMessage = "City name cannot exceed 100 characters")]
        public string? City { get; set; }

        /// <summary>
        /// Filter by province name
        /// </summary>
        [StringLength(50, ErrorMessage = "Province name cannot exceed 50 characters")]
        public string? Province { get; set; }

        /// <summary>
        /// Filter by postal code (supports partial matches)
        /// </summary>
        [StringLength(10, ErrorMessage = "Postal code cannot exceed 10 characters")]
        public string? PostalCode { get; set; }

        /// <summary>
        /// Current page number for pagination (1-based)
        /// </summary>
        [Range(1, int.MaxValue, ErrorMessage = "Page must be greater than 0")]
        public int Page { get; set; } = 1;

        /// <summary>
        /// Number of results per page (maximum 100 for performance)
        /// </summary>
        [Range(1, 100, ErrorMessage = "Page size must be between 1 and 100")]
        public int PageSize { get; set; } = 20;

        /// <summary>
        /// Field to sort by (e.g., "FirstName", "LastName", "DateCreated")
        /// </summary>
        [StringLength(50, ErrorMessage = "Sort field name cannot exceed 50 characters")]
        public string? SortBy { get; set; }

        /// <summary>
        /// Whether to sort in descending order
        /// </summary>
        public bool SortDescending { get; set; }

        /// <summary>
        /// Validates that date ranges are logical (from date is not after to date)
        /// </summary>
        /// <returns>True if validation passes</returns>
        public bool IsValid()
        {
            // Validate date range
            if (DateOfBirthFrom.HasValue && DateOfBirthTo.HasValue && DateOfBirthFrom > DateOfBirthTo)
                return false;

            // Validate age range
            if (AgeFrom.HasValue && AgeTo.HasValue && AgeFrom > AgeTo)
                return false;

            return true;
        }

        /// <summary>
        /// Checks if any search criteria are specified
        /// </summary>
        /// <returns>True if at least one search criterion is provided</returns>
        public bool HasSearchCriteria()
        {
            return !string.IsNullOrWhiteSpace(SearchTerm) ||
                   RegistrationTypeIds?.Any() == true ||
                   IsActive.HasValue ||
                   DateOfBirthFrom.HasValue ||
                   DateOfBirthTo.HasValue ||
                   AgeFrom.HasValue ||
                   AgeTo.HasValue ||
                   !string.IsNullOrWhiteSpace(City) ||
                   !string.IsNullOrWhiteSpace(Province) ||
                   !string.IsNullOrWhiteSpace(PostalCode);
        }
    }
}