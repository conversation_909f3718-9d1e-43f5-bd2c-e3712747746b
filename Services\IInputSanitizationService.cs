namespace ParaHockeyApp.Services
{
    /// <summary>
    /// Service for sanitizing user input to prevent XSS and other injection attacks
    /// </summary>
    public interface IInputSanitizationService
    {
        /// <summary>
        /// Sanitizes a text input by removing potentially dangerous HTML and scripts
        /// </summary>
        /// <param name="input">The input text to sanitize</param>
        /// <returns>Sanitized text safe for storage and display</returns>
        string SanitizeText(string? input);

        /// <summary>
        /// Sanitizes HTML content while preserving safe formatting tags
        /// </summary>
        /// <param name="html">The HTML content to sanitize</param>
        /// <returns>Sanitized HTML content</returns>
        string SanitizeHtml(string? html);

        /// <summary>
        /// Validates and sanitizes an email address
        /// </summary>
        /// <param name="email">The email address to sanitize</param>
        /// <returns>Sanitized email address or null if invalid</returns>
        string? SanitizeEmail(string? email);

        /// <summary>
        /// Sanitizes a phone number by removing potentially dangerous characters
        /// </summary>
        /// <param name="phone">The phone number to sanitize</param>
        /// <returns>Sanitized phone number</returns>
        string? SanitizePhone(string? phone);

        /// <summary>
        /// Sanitizes file names to prevent directory traversal attacks
        /// </summary>
        /// <param name="fileName">The file name to sanitize</param>
        /// <returns>Sanitized file name</returns>
        string SanitizeFileName(string? fileName);
    }
}