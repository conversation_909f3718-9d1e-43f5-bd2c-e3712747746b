using ParaHockeyApp.Models.Entities;
using ParaHockeyApp.Models;
using ParaHockeyApp.Models.Session;
using System.Security.Claims;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

namespace ParaHockeyApp.Services
{
    /// <summary>
    /// Service for tracking the current user context during operations.
    /// This helps distinguish between admin actions and member self-service actions.
    /// </summary>
    public class UserContextService : IUserContextService
    {
        private readonly IHttpContextAccessor _httpContextAccessor;
        private readonly IServiceProvider _serviceProvider;
        private readonly ILogger<UserContextService> _logger;
        private UserContext? _currentUser;

        public UserContextService(IHttpContextAccessor httpContextAccessor, IServiceProvider serviceProvider, ILogger<UserContextService> logger)
        {
            _httpContextAccessor = httpContextAccessor;
            _serviceProvider = serviceProvider;
            _logger = logger;
        }

        /// <summary>
        /// Gets the current user context for audit logging
        /// </summary>
        public UserContext GetCurrentUser()
        {
            // CRITICAL: Always refresh user context to avoid authentication state caching issues
            // Never use cached context as authentication state can change between requests
            _currentUser = null; // Force refresh

            var httpContext = _httpContextAccessor.HttpContext;
            
            // DEBUG: Log authentication detection
            _logger.LogInformation("🔍 UserContextService.GetCurrentUser() called");
            _logger.LogInformation("🔍 HttpContext available: {HasHttpContext}", httpContext != null);
            _logger.LogInformation("🔍 HttpContext.User available: {HasUser}", httpContext?.User != null);
            _logger.LogInformation("🔍 User.Identity available: {HasIdentity}", httpContext?.User?.Identity != null);
            _logger.LogInformation("🔍 User.Identity.IsAuthenticated: {IsAuthenticated}", httpContext?.User?.Identity?.IsAuthenticated ?? false);
            _logger.LogInformation("🔍 User.Identity.Name: {UserName}", httpContext?.User?.Identity?.Name ?? "NULL");
            _logger.LogInformation("🔍 User.Identity.AuthenticationType: {AuthType}", httpContext?.User?.Identity?.AuthenticationType ?? "NULL");
            
            // Check for ASP.NET Core Identity authentication (Azure AD/Admin) OR member session authentication
            bool hasIdentityAuth = httpContext?.User?.Identity?.IsAuthenticated == true;
            bool hasMemberSession = httpContext?.Session?.GetString("MemberSessionData") != null;
            
            _logger.LogInformation("🔍 Has Identity Auth: {HasIdentityAuth}", hasIdentityAuth);
            _logger.LogInformation("🔍 Has Member Session: {HasMemberSession}", hasMemberSession);
            
            // PRIORITY FIX: If we have BOTH member session AND identity auth, prioritize MEMBER SESSION
            // This handles the case where admin is being logged out during member login
            if (hasMemberSession || hasIdentityAuth)
            {
                var user = httpContext.User;
                string? userEmail = null;
                
                // PRIORITY: Check member session FIRST (even if identity auth exists)
                if (hasMemberSession)
                {
                    try
                    {
                        var memberSessionJson = httpContext.Session.GetString("MemberSessionData");
                        if (!string.IsNullOrEmpty(memberSessionJson))
                        {
                            var memberSession = System.Text.Json.JsonSerializer.Deserialize<MemberSession>(memberSessionJson);
                            if (memberSession != null && memberSession.IsValidSession())
                            {
                                userEmail = memberSession.Email;
                                _logger.LogInformation("🔍 Valid member session found - Email: {Email}, MemberId: {MemberId}", 
                                    memberSession.Email, memberSession.MemberId);
                            }
                            else
                            {
                                _logger.LogInformation("🔍 Member session found but expired or invalid");
                                // Clear expired session
                                httpContext.Session.Remove("MemberSessionData");
                                hasMemberSession = false;
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        _logger.LogWarning(ex, "Failed to deserialize member session data");
                        hasMemberSession = false;
                    }
                }
                
                // If member session didn't work but we have identity auth, use that as fallback
                if (!hasMemberSession && hasIdentityAuth)
                {
                    userEmail = user.Claims.FirstOrDefault(c => c.Type == "preferred_username")?.Value ?? user.Identity?.Name;
                    _logger.LogInformation("🔍 Using identity auth as fallback - Email: {Email}", userEmail);
                }
                
                // If neither authentication method is valid, return unauthenticated context
                if (!hasIdentityAuth && !hasMemberSession)
                {
                    _logger.LogInformation("🔍 Neither Identity auth nor valid member session found");
                    var unauthContext = new UserContext
                    {
                        IsAdmin = false,
                        UserName = "System",
                        Source = ActionSource.System
                    };
                    // Don't cache to avoid authentication state issues
                    // _currentUser = unauthContext;
                    return unauthContext;
                }
                
                // DEBUG: Log user claims and email detection
                _logger.LogInformation("🔍 User claims count: {ClaimsCount}", user.Claims.Count());
                _logger.LogInformation("🔍 Preferred username claim: {PreferredUsername}", user.Claims.FirstOrDefault(c => c.Type == "preferred_username")?.Value ?? "NULL");
                _logger.LogInformation("🔍 Resolved user email: {UserEmail}", userEmail ?? "NULL");
                
                // Check if user is an admin using database lookup (same pattern as AdminController)
                using (var scope = _serviceProvider.CreateScope())
                {
                    var context = scope.ServiceProvider.GetRequiredService<ApplicationContext>();
                    
                    // PRIORITY FIX: If we have a valid member session, treat as member (not admin)
                    bool isAdmin = false;
                    AdminUser? adminUser = null;
                    
                    if (hasMemberSession)
                    {
                        // Member session takes priority - they are definitely NOT admin
                        isAdmin = false;
                        _logger.LogInformation("🔍 Member session active - treating as member, not admin");
                    }
                    else if (hasIdentityAuth)
                    {
                        // Only check admin status if no member session
                        adminUser = context.AdminUsers.FirstOrDefault(a => a.Email == userEmail);
                        isAdmin = adminUser?.CanLogin == true;
                    }
                    
                    // DEBUG: Log admin and member detection
                    _logger.LogInformation("🔍 Admin user found: {AdminFound}", adminUser != null);
                    _logger.LogInformation("🔍 Admin can login: {CanLogin}", adminUser?.CanLogin ?? false);
                    _logger.LogInformation("🔍 Is admin: {IsAdmin}", isAdmin);
                    
                    var userContext = new UserContext
                    {
                        IsAdmin = isAdmin,
                        UserName = user.Identity.Name ?? "Unknown User",
                        Email = userEmail,
                        Source = isAdmin ? ActionSource.AdminPanel : ActionSource.SelfService
                    };

                    // Set admin ID if user is an admin
                    if (isAdmin && adminUser != null)
                    {
                        userContext.AdminId = adminUser.Id;
                    }
                    else
                    {
                        // If we have member session, get member ID directly from session
                        if (hasMemberSession)
                        {
                            try
                            {
                                var memberSessionJson = httpContext.Session.GetString("MemberSessionData");
                                if (!string.IsNullOrEmpty(memberSessionJson))
                                {
                                    var memberSession = System.Text.Json.JsonSerializer.Deserialize<MemberSession>(memberSessionJson);
                                    if (memberSession != null && memberSession.IsValidSession())
                                    {
                                        userContext.MemberId = memberSession.MemberId;
                                        _logger.LogInformation("🔍 Member ID from session: {MemberId}", memberSession.MemberId);
                                    }
                                }
                            }
                            catch (Exception ex)
                            {
                                _logger.LogWarning(ex, "Failed to get member ID from session");
                            }
                        }
                        
                        // Fallback: Try to find member by email if we don't have session data
                        if (userContext.MemberId == null)
                        {
                            var member = context.Members.FirstOrDefault(m => m.Email == userEmail);
                            if (member != null)
                            {
                                userContext.MemberId = member.Id;
                                _logger.LogInformation("🔍 Member found by email: {MemberId}", member.Id);
                            }
                            else
                            {
                                _logger.LogInformation("🔍 No member found with email: {Email}", userEmail ?? "NULL");
                            }
                        }
                    }

                    // DEBUG: Log final user context
                    _logger.LogInformation("🔍 Final UserContext - IsAdmin: {IsAdmin}, MemberId: {MemberId}, AdminId: {AdminId}", 
                        userContext.IsAdmin, userContext.MemberId?.ToString() ?? "NULL", userContext.AdminId?.ToString() ?? "NULL");
                    
                    // Don't cache the user context to avoid authentication state issues
                    // _currentUser = userContext;
                    return userContext;
                }
            }

            // Return system context for unauthenticated operations
            _logger.LogInformation("🔍 Returning unauthenticated system context");
            var systemContext = new UserContext
            {
                IsAdmin = false,
                UserName = "System",
                Source = ActionSource.System
            };
            // Don't cache to avoid authentication state issues
            // _currentUser = systemContext;
            return systemContext;
        }

        /// <summary>
        /// Sets the current user context (used by authentication middleware or for testing)
        /// </summary>
        public void SetCurrentUser(UserContext userContext)
        {
            _currentUser = userContext;
        }

        /// <summary>
        /// Gets the client IP address for the current request
        /// </summary>
        public string? GetClientIPAddress()
        {
            var httpContext = _httpContextAccessor.HttpContext;
            if (httpContext == null) return null;

            // Check for forwarded IP first (in case of proxy/load balancer)
            var forwardedFor = httpContext.Request.Headers["X-Forwarded-For"].FirstOrDefault();
            if (!string.IsNullOrEmpty(forwardedFor))
            {
                return forwardedFor.Split(',')[0].Trim();
            }

            // Check for real IP header
            var realIp = httpContext.Request.Headers["X-Real-IP"].FirstOrDefault();
            if (!string.IsNullOrEmpty(realIp))
            {
                return realIp;
            }

            // Fall back to remote IP address
            return httpContext.Connection.RemoteIpAddress?.ToString();
        }
    }
}