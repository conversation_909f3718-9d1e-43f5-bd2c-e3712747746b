namespace ParaHockeyApp.Models.Entities
{
    /// <summary>
    /// Enum to track the source of an action performed on an entity.
    /// This helps distinguish between system actions, member self-service, and admin actions.
    /// </summary>
    public enum ActionSource
    {
        /// <summary>
        /// Automated system actions (migrations, background processes, etc.)
        /// </summary>
        System = 0,

        /// <summary>
        /// Member performing actions on their own data (self-registration, profile updates)
        /// </summary>
        SelfService = 1,

        /// <summary>
        /// Admin performing actions through the admin panel
        /// </summary>
        AdminPanel = 2
    }
}