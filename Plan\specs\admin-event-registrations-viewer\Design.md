# Design: Admin Event Registration Viewer

## Architecture Overview

This feature enhances the existing Admin Calendar event modal with a new "Registrations" tab that provides comprehensive registration management capabilities while preserving all existing functionality.

## 1. Enhanced Admin Event Modal Structure

### 1.1 Modal Tab System

```html
<!-- Enhanced Admin Event Modal with Registration Tab -->
<div class="modal-dialog modal-xl">
    <div class="modal-content">
        <div class="modal-header">
            <ul class="nav nav-tabs modal-tabs" role="tablist">
                <li class="nav-item">
                    <a
                        class="nav-link active"
                        id="event-details-tab"
                        data-bs-toggle="tab"
                        href="#event-details-pane"
                        role="tab">
                        <i class="fas fa-calendar-alt"></i> Event Details
                    </a>
                </li>
                <li
                    class="nav-item"
                    id="registrations-tab-item"
                    style="display: none;">
                    <a
                        class="nav-link"
                        id="registrations-tab"
                        data-bs-toggle="tab"
                        href="#registrations-pane"
                        role="tab">
                        <i class="fas fa-users"></i> Registrations
                        <span
                            class="badge bg-primary ms-1"
                            id="registration-count"
                            >0</span
                        >
                    </a>
                </li>
            </ul>
            <button
                type="button"
                class="btn-close"
                data-bs-dismiss="modal"></button>
        </div>
        <div class="modal-body">
            <div class="tab-content">
                <!-- Existing Event Details Tab -->
                <div class="tab-pane fade show active" id="event-details-pane">
                    <!-- Existing event form content -->
                </div>

                <!-- New Registrations Tab -->
                <div class="tab-pane fade" id="registrations-pane">
                    <!-- Registration management interface -->
                </div>
            </div>
        </div>
    </div>
</div>
```

### 1.2 Registration Tab Visibility Logic

```javascript
// Show registrations tab only for events that require registration
function updateRegistrationTabVisibility(eventData) {
    const registrationsTabItem = document.getElementById(
        "registrations-tab-item"
    );
    if (eventData.requiresRegistration && eventData.id) {
        registrationsTabItem.style.display = "block";
        updateRegistrationCount(eventData.id);
    } else {
        registrationsTabItem.style.display = "none";
    }
}
```

## 2. Registration Management Interface

### 2.1 Registration List Layout

```html
<div id="registrations-pane" class="tab-pane fade">
    <!-- Registration Summary -->
    <div class="row mb-4">
        <div class="col-md-8">
            <div class="d-flex align-items-center">
                <h5 class="mb-0">Event Registrations</h5>
                <div class="ms-3">
                    <span class="badge bg-success me-1">
                        <span id="confirmed-count">0</span> Confirmed
                    </span>
                    <span class="badge bg-warning me-1">
                        <span id="pending-count">0</span> Pending
                    </span>
                    <span class="badge bg-secondary me-1">
                        <span id="waitlisted-count">0</span> Waitlisted
                    </span>
                </div>
            </div>
            <p class="text-muted mb-0">
                <span id="total-participants">0</span> total participants (<span
                    id="capacity-display"
                    >No limit</span
                >)
            </p>
        </div>
        <div class="col-md-4 text-end">
            <div class="btn-group">
                <button class="btn btn-outline-primary btn-sm" id="filter-btn">
                    <i class="fas fa-filter"></i> Filter
                </button>
                <button
                    class="btn btn-outline-success btn-sm"
                    id="export-registrations-btn">
                    <i class="fas fa-download"></i> Export CSV
                </button>
            </div>
        </div>
    </div>

    <!-- Filter Controls -->
    <div id="registration-filters" class="row mb-3" style="display: none;">
        <div class="col-md-6">
            <select class="form-select form-select-sm" id="status-filter">
                <option value="">All Statuses</option>
                <option value="Confirmed">Confirmed Only</option>
                <option value="Pending">Pending Only</option>
                <option value="Waitlisted">Waitlisted Only</option>
                <option value="Cancelled">Cancelled Only</option>
            </select>
        </div>
        <div class="col-md-6">
            <input
                type="date"
                class="form-control form-control-sm"
                id="date-filter"
                placeholder="Registration date" />
        </div>
    </div>

    <!-- Registrations List -->
    <div
        id="registrations-loading"
        class="text-center py-4"
        style="display: none;">
        <div class="spinner-border text-primary" role="status">
            <span class="visually-hidden">Loading registrations...</span>
        </div>
    </div>

    <div id="registrations-content">
        <div id="registrations-list" class="list-group">
            <!-- Dynamic registration items -->
        </div>

        <div
            id="no-registrations"
            class="text-center py-5"
            style="display: none;">
            <i class="fas fa-user-slash fa-3x text-muted mb-3"></i>
            <h5 class="text-muted">No Registrations Yet</h5>
            <p class="text-muted">
                When members register for this event, they will appear here.
            </p>
        </div>
    </div>
</div>
```

### 2.2 Registration Item Template

```html
<div class="list-group-item registration-item" data-registration-id="{{id}}">
    <div class="row align-items-center">
        <div class="col-md-4">
            <div class="d-flex align-items-center">
                <div class="me-3">
                    <span class="badge badge-status bg-{{statusColor}}"
                        >{{status}}</span
                    >
                </div>
                <div>
                    <strong>{{memberName}}</strong>
                    <br />
                    <small class="text-muted">
                        <a href="mailto:{{memberEmail}}">{{memberEmail}}</a>
                    </small>
                </div>
            </div>
        </div>

        <div class="col-md-3">
            <small class="text-muted">
                <i class="fas fa-calendar"></i> {{registrationDate}}<br />
                {{#if guestCount}}
                <i class="fas fa-users"></i> +{{guestCount}} guests {{/if}}
            </small>
        </div>

        <div class="col-md-3">
            {{#if memberNotes}}
            <small class="text-info">
                <i class="fas fa-comment"></i> {{memberNotes}}
            </small>
            {{/if}} {{#if adminNotes}}
            <small class="text-warning">
                <i class="fas fa-sticky-note"></i> {{adminNotes}}
            </small>
            {{/if}}
        </div>

        <div class="col-md-2 text-end">
            <div class="btn-group">
                <button
                    class="btn btn-sm btn-outline-secondary dropdown-toggle"
                    data-bs-toggle="dropdown">
                    Actions
                </button>
                <ul class="dropdown-menu">
                    <li>
                        <a
                            class="dropdown-item"
                            href="#"
                            onclick="changeStatus('{{id}}', 'Confirmed')">
                            <i class="fas fa-check text-success"></i> Confirm
                        </a>
                    </li>
                    <li>
                        <a
                            class="dropdown-item"
                            href="#"
                            onclick="changeStatus('{{id}}', 'Pending')">
                            <i class="fas fa-clock text-warning"></i> Mark
                            Pending
                        </a>
                    </li>
                    <li>
                        <a
                            class="dropdown-item"
                            href="#"
                            onclick="changeStatus('{{id}}', 'Rejected')">
                            <i class="fas fa-times text-danger"></i> Reject
                        </a>
                    </li>
                    <li><hr class="dropdown-divider" /></li>
                    <li>
                        <a
                            class="dropdown-item"
                            href="#"
                            onclick="addAdminNote('{{id}}')">
                            <i class="fas fa-edit"></i> Add Note
                        </a>
                    </li>
                    {{#if isPastEvent}}
                    <li>
                        <a
                            class="dropdown-item"
                            href="#"
                            onclick="markAttendance('{{id}}')">
                            <i class="fas fa-user-check"></i> Mark Attendance
                        </a>
                    </li>
                    {{/if}}
                </ul>
            </div>
        </div>
    </div>
</div>
```

## 3. Enhanced AdminController

### 3.1 Registration Management Endpoints

```csharp
[HttpGet]
public async Task<IActionResult> GetEventRegistrations(int id)
{
    if (!await IsUserAdminAsync())
        return Forbid();

    var registrations = await _eventService.GetEventRegistrationsAsync(id);

    return Json(new {
        registrations = registrations.Select(r => new
        {
            id = r.Id,
            memberName = $"{r.Member.FirstName} {r.Member.LastName}",
            memberEmail = r.Member.Email,
            memberId = r.MemberId,
            status = r.Status.ToString(),
            statusColor = GetStatusColor(r.Status),
            registrationDate = r.RegistrationDate.ToString("yyyy-MM-dd HH:mm"),
            confirmationDate = r.ConfirmationDate?.ToString("yyyy-MM-dd HH:mm"),
            memberNotes = r.MemberNotes,
            adminNotes = r.AdminNotes,
            guestCount = r.GuestCount,
            totalParticipants = r.TotalParticipants,
            attended = r.Attended,
            canModify = r.CanBeCancelled
        }),
        summary = new {
            totalRegistrations = registrations.Count,
            confirmedCount = registrations.Count(r => r.Status == RegistrationStatus.Confirmed),
            pendingCount = registrations.Count(r => r.Status == RegistrationStatus.Pending),
            waitlistedCount = registrations.Count(r => r.Status == RegistrationStatus.Waitlisted),
            totalParticipants = registrations.Where(r => r.Status == RegistrationStatus.Confirmed)
                                           .Sum(r => r.TotalParticipants)
        }
    });
}

[HttpPost]
public async Task<IActionResult> UpdateRegistrationStatus([FromBody] UpdateRegistrationStatusRequest request)
{
    if (!await IsUserAdminAsync())
        return Forbid();

    try
    {
        var success = await _eventService.UpdateRegistrationStatusAsync(
            request.RegistrationId,
            request.Status,
            request.AdminNotes,
            GetCurrentAdminContext());

        if (success)
        {
            return Json(new { success = true, message = "Registration status updated successfully" });
        }

        return Json(new { success = false, error = "Failed to update registration status" });
    }
    catch (Exception ex)
    {
        _logger.LogError(ex, "Error updating registration status for {RegistrationId}", request.RegistrationId);
        return Json(new { success = false, error = "An error occurred while updating the registration" });
    }
}

[HttpPost]
public async Task<IActionResult> MarkAttendance([FromBody] MarkAttendanceRequest request)
{
    if (!await IsUserAdminAsync())
        return Forbid();

    var success = await _eventService.MarkAttendanceAsync(request.RegistrationId, request.Attended);

    return Json(new {
        success = success,
        message = success ? "Attendance updated" : "Failed to update attendance"
    });
}

[HttpGet]
public async Task<IActionResult> ExportEventRegistrations(int eventId, string status = "", string format = "CSV")
{
    if (!await IsUserAdminAsync())
        return Forbid();

    var registrations = await _eventService.GetEventRegistrationsAsync(eventId);

    if (!string.IsNullOrEmpty(status))
    {
        registrations = registrations.Where(r => r.Status.ToString() == status).ToList();
    }

    var csvContent = GenerateRegistrationsCsv(registrations);
    var eventDetails = await _eventService.GetEventByIdAsync(eventId);
    var fileName = $"{eventDetails.Title}_Registrations_{DateTime.Now:yyyyMMdd}.csv";

    return File(Encoding.UTF8.GetBytes(csvContent), "text/csv", fileName);
}

private string GetStatusColor(RegistrationStatus status) => status switch
{
    RegistrationStatus.Confirmed => "success",
    RegistrationStatus.Pending => "warning",
    RegistrationStatus.Waitlisted => "info",
    RegistrationStatus.Rejected => "danger",
    RegistrationStatus.Cancelled => "secondary",
    _ => "secondary"
};
```

### 3.2 Request/Response DTOs

```csharp
public class UpdateRegistrationStatusRequest
{
    public int RegistrationId { get; set; }
    public RegistrationStatus Status { get; set; }
    public string? AdminNotes { get; set; }
}

public class MarkAttendanceRequest
{
    public int RegistrationId { get; set; }
    public bool Attended { get; set; }
}

public class EventRegistrationSummary
{
    public int TotalRegistrations { get; set; }
    public int ConfirmedCount { get; set; }
    public int PendingCount { get; set; }
    public int WaitlistedCount { get; set; }
    public int TotalParticipants { get; set; }
}
```

## 4. Enhanced EventService

### 4.1 Registration Management Methods

```csharp
public async Task<bool> UpdateRegistrationStatusAsync(int registrationId, RegistrationStatus newStatus,
    string? adminNotes, AdminContext adminContext)
{
    var registration = await _context.EventRegistrations
        .Include(r => r.Event)
        .Include(r => r.Member)
        .FirstOrDefaultAsync(r => r.Id == registrationId);

    if (registration == null)
        return false;

    var oldStatus = registration.Status;
    registration.Status = newStatus;
    registration.AdminNotes = adminNotes;

    if (newStatus == RegistrationStatus.Confirmed && !registration.ConfirmationDate.HasValue)
    {
        registration.ConfirmationDate = DateTime.UtcNow;
    }

    await _context.SaveChangesAsync();

    // Create audit log entry
    await _auditLogService.LogActionAsync(
        $"Registration status changed from {oldStatus} to {newStatus}",
        registration.Member,
        ActionSource.AdminPanel,
        adminContext.AdminName,
        adminContext.IPAddress);

    return true;
}

public async Task<bool> BulkUpdateRegistrationStatusAsync(List<int> registrationIds,
    RegistrationStatus newStatus, AdminContext adminContext)
{
    var registrations = await _context.EventRegistrations
        .Where(r => registrationIds.Contains(r.Id))
        .ToListAsync();

    foreach (var registration in registrations)
    {
        registration.Status = newStatus;
        if (newStatus == RegistrationStatus.Confirmed && !registration.ConfirmationDate.HasValue)
        {
            registration.ConfirmationDate = DateTime.UtcNow;
        }
    }

    await _context.SaveChangesAsync();

    await _auditLogService.LogActionAsync(
        $"Bulk status update: {registrations.Count} registrations set to {newStatus}",
        null,
        ActionSource.AdminPanel,
        adminContext.AdminName,
        adminContext.IPAddress);

    return true;
}
```

## 5. JavaScript Registration Management

### 5.1 Registration Tab Controller

```javascript
class RegistrationTabController {
    constructor() {
        this.currentEventId = null;
        this.registrations = [];
        this.filters = {
            status: "",
            date: "",
        };
    }

    async loadRegistrations(eventId) {
        this.currentEventId = eventId;
        this.showLoading();

        try {
            const response = await fetch(
                `/Admin/GetEventRegistrations/${eventId}`
            );
            const data = await response.json();

            this.registrations = data.registrations;
            this.updateSummary(data.summary);
            this.renderRegistrations();
        } catch (error) {
            console.error("Error loading registrations:", error);
            this.showError("Failed to load registrations");
        }
    }

    renderRegistrations() {
        const container = document.getElementById("registrations-list");
        const filteredRegistrations = this.applyFilters();

        if (filteredRegistrations.length === 0) {
            document.getElementById("no-registrations").style.display = "block";
            container.style.display = "none";
        } else {
            document.getElementById("no-registrations").style.display = "none";
            container.style.display = "block";
            container.innerHTML = filteredRegistrations
                .map(this.renderRegistrationItem)
                .join("");
        }
    }

    renderRegistrationItem(registration) {
        return `
            <div class="list-group-item registration-item" data-registration-id="${
                registration.id
            }">
                <div class="row align-items-center">
                    <div class="col-md-4">
                        <div class="d-flex align-items-center">
                            <div class="me-3">
                                <span class="badge bg-${
                                    registration.statusColor
                                }">${registration.status}</span>
                            </div>
                            <div>
                                <strong>${registration.memberName}</strong>
                                <br>
                                <small class="text-muted">
                                    <a href="mailto:${
                                        registration.memberEmail
                                    }">${registration.memberEmail}</a>
                                </small>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-3">
                        <small class="text-muted">
                            <i class="fas fa-calendar"></i> ${
                                registration.registrationDate
                            }<br>
                            ${
                                registration.guestCount > 0
                                    ? `<i class="fas fa-users"></i> +${registration.guestCount} guests`
                                    : ""
                            }
                        </small>
                    </div>
                    
                    <div class="col-md-3">
                        ${
                            registration.memberNotes
                                ? `<small class="text-info"><i class="fas fa-comment"></i> ${registration.memberNotes}</small><br>`
                                : ""
                        }
                        ${
                            registration.adminNotes
                                ? `<small class="text-warning"><i class="fas fa-sticky-note"></i> ${registration.adminNotes}</small>`
                                : ""
                        }
                    </div>
                    
                    <div class="col-md-2 text-end">
                        ${this.renderActionButtons(registration)}
                    </div>
                </div>
            </div>
        `;
    }

    async changeRegistrationStatus(registrationId, newStatus) {
        try {
            const response = await fetch("/Admin/UpdateRegistrationStatus", {
                method: "POST",
                headers: {
                    "Content-Type": "application/json",
                    RequestVerificationToken: document.querySelector(
                        'input[name="__RequestVerificationToken"]'
                    ).value,
                },
                body: JSON.stringify({
                    registrationId: registrationId,
                    status: newStatus,
                }),
            });

            const result = await response.json();

            if (result.success) {
                // Reload registrations to reflect changes
                await this.loadRegistrations(this.currentEventId);
                this.showSuccessMessage(
                    `Registration status updated to ${newStatus}`
                );
            } else {
                this.showErrorMessage(
                    result.error || "Failed to update registration status"
                );
            }
        } catch (error) {
            console.error("Error updating registration status:", error);
            this.showErrorMessage(
                "An error occurred while updating the registration"
            );
        }
    }

    async exportRegistrations() {
        const statusFilter = this.filters.status
            ? `&status=${this.filters.status}`
            : "";
        const url = `/Admin/ExportEventRegistrations?eventId=${this.currentEventId}${statusFilter}`;

        // Create temporary link to download file
        const link = document.createElement("a");
        link.href = url;
        link.download = "";
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
    }

    applyFilters() {
        return this.registrations.filter((registration) => {
            if (
                this.filters.status &&
                registration.status !== this.filters.status
            ) {
                return false;
            }
            if (
                this.filters.date &&
                !registration.registrationDate.startsWith(this.filters.date)
            ) {
                return false;
            }
            return true;
        });
    }

    updateSummary(summary) {
        document.getElementById("confirmed-count").textContent =
            summary.confirmedCount;
        document.getElementById("pending-count").textContent =
            summary.pendingCount;
        document.getElementById("waitlisted-count").textContent =
            summary.waitlistedCount;
        document.getElementById("total-participants").textContent =
            summary.totalParticipants;
        document.getElementById("registration-count").textContent =
            summary.totalRegistrations;
    }
}

// Global instance
const registrationController = new RegistrationTabController();
```

## 6. Modal Integration Logic

### 6.1 Enhanced Event Modal Initialization

```javascript
function openEventModal(eventId = null, startDate = null, endDate = null) {
    // Reset both tabs
    document.getElementById("event-details-tab").click(); // Show event details first
    resetEventForm();
    resetRegistrationTab();

    if (eventId) {
        // Edit mode - load event details
        loadEventDetails(eventId).then((eventData) => {
            populateEventForm(eventData);
            updateRegistrationTabVisibility(eventData);
        });
    } else {
        // Create mode
        hideRegistrationTab();
        if (startDate)
            document.getElementById("eventStartDate").value = startDate;
        if (endDate) document.getElementById("eventEndDate").value = endDate;
    }

    // Show modal
    document.getElementById("eventModal").classList.add("show");
}

function updateRegistrationTabVisibility(eventData) {
    const registrationsTabItem = document.getElementById(
        "registrations-tab-item"
    );

    if (eventData.requiresRegistration && eventData.id) {
        registrationsTabItem.style.display = "block";

        // Set up lazy loading for registration data
        document.getElementById("registrations-tab").addEventListener(
            "shown.bs.tab",
            function () {
                if (!this.dataset.loaded) {
                    registrationController.loadRegistrations(eventData.id);
                    this.dataset.loaded = "true";
                }
            },
            { once: true }
        );
    } else {
        registrationsTabItem.style.display = "none";
    }
}

function resetRegistrationTab() {
    document.getElementById("registrations-tab").dataset.loaded = "false";
    document.getElementById("registrations-content").innerHTML = "";
    document.getElementById("registration-count").textContent = "0";
}
```

## 7. CSS Enhancements

### 7.1 Modal and Registration Styling

```css
/* Enhanced modal for registration management */
.modal-xl .modal-dialog {
    max-width: 1200px;
}

.modal-tabs {
    border-bottom: 1px solid #dee2e6;
    margin-bottom: 0;
    background-color: #f8f9fa;
}

.modal-tabs .nav-link {
    border: none;
    border-bottom: 3px solid transparent;
    background-color: transparent;
    color: #6c757d;
}

.modal-tabs .nav-link.active {
    background-color: #fff;
    color: #495057;
    border-bottom-color: #007bff;
}

/* Registration list styling */
.registration-item {
    border-left: 4px solid transparent;
    transition: all 0.2s ease;
}

.registration-item:hover {
    background-color: #f8f9fa;
    border-left-color: #007bff;
}

.registration-item[data-status="Confirmed"] {
    border-left-color: #28a745;
}

.registration-item[data-status="Pending"] {
    border-left-color: #ffc107;
}

.registration-item[data-status="Waitlisted"] {
    border-left-color: #17a2b8;
}

.registration-item[data-status="Rejected"] {
    border-left-color: #dc3545;
}

.badge-status {
    font-size: 0.75em;
    min-width: 80px;
    text-align: center;
}

/* Filter controls */
.registration-filters {
    background-color: #f8f9fa;
    border-radius: 0.375rem;
    padding: 1rem;
    border: 1px solid #dee2e6;
}

/* Loading states */
.registrations-loading {
    min-height: 200px;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* Mobile responsiveness */
@media (max-width: 768px) {
    .modal-xl .modal-dialog {
        max-width: 95%;
        margin: 1rem;
    }

    .registration-item .col-md-2,
    .registration-item .col-md-3 {
        margin-top: 0.5rem;
    }

    .btn-group .dropdown-toggle {
        width: 100%;
    }
}
```

## 8. Data Flow Architecture

```mermaid
sequenceDiagram
    participant A as Admin
    participant M as Admin Modal
    participant JS as JavaScript
    participant AC as AdminController
    participant ES as EventService
    participant DB as Database

    A->>M: Opens event modal
    M->>JS: Initialize modal
    JS->>AC: Load event details
    AC->>ES: GetEventByIdAsync()
    ES->>DB: Query event
    DB-->>ES: Event data
    ES-->>AC: Event object
    AC-->>JS: Event JSON
    JS->>M: Show event details + registration tab

    A->>M: Clicks "Registrations" tab
    M->>JS: Tab activated (lazy load)
    JS->>AC: GET /Admin/GetEventRegistrations/{id}
    AC->>ES: GetEventRegistrationsAsync()
    ES->>DB: Query registrations with members
    DB-->>ES: Registration data
    ES-->>AC: Registration list
    AC-->>JS: Registration JSON
    JS->>M: Render registration list

    A->>M: Changes registration status
    M->>JS: Status change action
    JS->>AC: POST /Admin/UpdateRegistrationStatus
    AC->>ES: UpdateRegistrationStatusAsync()
    ES->>DB: Update registration + audit log
    DB-->>ES: Success
    ES-->>AC: Success result
    AC-->>JS: Success response
    JS->>M: Update UI + reload data
```

## 9. Security Considerations

### 9.1 Authorization Controls

-   All registration management endpoints require admin authentication
-   Admin actions are logged with user context and IP address
-   Registration data access is controlled through existing role-based system
-   CSV export functionality respects data privacy requirements

### 9.2 Input Validation

-   Registration status changes validated against allowed enum values
-   Admin notes sanitized to prevent XSS attacks
-   Event ID validation to prevent unauthorized access to other event data
-   Request size limits for bulk operations

### 9.3 Audit Trail

-   All registration status changes logged with before/after values
-   Admin note additions tracked with timestamps
-   Attendance marking creates permanent records
-   Export actions logged for compliance tracking

This design provides a comprehensive, integrated solution for admin event registration management while maintaining the existing system's architecture and user experience patterns.
