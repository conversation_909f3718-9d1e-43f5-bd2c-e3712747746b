using System.Collections.Generic;

namespace ParaHockeyApp.Models.ViewModels
{
    /// <summary>
    /// View model for member dashboard displaying member information and event subscriptions
    /// </summary>
    public class MemberDashboardViewModel
    {
        /// <summary>
        /// Member details
        /// </summary>
        public MemberDetailsViewModel Member { get; set; } = new MemberDetailsViewModel();

        /// <summary>
        /// Upcoming event registrations for this member
        /// </summary>
        public List<EventRegistrationViewModel> UpcomingEventRegistrations { get; set; } = new List<EventRegistrationViewModel>();

        /// <summary>
        /// Past event registrations for this member (limited to recent events)
        /// </summary>
        public List<EventRegistrationViewModel> PastEventRegistrations { get; set; } = new List<EventRegistrationViewModel>();

        /// <summary>
        /// Total number of upcoming events
        /// </summary>
        public int UpcomingEventsCount => UpcomingEventRegistrations.Count;

        /// <summary>
        /// Total number of past events shown
        /// </summary>
        public int PastEventsCount => PastEventRegistrations.Count;

        /// <summary>
        /// Whether the member has any event registrations
        /// </summary>
        public bool HasEventRegistrations => UpcomingEventsCount > 0 || PastEventsCount > 0;
    }
}