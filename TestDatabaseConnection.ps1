# Test database connection directly
Write-Host "🔍 TESTING DATABASE CONNECTION" -ForegroundColor Yellow
Write-Host "==============================" -ForegroundColor Yellow

$connectionString = "Server=SIMBA\SQLEXPRESS;User Id=ParaHockeyUser;Password=***************;Database=ParaHockeyDB_TEST;Encrypt=False;TrustServerCertificate=True;"

try {
    $connection = New-Object System.Data.SqlClient.SqlConnection
    $connection.ConnectionString = $connectionString
    
    Write-Host "`n📊 Testing connection to database..." -ForegroundColor Cyan
    $connection.Open()
    Write-Host "✅ Connection successful!" -ForegroundColor Green
    
    # Check if AdminUsers table exists
    $command = $connection.CreateCommand()
    $command.CommandText = "SELECT COUNT(*) FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'AdminUsers'"
    $result = $command.ExecuteScalar()
    
    if ($result -gt 0) {
        Write-Host "✅ AdminUsers table exists" -ForegroundColor Green
        
        # Check columns
        $command.CommandText = "SELECT COLUMN_NAME FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'AdminUsers' ORDER BY ORDINAL_POSITION"
        $reader = $command.ExecuteReader()
        Write-Host "`nColumns in AdminUsers table:" -ForegroundColor Yellow
        while ($reader.Read()) {
            Write-Host "  - $($reader['COLUMN_NAME'])" -ForegroundColor Cyan
        }
        $reader.Close()
    } else {
        Write-Host "❌ AdminUsers table does not exist!" -ForegroundColor Red
    }
    
    # Check if AdminTypes table exists
    $command.CommandText = "SELECT COUNT(*) FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'AdminTypes'"
    $result = $command.ExecuteScalar()
    
    if ($result -gt 0) {
        Write-Host "`n✅ AdminTypes table exists" -ForegroundColor Green
    } else {
        Write-Host "`n❌ AdminTypes table does not exist!" -ForegroundColor Red
    }
    
    # Check migration history
    $command.CommandText = "SELECT COUNT(*) FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = '__EFMigrationsHistory'"
    $result = $command.ExecuteScalar()
    
    if ($result -gt 0) {
        Write-Host "`n✅ Migration history table exists" -ForegroundColor Green
        
        # List applied migrations
        $command.CommandText = "SELECT MigrationId FROM __EFMigrationsHistory ORDER BY MigrationId"
        $reader = $command.ExecuteReader()
        Write-Host "`nApplied migrations:" -ForegroundColor Yellow
        while ($reader.Read()) {
            Write-Host "  - $($reader['MigrationId'])" -ForegroundColor Cyan
        }
        $reader.Close()
    } else {
        Write-Host "`n❌ Migration history table does not exist!" -ForegroundColor Red
    }
    
    $connection.Close()
} catch {
    Write-Host "❌ Connection failed: $_" -ForegroundColor Red
}

Write-Host "`n✅ Test complete!" -ForegroundColor Green