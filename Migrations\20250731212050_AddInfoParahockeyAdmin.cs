﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace ParaHockeyApp.Migrations
{
    /// <inheritdoc />
    public partial class AddInfoParahockeyAdmin : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            // Add <EMAIL> as Master Admin (AdminType = 9, AdminTypeId = 3)
            migrationBuilder.Sql(@"
                IF NOT EXISTS (SELECT 1 FROM AdminUsers WHERE Email = '<EMAIL>')
                BEGIN
                    INSERT INTO AdminUsers (Email, Name, AdminType, AdminTypeId, IsActive, DateCreated, DateModified, CreatedBySource, ModifiedBySource)
                    VALUES ('<EMAIL>', 'Info Parahockey', 9, 3, 1, GETUTCDATE(), GETUTCDATE(), 0, 0)
                    PRINT 'Added <EMAIL> as Master Admin'
                END
                ELSE
                BEGIN
                    PRINT '<EMAIL> already exists as admin'
                END
            ");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            // Remove <EMAIL> admin if it exists
            migrationBuilder.Sql(@"
                DELETE FROM AdminUsers WHERE Email = '<EMAIL>'
                PRINT 'Removed <EMAIL> admin'
            ");
        }
    }
}
