# Design Document

## Overview

The Member Excel Import system provides administrators with a comprehensive solution for importing member data from Excel files with sophisticated validation, duplicate detection, and resolution capabilities. The system follows a staged approach where all imported data is first placed in temporary tables for review and processing before being committed to the main Members table.

The design leverages the existing ParaHockey application architecture, including the BaseEntity audit system, existing services patterns, and the established security model. The system supports importing from multiple Excel files with relationship matching for parents and emergency contacts.

## Architecture

### High-Level Flow

```mermaid
graph TD
    A[Excel Upload] --> B[Parse & Normalize]
    B --> C[Validate Rows]
    C --> D[Duplicate Detection]
    D --> E[Stage to TempMembers]
    E --> F[Batch Summary]
    F --> G{Queue Type}
    G -->|NeedsFix| H[Fix & Create]
    G -->|ReadyToCreate| I[Bulk Create]
    G -->|Duplicate| J[Merge Resolution]
    H --> K[Update Members]
    I --> K
    J --> L[Confirmation Modal]
    L --> K
    K --> M[Audit Log]
```

### Database Schema Changes

#### New Tables

**TempMembers** - Staging table for imported member data
```sql
CREATE TABLE TempMembers (
    TempMemberId UNIQUEIDENTIFIER PRIMARY KEY DEFAULT NEWID(),
    ImportBatchId UNIQUEIDENTIFIER NOT NULL,
    
    -- Member fields (matching Members table structure)
    FirstName NVARCHAR(50) NOT NULL,
    LastName NVARCHAR(50) NOT NULL,
    DateOfBirth DATE,
    Email NVARCHAR(254),
    Phone NVARCHAR(20),
    Address NVARCHAR(200),
    City NVARCHAR(100),
    PostalCode NVARCHAR(10),
    
    -- Lookup field values (stored as text during import)
    GenderText NVARCHAR(50),
    ProvinceText NVARCHAR(50),
    PhoneTypeText NVARCHAR(50),
    RegistrationTypeText NVARCHAR(50),
    
    -- Resolved lookup IDs (set during validation)
    GenderId INT,
    ProvinceId INT,
    PhoneTypeId INT,
    RegistrationTypeId INT,
    
    -- Duplicate detection
    ExistingMemberId INT NULL,
    
    -- Processing status
    Status NVARCHAR(20) NOT NULL, -- Imported, NeedsFix, Duplicate, ReadyToCreate, Merged, Created, Rejected
    ValidationErrorsJson NVARCHAR(MAX),
    RawSourceJson NVARCHAR(MAX) NOT NULL,
    
    -- Relationship data
    ParentData NVARCHAR(MAX), -- JSON for parent information
    EmergencyContactData NVARCHAR(MAX), -- JSON for emergency contact
    
    -- Audit fields
    CreatedAtUtc DATETIME2 NOT NULL DEFAULT GETUTCDATE(),
    CreatedBy NVARCHAR(100) NOT NULL,
    LastUpdatedAtUtc DATETIME2,
    LastUpdatedBy NVARCHAR(100),
    
    FOREIGN KEY (ImportBatchId) REFERENCES MemberImportBatches(ImportBatchId),
    FOREIGN KEY (ExistingMemberId) REFERENCES Members(Id)
);
```

**MemberImportBatches** - Tracks import operations
```sql
CREATE TABLE MemberImportBatches (
    ImportBatchId UNIQUEIDENTIFIER PRIMARY KEY DEFAULT NEWID(),
    FileName NVARCHAR(255) NOT NULL,
    UploadedBy NVARCHAR(100) NOT NULL,
    UploadedAtUtc DATETIME2 NOT NULL DEFAULT GETUTCDATE(),
    
    -- Processing statistics
    TotalRows INT NOT NULL DEFAULT 0,
    CreatedCount INT NOT NULL DEFAULT 0,
    DuplicateCount INT NOT NULL DEFAULT 0,
    NeedsFixCount INT NOT NULL DEFAULT 0,
    MergedCount INT NOT NULL DEFAULT 0,
    RejectedCount INT NOT NULL DEFAULT 0,
    
    -- Configuration used for this import
    ConfigurationJson NVARCHAR(MAX),
    
    -- Status
    Status NVARCHAR(20) NOT NULL DEFAULT 'Processing', -- Processing, Completed, Failed
    ErrorMessage NVARCHAR(MAX)
);
```

#### Indexes for Performance

```sql
-- TempMembers indexes
CREATE INDEX IX_TempMembers_ImportBatchId ON TempMembers(ImportBatchId);
CREATE INDEX IX_TempMembers_Status ON TempMembers(Status);
CREATE INDEX IX_TempMembers_ExistingMemberId ON TempMembers(ExistingMemberId);
CREATE INDEX IX_TempMembers_Email ON TempMembers(Email);
CREATE INDEX IX_TempMembers_Name_DOB ON TempMembers(LastName, FirstName, DateOfBirth);

-- MemberImportBatches indexes
CREATE INDEX IX_MemberImportBatches_UploadedBy ON MemberImportBatches(UploadedBy);
CREATE INDEX IX_MemberImportBatches_UploadedAtUtc ON MemberImportBatches(UploadedAtUtc);
```

## Components and Interfaces

### Services Layer

#### IMemberImportService
```csharp
public interface IMemberImportService
{
    Task<Guid> ParseAndStageAsync(IFormFile file, string uploadedBy);
    Task<ImportBatchSummary> GetBatchSummaryAsync(Guid batchId);
    Task<List<TempMember>> GetQueueAsync(Guid batchId, TempMemberStatus status);
    Task<bool> ValidateFileAsync(IFormFile file);
}
```

#### ITempMemberService
```csharp
public interface ITempMemberService
{
    Task<TempMember> GetByIdAsync(Guid tempMemberId);
    Task<Member> CreateFromTempAsync(Guid tempMemberId, string createdBy);
    Task<List<Member>> BulkCreateFromTempAsync(List<Guid> tempMemberIds, string createdBy);
    Task UpdateStatusAsync(Guid tempMemberId, TempMemberStatus status);
    Task DeleteAsync(Guid tempMemberId);
}
```

#### IDuplicateDetectionService
```csharp
public interface IDuplicateDetectionService
{
    Task<Member?> FindExistingMemberAsync(TempMember tempMember);
    Task<DuplicateResolutionPreview> PreviewMergeAsync(Guid tempMemberId, Dictionary<string, string> fieldChoices);
    Task<Member> ApplyMergeAsync(Guid tempMemberId, Dictionary<string, string> fieldChoices, string performedBy);
}
```

#### INormalizationService
```csharp
public interface INormalizationService
{
    string NormalizeEmail(string email);
    string NormalizePhone(string phone);
    string NormalizePostalCode(string postalCode);
    string NormalizeName(string name);
    TempMember NormalizeTempMember(TempMember tempMember);
}
```

### Controllers

#### ImportController
- `GET /Import/Members` - Upload page
- `POST /Import/Members` - Process upload
- `GET /Import/Batch/{id}` - Batch summary

#### TempMembersController
- `GET /TempMembers/NeedsFix` - Queue for invalid records
- `GET /TempMembers/ReadyToCreate` - Queue for valid records
- `GET /TempMembers/Duplicates` - Queue for duplicates
- `GET /TempMembers/Resolve/{id}` - Merge resolution page
- `POST /TempMembers/Resolve/{id}` - Preview merge
- `POST /TempMembers/ConfirmMerge/{id}` - Apply merge
- `POST /TempMembers/CreateSelected` - Bulk create
- `POST /TempMembers/Create/{id}` - Create single member

### View Models

#### ImportBatchSummary
```csharp
public class ImportBatchSummary
{
    public Guid ImportBatchId { get; set; }
    public string FileName { get; set; }
    public DateTime UploadedAtUtc { get; set; }
    public string UploadedBy { get; set; }
    public int TotalRows { get; set; }
    public int CreatedCount { get; set; }
    public int DuplicateCount { get; set; }
    public int NeedsFixCount { get; set; }
    public int MergedCount { get; set; }
    public int RejectedCount { get; set; }
    public string Status { get; set; }
}
```

#### DuplicateResolutionViewModel
```csharp
public class DuplicateResolutionViewModel
{
    public TempMember TempMember { get; set; }
    public Member ExistingMember { get; set; }
    public Dictionary<string, FieldComparison> FieldComparisons { get; set; }
    public Dictionary<string, string> SelectedChoices { get; set; }
}

public class FieldComparison
{
    public string FieldName { get; set; }
    public string TempValue { get; set; }
    public string ExistingValue { get; set; }
    public bool AreIdentical { get; set; }
    public string SelectedValue { get; set; }
}
```

## Data Models

### TempMember Entity
```csharp
public class TempMember : BaseEntity
{
    public Guid TempMemberId { get; set; }
    public Guid ImportBatchId { get; set; }
    
    // Member fields
    public string FirstName { get; set; }
    public string LastName { get; set; }
    public DateTime? DateOfBirth { get; set; }
    public string Email { get; set; }
    public string Phone { get; set; }
    public string Address { get; set; }
    public string City { get; set; }
    public string PostalCode { get; set; }
    
    // Lookup text values
    public string GenderText { get; set; }
    public string ProvinceText { get; set; }
    public string PhoneTypeText { get; set; }
    public string RegistrationTypeText { get; set; }
    
    // Resolved lookup IDs
    public int? GenderId { get; set; }
    public int? ProvinceId { get; set; }
    public int? PhoneTypeId { get; set; }
    public int? RegistrationTypeId { get; set; }
    
    // Processing fields
    public int? ExistingMemberId { get; set; }
    public TempMemberStatus Status { get; set; }
    public string ValidationErrorsJson { get; set; }
    public string RawSourceJson { get; set; }
    
    // Relationship data
    public string ParentData { get; set; }
    public string EmergencyContactData { get; set; }
    
    // Navigation properties
    public virtual MemberImportBatch ImportBatch { get; set; }
    public virtual Member ExistingMember { get; set; }
}
```

### Enums
```csharp
public enum TempMemberStatus
{
    Imported,
    NeedsFix,
    Duplicate,
    ReadyToCreate,
    Merged,
    Created,
    Rejected
}
```

## Error Handling

### Validation Errors
- Store field-level validation errors in JSON format
- Display errors inline on forms
- Provide clear, actionable error messages
- Support multiple errors per field

### Processing Errors
- Wrap operations in transactions
- Rollback on failure
- Log detailed error information
- Provide user-friendly error messages

### File Processing Errors
- Validate file format and size before processing
- Handle corrupted or malformed Excel files
- Provide progress feedback for large files
- Support cancellation of long-running operations

## Testing Strategy

### Unit Tests
- Service layer methods for import processing
- Validation logic for member data
- Duplicate detection algorithms
- Normalization functions
- Merge resolution logic

### Integration Tests
- End-to-end import workflow
- Database operations and transactions
- File upload and processing
- Audit logging integration

### Test Data
- Sample Excel files with various scenarios:
  - Valid complete data
  - Missing required fields
  - Duplicate members
  - Invalid data formats
  - Large files (performance testing)
  - Multiple relationship files

### Test Scenarios
1. **Valid Import**: Upload file with clean data, verify all members created
2. **Validation Errors**: Upload file with missing fields, verify NeedsFix queue
3. **Duplicate Detection**: Upload file with existing member data, verify Duplicate queue
4. **Merge Resolution**: Resolve duplicate with field selection, verify merge applied
5. **Bulk Operations**: Create multiple members from ReadyToCreate queue
6. **Relationship Matching**: Import with parent/contact data, verify relationships
7. **Audit Trail**: Verify all operations are logged correctly
8. **Concurrency**: Test multiple admins working on same batch
9. **Performance**: Test with maximum file size and row limits
10. **Error Recovery**: Test rollback scenarios and error handling

### Security Testing
- Verify admin-only access to all import functions
- Test CSRF protection on forms
- Validate input sanitization
- Test file upload security (malicious files)
- Verify audit logging captures all actions

## Implementation Notes

### Excel Processing
- Use ClosedXML library (MIT license) for Excel file parsing
- Support flexible column mapping with header detection
- Handle merged cells and formatting variations
- Stream processing for large files to avoid memory issues

### Performance Considerations
- Process imports in batches (500 rows per transaction)
- Use bulk insert operations where possible
- Implement progress tracking for large imports
- Add database indexes for common query patterns

### Concurrency Handling
- Lock temp records during merge operations
- Use optimistic concurrency for batch updates
- Prevent multiple admins from editing same temp record
- Handle concurrent access to import queues

### Multi-File Import Strategy
- Process files sequentially to maintain relationship integrity
- Match members to parents/contacts using normalized identifiers
- Flag unmatched relationships for manual review
- Support different file formats for different entity types

This design provides a robust, scalable solution for member import functionality while maintaining consistency with the existing ParaHockey application architecture and patterns.