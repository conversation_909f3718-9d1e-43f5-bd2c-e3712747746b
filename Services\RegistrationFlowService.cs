using Microsoft.Extensions.Localization;
using ParaHockeyApp.Models.Entities;
using ParaHockeyApp.Resources;

namespace ParaHockeyApp.Services
{
    /// <summary>
    /// Service for managing the registration flow based on age and other business rules
    /// </summary>
    public class RegistrationFlowService : IRegistrationFlowService
    {
        private readonly IEnvironmentConfigurationService _environmentConfig;
        private readonly IStringLocalizer<SharedResource> _localizer;

        public RegistrationFlowService(
            IEnvironmentConfigurationService environmentConfig,
            IStringLocalizer<SharedResource> localizer)
        {
            _environmentConfig = environmentConfig;
            _localizer = localizer;
        }

        /// <summary>
        /// Determines if a person is under the age of majority based on their date of birth
        /// </summary>
        public bool IsUnderAge(DateTime dateOfBirth)
        {
            return CalculateAge(dateOfBirth) < GetAgeOfMajority();
        }

        /// <summary>
        /// Calculates the age of a person based on their date of birth
        /// </summary>
        public int CalculateAge(DateTime dateOfBirth)
        {
            var today = DateTime.Today;
            var age = today.Year - dateOfBirth.Year;
            
            // Go back to the previous year if we haven't passed the birthday this year
            if (dateOfBirth.Date > today.AddYears(-age))
            {
                age--;
            }
            
            return age;
        }

        /// <summary>
        /// Determines if a specific membership type is allowed for a person based on their date of birth
        /// </summary>
        public bool IsMembershipTypeAllowed(string membershipType, DateTime dateOfBirth)
        {
            if (string.IsNullOrWhiteSpace(membershipType))
            {
                return false;
            }

            // Junior membership is only allowed for those under the age of majority
            if (membershipType.Equals("Junior", StringComparison.OrdinalIgnoreCase))
            {
                return IsUnderAge(dateOfBirth);
            }

            // All other membership types are allowed for everyone
            return true;
        }

        /// <summary>
        /// Gets the next form step in the registration flow after DOB entry
        /// </summary>
        public string GetNextFormStep(DateTime dateOfBirth)
        {
            return IsUnderAge(dateOfBirth) ? "Parent" : "EmergencyContact";
        }

        /// <summary>
        /// Gets a localized explanation for why a membership type is disabled
        /// </summary>
        public string GetMembershipTypeDisabledReason(string membershipType, DateTime dateOfBirth)
        {
            if (membershipType.Equals("Junior", StringComparison.OrdinalIgnoreCase) && !IsUnderAge(dateOfBirth))
            {
                var age = CalculateAge(dateOfBirth);
                var ageOfMajority = GetAgeOfMajority();
                
                // Return localized message explaining that Junior membership is only for those under the age of majority
                return _localizer["JuniorMembershipAgeRestriction", ageOfMajority].Value;
            }

            return string.Empty;
        }

        /// <summary>
        /// Gets the configured age of majority for the current environment
        /// </summary>
        public int GetAgeOfMajority()
        {
            try
            {
                return _environmentConfig.GetAgeOfMajority();
            }
            catch
            {
                // Fallback to default age of majority if configuration fails
                return 18;
            }
        }
    }
}