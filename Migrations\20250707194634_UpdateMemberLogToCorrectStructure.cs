﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace ParaHockeyApp.Migrations
{
    /// <inheritdoc />
    public partial class UpdateMemberLogToCorrectStructure : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            // Defensive: Drop DateCreated column only if it exists
            migrationBuilder.Sql(@"
                IF EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('MemberLogs') AND name = 'DateCreated')
                BEGIN
                    ALTER TABLE MemberLogs DROP COLUMN DateCreated
                END");

            // Defensive: Drop UserEmail column only if it exists
            migrationBuilder.Sql(@"
                IF EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('MemberLogs') AND name = 'UserEmail')
                BEGIN
                    ALTER TABLE MemberLogs DROP COLUMN UserEmail
                END");

            // Defensive: Rename column only if source exists and target doesn't exist
            migrationBuilder.Sql(@"
                IF EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('MemberLogs') AND name = 'EditorNum')
                AND NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('MemberLogs') AND name = 'EditorId')
                BEGIN
                    EXEC sp_rename 'MemberLogs.EditorNum', 'EditorId', 'COLUMN'
                END");

            // Defensive: Rename index only if source exists and target doesn't exist
            migrationBuilder.Sql(@"
                IF EXISTS (SELECT * FROM sys.indexes WHERE object_id = OBJECT_ID('MemberLogs') AND name = 'IX_MemberLogs_EditorNum')
                AND NOT EXISTS (SELECT * FROM sys.indexes WHERE object_id = OBJECT_ID('MemberLogs') AND name = 'IX_MemberLogs_EditorId')
                BEGIN
                    EXEC sp_rename 'MemberLogs.IX_MemberLogs_EditorNum', 'IX_MemberLogs_EditorId', 'INDEX'
                END");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.RenameColumn(
                name: "EditorId",
                table: "MemberLogs",
                newName: "EditorNum");

            migrationBuilder.RenameIndex(
                name: "IX_MemberLogs_EditorId",
                table: "MemberLogs",
                newName: "IX_MemberLogs_EditorNum");

            migrationBuilder.AddColumn<DateTime>(
                name: "DateCreated",
                table: "MemberLogs",
                type: "datetime2",
                nullable: false,
                defaultValue: new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified));

            migrationBuilder.AddColumn<string>(
                name: "UserEmail",
                table: "MemberLogs",
                type: "nvarchar(254)",
                maxLength: 254,
                nullable: false,
                defaultValue: "");
        }
    }
}
