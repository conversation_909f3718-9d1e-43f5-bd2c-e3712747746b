using Xunit;
using FluentAssertions;
using ParaHockey.E2E.Tests.Infrastructure;
using ParaHockey.E2E.Tests.PageObjects;
using OpenQA.Selenium;

namespace ParaHockey.E2E.Tests.Tests
{
    public class LocalizationTests : BaseTest, IDisposable
    {
        private readonly RegistrationPage _registrationPage;
        private readonly HomePage _homePage;

        public LocalizationTests()
        {
            _registrationPage = new RegistrationPage(Driver, Wait);
            _homePage = new HomePage(Driver, Wait);
        }

        [Fact]
        public void LanguageSwitching_ShouldChangePageContent()
        {
            try
            {
                // Arrange
                NavigateToHomePage();

                // Act & Assert - Switch to English
                var originalTitle = _homePage.GetWelcomeTitle();
                _homePage.SwitchToEnglish();
                Thread.Sleep(2000);

                var englishTitle = _homePage.GetWelcomeTitle();
                englishTitle.Should().NotBe(originalTitle, "Title should change when switching languages");
                englishTitle.Should().Contain("Welcome", "English title should contain 'Welcome'");

                // Switch back to French
                _homePage.SwitchToFrench();
                Thread.Sleep(2000);

                var frenchTitle = _homePage.GetWelcomeTitle();
                frenchTitle.Should().NotBe(englishTitle, "Title should change back when switching to French");
                frenchTitle.Should().Contain("Bienvenue", "French title should contain 'Bienvenue'");
            }
            catch (Exception ex)
            {
                TakeScreenshot("LanguageSwitching_ShouldChangePageContent");
                throw;
            }
        }

        [Fact]
        public void RegistrationFormLanguageSwitching_ShouldPreserveFormData()
        {
            try
            {
                // Arrange
                NavigateToRegistrationPage();

                // Fill form with test data
                var testData = new
                {
                    FirstName = "Jean",
                    LastName = "Dupont",
                    Email = "<EMAIL>",
                    Phone = "(*************",
                    PostalCode = "H3B 2Y5"
                };

                _registrationPage.FillBasicInformation(testData.FirstName, testData.LastName, TestData.AdultBirthDate, "male");
                _registrationPage.FillAddress("123 Rue Test", "Montreal", "QC", testData.PostalCode);
                _registrationPage.FillContactInformation(testData.Phone, "mobile", testData.Email);

                // Act - Switch language
                _registrationPage.SwitchToEnglish();
                Thread.Sleep(3000); // Wait for page reload and data restoration

                // Assert - Form data should be preserved
                _registrationPage.GetFirstNameValue().Should().Be(testData.FirstName, "First name should be preserved after language switch");
                _registrationPage.GetLastNameValue().Should().Be(testData.LastName, "Last name should be preserved after language switch");
                _registrationPage.GetEmailValue().Should().Be(testData.Email, "Email should be preserved after language switch");
                _registrationPage.GetPhoneValue().Should().Be(testData.Phone, "Phone should be preserved after language switch");
                _registrationPage.GetPostalCodeValue().Should().Be(testData.PostalCode, "Postal code should be preserved after language switch");
            }
            catch (Exception ex)
            {
                TakeScreenshot("RegistrationFormLanguageSwitching_ShouldPreserveFormData");
                throw;
            }
        }

        [Fact]
        public void RegistrationTypeLanguageSwitching_ShouldPreserveSelection()
        {
            try
            {
                // Arrange
                NavigateToRegistrationPage();

                // Select Junior registration type
                _registrationPage.SelectRegistrationType("Junior");
                Thread.Sleep(2000);

                // Verify parent section is visible
                _registrationPage.IsParentSectionVisible().Should().BeTrue("Parent section should be visible for Junior");

                // Act - Switch language
                _registrationPage.SwitchToEnglish();
                Thread.Sleep(3000);

                // Assert - Registration type selection should be preserved
                var juniorRadio = Driver.FindElement(By.Id("reg-1"));
                juniorRadio.Selected.Should().BeTrue("Junior registration type should remain selected after language switch");
                
                // Dynamic fields should still be visible
                _registrationPage.IsParentSectionVisible().Should().BeTrue("Parent section should still be visible after language switch");
            }
            catch (Exception ex)
            {
                TakeScreenshot("RegistrationTypeLanguageSwitching_ShouldPreserveSelection");
                throw;
            }
        }

        [Theory]
        [InlineData("French", "Français")]
        [InlineData("English", "English")]
        public void LanguageDropdown_ShouldDisplayCorrectOptions(string expectedLanguage, string expectedText)
        {
            try
            {
                // Arrange
                NavigateToHomePage();

                // Act
                var languageDropdown = Driver.FindElement(By.Id("languageDropdown"));
                languageDropdown.Click();
                Thread.Sleep(1000);

                // Assert
                var languageLinks = Driver.FindElements(By.CssSelector(".dropdown-menu a"));
                var languageTexts = languageLinks.Select(link => link.Text).ToList();
                
                languageTexts.Should().Contain(expectedText, $"Language dropdown should contain '{expectedText}' option");
            }
            catch (Exception ex)
            {
                TakeScreenshot($"LanguageDropdown_ShouldDisplayCorrectOptions_{expectedLanguage}");
                throw;
            }
        }

        [Fact]
        public void FormLabels_ShouldBeTranslated()
        {
            try
            {
                // Arrange
                NavigateToRegistrationPage();

                // Get French labels
                var frenchLabels = GetFormLabels();

                // Act - Switch to English
                _registrationPage.SwitchToEnglish();
                Thread.Sleep(2000);

                // Get English labels
                var englishLabels = GetFormLabels();

                // Assert - Labels should be different
                englishLabels.Should().NotBeEquivalentTo(frenchLabels, "Form labels should be translated when language changes");
                
                // Check specific translations
                englishLabels.Should().Contain(label => label.Contains("First Name"), "Should have 'First Name' label in English");
                englishLabels.Should().Contain(label => label.Contains("Last Name"), "Should have 'Last Name' label in English");
                englishLabels.Should().Contain(label => label.Contains("Email"), "Should have 'Email' label in English");
            }
            catch (Exception ex)
            {
                TakeScreenshot("FormLabels_ShouldBeTranslated");
                throw;
            }
        }

        [Fact]
        public void ValidationMessages_ShouldBeLocalized()
        {
            try
            {
                // Arrange
                NavigateToRegistrationPage();

                // Act - Submit empty form to trigger validation in French
                _registrationPage.SubmitForm();
                Thread.Sleep(2000);

                var frenchValidationMessages = GetValidationMessages();
                frenchValidationMessages.Should().NotBeEmpty("Should have validation messages in French");

                // Switch to English and trigger validation again
                _registrationPage.SwitchToEnglish();
                Thread.Sleep(2000);
                _registrationPage.SubmitForm();
                Thread.Sleep(2000);

                var englishValidationMessages = GetValidationMessages();

                // Assert
                englishValidationMessages.Should().NotBeEmpty("Should have validation messages in English");
                englishValidationMessages.Should().NotBeEquivalentTo(frenchValidationMessages, "Validation messages should be translated");
            }
            catch (Exception ex)
            {
                TakeScreenshot("ValidationMessages_ShouldBeLocalized");
                throw;
            }
        }

        [Fact]
        public void DateFormat_ShouldBeLocalizedInPlaceholder()
        {
            try
            {
                // Arrange
                NavigateToRegistrationPage();

                // Get French date placeholder
                var dateField = Driver.FindElement(By.Name("DateOfBirth"));
                var frenchPlaceholder = dateField.GetAttribute("placeholder");

                // Act - Switch to English
                _registrationPage.SwitchToEnglish();
                Thread.Sleep(2000);

                // Get English date placeholder
                dateField = Driver.FindElement(By.Name("DateOfBirth"));
                var englishPlaceholder = dateField.GetAttribute("placeholder");

                // Assert
                frenchPlaceholder.Should().NotBe(englishPlaceholder, "Date format placeholder should change with language");
                
                // Both should contain date format indicators
                englishPlaceholder.Should().MatchRegex(@".*\d{4}.*\d{2}.*\d{2}.*", "English placeholder should contain date format");
                frenchPlaceholder.Should().MatchRegex(@".*\d{4}.*\d{2}.*\d{2}.*", "French placeholder should contain date format");
            }
            catch (Exception ex)
            {
                TakeScreenshot("DateFormat_ShouldBeLocalizedInPlaceholder");
                throw;
            }
        }

        [Fact]
        public void ProvinceDropdown_ShouldBeTranslated()
        {
            try
            {
                // Arrange
                NavigateToRegistrationPage();

                // Get French province options
                var provinceDropdown = Driver.FindElement(By.Name("ProvinceId"));
                var frenchOptions = GetDropdownOptions(provinceDropdown);

                // Act - Switch to English
                _registrationPage.SwitchToEnglish();
                Thread.Sleep(2000);

                // Get English province options
                provinceDropdown = Driver.FindElement(By.Name("ProvinceId"));
                var englishOptions = GetDropdownOptions(provinceDropdown);

                // Assert
                englishOptions.Should().NotBeEquivalentTo(frenchOptions, "Province options should be translated");
                
                // Check for specific provinces that should be translated
                englishOptions.Should().Contain(opt => opt.Contains("Quebec"), "Should have 'Quebec' in English");
                frenchOptions.Should().Contain(opt => opt.Contains("Québec"), "Should have 'Québec' in French");
            }
            catch (Exception ex)
            {
                TakeScreenshot("ProvinceDropdown_ShouldBeTranslated");
                throw;
            }
        }

        [Fact]
        public void RegistrationTypeLabels_ShouldBeTranslated()
        {
            try
            {
                // Arrange
                NavigateToRegistrationPage();

                // Get French registration type labels
                var frenchRegTypes = GetRegistrationTypeLabels();

                // Act - Switch to English
                _registrationPage.SwitchToEnglish();
                Thread.Sleep(2000);

                // Get English registration type labels
                var englishRegTypes = GetRegistrationTypeLabels();

                // Assert
                englishRegTypes.Should().NotBeEquivalentTo(frenchRegTypes, "Registration type labels should be translated");
                
                // Check specific translations
                englishRegTypes.Should().Contain(label => label.Contains("Junior"), "Should have 'Junior' in English");
                englishRegTypes.Should().Contain(label => label.Contains("Development"), "Should have 'Development' in English");
                englishRegTypes.Should().Contain(label => label.Contains("Coach"), "Should have 'Coach' in English");
                englishRegTypes.Should().Contain(label => label.Contains("Volunteer"), "Should have 'Volunteer' in English");
            }
            catch (Exception ex)
            {
                TakeScreenshot("RegistrationTypeLabels_ShouldBeTranslated");
                throw;
            }
        }

        [Fact]
        public void BrandName_ShouldBeConsistentAcrossLanguages()
        {
            try
            {
                // Arrange
                NavigateToHomePage();

                // Get brand name in French
                var frenchBrandElements = Driver.FindElements(By.XPath("//*[contains(text(), 'Parahockey')]"));
                frenchBrandElements.Should().NotBeEmpty("Should have 'Parahockey' brand name in French");

                // Act - Switch to English
                _homePage.SwitchToEnglish();
                Thread.Sleep(2000);

                // Get brand name in English
                var englishBrandElements = Driver.FindElements(By.XPath("//*[contains(text(), 'Parahockey')]"));

                // Assert
                englishBrandElements.Should().NotBeEmpty("Should have 'Parahockey' brand name in English");
                
                // Brand name should be consistent (same spelling)
                var frenchBrandTexts = frenchBrandElements.Select(el => el.Text).Where(text => text.Contains("Parahockey"));
                var englishBrandTexts = englishBrandElements.Select(el => el.Text).Where(text => text.Contains("Parahockey"));
                
                foreach (var brandText in englishBrandTexts)
                {
                    brandText.Should().Contain("Parahockey", "Brand name should be spelled consistently across languages");
                }
            }
            catch (Exception ex)
            {
                TakeScreenshot("BrandName_ShouldBeConsistentAcrossLanguages");
                throw;
            }
        }

        // Helper methods
        private List<string> GetFormLabels()
        {
            var labels = Driver.FindElements(By.CssSelector("label"));
            return labels.Select(label => label.Text).Where(text => !string.IsNullOrWhiteSpace(text)).ToList();
        }

        private List<string> GetValidationMessages()
        {
            var validationElements = Driver.FindElements(By.CssSelector(".text-danger"));
            return validationElements.Select(el => el.Text).Where(text => !string.IsNullOrWhiteSpace(text)).ToList();
        }

        private List<string> GetDropdownOptions(IWebElement dropdown)
        {
            var options = dropdown.FindElements(By.TagName("option"));
            return options.Select(option => option.Text).Where(text => !string.IsNullOrWhiteSpace(text)).ToList();
        }

        private List<string> GetRegistrationTypeLabels()
        {
            var regTypeLabels = Driver.FindElements(By.CssSelector(".registration-types label"));
            return regTypeLabels.Select(label => label.Text).Where(text => !string.IsNullOrWhiteSpace(text)).ToList();
        }

        public new void Dispose()
        {
            base.Dispose();
        }
    }
}