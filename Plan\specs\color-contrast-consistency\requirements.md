# Requirements Document

## Introduction
Some users customise their operating-system or browser colour settings (high-contrast mode, forced dark mode, custom themes). When this happens, parts of our ParaHockey web app can turn unreadable (e.g., black text on black backgrounds).  
This feature guarantees that all pages remain legible and visually consistent, regardless of user colour overrides, while preserving our multilingual support and responsive design.

## Requirement 1 — Baseline Readability
**User Story:** As a visitor, I want every page to stay readable even if my browser or OS overrides default colours.

**Acceptance Criteria**
1. WHEN the site is viewed in standard, dark, or high-contrast modes in Chrome, Edge, Firefox, and Safari  
   THEN all text/background combos SHALL meet WCAG AA contrast (≥ 4.5 : 1 normal text, ≥ 3 : 1 large).  
2. WHEN a user enables Windows “High Contrast” or a custom theme  
   THEN no text SHALL appear on an identically-coloured background.  
3. WHEN automated accessibility audits (axe-core, Lighthouse) are run  
   THEN they SHALL report zero contrast errors across all pages.

## Requirement 2 — Adaptive Colour-Scheme Support
**User Story:** As a visitor who uses dark mode, I want the site to adopt a matching dark palette.

**Acceptance Criteria**
1. WHEN `prefers-color-scheme: dark` is detected  
   THEN the site SHALL load a dark-mode theme based on project design tokens.  
2. WHEN the scheme changes at runtime  
   THEN colours SHALL update without a reload.  
3. WHEN the user forces a light theme  
   THEN the site SHALL respect it.

## Requirement 3 — Forced-Colours / High-Contrast Compatibility
**User Story:** As a visually-impaired user using Windows High Contrast or `forced-colors` mode, I want all UI elements to stay visible.

**Acceptance Criteria**
1. WHEN `@media (forced-colors: active)` is active  
   THEN focus outlines, borders, and form inputs SHALL inherit system colours.  
2. WHEN interactive elements render in forced-colours  
   THEN their shapes and focus styles remain distinguishable.  
3. WHEN automated tests simulate forced-colours  
   THEN no element SHALL rely solely on colour for meaning.

## Requirement 4 — Theming Infrastructure
**User Story:** As a developer, I want a single colour source-of-truth so future changes propagate to light, dark, and high-contrast variants.

**Acceptance Criteria**
1. New colours SHALL be added as CSS custom properties in `wwwroot/css/shared/variables.css`.  
2. Switching themes SHALL involve only variable value changes.  
3. Build SHALL output light and dark bundles without markup duplication.

## Requirement 5 — Testing & Tooling
**User Story:** As QA, I want automated tests that flag contrast regressions.

**Acceptance Criteria**
1. E2E suite runs axe-core scans in light and dark modes and fails on any contrast error.  
2. Colour-regression tests run when colour variables change in a PR.  
3. Manual smoke tests verify readability in:  
   • Standard theme  • Dark mode  • Windows High Contrast  • macOS “Increase Contrast”. 