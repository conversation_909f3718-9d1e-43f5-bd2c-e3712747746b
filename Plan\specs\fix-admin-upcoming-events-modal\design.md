# Design – Admin Upcoming Events Modal Fix

## Overview
The admin calendar view (`Views/Admin/Calendar.cshtml`) already instantiates an **Admin Event Details Modal** and connects it to clicks on FullCalendar events through JavaScript initialisation (likely in `site.js` or dedicated script). However, the "Upcoming Events" sidebar list currently renders each event as plain static markup without click handlers, so the modal is never triggered. In contrast, the public-facing calendar uses a different JS module (`event-details-modal.js`) bound to its sidebar which opens the **Public Event Details Modal**.

Our goal is to attach the existing admin modal logic to the sidebar entries while avoiding code duplication and ensuring role-based security.

## Current Components

1. **View:** `Views/Admin/Calendar.cshtml`
   * Renders FullCalendar grid (#calendar) and Upcoming Events sidebar `.event-item` list.
2. **Modal Markup:** Admin modal HTML is included in `Calendar.cshtml` (near the bottom, id likely `#adminEventModal`).
3. **JS Logic:** Event click callback in `site.js` (`initAdminCalendar()` function) fetches event details from `/Admin/GetEventDetails/{id}` and injects them into the modal.

## Proposed Changes

### Frontend
1. **Add Clickable Markup**
   * Wrap each upcoming event item with `<a href="#" class="upcoming-event-link" data-event-id="@evt.Id"> … </a>` so it can receive focus and clicks.
2. **JS Behaviour**
   * Extend or reuse existing `initAdminCalendar()` by adding a delegated click listener on `.upcoming-event-link` elements:
     ```js
     document.addEventListener('click', function(e){
         const link = e.target.closest('.upcoming-event-link');
         if (!link) return;
         e.preventDefault();
         const id = link.dataset.eventId;
         showAdminEventModal(id); // existing function
     });
     ```
   * `showAdminEventModal(id)` already exists (confirmation required). If not, extract logic from FullCalendar eventClick handler into reusable function.
3. **Accessibility**
   * Ensure links have `role="button"` and `tabindex="0"`. Add keyboard `keydown` listener for **Enter/Space**.

### Backend
No changes to controller logic are required if endpoint `/Admin/GetEventDetails/{id}` already exists. Confirm permissions to ensure only admins can access.

### Localization
Any new text (tooltips, aria-labels) must be added to `SharedResource.resx` and `SharedResource.en-CA.resx`.

## Key Decisions
* **Delegated Listener** vs inline `onclick`: delegated listener keeps markup clean and works for dynamically refreshed lists.
* **Reuse Existing Modal Function**: avoids maintenance overhead by centralising event detail rendering in one place.

## Risks & Mitigations
| Risk | Impact | Likelihood | Mitigation |
|------|--------|------------|-----------|
| Click handler duplication causes double modal open | UI glitch | Low | Ensure single unified `showAdminEventModal` function |
| Sidebar markup change affects CSS layout | Medium | Low | Test responsive design after change |
| Accessibility regression | Compliance issue | Medium | Medium | Add keyboard & ARIA support, test with screen reader |
| Performance hit from extra DOM listeners | Low | Low | Use delegated listener on sidebar parent |

## Alternatives Considered
1. **Server-side Link to Edit Page** – simpler but loses modal UX, slower context switch.
2. **Duplicate Public Modal Script** – quick fix but increases code duplication and maintenance cost.

Chosen approach: **Delegated listener that reuses existing admin modal logic** for maintainability and consistency. 