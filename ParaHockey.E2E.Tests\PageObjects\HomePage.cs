using OpenQA.Selenium;
using OpenQA.Selenium.Support.UI;
using ParaHockey.E2E.Tests.Infrastructure;

namespace ParaHockey.E2E.Tests.PageObjects
{
    public class HomePage
    {
        private readonly IWebDriver _driver;
        private readonly WebDriverWait _wait;

        public HomePage(IWebDriver driver, WebDriverWait wait)
        {
            _driver = driver;
            _wait = wait;
        }

        // Page Elements
        private IWebElement Logo => _driver.FindElement(By.CssSelector(".hero-logo"));
        private IWebElement WelcomeTitle => _driver.FindElement(By.CssSelector(".hero-title"));
        private IWebElement RegisterNowButton => _driver.FindElement(By.CssSelector("a[href*='Register']"));
        private IWebElement LanguageDropdown => _driver.FindElement(By.Id("languageDropdown"));
        private IWebElement FrenchLanguageLink => _driver.FindElement(By.PartialLinkText("Français"));
        private IWebElement EnglishLanguageLink => _driver.FindElement(By.PartialLinkText("English"));

        // Statistics elements
        private IWebElement ActivePlayersCount => _driver.FindElement(By.XPath("//div[contains(text(), 'Active Players')]/preceding-sibling::div"));
        private IWebElement TeamsCount => _driver.FindElement(By.XPath("//div[contains(text(), 'Teams')]/preceding-sibling::div"));
        private IWebElement CoachesCount => _driver.FindElement(By.XPath("//div[contains(text(), 'Coaches')]/preceding-sibling::div"));
        private IWebElement VolunteersCount => _driver.FindElement(By.XPath("//div[contains(text(), 'Volunteers')]/preceding-sibling::div"));

        // Registration type cards
        private IWebElement JuniorCard => _driver.FindElement(By.XPath("//h4[contains(text(), 'Junior')]/parent::div"));
        private IWebElement DevelopmentCard => _driver.FindElement(By.XPath("//h4[contains(text(), 'Development')]/parent::div"));
        private IWebElement EliteCard => _driver.FindElement(By.XPath("//h4[contains(text(), 'Elite')]/parent::div"));
        private IWebElement CoachCard => _driver.FindElement(By.XPath("//h4[contains(text(), 'Coach')]/parent::div"));
        private IWebElement VolunteerCard => _driver.FindElement(By.XPath("//h4[contains(text(), 'Volunteer')]/parent::div"));

        // Actions
        public void ClickRegisterNow()
        {
            RegisterNowButton.Click();
        }

        public void SwitchToFrench()
        {
            LanguageDropdown.Click();
            _wait.Until(driver => FrenchLanguageLink.Displayed);
            FrenchLanguageLink.Click();
        }

        public void SwitchToEnglish()
        {
            LanguageDropdown.Click();
            _wait.Until(driver => EnglishLanguageLink.Displayed);
            EnglishLanguageLink.Click();
        }

        // Verifications
        public bool IsLogoDisplayed() => Logo.Displayed;
        public string GetWelcomeTitle() => WelcomeTitle.Text;
        public bool IsRegisterButtonDisplayed() => RegisterNowButton.Displayed;

        public bool AreStatisticsDisplayed()
        {
            return ActivePlayersCount.Displayed && 
                   TeamsCount.Displayed && 
                   CoachesCount.Displayed && 
                   VolunteersCount.Displayed;
        }

        public bool AreRegistrationTypesDisplayed()
        {
            return JuniorCard.Displayed && 
                   DevelopmentCard.Displayed && 
                   EliteCard.Displayed && 
                   CoachCard.Displayed && 
                   VolunteerCard.Displayed;
        }

        public string GetActivePlayersCount() => ActivePlayersCount.Text;
        public string GetTeamsCount() => TeamsCount.Text;
        public string GetCoachesCount() => CoachesCount.Text;
        public string GetVolunteersCount() => VolunteersCount.Text;
    }
}