using ParaHockeyApp.Models.Entities;

namespace ParaHockeyApp.Services
{
    /// <summary>
    /// Service for managing temporary member records during import process
    /// </summary>
    public interface ITempMemberService
    {
        /// <summary>
        /// Gets a temp member by ID with related data
        /// </summary>
        /// <param name="tempMemberId">The temp member ID</param>
        /// <returns>The temp member with navigation properties loaded</returns>
        Task<TempMember?> GetByIdAsync(Guid tempMemberId);

        /// <summary>
        /// Gets temp members by import batch and status
        /// </summary>
        /// <param name="importBatchId">The import batch ID</param>
        /// <param name="status">Optional status filter</param>
        /// <param name="pageNumber">Page number for pagination (1-based)</param>
        /// <param name="pageSize">Number of records per page</param>
        /// <returns>List of temp members matching criteria</returns>
        Task<PagedResult<TempMember>> GetByBatchAsync(int importBatchId, TempMemberStatus? status = null, int pageNumber = 1, int pageSize = 50);

        /// <summary>
        /// Creates a new member from temp member data
        /// </summary>
        /// <param name="tempMemberId">The temp member ID to convert</param>
        /// <param name="createdBy">Username of the person performing the creation</param>
        /// <returns>The created member</returns>
        Task<Member> CreateMemberFromTempAsync(Guid tempMemberId, string createdBy);

        /// <summary>
        /// Bulk creates members from multiple temp member records
        /// </summary>
        /// <param name="tempMemberIds">List of temp member IDs to convert</param>
        /// <param name="createdBy">Username of the person performing the creation</param>
        /// <returns>List of created members</returns>
        Task<List<Member>> BulkCreateMembersFromTempAsync(List<Guid> tempMemberIds, string createdBy);

        /// <summary>
        /// Updates a temp member's status
        /// </summary>
        /// <param name="tempMemberId">The temp member ID</param>
        /// <param name="status">New status</param>
        /// <param name="updatedBy">Username of the person updating</param>
        /// <returns>The updated temp member</returns>
        Task<TempMember> UpdateStatusAsync(Guid tempMemberId, TempMemberStatus status, string updatedBy);

        /// <summary>
        /// Updates temp member data (for fixing validation errors)
        /// </summary>
        /// <param name="tempMemberId">The temp member ID</param>
        /// <param name="updatedData">Updated member data</param>
        /// <param name="updatedBy">Username of the person updating</param>
        /// <returns>The updated temp member</returns>
        Task<TempMember> UpdateTempMemberDataAsync(Guid tempMemberId, TempMemberUpdateData updatedData, string updatedBy);

        /// <summary>
        /// Deletes a temp member record
        /// </summary>
        /// <param name="tempMemberId">The temp member ID to delete</param>
        /// <param name="deletedBy">Username of the person deleting</param>
        /// <returns>True if deleted successfully</returns>
        Task<bool> DeleteAsync(Guid tempMemberId, string deletedBy);

        /// <summary>
        /// Validates temp member data and returns validation errors
        /// </summary>
        /// <param name="tempMember">The temp member to validate</param>
        /// <returns>List of validation errors</returns>
        Task<List<TempMemberValidationError>> ValidateTempMemberAsync(TempMember tempMember);

        /// <summary>
        /// Resolves lookup values for temp member (gender, province, etc.)
        /// </summary>
        /// <param name="tempMemberId">The temp member ID</param>
        /// <returns>The temp member with resolved lookup IDs</returns>
        Task<TempMember> ResolveLookupValuesAsync(Guid tempMemberId);
    }

    /// <summary>
    /// Data for updating temp member information
    /// </summary>
    public class TempMemberUpdateData
    {
        public string? FirstName { get; set; }
        public string? LastName { get; set; }
        public DateTime? DateOfBirth { get; set; }
        public string? Email { get; set; }
        public string? Phone { get; set; }
        public string? Address { get; set; }
        public string? City { get; set; }
        public string? PostalCode { get; set; }
        public string? GenderText { get; set; }
        public string? ProvinceText { get; set; }
        public string? PhoneTypeText { get; set; }
        public string? RegistrationTypeText { get; set; }
    }

    /// <summary>
    /// Validation error for temp member data
    /// </summary>
    public class TempMemberValidationError
    {
        public string FieldName { get; set; } = string.Empty;
        public string ErrorMessage { get; set; } = string.Empty;
        public string? CurrentValue { get; set; }
    }

    /// <summary>
    /// Paged result for temp member queries
    /// </summary>
    public class PagedResult<T>
    {
        public List<T> Items { get; set; } = new();
        public int TotalCount { get; set; }
        public int PageNumber { get; set; }
        public int PageSize { get; set; }
        public int TotalPages => (int)Math.Ceiling((double)TotalCount / PageSize);
        public bool HasPreviousPage => PageNumber > 1;
        public bool HasNextPage => PageNumber < TotalPages;
    }
}