using ParaHockeyApp.Models.Entities;

namespace ParaHockeyApp.Services
{
    /// <summary>
    /// Service for managing temporary member records during import process
    /// </summary>
    public interface ITempMemberService
    {
        /// <summary>
        /// Gets temp member by ID with related data
        /// </summary>
        /// <param name="tempMemberId">Temp member ID</param>
        /// <returns>Temp member if found, null otherwise</returns>
        Task<TempMember?> GetByIdAsync(Guid tempMemberId);

        /// <summary>
        /// Creates a new member from temp member data
        /// </summary>
        /// <param name="tempMemberId">Temp member ID</param>
        /// <param name="createdBy">Admin performing the action</param>
        /// <returns>Created member</returns>
        Task<Member> CreateFromTempAsync(Guid tempMemberId, string createdBy);

        /// <summary>
        /// Bulk creates members from multiple temp member records
        /// </summary>
        /// <param name="tempMemberIds">List of temp member IDs</param>
        /// <param name="createdBy">Admin performing the action</param>
        /// <returns>List of created members</returns>
        Task<List<Member>> BulkCreateFromTempAsync(List<Guid> tempMemberIds, string createdBy);

        /// <summary>
        /// Updates temp member status
        /// </summary>
        /// <param name="tempMemberId">Temp member ID</param>
        /// <param name="status">New status</param>
        Task UpdateStatusAsync(Guid tempMemberId, TempMemberStatus status);

        /// <summary>
        /// Updates temp member data (for fixing validation errors)
        /// </summary>
        /// <param name="tempMember">Updated temp member data</param>
        Task UpdateAsync(TempMember tempMember);

        /// <summary>
        /// Deletes temp member record
        /// </summary>
        /// <param name="tempMemberId">Temp member ID</param>
        Task DeleteAsync(Guid tempMemberId);

        /// <summary>
        /// Resolves lookup field IDs from text values
        /// </summary>
        /// <param name="tempMember">Temp member to resolve</param>
        Task ResolveLookupFieldsAsync(TempMember tempMember);

        /// <summary>
        /// Determines registration type based on age and business rules
        /// </summary>
        /// <param name="dateOfBirth">Member's date of birth</param>
        /// <returns>Registration type ID</returns>
        Task<int> DetermineRegistrationTypeAsync(DateTime dateOfBirth);

        /// <summary>
        /// Validates temp member data and returns validation errors
        /// </summary>
        /// <param name="tempMember">Temp member to validate</param>
        /// <returns>List of validation errors (empty if valid)</returns>
        Task<List<string>> ValidateAsync(TempMember tempMember);

        /// <summary>
        /// Gets count of temp members by status for a batch
        /// </summary>
        /// <param name="batchId">Import batch ID</param>
        /// <returns>Dictionary of status counts</returns>
        Task<Dictionary<TempMemberStatus, int>> GetStatusCountsAsync(int batchId);

        /// <summary>
        /// Reprocesses temp member for duplicate detection after data changes
        /// </summary>
        /// <param name="tempMemberId">Temp member ID</param>
        Task ReprocessForDuplicatesAsync(Guid tempMemberId);
    }
}