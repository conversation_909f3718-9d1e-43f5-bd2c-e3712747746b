using System.ComponentModel.DataAnnotations;

namespace ParaHockeyApp.Models.Entities
{
    /// <summary>
    /// Represents information about a specific page in the application
    /// Used for tracking page details during modernization process
    /// </summary>
    public class PageInfo : BaseEntity
    {
        /// <summary>
        /// Unique name identifier for the page (e.g., "Home/Index", "Members/Register")
        /// </summary>
        [Required]
        [StringLength(200)]
        public string Name { get; set; } = string.Empty;

        /// <summary>
        /// Controller name
        /// </summary>
        [Required]
        [StringLength(100)]
        public string Controller { get; set; } = string.Empty;

        /// <summary>
        /// Action name
        /// </summary>
        [Required]
        [StringLength(100)]
        public string Action { get; set; } = string.Empty;

        /// <summary>
        /// Comma-separated list of routes for this page
        /// </summary>
        [StringLength(500)]
        public string Routes { get; set; } = string.Empty;

        /// <summary>
        /// Comma-separated list of view files associated with this page
        /// </summary>
        [StringLength(1000)]
        public string ViewFiles { get; set; } = string.Empty;

        /// <summary>
        /// Comma-separated list of JavaScript files associated with this page
        /// </summary>
        [StringLength(1000)]
        public string JavaScriptFiles { get; set; } = string.Empty;

        /// <summary>
        /// Comma-separated list of stylesheet files associated with this page
        /// </summary>
        [StringLength(1000)]
        public string StylesheetFiles { get; set; } = string.Empty;

        /// <summary>
        /// Complexity level of modernizing this page
        /// </summary>
        [Required]
        public ComplexityLevel Complexity { get; set; }

        /// <summary>
        /// Comma-separated list of dependencies (other pages/components this page depends on)
        /// </summary>
        [StringLength(1000)]
        public string Dependencies { get; set; } = string.Empty;

        /// <summary>
        /// Priority level for modernization (1 = highest, 3 = lowest)
        /// </summary>
        [Required]
        public int Priority { get; set; } = 2;

        /// <summary>
        /// Whether this page has been modernized
        /// </summary>
        [Required]
        public bool IsModernized { get; set; } = false;

        /// <summary>
        /// Date when modernization was completed (if applicable)
        /// </summary>
        public DateTime? ModernizedDate { get; set; }

        /// <summary>
        /// Foreign key to the page inventory this page belongs to
        /// </summary>
        [Required]
        public int PageInventoryId { get; set; }

        /// <summary>
        /// Navigation property to the page inventory
        /// </summary>
        public virtual PageInventory PageInventory { get; set; } = null!;

        /// <summary>
        /// Navigation property to audit results for this page
        /// </summary>
        public virtual ICollection<PageAuditResult> AuditResults { get; set; } = new List<PageAuditResult>();

        // Helper properties for display
        public List<string> RoutesList => Routes.Split(',', StringSplitOptions.RemoveEmptyEntries).ToList();
        public List<string> ViewFilesList => ViewFiles.Split(',', StringSplitOptions.RemoveEmptyEntries).ToList();
        public List<string> JavaScriptFilesList => JavaScriptFiles.Split(',', StringSplitOptions.RemoveEmptyEntries).ToList();
        public List<string> StylesheetFilesList => StylesheetFiles.Split(',', StringSplitOptions.RemoveEmptyEntries).ToList();
        public List<string> DependenciesList => Dependencies.Split(',', StringSplitOptions.RemoveEmptyEntries).ToList();
    }

    /// <summary>
    /// Complexity levels for page modernization
    /// </summary>
    public enum ComplexityLevel
    {
        /// <summary>
        /// Simple display pages with minimal interaction
        /// </summary>
        Low = 1,

        /// <summary>
        /// Forms with basic validation and moderate complexity
        /// </summary>
        Medium = 2,

        /// <summary>
        /// Complex forms with multiple sections and advanced features
        /// </summary>
        High = 3,

        /// <summary>
        /// Critical admin pages with sensitive operations
        /// </summary>
        Critical = 4
    }
}