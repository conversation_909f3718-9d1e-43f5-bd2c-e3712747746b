{"DetailedErrors": true, "Logging": {"LogLevel": {"Default": "Warning", "Microsoft.AspNetCore": "Warning"}}, "ConnectionStrings": {"DefaultConnection": "Server=SIMBASQLEXPRESS;User Id=ParaHockeyUser;Password=***************;Database=ParaHockeyDB;Encrypt=False;TrustServerCertificate=True;"}, "Environment": {"Name": "PRODUCTION", "Theme": "primary", "ShowBanner": false, "UseAuthentication": true, "BannerText": "", "ShowDevelopmentTools": false, "EnableDetailedErrorLogging": true, "EnvironmentIndicatorColor": "primary", "ShowUserFriendlyErrors": false, "ErrorDetailLevel": "detailed"}, "Email": {"SmtpHost": "smtp.office365.com", "SmtpPort": "587", "Username": "<EMAIL>", "Password": "L@535539113654on", "FromEmail": "<EMAIL>", "FromName": "Parahockey Verification"}}