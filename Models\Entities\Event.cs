using System.ComponentModel.DataAnnotations;

namespace ParaHockeyApp.Models.Entities
{
    /// <summary>
    /// Main event entity for calendar events
    /// </summary>
    public class Event : BaseEntity
    {
        /// <summary>
        /// Event title
        /// </summary>
        [Required]
        [StringLength(200)]
        public string Title { get; set; } = string.Empty;

        /// <summary>
        /// Event description
        /// </summary>
        [StringLength(2000)]
        public string? Description { get; set; }

        /// <summary>
        /// Event start date and time
        /// </summary>
        [Required]
        public DateTime StartDate { get; set; }

        /// <summary>
        /// Event end date and time
        /// </summary>
        [Required]
        public DateTime EndDate { get; set; }

        /// <summary>
        /// Whether this is an all-day event
        /// </summary>
        public bool IsAllDay { get; set; } = false;

        /// <summary>
        /// Event location
        /// </summary>
        [StringLength(300)]
        public string? Location { get; set; }

        /// <summary>
        /// Event category ID
        /// </summary>
        [Required]
        public int EventCategoryId { get; set; }

        /// <summary>
        /// Whether registration is required for this specific event
        /// </summary>
        public bool RequiresRegistration { get; set; } = false;

        /// <summary>
        /// Maximum number of participants for this event (-1 for unlimited)
        /// </summary>
        public int MaxParticipants { get; set; } = -1;

        /// <summary>
        /// Registration deadline (if registration is required)
        /// </summary>
        public DateTime? RegistrationDeadline { get; set; }

        /// <summary>
        /// Whether the event is published and visible to members
        /// </summary>
        public bool IsPublished { get; set; } = false;

        /// <summary>
        /// Whether the event is recurring
        /// </summary>
        public bool IsRecurring { get; set; } = false;

        /// <summary>
        /// Recurrence pattern (if recurring)
        /// </summary>
        [StringLength(100)]
        public string? RecurrencePattern { get; set; }

        /// <summary>
        /// Priority level for display (1 = highest, 5 = lowest)
        /// </summary>
        public int Priority { get; set; } = 3;

        /// <summary>
        /// Contact person for event inquiries
        /// </summary>
        [StringLength(100)]
        public string? ContactPerson { get; set; }

        /// <summary>
        /// Contact email for event inquiries
        /// </summary>
        [StringLength(100)]
        public string? ContactEmail { get; set; }

        /// <summary>
        /// Contact phone for event inquiries
        /// </summary>
        [StringLength(20)]
        public string? ContactPhone { get; set; }

        /// <summary>
        /// Additional notes for organizers
        /// </summary>
        [StringLength(1000)]
        public string? OrganizerNotes { get; set; }

        /// <summary>
        /// Navigation property to event category
        /// </summary>
        public virtual EventCategory? EventCategory { get; set; }

        /// <summary>
        /// Navigation property to event registrations
        /// </summary>
        public virtual ICollection<EventRegistration> EventRegistrations { get; set; } = new List<EventRegistration>();

        /// <summary>
        /// Computed property for current registration count
        /// </summary>
        public int CurrentRegistrations => EventRegistrations.Count(er => er.Status == RegistrationStatus.Confirmed);

        /// <summary>
        /// Computed property for available spots
        /// </summary>
        public int AvailableSpots => MaxParticipants == -1 ? int.MaxValue : MaxParticipants - CurrentRegistrations;

        /// <summary>
        /// Computed property to check if event is full
        /// </summary>
        public bool IsFull => MaxParticipants > 0 && CurrentRegistrations >= MaxParticipants;

        /// <summary>
        /// Computed property to check if registration is still open
        /// </summary>
        public bool IsRegistrationOpen => RequiresRegistration && 
                                        IsPublished && 
                                        !IsFull && 
                                        (RegistrationDeadline == null || DateTime.Now <= RegistrationDeadline);

        /// <summary>
        /// Computed property for event duration in minutes
        /// </summary>
        public int DurationMinutes => (int)(EndDate - StartDate).TotalMinutes;

        /// <summary>
        /// Computed property for display purposes
        /// </summary>
        public string DateRangeDisplay => IsAllDay 
            ? (StartDate.Date == EndDate.Date 
                ? StartDate.ToString("MMMM d, yyyy") 
                : $"{StartDate:MMMM d} - {EndDate:MMMM d, yyyy}")
            : (StartDate.Date == EndDate.Date 
                ? $"{StartDate:MMMM d, yyyy} at {StartDate:h:mm tt} - {EndDate:h:mm tt}"
                : $"{StartDate:MMMM d, yyyy h:mm tt} - {EndDate:MMMM d, yyyy h:mm tt}");
    }
}