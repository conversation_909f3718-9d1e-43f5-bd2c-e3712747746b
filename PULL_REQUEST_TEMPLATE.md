# Pull Request Checklist

## Description
<!-- Provide a brief description of the changes in this PR -->

## Type of Change
- [ ] Bug fix (non-breaking change which fixes an issue)
- [ ] New feature (non-breaking change which adds functionality)
- [ ] Breaking change (fix or feature that would cause existing functionality to not work as expected)
- [ ] Documentation update
- [ ] Configuration change

## Testing
- [ ] I have tested my changes in the **Development** environment
- [ ] I have tested my changes in the **Test/Staging** environment
- [ ] All new and existing unit tests pass
- [ ] I have manually tested the registration process if my changes affect forms

## Configuration
- [ ] I have added any new configuration settings to **ALL** relevant `appsettings.*.json` files:
  - [ ] `appsettings.json` (base configuration)
  - [ ] `appsettings.Staging.json` (test environment)
  - [ ] `appsettings.Production.json` (production environment)
- [ ] I have verified that the application behaves identically across all environments (except for approved exceptions)

## Code Quality
- [ ] My code follows the existing code style and conventions
- [ ] I have added appropriate `autocomplete` attributes to any new form inputs
- [ ] All `<label>` elements have proper `for` attributes pointing to their inputs
- [ ] I have used localization for all user-facing text (`@SharedLocalizer["Key"]`)
- [ ] I have not added any environment-specific logic that changes functionality

## Environment Consistency
- [ ] I confirm that my changes do not introduce environment-specific behavior
- [ ] If my changes include test/debug features, they are controlled by configuration settings
- [ ] I understand the approved environment differences:
  - Test buttons (controlled by `ShowDevelopmentTools` setting)
  - Theme colors (controlled by `Theme` setting)
  - Environment banners (controlled by `ShowBanner` setting)

## Documentation
- [ ] I have updated relevant documentation
- [ ] I have added comments to complex code sections
- [ ] I have updated the README if needed

## Additional Notes
<!-- Add any additional information that reviewers should know -->

---
**Reminder**: The application must work identically in Development, Test, and Production environments. Any differences must be controlled through configuration files, not code.