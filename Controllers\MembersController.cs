using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Authentication;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Rendering;
using Microsoft.EntityFrameworkCore;
using ParaHockeyApp.Models;
using ParaHockeyApp.Models.ViewModels;
using ParaHockeyApp.Services;
using System.Linq;
using System.Threading.Tasks;
using System.Collections.Generic;
using System;
using AutoMapper;
using ParaHockeyApp.Models.Entities;
using ParaHockeyApp.Models.Configuration;
using Microsoft.Extensions.Options;
using Microsoft.Extensions.Localization;
using ParaHockeyApp.Resources;
using ParaHockeyApp.DTOs;
using ParaHockeyApp.Models.Session;
using System.Text.Json;

namespace ParaHockeyApp.Controllers
{
    public class MembersController : BaseMvcController
    {
        private readonly IMemberService _memberService;
        private readonly ApplicationContext _context;
        private readonly IMapper _mapper;
        private readonly UserManager<AppUser> _userManager;
        private readonly SignInManager<AppUser> _signInManager;
        private readonly IEmailService _emailService;
        private readonly EnvironmentSettings _environmentSettings;
        private readonly IDuplicateMemberService _duplicateMemberService;
        private readonly IEventService _eventService;
        private readonly IEmptyStateService _emptyStateService;
        private readonly ITempMemberService _tempMemberService;

        public MembersController(IMemberService memberService, ApplicationContext context, ILogger<MembersController> logger, IMapper mapper, UserManager<AppUser> userManager, SignInManager<AppUser> signInManager, IEmailService emailService, IOptions<EnvironmentSettings> environmentSettings, IDuplicateMemberService duplicateMemberService, IStringLocalizer<SharedResourceMarker> localizer, IEventService eventService, IEmptyStateService emptyStateService, ITempMemberService tempMemberService)
            : base(logger, localizer)
        {
            _memberService = memberService;
            _context = context;
            _mapper = mapper;
            _userManager = userManager;
            _signInManager = signInManager;
            _emailService = emailService;
            _environmentSettings = environmentSettings.Value;
            _duplicateMemberService = duplicateMemberService;
            _eventService = eventService;
            _emptyStateService = emptyStateService;
            _tempMemberService = tempMemberService;
        }

        #region Session Management

        /// <summary>
        /// Gets the current member session from HTTP session
        /// </summary>
        private MemberSession? GetCurrentMemberSession()
        {
            try
            {
                var sessionData = HttpContext.Session.GetString("MemberSessionData");
                if (string.IsNullOrEmpty(sessionData))
                    return null;

                var session = JsonSerializer.Deserialize<MemberSession>(sessionData);
                return session?.IsValidSession() == true ? session : null;
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Error retrieving member session");
                return null;
            }
        }

        /// <summary>
        /// Sets the member session in HTTP session
        /// </summary>
        private async Task SetMemberSession(MemberSession session)
        {
            try
            {
                // Admin logout should have already happened in VerifyCode method
                // This is just for any edge cases
                
                // IMPORTANT: Cross-browser logout requires infrastructure not currently implemented.
                // This only clears the admin session in the SAME browser where member logs in.
                // To clear admin sessions in OTHER browsers, we would need:
                // - SignalR for real-time session management, OR
                // - Distributed cache (Redis) to track/invalidate sessions, OR  
                // - Database-based session invalidation tokens
                // For now, each browser maintains independent sessions as per web standards.

                var sessionData = JsonSerializer.Serialize(session);
                HttpContext.Session.SetString("MemberSessionData", sessionData);
                _logger.LogInformation("Member session created for {MemberName} (ID: {MemberId})", 
                    session.MemberName, session.MemberId);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error setting member session");
            }
        }

        /// <summary>
        /// Validates if the current member session is valid
        /// </summary>
        private bool IsValidMemberSession()
        {
            var session = GetCurrentMemberSession();
            var isValid = session?.IsValidSession() == true;
            
            _logger.LogInformation($"🔍 IsValidMemberSession: Session={session != null}, IsValid={isValid}, AdminAuth={User.Identity?.IsAuthenticated}");
            if (session != null)
            {
                _logger.LogInformation($"🔍 Member session details: MemberId={session.MemberId}, MemberName={session.MemberName}, Expires={session.ExpiryTime}");
            }
            
            return isValid;
        }

        /// <summary>
        /// Clears the member session
        /// </summary>
        private void ClearMemberSession()
        {
            HttpContext.Session.Remove("MemberSessionData");
            _logger.LogInformation("Member session cleared");
        }

        /// <summary>
        /// Refreshes the current member session expiry time
        /// </summary>
        private async Task RefreshMemberSession()
        {
            var session = GetCurrentMemberSession();
            if (session != null)
            {
                session.RefreshSession();
                await SetMemberSession(session);
            }
        }

        /// <summary>
        /// Validates member access and returns redirect if session is invalid
        /// </summary>
        private IActionResult? ValidateMemberSessionAccess()
        {
            if (!IsValidMemberSession())
            {
                ClearMemberSession();
                SetErrorMessage("SessionExpired");
                return RedirectToAction(nameof(Login));
            }
            
            RefreshMemberSession();
            return null;
        }

        #endregion

        private async Task<bool> IsUserAdminAsync()
        {
            if (User.Identity?.IsAuthenticated != true)
            {
                return false;
            }
            
            var userEmail = User.Claims.FirstOrDefault(c => c.Type == "preferred_username")?.Value ?? User.Identity?.Name;
            
            if (string.IsNullOrEmpty(userEmail))
            {
                return false;
            }

            // Check if user is an admin
            var admin = await _context.AdminUsers.FirstOrDefaultAsync(a => a.Email == userEmail && a.IsActive);
            return admin != null;
        }

        [HttpGet]
        public async Task<IActionResult> Register()
        {
            // Set admin flag for conditional field visibility
            ViewBag.IsAdmin = await IsUserAdminAsync();
            
            var viewModel = new MemberRegistrationViewModel
            {
                Provinces = await _context.Provinces
                                          .Select(p => new SelectListItem
                                          {
                                              Value = p.Id.ToString(),
                                              Text = p.DisplayNameKey // We'll handle localization in the View
                                          }).ToListAsync(),
                Genders = await _context.Genders
                                        .Select(g => new SelectListItem
                                        {
                                            Value = g.Id.ToString(),
                                            Text = g.DisplayNameKey
                                        }).ToListAsync(),
                PhoneTypes = await _context.PhoneTypes
                                           .Select(pt => new SelectListItem
                                           {
                                               Value = pt.Id.ToString(),
                                               Text = pt.DisplayNameKey
                                           }).ToListAsync(),
                RegistrationTypes = await _context.RegistrationTypes
                                                  .Select(rt => new SelectListItem
                                                  {
                                                      Value = rt.Id.ToString(),
                                                      Text = rt.DisplayNameKey,
                                                      Group = new SelectListGroup { Name = rt.DescriptionKey }
                                                  }).ToListAsync(),
                EnvironmentSettings = _environmentSettings
            };

            // Ensure at least one ParentViewModel for Junior form
            viewModel.Parents.Add(new ParentViewModel());

            return View(viewModel);
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Register(MemberRegistrationViewModel viewModel, Guid? tempMemberId = null)
        {
            // Set admin flag for conditional field visibility
            ViewBag.IsAdmin = await IsUserAdminAsync();
            
            // Preserve temp member ID for status updates
            if (tempMemberId.HasValue)
            {
                ViewBag.TempMemberId = tempMemberId.Value;
            }
            
            // CRITICAL FIX: Clear inappropriate validation errors based on age
            // This must happen BEFORE ModelState.IsValid check
            if (viewModel.DateOfBirth.HasValue)
            {
                var today = DateTime.Today;
                var age = today.Year - viewModel.DateOfBirth.Value.Year;
                if (viewModel.DateOfBirth.Value.Date > today.AddYears(-age)) age--;
                
                var ageOfMajority = 18;
                if (age >= ageOfMajority)
                {
                    // For adults, clear ALL Parent-related ModelState errors
                    var parentKeys = ModelState.Keys.Where(key => key.StartsWith("Parents[")).ToList();
                    foreach (var key in parentKeys)
                    {
                        ModelState.Remove(key);
                    }
                    _logger.LogInformation("Cleared {Count} Parent validation errors for adult registration (age: {Age})", parentKeys.Count, age);
                }
                else
                {
                    // For children, clear ALL EmergencyContact-related ModelState errors
                    var emergencyKeys = ModelState.Keys.Where(key => key.StartsWith("EmergencyContact.")).ToList();
                    foreach (var key in emergencyKeys)
                    {
                        ModelState.Remove(key);
                    }
                    _logger.LogInformation("Cleared {Count} EmergencyContact validation errors for child registration (age: {Age})", emergencyKeys.Count, age);
                }
            }
            
            if (!ModelState.IsValid)
            {
                // --- START DEBUGGING CODE ---
                var errors = ModelState
                    .Where(x => x.Value?.Errors.Count > 0)
                    .Select(x => new { x.Key, Errors = x.Value?.Errors })
                    .ToArray();

                var errorList = new System.Text.StringBuilder();
                errorList.AppendLine("Server-side validation failed. Errors:");
                foreach (var error in errors)
                {
                    foreach (var subError in error.Errors ?? new Microsoft.AspNetCore.Mvc.ModelBinding.ModelErrorCollection())
                    {
                        errorList.AppendLine($"Field: {error.Key}, Error: {subError.ErrorMessage}");
                    }
                }
                _logger.LogError(errorList.ToString()); // Log the detailed errors
                TempData["ServerValidationErrors"] = errorList.ToString(); // Pass errors to the view
                // --- END DEBUGGING CODE ---

                // If model state is invalid, we need to re-populate the lists for the view
                viewModel.Provinces = await _context.Provinces
                                         .Select(p => new SelectListItem
                                         {
                                             Value = p.Id.ToString(),
                                             Text = p.DisplayNameKey
                                         }).ToListAsync();
                viewModel.Genders = await _context.Genders
                                        .Select(g => new SelectListItem
                                        {
                                            Value = g.Id.ToString(),
                                            Text = g.DisplayNameKey
                                        }).ToListAsync();
                viewModel.PhoneTypes = await _context.PhoneTypes
                                           .Select(pt => new SelectListItem
                                           {
                                               Value = pt.Id.ToString(),
                                               Text = pt.DisplayNameKey
                                           }).ToListAsync();
                viewModel.RegistrationTypes = await _context.RegistrationTypes
                                                  .Select(rt => new SelectListItem
                                                  {
                                                      Value = rt.Id.ToString(),
                                                      Text = rt.DisplayNameKey,
                                                      Group = new SelectListGroup { Name = rt.DescriptionKey }
                                                  }).ToListAsync();
                viewModel.EnvironmentSettings = _environmentSettings;
                return View(viewModel);
            }

            // Server-side duplicate check as safety net (unless user chose to bypass)
            var bypassDuplicateCheck = Request.Form["BypassDuplicateCheck"].ToString() == "true";
            if (!bypassDuplicateCheck)
            {
                var duplicateResult = await _duplicateMemberService.CheckForDuplicatesAsync(
                    viewModel.Email, 
                    viewModel.LastName, 
                    viewModel.DateOfBirth);

                if (duplicateResult.Type != DuplicateType.NoDuplicate)
                {
                    // This should not happen if JavaScript is working, but safety net
                    _logger.LogWarning("Server-side duplicate detection triggered - JavaScript may have failed");
                    SetErrorMessage("DuplicateEmailMessage");
                    return RedirectToAction("Register");
                }
            }
            else
            {
                _logger.LogInformation("User chose to bypass duplicate check - proceeding with registration");
            }

            // Begin atomic transaction
            await using var transaction = await _context.Database.BeginTransactionAsync();
            try
            {
                // Create AppUser first, then save member with UserId
                var user = await _userManager.FindByEmailAsync(viewModel.Email);
                if (user == null)
                {
                    user = new AppUser { UserName = viewModel.Email, Email = viewModel.Email, EmailConfirmed = false };
                    var createUserResult = await _userManager.CreateAsync(user);
                    if (!createUserResult.Succeeded)
                    {
                        foreach (var error in createUserResult.Errors)
                        {
                            ModelState.AddModelError(string.Empty, error.Description);
                        }
                        throw new Exception($"Failed to create AppUser: {string.Join(", ", createUserResult.Errors.Select(e => e.Description))}");
                    }
                }

                // Save member with UserId using MemberService for logging
                var member = _mapper.Map<Member>(viewModel);
                member.UserId = user.Id; // Set UserId before saving
                await _memberService.RegisterMemberAsync(viewModel, member);

                // Use age-based logic (same as validation) instead of RegistrationTypeId
                var today = DateTime.Today;
                var age = today.Year - viewModel.DateOfBirth!.Value.Year;
                if (viewModel.DateOfBirth.Value.Date > today.AddYears(-age)) age--;
                
                var ageOfMajority = 18;
                if (age < ageOfMajority)
                {
                    // Child - create Parents
                    foreach (var parentVm in viewModel.Parents.Where(p => p != null))
                    {
                        var parentEntity = _mapper.Map<Parent>(parentVm);
                        parentEntity.MemberId = member.Id;
                        _context.Parents.Add(parentEntity);
                    }
                }
                else
                {
                    // Adult - create EmergencyContact
                    var ecEntity = _mapper.Map<EmergencyContact>(viewModel.EmergencyContact);
                    ecEntity.MemberId = member.Id;
                    _context.EmergencyContacts.Add(ecEntity);
                }

                await _context.SaveChangesAsync();
                
                // Update temp member status if this registration came from temp data
                if (ViewBag.TempMemberId != null)
                {
                    try
                    {
                        var registrationTempMemberId = (Guid)ViewBag.TempMemberId;
                        await _tempMemberService.UpdateStatusAsync(registrationTempMemberId, TempMemberStatus.Created, "System");
                        _logger.LogInformation("Updated temp member {TempMemberId} status to Created after successful member creation", registrationTempMemberId);
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "Failed to update temp member status after member creation");
                        // Don't fail the registration for this - just log the error
                    }
                }
                
                await transaction.CommitAsync();

                SetSuccessMessage("RegistrationSuccessful");
                return RedirectToAction("Index", "Home");
            }
            catch (Exception ex)
            {
                await transaction.RollbackAsync();
                _logger.LogError(ex, "Error registering member");
                ModelState.AddModelError(string.Empty, "An error occurred while processing your request.");
                // repopulate lists again
                viewModel.Provinces = await _context.Provinces
                                         .Select(p => new SelectListItem
                                         {
                                             Value = p.Id.ToString(),
                                             Text = p.DisplayNameKey
                                         }).ToListAsync();
                viewModel.Genders = await _context.Genders
                                        .Select(g => new SelectListItem
                                        {
                                            Value = g.Id.ToString(),
                                            Text = g.DisplayNameKey
                                        }).ToListAsync();
                viewModel.PhoneTypes = await _context.PhoneTypes
                                           .Select(pt => new SelectListItem
                                           {
                                               Value = pt.Id.ToString(),
                                               Text = pt.DisplayNameKey
                                           }).ToListAsync();
                viewModel.RegistrationTypes = await _context.RegistrationTypes
                                                  .Select(rt => new SelectListItem
                                                  {
                                                      Value = rt.Id.ToString(),
                                                      Text = rt.DisplayNameKey,
                                                      Group = new SelectListGroup { Name = rt.DescriptionKey }
                                                  }).ToListAsync();
                viewModel.EnvironmentSettings = _environmentSettings;
                
                // Preserve tempMemberId in ViewBag for form re-display
                if (tempMemberId.HasValue)
                {
                    ViewBag.TempMemberId = tempMemberId.Value;
                }
                
                return View(viewModel);
            }
        }

        /// <summary>
        /// GET: Register with temp member pre-filling
        /// </summary>
        public async Task<IActionResult> Register(Guid? tempMemberId)
        {
            // Set admin flag for conditional field visibility
            ViewBag.IsAdmin = await IsUserAdminAsync();
            
            var viewModel = new MemberRegistrationViewModel
            {
                Provinces = await _context.Provinces
                    .Select(p => new SelectListItem
                    {
                        Value = p.Id.ToString(),
                        Text = p.DisplayNameKey
                    }).ToListAsync(),
                Genders = await _context.Genders
                    .Select(g => new SelectListItem
                    {
                        Value = g.Id.ToString(),
                        Text = g.DisplayNameKey
                    }).ToListAsync(),
                PhoneTypes = await _context.PhoneTypes
                    .Select(pt => new SelectListItem
                    {
                        Value = pt.Id.ToString(),
                        Text = pt.DisplayNameKey
                    }).ToListAsync(),
                RegistrationTypes = await _context.RegistrationTypes
                    .Select(rt => new SelectListItem
                    {
                        Value = rt.Id.ToString(),
                        Text = rt.DisplayNameKey,
                        Group = new SelectListGroup { Name = rt.DescriptionKey }
                    }).ToListAsync(),
                EnvironmentSettings = _environmentSettings
            };

            // Pre-fill form with temp member data if provided
            if (tempMemberId.HasValue)
            {
                try
                {
                    var tempMember = await _tempMemberService.GetByIdAsync(tempMemberId.Value);
                    if (tempMember != null)
                    {
                        // Map basic information
                        viewModel.FirstName = tempMember.FirstName ?? string.Empty;
                        viewModel.LastName = tempMember.LastName ?? string.Empty;
                        viewModel.DateOfBirth = tempMember.DateOfBirth;
                        viewModel.Email = tempMember.Email ?? string.Empty;
                        viewModel.Phone = tempMember.Phone ?? string.Empty;
                        viewModel.Address = tempMember.Address ?? string.Empty;
                        viewModel.City = tempMember.City ?? string.Empty;
                        viewModel.PostalCode = tempMember.PostalCode ?? string.Empty;
                        viewModel.HQc_Id = tempMember.HcrNumber ?? string.Empty;

                        // Map lookup values if they exist
                        if (tempMember.GenderId.HasValue)
                            viewModel.GenderId = tempMember.GenderId.Value;
                        if (tempMember.ProvinceId.HasValue)
                            viewModel.ProvinceId = tempMember.ProvinceId.Value;
                        if (tempMember.PhoneTypeId.HasValue)
                            viewModel.PhoneTypeId = tempMember.PhoneTypeId.Value;
                        if (tempMember.RegistrationTypeId.HasValue)
                            viewModel.RegistrationTypeId = tempMember.RegistrationTypeId.Value;

                        // Add temp member ID to ViewBag for tracking
                        ViewBag.TempMemberId = tempMemberId.Value;
                        
                        // Add success message to indicate pre-filling
                        TempData["InfoMessage"] = "Form has been pre-filled with imported member data. Please review and complete any missing information.";
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error loading temp member {TempMemberId} for pre-filling", tempMemberId);
                    TempData["ErrorMessage"] = "Unable to load imported member data. Please fill out the form manually.";
                }
            }

            return View(viewModel);
        }

        [HttpPost]
        public async Task<IActionResult> CheckDuplicate([FromBody] DuplicateCheckRequest request)
        {
            if (string.IsNullOrWhiteSpace(request?.Email) || string.IsNullOrWhiteSpace(request?.LastName) || !request.DateOfBirth.HasValue)
            {
                return BadRequest(new { error = _localizer["ApiInvalidRequestData"].Value });
            }

            var duplicateResult = await _duplicateMemberService.CheckForDuplicatesAsync(
                request.Email, 
                request.LastName, 
                request.DateOfBirth.Value);

            var response = new DuplicateResponseDto
            {
                IsDuplicate = duplicateResult.Type != DuplicateType.NoDuplicate,
                DuplicateType = duplicateResult.Type.ToString()
            };

            if (duplicateResult.Type == DuplicateType.ExactEmailMatch)
            {
                response.Message = _localizer["DuplicateEmailMessage"];
                response.RedirectUrl = Url.Action("Login", "Members");
            }
            else if (duplicateResult.Type == DuplicateType.PartialMatch)
            {
                response.Message = string.Format(_localizer["DuplicatePartialMessage"], duplicateResult.MaskedEmail);
                response.MaskedEmail = duplicateResult.MaskedEmail;
            }

            return Json(response);
        }

        [HttpGet]
        public IActionResult Login()
        {
            // Smart redirect: If user is already authenticated, redirect to dashboard
            var memberSession = GetCurrentMemberSession();
            if (memberSession != null && memberSession.IsValidSession())
            {
                _logger.LogInformation("Authenticated member {MemberId} attempted to access login page, redirecting to dashboard", memberSession.MemberId);
                return RedirectToAction("Dashboard");
            }
            
            return View(new MemberSearchViewModel
            {
                EnvironmentSettings = _environmentSettings
            });
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Login(MemberSearchViewModel viewModel)
        {
            if (!ModelState.IsValid)
            {
                return View(viewModel);
            }

            var searchResults = await _context.Members
                .Where(m => m.LastName == viewModel.LastName &&
                            m.DateOfBirth == viewModel.DateOfBirth &&
                            m.IsActive)
                .ToListAsync();

            viewModel.SearchResults = searchResults;
            viewModel.EnvironmentSettings = _environmentSettings;

            return View(viewModel);
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> SendVerificationCode(string email)
        {
            var user = await _userManager.FindByEmailAsync(email);
            if (user == null)
            {
                // Don't reveal that the user does not exist or is not confirmed
                if (Request.Headers["X-Requested-With"] == "XMLHttpRequest")
                {
                    return Json(new { success = false, message = _localizer["ApiUserNotFound"].Value });
                }
                return RedirectToAction(nameof(Login));
            }

            // Generate verification code
            var code = await _userManager.GenerateTwoFactorTokenAsync(user, "Email");

            // Get member and check if active
            var member = await _context.Members.FirstOrDefaultAsync(m => m.Email == email);
            if (member != null && !member.IsActive)
            {
                // Member account is deactivated
                var deactivatedMessage = _localizer["AccountDeactivated"].ToString();
                if (Request.Headers["X-Requested-With"] == "XMLHttpRequest")
                {
                    return Json(new { success = false, message = deactivatedMessage });
                }
                SetErrorMessage("AccountDeactivated");
                return RedirectToAction(nameof(Login));
            }

            var memberName = member != null ? $"{member.FirstName} {member.LastName}" : user.UserName ?? "Member";

            // Send email with verification code
            var emailSent = await _emailService.SendVerificationCodeAsync(email, code, memberName);

            if (!emailSent)
            {
                _logger.LogWarning($"Failed to send verification email to {email}");
            }

            // Only set TempData for non-AJAX requests
            if (Request.Headers["X-Requested-With"] == "XMLHttpRequest")
            {
                return Json(new { success = emailSent, message = emailSent ? _localizer["ApiCodeSentEmail"].Value : _localizer["ApiEmailSendingError"].Value });
            }
            else
            {
                TempData["VerificationEmail"] = email;
                TempData["CodeSent"] = emailSent;
                TempData["EmailSentResult"] = emailSent ? "Email envoyé avec succès" : "Erreur lors de l'envoi de l'email";
                return RedirectToAction(nameof(Login));
            }
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> VerifyCode(string email, string code)
        {
            var user = await _userManager.FindByEmailAsync(email);
            if (user == null)
            {
                if (Request.Headers["X-Requested-With"] == "XMLHttpRequest")
                {
                    return Json(new { success = false, message = _localizer["ApiValidationBad"].Value });
                }
                TempData["VerificationResult"] = _localizer["ApiValidationBad"].Value;
                return RedirectToAction(nameof(Login));
            }

            var result = await _userManager.VerifyTwoFactorTokenAsync(user, "Email", code);

            _logger.LogInformation($"🔍 Code verification for {email}: Code='{code}', Result={result}");

            if (result)
            {
                // FIRST: Clear admin authentication for member login
                if (User.Identity?.IsAuthenticated == true)
                {
                    _logger.LogInformation("Member verification succeeded - clearing admin authentication for AJAX");
                    
                    // For AJAX requests, we CAN'T use SignOutAsync because it causes browser redirects
                    // which break the AJAX call. Instead, just clear the session context.
                    try
                    {
                        // Clear the HttpContext.User to break the authentication loop
                        HttpContext.User = new System.Security.Claims.ClaimsPrincipal();
                        
                        // Clear admin-specific session data only
                        HttpContext.Session.Remove("AdminSessionData");
                        
                        _logger.LogInformation("Admin authentication cleared for member login (AJAX safe)");
                    }
                    catch (Exception ex)
                    {
                        _logger.LogWarning(ex, "Error clearing admin during member verification, continuing anyway");
                    }
                }
                
                // Invalidate the code after successful verification
                await _userManager.ResetAuthenticatorKeyAsync(user);
            }

            if (Request.Headers["X-Requested-With"] == "XMLHttpRequest")
            {
                if (result)
                {
                    var member = await _context.Members.FirstOrDefaultAsync(m => m.Email == email);
                    if (member != null)
                    {
                        // Create member session for AJAX requests too
                        var memberSession = MemberSession.Create(
                            member.Id, 
                            $"{member.FirstName} {member.LastName}", 
                            member.Email
                        );
                        await SetMemberSession(memberSession);
                        
                        var optionsUrl = Url.Action("Options");
                        return Json(new { success = true, message = _localizer["ApiValidationGood"].Value, redirectUrl = optionsUrl });
                    }
                }
                return Json(new { success = false, message = _localizer["ApiValidationBad"].Value });
            }
            else
            {
                if (result)
                {
                    // Find the member and create session, then redirect to options page
                    var member = await _context.Members.FirstOrDefaultAsync(m => m.Email == email);
                    if (member != null)
                    {
                        // Create member session
                        var memberSession = MemberSession.Create(
                            member.Id, 
                            $"{member.FirstName} {member.LastName}", 
                            member.Email
                        );
                        await SetMemberSession(memberSession);
                        
                        _logger.LogInformation("Member {MemberName} (ID: {MemberId}) successfully verified and redirected to options page", 
                            memberSession.MemberName, member.Id);
                        
                        return RedirectToAction("Options");
                    }
                    else
                    {
                        TempData["VerificationResult"] = _localizer["ApiValidationBad"].Value;
                        _logger.LogWarning($"Member not found for email: {email}");
                    }
                }
                else
                {
                    TempData["VerificationResult"] = _localizer["ApiValidationBad"].Value;
                    _logger.LogInformation("❌ Setting TempData VerificationResult = BAD");
                }

                TempData["VerificationEmail"] = email; // Keep email for display
                TempData["CodeSent"] = null; // Clear the CodeSent flag
                return RedirectToAction(nameof(Login));
            }
        }

        /// <summary>
        /// Member options/dashboard page - displays member information and navigation options
        /// </summary>
        [HttpGet]
        public async Task<IActionResult> Options()
        {
            // Validate member session
            var sessionValidation = ValidateMemberSessionAccess();
            if (sessionValidation != null)
                return sessionValidation;

            var memberSession = GetCurrentMemberSession();
            if (memberSession == null)
            {
                _logger.LogWarning("Options page accessed without valid member session");
                return RedirectToAction(nameof(Login));
            }

            try
            {
                // Get member details with related data
                var member = await _context.Members
                    .Include(m => m.RegistrationType)
                    .Include(m => m.Province)
                    .FirstOrDefaultAsync(m => m.Id == memberSession.MemberId);

                if (member == null)
                {
                    _logger.LogWarning("Member not found for session: {MemberId}", memberSession.MemberId);
                    ClearMemberSession();
                    SetErrorMessage("MemberNotFound");
                    return RedirectToAction(nameof(Login));
                }

                // Map to view model
                var viewModel = new MemberDetailsViewModel
                {
                    Id = member.Id,
                    FirstName = member.FirstName,
                    LastName = member.LastName,
                    Email = member.Email,
                    Phone = member.Phone ?? string.Empty,
                    DateOfBirth = member.DateOfBirth,
                    Address = member.Address ?? string.Empty,
                    City = member.City ?? string.Empty,
                    Province = member.Province?.DisplayNameKey != null ? _localizer[member.Province.DisplayNameKey].Value : string.Empty,
                    PostalCode = member.PostalCode ?? string.Empty,
                    RegistrationTypeName = member.RegistrationType?.DisplayNameKey != null ? _localizer[member.RegistrationType.DisplayNameKey].Value : string.Empty,
                    DateCreated = member.DateCreated,
                    IsActive = member.IsActive,
                    HQc_Id = member.HQc_Id
                };

                _logger.LogInformation("Member options page accessed by {MemberName} (ID: {MemberId})", 
                    memberSession.MemberName, memberSession.MemberId);

                return View(viewModel);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error loading member options for member {MemberId}", memberSession.MemberId);
                SetErrorMessage("ErrorLoadingMemberData");
                return RedirectToAction(nameof(Login));
            }
        }

        /// <summary>
        /// Member dashboard page with event subscriptions and member information
        /// </summary>
        [HttpGet]
        public async Task<IActionResult> Dashboard()
        {
            // Validate member session
            var sessionValidation = ValidateMemberSessionAccess();
            if (sessionValidation != null)
                return sessionValidation;

            var memberSession = GetCurrentMemberSession();
            if (memberSession == null)
            {
                _logger.LogWarning("Dashboard page accessed without valid member session");
                return RedirectToAction(nameof(Login));
            }

            try
            {
                // Get member details
                var member = await _context.Members
                    .Include(m => m.RegistrationType)
                    .Include(m => m.Province)
                    .FirstOrDefaultAsync(m => m.Id == memberSession.MemberId);

                if (member == null)
                {
                    _logger.LogWarning("Member not found for session: {MemberId}", memberSession.MemberId);
                    ClearMemberSession();
                    SetErrorMessage("MemberNotFound");
                    return RedirectToAction(nameof(Login));
                }

                // Get member's event registrations with event details
                var eventRegistrations = await _context.EventRegistrations
                    .Include(er => er.Event)
                    .ThenInclude(e => e.EventCategory)
                    .Where(er => er.MemberId == memberSession.MemberId && 
                                er.Status != RegistrationStatus.Cancelled &&
                                er.Event.IsPublished)
                    .OrderBy(er => er.Event.StartDate)
                    .ToListAsync();

                // Separate upcoming and past events
                var now = DateTime.Now;
                var upcomingRegistrations = eventRegistrations
                    .Where(er => er.Event.StartDate >= now)
                    .ToList();
                
                var pastRegistrations = eventRegistrations
                    .Where(er => er.Event.StartDate < now)
                    .OrderByDescending(er => er.Event.StartDate)
                    .Take(5) // Show last 5 past events
                    .ToList();

                // Create view model
                var viewModel = new MemberDashboardViewModel
                {
                    Member = new MemberDetailsViewModel
                    {
                        Id = member.Id,
                        FirstName = member.FirstName,
                        LastName = member.LastName,
                        Email = member.Email,
                        Phone = member.Phone ?? string.Empty,
                        DateOfBirth = member.DateOfBirth,
                        Address = member.Address ?? string.Empty,
                        City = member.City ?? string.Empty,
                        Province = member.Province?.DisplayNameKey != null ? _localizer[member.Province.DisplayNameKey].Value : string.Empty,
                        PostalCode = member.PostalCode ?? string.Empty,
                        RegistrationTypeName = member.RegistrationType?.DisplayNameKey != null ? _localizer[member.RegistrationType.DisplayNameKey].Value : string.Empty,
                        DateCreated = member.DateCreated,
                        IsActive = member.IsActive,
                        HQc_Id = member.HQc_Id
                    },
                    UpcomingEventRegistrations = upcomingRegistrations.Select(er => new EventRegistrationViewModel
                    {
                        RegistrationId = er.Id,
                        EventId = er.Event.Id,
                        EventTitle = er.Event.Title,
                        EventDescription = er.Event.Description,
                        EventStartDate = er.Event.StartDate,
                        EventEndDate = er.Event.EndDate,
                        EventLocation = er.Event.Location,
                        IsAllDay = er.Event.IsAllDay,
                        CategoryName = er.Event.EventCategory?.DisplayNameKey != null ? _localizer[er.Event.EventCategory.DisplayNameKey].Value : "",
                        CategoryColor = er.Event.EventCategory?.Color ?? "#6f42c1",
                        Status = er.Status,
                        StatusDisplay = er.StatusDisplay,
                        RegistrationDate = er.RegistrationDate,
                        GuestCount = er.GuestCount,
                        Notes = er.MemberNotes,
                        CanBeCancelled = er.CanBeCancelled,
                        DateRangeDisplay = er.Event.DateRangeDisplay
                    }).ToList(),
                    PastEventRegistrations = pastRegistrations.Select(er => new EventRegistrationViewModel
                    {
                        RegistrationId = er.Id,
                        EventId = er.Event.Id,
                        EventTitle = er.Event.Title,
                        EventDescription = er.Event.Description,
                        EventStartDate = er.Event.StartDate,
                        EventEndDate = er.Event.EndDate,
                        EventLocation = er.Event.Location,
                        IsAllDay = er.Event.IsAllDay,
                        CategoryName = er.Event.EventCategory?.DisplayNameKey != null ? _localizer[er.Event.EventCategory.DisplayNameKey].Value : "",
                        CategoryColor = er.Event.EventCategory?.Color ?? "#6f42c1",
                        Status = er.Status,
                        StatusDisplay = er.StatusDisplay,
                        RegistrationDate = er.RegistrationDate,
                        GuestCount = er.GuestCount,
                        Notes = er.MemberNotes,
                        CanBeCancelled = false, // Past events cannot be cancelled
                        DateRangeDisplay = er.Event.DateRangeDisplay,
                        Attended = er.Attended
                    }).ToList()
                };

                _logger.LogInformation("Member dashboard accessed by {MemberName} (ID: {MemberId})", 
                    memberSession.MemberName, memberSession.MemberId);

                return View(viewModel);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error loading member dashboard for member {MemberId}", memberSession.MemberId);
                SetErrorMessage("ErrorLoadingMemberData");
                return RedirectToAction(nameof(Login));
            }
        }

        [HttpGet]
        public async Task<IActionResult> Edit(int memberId)
        {
            // Check if admin or member session access
            var isAdmin = await IsUserAdminAsync();
            ViewBag.IsAdmin = isAdmin;
            
            // If not admin, validate member session access
            if (!isAdmin)
            {
                var sessionValidation = ValidateMemberSessionAccess();
                if (sessionValidation != null)
                    return sessionValidation;
                
                var memberSession = GetCurrentMemberSession();
                if (memberSession == null || memberSession.MemberId != memberId)
                {
                    _logger.LogWarning("Unauthorized edit attempt: Member {MemberId} tried to edit member {TargetMemberId}", 
                        memberSession?.MemberId ?? 0, memberId);
                    SetErrorMessage("UnauthorizedAccess");
                    return RedirectToAction(nameof(Login));
                }
            }
            
            var member = await _context.Members.FirstOrDefaultAsync(m => m.Id == memberId);
            if (member == null)
            {
                SetErrorMessage("MemberNotFound");
                return RedirectToAction(isAdmin ? "Members" : nameof(Login), isAdmin ? "Admin" : null);
            }

            // Explicitly populate the view model
            var viewModel = new MemberRegistrationViewModel
            {
                HQc_Id = member.HQc_Id,
                FirstName = member.FirstName,
                LastName = member.LastName,
                DateOfBirth = member.DateOfBirth,
                Address = member.Address,
                City = member.City,
                PostalCode = member.PostalCode,
                Phone = member.Phone,
                Email = member.Email,
                GenderId = member.GenderId,
                ProvinceId = member.ProvinceId,
                PhoneTypeId = member.PhoneTypeId,
                RegistrationTypeId = member.RegistrationTypeId,
                Parents = new List<ParentViewModel>(),
                EmergencyContact = new EmergencyContactViewModel()
            };

            // Load related data
            var parents = await _context.Parents.Where(p => p.MemberId == memberId).ToListAsync();
            foreach (var parent in parents)
            {
                viewModel.Parents.Add(new ParentViewModel
                {
                    FirstName = parent.FirstName,
                    LastName = parent.LastName,
                    ParentType = parent.ParentType,
                    Phone = parent.Phone,
                    Email = parent.Email
                });
            }

            var emergencyContact = await _context.EmergencyContacts.FirstOrDefaultAsync(ec => ec.MemberId == memberId);
            if (emergencyContact != null)
            {
                viewModel.EmergencyContact = new EmergencyContactViewModel
                {
                    FirstName = emergencyContact.FirstName,
                    LastName = emergencyContact.LastName,
                    RelationToUser = emergencyContact.RelationToUser,
                    Phone = emergencyContact.Phone,
                    Email = emergencyContact.Email
                };
            }

            // Populate dropdown lists (after setting IDs)
            viewModel.Provinces = await _context.Provinces
                .Select(p => new SelectListItem
                {
                    Value = p.Id.ToString(),
                    Text = p.DisplayNameKey,
                    Selected = p.Id == viewModel.ProvinceId
                }).ToListAsync();

            viewModel.Genders = await _context.Genders
                .Select(g => new SelectListItem
                {
                    Value = g.Id.ToString(),
                    Text = g.DisplayNameKey,
                    Selected = g.Id == viewModel.GenderId
                }).ToListAsync();

            viewModel.PhoneTypes = await _context.PhoneTypes
                .Select(pt => new SelectListItem
                {
                    Value = pt.Id.ToString(),
                    Text = pt.DisplayNameKey,
                    Selected = pt.Id == viewModel.PhoneTypeId
                }).ToListAsync();

            viewModel.RegistrationTypes = await _context.RegistrationTypes
                .Select(rt => new SelectListItem
                {
                    Value = rt.Id.ToString(),
                    Text = rt.DisplayNameKey,
                    Group = new SelectListGroup { Name = rt.DescriptionKey },
                    Selected = rt.Id == viewModel.RegistrationTypeId
                }).ToListAsync();

            // Set environment settings
            viewModel.EnvironmentSettings = _environmentSettings;

            // Debug logging
            _logger.LogWarning($"DEBUG: Edit form for memberId={memberId}, FirstName={viewModel.FirstName}, LastName={viewModel.LastName}, Email={viewModel.Email}, Phone={viewModel.Phone}, ProvinceId={viewModel.ProvinceId}, GenderId={viewModel.GenderId}, PhoneTypeId={viewModel.PhoneTypeId}, RegistrationTypeId={viewModel.RegistrationTypeId}, ParentsCount={viewModel.Parents.Count}, EmergencyContactFirstName={viewModel.EmergencyContact.FirstName}");

            // Set edit mode flags
            ViewBag.IsEditMode = true;
            ViewBag.MemberId = memberId;
            ViewBag.ShowReturnToOptions = !isAdmin; // Show return to options only for member sessions

            // Clear ModelState to avoid it overwriting the view model
            ModelState.Clear();

            return View("Register", viewModel);
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> UpdateMember(int memberId, MemberRegistrationViewModel viewModel)
        {
            // Set admin flag for conditional field visibility
            var isAdmin = await IsUserAdminAsync();
            ViewBag.IsAdmin = isAdmin;
            
            // If not admin, validate member session access
            if (!isAdmin)
            {
                var sessionValidation = ValidateMemberSessionAccess();
                if (sessionValidation != null)
                    return sessionValidation;
                
                var memberSession = GetCurrentMemberSession();
                if (memberSession == null || memberSession.MemberId != memberId)
                {
                    _logger.LogWarning("Unauthorized update attempt: Member {MemberId} tried to update member {TargetMemberId}", 
                        memberSession?.MemberId ?? 0, memberId);
                    SetErrorMessage("UnauthorizedAccess");
                    return RedirectToAction(nameof(Login));
                }
            }
            
            if (!ModelState.IsValid)
            {
                // Repopulate lists for the view
                viewModel.Provinces = await _context.Provinces
                    .Select(p => new SelectListItem
                    {
                        Value = p.Id.ToString(),
                        Text = p.DisplayNameKey,
                        Selected = p.Id == viewModel.ProvinceId
                    }).ToListAsync();

                viewModel.Genders = await _context.Genders
                    .Select(g => new SelectListItem
                    {
                        Value = g.Id.ToString(),
                        Text = g.DisplayNameKey,
                        Selected = g.Id == viewModel.GenderId
                    }).ToListAsync();

                viewModel.PhoneTypes = await _context.PhoneTypes
                    .Select(pt => new SelectListItem
                    {
                        Value = pt.Id.ToString(),
                        Text = pt.DisplayNameKey,
                        Selected = pt.Id == viewModel.PhoneTypeId
                    }).ToListAsync();

                viewModel.RegistrationTypes = await _context.RegistrationTypes
                    .Select(rt => new SelectListItem
                    {
                        Value = rt.Id.ToString(),
                        Text = rt.DisplayNameKey,
                        Group = new SelectListGroup { Name = rt.DescriptionKey },
                        Selected = rt.Id == viewModel.RegistrationTypeId
                    }).ToListAsync();

                viewModel.EnvironmentSettings = _environmentSettings;
                ViewBag.IsEditMode = true;
                ViewBag.MemberId = memberId;

                // Clear ModelState to ensure fields are pre-filled
                ModelState.Clear();

                return View("Register", viewModel);
            }

            // Begin atomic transaction
            await using var transaction = await _context.Database.BeginTransactionAsync();
            try
            {
                var member = await _context.Members
                    .FirstOrDefaultAsync(m => m.Id == memberId);

                if (member == null)
                {
                    SetErrorMessage("MemberNotFound");
                    return RedirectToAction(isAdmin ? "Members" : nameof(Login), isAdmin ? "Admin" : null);
                }

                // Track changes for logging
                var changes = new List<string>();

                // Update member properties
                if (member.HQc_Id != viewModel.HQc_Id)
                {
                    // Check for duplicate HQc_Id (only if not null and not empty)
                    if (!string.IsNullOrWhiteSpace(viewModel.HQc_Id))
                    {
                        var existingMemberWithHQcId = await _context.Members
                            .FirstOrDefaultAsync(m => m.HQc_Id == viewModel.HQc_Id && m.Id != memberId);
                        
                        if (existingMemberWithHQcId != null)
                        {
                            ModelState.AddModelError("HQc_Id", _localizer["DuplicateHQcIdUpdateError"]);
                            // Don't proceed with update - will return to form with error
                        }
                        else
                        {
                            changes.Add($"HQc_Id: '{member.HQc_Id}' → '{viewModel.HQc_Id}'");
                            member.HQc_Id = viewModel.HQc_Id;
                        }
                    }
                    else
                    {
                        // Setting to null/empty is always allowed
                        changes.Add($"HQc_Id: '{member.HQc_Id}' → '{viewModel.HQc_Id}'");
                        member.HQc_Id = viewModel.HQc_Id;
                    }
                }

                if (member.FirstName != viewModel.FirstName)
                {
                    changes.Add($"FirstName: '{member.FirstName}' → '{viewModel.FirstName}'");
                    member.FirstName = viewModel.FirstName;
                }

                if (member.LastName != viewModel.LastName)
                {
                    changes.Add($"LastName: '{member.LastName}' → '{viewModel.LastName}'");
                    member.LastName = viewModel.LastName;
                }

                if (member.DateOfBirth != viewModel.DateOfBirth)
                {
                    changes.Add($"DateOfBirth: '{member.DateOfBirth:yyyy-MM-dd}' → '{viewModel.DateOfBirth?.ToString("yyyy-MM-dd") ?? "null"}'");
                    member.DateOfBirth = viewModel.DateOfBirth ?? member.DateOfBirth;
                }

                // Check for potential name + date of birth duplicates (log warning but don't prevent)
                if (member.LastName != viewModel.LastName || member.DateOfBirth != viewModel.DateOfBirth)
                {
                    var existingMemberWithSameNameAndDOB = viewModel.DateOfBirth.HasValue ? await _context.Members
                        .FirstOrDefaultAsync(m => m.LastName.ToLower() == viewModel.LastName.ToLower() 
                                               && m.DateOfBirth.Date == viewModel.DateOfBirth.Value.Date 
                                               && m.Id != memberId) : null;
                    
                    if (existingMemberWithSameNameAndDOB != null)
                    {
                        _logger.LogWarning($"Member {memberId} updated to have same name and date of birth as existing member {existingMemberWithSameNameAndDOB.Id}. Names: '{viewModel.LastName}', DOB: '{viewModel.DateOfBirth?.ToString("yyyy-MM-dd") ?? "null"}'");
                    }
                }

                if (member.Address != viewModel.Address)
                {
                    changes.Add($"Address: '{member.Address}' → '{viewModel.Address}'");
                    member.Address = viewModel.Address;
                }

                if (member.City != viewModel.City)
                {
                    changes.Add($"City: '{member.City}' → '{viewModel.City}'");
                    member.City = viewModel.City;
                }

                if (member.PostalCode != viewModel.PostalCode)
                {
                    changes.Add($"PostalCode: '{member.PostalCode}' → '{viewModel.PostalCode}'");
                    member.PostalCode = viewModel.PostalCode;
                }

                if (member.Phone != viewModel.Phone)
                {
                    changes.Add($"Phone: '{member.Phone}' → '{viewModel.Phone}'");
                    member.Phone = viewModel.Phone;
                }

                if (member.Email != viewModel.Email)
                {
                    // Check for duplicate email (exclude current member)
                    var existingMemberWithEmail = await _context.Members
                        .FirstOrDefaultAsync(m => m.Email.ToLower() == viewModel.Email.ToLower() && m.Id != memberId);
                    
                    if (existingMemberWithEmail != null)
                    {
                        ModelState.AddModelError("Email", _localizer["DuplicateEmailUpdateError"]);
                        // Don't proceed with update - will return to form with error
                    }
                    else
                    {
                        changes.Add($"Email: '{member.Email}' → '{viewModel.Email}'");
                        member.Email = viewModel.Email;
                    }
                }

                if (member.GenderId != viewModel.GenderId)
                {
                    changes.Add($"GenderId: {member.GenderId} → {viewModel.GenderId}");
                    member.GenderId = viewModel.GenderId;
                }

                if (member.ProvinceId != viewModel.ProvinceId)
                {
                    changes.Add($"ProvinceId: {member.ProvinceId} → {viewModel.ProvinceId}");
                    member.ProvinceId = viewModel.ProvinceId;
                }

                if (member.PhoneTypeId != viewModel.PhoneTypeId)
                {
                    changes.Add($"PhoneTypeId: {member.PhoneTypeId} → {viewModel.PhoneTypeId}");
                    member.PhoneTypeId = viewModel.PhoneTypeId;
                }

                // Check if any validation errors occurred during duplicate checking
                if (!ModelState.IsValid)
                {
                    // Repopulate lists for the view
                    viewModel.Provinces = await _context.Provinces
                        .Select(p => new SelectListItem
                        {
                            Value = p.Id.ToString(),
                            Text = p.DisplayNameKey,
                            Selected = p.Id == viewModel.ProvinceId
                        }).ToListAsync();

                    viewModel.Genders = await _context.Genders
                        .Select(g => new SelectListItem
                        {
                            Value = g.Id.ToString(),
                            Text = g.DisplayNameKey,
                            Selected = g.Id == viewModel.GenderId
                        }).ToListAsync();

                    viewModel.PhoneTypes = await _context.PhoneTypes
                        .Select(pt => new SelectListItem
                        {
                            Value = pt.Id.ToString(),
                            Text = pt.DisplayNameKey,
                            Selected = pt.Id == viewModel.PhoneTypeId
                        }).ToListAsync();

                    viewModel.RegistrationTypes = await _context.RegistrationTypes
                        .Select(rt => new SelectListItem
                        {
                            Value = rt.Id.ToString(),
                            Text = rt.DisplayNameKey,
                            Group = new SelectListGroup { Name = rt.DescriptionKey },
                            Selected = rt.Id == viewModel.RegistrationTypeId
                        }).ToListAsync();

                    viewModel.EnvironmentSettings = _environmentSettings;
                    ViewBag.IsEditMode = true;
                    ViewBag.MemberId = memberId;
                    ViewBag.IsAdmin = await IsUserAdminAsync();
                    return View("Register", viewModel);
                }

                // Handle Parents (for Junior registrations)
                if (viewModel.RegistrationTypeId == 1)
                {
                    var existingParents = await _context.Parents.Where(p => p.MemberId == memberId).ToListAsync();

                    // Remove existing parents that are no longer in the view model
                    var parentsToRemove = existingParents.Where(p => !viewModel.Parents.Any(vp =>
                        vp.FirstName == p.FirstName && vp.LastName == p.LastName && vp.ParentType == p.ParentType)).ToList();

                    foreach (var parent in parentsToRemove)
                    {
                        changes.Add($"Removed Parent: {parent.FirstName} {parent.LastName}");
                        _context.Parents.Remove(parent);
                    }

                    // Update or add parents
                    for (int i = 0; i < viewModel.Parents.Count; i++)
                    {
                        var parentVm = viewModel.Parents[i];
                        if (parentVm != null && !string.IsNullOrWhiteSpace(parentVm.FirstName))
                        {
                            var existingParent = existingParents.FirstOrDefault(p =>
                                p.FirstName == parentVm.FirstName && p.LastName == parentVm.LastName && p.ParentType == parentVm.ParentType);

                            if (existingParent != null)
                            {
                                // Update existing parent
                                if (existingParent.Phone != parentVm.Phone)
                                {
                                    changes.Add($"Parent {i + 1} Phone: '{existingParent.Phone}' → '{parentVm.Phone}'");
                                    existingParent.Phone = parentVm.Phone;
                                }
                                if (existingParent.Email != parentVm.Email)
                                {
                                    changes.Add($"Parent {i + 1} Email: '{existingParent.Email}' → '{parentVm.Email}'");
                                    existingParent.Email = parentVm.Email;
                                }
                            }
                            else
                            {
                                // Add new parent
                                var newParent = new Parent
                                {
                                    FirstName = parentVm.FirstName,
                                    LastName = parentVm.LastName,
                                    ParentType = parentVm.ParentType,
                                    Phone = parentVm.Phone,
                                    Email = parentVm.Email,
                                    MemberId = member.Id
                                };
                                _context.Parents.Add(newParent);
                                changes.Add($"Added Parent: {newParent.FirstName} {newParent.LastName}");
                            }
                        }
                    }
                }
                else
                {
                    // Handle Emergency Contact (for non-Junior registrations)
                    var existingEmergencyContact = await _context.EmergencyContacts.FirstOrDefaultAsync(ec => ec.MemberId == memberId);

                    if (existingEmergencyContact != null)
                    {
                        var ecVm = viewModel.EmergencyContact;

                        if (existingEmergencyContact.FirstName != ecVm.FirstName)
                        {
                            changes.Add($"EmergencyContact FirstName: '{existingEmergencyContact.FirstName}' → '{ecVm.FirstName}'");
                            existingEmergencyContact.FirstName = ecVm.FirstName;
                        }
                        if (existingEmergencyContact.LastName != ecVm.LastName)
                        {
                            changes.Add($"EmergencyContact LastName: '{existingEmergencyContact.LastName}' → '{ecVm.LastName}'");
                            existingEmergencyContact.LastName = ecVm.LastName;
                        }
                        if (existingEmergencyContact.Phone != ecVm.Phone)
                        {
                            changes.Add($"EmergencyContact Phone: '{existingEmergencyContact.Phone}' → '{ecVm.Phone}'");
                            existingEmergencyContact.Phone = ecVm.Phone;
                        }
                        if (existingEmergencyContact.RelationToUser != ecVm.RelationToUser)
                        {
                            changes.Add($"EmergencyContact RelationToUser: '{existingEmergencyContact.RelationToUser}' → '{ecVm.RelationToUser}'");
                            existingEmergencyContact.RelationToUser = ecVm.RelationToUser;
                        }
                        if (existingEmergencyContact.Email != ecVm.Email)
                        {
                            changes.Add($"EmergencyContact Email: '{existingEmergencyContact.Email}' → '{ecVm.Email}'");
                            existingEmergencyContact.Email = ecVm.Email;
                        }
                    }
                    else
                    {
                        // Create new emergency contact
                        var newEc = new EmergencyContact
                        {
                            FirstName = viewModel.EmergencyContact.FirstName,
                            LastName = viewModel.EmergencyContact.LastName,
                            RelationToUser = viewModel.EmergencyContact.RelationToUser,
                            Phone = viewModel.EmergencyContact.Phone,
                            Email = viewModel.EmergencyContact.Email,
                            MemberId = member.Id
                        };
                        _context.EmergencyContacts.Add(newEc);
                        changes.Add($"Added EmergencyContact: {newEc.FirstName} {newEc.LastName}");
                    }
                }

                // Only save and log if there were changes
                if (changes.Any())
                {
                    await _context.SaveChangesAsync();

                    // Create log entry
                    var logEntry = new MemberLog
                    {
                        MemberId = member.Id,
                        LogDate = DateTime.UtcNow,
                        Description = $"Member profile updated. Changes: {string.Join("; ", changes)}",
                        EditorId = member.Id, // Using member ID as editor ID for self-edits
                        EditorName = $"{member.FirstName} {member.LastName}"
                    };

                    _context.MemberLogs.Add(logEntry);
                    await _context.SaveChangesAsync();

                    await transaction.CommitAsync();

                    SetSuccessMessage("ProfileUpdatedSuccessfully");
                }
                else
                {
                    await transaction.RollbackAsync();
                    TempData["InfoMessage"] = _localizer["NoChangesToProfile"].Value;
                }

                // Redirect based on user type - admin goes to member list, member goes to dashboard
                if (isAdmin)
                {
                    return RedirectToAction("Members", "Admin");
                }
                else
                {
                    return RedirectToAction(nameof(Options));
                }
            }
            catch (Exception ex)
            {
                await transaction.RollbackAsync();
                _logger.LogError(ex, "Error updating member {MemberId}", memberId);
                ModelState.AddModelError(string.Empty, _localizer["ErrorUpdatingProfile"].Value);

                // Repopulate lists for the view
                viewModel.Provinces = await _context.Provinces
                    .Select(p => new SelectListItem
                    {
                        Value = p.Id.ToString(),
                        Text = p.DisplayNameKey,
                        Selected = p.Id == viewModel.ProvinceId
                    }).ToListAsync();

                viewModel.Genders = await _context.Genders
                    .Select(g => new SelectListItem
                    {
                        Value = g.Id.ToString(),
                        Text = g.DisplayNameKey,
                        Selected = g.Id == viewModel.GenderId
                    }).ToListAsync();

                viewModel.PhoneTypes = await _context.PhoneTypes
                    .Select(pt => new SelectListItem
                    {
                        Value = pt.Id.ToString(),
                        Text = pt.DisplayNameKey,
                        Selected = pt.Id == viewModel.PhoneTypeId
                    }).ToListAsync();

                viewModel.RegistrationTypes = await _context.RegistrationTypes
                    .Select(rt => new SelectListItem
                    {
                        Value = rt.Id.ToString(),
                        Text = rt.DisplayNameKey,
                        Group = new SelectListGroup { Name = rt.DescriptionKey },
                        Selected = rt.Id == viewModel.RegistrationTypeId
                    }).ToListAsync();

                viewModel.EnvironmentSettings = _environmentSettings;
                ViewBag.IsEditMode = true;
                ViewBag.MemberId = memberId;
                ViewBag.ShowReturnToOptions = !isAdmin; // Show return to options only for member sessions
                return View("Register", viewModel);
            }
        }

        // CalendarReadOnly action for members
        [HttpGet]
        public async Task<IActionResult> CalendarReadOnly()
        {
            // Validate member session access
            var sessionValidation = ValidateMemberSessionAccess();
            if (sessionValidation != null)
                return sessionValidation;

            var memberSession = GetCurrentMemberSession();
            if (memberSession == null)
            {
                _logger.LogWarning("Calendar read-only page accessed without valid member session");
                return RedirectToAction(nameof(Login));
            }

            try
            {
                // Get event categories for the filter dropdown (read-only, sorted alphabetically with Other at end)
                var eventCategories = await _eventService.GetAllEventCategoriesSortedAsync();
                
                // Get upcoming events for the sidebar
                var upcomingEvents = await _eventService.GetUpcomingEventsAsync(5);
                
                // Get recent events for the sidebar
                var recentEvents = await _eventService.GetRecentEventsAsync(5);
                
                // Get statistics for published events only (members should only see published events)
                var publishedEvents = await _eventService.GetEventCountAsync(true);
                
                ViewBag.EventCategories = eventCategories;
                ViewBag.UpcomingEvents = upcomingEvents;
                ViewBag.RecentEvents = recentEvents;
                ViewBag.PublishedEvents = publishedEvents;
                ViewBag.IsReadOnly = true; // Flag to disable admin-only features
                ViewBag.MemberSession = memberSession;
                
                // Add empty state support for upcoming and recent events
                ViewBag.UpcomingEventsEmptyState = _emptyStateService.GetEmptyState("events", "upcoming");
                ViewBag.RecentEventsEmptyState = _emptyStateService.GetEmptyState("events", "recent");
                
                _logger.LogInformation("Calendar read-only page accessed by {MemberName} (ID: {MemberId})", 
                    memberSession.MemberName, memberSession.MemberId);

                return View();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error loading calendar for member {MemberId}", memberSession.MemberId);
                SetErrorMessage("ErrorLoadingCalendar");
                return RedirectToAction(nameof(Options));
            }
        }

        // GetCalendarEvents for read-only member access
        [HttpGet]
        public async Task<IActionResult> GetMemberCalendarEvents(DateTime? start = null, DateTime? end = null, int? categoryId = null)
        {
            // Validate member session access
            var sessionValidation = ValidateMemberSessionAccess();
            if (sessionValidation != null)
                return Json(new { error = _localizer["ApiUnauthorizedAccess"].Value });

            var memberSession = GetCurrentMemberSession();
            if (memberSession == null)
            {
                return Json(new { error = _localizer["ApiSessionExpired"].Value });
            }

            var startDate = start ?? DateTime.Now.AddMonths(-1);
            var endDate = end ?? DateTime.Now.AddMonths(6);

            // Get only published events for members (read-only access)
            var events = await _eventService.GetCalendarEventsJsonAsync(startDate, endDate, true, categoryId); // true = published only
            return Json(events);
        }

        /// <summary>
        /// Logs out the current member by clearing their session
        /// </summary>
        [HttpGet]
        [HttpPost]
        public IActionResult Logout()
        {
            try
            {
                // Clear the member session
                HttpContext.Session.Remove("MemberSessionData");
                
                // Clear the entire session for security
                HttpContext.Session.Clear();
                
                // Log the logout action
                _logger.LogInformation("Member logged out successfully from IP: {IPAddress}", 
                    HttpContext.Connection.RemoteIpAddress?.ToString());
                
                // Set a temporary success message
                TempData["InfoMessage"] = _localizer["LogoutSuccessful"].Value;
                
                // Redirect to home page
                return RedirectToAction("Index", "Home");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error during member logout");
                
                // Even if there's an error, clear the session for security
                try
                {
                    HttpContext.Session.Clear();
                }
                catch
                {
                    // Ignore session clear errors
                }
                
                TempData["InfoMessage"] = _localizer["LogoutCompleted"].Value;
                return RedirectToAction("Index", "Home");
            }
        }
    }
}