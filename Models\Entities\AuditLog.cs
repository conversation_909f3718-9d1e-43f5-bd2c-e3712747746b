using System.ComponentModel.DataAnnotations;

namespace ParaHockeyApp.Models.Entities
{
    /// <summary>
    /// Universal audit log table that tracks all changes made to any entity.
    /// This replaces the entity-specific MemberLog and provides complete audit trails
    /// for Members, AdminUsers, Parents, EmergencyContacts, and all other entities.
    /// </summary>
    public class AuditLog
    {
        /// <summary>
        /// Primary key for the audit log entry
        /// </summary>
        [Key]
        public int Id { get; set; }

        /// <summary>
        /// Type of entity that was modified (e.g., "Member", "AdminUser", "Parent", "EmergencyContact")
        /// </summary>
        [Required]
        [StringLength(50)]
        public string EntityType { get; set; } = string.Empty;

        /// <summary>
        /// Primary key ID of the entity that was affected
        /// </summary>
        [Required]
        public int EntityId { get; set; }

        /// <summary>
        /// Action performed: "Create", "Update", "Delete"
        /// </summary>
        [Required]
        [StringLength(20)]
        public string Action { get; set; } = string.Empty;

        /// <summary>
        /// When the action occurred (UTC)
        /// </summary>
        [Required]
        public DateTime Timestamp { get; set; } = DateTime.UtcNow;

        /// <summary>
        /// Member ID if the action was performed by a member (self-service)
        /// </summary>
        public int? PerformedByMemberId { get; set; }

        /// <summary>
        /// Admin ID if the action was performed by an admin
        /// </summary>
        public int? PerformedByAdminId { get; set; }

        /// <summary>
        /// Source of the action (System, SelfService, AdminPanel)
        /// </summary>
        [Required]
        public ActionSource PerformedBySource { get; set; }

        /// <summary>
        /// Display name of the person who performed the action
        /// </summary>
        [Required]
        [StringLength(101)] // 50 + 1 + 50 for "FirstName LastName"
        public string PerformerName { get; set; } = string.Empty;

        /// <summary>
        /// IP address where the action originated (for security tracking)
        /// </summary>
        [StringLength(45)] // IPv6 can be up to 45 characters
        public string? IPAddress { get; set; }

        /// <summary>
        /// JSON representation of the entity's values before the change
        /// Used for detailed audit trails and rollback capabilities
        /// </summary>
        public string? OldValues { get; set; }

        /// <summary>
        /// JSON representation of the entity's values after the change
        /// </summary>
        public string? NewValues { get; set; }

        /// <summary>
        /// Human-readable description of what changed
        /// e.g., "FirstName: 'John' → 'Jonathan', Email: '<EMAIL>' → '<EMAIL>'"
        /// </summary>
        [StringLength(1000)]
        public string? Description { get; set; }

        /// <summary>
        /// Navigation property to the member who performed the action (if applicable)
        /// </summary>
        public virtual Member? PerformedByMember { get; set; }

        /// <summary>
        /// Navigation property to the admin who performed the action (if applicable)
        /// </summary>
        public virtual AdminUser? PerformedByAdmin { get; set; }

        /// <summary>
        /// Display formatted timestamp
        /// </summary>
        public string TimestampFormatted => Timestamp.ToString("yyyy-MM-dd HH:mm:ss UTC");

        /// <summary>
        /// Short description for display in lists
        /// </summary>
        public string ShortDescription => Description?.Length > 100 
            ? Description.Substring(0, 97) + "..." 
            : Description ?? $"{Action} {EntityType}";
    }
}