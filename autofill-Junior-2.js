﻿// Auto-fill registration form for Chantal2 Caron2 (Junior)
document.getElementById('FirstName').value = 'Chantal2';
document.getElementById('LastName').value = 'Caron2';
document.getElementById('Email').value = '<EMAIL>';
document.getElementById('Phone').value = '(*************';
document.getElementById('DateOfBirth').value = '2010-07-04';
document.getElementById('Address').value = '123 rue Principale';
document.getElementById('City').value = 'Trois-Rivières';
document.getElementById('PostalCode').value = 'J7G 1M2';
document.getElementById('RegistrationTypeId').value = '1';
document.getElementById('GenderId').value = '2';
document.getElementById('ProvinceId').value = '2';
document.getElementById('PhoneTypeId').value = '1';
// Parent information for Junior
document.getElementById('Parents_0__FirstName').value = 'Chantal_Parent2';
document.getElementById('Parents_0__LastName').value = 'Caron2';
document.getElementById('Parents_0__Email').value = '<EMAIL>';
document.getElementById('Parents_0__Phone').value = '(*************';
