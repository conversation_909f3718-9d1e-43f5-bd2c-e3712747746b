namespace ParaHockeyApp.DTOs
{
    /// <summary>
    /// Response DTO containing filter options for a specific filter type
    /// Used by AJAX endpoints to provide dynamic filter dropdown data
    /// </summary>
    public class FilterOptionsResponse
    {
        /// <summary>
        /// The type of filter these options are for (e.g., "registrationType", "province", "city")
        /// </summary>
        public string FilterType { get; set; } = string.Empty;

        /// <summary>
        /// List of available filter options for the specified filter type
        /// </summary>
        public List<FilterOption> Options { get; set; } = new List<FilterOption>();

        /// <summary>
        /// Indicates whether the request was successful
        /// </summary>
        public bool Success { get; set; } = true;

        /// <summary>
        /// Error message if the request failed
        /// </summary>
        public string? ErrorMessage { get; set; }

        /// <summary>
        /// Total count of available options (useful for pagination or limiting)
        /// </summary>
        public int TotalCount { get; set; }

        /// <summary>
        /// Creates a successful response with the specified filter type and options
        /// </summary>
        /// <param name="filterType">The filter type</param>
        /// <param name="options">The list of filter options</param>
        public FilterOptionsResponse(string filterType, List<FilterOption> options)
        {
            FilterType = filterType;
            Options = options;
            TotalCount = options.Count;
            Success = true;
        }

        /// <summary>
        /// Creates an error response with the specified filter type and error message
        /// </summary>
        /// <param name="filterType">The filter type</param>
        /// <param name="errorMessage">The error message</param>
        /// <returns>FilterOptionsResponse with error details</returns>
        public static FilterOptionsResponse CreateError(string filterType, string errorMessage)
        {
            return new FilterOptionsResponse
            {
                FilterType = filterType,
                Success = false,
                ErrorMessage = errorMessage,
                Options = new List<FilterOption>(),
                TotalCount = 0
            };
        }

        /// <summary>
        /// Parameterless constructor for serialization
        /// </summary>
        public FilterOptionsResponse() { }
    }
}