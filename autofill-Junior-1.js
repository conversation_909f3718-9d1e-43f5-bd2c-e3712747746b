﻿// Auto-fill registration form for <PERSON>halie1 Gauthier1 (Junior)
document.getElementById('FirstName').value = 'Nathalie1';
document.getElementById('LastName').value = 'Gauthier1';
document.getElementById('Email').value = '<EMAIL>';
document.getElementById('Phone').value = '(*************';
document.getElementById('DateOfBirth').value = '2010-07-04';
document.getElementById('Address').value = '789 boulevard Saint-Laurent';
document.getElementById('City').value = 'Terrebonne';
document.getElementById('PostalCode').value = 'A7N 9I0';
document.getElementById('RegistrationTypeId').value = '1';
document.getElementById('GenderId').value = '2';
document.getElementById('ProvinceId').value = '1';
document.getElementById('PhoneTypeId').value = '1';
// Parent information for Junior
document.getElementById('Parents_0__FirstName').value = 'Alexandre_Parent1';
document.getElementById('Parents_0__LastName').value = 'Gauthier1';
document.getElementById('Parents_0__Email').value = '<EMAIL>';
document.getElementById('Parents_0__Phone').value = '(*************';
