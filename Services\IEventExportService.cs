using ParaHockeyApp.Models.Entities;

namespace ParaHockeyApp.Services
{
    /// <summary>
    /// Service for exporting event data in various formats
    /// </summary>
    public interface IEventExportService
    {
        /// <summary>
        /// Exports event data to CSV format
        /// </summary>
        /// <param name="events">Events to export</param>
        /// <param name="targetDate">Target date for filename generation</param>
        /// <returns>CSV file content as byte array</returns>
        Task<byte[]> ExportToCsvAsync(List<Event> events, DateTime targetDate);

        /// <summary>
        /// Exports event data to Excel format
        /// </summary>
        /// <param name="events">Events to export</param>
        /// <param name="targetDate">Target date for filename generation</param>
        /// <returns>Excel file content as byte array</returns>
        Task<byte[]> ExportToExcelAsync(List<Event> events, DateTime targetDate);

        /// <summary>
        /// Generates an intelligent filename based on target date and format
        /// </summary>
        /// <param name="targetDate">Target date for the export</param>
        /// <param name="format">Export format (excel/csv)</param>
        /// <returns>Generated filename</returns>
        string GenerateFileName(DateTime targetDate, string format);

        /// <summary>
        /// Gets the MIME type for the specified format
        /// </summary>
        /// <param name="format">Export format (excel/csv)</param>
        /// <returns>MIME type string</returns>
        string GetMimeType(string format);

        /// <summary>
        /// Parses CSV file and returns list of events
        /// </summary>
        /// <param name="stream">File stream</param>
        /// <param name="categoryMappings">Category name to ID mappings</param>
        /// <param name="errors">List to collect parsing errors</param>
        /// <returns>List of parsed events</returns>
        Task<List<Event>> ParseCsvFileAsync(Stream stream, Dictionary<string, int> categoryMappings, List<string> errors);

        /// <summary>
        /// Parses Excel file and returns list of events
        /// </summary>
        /// <param name="stream">File stream</param>
        /// <param name="categoryMappings">Category name to ID mappings</param>
        /// <param name="errors">List to collect parsing errors</param>
        /// <returns>List of parsed events</returns>
        Task<List<Event>> ParseExcelFileAsync(Stream stream, Dictionary<string, int> categoryMappings, List<string> errors);
    }
}