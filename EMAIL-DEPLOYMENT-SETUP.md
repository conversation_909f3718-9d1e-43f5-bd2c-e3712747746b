# Email Configuration Deployment Setup

## Overview
The email verification system uses Office365 SMTP with **IDENTICAL** configuration across ALL environments.

## Working Configuration
**ALL ENVIRONMENTS** use the exact same Office365 SMTP settings:
- **SmtpHost**: `smtp.office365.com`
- **SmtpPort**: `587`
- **Username**: `<EMAIL>`
- **FromEmail**: `<EMAIL>` (must match Username for Office365)
- **FromName**: `Parahockey Verification`
- **Password**: Retrieved from environment variable `Email__Password`

## Single Solution for ALL Environments

### Setup (Same for Dev/Test/Production):
**Set environment variable**: `Email__Password=L@535539113654on`

- **Windows (Development)**: `setx Email__Password "L@535539113654on"`
- **Azure DevOps Pipeline**: Add variable to pipeline or variable group
- **IIS Server**: Set in application settings or web.config

### Verification (Same for all environments):
Check that environment variable is set correctly

## Deployment Checklist

### For ALL Environments:
1. ✅ Code deployed with empty password in config files
2. ⚠️ **REQUIRED**: Set environment variable `Email__Password=L@535539113654on`
3. ✅ Verify email functionality works

## Troubleshooting

### "SMTP configuration not complete" Error
- Check that environment variable `Email__Password` is set correctly
- Verify variable name uses double underscore: `Email__Password` (not `Email:Password`)

### "SendAsDenied" Error
- Ensure `FromEmail` matches `Username` (both should be `<EMAIL>`)
- Do not use `<EMAIL>` as FromEmail with Office365

## Security Notes
- ✅ No passwords stored in source code
- ✅ **IDENTICAL** environment variable solution for all environments
- ✅ Same configuration across all environments