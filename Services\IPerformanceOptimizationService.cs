using ParaHockeyApp.Models.Entities;

namespace ParaHockeyApp.Services;

/// <summary>
/// Service for managing performance optimization features including bundle management,
/// image optimization, critical CSS extraction, and caching strategies.
/// </summary>
public interface IPerformanceOptimizationService
{
    /// <summary>
    /// Generates optimized bundle configuration for JavaScript and CSS assets
    /// </summary>
    Task<BundleConfiguration> GenerateBundleConfigurationAsync();
    
    /// <summary>
    /// Extracts critical CSS for above-the-fold content
    /// </summary>
    Task<CriticalCssResult> ExtractCriticalCssAsync(string pageName, string url);
    
    /// <summary>
    /// Optimizes images with responsive generation and WebP support
    /// </summary>
    Task<ImageOptimizationResult> OptimizeImageAsync(string imagePath, ImageOptimizationOptions options);
    
    /// <summary>
    /// Analyzes database queries for N+1 problems and optimization opportunities
    /// </summary>
    Task<QueryOptimizationResult> AnalyzeDatabaseQueriesAsync();
    
    /// <summary>
    /// Generates appropriate cache headers for different resource types
    /// </summary>
    CacheHeaderConfiguration GetCacheHeaders(string resourceType, string resourcePath);
    
    /// <summary>
    /// Validates Core Web Vitals for a given page
    /// </summary>
    Task<CoreWebVitalsResult> ValidateCoreWebVitalsAsync(string pageName, string url);
}