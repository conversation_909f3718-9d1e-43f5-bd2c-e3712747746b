using System.ComponentModel.DataAnnotations;

namespace ParaHockeyApp.Models.Entities
{
    /// <summary>
    /// Represents a specific finding from a page audit
    /// Contains details about issues discovered and recommended fixes
    /// </summary>
    public class AuditFinding : BaseEntity
    {
        /// <summary>
        /// Foreign key to the audit result this finding belongs to
        /// </summary>
        [Required]
        public int PageAuditResultId { get; set; }

        /// <summary>
        /// Category of the finding (Security, Accessibility, Performance, Localization, etc.)
        /// </summary>
        [Required]
        [StringLength(50)]
        public string Category { get; set; } = string.Empty;

        /// <summary>
        /// Brief description of the issue
        /// </summary>
        [Required]
        [StringLength(500)]
        public string Issue { get; set; } = string.Empty;

        /// <summary>
        /// Severity level of the finding
        /// </summary>
        [Required]
        public Severity Severity { get; set; }

        /// <summary>
        /// Detailed rationale explaining why this is an issue
        /// </summary>
        [StringLength(1000)]
        public string Rationale { get; set; } = string.Empty;

        /// <summary>
        /// Recommended fix or action plan
        /// </summary>
        [StringLength(1000)]
        public string FixPlan { get; set; } = string.Empty;

        /// <summary>
        /// Specific code location or file where the issue was found
        /// </summary>
        [StringLength(500)]
        public string CodeLocation { get; set; } = string.Empty;

        /// <summary>
        /// Whether this finding has been addressed
        /// </summary>
        [Required]
        public bool IsResolved { get; set; } = false;

        /// <summary>
        /// Date when the finding was resolved (if applicable)
        /// </summary>
        public DateTime? ResolvedDate { get; set; }

        /// <summary>
        /// Notes about how the finding was resolved
        /// </summary>
        [StringLength(1000)]
        public string ResolutionNotes { get; set; } = string.Empty;

        /// <summary>
        /// Navigation property to the audit result this finding belongs to
        /// </summary>
        public virtual PageAuditResult PageAuditResult { get; set; } = null!;
    }

    /// <summary>
    /// Severity levels for audit findings
    /// </summary>
    public enum Severity
    {
        /// <summary>
        /// Low priority issue - nice to have fix
        /// </summary>
        Low = 1,

        /// <summary>
        /// Medium priority issue - should be fixed
        /// </summary>
        Medium = 2,

        /// <summary>
        /// High priority issue - must be fixed before release
        /// </summary>
        High = 3,

        /// <summary>
        /// Critical issue - blocks release, immediate attention required
        /// </summary>
        Critical = 4
    }
}