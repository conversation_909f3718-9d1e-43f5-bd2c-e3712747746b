@model IEnumerable<ParaHockeyApp.Models.Entities.Member>
@{
    ViewData["Title"] = SharedLocalizer["ManageMembers"];
}

<!-- Skip navigation links for accessibility -->
<a href="#main-content" class="skip-link visually-hidden-focusable">@SharedLocalizer["SkipToMainContent"]</a>
<a href="#search-filters" class="skip-link visually-hidden-focusable">@SharedLocalizer["SkipToSearchFilters"]</a>
<a href="#member-actions" class="skip-link visually-hidden-focusable">@SharedLocalizer["SkipToMemberActions"]</a>
<a href="#members-table" class="skip-link visually-hidden-focusable">@SharedLocalizer["SkipToMembersTable"]</a>

<main id="main-content" role="main" aria-label="@SharedLocalizer["MemberManagementPage"]">
    <div class="container-fluid mt-4">
        <!-- ARIA live regions for dynamic feedback -->
        <div id="page-status" aria-live="polite" aria-atomic="true" class="visually-hidden"></div>
        <div id="page-errors" aria-live="assertive" aria-atomic="true" class="visually-hidden"></div>

        <!-- Header -->
        <header class="row mb-4" role="banner">
            <div class="col">
                <h1 class="h2 text-primary" id="page-title">
                    <i class="fas fa-users" aria-hidden="true"></i> 
                    @SharedLocalizer["ManageMembers"]
                </h1>
                <nav aria-label="@SharedLocalizer["Breadcrumb"]">
                    <ol class="breadcrumb" role="list">
                        <li class="breadcrumb-item">
                            <a href="@Url.Action("Index", "Admin")" 
                               aria-label="@SharedLocalizer["BackToAdminDashboard"]">
                                @SharedLocalizer["AdminDashboard"]
                            </a>
                        </li>
                        <li class="breadcrumb-item active" aria-current="page">
                            @SharedLocalizer["Members"]
                        </li>
                    </ol>
                </nav>
            </div>
        </header>

        <!-- Search, Filters and Actions -->
        <section id="search-filters" class="row mb-4" aria-labelledby="search-filters-title">
            <div class="col-lg-8 col-12">
                <h2 id="search-filters-title" class="h5 mb-3">
                    <i class="fas fa-search" aria-hidden="true"></i>
                    @SharedLocalizer["SearchAndFilters"]
                </h2>
                
                <!-- Search Form -->
                <form method="get" 
                      action="@Url.Action("Members", "Admin")" 
                      class="mb-3"
                      aria-labelledby="search-form-title"
                      role="search">
                    <div class="input-group">
                        <label for="search-input" class="visually-hidden">@SharedLocalizer["SearchByNameOrEmail"]</label>
                        <input type="text" 
                               id="search-input"
                               name="search" 
                               value="@ViewBag.SearchTerm" 
                               class="form-control" 
                               placeholder="@SharedLocalizer["SearchByNameOrEmail"]"
                               aria-describedby="search-help"
                               inputmode="search"
                               autocomplete="off"
                               maxlength="100">
                        <button type="submit" 
                                class="btn btn-outline-primary"
                                aria-describedby="search-help">
                            <i class="fas fa-search" aria-hidden="true"></i>
                            <span class="d-none d-sm-inline ms-1">@SharedLocalizer["Search"]</span>
                            <span class="visually-hidden d-sm-none">@SharedLocalizer["Search"]</span>
                        </button>
                        @if (!string.IsNullOrEmpty(ViewBag.SearchTerm))
                        {
                            <a href="@Url.Action("Members", "Admin")" 
                               class="btn btn-outline-secondary"
                               aria-label="@SharedLocalizer["ClearSearchResults"]">
                                <i class="fas fa-times" aria-hidden="true"></i>
                                <span class="d-none d-sm-inline ms-1">@SharedLocalizer["Clear"]</span>
                            </a>
                        }
                    </div>
                    <small id="search-help" class="form-text text-muted">
                        @SharedLocalizer["SearchHelpText"]
                    </small>
                </form>
                
                <!-- Filter System -->
                <div class="filter-container" role="region" aria-labelledby="filters-title">
                    <h3 id="filters-title" class="h6 mb-2">@SharedLocalizer["AdvancedFilters"]</h3>
                    
                    <div class="row g-2 mb-2">
                        <div class="col-md-4 col-sm-6">
                            <label for="filterType" class="form-label">@SharedLocalizer["FilterBy"]</label>
                            <select id="filterType" 
                                    class="form-select form-select-sm"
                                    aria-describedby="filter-type-help">
                                <option value="">@SharedLocalizer["SelectFilterType"]</option>
                                <option value="registrationtype">@SharedLocalizer["RegistrationType"]</option>
                                <option value="province">@SharedLocalizer["Province"]</option>
                                <option value="city">@SharedLocalizer["City"]</option>
                                <option value="status">@SharedLocalizer["Status"]</option>
                            </select>
                            <small id="filter-type-help" class="form-text text-muted">@SharedLocalizer["SelectFilterTypeHelp"]</small>
                        </div>

                        <div class="col-md-4 col-sm-6">
                            <label for="filterValue" class="form-label">@SharedLocalizer["FilterValue"]</label>
                            <select id="filterValue" 
                                    class="form-select form-select-sm" 
                                    style="display:none;"
                                    aria-describedby="filter-value-help">
                                <!-- Dynamically populated -->
                            </select>
                            
                            <input type="text" 
                                   id="citySearch" 
                                   class="form-control form-control-sm" 
                                   placeholder="@SharedLocalizer["TypeToSearchCities"]"
                                   style="display:none;"
                                   aria-describedby="city-search-help"
                                   autocomplete="off"
                                   inputmode="search"
                                   maxlength="50" />
                            <small id="filter-value-help" class="form-text text-muted">@SharedLocalizer["SelectFilterValueHelp"]</small>
                            <small id="city-search-help" class="form-text text-muted">@SharedLocalizer["CitySearchHelp"]</small>
                        </div>
                        
                        <div class="col-md-4 col-12">
                            <label class="form-label">&nbsp;</label>
                            <div class="d-flex gap-2">
                                <button type="button" 
                                        id="applyFilter" 
                                        class="btn btn-outline-secondary btn-sm"
                                        style="display:none;"
                                        aria-describedby="apply-filter-help">
                                    <i class="fas fa-filter" aria-hidden="true"></i>
                                    @SharedLocalizer["ApplyFilter"]
                                </button>
                                
                                <button type="button" 
                                        id="clearFilters" 
                                        class="btn btn-outline-danger btn-sm"
                                        style="display:none;"
                                        aria-describedby="clear-filters-help">
                                    <i class="fas fa-times" aria-hidden="true"></i>
                                    @SharedLocalizer["ClearFilters"]
                                </button>
                            </div>
                            <small id="apply-filter-help" class="form-text text-muted">@SharedLocalizer["ApplyFilterHelp"]</small>
                            <small id="clear-filters-help" class="form-text text-muted">@SharedLocalizer["ClearFiltersHelp"]</small>
                        </div>
                    </div>
                </div>
                
                <!-- Active Filters Display -->
                <div id="activeFilters" 
                     class="mt-2" 
                     style="display:none;"
                     role="region"
                     aria-labelledby="active-filters-title">
                    <h4 id="active-filters-title" class="h6 mb-1">@SharedLocalizer["ActiveFilters"]:</h4>
                    <div id="filterBadges" class="d-flex flex-wrap gap-1">
                        <!-- Dynamically populated filter badges -->
                    </div>
                </div>
            </div>
            
            <!-- Member Actions -->
            <div id="member-actions" class="col-lg-4 col-12">
                <h2 class="h5 mb-3">
                    <i class="fas fa-tools" aria-hidden="true"></i>
                    @SharedLocalizer["MemberActions"]
                </h2>
                
                <div class="d-flex flex-column gap-2">
                    <!-- Export Button -->
                    <button type="button" 
                            class="btn btn-info"
                            data-bs-toggle="modal" 
                            data-bs-target="#exportModal"
                            aria-describedby="export-help">
                        <i class="fas fa-download" aria-hidden="true"></i>
                        @SharedLocalizer["ExportMembers"]
                    </button>
                    <small id="export-help" class="form-text text-muted">@SharedLocalizer["ExportMembersHelp"]</small>
                    
                    <!-- Add New Member -->
                    <a href="@Url.Action("Register", "Members")" 
                       class="btn btn-success"
                       aria-describedby="add-member-help">
                        <i class="fas fa-plus" aria-hidden="true"></i>
                        @SharedLocalizer["AddNewMember"]
                    </a>
                    <small id="add-member-help" class="form-text text-muted">@SharedLocalizer["AddNewMemberHelp"]</small>
                </div>
            </div>
        </section>

        <!-- Results Summary -->
        <section class="row mb-3" aria-labelledby="results-summary-title">
            <div class="col">
                <h2 id="results-summary-title" class="visually-hidden">@SharedLocalizer["SearchResultsSummary"]</h2>
                <p class="text-muted" role="status" aria-live="polite">
                    @SharedLocalizer["Showing"] 
                    <strong>@Model.Count()</strong> 
                    @SharedLocalizer["Of"] 
                    <strong>@ViewBag.TotalMembers</strong> 
                    @SharedLocalizer["Members"]
                    @if (!string.IsNullOrEmpty(ViewBag.SearchTerm))
                    {
                        <span>@SharedLocalizer["Matching"] "<em>@ViewBag.SearchTerm</em>"</span>
                    }
                    (@SharedLocalizer["Page"] <strong>@ViewBag.CurrentPage</strong> @SharedLocalizer["Of"] <strong>@ViewBag.TotalPages</strong>)
                </p>
            </div>
        </section>

        <!-- Members Table -->
        <section id="members-table" class="row" aria-labelledby="members-table-title">
            <div class="col">
                <div class="card">
                    <header class="card-header">
                        <h2 id="members-table-title" class="card-title mb-0 h5">
                            <i class="fas fa-table" aria-hidden="true"></i>
                            @SharedLocalizer["MembersTable"]
                        </h2>
                    </header>
                    <div class="card-body">
                        @if (Model.Any())
                        {
                            <div class="table-responsive">
                                <table class="table table-hover" 
                                       role="table" 
                                       aria-labelledby="members-table-title"
                                       aria-describedby="members-table-description">
                                    <caption id="members-table-description" class="visually-hidden">
                                        @SharedLocalizer["MembersTableDescription"]
                                    </caption>
                                    <thead class="table-dark">
                                        <tr role="row">
                                            <th scope="col" class="text-nowrap">
                                                <i class="fas fa-hashtag me-1" aria-hidden="true"></i>
                                                @SharedLocalizer["MemberID"]
                                            </th>
                                            <th scope="col">
                                                <i class="fas fa-user me-1" aria-hidden="true"></i>
                                                @SharedLocalizer["FullName"]
                                            </th>
                                            <th scope="col" class="d-none d-md-table-cell">
                                                <i class="fas fa-envelope me-1" aria-hidden="true"></i>
                                                @SharedLocalizer["Email"]
                                            </th>
                                            <th scope="col" class="d-none d-lg-table-cell">
                                                <i class="fas fa-phone me-1" aria-hidden="true"></i>
                                                @SharedLocalizer["Phone"]
                                            </th>
                                            <th scope="col" class="d-none d-xl-table-cell">
                                                <i class="fas fa-tag me-1" aria-hidden="true"></i>
                                                @SharedLocalizer["RegistrationType"]
                                            </th>
                                            <th scope="col" class="d-none d-lg-table-cell">
                                                <i class="fas fa-calendar me-1" aria-hidden="true"></i>
                                                @SharedLocalizer["RegistrationDate"]
                                            </th>
                                            <th scope="col">
                                                <i class="fas fa-circle me-1" aria-hidden="true"></i>
                                                @SharedLocalizer["Status"]
                                            </th>
                                            <th scope="col" class="text-center">
                                                <i class="fas fa-cogs me-1" aria-hidden="true"></i>
                                                @SharedLocalizer["Actions"]
                                            </th>
                                        </tr>
                                    </thead>
                                <tbody>
                                    @foreach (var member in Model)
                                    {
                                        <tr>
                                            <td>@member.Id</td>
                                            <td>
                                                <strong>@member.FirstName @member.LastName</strong>
                                                <br>
                                                <small class="text-muted">
                                                    @member.DateOfBirth.ToString("yyyy-MM-dd") 
                                                    (@((DateTime.Now.Year - member.DateOfBirth.Year)) @SharedLocalizer["YearsOld"])
                                                </small>
                                            </td>
                                            <td>@member.Email</td>
                                            <td>@member.Phone</td>
                                            <td>
                                                @if (member.RegistrationType != null)
                                                {
                                                    <span class="badge bg-info">@SharedLocalizer[member.RegistrationType.DisplayNameKey]</span>
                                                }
                                                else
                                                {
                                                    <span class="badge bg-secondary">@SharedLocalizer["Unknown"]</span>
                                                }
                                            </td>
                                            <td>
                                                @member.DateCreated.ToString("yyyy-MM-dd")
                                                <br>
                                                <small class="text-muted">@member.DateCreated.ToString("HH:mm")</small>
                                            </td>
                                            <td>
                                                @if (member.IsActive)
                                                {
                                                    <span class="badge bg-success">@SharedLocalizer["Active"]</span>
                                                }
                                                else
                                                {
                                                    <span class="badge bg-secondary">@SharedLocalizer["Inactive"]</span>
                                                }
                                            </td>
                                            <td>
                                                <div class="btn-group" role="group">
                                                    <a href="@Url.Action("MemberDetails", "Admin", new { id = member.Id })" 
                                                       class="btn btn-sm btn-outline-primary" title="@SharedLocalizer["ViewDetails"]">
                                                        <i class="fas fa-eye"></i>
                                                    </a>
                                                    <a href="@Url.Action("Edit", "Members", new { memberId = member.Id })" 
                                                       class="btn btn-sm btn-outline-secondary" title="@SharedLocalizer["Edit"]">
                                                        <i class="fas fa-edit"></i>
                                                    </a>
                                                    @if (member.IsActive)
                                                    {
                                                        <button type="button" class="btn btn-sm btn-outline-warning" 
                                                                onclick="confirmDisable(@member.Id, '@member.FirstName @member.LastName')" 
                                                                title="@SharedLocalizer["Disable"]">
                                                            <i class="fas fa-ban"></i>
                                                        </button>
                                                    }
                                                    else
                                                    {
                                                        <button type="button" class="btn btn-sm btn-outline-success" 
                                                                onclick="confirmEnable(@member.Id, '@member.FirstName @member.LastName')" 
                                                                title="@SharedLocalizer["Enable"]">
                                                            <i class="fas fa-check"></i>
                                                        </button>
                                                    }
                                                </div>
                                            </td>
                                        </tr>
                                    }
                                </tbody>
                            </table>
                        </div>

                        <!-- Pagination -->
                        @if (ViewBag.TotalPages > 1)
                        {
                            <nav aria-label="Members pagination">
                                <ul class="pagination justify-content-center">
                                    @if (ViewBag.CurrentPage > 1)
                                    {
                                        <li class="page-item">
                                            <a class="page-link" href="@Url.Action("Members", "Admin", new { page = ViewBag.CurrentPage - 1, search = ViewBag.SearchTerm })">
                                                @SharedLocalizer["Previous"]
                                            </a>
                                        </li>
                                    }

                                    @for (int i = Math.Max(1, ViewBag.CurrentPage - 2); i <= Math.Min(ViewBag.TotalPages, ViewBag.CurrentPage + 2); i++)
                                    {
                                        <li class="page-item @(i == ViewBag.CurrentPage ? "active" : "")">
                                            <a class="page-link" href="@Url.Action("Members", "Admin", new { page = i, search = ViewBag.SearchTerm })">
                                                @i
                                            </a>
                                        </li>
                                    }

                                    @if (ViewBag.CurrentPage < ViewBag.TotalPages)
                                    {
                                        <li class="page-item">
                                            <a class="page-link" href="@Url.Action("Members", "Admin", new { page = ViewBag.CurrentPage + 1, search = ViewBag.SearchTerm })">
                                                @SharedLocalizer["Next"]
                                            </a>
                                        </li>
                                    }
                                </ul>
                            </nav>
                        }
                    }
                    else
                    {
                        var emptyState = ViewBag.EmptyState as ParaHockeyApp.Models.ViewModels.EmptyStateViewModel;
                        @if (emptyState != null)
                        {
                            <div class="text-center text-muted py-5">
                                <i class="@emptyState.IconClass fa-3x mb-3"></i>
                                <h5>@emptyState.Title</h5>
                                <p>@emptyState.Message</p>
                                @if (emptyState.ShowAction && !string.IsNullOrEmpty(emptyState.ActionUrl) && !string.IsNullOrEmpty(emptyState.ActionText))
                                {
                                    <a href="@emptyState.ActionUrl" class="btn btn-primary">
                                        <i class="fas fa-plus"></i> @emptyState.ActionText
                                    </a>
                                }
                            </div>
                        }
                        else
                        {
                            <!-- Fallback to original empty state -->
                            <div class="text-center text-muted py-5">
                                <i class="fas fa-users fa-3x mb-3"></i>
                                @if (!string.IsNullOrEmpty(ViewBag.SearchTerm))
                                {
                                    <h5>@SharedLocalizer["NoMembersFound"] "@ViewBag.SearchTerm"</h5>
                                    <p>@SharedLocalizer["TryDifferentSearch"] <a href="@Url.Action("Members", "Admin")">@SharedLocalizer["ViewAllMembersLink"]</a>.</p>
                                }
                                else
                                {
                                    <h5>@SharedLocalizer["NoMembersRegisteredYet"]</h5>
                                    <p>@SharedLocalizer["StartByRegistering"]</p>
                                    <a href="@Url.Action("Register", "Members")" class="btn btn-primary">
                                        <i class="fas fa-plus"></i> @SharedLocalizer["RegisterFirstMember"]
                                    </a>
                                }
                            </div>
                        }
                    }
                </div>
            </div>
        </section>
    </div>
</main>

<!-- Disable Confirmation Modal -->
<div class="modal fade" id="disableModal" tabindex="-1" aria-labelledby="disableModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header bg-warning text-dark">
                <h5 class="modal-title" id="disableModalLabel">
                    <i class="fas fa-ban"></i> @SharedLocalizer["ConfirmDisable"]
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p>@SharedLocalizer["ConfirmDisableMessage"] <strong id="disableMemberName"></strong>?</p>
                <p class="text-warning"><i class="fas fa-exclamation-triangle"></i> @SharedLocalizer["DisableWarning"]</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">@SharedLocalizer["Cancel"]</button>
                <form method="post" id="disableForm" style="display: inline;">
                    @Html.AntiForgeryToken()
                    <button type="submit" class="btn btn-warning">
                        <i class="fas fa-ban"></i> @SharedLocalizer["DisableMember"]
                    </button>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Enable Confirmation Modal -->
<div class="modal fade" id="enableModal" tabindex="-1" aria-labelledby="enableModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header bg-success text-white">
                <h5 class="modal-title" id="enableModalLabel">
                    <i class="fas fa-check"></i> @SharedLocalizer["ConfirmEnable"]
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p>@SharedLocalizer["ConfirmEnableMessage"] <strong id="enableMemberName"></strong>?</p>
                <p class="text-success"><i class="fas fa-check-circle"></i> @SharedLocalizer["EnableWarning"]</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">@SharedLocalizer["Cancel"]</button>
                <form method="post" id="enableForm" style="display: inline;">
                    @Html.AntiForgeryToken()
                    <button type="submit" class="btn btn-success">
                        <i class="fas fa-check"></i> @SharedLocalizer["EnableMember"]
                    </button>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Export Modal -->
<div class="modal fade" id="exportModal" tabindex="-1" aria-labelledby="exportModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header bg-info text-white">
                <h5 class="modal-title" id="exportModalLabel">
                    <i class="fas fa-download"></i> @SharedLocalizer["ExportMembers"]
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p>@SharedLocalizer["ExportMembersDescription"]</p>
                
                <form id="exportForm" method="get" action="@Url.Action("ExportMembersAdvanced", "Admin")">
                    <!-- Hidden fields to carry over current search/filter state -->
                    <input type="hidden" name="SearchTerm" value="@ViewBag.SearchTerm" />
                    @if (ViewBag.SearchRequest?.RegistrationTypeIds != null)
                    {
                        foreach (var id in ViewBag.SearchRequest.RegistrationTypeIds)
                        {
                            <input type="hidden" name="RegistrationTypeIds" value="@id" />
                        }
                    }
                    @if (ViewBag.SearchRequest?.IsActive != null)
                    {
                        <input type="hidden" name="IsActive" value="@ViewBag.SearchRequest.IsActive" />
                    }
                    @if (!string.IsNullOrEmpty(ViewBag.SearchRequest?.City))
                    {
                        <input type="hidden" name="City" value="@ViewBag.SearchRequest.City" />
                    }
                    @if (!string.IsNullOrEmpty(ViewBag.SearchRequest?.Province))
                    {
                        <input type="hidden" name="Province" value="@ViewBag.SearchRequest.Province" />
                    }
                    
                    <!-- Export format selection -->
                    <input type="hidden" id="exportFormat" name="Format" value="Excel" />
                    
                    <div class="mb-3">
                        <label class="form-label">@SharedLocalizer["SelectExportFormat"]:</label>
                        <div class="d-flex">
                            <div class="form-check me-4">
                                <input class="form-check-input export-format" type="radio" name="formatRadio" 
                                       id="formatExcel" value="Excel" checked data-format="Excel">
                                <label class="form-check-label" for="formatExcel">
                                    <i class="fas fa-file-excel text-success"></i> Excel (.xlsx)
                                </label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input export-format" type="radio" name="formatRadio" 
                                       id="formatCsv" value="CSV" data-format="CSV">
                                <label class="form-check-label" for="formatCsv">
                                    <i class="fas fa-file-csv text-primary"></i> CSV (.csv)
                                </label>
                            </div>
                        </div>
                    </div>
                </form>
                
                <div class="alert alert-info">
                    <i class="fas fa-info-circle"></i> 
                    @SharedLocalizer["ExportWillIncludeAllResults"] 
                    <strong>@ViewBag.TotalMembers</strong> @SharedLocalizer["Members"]
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                    @SharedLocalizer["Cancel"]
                </button>
                <button type="submit" form="exportForm" class="btn btn-info">
                    <i class="fas fa-download"></i> @SharedLocalizer["Export"]
                </button>
            </div>
        </div>
    </div>
</div>

<!-- City Search Dropdown Styles -->
<style>
    .city-results-dropdown {
        width: 100%;
        max-height: 200px;
        overflow-y: auto;
        position: absolute;
        z-index: 1000;
    }
    
    .city-option {
        cursor: pointer;
    }
    
    .city-option:hover {
        background-color: #f8f9fa;
    }
</style>

<script>
function confirmDisable(memberId, memberName) {
    document.getElementById('disableMemberName').textContent = memberName;
    document.getElementById('disableForm').action = '@Url.Action("DisableMember", "Admin")/' + memberId;
    new bootstrap.Modal(document.getElementById('disableModal')).show();
}

function confirmEnable(memberId, memberName) {
    document.getElementById('enableMemberName').textContent = memberName;
    document.getElementById('enableForm').action = '@Url.Action("EnableMember", "Admin")/' + memberId;
    new bootstrap.Modal(document.getElementById('enableModal')).show();
}

// Export and Filter Functionality
console.log('Members page JavaScript loaded successfully');

// Wait for jQuery to be available
function waitForJQuery() {
    if (typeof $ !== 'undefined') {
        console.log('jQuery is now available');
        initializeFilterFunctionality();
    } else {
        console.log('Waiting for jQuery...');
        setTimeout(waitForJQuery, 100);
    }
}

function initializeFilterFunctionality() {
    $(document).ready(function() {
    console.log('Document ready - jQuery working');
    console.log('FilterType element found:', $('#filterType').length > 0);
    console.log('FilterType element:', $('#filterType')[0]);
    // Filter type change handler
    $('#filterType').on('change', function() {
        const filterType = $(this).val();
        console.log('Filter type changed to:', filterType);
        
        // Hide all filter inputs first
        $('#filterValue').hide();
        $('#citySearch').hide();
        $('#applyFilter').hide();
        
        if (!filterType) {
            console.log('No filter type selected, returning');
            return;
        }
        
        console.log('Processing filter type:', filterType);
        // Don't show apply button yet - wait for user to select value
        
        // Handle city search separately
        if (filterType === 'city') {
            $('#citySearch').show().focus();
            loadCityOptions();
        } else {
            // For other filter types, show the dropdown and load options
            $('#filterValue').show().empty().append('<option value="">' + @Json.Serialize(SharedLocalizer["Loading"].Value) + '...</option>');
            loadFilterOptions(filterType);
        }
    });
    
    // Filter value change handler (for second dropdown)
    $('#filterValue').on('change', function() {
        const filterValue = $(this).val();
        if (filterValue) {
            $('#applyFilter').show();
        } else {
            $('#applyFilter').hide();
        }
    });
    
    // City search with debounce
    let citySearchTimeout;
    $('#citySearch').on('input', function() {
        clearTimeout(citySearchTimeout);
        const searchTerm = $(this).val();
        
        // Only search if at least 2 characters entered
        if (searchTerm.length >= 2) {
            citySearchTimeout = setTimeout(function() {
                loadCityOptions(searchTerm);
            }, 300); // 300ms debounce
        }
    });
    
    // Apply filter button click
    $('#applyFilter').on('click', function() {
        const filterType = $('#filterType').val();
        let filterValue;
        
        if (filterType === 'city') {
            filterValue = $('#citySearch').val();
        } else {
            filterValue = $('#filterValue').val();
        }
        
        if (!filterValue) {
            return;
        }
        
        // Add filter to current URL and reload
        applyFilter(filterType, filterValue);
    });
    
    // Clear filters button
    $('#clearFilters').on('click', function() {
        window.location.href = '@Url.Action("Members", "Admin")';
    });
    
    // Export format selection
    $('.export-format').on('click', function() {
        const format = $(this).data('format');
        $('#exportFormat').val(format);
    });
    
    // Initialize filters if any are active
    initializeActiveFilters();
    });
}

// Start waiting for jQuery
waitForJQuery();

// Load filter options from server
function loadFilterOptions(filterType, searchTerm = '') {
    console.log('Loading filter options for:', filterType, 'with search term:', searchTerm);
    const url = '@Url.Action("GetFilterOptions", "Admin")';
    console.log('AJAX URL:', url);
    
    $.ajax({
        url: url,
        data: { filterType: filterType, searchTerm: searchTerm },
        method: 'GET',
        success: function(response) {
            console.log('Filter options response:', response);
            if (response.success) {
                const $select = $('#filterValue');
                $select.empty();
                
                if (response.options.length > 0) {
                    $select.append('<option value="">' + @Json.Serialize(SharedLocalizer["SelectOption"].Value) + '...</option>');
                    response.options.forEach(function(option) {
                        let displayText = option.display;
                        if (option.count) {
                            displayText += ` (${option.count})`;
                        }
                        $select.append(`<option value="${option.value}">${displayText}</option>`);
                    });
                } else {
                    $select.append('<option value="">' + @Json.Serialize(SharedLocalizer["NoOptionsAvailable"].Value) + '</option>');
                }
            } else {
                console.error('Error loading filter options:', response.error);
            }
        },
        error: function(xhr, status, error) {
            console.error('AJAX error loading filter options:', error);
            console.error('XHR response:', xhr.responseText);
            console.error('Status:', status);
        }
    });
}

// Load city options with optional search term
function loadCityOptions(searchTerm = '') {
    $.ajax({
        url: '@Url.Action("GetFilterOptions", "Admin")',
        data: { filterType: 'city', searchTerm: searchTerm },
        method: 'GET',
        success: function(response) {
            if (response.success) {
                const $citySearch = $('#citySearch');
                
                // Create and show dropdown for results
                let $dropdown = $('#cityResultsDropdown');
                if ($dropdown.length === 0) {
                    $dropdown = $('<div id="cityResultsDropdown" class="dropdown-menu city-results-dropdown"></div>');
                    $citySearch.after($dropdown);
                }
                
                $dropdown.empty();
                
                if (response.options.length === 0) {
                    $dropdown.append('<div class="dropdown-item text-muted">' + @Json.Serialize(SharedLocalizer["NoCitiesFound"].Value) + '</div>');
                } else {
                    response.options.forEach(function(option) {
                        let displayText = option.display;
                        if (option.count) {
                            displayText += ` (${option.count})`;
                        }
                        $dropdown.append(`<a class="dropdown-item city-option" data-value="${option.value}">${displayText}</a>`);
                    });
                }
                
                $dropdown.addClass('show');
                
                // Handle city selection
                $('.city-option').on('click', function() {
                    const cityValue = $(this).data('value');
                    $citySearch.val(cityValue);
                    $dropdown.removeClass('show');
                });
                
                // Close dropdown when clicking outside
                $(document).on('click', function(e) {
                    if (!$(e.target).closest('#citySearch, #cityResultsDropdown').length) {
                        $dropdown.removeClass('show');
                    }
                });
            } else {
                console.error('Error loading city options:', response.error);
            }
        },
        error: function(xhr, status, error) {
            console.error('AJAX error loading city options:', error);
        }
    });
}

// Apply filter to current URL and reload
function applyFilter(filterType, filterValue) {
    const currentUrl = new URL(window.location.href);
    const params = new URLSearchParams(currentUrl.search);
    
    // Map filter types to request parameter names
    const paramMapping = {
        'registrationtype': 'RegistrationTypeIds',
        'province': 'Province',
        'city': 'City',
        'status': 'IsActive'
    };
    
    const paramName = paramMapping[filterType];
    if (paramName) {
        params.set(paramName, filterValue);
        
        // Reset to page 1 when applying filters
        params.set('page', '1');
        
        currentUrl.search = params.toString();
        window.location.href = currentUrl.toString();
    }
}

// Initialize active filters from URL parameters
function initializeActiveFilters() {
    const params = new URLSearchParams(window.location.search);
    let hasActiveFilters = false;
    
    // Check for each filter type
    if (params.has('RegistrationTypeIds')) {
        addFilterBadge('registrationtype', params.get('RegistrationTypeIds'));
        hasActiveFilters = true;
    }
    
    if (params.has('Province')) {
        addFilterBadge('province', params.get('Province'));
        hasActiveFilters = true;
    }
    
    if (params.has('City')) {
        addFilterBadge('city', params.get('City'));
        hasActiveFilters = true;
    }
    
    if (params.has('IsActive')) {
        addFilterBadge('status', params.get('IsActive'));
        hasActiveFilters = true;
    }
    
    // Show active filters section and clear button if we have filters
    if (hasActiveFilters) {
        $('#activeFilters').show();
        $('#clearFilters').show();
    }
}

// Add a filter badge to the active filters section
function addFilterBadge(filterType, filterValue) {
    const filterTypeDisplay = {
        'registrationtype': @Json.Serialize(SharedLocalizer["RegistrationType"].Value),
        'province': @Json.Serialize(SharedLocalizer["Province"].Value),
        'city': @Json.Serialize(SharedLocalizer["City"].Value),
        'status': @Json.Serialize(SharedLocalizer["Status"].Value)
    };
    
    // For status, convert boolean to text
    let displayValue = filterValue;
    if (filterType === 'status') {
        displayValue = filterValue === 'true' ? @Json.Serialize(SharedLocalizer["Active"].Value) : @Json.Serialize(SharedLocalizer["Inactive"].Value);
    }
    
    const badge = `
        <span class="badge bg-secondary me-2 mb-1">
            ${filterTypeDisplay[filterType]}: ${displayValue}
            <button type="button" class="btn-close btn-close-white ms-1" 
                    onclick="removeFilter('${filterType}')" 
                    style="font-size: 0.5rem;" aria-label="' + @Json.Serialize(SharedLocalizer["Remove"].Value) + '"></button>
        </span>
    `;
    
    $('#filterBadges').append(badge);
}

// Remove a filter and reload
function removeFilter(filterType) {
    const currentUrl = new URL(window.location.href);
    const params = new URLSearchParams(currentUrl.search);
    
    // Map filter types to request parameter names
    const paramMapping = {
        'registrationtype': 'RegistrationTypeIds',
        'province': 'Province',
        'city': 'City',
        'status': 'IsActive'
    };
    
    const paramName = paramMapping[filterType];
    if (paramName && params.has(paramName)) {
        params.delete(paramName);
        currentUrl.search = params.toString();
        window.location.href = currentUrl.toString();
    }
}
</script>