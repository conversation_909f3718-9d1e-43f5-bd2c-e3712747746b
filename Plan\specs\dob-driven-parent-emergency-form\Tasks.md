# Tasks: DOB-Driven Parent/Emergency Form Logic

## Phase 1: Configuration & Service Layer

-   [x] **T1.1** [NFR3.2] Add `AgeOfMajority` to `appsettings.json` and `EnvironmentSettings.cs`
-   [x] **T1.2** [NFR3.1] Implement `IRegistrationFlowService` with age-based logic for form flow and membership type restrictions

## Phase 2: UI Flow Refactor

-   [x] **T2.1** [FR1.1, FR1.2] Refactor registration flow so that after DOB entry, the Parent or Emergency Contact form is shown immediately based solely on age (not on membership type selection)
-   [x] **T2.2** [FR1.3] Ensure membership type selection is always shown only after the appropriate Parent or Emergency Contact form is completed, and never before.

## Phase 3: Membership Type UI

-   [x] **T3.1** [FR2.2] Update membership type selection UI to disable (grey out) "Junior" for 18+ with tooltip
-   [x] **T3.2** [FR3.2, NFR2.1] Add ARIA and tooltip for disabled options, ensure accessibility

## Phase 4: Localization

-   [x] **T4.1** [NFR1.1, FR3.2] Add all new UI text/tooltips/messages to `SharedResource.resx` (EN/FR)

## Phase 5: Testing & Validation

-   [x] **T5.1** [AC1-AC6] Manual and automated tests for all acceptance criteria

---

# Success Verification Checklist

-   [x] Entering DOB < 18 shows Parent Form, skips membership type
-   [x] Entering DOB >= 18 shows Emergency Contact Form, skips membership type
-   [x] After Parent/Emergency Contact, membership type selection appears
-   [x] "Junior" is disabled for 18+, with tooltip
-   [x] All new UI text is localized and accessible
-   [x] Age logic is in service layer and config-driven
