using ParaHockeyApp.Models.Entities;
using ParaHockeyApp.Models.ViewModels;

namespace ParaHockeyApp.Services
{
    /// <summary>
    /// Service interface for processing and consolidating raw audit logs into user-friendly view models
    /// </summary>
    public interface IAuditLogProcessingService
    {
        /// <summary>
        /// Processes a list of raw audit logs and consolidates them into user-friendly view models
        /// </summary>
        /// <param name="rawLogs">List of raw audit logs from the database</param>
        /// <returns>List of consolidated audit log view models</returns>
        Task<List<AuditLogViewModel>> ProcessAuditLogsAsync(List<AuditLog> rawLogs);
    }
}