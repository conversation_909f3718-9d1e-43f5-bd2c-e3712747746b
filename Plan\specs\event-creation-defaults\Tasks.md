# Tasks: Event Creation & Visibility Defaults

## 1. Backend: Service Layer

- [x] **Task 1.1:** In `Services/EventService.cs`, create a new private method `ApplyCategoryDefaults(Event eventEntity)` that sets the `IsPublished` and `RequiresRegistration` properties based on `eventEntity.EventCategoryId`. (Implements REQ-1.1.2, REQ-1.1.3)
- [x] **Task 1.2:** In `Services/EventService.cs`, call this new `ApplyCategoryDefaults` method from `CreateEventAsync`. (Implements REQ-1.1.1)
- [x] **Task 1.3:** In `Services/EventService.cs`, call the `ApplyCategoryDefaults` method from the event import logic (e.g., `ImportEventsFromCsvAsync`). (Implements REQ-1.2.1)
- [x] **Task 1.4:** In `Services/EventService.cs`, create or update a method `GetSubscribableEventsAsync()` that filters events where `IsPublished` is true and `IsRegistrationOpen` is true. (Implements REQ-1.3.2)

## 2. Frontend: Admin Calendar UI

- [x] **Task 2.1:** In the JavaScript for `Views/Admin/Calendar.cshtml`, add an event listener to the category dropdown in the event modal.
- [x] **Task 2.2:** On category change, apply the default states to the "Requires Registration" and "Publish Event" checkboxes based on the new category. (Implements NFR-2.1)

## 3. Backend: Controllers

- [x] **Task 3.1:** In `Controllers/EventsController.cs`, update the `Subscribe` action to use the new `GetSubscribableEventsAsync` method from `EventService`. (Implements REQ-1.3.2)
- [x] **Task 3.2:** Review `HomeController.PublicCalendar` and `MembersController.CalendarReadOnly` to ensure they call `GetCalendarEventsJsonAsync` with the `publishedOnly` parameter set to `true`. (Implements REQ-1.3.1)

## 4. Verification

- [x] **Task 4.1:** Manually test the scenarios outlined in the Acceptance Criteria (AC-1 to AC-7).
