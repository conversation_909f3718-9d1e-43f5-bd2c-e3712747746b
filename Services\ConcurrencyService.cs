using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Caching.Memory;
using ParaHockeyApp.Models;
using System.Collections.Concurrent;

namespace ParaHockeyApp.Services
{
    /// <summary>
    /// Service for handling concurrency and preventing conflicts during import processing
    /// </summary>
    public class ConcurrencyService : IConcurrencyService
    {
        private readonly ApplicationContext _context;
        private readonly IMemoryCache _cache;
        private readonly ILogger<ConcurrencyService> _logger;

        // In-memory lock storage for high performance
        private static readonly ConcurrentDictionary<Guid, LockInfo> _activeLocks = new();
        private static readonly object _lockObject = new object();

        public ConcurrencyService(
            ApplicationContext context,
            IMemoryCache cache,
            ILogger<ConcurrencyService> logger)
        {
            _context = context;
            _cache = cache;
            _logger = logger;
        }

        /// <summary>
        /// Attempts to acquire a lock on a temp member for editing
        /// </summary>
        public async Task<LockResult> TryAcquireLockAsync(Guid tempMemberId, string userId, int lockDurationMinutes = 30)
        {
            lock (_lockObject)
            {
                // Clean up expired locks first
                CleanupExpiredLocksInternal();

                // Check if already locked
                if (_activeLocks.TryGetValue(tempMemberId, out var existingLock))
                {
                    if (existingLock.UserId == userId)
                    {
                        // Same user, extend the lock
                        existingLock.ExpiresAt = DateTime.UtcNow.AddMinutes(lockDurationMinutes);
                        return new LockResult 
                        { 
                            Success = true, 
                            LockExpiration = existingLock.ExpiresAt 
                        };
                    }
                    else
                    {
                        // Different user has the lock
                        return new LockResult
                        {
                            Success = false,
                            FailureReason = $"Record is currently being edited by {existingLock.UserDisplayName}",
                            ExistingLock = existingLock
                        };
                    }
                }

                // Acquire new lock
                var newLock = new LockInfo
                {
                    TempMemberId = tempMemberId,
                    UserId = userId,
                    UserDisplayName = GetUserDisplayName(userId),
                    AcquiredAt = DateTime.UtcNow,
                    ExpiresAt = DateTime.UtcNow.AddMinutes(lockDurationMinutes)
                };

                _activeLocks[tempMemberId] = newLock;

                _logger.LogDebug("Lock acquired for temp member {TempMemberId} by user {UserId}", 
                    tempMemberId, userId);

                return new LockResult 
                { 
                    Success = true, 
                    LockExpiration = newLock.ExpiresAt 
                };
            }
        }

        /// <summary>
        /// Releases a lock on a temp member
        /// </summary>
        public async Task<bool> ReleaseLockAsync(Guid tempMemberId, string userId)
        {
            lock (_lockObject)
            {
                if (_activeLocks.TryGetValue(tempMemberId, out var existingLock))
                {
                    if (existingLock.UserId == userId)
                    {
                        _activeLocks.TryRemove(tempMemberId, out _);
                        _logger.LogDebug("Lock released for temp member {TempMemberId} by user {UserId}", 
                            tempMemberId, userId);
                        return true;
                    }
                    else
                    {
                        _logger.LogWarning("User {UserId} attempted to release lock owned by {LockUserId} for temp member {TempMemberId}", 
                            userId, existingLock.UserId, tempMemberId);
                        return false;
                    }
                }

                return true; // No lock exists, consider it released
            }
        }

        /// <summary>
        /// Extends an existing lock for additional time
        /// </summary>
        public async Task<bool> ExtendLockAsync(Guid tempMemberId, string userId, int additionalMinutes = 30)
        {
            lock (_lockObject)
            {
                if (_activeLocks.TryGetValue(tempMemberId, out var existingLock))
                {
                    if (existingLock.UserId == userId)
                    {
                        existingLock.ExpiresAt = DateTime.UtcNow.AddMinutes(additionalMinutes);
                        _logger.LogDebug("Lock extended for temp member {TempMemberId} by user {UserId}", 
                            tempMemberId, userId);
                        return true;
                    }
                }

                return false;
            }
        }

        /// <summary>
        /// Gets information about who has a lock on a temp member
        /// </summary>
        public async Task<LockInfo?> GetLockInfoAsync(Guid tempMemberId)
        {
            CleanupExpiredLocksInternal();

            if (_activeLocks.TryGetValue(tempMemberId, out var lockInfo))
            {
                return lockInfo;
            }

            return null;
        }

        /// <summary>
        /// Cleans up expired locks
        /// </summary>
        public async Task<int> CleanupExpiredLocksAsync()
        {
            return CleanupExpiredLocksInternal();
        }

        /// <summary>
        /// Attempts to acquire locks on multiple temp members (for bulk operations)
        /// </summary>
        public async Task<BulkLockResult> TryAcquireBulkLocksAsync(List<Guid> tempMemberIds, string userId, int lockDurationMinutes = 30)
        {
            var result = new BulkLockResult();

            lock (_lockObject)
            {
                CleanupExpiredLocksInternal();

                // Check all locks first
                foreach (var tempMemberId in tempMemberIds)
                {
                    if (_activeLocks.TryGetValue(tempMemberId, out var existingLock))
                    {
                        if (existingLock.UserId != userId)
                        {
                            result.FailedLocks.Add(tempMemberId);
                            result.ConflictingLocks[tempMemberId] = existingLock;
                        }
                        else
                        {
                            result.SuccessfulLocks.Add(tempMemberId);
                        }
                    }
                    else
                    {
                        result.SuccessfulLocks.Add(tempMemberId);
                    }
                }

                // If any failed, don't acquire any locks
                if (result.FailedLocks.Any())
                {
                    result.AllSuccessful = false;
                    result.ErrorMessage = $"Cannot acquire locks: {result.FailedLocks.Count} records are currently being edited by other users";
                    result.SuccessfulLocks.Clear();
                    return result;
                }

                // Acquire all locks
                var expirationTime = DateTime.UtcNow.AddMinutes(lockDurationMinutes);
                foreach (var tempMemberId in tempMemberIds)
                {
                    var lockInfo = new LockInfo
                    {
                        TempMemberId = tempMemberId,
                        UserId = userId,
                        UserDisplayName = GetUserDisplayName(userId),
                        AcquiredAt = DateTime.UtcNow,
                        ExpiresAt = expirationTime
                    };

                    _activeLocks[tempMemberId] = lockInfo;
                }

                result.AllSuccessful = true;
                _logger.LogDebug("Bulk locks acquired for {Count} temp members by user {UserId}", 
                    tempMemberIds.Count, userId);
            }

            return result;
        }

        /// <summary>
        /// Releases multiple locks at once
        /// </summary>
        public async Task<int> ReleaseBulkLocksAsync(List<Guid> tempMemberIds, string userId)
        {
            int releasedCount = 0;

            lock (_lockObject)
            {
                foreach (var tempMemberId in tempMemberIds)
                {
                    if (_activeLocks.TryGetValue(tempMemberId, out var existingLock))
                    {
                        if (existingLock.UserId == userId)
                        {
                            _activeLocks.TryRemove(tempMemberId, out _);
                            releasedCount++;
                        }
                    }
                }

                if (releasedCount > 0)
                {
                    _logger.LogDebug("Bulk locks released for {Count} temp members by user {UserId}", 
                        releasedCount, userId);
                }
            }

            return releasedCount;
        }

        /// <summary>
        /// Internal method to clean up expired locks (not thread-safe, call within lock)
        /// </summary>
        private int CleanupExpiredLocksInternal()
        {
            var expiredLocks = _activeLocks.Where(kvp => kvp.Value.IsExpired).ToList();
            
            foreach (var expiredLock in expiredLocks)
            {
                _activeLocks.TryRemove(expiredLock.Key, out _);
            }

            if (expiredLocks.Any())
            {
                _logger.LogDebug("Cleaned up {Count} expired locks", expiredLocks.Count);
            }

            return expiredLocks.Count;
        }

        /// <summary>
        /// Gets user display name for lock info
        /// </summary>
        private string GetUserDisplayName(string userId)
        {
            // Try to get from cache first
            if (_cache.TryGetValue($"user_display_name_{userId}", out string? cachedName) && cachedName != null)
            {
                return cachedName;
            }

            // In a real implementation, you'd look this up from your user service
            // For now, just return the userId
            var displayName = userId.Contains("@") ? userId.Split('@')[0] : userId;
            
            // Cache for 5 minutes
            _cache.Set($"user_display_name_{userId}", displayName, TimeSpan.FromMinutes(5));
            
            return displayName;
        }
    }
}