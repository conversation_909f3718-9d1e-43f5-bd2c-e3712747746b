# Requirements: Event Subscription Enhancement

## Project Overview

Enhance the existing ParaHockey event subscription system to provide seamless user experience flows for visitors, members, and admins across all four calendar implementations.

## Current System Analysis ✅

-   **Database**: Perfect structure already exists (Events, EventRegistrations, Members, AdminUsers tables)
-   **Authentication**: Working UserContextService with IsAdmin/IsMember detection
-   **Calendars**: Four implementations (Admin, Subscribe, Public, Member readonly)
-   **Registration**: Basic Join/Leave endpoints functional
-   **Modal System**: Event details modal with basic registration buttons

## Functional Requirements

### FR1: Visitor Conversion Flow

**Priority: High**

-   **FR1.1**: When unauthenticated visitor clicks "Register" on any event, show login/signup prompt
-   **FR1.2**: After login/registration, automatically redirect back to event registration
-   **FR1.3**: Store registration intent across login session
-   **FR1.4**: Display clear call-to-action messaging for visitors on event details

### FR2: Admin Protection & Experience

**Priority: High**

-   **FR2.1**: Prevent admins from registering for events (business rule)
-   **FR2.2**: Show appropriate messaging: "Admins manage events but cannot register"
-   **FR2.3**: Use UserContextService.IsAdmin for consistent role detection
-   **FR2.4**: Maintain admin calendar separation (manage vs participate)

### FR3: Enhanced Registration UX

**Priority: High**

-   **FR3.1**: Add AJAX registration to event modals (no page refresh)
-   **FR3.2**: Show loading states during registration actions
-   **FR3.3**: Display success/error feedback immediately in modal
-   **FR3.4**: Update registration status in real-time without closing modal

### FR4: Calendar Integration

**Priority: Medium**

-   **FR4.1**: Ensure consistent modal behavior across all 4 calendar implementations
-   **FR4.2**: Add registration prompts to Public Calendar for visitors
-   **FR4.3**: Show registration status indicators in calendar views
-   **FR4.4**: Maintain role-appropriate functionality per calendar type

### FR5: Quick Registration Actions

**Priority: Medium**

-   **FR5.1**: Add quick-register buttons to event list items on Subscribe page
-   **FR5.2**: Enable one-click registration for authenticated members
-   **FR5.3**: Show registration status badges on event cards
-   **FR5.4**: Implement immediate visual feedback for registration actions

## Non-Functional Requirements

### NFR1: Security

-   **NFR1.1**: All registration endpoints must validate user authentication
-   **NFR1.2**: Enforce admin registration prevention at service layer
-   **NFR1.3**: Maintain existing audit logging for all registration actions
-   **NFR1.4**: Preserve existing authorization patterns

### NFR2: Performance

-   **NFR2.1**: AJAX operations should complete within 2 seconds
-   **NFR2.2**: Modal loading should show immediate feedback
-   **NFR2.3**: Maintain existing page load performance
-   **NFR2.4**: Cache user context for request duration

### NFR3: Usability

-   **NFR3.1**: Mobile-responsive registration experience
-   **NFR3.2**: Consistent button states and messaging across all calendars
-   **NFR3.3**: Clear error messages with actionable guidance
-   **NFR3.4**: Intuitive registration flow for first-time users

### NFR4: Maintainability

-   **NFR4.1**: Use existing service layer architecture (EventService, UserContextService)
-   **NFR4.2**: Follow established localization patterns (SharedLocalizer)
-   **NFR4.3**: Maintain environment-conditional configuration approach
-   **NFR4.4**: Preserve existing database schema (no table changes)

## Acceptance Criteria

### AC1: Visitor Experience

-   ✅ **AC1.1**: Visitor clicks event register → sees login modal with return-to-event flow
-   ✅ **AC1.2**: After login → automatically redirects to event registration
-   ✅ **AC1.3**: New member signup → includes intent to register for specific event
-   ✅ **AC1.4**: Clear messaging guides visitors through the conversion process

### AC2: Member Experience

-   ✅ **AC2.1**: Authenticated member can register/unregister via modal without page refresh
-   ✅ **AC2.2**: Registration status updates immediately in UI
-   ✅ **AC2.3**: Quick-register buttons work from event list view
-   ✅ **AC2.4**: Mobile registration experience is smooth and intuitive

### AC3: Admin Experience

-   ✅ **AC3.1**: Admin cannot see register buttons on events
-   ✅ **AC3.2**: Admin sees "Admins manage events but cannot register" message
-   ✅ **AC3.3**: Admin calendar focuses on event management, not participation
-   ✅ **AC3.4**: Admin actions properly audited via existing logging

### AC4: System Integration

-   ✅ **AC4.1**: All 4 calendar implementations use consistent modal behavior
-   ✅ **AC4.2**: Registration flows work across all authentication states
-   ✅ **AC4.3**: Existing Join/Leave endpoints remain functional as fallbacks
-   ✅ **AC4.4**: No breaking changes to current functionality

## User Stories

### US1: As a Visitor

-   **US1.1**: I want to see event details and register prompts so I know I can participate after joining
-   **US1.2**: I want a smooth login flow that returns me to my intended event registration
-   **US1.3**: I want clear guidance on how to become a member and register for events

### US2: As a Member

-   **US2.1**: I want to register for events quickly without page refreshes
-   **US2.2**: I want immediate feedback when I register or unregister
-   **US2.3**: I want to see my registration status clearly in all calendar views
-   **US2.4**: I want quick-action buttons for faster event registration

### US3: As an Admin

-   **US3.1**: I want to manage events without accidentally registering as a participant
-   **US3.2**: I want clear indication that my role is event management, not participation
-   **US3.3**: I want to maintain audit trails for all user registration actions

### US4: As a System User

-   **US4.1**: I want consistent behavior across all calendar interfaces
-   **US4.2**: I want the system to remember my registration intent during login
-   **US4.3**: I want mobile-friendly registration that works on any device

## Business Rules

### BR1: Role-Based Registration

-   **BR1.1**: Only authenticated members can register for events
-   **BR1.2**: Admins cannot register for events (they manage, not participate)
-   **BR1.3**: Visitors must become members before registering
-   **BR1.4**: All registration actions require proper authentication

### BR2: Event Registration Logic

-   **BR2.1**: Cannot register if event is full (use existing Event.IsFull logic)
-   **BR2.2**: Cannot register after registration deadline
-   **BR2.3**: Cannot register twice for same event
-   **BR2.4**: Can unregister if registration is cancellable (existing logic)

### BR3: User Experience Flow

-   **BR3.1**: Preserve user intent across authentication flows
-   **BR3.2**: Show role-appropriate interface elements
-   **BR3.3**: Provide immediate feedback for all user actions
-   **BR3.4**: Maintain consistent behavior across all calendar views

## Success Metrics

-   📈 Increased event registration completion rate (baseline: measure current)
-   📱 Mobile registration success rate > 95%
-   ⚡ Registration action response time < 2 seconds
-   🎯 Zero admin accidental registrations
-   📊 Visitor-to-member conversion tracking through event registration intent
-   ✅ 100% compatibility across all 4 calendar implementations

## Out of Scope

-   ❌ Database schema changes (existing structure is perfect)
-   ❌ New user roles or permission systems
-   ❌ Event creation/management enhancements
-   ❌ Email notification systems
-   ❌ Payment processing for events
-   ❌ Waitlist functionality (beyond existing status)
