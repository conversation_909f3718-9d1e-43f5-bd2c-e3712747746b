/* ParaHockey - Forced Colors / High Contrast Support */
/* This stylesheet provides support for Windows High Contrast mode and forced-colors media query */

@media (forced-colors: active) {
    :root {
        /* Override semantic tokens with system colors */
        --ph-bg-primary: Canvas;
        --ph-bg-secondary: Canvas;
        --ph-text-primary: CanvasText;
        --ph-text-secondary: CanvasText;
        --ph-link: LinkText;
        --ph-link-hover: LinkText;
        --ph-border: GrayText;
        
        /* Ensure focus ring uses system colors */
        --ph-focus-ring-color: Highlight;
    }
    
    /* Ensure interactive elements maintain visibility */
    a, button, input, select, textarea {
        forced-color-adjust: none;
        border: 1px solid ButtonBorder !important;
    }
    
    /* Links should be clearly identifiable */
    a {
        color: LinkText !important;
        text-decoration: underline !important;
    }
    
    a:hover, a:focus {
        color: LinkText !important;
        background-color: Highlight !important;
        color: HighlightText !important;
    }
    
    /* Buttons need clear borders and text */
    button, .btn {
        background-color: ButtonFace !important;
        color: ButtonText !important;
        border: 2px solid ButtonBorder !important;
    }
    
    button:hover, .btn:hover,
    button:focus, .btn:focus {
        background-color: Highlight !important;
        color: HighlightText !important;
        border-color: Highlight !important;
    }
    
    /* Form inputs */
    input, select, textarea {
        background-color: Field !important;
        color: FieldText !important;
        border: 1px solid FieldText !important;
    }
    
    input:focus, select:focus, textarea:focus {
        outline: 2px solid Highlight !important;
        outline-offset: 1px !important;
    }
    
    /* Ensure cards and containers have borders */
    .card, .modal-content, .dropdown-menu {
        border: 1px solid CanvasText !important;
        background-color: Canvas !important;
        color: CanvasText !important;
    }
    
    /* Tables need clear borders */
    table, th, td {
        border: 1px solid CanvasText !important;
    }
    
    /* Navigation elements */
    .navbar, .nav {
        border-bottom: 1px solid CanvasText !important;
    }
    
    .nav-link {
        color: CanvasText !important;
        border: 1px solid transparent !important;
    }
    
    .nav-link:hover, .nav-link:focus,
    .nav-link.active {
        background-color: Highlight !important;
        color: HighlightText !important;
        border-color: Highlight !important;
    }
    
    /* Alerts and status messages */
    .alert {
        border: 2px solid CanvasText !important;
        background-color: Canvas !important;
        color: CanvasText !important;
    }
    
    /* Ensure icons don't disappear */
    .icon, i[class*="fa-"], svg {
        forced-color-adjust: auto;
    }
    
    /* Form validation states */
    .is-valid {
        border-color: CanvasText !important;
    }
    
    .is-invalid {
        border-color: CanvasText !important;
    }
    
    .valid-feedback, .invalid-feedback {
        color: CanvasText !important;
    }
}