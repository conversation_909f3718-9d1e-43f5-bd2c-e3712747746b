# Check Production Database Structure
Write-Host "=== Checking Production Database Structure ===" -ForegroundColor Cyan

try {
    $connStr = "Server=SIMBA\SQLEXPRESS;Database=ParaHockeyDB;User Id=ParaHockeyUser;Password=***************;TrustServerCertificate=True;"
    $conn = New-Object System.Data.SqlClient.SqlConnection($connStr)
    $conn.Open()
    
    Write-Host "Connected to Production DB" -ForegroundColor Green
    
    # Check if tables exist
    $cmd = $conn.CreateCommand()
    $cmd.CommandText = @"
SELECT TABLE_NAME, TABLE_TYPE 
FROM INFORMATION_SCHEMA.TABLES 
WHERE TABLE_TYPE = 'BASE TABLE'
ORDER BY TABLE_NAME
"@
    
    Write-Host "`nTables in Production:" -ForegroundColor Yellow
    $reader = $cmd.ExecuteReader()
    $tableCount = 0
    while ($reader.Read()) {
        $tableCount++
        Write-Host "  $($reader['TABLE_NAME'])"
    }
    $reader.Close()
    
    if ($tableCount -eq 0) {
        Write-Host "  NO TABLES FOUND!" -ForegroundColor Red
    } else {
        Write-Host "  Total tables: $tableCount" -ForegroundColor Green
    }
    
    # Check migration history
    Write-Host "`nMigration History:" -ForegroundColor Yellow
    try {
        $cmd.CommandText = "SELECT MigrationId, ProductVersion FROM __EFMigrationsHistory ORDER BY MigrationId"
        $reader = $cmd.ExecuteReader()
        $migrationCount = 0
        while ($reader.Read()) {
            $migrationCount++
            Write-Host "  $($reader['MigrationId']) (EF $($reader['ProductVersion']))"
        }
        $reader.Close()
        
        if ($migrationCount -eq 0) {
            Write-Host "  No migrations applied!" -ForegroundColor Red
        } else {
            Write-Host "  Total migrations: $migrationCount" -ForegroundColor Green
        }
    } catch {
        Write-Host "  Migration table not found: $_" -ForegroundColor Red
    }
    
    # Check EventCategories
    Write-Host "`nEvent Categories:" -ForegroundColor Yellow
    try {
        $cmd.CommandText = "SELECT COUNT(*) FROM EventCategories"
        $count = $cmd.ExecuteScalar()
        Write-Host "  EventCategories: $count records"
        
        if ($count -gt 0) {
            $cmd.CommandText = "SELECT Id, DisplayNameKey FROM EventCategories ORDER BY Id"
            $reader = $cmd.ExecuteReader()
            while ($reader.Read()) {
                Write-Host "    $($reader['Id']): $($reader['DisplayNameKey'])"
            }
            $reader.Close()
        }
    } catch {
        Write-Host "  EventCategories table: $_" -ForegroundColor Red
    }
    
    $conn.Close()
}
catch {
    Write-Host "Connection failed: $_" -ForegroundColor Red
}