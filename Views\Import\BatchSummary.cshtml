@model ImportBatchSummaryViewModel
@{
    ViewBag.Title = $"Import Summary - {Model.BatchSummary.FileName}";
}

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2><i class="fas fa-chart-bar text-primary"></i> Import Summary</h2>
                <div>
                    <a href="@Url.Action("Upload", "Import")" class="btn btn-outline-primary me-2">
                        <i class="fas fa-plus"></i> New Import
                    </a>
                    <a href="@Url.Action("History", "Import")" class="btn btn-outline-secondary">
                        <i class="fas fa-history"></i> View History
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Batch Information -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card shadow-sm">
                <div class="card-header @(Model.BatchSummary.HasErrors ? "bg-danger" : (Model.BatchSummary.IsCompleted ? "bg-success" : "bg-info")) text-white">
                    <div class="row align-items-center">
                        <div class="col">
                            <h5 class="mb-0">
                                <i class="fas @(Model.BatchSummary.HasErrors ? "fa-exclamation-triangle" : (Model.BatchSummary.IsCompleted ? "fa-check-circle" : "fa-clock"))"></i>
                                @Model.BatchSummary.FileName
                            </h5>
                            <small>
                                Uploaded @Model.BatchSummary.UploadedAtLocal by @Model.BatchSummary.UploadedBy
                                <span class="badge @(Model.BatchSummary.StatusBadgeClass) ms-2">@Model.BatchSummary.Status</span>
                            </small>
                        </div>
                        <div class="col-auto">
                            <span class="badge bg-light text-dark fs-6">@Model.BatchSummary.TotalRows rows</span>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    @if (Model.BatchSummary.HasErrors)
                    {
                        <div class="alert alert-danger">
                            <h6><i class="fas fa-exclamation-triangle"></i> Import Error</h6>
                            <p class="mb-0">@Model.BatchSummary.ErrorMessage</p>
                        </div>
                    }
                    else
                    {
                        <!-- Progress Bar -->
                        <div class="mb-3">
                            <div class="d-flex justify-content-between mb-1">
                                <span class="text-muted">Processing Progress</span>
                                <span class="text-muted">@Model.BatchSummary.ProgressText</span>
                            </div>
                            <div class="progress">
                                <div class="progress-bar @(Model.BatchSummary.IsCompleted ? "bg-success" : "bg-info")" 
                                     role="progressbar" 
                                     style="width: @(Model.BatchSummary.ProgressPercentage)%">
                                </div>
                            </div>
                        </div>

                        @if (!string.IsNullOrEmpty(Model.BatchSummary.ErrorMessage))
                        {
                            <div class="alert alert-warning">
                                <h6><i class="fas fa-exclamation-triangle"></i> Processing Notes</h6>
                                <p class="mb-0">@Model.BatchSummary.ErrorMessage</p>
                            </div>
                        }
                    }
                </div>
            </div>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-xl-2 col-md-4 col-sm-6 mb-3">
            <div class="card text-center border-primary">
                <div class="card-body">
                    <i class="fas fa-upload text-primary fa-2x mb-2"></i>
                    <h4 class="text-primary mb-1">@Model.BatchSummary.ImportedCount</h4>
                    <small class="text-muted">Imported</small>
                </div>
            </div>
        </div>
        <div class="col-xl-2 col-md-4 col-sm-6 mb-3">
            <div class="card text-center border-success">
                <div class="card-body">
                    <i class="fas fa-check-circle text-success fa-2x mb-2"></i>
                    <h4 class="text-success mb-1">@Model.BatchSummary.ReadyToCreateCount</h4>
                    <small class="text-muted">Ready to Create</small>
                </div>
            </div>
        </div>
        <div class="col-xl-2 col-md-4 col-sm-6 mb-3">
            <div class="card text-center border-warning">
                <div class="card-body">
                    <i class="fas fa-exclamation-triangle text-warning fa-2x mb-2"></i>
                    <h4 class="text-warning mb-1">@Model.BatchSummary.NeedsFixCount</h4>
                    <small class="text-muted">Needs Fix</small>
                </div>
            </div>
        </div>
        <div class="col-xl-2 col-md-4 col-sm-6 mb-3">
            <div class="card text-center border-info">
                <div class="card-body">
                    <i class="fas fa-copy text-info fa-2x mb-2"></i>
                    <h4 class="text-info mb-1">@Model.BatchSummary.DuplicateCount</h4>
                    <small class="text-muted">Duplicates</small>
                </div>
            </div>
        </div>
        <div class="col-xl-2 col-md-4 col-sm-6 mb-3">
            <div class="card text-center border-success">
                <div class="card-body">
                    <i class="fas fa-user-plus text-success fa-2x mb-2"></i>
                    <h4 class="text-success mb-1">@Model.BatchSummary.CreatedCount</h4>
                    <small class="text-muted">Created</small>
                </div>
            </div>
        </div>
        <div class="col-xl-2 col-md-4 col-sm-6 mb-3">
            <div class="card text-center border-secondary">
                <div class="card-body">
                    <i class="fas fa-times-circle text-secondary fa-2x mb-2"></i>
                    <h4 class="text-secondary mb-1">@Model.BatchSummary.RejectedCount</h4>
                    <small class="text-muted">Rejected</small>
                </div>
            </div>
        </div>
    </div>

    <!-- Queue Management -->
    @if (Model.QueueStatuses.Any())
    {
        <div class="row">
            <div class="col-12">
                <div class="card shadow-sm">
                    <div class="card-header bg-light">
                        <h5 class="mb-0"><i class="fas fa-tasks"></i> Queue Management</h5>
                        <small class="text-muted">Click on a queue to manage members in that status</small>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            @foreach (var queue in Model.QueueStatuses.OrderBy(q => q.RequiresAction ? 0 : 1))
                            {
                                <div class="col-lg-6 col-xl-4 mb-3">
                                    <div class="card h-100 @(queue.RequiresAction ? "border-warning" : "border-light")">
                                        <div class="card-body">
                                            <div class="d-flex align-items-center mb-2">
                                                <i class="@queue.QueueIconClass @queue.QueueColorClass fa-lg me-2"></i>
                                                <h6 class="mb-0 flex-grow-1">@queue.QueueDisplayName</h6>
                                                @if (queue.RequiresAction)
                                                {
                                                    <span class="badge bg-warning text-dark">Action Required</span>
                                                }
                                            </div>
                                            <p class="text-muted small mb-2">@queue.QueueDescription</p>
                                            <div class="d-flex justify-content-between align-items-center">
                                                <span class="badge bg-primary">@queue.TotalItems items</span>
                                                @if (queue.TotalItems > 0)
                                                {
                                                    <a href="@Url.Action("Queue", "TempMembers", new { batchId = queue.ImportBatchId, status = queue.QueueType })" 
                                                       class="btn btn-sm btn-outline-primary">
                                                        <i class="fas fa-arrow-right"></i> Manage
                                                    </a>
                                                }
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            }
                        </div>
                    </div>
                </div>
            </div>
        </div>
    }

    <!-- Next Steps -->
    @if (Model.BatchSummary.CanProcessMore)
    {
        <div class="row mt-4">
            <div class="col-12">
                <div class="alert alert-info">
                    <h6><i class="fas fa-info-circle"></i> Next Steps</h6>
                    <p class="mb-2">Your import batch contains members that need attention:</p>
                    <ul class="mb-3">
                        @if (Model.BatchSummary.ReadyToCreateCount > 0)
                        {
                            <li><strong>@Model.BatchSummary.ReadyToCreateCount members</strong> are ready to be created - 
                                <a href="@Url.Action("Queue", "TempMembers", new { batchId = Model.BatchSummary.ImportBatchId, status = "ReadyToCreate" })">Review and create them</a>
                            </li>
                        }
                        @if (Model.BatchSummary.NeedsFixCount > 0)
                        {
                            <li><strong>@Model.BatchSummary.NeedsFixCount members</strong> have validation errors - 
                                <a href="@Url.Action("Queue", "TempMembers", new { batchId = Model.BatchSummary.ImportBatchId, status = "NeedsFix" })">Fix the errors</a>
                            </li>
                        }
                        @if (Model.BatchSummary.DuplicateCount > 0)
                        {
                            <li><strong>@Model.BatchSummary.DuplicateCount potential duplicates</strong> found - 
                                <a href="@Url.Action("Queue", "TempMembers", new { batchId = Model.BatchSummary.ImportBatchId, status = "Duplicate" })">Resolve duplicates</a>
                            </li>
                        }
                    </ul>
                    <div>
                        <a href="@Url.Action("Queue", "TempMembers", new { batchId = Model.BatchSummary.ImportBatchId, status = "ReadyToCreate" })" 
                           class="btn btn-success me-2">
                            <i class="fas fa-play"></i> Start Processing
                        </a>
                        <a href="@Url.Action("History", "Import")" class="btn btn-outline-secondary">
                            <i class="fas fa-list"></i> View All Imports
                        </a>
                    </div>
                </div>
            </div>
        </div>
    }
    else if (Model.BatchSummary.IsCompleted)
    {
        <div class="row mt-4">
            <div class="col-12">
                <div class="alert alert-success">
                    <h6><i class="fas fa-check-circle"></i> Import Completed Successfully!</h6>
                    <p class="mb-2">All members from this import batch have been processed:</p>
                    <ul class="mb-3">
                        <li><strong>@Model.BatchSummary.CreatedCount members</strong> were successfully created</li>
                        @if (Model.BatchSummary.MergedCount > 0)
                        {
                            <li><strong>@Model.BatchSummary.MergedCount members</strong> were merged with existing records</li>
                        }
                        @if (Model.BatchSummary.RejectedCount > 0)
                        {
                            <li><strong>@Model.BatchSummary.RejectedCount members</strong> were rejected during processing</li>
                        }
                    </ul>
                    <div>
                        <a href="@Url.Action("Upload", "Import")" class="btn btn-primary me-2">
                            <i class="fas fa-plus"></i> Import More Members
                        </a>
                        <a href="@Url.Action("Index", "Admin")" class="btn btn-outline-secondary">
                            <i class="fas fa-tachometer-alt"></i> Back to Dashboard
                        </a>
                    </div>
                </div>
            </div>
        </div>
    }
</div>

@section Scripts {
<script>
    // Auto-refresh progress if batch is still processing
    @if (Model.BatchSummary.Status == "Processing")
    {
        <text>
        setInterval(function() {
            // Refresh the page to get updated progress
            window.location.reload();
        }, 5000); // Refresh every 5 seconds
        </text>
    }
</script>
}