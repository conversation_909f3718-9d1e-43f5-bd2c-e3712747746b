@{
    ViewData["Title"] = SharedLocalizer["CalendarEvents"];
}

<div class="container-fluid mt-4">
    <div class="row">
        <div class="col">
            <h2 class="text-primary">
                <i class="fas fa-calendar-alt"></i> @SharedLocalizer["CalendarEvents"]
            </h2>
            <p class="text-muted">@SharedLocalizer["ManageEventsAndSubscriptions"]</p>
        </div>
    </div>

    <div class="row mb-4">
        <!-- Calendar Controls -->
        <div class="col-md-8">
            <div class="card">
                <!-- Desktop Header -->
                <div class="card-header d-flex justify-content-between align-items-center calendar-desktop-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-calendar"></i> @SharedLocalizer["EventCalendar"]
                    </h5>
                    <div>
                        <button type="button" class="btn btn-success btn-sm" id="addEventBtn">
                            <i class="fas fa-plus"></i> @SharedLocalizer["AddEvent"]
                        </button>
                        
                        <!-- Import/Export Buttons -->
                        <div class="dropdown d-inline-block">
                            <button class="btn btn-info btn-sm dropdown-toggle" type="button" id="importExportDropdown" data-bs-toggle="dropdown">
                                <i class="fas fa-file-import"></i> @SharedLocalizer["ImportExport"]
                            </button>
                            <ul class="dropdown-menu" aria-labelledby="importExportDropdown">
                                <li><a class="dropdown-item" href="#" data-bs-toggle="modal" data-bs-target="#importModal">
                                    <i class="fas fa-file-import text-success"></i> @SharedLocalizer["ImportEvents"]
                                </a></li>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item" href="#" data-bs-toggle="modal" data-bs-target="#exportModal">
                                    <i class="fas fa-file-export text-primary"></i> @SharedLocalizer["ExportEvents"]
                                </a></li>
                            </ul>
                        </div>
                        
                        <div class="dropdown d-inline-block">
                            <button class="btn btn-outline-secondary btn-sm dropdown-toggle" type="button" id="filterDropdown" data-bs-toggle="dropdown">
                                <i class="fas fa-filter"></i> @SharedLocalizer["Filter"]
                            </button>
                            <ul class="dropdown-menu" aria-labelledby="filterDropdown">
                                <li><a class="dropdown-item" href="#" data-filter="all">@SharedLocalizer["AllEvents"]</a></li>
                                @foreach (var category in ViewBag.EventCategories)
                                {
                                    <li><a class="dropdown-item" href="#" data-filter="@category.Id">
                                        <i class="@category.IconClass" style="color: @category.Color;"></i>
                                        @SharedLocalizer[category.DisplayNameKey]
                                    </a></li>
                                }
                            </ul>
                        </div>
                    </div>
                </div>

                <!-- Mobile Header -->
                <div class="card-header calendar-mobile-header">
                    <div class="d-flex justify-content-between align-items-center">
                        <div class="d-flex align-items-center">
                            <button class="btn btn-outline-secondary btn-sm me-2" id="mobilePrevBtn">
                                <i class="fas fa-chevron-left"></i>
                            </button>
                            <h6 class="mb-0 me-2" id="mobileCurrentPeriod">@SharedLocalizer["EventCalendar"]</h6>
                            <button class="btn btn-outline-secondary btn-sm" id="mobileNextBtn">
                                <i class="fas fa-chevron-right"></i>
                            </button>
                        </div>
                        <div>
                            <button class="btn btn-primary btn-sm me-2" id="mobileTodayBtn">
                                @SharedLocalizer["Today"]
                            </button>
                            <button class="btn btn-outline-secondary btn-sm me-2" id="mobileViewToggle" title="@SharedLocalizer["ToggleView"]">
                                <i class="fas fa-th"></i>
                            </button>
                            <div class="dropdown d-inline-block">
                                <button class="btn btn-outline-secondary btn-sm" type="button" id="mobileMoreMenu" data-bs-toggle="dropdown">
                                    <i class="fas fa-ellipsis-v"></i>
                                </button>
                                <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="mobileMoreMenu">
                                    <li><a class="dropdown-item" href="#" id="mobileAddEventBtn">
                                        <i class="fas fa-plus text-success"></i> @SharedLocalizer["AddEvent"]
                                    </a></li>
                                    <li><hr class="dropdown-divider"></li>
                                    <li><a class="dropdown-item" href="#" data-bs-toggle="modal" data-bs-target="#importModal">
                                        <i class="fas fa-file-import text-info"></i> @SharedLocalizer["ImportEvents"]
                                    </a></li>
                                    <li><a class="dropdown-item" href="#" data-bs-toggle="modal" data-bs-target="#exportModal">
                                        <i class="fas fa-file-export text-primary"></i> @SharedLocalizer["ExportEvents"]
                                    </a></li>
                                    <li><hr class="dropdown-divider"></li>
                                    <li><a class="dropdown-item" href="#" data-filter="all">
                                        <i class="fas fa-filter"></i> @SharedLocalizer["AllEvents"]
                                    </a></li>
                                    @foreach (var category in ViewBag.EventCategories)
                                    {
                                        <li><a class="dropdown-item" href="#" data-filter="@category.Id">
                                            <i class="@category.IconClass" style="color: @category.Color;"></i>
                                            @SharedLocalizer[category.DisplayNameKey]
                                        </a></li>
                                    }
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="card-body">
                    <!-- Desktop Calendar Grid -->
                    <div class="calendar-grid-view">
                        <div id="calendar" style="height: 600px;"></div>
                    </div>

                    <!-- Mobile Agenda View -->
                    <div class="calendar-agenda-view">
                        <div id="mobileAgenda" class="mobile-agenda-container">
                            <div class="text-center py-4">
                                <div class="spinner-border text-primary" role="status">
                                    <span class="visually-hidden">Loading...</span>
                                </div>
                                <p class="mt-2 text-muted">@SharedLocalizer["LoadingEvents"]</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Sidebar -->
        <div class="col-md-4">
            <!-- Statistics -->
            <div class="card mb-3">
                <div class="card-header">
                    <h6 class="card-title mb-0">
                        <i class="fas fa-chart-bar"></i> @SharedLocalizer["Statistics"]
                    </h6>
                </div>
                <div class="card-body">
                    <div class="row text-center">
                        <div class="col-6">
                            <div class="stat-item">
                                <span class="stat-number text-primary">@ViewBag.TotalEvents</span>
                                <span class="stat-label">@SharedLocalizer["TotalEvents"]</span>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="stat-item">
                                <span class="stat-number text-success">@ViewBag.PublishedEvents</span>
                                <span class="stat-label">@SharedLocalizer["PublishedEvents"]</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Upcoming Events -->
            <div class="card mb-3">
                <div class="card-header">
                    <h6 class="card-title mb-0">
                        <i class="fas fa-clock"></i> @SharedLocalizer["UpcomingEvents"]
                    </h6>
                </div>
                <div class="card-body">
                    @if (ViewBag.UpcomingEvents != null && ((List<ParaHockeyApp.Models.Entities.Event>)ViewBag.UpcomingEvents).Count > 0)
                    {
                        @foreach (var evt in (List<ParaHockeyApp.Models.Entities.Event>)ViewBag.UpcomingEvents)
                        {
                            <a href="#" class="upcoming-event-link d-block text-decoration-none text-body" data-event-id="@evt.Id" role="button" tabindex="0" aria-label="@SharedLocalizer["ViewEventDetails"]: @evt.Title">
                                <div class="event-item mb-2">
                                    <div class="d-flex align-items-center">
                                        <div class="event-color" style="background-color: @evt.EventCategory?.Color;"></div>
                                        <div class="flex-grow-1">
                                            <div class="event-title">@evt.Title</div>
                                            <div class="event-date text-muted">@evt.StartDate.ToString("MMM dd, yyyy")</div>
                                        </div>
                                    </div>
                                </div>
                            </a>
                        }
                    }
                    else
                    {
                        var upcomingEmptyState = ViewBag.UpcomingEventsEmptyState as ParaHockeyApp.Models.ViewModels.EmptyStateViewModel;
                        @if (upcomingEmptyState != null)
                        {
                            <div class="text-center py-3">
                                <i class="@upcomingEmptyState.IconClass fa-lg mb-2"></i>
                                <h6 class="text-muted small">@upcomingEmptyState.Title</h6>
                                <p class="text-muted small">@upcomingEmptyState.Message</p>
                            </div>
                        }
                        else
                        {
                            <p class="text-muted">@SharedLocalizer["NoUpcomingEvents"]</p>
                        }
                    }
                </div>
            </div>

            <!-- Event Categories -->
            <div class="card">
                <div class="card-header">
                    <h6 class="card-title mb-0">
                        <i class="fas fa-tags"></i> @SharedLocalizer["EventCategories"]
                    </h6>
                </div>
                <div class="card-body">
                    @if (ViewBag.EventCategories != null)
                    {
                        @foreach (var category in ViewBag.EventCategories)
                        {
                            <div class="category-item mb-2">
                                <div class="d-flex align-items-center">
                                    <div class="category-color" style="background-color: @category.Color;"></div>
                                    <i class="@category.IconClass me-2" style="color: @category.Color;"></i>
                                    <span>@SharedLocalizer[category.DisplayNameKey]</span>
                                </div>
                            </div>
                        }
                    }
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Event Modal -->
<div class="modal fade" id="eventModal" tabindex="-1" aria-labelledby="eventModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <ul class="nav nav-tabs modal-tabs" role="tablist">
                    <li class="nav-item">
                        <a class="nav-link active" id="event-details-tab" data-bs-toggle="tab" href="#event-details-pane" role="tab" aria-controls="event-details-pane" aria-selected="true">
                            <i class="fas fa-calendar-alt"></i> @SharedLocalizer["EventDetails"]
                        </a>
                    </li>
                    <li class="nav-item" id="registrations-tab-item" style="display: none;">
                        <a class="nav-link" id="registrations-tab" data-bs-toggle="tab" href="#registrations-pane" role="tab" aria-controls="registrations-pane" aria-selected="false">
                            <i class="fas fa-users"></i> @SharedLocalizer["Registrations"]
                            <span class="badge bg-primary ms-1" id="registration-count">0</span>
                        </a>
                    </li>
                </ul>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="tab-content">
                    <!-- Event Details Tab -->
                    <div class="tab-pane fade show active" id="event-details-pane" role="tabpanel" aria-labelledby="event-details-tab">
                <form id="eventForm">
                    <input type="hidden" id="eventId" name="id" value="" />
                    
                    <div class="row mb-3">
                        <div class="col-md-8">
                            <label for="eventTitle" class="form-label">@SharedLocalizer["Title"]</label>
                            <input type="text" class="form-control" id="eventTitle" name="title" required>
                        </div>
                        <div class="col-md-4">
                            <label for="eventCategory" class="form-label">@SharedLocalizer["Category"]</label>
                            <select class="form-select" id="eventCategory" name="eventCategoryId" required>
                                @foreach (var category in ViewBag.EventCategories)
                                {
                                    <option value="@category.Id">@SharedLocalizer[category.DisplayNameKey]</option>
                                }
                            </select>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="eventDescription" class="form-label">@SharedLocalizer["Description"]</label>
                        <textarea class="form-control" id="eventDescription" name="description" rows="3"></textarea>
                    </div>

                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label for="eventStartDate" class="form-label">@SharedLocalizer["StartDate"]</label>
                            <input type="datetime-local" class="form-control" id="eventStartDate" name="startDate" required>
                        </div>
                        <div class="col-md-6">
                            <label for="eventEndDate" class="form-label">@SharedLocalizer["EndDate"]</label>
                            <input type="datetime-local" class="form-control" id="eventEndDate" name="endDate" required>
                        </div>
                    </div>

                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label for="eventLocation" class="form-label">@SharedLocalizer["Location"]</label>
                            <input type="text" class="form-control" id="eventLocation" name="location">
                        </div>
                        <div class="col-md-6">
                            <label class="form-label">@SharedLocalizer["MaxParticipants"]</label>
                            <div class="form-check mb-2">
                                <input type="checkbox" class="form-check-input" id="eventUnlimitedParticipants">
                                <label class="form-check-label" for="eventUnlimitedParticipants">
                                    @SharedLocalizer["UnlimitedParticipants"]
                                </label>
                            </div>
                            <input type="number" class="form-control" id="eventMaxParticipants" name="maxParticipants" min="1" placeholder="@SharedLocalizer["MaxParticipants"]">
                            <div class="form-text">@SharedLocalizer["MaxParticipantsHelp"]</div>
                        </div>
                    </div>

                    <div class="row mb-3">
                        <div class="col-md-6">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="eventRequiresRegistration" name="requiresRegistration">
                                <label class="form-check-label" for="eventRequiresRegistration">
                                    @SharedLocalizer["RequiresRegistration"]
                                </label>
                            </div>
                        </div>
                    </div>

                    <div class="row mb-3">
                        <div class="col-md-6">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="eventIsPublished" name="isPublished">
                                <label class="form-check-label" for="eventIsPublished">
                                    @SharedLocalizer["PublishEvent"]
                                </label>
                            </div>
                        </div>
                    </div>
                </form>
                    </div>
                    
                    <!-- Registrations Tab -->
                    <div class="tab-pane fade" id="registrations-pane" role="tabpanel" aria-labelledby="registrations-tab">
                        <!-- Registration Summary -->
                        <div class="row mb-4">
                            <div class="col-md-8">
                                <div class="d-flex align-items-center">
                                    <h5 class="mb-0">@SharedLocalizer["EventRegistrations"]</h5>
                                    <div class="ms-3">
                                        <span class="badge bg-success me-1">
                                            <span id="confirmed-count">0</span> @SharedLocalizer["Confirmed"]
                                        </span>
                                        <span class="badge bg-warning me-1">
                                            <span id="pending-count">0</span> @SharedLocalizer["Pending"]
                                        </span>
                                        <span class="badge bg-info me-1">
                                            <span id="waitlisted-count">0</span> @SharedLocalizer["Waitlisted"]
                                        </span>
                                    </div>
                                </div>
                                <p class="text-muted mb-2">
                                    <span id="total-participants">0</span> @SharedLocalizer["TotalParticipants"] 
                                    (<span id="capacity-display">@SharedLocalizer["NoLimit"]</span>)
                                </p>
                                <div id="capacity-progress" class="progress" style="height: 6px; display: none;">
                                    <div class="progress-bar" role="progressbar" style="width: 0%"></div>
                                </div>
                            </div>
                            <div class="col-md-4 text-end">
                                <div class="btn-group">
                                    <button class="btn btn-outline-primary btn-sm" id="filter-btn">
                                        <i class="fas fa-filter"></i> @SharedLocalizer["Filter"]
                                    </button>
                                    <button class="btn btn-outline-success btn-sm" id="export-registrations-btn">
                                        <i class="fas fa-download"></i> @SharedLocalizer["ExportCSV"]
                                    </button>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Filter Controls -->
                        <div id="registration-filters" class="row mb-3" style="display: none;">
                            <div class="col-md-6">
                                <select class="form-select form-select-sm" id="status-filter">
                                    <option value="">@SharedLocalizer["AllStatuses"]</option>
                                    <option value="Confirmed">@SharedLocalizer["ConfirmedOnly"]</option>
                                    <option value="Pending">@SharedLocalizer["PendingOnly"]</option>
                                    <option value="Waitlisted">@SharedLocalizer["WaitlistedOnly"]</option>
                                    <option value="Cancelled">@SharedLocalizer["CancelledOnly"]</option>
                                </select>
                            </div>
                            <div class="col-md-6">
                                <input type="date" class="form-control form-control-sm" id="date-filter" placeholder="@SharedLocalizer["RegistrationDate"]" />
                            </div>
                        </div>
                        
                        <!-- Registrations List -->
                        <div id="registrations-loading" class="text-center py-4" style="display: none;">
                            <div class="spinner-border text-primary" role="status">
                                <span class="visually-hidden">@SharedLocalizer["LoadingRegistrations"]</span>
                            </div>
                        </div>
                        
                        <div id="registrations-content">
                            <div id="registrations-list" class="list-group">
                                <!-- Dynamic registration items will be loaded here -->
                            </div>
                            
                            <div id="no-registrations" class="text-center py-5" style="display: none;">
                                <i class="fas fa-user-slash fa-3x text-muted mb-3"></i>
                                <h5 class="text-muted">@SharedLocalizer["NoRegistrationsYet"]</h5>
                                <p class="text-muted">@SharedLocalizer["NoRegistrationsText"]</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">@SharedLocalizer["Cancel"]</button>
                <button type="button" class="btn btn-danger" id="deleteEventBtn" style="display:none;">@SharedLocalizer["Delete"]</button>
                <button type="button" class="btn btn-primary" id="saveEventBtn">@SharedLocalizer["Save"]</button>
            </div>
        </div>
    </div>
</div>

<!-- Import Events Modal -->
<div class="modal fade" id="importModal" tabindex="-1" aria-labelledby="importModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="importModalLabel">
                    <i class="fas fa-file-import text-success"></i> @SharedLocalizer["ImportEvents"]
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form asp-action="ImportEvents" method="post" enctype="multipart/form-data">
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="importFile" class="form-label">@SharedLocalizer["SelectFile"]</label>
                        <input type="file" class="form-control" id="importFile" name="file" accept=".csv,.xlsx" required>
                        <div class="form-text">@SharedLocalizer["ImportFileHelp"]</div>
                    </div>
                    
                    <div class="alert alert-info">
                        <h6 class="alert-heading">@SharedLocalizer["ImportInstructions"]</h6>
                        <p class="mb-2">@SharedLocalizer["ImportSupportedFormats"]:</p>
                        <ul class="mb-0">
                            <li>Excel (.xlsx)</li>
                            <li>CSV (.csv)</li>
                        </ul>
                        <hr>
                        <p class="mb-0">@SharedLocalizer["ImportExpectedColumns"]: Mois, Date, Heure, Catégorie, Aréna ou Site, Ville, Détails</p>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">@SharedLocalizer["Cancel"]</button>
                    <button type="submit" class="btn btn-success">
                        <i class="fas fa-upload"></i> @SharedLocalizer["ImportEvents"]
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Export Events Modal -->
<div class="modal fade" id="exportModal" tabindex="-1" aria-labelledby="exportModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="exportModalLabel">
                    <i class="fas fa-file-export text-primary"></i> @SharedLocalizer["ExportEvents"]
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="exportForm">
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label for="exportYear" class="form-label">@SharedLocalizer["Year"]</label>
                            <select class="form-select" id="exportYear" name="year">
                                @for (int year = DateTime.Now.Year - 1; year <= DateTime.Now.Year + 1; year++)
                                {
                                    <option value="@year" selected="@(year == DateTime.Now.Year)">@year</option>
                                }
                            </select>
                        </div>
                        <div class="col-md-6">
                            <label for="exportMonth" class="form-label">@SharedLocalizer["Month"]</label>
                            <select class="form-select" id="exportMonth" name="month">
                                @for (int month = 1; month <= 12; month++)
                                {
                                    var monthName = System.Globalization.DateTimeFormatInfo.CurrentInfo.GetMonthName(month);
                                    <option value="@month" selected="@(month == DateTime.Now.Month)">@monthName</option>
                                }
                            </select>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="exportFormat" class="form-label">@SharedLocalizer["FileFormat"]</label>
                        <select class="form-select" id="exportFormat" name="format">
                            <option value="excel">Excel (.xlsx)</option>
                            <option value="csv">CSV (.csv)</option>
                        </select>
                    </div>
                    
                    <div class="alert alert-info">
                        <h6 class="alert-heading">@SharedLocalizer["ExportInformation"]</h6>
                        <p class="mb-0">@SharedLocalizer["ExportDescription"]</p>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">@SharedLocalizer["Cancel"]</button>
                <button type="button" class="btn btn-primary" id="exportBtn">
                    <i class="fas fa-download"></i> @SharedLocalizer["ExportEvents"]
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Mobile Event Detail Modal -->
<div class="modal fade" id="mobileEventDetailModal" tabindex="-1" aria-labelledby="mobileEventDetailModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-fullscreen-sm-down">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="mobileEventDetailModalLabel">@SharedLocalizer["EventDetails"]</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div id="mobileEventContent">
                    <!-- Event details will be loaded here -->
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">@SharedLocalizer["Close"]</button>
                <button type="button" class="btn btn-primary" id="mobileEditEventBtn" style="display: none;">
                    <i class="fas fa-edit"></i> @SharedLocalizer["Edit"]
                </button>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <!-- FullCalendar CSS -->
    <link href="https://cdn.jsdelivr.net/npm/fullcalendar@6.1.8/index.global.min.css" rel="stylesheet">
    
    <!-- FullCalendar JS -->
    <script src="https://cdn.jsdelivr.net/npm/fullcalendar@6.1.8/index.global.min.js"></script>
    
    <!-- FullCalendar Locales -->
    <script src="https://cdn.jsdelivr.net/npm/fullcalendar@6.1.8/locales/fr.global.min.js"></script>
    
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const calendarEl = document.getElementById('calendar');
            const eventModal = new bootstrap.Modal(document.getElementById('eventModal'));
            const mobileEventModal = new bootstrap.Modal(document.getElementById('mobileEventDetailModal'));
            const eventForm = document.getElementById('eventForm');
            
            // ============= REGISTRATION TAB CONTROLLER =============
            class RegistrationTabController {
                constructor() {
                    this.eventId = null;
                    this.registrations = [];
                    this.currentFilter = '';
                    this.dateFilter = '';
                    this.isLoading = false;
                    this.setupEventListeners();
                }
                
                setupEventListeners() {
                    // Tab click handler for lazy loading
                    const registrationsTab = document.getElementById('registrations-tab');
                    registrationsTab.addEventListener('shown.bs.tab', (e) => {
                        console.log('🔍 Registrations tab clicked');
                        console.log('🔍 Tab loaded:', e.target.dataset.loaded);
                        console.log('🔍 Current eventId:', this.eventId);
                        const isLoaded = e.target.dataset.loaded === 'true';
                        console.log('🔍 Condition check: !loaded && eventId =', !isLoaded, '&&', !!this.eventId);
                        if (!isLoaded && this.eventId) {
                            console.log('🔍 Calling loadRegistrations...');
                            this.loadRegistrations(this.eventId);
                        } else {
                            console.log('🔍 Not loading registrations - reason:', {
                                alreadyLoaded: isLoaded,
                                hasEventId: !!this.eventId
                            });
                        }
                    });
                    
                    // Status filter handler
                    document.addEventListener('change', (e) => {
                        if (e.target.matches('#status-filter')) {
                            this.currentFilter = e.target.value;
                            this.renderRegistrationList();
                        }
                        if (e.target.matches('#date-filter')) {
                            this.dateFilter = e.target.value;
                            this.renderRegistrationList();
                        }
                    });
                    
                    // Filter button toggle
                    document.addEventListener('click', (e) => {
                        if (e.target.matches('#filter-btn') || e.target.closest('#filter-btn')) {
                            const filtersDiv = document.getElementById('registration-filters');
                            const isVisible = filtersDiv.style.display !== 'none';
                            filtersDiv.style.display = isVisible ? 'none' : 'block';
                        }
                    });
                    
                    // Export button handler
                    document.addEventListener('click', (e) => {
                        if (e.target.matches('#export-registrations-btn')) {
                            this.exportRegistrations();
                        }
                    });
                }
                
                async loadRegistrations(eventId) {
                    if (this.isLoading) return;
                    
                    console.log('🔍 Loading registrations for eventId:', eventId);
                    this.eventId = eventId;
                    this.isLoading = true;
                    this.showLoadingState();
                    
                    try {
                        const url = `@Url.Action("GetEventRegistrations", "Admin")/${eventId}`;
                        console.log('🔍 Fetching from URL:', url);
                        const response = await fetch(url);
                        console.log('🔍 Response status:', response.status);
                        
                        if (!response.ok) {
                            throw new Error(`HTTP error! status: ${response.status}`);
                        }
                        
                        const data = await response.json();
                        console.log('🔍 Response data:', data);
                        
                        if (data && data.registrations) {
                            console.log('🔍 Found registrations:', data.registrations.length);
                            this.registrations = data.registrations;
                            this.updateSummary(data.summary);
                            this.renderRegistrationList();
                            document.getElementById('registrations-tab').dataset.loaded = 'true';
                        } else {
                            console.log('🔍 No registrations found in response');
                            const errorMessage = @Json.Serialize(SharedLocalizer["ErrorLoadingRegistrations"].Value);
                            this.showErrorState(errorMessage);
                        }
                    } catch (error) {
                        console.error('🔍 Error loading registrations:', error);
                        const errorMessage = @Json.Serialize(SharedLocalizer["ErrorLoadingRegistrations"].Value);
                        this.showErrorState(errorMessage);
                    } finally {
                        this.isLoading = false;
                    }
                }
                
                showLoadingState() {
                    const container = document.getElementById('registrations-list');
                    container.innerHTML = `
                        <div class="text-center py-4">
                            <div class="spinner-border text-primary" role="status">
                                <span class="visually-hidden">@SharedLocalizer["Loading"]...</span>
                            </div>
                            <div class="mt-2">@SharedLocalizer["LoadingRegistrations"]...</div>
                        </div>
                    `;
                }
                
                showErrorState(message) {
                    const container = document.getElementById('registrations-list');
                    container.innerHTML = `
                        <div class="alert alert-danger" role="alert">
                            <i class="fas fa-exclamation-triangle"></i>
                            ${message}
                        </div>
                    `;
                }
                
                updateSummary(summary) {
                    console.log('🔍 Updating summary with:', summary);
                    
                    // Safely update elements that exist
                    const safeSetText = (id, value) => {
                        const element = document.getElementById(id);
                        if (element) {
                            element.textContent = value;
                        } else {
                            console.warn('🔍 Element not found:', id);
                        }
                    };
                    
                    safeSetText('registration-count', summary.totalRegistrations || 0);
                    safeSetText('confirmed-count', summary.confirmedCount || 0);
                    safeSetText('pending-count', summary.pendingCount || 0);
                    safeSetText('waitlisted-count', summary.waitlistedCount || 0);
                    safeSetText('total-participants', summary.totalParticipants || 0);
                    
                    if (summary.maxParticipants > 0) {
                        const percentage = Math.round((summary.totalParticipants / summary.maxParticipants) * 100);
                        safeSetText('capacity-display', `${summary.totalParticipants}/${summary.maxParticipants} (${percentage}%)`);
                        
                        // Show and update progress bar
                        const progressContainer = document.getElementById('capacity-progress');
                        const progressBar = progressContainer?.querySelector('.progress-bar');
                        if (progressContainer && progressBar) {
                            progressContainer.style.display = 'block';
                            progressBar.style.width = `${percentage}%`;
                            progressBar.setAttribute('aria-valuenow', percentage);
                            progressBar.setAttribute('aria-valuemin', '0');
                            progressBar.setAttribute('aria-valuemax', '100');
                            
                            // Color code based on capacity
                            progressBar.className = 'progress-bar';
                            if (percentage >= 90) {
                                progressBar.classList.add('bg-danger');
                            } else if (percentage >= 75) {
                                progressBar.classList.add('bg-warning');
                            } else {
                                progressBar.classList.add('bg-success');
                            }
                        }
                    } else {
                        safeSetText('capacity-display', '@SharedLocalizer["NoLimit"]');
                        // Hide progress bar for unlimited capacity
                        const progressContainer = document.getElementById('capacity-progress');
                        if (progressContainer) {
                            progressContainer.style.display = 'none';
                        }
                    }
                }
                
                renderRegistrationList() {
                    const container = document.getElementById('registrations-list');
                    const filteredRegistrations = this.getFilteredRegistrations();
                    
                    if (filteredRegistrations.length === 0) {
                        container.innerHTML = this.getEmptyStateHTML();
                        return;
                    }
                    
                    const html = filteredRegistrations.map(registration => this.getRegistrationItemHTML(registration)).join('');
                    container.innerHTML = html;
                    
                    // Setup event handlers for the rendered items
                    this.setupRegistrationItemHandlers();
                }
                
                getFilteredRegistrations() {
                    let filtered = this.registrations;
                    
                    // Filter by status
                    if (this.currentFilter && this.currentFilter !== '') {
                        filtered = filtered.filter(r => r.status === this.currentFilter);
                    }
                    
                    // Filter by registration date
                    if (this.dateFilter && this.dateFilter !== '') {
                        const filterDate = new Date(this.dateFilter);
                        filtered = filtered.filter(r => {
                            const registrationDate = new Date(r.registrationDate);
                            return registrationDate.toDateString() === filterDate.toDateString();
                        });
                    }
                    
                    return filtered;
                }
                
                getEmptyStateHTML() {
                    const noRegistrationsText = '@SharedLocalizer["NoRegistrations"]';
                    const noRegistrationsWithStatusText = '@SharedLocalizer["NoRegistrationsWithStatus"]';
                    const willAppearText = '@SharedLocalizer["RegistrationsWillAppearHere"]';
                    
                    const filterText = this.currentFilter === '' ? noRegistrationsText : noRegistrationsWithStatusText;
                    return `
                        <div class="empty-state text-center py-5">
                            <i class="fas fa-users fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">${filterText}</h5>
                            <p class="text-muted">${willAppearText}</p>
                        </div>
                    `;
                }
                
                getRegistrationItemHTML(registration) {
                    const statusColor = this.getStatusColor(registration.status);
                    const statusText = this.getStatusText(registration.status);
                    const registrationDate = new Date(registration.registrationDate).toLocaleDateString();
                    
                    return `
                        <div class="registration-item" data-registration-id="${registration.id}">
                            <div class="registration-item-header">
                                <div class="member-info">
                                    <strong>${registration.memberName}</strong>
                                    <a href="mailto:${registration.memberEmail}" class="text-decoration-none ms-2">
                                        <i class="fas fa-envelope" title="@SharedLocalizer["SendEmail"]"></i>
                                    </a>
                                </div>
                                <div class="registration-actions">
                                    <div class="dropdown">
                                        <button class="btn btn-sm btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                                            @SharedLocalizer["Actions"]
                                        </button>
                                        <ul class="dropdown-menu">
                                            <li><a class="dropdown-item change-status-btn" href="#" data-status="Confirmed">@SharedLocalizer["Confirm"]</a></li>
                                            <li><a class="dropdown-item change-status-btn" href="#" data-status="Pending">@SharedLocalizer["SetPending"]</a></li>
                                            <li><a class="dropdown-item change-status-btn" href="#" data-status="Waitlisted">@SharedLocalizer["SetWaitlisted"]</a></li>
                                            <li><a class="dropdown-item change-status-btn" href="#" data-status="Rejected">@SharedLocalizer["Reject"]</a></li>
                                            <li><hr class="dropdown-divider"></li>
                                            <li><a class="dropdown-item edit-note-btn" href="#">@SharedLocalizer["EditNote"]</a></li>
                                            <li><a class="dropdown-item mark-attendance-btn" href="#" data-attended="true">@SharedLocalizer["MarkPresent"]</a></li>
                                            <li><a class="dropdown-item mark-attendance-btn" href="#" data-attended="false">@SharedLocalizer["MarkAbsent"]</a></li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                            <div class="registration-item-body">
                                <div class="row">
                                    <div class="col-md-6">
                                        <small class="text-muted">@SharedLocalizer["Status"]:</small>
                                        <span class="badge" style="background-color: ${statusColor}">${statusText}</span>
                                    </div>
                                    <div class="col-md-6">
                                        <small class="text-muted">@SharedLocalizer["RegistrationDate"]:</small>
                                        <span>${registrationDate}</span>
                                    </div>
                                </div>
                                ${typeof registration.attended !== 'undefined' && registration.attended !== null ? `
                                <div class="row mt-2">
                                    <div class="col-md-6">
                                        <small class="text-muted">@SharedLocalizer["Attendance"]:</small>
                                        <span class="badge ${registration.attended ? 'bg-success' : 'bg-secondary'}">
                                            ${registration.attended ? @Json.Serialize(SharedLocalizer["Present"].Value) : @Json.Serialize(SharedLocalizer["Absent"].Value)}
                                        </span>
                                    </div>
                                </div>
                                ` : ''}
                                ${registration.guestCount > 0 ? `
                                <div class="mt-2">
                                    <small class="text-muted">@SharedLocalizer["Guests"]:</small>
                                    <span>${registration.guestCount}</span>
                                </div>
                                ` : ''}
                                ${registration.memberNotes ? `
                                <div class="mt-2">
                                    <small class="text-muted">@SharedLocalizer["MemberNotes"]:</small>
                                    <div class="text-muted">${registration.memberNotes}</div>
                                </div>
                                ` : ''}
                                ${registration.adminNotes ? `
                                <div class="mt-2">
                                    <small class="text-muted">@SharedLocalizer["AdminNotes"]:</small>
                                    <div class="admin-notes" data-registration-id="${registration.id}">${registration.adminNotes}</div>
                                </div>
                                ` : ''}
                            </div>
                        </div>
                    `;
                }
                
                setupRegistrationItemHandlers() {
                    // Status change handlers
                    document.querySelectorAll('.change-status-btn').forEach(btn => {
                        btn.addEventListener('click', async (e) => {
                            e.preventDefault();
                            const registrationId = e.target.closest('.registration-item').dataset.registrationId;
                            const newStatus = e.target.dataset.status;
                            await this.changeRegistrationStatus(registrationId, newStatus);
                        });
                    });
                    
                    // Edit note handlers
                    document.querySelectorAll('.edit-note-btn').forEach(btn => {
                        btn.addEventListener('click', (e) => {
                            e.preventDefault();
                            const registrationId = e.target.closest('.registration-item').dataset.registrationId;
                            this.editAdminNote(registrationId);
                        });
                    });
                    
                    // Mark attendance handlers
                    document.querySelectorAll('.mark-attendance-btn').forEach(btn => {
                        btn.addEventListener('click', async (e) => {
                            e.preventDefault();
                            const registrationId = e.target.closest('.registration-item').dataset.registrationId;
                            const attended = e.target.dataset.attended === 'true';
                            await this.markAttendance(registrationId, attended);
                        });
                    });
                }
                
                async changeRegistrationStatus(registrationId, newStatus) {
                    // Add loading state to registration item
                    const registrationItem = document.querySelector(`[data-registration-id="${registrationId}"]`);
                    const actionButton = registrationItem?.querySelector('.dropdown-toggle');
                    
                    if (actionButton) {
                        actionButton.disabled = true;
                        actionButton.innerHTML = '<i class="fas fa-spinner fa-spin"></i> @SharedLocalizer["Updating"]...';
                    }
                    
                    try {
                        const response = await this.makeRequestWithRetry(async () => {
                            return fetch('@Url.Action("UpdateRegistrationStatus", "Admin")', {
                                method: 'POST',
                                headers: {
                                    'Content-Type': 'application/json',
                                    'RequestVerificationToken': document.querySelector('input[name="__RequestVerificationToken"]').value
                                },
                                body: JSON.stringify({
                                    registrationId: parseInt(registrationId),
                                    status: newStatus
                                })
                            });
                        });
                        
                        if (response.ok) {
                            const result = await response.json();
                            if (result.success) {
                                showNotification(result.message || '@SharedLocalizer["RegistrationStatusUpdated"]', 'success');
                                await this.loadRegistrations(this.eventId); // Reload to get updated data
                            } else {
                                showNotification(result.error || '@SharedLocalizer["ErrorUpdatingStatus"]', 'error');
                                this.restoreActionButton(actionButton);
                            }
                        } else {
                            const result = await response.json().catch(() => ({}));
                            showNotification(result.error || '@SharedLocalizer["ErrorUpdatingStatus"]', 'error');
                            this.restoreActionButton(actionButton);
                        }
                    } catch (error) {
                        console.error('Error updating registration status:', error);
                        const errorMessage = error.message || '@SharedLocalizer["ErrorUpdatingStatus"]';
                        showNotification(errorMessage, 'error');
                        this.restoreActionButton(actionButton);
                    }
                }
                
                // Helper method to restore action button state
                restoreActionButton(actionButton) {
                    if (actionButton) {
                        actionButton.disabled = false;
                        actionButton.innerHTML = '@SharedLocalizer["Actions"]';
                    }
                }
                
                // Retry logic for network requests
                async makeRequestWithRetry(requestFunc, maxRetries = 2) {
                    let lastError;
                    
                    for (let attempt = 0; attempt <= maxRetries; attempt++) {
                        try {
                            const response = await requestFunc();
                            
                            // Check if it's a network error that we should retry
                            if (!response.ok && response.status >= 500) {
                                if (attempt < maxRetries) {
                                    console.log(`Request failed with status ${response.status}, retrying... (attempt ${attempt + 1})`);
                                    await this.delay(1000 * (attempt + 1)); // Exponential backoff
                                    continue;
                                }
                            }
                            
                            return response;
                        } catch (error) {
                            lastError = error;
                            if (attempt < maxRetries && this.isRetriableError(error)) {
                                console.log(`Request failed with error: ${error.message}, retrying... (attempt ${attempt + 1})`);
                                await this.delay(1000 * (attempt + 1)); // Exponential backoff
                                continue;
                            }
                            break;
                        }
                    }
                    
                    throw lastError || new Error('Request failed after retries');
                }
                
                // Check if error is retriable
                isRetriableError(error) {
                    return error.name === 'TypeError' || // Network errors
                           error.message.includes('fetch') ||
                           error.message.includes('network');
                }
                
                // Simple delay utility
                delay(ms) {
                    return new Promise(resolve => setTimeout(resolve, ms));
                }
                
                async addAdminNote(registrationId, note) {
                    try {
                        const response = await fetch('@Url.Action("AddAdminNote", "Admin")', {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json',
                                'RequestVerificationToken': document.querySelector('input[name="__RequestVerificationToken"]').value
                            },
                            body: JSON.stringify({
                                registrationId: parseInt(registrationId),
                                adminNote: note
                            })
                        });
                        
                        if (response.ok) {
                            showNotification('@SharedLocalizer["AdminNoteAdded"]', 'success');
                            await this.loadRegistrations(this.eventId); // Reload to get updated data
                        } else {
                            showNotification('@SharedLocalizer["ErrorAddingNote"]', 'error');
                        }
                    } catch (error) {
                        console.error('Error adding admin note:', error);
                        showNotification('@SharedLocalizer["ErrorAddingNote"]', 'error');
                    }
                }
                
                editAdminNote(registrationId) {
                    const registration = this.registrations.find(r => r.id == registrationId);
                    const currentNote = registration?.adminNotes || '';
                    
                    const promptText = @Json.Serialize(SharedLocalizer["EnterAdminNote"].Value);
                    const newNote = prompt(promptText, currentNote);
                    if (newNote !== null && newNote !== currentNote) {
                        this.addAdminNote(registrationId, newNote);
                    }
                }
                
                async markAttendance(registrationId, attended) {
                    try {
                        const response = await fetch('@Url.Action("MarkAttendance", "Admin")', {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json',
                                'RequestVerificationToken': document.querySelector('input[name="__RequestVerificationToken"]').value
                            },
                            body: JSON.stringify({
                                registrationId: parseInt(registrationId),
                                attended: attended
                            })
                        });
                        
                        if (response.ok) {
                            const attendanceText = attended ? @Json.Serialize(SharedLocalizer["MarkedPresent"].Value) : @Json.Serialize(SharedLocalizer["MarkedAbsent"].Value);
                            showNotification(attendanceText, 'success');
                            await this.loadRegistrations(this.eventId); // Reload to get updated data
                        } else {
                            const errorMessage = @Json.Serialize(SharedLocalizer["ErrorMarkingAttendance"].Value);
                            showNotification(errorMessage, 'error');
                        }
                    } catch (error) {
                        console.error('Error marking attendance:', error);
                        const errorMessage = @Json.Serialize(SharedLocalizer["ErrorMarkingAttendance"].Value);
                        showNotification(errorMessage, 'error');
                    }
                }
                
                async exportRegistrations() {
                    if (!this.eventId) return;
                    
                    try {
                        const response = await fetch(`@Url.Action("ExportEventRegistrations", "Admin")/${this.eventId}?status=${this.currentFilter}`);
                        if (response.ok) {
                            const blob = await response.blob();
                            const url = window.URL.createObjectURL(blob);
                            const a = document.createElement('a');
                            a.href = url;
                            a.download = `event-registrations-${this.eventId}.csv`;
                            document.body.appendChild(a);
                            a.click();
                            document.body.removeChild(a);
                            window.URL.revokeObjectURL(url);
                            const successMessage = @Json.Serialize(SharedLocalizer["ExportComplete"].Value);
                            showNotification(successMessage, 'success');
                        } else {
                            const errorMessage = @Json.Serialize(SharedLocalizer["ExportError"].Value);
                            showNotification(errorMessage, 'error');
                        }
                    } catch (error) {
                        console.error('Export error:', error);
                        const errorMessage = @Json.Serialize(SharedLocalizer["ExportError"].Value);
                        showNotification(errorMessage, 'error');
                    }
                }
                
                getStatusColor(status) {
                    const colors = {
                        'Confirmed': '#28a745',
                        'Pending': '#ffc107',
                        'Waitlisted': '#17a2b8',
                        'Rejected': '#dc3545',
                        'Cancelled': '#6c757d'
                    };
                    return colors[status] || '#6c757d';
                }
                
                getStatusText(status) {
                    const statusTexts = {
                        'Confirmed': @Json.Serialize(SharedLocalizer["Confirmed"].Value),
                        'Pending': @Json.Serialize(SharedLocalizer["Pending"].Value),
                        'Waitlisted': @Json.Serialize(SharedLocalizer["Waitlisted"].Value),
                        'Rejected': @Json.Serialize(SharedLocalizer["Rejected"].Value),
                        'Cancelled': @Json.Serialize(SharedLocalizer["Cancelled"].Value)
                    };
                    return statusTexts[status] || status;
                }
                
                reset() {
                    console.log('🔍 RegistrationTabController.reset() called');
                    this.eventId = null;
                    this.registrations = [];
                    this.currentFilter = '';
                    this.dateFilter = '';
                    this.isLoading = false;
                    
                    const tabElement = document.getElementById('registrations-tab');
                    if (tabElement) {
                        tabElement.dataset.loaded = 'false';
                        console.log('🔍 Reset tab loaded status to false');
                    }
                    
                    // Reset filter controls
                    const statusFilter = document.getElementById('status-filter');
                    const dateFilter = document.getElementById('date-filter');
                    if (statusFilter) statusFilter.value = '';
                    if (dateFilter) dateFilter.value = '';
                    
                    // Hide filters
                    const filtersDiv = document.getElementById('registration-filters');
                    if (filtersDiv) filtersDiv.style.display = 'none';
                }
            }
            
            // Initialize the registration tab controller
            const registrationTabController = new RegistrationTabController();
            
            // Define resetRegistrationTab function
            function resetRegistrationTab() {
                console.log('🔍 resetRegistrationTab called');
                registrationTabController.reset();
                
                // Safely reset elements
                const safeReset = (id, value) => {
                    const element = document.getElementById(id);
                    if (element) {
                        element.textContent = value || '0';
                    }
                };
                
                const listElement = document.getElementById('registrations-list');
                if (listElement) listElement.innerHTML = '';
                
                safeReset('registration-count', '0');
                safeReset('confirmed-count', '0');
                safeReset('pending-count', '0');
                safeReset('waitlisted-count', '0');
                safeReset('total-participants', '0');
                const noLimitText = '@SharedLocalizer["NoLimit"]';
                safeReset('capacity-display', noLimitText);
            }
            
            // Add modal close event handler to reset registration tab
            document.getElementById('eventModal').addEventListener('hidden.bs.modal', function () {
                resetRegistrationTab();
            });
            
            // Add tab switching event listeners to handle lazy loading
            document.getElementById('registrations-tab').addEventListener('shown.bs.tab', function (event) {
                const eventId = registrationTabController.eventId;
                if (eventId && !document.getElementById('registrations-tab').dataset.loaded) {
                    registrationTabController.loadRegistrations(eventId);
                }
            });
            
            // Get current locale from ASP.NET Core
            const currentCulture = '@System.Globalization.CultureInfo.CurrentCulture.TwoLetterISOLanguageName';
            const isEnglish = currentCulture === 'en';
            
            // Variable to store original event data for change detection
            let originalEventData = null;
            
            // Mobile agenda state
            let mobileAgendaState = {
                currentWeekStart: new Date(),
                events: [],
                filteredEvents: [],
                currentFilter: 'all',
                isMobileView: window.innerWidth < 768
            };
            
            // Initialize FullCalendar (Desktop)
            const calendar = new FullCalendar.Calendar(calendarEl, {
                locale: isEnglish ? 'en' : 'fr',
                initialView: 'dayGridMonth',
                headerToolbar: {
                    left: 'prev,next today',
                    center: 'title',
                    right: 'dayGridMonth,timeGridWeek,timeGridDay'
                },
                buttonText: {
                    today: isEnglish ? 'Today' : 'Aujourd\'hui',
                    month: isEnglish ? 'Month' : 'Mois',
                    week: isEnglish ? 'Week' : 'Semaine',
                    day: isEnglish ? 'Day' : 'Jour',
                    list: isEnglish ? 'List' : 'Liste'
                },
                events: '@Url.Action("GetCalendarEvents", "Admin")',
                eventClick: function(info) {
                    openAdminEventModal(info.event.id);
                },
                select: function(info) {
                    openAdminEventModal(null, info.startStr, info.endStr);
                },
                selectable: true,
                selectMirror: true,
                dayMaxEvents: true,
                height: 600,
                firstDay: 1, // Start week on Monday
                eventDidMount: function(info) {
                    // Add tooltip with event details
                    info.el.setAttribute('title', 
                        info.event.title + 
                        (info.event.extendedProps.location ? '\n' + info.event.extendedProps.location : '') +
                        (info.event.extendedProps.description ? '\n' + info.event.extendedProps.description : '')
                    );
                }
            });
            
            calendar.render();
            
            // Initialize mobile agenda view
            initializeMobileAgenda();
            
            // Desktop Add Event button
            document.getElementById('addEventBtn').addEventListener('click', function() {
                openAdminEventModal();
            });

            // Mobile Add Event button
            document.getElementById('mobileAddEventBtn').addEventListener('click', function() {
                openAdminEventModal();
            });
            
            // Save Event button
            document.getElementById('saveEventBtn').addEventListener('click', function() {
                saveEvent();
            });
            
            // Delete Event button
            document.getElementById('deleteEventBtn').addEventListener('click', function() {
                deleteEvent();
            });
            
            // Filter dropdown
            document.querySelectorAll('[data-filter]').forEach(function(item) {
                item.addEventListener('click', function(e) {
                    e.preventDefault();
                    const filter = this.getAttribute('data-filter');
                    filterEvents(filter);
                });
            });
            
            // Mobile Navigation Controls
            document.getElementById('mobilePrevBtn').addEventListener('click', function() {
                mobileAgendaState.currentWeekStart.setDate(mobileAgendaState.currentWeekStart.getDate() - 7);
                updateMobileAgendaView();
            });
            
            document.getElementById('mobileNextBtn').addEventListener('click', function() {
                mobileAgendaState.currentWeekStart.setDate(mobileAgendaState.currentWeekStart.getDate() + 7);
                updateMobileAgendaView();
            });
            
            document.getElementById('mobileTodayBtn').addEventListener('click', function() {
                mobileAgendaState.currentWeekStart = getWeekStart(new Date());
                updateMobileAgendaView();
            });
            
            document.getElementById('mobileViewToggle').addEventListener('click', function() {
                // Toggle between agenda and simplified grid view
                const gridView = document.querySelector('.calendar-grid-view');
                const agendaView = document.querySelector('.calendar-agenda-view');
                
                if (agendaView.style.display !== 'none') {
                    // Switch to grid view
                    agendaView.style.display = 'none';
                    gridView.style.display = 'block';
                    this.innerHTML = '<i class="fas fa-list"></i>';
                    this.title = 'Switch to Agenda View';
                } else {
                    // Switch to agenda view
                    gridView.style.display = 'none';
                    agendaView.style.display = 'block';
                    this.innerHTML = '<i class="fas fa-th"></i>';
                    this.title = 'Switch to Grid View';
                }
            });
            
            // Category dropdown change event listener for applying defaults
            document.getElementById('eventCategory').addEventListener('change', function() {
                applyCategoryDefaults();
            });
            
            // Mobile Edit Event button
            document.getElementById('mobileEditEventBtn').addEventListener('click', function() {
                const eventId = this.dataset.eventId;
                if (eventId) {
                    mobileEventModal.hide();
                    openAdminEventModal(eventId);
                }
            });
            
            // Window resize handler
            window.addEventListener('resize', function() {
                const wasMobile = mobileAgendaState.isMobileView;
                mobileAgendaState.isMobileView = window.innerWidth < 768;
                
                if (wasMobile !== mobileAgendaState.isMobileView) {
                    if (mobileAgendaState.isMobileView) {
                        loadMobileAgendaEvents();
                    } else {
                        calendar.refetchEvents();
                    }
                }
            });
            
            function initializeMobileAgenda() {
                mobileAgendaState.currentWeekStart = getWeekStart(new Date());
                if (mobileAgendaState.isMobileView) {
                    loadMobileAgendaEvents();
                }
            }
            
            function getWeekStart(date) {
                const d = new Date(date);
                const day = d.getDay();
                const diff = d.getDate() - day + (day === 0 ? -6 : 1); // Monday as start of week
                return new Date(d.setDate(diff));
            }
            
            function loadMobileAgendaEvents() {
                fetch('@Url.Action("GetCalendarEvents", "Admin")')
                    .then(response => response.json())
                    .then(events => {
                        mobileAgendaState.events = events;
                        filterMobileEvents();
                        updateMobileAgendaView();
                    })
                    .catch(error => {
                        console.error('Error loading events:', error);
                        showMobileAgendaError();
                    });
            }
            
            function filterMobileEvents() {
                if (mobileAgendaState.currentFilter === 'all') {
                    mobileAgendaState.filteredEvents = mobileAgendaState.events;
                } else {
                    mobileAgendaState.filteredEvents = mobileAgendaState.events.filter(event => 
                        event.eventCategoryId == mobileAgendaState.currentFilter
                    );
                }
            }
            
            function updateMobileAgendaView() {
                const container = document.getElementById('mobileAgenda');
                const weekStart = new Date(mobileAgendaState.currentWeekStart);
                const weekEnd = new Date(weekStart);
                weekEnd.setDate(weekStart.getDate() + 6);
                
                // Update header
                const periodText = formatDateRange(weekStart, weekEnd);
                document.getElementById('mobileCurrentPeriod').textContent = periodText;
                
                // Get events for the current week
                const weekEvents = getEventsForWeek(weekStart);
                
                // Group events by day
                const eventsByDay = groupEventsByDay(weekEvents);
                
                // Generate HTML
                let html = '';
                for (let i = 0; i < 7; i++) {
                    const currentDay = new Date(weekStart);
                    currentDay.setDate(weekStart.getDate() + i);
                    const dayKey = currentDay.toISOString().split('T')[0];
                    const dayEvents = eventsByDay[dayKey] || [];
                    
                    html += generateDayHTML(currentDay, dayEvents);
                }
                
                if (html === '') {
                    html = `
                        <div class="agenda-no-events">
                            <i class="fas fa-calendar-times"></i>
                            <p>@SharedLocalizer["NoEventsThisWeek"]</p>
                        </div>
                    `;
                }
                
                container.innerHTML = html;
                
                // Add event listeners to event cards
                container.querySelectorAll('.agenda-event-card').forEach(card => {
                    card.addEventListener('click', function() {
                        const eventId = this.dataset.eventId;
                        showMobileEventDetails(eventId);
                    });
                });
            }
            
            function getEventsForWeek(weekStart) {
                const weekEnd = new Date(weekStart);
                weekEnd.setDate(weekStart.getDate() + 6);
                weekEnd.setHours(23, 59, 59, 999);
                
                return mobileAgendaState.filteredEvents.filter(event => {
                    const eventStart = new Date(event.start);
                    return eventStart >= weekStart && eventStart <= weekEnd;
                });
            }
            
            function groupEventsByDay(events) {
                const grouped = {};
                events.forEach(event => {
                    const dayKey = event.start.split('T')[0];
                    if (!grouped[dayKey]) {
                        grouped[dayKey] = [];
                    }
                    grouped[dayKey].push(event);
                });
                
                // Sort events within each day by start time
                Object.keys(grouped).forEach(day => {
                    grouped[day].sort((a, b) => new Date(a.start) - new Date(b.start));
                });
                
                return grouped;
            }
            
            function generateDayHTML(date, events) {
                const dayName = date.toLocaleDateString(currentCulture, { weekday: 'long' });
                const dayDate = date.toLocaleDateString(currentCulture, { month: 'short', day: 'numeric' });
                const isToday = date.toDateString() === new Date().toDateString();
                
                let html = `
                    <div class="agenda-day-header ${isToday ? 'bg-primary text-white' : ''}">
                        <h6>${dayName} <span class="day-date">${dayDate}</span></h6>
                    </div>
                `;
                
                if (events.length === 0) {
                    html += `
                        <div class="agenda-empty-day">
                            <i class="fas fa-calendar-day"></i>
                            <span>@SharedLocalizer["NoEventsScheduled"]</span>
                        </div>
                    `;
                } else {
                    events.forEach(event => {
                        html += generateEventCardHTML(event);
                    });
                }
                
                return html;
            }
            
            function generateEventCardHTML(event) {
                const startTime = new Date(event.start).toLocaleTimeString(currentCulture, { 
                    hour: '2-digit', 
                    minute: '2-digit' 
                });
                
                const categoryColor = event.backgroundColor || '#007bff';
                const location = event.extendedProps?.location || '';
                const categoryName = event.extendedProps?.categoryName || '';
                
                return `
                    <div class="agenda-event-card" data-event-id="${event.id}" style="--event-color: ${categoryColor}">
                        <div class="agenda-event-time">${startTime}</div>
                        <div class="agenda-event-title">${event.title}</div>
                        ${location ? `<div class="agenda-event-location"><i class="fas fa-map-marker-alt"></i> ${location}</div>` : ''}
                        ${categoryName ? `<div class="agenda-event-category" style="background-color: ${categoryColor}20; color: ${categoryColor};">${categoryName}</div>` : ''}
                    </div>
                `;
            }
            
            function formatDateRange(start, end) {
                const startStr = start.toLocaleDateString(currentCulture, { month: 'short', day: 'numeric' });
                const endStr = end.toLocaleDateString(currentCulture, { month: 'short', day: 'numeric' });
                return `${startStr} - ${endStr}`;
            }
            
            function showMobileEventDetails(eventId) {
                fetch('@Url.Action("GetEventDetails", "Admin")/' + eventId)
                    .then(response => response.json())
                    .then(data => {
                        if (data) {
                            const startDate = new Date(data.startDate);
                            const endDate = new Date(data.endDate);
                            
                            // Localized strings
                            const noDescriptionText = '@Html.Raw(SharedLocalizer["NoDescription"].Value.Replace("'", "\\'"))';
                            const startDateText = '@Html.Raw(SharedLocalizer["StartDate"].Value.Replace("'", "\\'"))';
                            const endDateText = '@Html.Raw(SharedLocalizer["EndDate"].Value.Replace("'", "\\'"))';
                            const locationText = '@Html.Raw(SharedLocalizer["Location"].Value.Replace("'", "\\'"))';
                            const maxParticipantsText = '@Html.Raw(SharedLocalizer["MaxParticipants"].Value.Replace("'", "\\'"))';
                            const unlimitedParticipantsText = '@Html.Raw(SharedLocalizer["UnlimitedParticipants"].Value.Replace("'", "\\'"))';
                            
                            const html = `
                                <div class="mb-3">
                                    <h5>${data.title}</h5>
                                    <p class="text-muted">${data.description || noDescriptionText}</p>
                                </div>
                                <div class="row mb-3">
                                    <div class="col-6">
                                        <strong>${startDateText}:</strong><br>
                                        ${startDate.toLocaleDateString(currentCulture)} ${startDate.toLocaleTimeString(currentCulture)}
                                    </div>
                                    <div class="col-6">
                                        <strong>${endDateText}:</strong><br>
                                        ${endDate.toLocaleDateString(currentCulture)} ${endDate.toLocaleTimeString(currentCulture)}
                                    </div>
                                </div>
                                ${data.location ? `<div class="mb-3"><strong>${locationText}:</strong> ${data.location}</div>` : ''}
                                <div class="mb-3"><strong>${maxParticipantsText}:</strong> ${data.maxParticipants > 0 ? data.maxParticipants : unlimitedParticipantsText}</div>
                            `;
                            
                            document.getElementById('mobileEventContent').innerHTML = html;
                            document.getElementById('mobileEditEventBtn').dataset.eventId = eventId;
                            document.getElementById('mobileEditEventBtn').style.display = 'inline-block';
                            mobileEventModal.show();
                        }
                    })
                    .catch(error => {
                        console.error('Error loading event details:', error);
                        showNotification('Error loading event details', 'error');
                    });
            }
            
            function showMobileAgendaError() {
                document.getElementById('mobileAgenda').innerHTML = `
                    <div class="agenda-no-events">
                        <i class="fas fa-exclamation-triangle text-warning"></i>
                        <p>@SharedLocalizer["ErrorLoadingEvents"]</p>
                        <button class="btn btn-primary btn-sm" onclick="loadMobileAgendaEvents()">
                            @SharedLocalizer["TryAgain"]
                        </button>
                    </div>
                `;
            }
            
            function setupMaxParticipantsHandler() {
                const unlimitedCheckbox = document.getElementById('eventUnlimitedParticipants');
                const maxParticipantsInput = document.getElementById('eventMaxParticipants');
                
                unlimitedCheckbox.addEventListener('change', function() {
                    if (this.checked) {
                        maxParticipantsInput.disabled = true;
                        maxParticipantsInput.value = '';
                        maxParticipantsInput.style.opacity = '0.5';
                    } else {
                        maxParticipantsInput.disabled = false;
                        maxParticipantsInput.style.opacity = '1';
                    }
                });
            }
            
            // NOTE: UTC to local time conversion removed as it was causing time shifts
            // The server should already provide dates in the correct format for datetime-local inputs
            
            // Capture current form data for change detection
            function captureEventData() {
                try {
                    const titleEl = document.getElementById('eventTitle');
                    const descEl = document.getElementById('eventDescription');
                    const startEl = document.getElementById('eventStartDate');
                    const endEl = document.getElementById('eventEndDate');
                    const locationEl = document.getElementById('eventLocation');
                    const categoryEl = document.getElementById('eventCategory');
                    const unlimitedEl = document.getElementById('eventUnlimitedParticipants');
                    const maxParticipantsEl = document.getElementById('eventMaxParticipants');
                    const registrationEl = document.getElementById('eventRequiresRegistration');
                    const publishedEl = document.getElementById('eventIsPublished');
                    
                    if (!titleEl || !descEl || !startEl || !endEl || !locationEl || !categoryEl || 
                        !unlimitedEl || !maxParticipantsEl || !registrationEl || !publishedEl) {
                        return null;
                    }
                    
                    return {
                        title: titleEl.value.trim(),
                        description: descEl.value.trim(),
                        startDate: startEl.value,
                        endDate: endEl.value,
                        location: locationEl.value.trim(),
                        eventCategoryId: parseInt(categoryEl.value) || 0,
                        maxParticipants: unlimitedEl.checked ? -1 : (parseInt(maxParticipantsEl.value) || -1),
                        requiresRegistration: registrationEl.checked,
                        isPublished: publishedEl.checked
                    };
                } catch (error) {
                    console.error('Error capturing event data:', error);
                    return null;
                }
            }
            
            // Deep compare two objects
            function deepEqual(obj1, obj2) {
                if (obj1 === obj2) return true;
                if (obj1 == null || obj2 == null) return false;
                if (typeof obj1 !== 'object' || typeof obj2 !== 'object') return false;
                
                const keys1 = Object.keys(obj1);
                const keys2 = Object.keys(obj2);
                
                if (keys1.length !== keys2.length) return false;
                
                for (let key of keys1) {
                    if (!keys2.includes(key)) return false;
                    if (!deepEqual(obj1[key], obj2[key])) return false;
                }
                
                return true;
            }
            
            function applyCategoryDefaults() {
                const categorySelect = document.getElementById('eventCategory');
                const requiresRegistrationCheckbox = document.getElementById('eventRequiresRegistration');
                const isPublishedCheckbox = document.getElementById('eventIsPublished');
                
                if (!categorySelect || !requiresRegistrationCheckbox || !isPublishedCheckbox) {
                    return;
                }
                
                const selectedCategoryId = parseInt(categorySelect.value);
                const categoryOptions = categorySelect.options;
                let categoryDisplayNameKey = '';
                
                // Find the DisplayNameKey for the selected category
                for (let i = 0; i < categoryOptions.length; i++) {
                    if (parseInt(categoryOptions[i].value) === selectedCategoryId) {
                        // We need to map category IDs to DisplayNameKeys
                        // Based on CLAUDE.md, we know:
                        // Practice: ID 1, DisplayNameKey: "EventCategory_Practice"
                        // Tentative: ID 9, DisplayNameKey: "EventCategory_Tentative"
                        const categoryText = categoryOptions[i].text.toLowerCase();
                        
                        if (categoryText.includes('practice') || categoryText.includes('pratique')) {
                            categoryDisplayNameKey = 'EventCategory_Practice';
                        } else if (categoryText.includes('tentative') || categoryText.includes('tentatif')) {
                            categoryDisplayNameKey = 'EventCategory_Tentative';
                        } else {
                            categoryDisplayNameKey = 'Other';
                        }
                        break;
                    }
                }
                
                // Apply defaults based on category
                switch (categoryDisplayNameKey) {
                    case 'EventCategory_Tentative':
                        // Tentative events: both RequiresRegistration and IsPublished are false
                        requiresRegistrationCheckbox.checked = false;
                        isPublishedCheckbox.checked = false;
                        break;
                    
                    case 'EventCategory_Practice':
                        // Practice events: RequiresRegistration is false, IsPublished is true
                        requiresRegistrationCheckbox.checked = false;
                        isPublishedCheckbox.checked = true;
                        break;
                    
                    default:
                        // All other categories: both RequiresRegistration and IsPublished are true
                        requiresRegistrationCheckbox.checked = true;
                        isPublishedCheckbox.checked = true;
                        break;
                }
            }
            
            function openAdminEventModal(eventId = null, startDate = null, endDate = null) {
                // Reset both tabs
                document.getElementById('event-details-tab').click(); // Show event details first
                resetEventForm();
                resetRegistrationTab();
                
                if (eventId) {
                    // Edit mode - load event details
                    fetch('@Url.Action("GetEventDetails", "Admin")/' + eventId)
                        .then(response => response.json())
                        .then(data => {
                            if (data) {
                                populateEventForm(data);
                                updateRegistrationTabVisibility(data);
                            }
                        });
                } else {
                    // Create mode
                    hideRegistrationTab();
                    if (startDate) {
                        document.getElementById('eventStartDate').value = startDate;
                    }
                    if (endDate) {
                        document.getElementById('eventEndDate').value = endDate;
                    }
                    document.getElementById('deleteEventBtn').style.display = 'none';
                    
                    // Apply category defaults for new events
                    applyCategoryDefaults();
                }
                
                eventModal.show();
            }
            
            function resetEventForm() {
                eventForm.reset();
                document.getElementById('eventId').value = '';
            }
            
            
            function populateEventForm(data) {
                // CRITICAL: Set the eventId for updates - this was missing and causing duplicates!
                document.getElementById('eventId').value = data.id || '';

                document.getElementById('eventTitle').value = data.title || '';
                document.getElementById('eventDescription').value = data.description || '';
                document.getElementById('eventStartDate').value = data.startDate || '';
                document.getElementById('eventEndDate').value = data.endDate || '';
                document.getElementById('eventLocation').value = data.location || '';
                document.getElementById('eventCategory').value = data.eventCategoryId || '';

                // Handle max participants with unlimited checkbox
                const isUnlimited = !data.maxParticipants || data.maxParticipants === -1;
                document.getElementById('eventUnlimitedParticipants').checked = isUnlimited;
                document.getElementById('eventMaxParticipants').value = isUnlimited ? '' : data.maxParticipants;
                document.getElementById('eventMaxParticipants').disabled = isUnlimited;
                document.getElementById('eventMaxParticipants').style.opacity = isUnlimited ? '0.5' : '1';
                document.getElementById('eventRequiresRegistration').checked = data.requiresRegistration || false;
                document.getElementById('eventIsPublished').checked = data.isPublished || false;

                document.getElementById('deleteEventBtn').style.display = 'inline-block';
            }
            
            function updateRegistrationTabVisibility(eventData) {
                const registrationsTabItem = document.getElementById('registrations-tab-item');
                
                console.log('🔍 updateRegistrationTabVisibility called with:', eventData);
                console.log('🔍 requiresRegistration:', eventData.requiresRegistration);
                console.log('🔍 eventId:', eventData.id);
                
                if (eventData.requiresRegistration && eventData.id) {
                    console.log('🔍 Showing registration tab for event:', eventData.id);
                    registrationsTabItem.style.display = 'block';
                    
                    // Update the registration controller with the event ID
                    registrationTabController.eventId = eventData.id;
                    console.log('🔍 Set registrationTabController.eventId to:', registrationTabController.eventId);
                    
                    // Load registration count immediately for the badge
                    loadRegistrationCount(eventData.id);
                } else {
                    console.log('🔍 Hiding registration tab');
                    registrationsTabItem.style.display = 'none';
                    // Reset count to 0 when hiding
                    document.getElementById('registration-count').textContent = '0';
                }
            }
            
            function hideRegistrationTab() {
                document.getElementById('registrations-tab-item').style.display = 'none';
            }
            
            // Load just the registration count for the badge
            function loadRegistrationCount(eventId) {
                console.log('🔍 Loading registration count for event:', eventId);
                
                fetch(`@Url.Action("GetEventRegistrations", "Admin")/${eventId}`)
                    .then(response => response.json())
                    .then(data => {
                        console.log('🔍 Registration count response:', data);
                        if (data.registrations) {
                            const count = data.registrations.length;
                            document.getElementById('registration-count').textContent = count;
                            console.log('🔍 Updated registration count badge to:', count);
                        } else {
                            document.getElementById('registration-count').textContent = '0';
                            console.log('🔍 No registrations found, set count to 0');
                        }
                    })
                    .catch(error => {
                        console.error('🔍 Error loading registration count:', error);
                        document.getElementById('registration-count').textContent = '0';
                    });
            }
            
            // Old loadRegistrations function - now handled by RegistrationTabController
            // function loadRegistrations(eventId) {
            //     // Show loading state
            //     document.getElementById('registrations-loading').style.display = 'block';
            //     document.getElementById('registrations-content').style.display = 'none';
            //     
            //     // TODO: This will be implemented in the next phase
            //     // For now, just show empty state
            //     setTimeout(() => {
            //         document.getElementById('registrations-loading').style.display = 'none';
            //         document.getElementById('registrations-content').style.display = 'block';
            //         document.getElementById('no-registrations').style.display = 'block';
            //     }, 500);
            // }
            
            function saveEvent() {
                // TODO: Add change detection back after fixing the basic functionality
                // const eventId = document.getElementById('eventId').value;
                // if (eventId && originalEventData) {
                //     const currentEventData = captureEventData();
                //     if (currentEventData && deepEqual(originalEventData, currentEventData)) {
                //         showNotification('@SharedLocalizer["NoChangesDetected"]', 'info');
                //         eventModal.hide();
                //         return;
                //     }
                // }
                
                // Get the datetime-local values
                const startDateValue = document.getElementById('eventStartDate').value;
                const endDateValue = document.getElementById('eventEndDate').value;
                
                // Validate dates first
                if (!startDateValue || !endDateValue) {
                    showNotification('Start and end dates are required', 'error');
                    return;
                }
                
                // Parse datetime-local values (format: YYYY-MM-DDTHH:mm)
                // datetime-local is in local timezone, convert to UTC for server
                const startDate = new Date(startDateValue);
                const endDate = new Date(endDateValue);
                
                // Check if dates are valid
                if (isNaN(startDate.getTime()) || isNaN(endDate.getTime())) {
                    showNotification('Invalid date format', 'error');
                    return;
                }
                
                // Build event data object manually with proper property names
                const eventData = {
                    Title: document.getElementById('eventTitle').value.trim(),
                    Description: document.getElementById('eventDescription').value.trim(),
                    StartDate: startDateValue,
                    EndDate: endDateValue,
                    IsAllDay: false, // Default to false since checkbox removed
                    Location: document.getElementById('eventLocation').value.trim(),
                    EventCategoryId: parseInt(document.getElementById('eventCategory').value),
                    RequiresRegistration: document.getElementById('eventRequiresRegistration').checked,
                    MaxParticipants: document.getElementById('eventUnlimitedParticipants').checked ? -1 : (parseInt(document.getElementById('eventMaxParticipants').value) || -1),
                    IsPublished: document.getElementById('eventIsPublished').checked,
                    ContactPerson: '', // Default empty since field removed
                    ContactEmail: '', // Default empty since field removed
                    ContactPhone: '', // Default empty since field removed
                    OrganizerNotes: '', // Default empty since field removed
                    Priority: 3, // Default priority
                    IsRecurring: false, // Default
                    RecurrencePattern: null
                };
                
                // Validation
                if (!eventData.Title) {
                    const titleRequiredMessage = @Json.Serialize(SharedLocalizer["Title"].Value + " is required");
                    showNotification(titleRequiredMessage, 'error');
                    return;
                }
                
                if (eventData.EventCategoryId <= 0) {
                    showNotification('Category is required', 'error');
                    return;
                }
                
                if (startDate >= endDate) {
                    showNotification('End date must be after start date', 'error');
                    return;
                }
                
                const eventId = document.getElementById('eventId').value;
                const url = eventId ? 
                    '@Url.Action("UpdateEvent", "Admin")/' + eventId : 
                    '@Url.Action("CreateEvent", "Admin")';
                
                fetch(url, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(eventData)
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        eventModal.hide();
                        calendar.refetchEvents();
                        showNotification('Event saved successfully!', 'success');
                    } else {
                        showNotification('Error: ' + data.error, 'error');
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    showNotification('An error occurred while saving the event.', 'error');
                });
            }
            
            function deleteEvent() {
                const eventId = document.getElementById('eventId').value;
                if (!eventId) return;
                
                const confirmMessage = @Json.Serialize(SharedLocalizer["ConfirmDeleteEvent"].Value);
                if (confirm(confirmMessage)) {
                    fetch('@Url.Action("DeleteEvent", "Admin")/' + eventId, {
                        method: 'POST'
                    })
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            eventModal.hide();
                            calendar.refetchEvents();
                            showNotification('Event deleted successfully!', 'success');
                        } else {
                            showNotification('Error: ' + data.error, 'error');
                        }
                    })
                    .catch(error => {
                        console.error('Error:', error);
                        showNotification('An error occurred while deleting the event.', 'error');
                    });
                }
            }
            
            function filterEvents(categoryId) {
                mobileAgendaState.currentFilter = categoryId;
                
                if (mobileAgendaState.isMobileView) {
                    filterMobileEvents();
                    updateMobileAgendaView();
                } else {
                    // For desktop, remove existing event sources and add new one with filter
                    calendar.getEventSources().forEach(function(eventSource) {
                        eventSource.remove();
                    });
                    
                    // Add new event source with filter
                    var url = '@Url.Action("GetCalendarEvents", "Admin")';
                    if (categoryId !== 'all') {
                        url += '?categoryId=' + categoryId;
                    }
                    
                    calendar.addEventSource({
                        url: url,
                        method: 'GET',
                        failure: function() {
                            const errorMessage = @Json.Serialize(SharedLocalizer["ErrorLoadingEvents"].Value);
                            showNotification(errorMessage, 'error');
                        }
                    });
                }
            }
            
            function showNotification(message, type) {
                // Simple notification system - you can enhance this
                let alertClass = 'alert-danger';
                if (type === 'success') alertClass = 'alert-success';
                else if (type === 'info') alertClass = 'alert-info';
                
                const alertHtml = `
                    <div class="alert ${alertClass} alert-dismissible fade show" role="alert">
                        ${message}
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                `;
                
                // Insert notification at the top of the page
                document.querySelector('.container-fluid').insertAdjacentHTML('afterbegin', alertHtml);
                
                // Auto-dismiss after 5 seconds
                setTimeout(() => {
                    const alert = document.querySelector('.alert');
                    if (alert) alert.remove();
                }, 5000);
            }
            
            // Export functionality
            document.getElementById('exportBtn').addEventListener('click', function() {
                const year = document.getElementById('exportYear').value;
                const month = document.getElementById('exportMonth').value;
                const format = document.getElementById('exportFormat').value;
                
                // Create download URL
                const url = '@Url.Action("ExportEvents", "Admin")' + 
                           '?format=' + encodeURIComponent(format) + 
                           '&year=' + encodeURIComponent(year) + 
                           '&month=' + encodeURIComponent(month);
                
                // Create temporary link to trigger download
                const link = document.createElement('a');
                link.href = url;
                link.download = '';
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);
                
                // Close modal
                bootstrap.Modal.getInstance(document.getElementById('exportModal')).hide();
                
                showNotification('Export started! Your file will download shortly.', 'success');
            });
            
            // Setup max participants handler
            setupMaxParticipantsHandler();
            
            // Add click handler for upcoming events sidebar
            document.addEventListener('click', function(e) {
                const link = e.target.closest('.upcoming-event-link');
                if (!link) return;
                
                e.preventDefault();
                const eventId = link.dataset.eventId;
                if (eventId) {
                    openAdminEventModal(eventId);
                }
            });
            
            // Add keyboard support for upcoming events sidebar
            document.addEventListener('keydown', function(e) {
                if (e.key !== 'Enter' && e.key !== ' ') return;
                
                const link = e.target.closest('.upcoming-event-link');
                if (!link) return;
                
                e.preventDefault();
                const eventId = link.dataset.eventId;
                if (eventId) {
                    openAdminEventModal(eventId);
                }
            });
            
        });
    </script>
    
    <style>
        /* === RESPONSIVE CALENDAR STYLES === */
        
        /* Default Mobile-First Styles */
        .calendar-desktop-header {
            display: none;
        }
        
        .calendar-mobile-header {
            display: block;
        }
        
        .calendar-grid-view {
            display: none;
        }
        
        .calendar-agenda-view {
            display: block;
        }
        
        /* Desktop Styles */
        @@media (min-width: 768px) {
            .calendar-desktop-header {
                display: flex;
            }
            
            .calendar-mobile-header {
                display: none;
            }
            
            .calendar-grid-view {
                display: block;
            }
            
            .calendar-agenda-view {
                display: none;
            }
        }
        
        /* === MOBILE AGENDA VIEW STYLES === */
        
        .mobile-agenda-container {
            min-height: 400px;
            max-height: 70vh;
            overflow-y: auto;
            -webkit-overflow-scrolling: touch;
        }
        
        .agenda-day-header {
            position: sticky;
            top: 0;
            background: #f8f9fa;
            border-bottom: 2px solid #dee2e6;
            padding: 12px 16px;
            margin-bottom: 8px;
            z-index: 10;
        }
        
        .agenda-day-header h6 {
            margin: 0;
            font-weight: 600;
            color: #495057;
            font-size: 0.95rem;
        }
        
        .agenda-day-header .day-date {
            font-size: 0.85rem;
            color: #6c757d;
            margin-left: 8px;
        }
        
        .agenda-event-card {
            background: #fff;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            margin-bottom: 8px;
            padding: 12px 16px;
            cursor: pointer;
            transition: all 0.2s ease;
            touch-action: manipulation;
            position: relative;
        }
        
        .agenda-event-card:hover,
        .agenda-event-card:active {
            background: #f8f9fa;
            border-color: #adb5bd;
            transform: translateY(-1px);
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .agenda-event-card::before {
            content: '';
            position: absolute;
            left: 0;
            top: 0;
            bottom: 0;
            width: 4px;
            background: var(--event-color, #007bff);
            border-radius: 4px 0 0 4px;
        }
        
        .agenda-event-time {
            font-size: 0.9rem;
            font-weight: 600;
            color: #495057;
            margin-bottom: 4px;
        }
        
        .agenda-event-title {
            font-size: 1rem;
            font-weight: 500;
            color: #212529;
            margin-bottom: 4px;
            line-height: 1.3;
        }
        
        .agenda-event-location {
            font-size: 0.85rem;
            color: #6c757d;
            margin-bottom: 2px;
        }
        
        .agenda-event-category {
            display: inline-block;
            font-size: 0.75rem;
            padding: 2px 6px;
            border-radius: 12px;
            background: rgba(0,123,255,0.1);
            color: #007bff;
            margin-top: 4px;
        }
        
        .agenda-no-events {
            text-align: center;
            padding: 40px 20px;
            color: #6c757d;
        }
        
        .agenda-no-events i {
            font-size: 2rem;
            margin-bottom: 16px;
            opacity: 0.5;
        }
        
        .agenda-empty-day {
            text-align: center;
            padding: 20px;
            color: #6c757d;
            font-size: 0.9rem;
            margin: 8px 0;
        }
        
        /* === MOBILE NAVIGATION STYLES === */
        
        .calendar-mobile-header {
            background: #f8f9fa;
            border-bottom: 1px solid #dee2e6;
        }
        
        .calendar-mobile-header .btn {
            font-size: 0.85rem;
            padding: 6px 12px;
        }
        
        .calendar-mobile-header h6 {
            font-size: 0.95rem;
            white-space: nowrap;
        }
        
        #mobileCurrentPeriod {
            min-width: 120px;
            text-align: center;
        }
        
        /* === MOBILE MODAL STYLES === */
        
        .modal-fullscreen-sm-down {
            max-width: 100vw;
            margin: 0;
        }
        
        @@media (max-width: 575.98px) {
            .modal-fullscreen-sm-down .modal-dialog {
                width: 100vw;
                height: 100vh;
                margin: 0;
                border-radius: 0;
            }
            
            .modal-fullscreen-sm-down .modal-content {
                height: 100vh;
                border-radius: 0;
                border: none;
            }
            
            .modal-fullscreen-sm-down .modal-body {
                flex: 1;
                overflow-y: auto;
            }
        }
        
        /* === EXISTING STYLES === */
        
        .upcoming-event-link:hover .event-item,
        .upcoming-event-link:focus .event-item {
            background-color: #f8f9fa;
            border-radius: 4px;
            padding: 8px;
            margin: -8px;
            transition: background-color 0.2s ease;
        }
        
        .event-item, .category-item {
            padding: 8px 0;
            border-bottom: 1px solid #eee;
        }
        
        .event-item:last-child, .category-item:last-child {
            border-bottom: none;
        }
        
        .event-color, .category-color {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
            flex-shrink: 0;
        }
        
        .event-title {
            font-weight: 500;
            font-size: 0.9rem;
        }
        
        .event-date {
            font-size: 0.8rem;
        }
        
        .stat-item {
            display: flex;
            flex-direction: column;
            align-items: center;
        }
        
        .stat-number {
            font-size: 1.8rem;
            font-weight: bold;
            line-height: 1;
        }
        
        .stat-label {
            font-size: 0.8rem;
            color: #6c757d;
        }
        
        .fc-event {
            border: none !important;
        }
        
        .fc-event-title {
            font-weight: 500;
        }
        
        .fc-daygrid-event {
            margin: 1px;
        }
        
        /* === MODAL TAB STYLING === */
        
        /* Enhanced modal for registration management */
        .modal-xl .modal-dialog {
            max-width: 1200px;
        }
        
        .modal-tabs {
            border-bottom: 1px solid #dee2e6;
            margin-bottom: 0;
            background-color: #f8f9fa;
        }
        
        .modal-tabs .nav-link {
            border: none;
            border-bottom: 3px solid transparent;
            background-color: transparent;
            color: #6c757d;
        }
        
        .modal-tabs .nav-link.active {
            background-color: #fff;
            color: #495057;
            border-bottom-color: #007bff;
        }
        
        /* Registration list styling */
        .registration-item {
            border-left: 4px solid transparent;
            transition: all 0.2s ease;
        }
        
        .registration-item:hover {
            background-color: #f8f9fa;
            border-left-color: #007bff;
        }
        
        .registration-item[data-status="Confirmed"] {
            border-left-color: #28a745;
        }
        
        .registration-item[data-status="Pending"] {
            border-left-color: #ffc107;
        }
        
        .registration-item[data-status="Waitlisted"] {
            border-left-color: #17a2b8;
        }
        
        .registration-item[data-status="Rejected"] {
            border-left-color: #dc3545;
        }
        
        .badge-status {
            font-size: 0.75em;
            min-width: 80px;
            text-align: center;
        }
        
        /* Filter controls */
        .registration-filters {
            background-color: #f8f9fa;
            border-radius: 0.375rem;
            padding: 1rem;
            border: 1px solid #dee2e6;
        }
        
        /* Loading states */
        .registrations-loading {
            min-height: 200px;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        /* Mobile responsiveness for registration tab */
        @@media (max-width: 768px) {
            .modal-xl .modal-dialog {
                max-width: 95%;
                margin: 1rem;
            }
            
            .registration-item .col-md-2,
            .registration-item .col-md-3 {
                margin-top: 0.5rem;
            }
            
            .btn-group .dropdown-toggle {
                width: 100%;
            }
        }
        
        /* === RESPONSIVE SIDEBAR === */
        
        @@media (max-width: 767.98px) {
            .col-md-4 {
                margin-top: 24px;
            }
            
            .card {
                margin-bottom: 16px;
            }
        }
    </style>
}