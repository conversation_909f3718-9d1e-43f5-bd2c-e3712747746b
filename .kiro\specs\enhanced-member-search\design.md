# Design Document

## Overview

This design enhances the existing member search functionality in the admin panel by expanding search capabilities beyond the current name/email limitation and adding comprehensive filtering options. The solution builds upon the existing AdminController Members action and MemberSearchViewModel, extending them to support multi-field search, advanced filtering, and improved user experience.

The design follows the existing application patterns using ASP.NET Core MVC with Entity Framework Core, maintaining consistency with the current architecture while adding new capabilities for phone number search, address search, date range filtering, registration type filtering, and status filtering.

## Architecture

### High-Level Components

```mermaid
graph TB
    A[Admin Members View] --> B[Enhanced AdminController]
    B --> C[Enhanced MemberSearchService]
    C --> D[ApplicationContext/EF Core]
    B --> E[Enhanced MemberSearchViewModel]
    E --> F[Search Result DTOs]

    G[Search UI Components] --> A
    H[Filter UI Components] --> A
    I[Export Functionality] --> B
    J[Saved Searches] --> B
```

### Data Flow

1. **User Input**: Admin enters search criteria and applies filters in the enhanced UI
2. **Request Processing**: AdminController receives search parameters and validates them
3. **Service Layer**: MemberSearchService builds dynamic LINQ queries based on criteria
4. **Data Access**: Entity Framework executes optimized queries with proper indexing
5. **Response**: Results are mapped to ViewModels and returned with pagination metadata
6. **UI Rendering**: Enhanced view displays results with highlighting and filter state

## Components and Interfaces

### 1. Enhanced AdminController

**New/Modified Actions:**

-   `Members(MemberSearchRequest request)` - Enhanced with new search parameters
-   `ExportMembers(MemberSearchRequest request)` - New action for CSV export
-   `SaveSearch(SavedSearchRequest request)` - New action for saving search criteria
-   `GetSavedSearches()` - New action to retrieve user's saved searches
-   `DeleteSavedSearch(int id)` - New action to remove saved searches

**Key Responsibilities:**

-   Validate search input parameters
-   Coordinate with MemberSearchService for data retrieval
-   Handle pagination and result formatting
-   Manage saved search functionality
-   Generate export files

### 2. MemberSearchService (New Service)

**Interface: IMemberSearchService**

```csharp
public interface IMemberSearchService
{
    Task<MemberSearchResult> SearchMembersAsync(MemberSearchRequest request);
    Task<List<RegistrationType>> GetRegistrationTypesAsync();
    Task<byte[]> ExportMembersAsync(MemberSearchRequest request);
    Task<SavedSearch> SaveSearchAsync(SavedSearchRequest request, string userId);
    Task<List<SavedSearch>> GetSavedSearchesAsync(string userId);
    Task<bool> DeleteSavedSearchAsync(int searchId, string userId);
}
```

**Key Responsibilities:**

-   Build dynamic LINQ queries based on search criteria
-   Apply multiple filters simultaneously
-   Handle phone number normalization for search
-   Implement efficient pagination
-   Generate export data
-   Manage saved search persistence

### 3. Enhanced Data Models

**MemberSearchRequest (Enhanced)**

```csharp
public class MemberSearchRequest
{
    public string? SearchTerm { get; set; }
    public List<int>? RegistrationTypeIds { get; set; }
    public bool? IsActive { get; set; }
    public DateTime? DateOfBirthFrom { get; set; }
    public DateTime? DateOfBirthTo { get; set; }
    public int? AgeFrom { get; set; }
    public int? AgeTo { get; set; }
    public string? City { get; set; }
    public string? Province { get; set; }
    public string? PostalCode { get; set; }
    public int Page { get; set; } = 1;
    public int PageSize { get; set; } = 20;
    public string? SortBy { get; set; }
    public bool SortDescending { get; set; }
}
```

**MemberSearchResult**

```csharp
public class MemberSearchResult
{
    public List<MemberSearchResultItem> Members { get; set; }
    public int TotalCount { get; set; }
    public int CurrentPage { get; set; }
    public int PageSize { get; set; }
    public int TotalPages { get; set; }
    public MemberSearchRequest SearchCriteria { get; set; }
}
```

**MemberSearchResultItem**

```csharp
public class MemberSearchResultItem
{
    public int Id { get; set; }
    public string FirstName { get; set; }
    public string LastName { get; set; }
    public string Email { get; set; }
    public string Phone { get; set; }
    public DateTime DateOfBirth { get; set; }
    public int Age { get; set; }
    public string Address { get; set; }
    public string City { get; set; }
    public string PostalCode { get; set; }
    public string RegistrationTypeName { get; set; }
    public bool IsActive { get; set; }
    public DateTime DateCreated { get; set; }
    public List<SearchMatchInfo> MatchedFields { get; set; }
}
```

**SavedSearch (New Entity)**

```csharp
public class SavedSearch : BaseEntity
{
    public string Name { get; set; }
    public string UserId { get; set; }
    public string SearchCriteriaJson { get; set; }
    public DateTime LastUsed { get; set; }
}
```

### 4. Enhanced UI Components

**Search Form Enhancements:**

-   Multi-field search input with placeholder text
-   Registration type multi-select dropdown
-   Active/Inactive status radio buttons
-   Date range pickers for birth date filtering
-   Age range numeric inputs
-   Location filters (city, province, postal code)
-   Clear all filters button
-   Save search functionality

**Results Display Enhancements:**

-   Highlighted search terms in results
-   Expandable member details
-   Sort options for different columns
-   Export button for current results
-   Improved pagination with page size options

## Data Models

### Database Schema Changes

**New Table: SavedSearches**

```sql
CREATE TABLE SavedSearches (
    Id int IDENTITY(1,1) PRIMARY KEY,
    Name nvarchar(100) NOT NULL,
    UserId nvarchar(450) NOT NULL,
    SearchCriteriaJson nvarchar(max) NOT NULL,
    LastUsed datetime2 NOT NULL,
    DateCreated datetime2 NOT NULL,
    DateModified datetime2 NULL,
    IsActive bit NOT NULL DEFAULT 1,
    FOREIGN KEY (UserId) REFERENCES AspNetUsers(Id)
);
```

**Indexes for Performance:**

```sql
-- Optimize member search queries
CREATE INDEX IX_Members_Phone ON Members(Phone);
CREATE INDEX IX_Members_City ON Members(City);
CREATE INDEX IX_Members_PostalCode ON Members(PostalCode);
CREATE INDEX IX_Members_DateOfBirth ON Members(DateOfBirth);
CREATE INDEX IX_Members_IsActive_RegistrationTypeId ON Members(IsActive, RegistrationTypeId);

-- Optimize saved searches
CREATE INDEX IX_SavedSearches_UserId ON SavedSearches(UserId);
```

### Search Query Optimization

**Dynamic Query Building:**
The service will build queries dynamically based on provided criteria:

```csharp
var query = _context.Members
    .Include(m => m.RegistrationType)
    .Include(m => m.Province)
    .AsQueryable();

// Apply text search across multiple fields
if (!string.IsNullOrEmpty(request.SearchTerm))
{
    var normalizedPhone = NormalizePhoneNumber(request.SearchTerm);
    query = query.Where(m =>
        m.FirstName.Contains(request.SearchTerm) ||
        m.LastName.Contains(request.SearchTerm) ||
        m.Email.Contains(request.SearchTerm) ||
        m.Phone.Replace(" ", "").Replace("-", "").Replace("(", "").Replace(")", "").Contains(normalizedPhone) ||
        m.Address.Contains(request.SearchTerm) ||
        m.City.Contains(request.SearchTerm) ||
        m.PostalCode.Contains(request.SearchTerm));
}

// Apply filters
if (request.RegistrationTypeIds?.Any() == true)
    query = query.Where(m => request.RegistrationTypeIds.Contains(m.RegistrationTypeId));

if (request.IsActive.HasValue)
    query = query.Where(m => m.IsActive == request.IsActive.Value);
```

## Error Handling

### Input Validation

-   **Search Term Length**: Maximum 100 characters to prevent performance issues
-   **Date Range Validation**: Ensure "from" date is not after "to" date
-   **Age Range Validation**: Ensure valid age ranges (0-120)
-   **Page Size Limits**: Maximum 100 results per page
-   **SQL Injection Prevention**: Use parameterized queries exclusively

### Performance Safeguards

-   **Query Timeout**: 30-second timeout for complex searches
-   **Result Limits**: Maximum 10,000 total results for export
-   **Rate Limiting**: Prevent excessive search requests from single user
-   **Index Monitoring**: Log slow queries for optimization

### User Experience

-   **Graceful Degradation**: Show partial results if some filters fail
-   **Clear Error Messages**: Specific feedback for validation failures
-   **Loading States**: Progress indicators for long-running searches
-   **Fallback Options**: Suggest alternative search terms for no results

## Testing Strategy

### Unit Tests

-   **MemberSearchService**: Test query building logic with various criteria combinations
-   **Phone Normalization**: Test phone number formatting and matching
-   **Date Range Logic**: Test age calculation and date filtering
-   **Export Functionality**: Test CSV generation with different data sets

### Integration Tests

-   **Database Queries**: Test actual query performance with sample data
-   **Controller Actions**: Test full request/response cycle
-   **Search Accuracy**: Verify search results match expected criteria
-   **Pagination**: Test page navigation and result consistency

### Performance Tests

-   **Large Dataset**: Test with 10,000+ member records
-   **Complex Queries**: Test multiple simultaneous filters
-   **Concurrent Users**: Test multiple admin users searching simultaneously
-   **Export Performance**: Test CSV generation time with large result sets

### User Acceptance Tests

-   **Search Scenarios**: Test common admin search workflows
-   **Filter Combinations**: Test various filter combinations
-   **Mobile Responsiveness**: Test search interface on mobile devices
-   **Accessibility**: Test keyboard navigation and screen reader compatibility

## Implementation Phases

### Phase 1: Core Search Enhancement

-   Extend search to include phone, address, and city fields
-   Add basic registration type filtering
-   Implement active/inactive status filtering
-   Update UI with new search options

### Phase 2: Advanced Filtering

-   Add date range and age filtering
-   Implement saved search functionality
-   Add search result highlighting
-   Improve pagination and sorting

### Phase 3: Export and Performance

-   Implement CSV export functionality
-   Add database indexes for performance
-   Implement query optimization
-   Add performance monitoring

### Phase 4: User Experience Polish

-   Add advanced UI components (date pickers, multi-select)
-   Implement search suggestions
-   Add keyboard shortcuts
-   Enhance mobile responsiveness
