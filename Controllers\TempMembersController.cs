using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Localization;
using ParaHockeyApp.Controllers;
using ParaHockeyApp.Models.Entities;
using ParaHockeyApp.Resources;
using ParaHockeyApp.Services;

namespace ParaHockeyApp.Controllers
{
    /// <summary>
    /// Controller for managing temporary member records during import process
    /// </summary>
    [Authorize]
    public class TempMembersController : BaseMvcController
    {
        private readonly IMemberImportService _memberImportService;
        private readonly ITempMemberService _tempMemberService;
        private readonly IDuplicateDetectionService _duplicateDetectionService;

        public TempMembersController(
            IMemberImportService memberImportService,
            ITempMemberService tempMemberService,
            IDuplicateDetectionService duplicateDetectionService,
            ILogger<TempMembersController> logger,
            IStringLocalizer<SharedResourceMarker> localizer)
            : base(logger, localizer)
        {
            _memberImportService = memberImportService;
            _tempMemberService = tempMemberService;
            _duplicateDetectionService = duplicateDetectionService;
        }

        /// <summary>
        /// Display queue of temp members that need fixes
        /// </summary>
        [HttpGet]
        public async Task<IActionResult> NeedsFix(int batchId)
        {
            try
            {
                var batch = await _memberImportService.GetBatchSummaryAsync(batchId);
                var tempMembers = await _memberImportService.GetQueueAsync(batchId, TempMemberStatus.NeedsFix);
                
                ViewBag.Batch = batch;
                return View(tempMembers);
            }
            catch (Exception ex)
            {
                TempData["ErrorMessage"] = $"{_localizer["Import_QueueLoadError"]}: {ex.Message}";
                return RedirectToAction("Batch", "Import", new { id = batchId });
            }
        }

        /// <summary>
        /// Display queue of temp members ready to create
        /// </summary>
        [HttpGet]
        public async Task<IActionResult> ReadyToCreate(int batchId)
        {
            try
            {
                var batch = await _memberImportService.GetBatchSummaryAsync(batchId);
                var tempMembers = await _memberImportService.GetQueueAsync(batchId, TempMemberStatus.ReadyToCreate);
                
                ViewBag.Batch = batch;
                return View(tempMembers);
            }
            catch (Exception ex)
            {
                TempData["ErrorMessage"] = $"{_localizer["Import_QueueLoadError"]}: {ex.Message}";
                return RedirectToAction("Batch", "Import", new { id = batchId });
            }
        }

        /// <summary>
        /// Display queue of temp members with duplicates
        /// </summary>
        [HttpGet]
        public async Task<IActionResult> Duplicates(int batchId)
        {
            try
            {
                var batch = await _memberImportService.GetBatchSummaryAsync(batchId);
                var tempMembers = await _memberImportService.GetQueueAsync(batchId, TempMemberStatus.Duplicate);
                
                ViewBag.Batch = batch;
                return View(tempMembers);
            }
            catch (Exception ex)
            {
                TempData["ErrorMessage"] = $"{_localizer["Import_QueueLoadError"]}: {ex.Message}";
                return RedirectToAction("Batch", "Import", new { id = batchId });
            }
        }

        /// <summary>
        /// Create single member from temp data
        /// </summary>
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Create(Guid tempMemberId)
        {
            try
            {
                var adminEmail = GetCurrentAdminEmail();
                var member = await _tempMemberService.CreateFromTempAsync(tempMemberId, adminEmail);
                
                TempData["SuccessMessage"] = _localizer["Import_MemberCreatedSuccessfully"];
                return Json(new { success = true, memberId = member.Id });
            }
            catch (Exception ex)
            {
                return Json(new { success = false, error = ex.Message });
            }
        }

        /// <summary>
        /// Bulk create members from selected temp records
        /// </summary>
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> BulkCreate([FromBody] List<Guid> tempMemberIds)
        {
            try
            {
                var adminEmail = GetCurrentAdminEmail();
                var members = await _tempMemberService.BulkCreateFromTempAsync(tempMemberIds, adminEmail);
                
                return Json(new { 
                    success = true, 
                    count = members.Count,
                    message = string.Format(_localizer["Import_MembersCreatedSuccessfully"], members.Count)
                });
            }
            catch (Exception ex)
            {
                return Json(new { success = false, error = ex.Message });
            }
        }

        /// <summary>
        /// Display side-by-side duplicate resolution interface
        /// </summary>
        [HttpGet]
        public async Task<IActionResult> Resolve(Guid tempMemberId)
        {
            try
            {
                var tempMember = await _tempMemberService.GetByIdAsync(tempMemberId);
                if (tempMember?.ExistingMember == null)
                {
                    TempData["ErrorMessage"] = _localizer["Import_DuplicateNotFound"];
                    return RedirectToAction("Index", "Admin");
                }

                var fieldComparisons = _duplicateDetectionService.GetFieldComparisons(tempMember, tempMember.ExistingMember);

                var model = new DuplicateResolutionViewModel
                {
                    TempMember = tempMember,
                    ExistingMember = tempMember.ExistingMember,
                    FieldComparisons = fieldComparisons,
                    SelectedChoices = new Dictionary<string, string>()
                };

                return View(model);
            }
            catch (Exception ex)
            {
                TempData["ErrorMessage"] = $"{_localizer["Import_ResolveLoadError"]}: {ex.Message}";
                return RedirectToAction("Index", "Admin");
            }
        }

        /// <summary>
        /// Preview merge changes before applying
        /// </summary>
        [HttpPost]
        public async Task<IActionResult> PreviewMerge(Guid tempMemberId, [FromBody] Dictionary<string, string> fieldChoices)
        {
            try
            {
                var preview = await _duplicateDetectionService.PreviewMergeAsync(tempMemberId, fieldChoices);
                return Json(new { success = true, preview });
            }
            catch (Exception ex)
            {
                return Json(new { success = false, error = ex.Message });
            }
        }

        /// <summary>
        /// Apply merge with selected field choices
        /// </summary>
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> ConfirmMerge(Guid tempMemberId, [FromBody] Dictionary<string, string> fieldChoices)
        {
            try
            {
                var adminEmail = GetCurrentAdminEmail();
                var member = await _duplicateDetectionService.ApplyMergeAsync(tempMemberId, fieldChoices, adminEmail);
                
                return Json(new { 
                    success = true, 
                    message = _localizer["Import_MergeCompletedSuccessfully"],
                    memberId = member.Id
                });
            }
            catch (Exception ex)
            {
                return Json(new { success = false, error = ex.Message });
            }
        }

        /// <summary>
        /// Update temp member status (AJAX endpoint)
        /// </summary>
        [HttpPost]
        public async Task<IActionResult> UpdateStatus(Guid tempMemberId, TempMemberStatus status)
        {
            try
            {
                await _tempMemberService.UpdateStatusAsync(tempMemberId, status);
                return Json(new { success = true });
            }
            catch (Exception ex)
            {
                return Json(new { success = false, error = ex.Message });
            }
        }

        /// <summary>
        /// Delete temp member record (AJAX endpoint)
        /// </summary>
        [HttpPost]
        public async Task<IActionResult> Delete(Guid tempMemberId)
        {
            try
            {
                await _tempMemberService.DeleteAsync(tempMemberId);
                return Json(new { success = true, message = _localizer["Import_TempMemberDeleted"] });
            }
            catch (Exception ex)
            {
                return Json(new { success = false, error = ex.Message });
            }
        }

        /// <summary>
        /// Helper method to get current admin email from claims
        /// </summary>
        private string GetCurrentAdminEmail()
        {
            // In development mode (NoAuth), use a default admin email
            if (User?.Identity?.Name == null)
            {
                return "<EMAIL>";
            }

            // In production, get from Azure AD claims
            return User.Identity.Name ?? User.FindFirst("preferred_username")?.Value ?? "<EMAIL>";
        }
    }

    /// <summary>
    /// View model for duplicate resolution interface
    /// </summary>
    public class DuplicateResolutionViewModel
    {
        public TempMember TempMember { get; set; } = null!;
        public Member ExistingMember { get; set; } = null!;
        public Dictionary<string, FieldComparison> FieldComparisons { get; set; } = new();
        public Dictionary<string, string> SelectedChoices { get; set; } = new();
    }
}