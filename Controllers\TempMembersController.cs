using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using ParaHockeyApp.Models.Entities;
using ParaHockeyApp.Services;
using ParaHockeyApp.ViewModels;
using ParaHockeyApp.Attributes;

namespace ParaHockeyApp.Controllers
{
    /// <summary>
    /// Controller for managing temporary member queues during import process
    /// </summary>
    [AdminAuthorize]
    public class TempMembersController : Controller
    {
        private readonly ITempMemberService _tempMemberService;
        private readonly IDuplicateDetectionService _duplicateDetectionService;
        private readonly IDuplicateResolutionCoordinator _duplicateResolutionCoordinator;
        private readonly IImportBatchService _importBatchService;
        private readonly IUserContextService _userContextService;

        public TempMembersController(
            ITempMemberService tempMemberService,
            IDuplicateDetectionService duplicateDetectionService,
            IDuplicateResolutionCoordinator duplicateResolutionCoordinator,
            IImportBatchService importBatchService,
            IUserContextService userContextService)
        {
            _tempMemberService = tempMemberService;
            _duplicateDetectionService = duplicateDetectionService;
            _duplicateResolutionCoordinator = duplicateResolutionCoordinator;
            _importBatchService = importBatchService;
            _userContextService = userContextService;
        }

        /// <summary>
        /// GET: Display queue for specific batch and status
        /// </summary>
        public async Task<IActionResult> Queue(int batchId, TempMemberStatus? status = null, int page = 1, int pageSize = 20, string? search = null)
        {
            try
            {
                // Get batch information
                var batch = await _importBatchService.GetBatchSummaryAsync(batchId);
                
                // Get temp members for the specified status
                PagedResult<TempMember> pagedResult;
                if (status.HasValue)
                {
                    pagedResult = await _tempMemberService.GetByBatchAsync(batchId, status, page, pageSize);
                }
                else
                {
                    pagedResult = await _tempMemberService.GetByBatchAsync(batchId, null, page, pageSize);
                }

                var viewModel = new TempMemberQueueViewModel
                {
                    BatchId = batchId,
                    BatchFileName = batch.FileName,
                    Status = status,
                    TempMembers = pagedResult.Items,
                    PageNumber = page,
                    PageSize = pageSize,
                    TotalCount = pagedResult.TotalCount,
                    SearchTerm = search
                };

                return View(viewModel);
            }
            catch (ArgumentException)
            {
                return NotFound($"Import batch {batchId} not found.");
            }
            catch (Exception ex)
            {
                return View("Error", new ErrorViewModel 
                { 
                    Message = $"An error occurred while loading the queue: {ex.Message}" 
                });
            }
        }

        /// <summary>
        /// GET: Display details for a specific temp member
        /// </summary>
        public async Task<IActionResult> Details(Guid id)
        {
            try
            {
                var tempMember = await _tempMemberService.GetByIdAsync(id);
                if (tempMember == null)
                {
                    return NotFound($"Temp member {id} not found.");
                }

                // If it's a duplicate, get the duplicate resolution view model
                if (tempMember.Status == TempMemberStatus.Duplicate)
                {
                    var duplicateViewModel = await _duplicateDetectionService.GetDuplicateResolutionViewModelAsync(id);
                    return View("DuplicateDetails", duplicateViewModel);
                }

                return View(tempMember);
            }
            catch (Exception ex)
            {
                return View("Error", new ErrorViewModel 
                { 
                    Message = $"An error occurred while loading temp member details: {ex.Message}" 
                });
            }
        }

        /// <summary>
        /// GET: Edit temp member data to fix validation errors
        /// </summary>
        public async Task<IActionResult> Edit(Guid id)
        {
            try
            {
                var tempMember = await _tempMemberService.GetByIdAsync(id);
                if (tempMember == null)
                {
                    return NotFound($"Temp member {id} not found.");
                }

                // Resolve lookup values for dropdowns
                var resolvedTempMember = await _tempMemberService.ResolveLookupValuesAsync(id);
                
                return View(resolvedTempMember);
            }
            catch (Exception ex)
            {
                return View("Error", new ErrorViewModel 
                { 
                    Message = $"An error occurred while loading temp member for editing: {ex.Message}" 
                });
            }
        }

        /// <summary>
        /// POST: Update temp member data
        /// </summary>
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Edit(Guid id, TempMemberUpdateData updateData)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    // Re-resolve lookup values if validation fails
                    var tempMember = await _tempMemberService.ResolveLookupValuesAsync(id);
                    return View(tempMember);
                }

                var currentUser = _userContextService.GetCurrentUser()?.UserName ?? "Unknown";
                var updatedTempMember = await _tempMemberService.UpdateTempMemberDataAsync(id, updateData, currentUser);

                TempData["SuccessMessage"] = "Temp member updated successfully.";
                return RedirectToAction("Queue", new { batchId = updatedTempMember.ImportBatchId, status = updatedTempMember.Status });
            }
            catch (Exception ex)
            {
                ModelState.AddModelError("", $"An error occurred while updating the temp member: {ex.Message}");
                var tempMember = await _tempMemberService.ResolveLookupValuesAsync(id);
                return View(tempMember);
            }
        }

        /// <summary>
        /// POST: Create member from temp member data
        /// </summary>
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> CreateMember(Guid id)
        {
            try
            {
                var currentUser = _userContextService.GetCurrentUser()?.UserName ?? "Unknown";
                var member = await _tempMemberService.CreateMemberFromTempAsync(id, currentUser);

                TempData["SuccessMessage"] = $"Member '{member.FirstName} {member.LastName}' created successfully.";
                
                // Get batch ID for redirect
                var tempMember = await _tempMemberService.GetByIdAsync(id);
                return RedirectToAction("Queue", new { batchId = tempMember?.ImportBatchId, status = TempMemberStatus.Created });
            }
            catch (Exception ex)
            {
                TempData["ErrorMessage"] = $"Failed to create member: {ex.Message}";
                var tempMember = await _tempMemberService.GetByIdAsync(id);
                return RedirectToAction("Queue", new { batchId = tempMember?.ImportBatchId });
            }
        }

        /// <summary>
        /// POST: Bulk create members from multiple temp members
        /// </summary>
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> BulkCreateMembers(int batchId, List<Guid> selectedIds)
        {
            try
            {
                if (selectedIds == null || !selectedIds.Any())
                {
                    TempData["ErrorMessage"] = "No temp members selected for creation.";
                    return RedirectToAction("Queue", new { batchId });
                }

                var currentUser = _userContextService.GetCurrentUser()?.UserName ?? "Unknown";
                var createdMembers = await _tempMemberService.BulkCreateMembersFromTempAsync(selectedIds, currentUser);

                TempData["SuccessMessage"] = $"Successfully created {createdMembers.Count} members.";
                return RedirectToAction("Queue", new { batchId, status = TempMemberStatus.Created });
            }
            catch (Exception ex)
            {
                TempData["ErrorMessage"] = $"Failed to create members: {ex.Message}";
                return RedirectToAction("Queue", new { batchId });
            }
        }

        /// <summary>
        /// POST: Reject temp member
        /// </summary>
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Reject(Guid id)
        {
            try
            {
                var currentUser = _userContextService.GetCurrentUser()?.UserName ?? "Unknown";
                
                // Get temp member info before rejection
                var tempMember = await _tempMemberService.GetByIdAsync(id);
                if (tempMember == null)
                {
                    TempData["ErrorMessage"] = "Temp member not found.";
                    return RedirectToAction("Index", "Import");
                }

                // Handle rejection based on status
                if (tempMember.Status == TempMemberStatus.Duplicate)
                {
                    await _duplicateResolutionCoordinator.RejectDuplicateAsync(id, currentUser);
                }
                else
                {
                    await _tempMemberService.UpdateStatusAsync(id, TempMemberStatus.Rejected, currentUser);
                }

                TempData["SuccessMessage"] = "Temp member rejected successfully.";
                return RedirectToAction("Queue", new { batchId = tempMember.ImportBatchId, status = TempMemberStatus.Rejected });
            }
            catch (Exception ex)
            {
                TempData["ErrorMessage"] = $"Failed to reject temp member: {ex.Message}";
                var tempMember = await _tempMemberService.GetByIdAsync(id);
                return RedirectToAction("Queue", new { batchId = tempMember?.ImportBatchId });
            }
        }

        /// <summary>
        /// POST: Bulk reject multiple temp members
        /// </summary>
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> BulkReject(int batchId, List<Guid> selectedIds, TempMemberStatus? currentStatus = null)
        {
            try
            {
                if (selectedIds == null || !selectedIds.Any())
                {
                    TempData["ErrorMessage"] = "No temp members selected for rejection.";
                    return RedirectToAction("Queue", new { batchId });
                }

                var currentUser = _userContextService.GetCurrentUser()?.UserName ?? "Unknown";
                
                // Handle bulk rejection based on status
                int rejectedCount;
                if (currentStatus == TempMemberStatus.Duplicate)
                {
                    rejectedCount = await _duplicateResolutionCoordinator.BulkRejectDuplicatesAsync(selectedIds, currentUser);
                }
                else
                {
                    rejectedCount = selectedIds.Count;
                    foreach (var tempMemberId in selectedIds)
                    {
                        await _tempMemberService.UpdateStatusAsync(tempMemberId, TempMemberStatus.Rejected, currentUser);
                    }
                }

                TempData["SuccessMessage"] = $"Successfully rejected {rejectedCount} temp members.";
                return RedirectToAction("Queue", new { batchId, status = TempMemberStatus.Rejected });
            }
            catch (Exception ex)
            {
                TempData["ErrorMessage"] = $"Failed to reject temp members: {ex.Message}";
                return RedirectToAction("Queue", new { batchId });
            }
        }

        /// <summary>
        /// GET: Resolve duplicate by comparing with existing member
        /// </summary>
        public async Task<IActionResult> ResolveDuplicate(Guid id)
        {
            try
            {
                var duplicateViewModel = await _duplicateDetectionService.GetDuplicateResolutionViewModelAsync(id);
                return View(duplicateViewModel);
            }
            catch (Exception ex)
            {
                return View("Error", new ErrorViewModel 
                { 
                    Message = $"An error occurred while loading duplicate resolution: {ex.Message}" 
                });
            }
        }

        /// <summary>
        /// POST: Preview merge resolution changes before applying
        /// </summary>
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> ConfirmMerge(Guid id, Dictionary<string, string> fieldChoices)
        {
            try
            {
                // Get the duplicate resolution data with field comparisons
                var duplicateViewModel = await _duplicateDetectionService.GetDuplicateResolutionViewModelAsync(id);
                
                // Create merge preview data
                var mergePreview = new List<object>();
                
                foreach (var kvp in fieldChoices)
                {
                    var fieldName = kvp.Key;
                    var choice = kvp.Value; // "existing" or "new"
                    
                    if (duplicateViewModel.FieldComparisons.TryGetValue(fieldName, out var comparison))
                    {
                        var oldValue = comparison.ExistingValue ?? "Not set";
                        var newValue = choice == "new" ? (comparison.TempValue ?? "Not set") : oldValue;
                        var willChange = choice == "new" && comparison.ExistingValue != comparison.TempValue;
                        
                        mergePreview.Add(new
                        {
                            FieldName = fieldName,
                            DisplayName = comparison.DisplayName,
                            OldValue = oldValue,
                            NewValue = newValue,
                            WillChange = willChange
                        });
                    }
                }
                
                return Json(new { success = true, mergePreview });
            }
            catch (Exception ex)
            {
                return Json(new { success = false, error = ex.Message });
            }
        }

        /// <summary>
        /// POST: Apply merge resolution for duplicate
        /// </summary>
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> ResolveDuplicate(Guid id, Dictionary<string, string> fieldChoices)
        {
            try
            {
                var currentUser = _userContextService.GetCurrentUser()?.UserName ?? "Unknown";
                var mergedMember = await _duplicateResolutionCoordinator.ResolveDuplicateAsync(id, fieldChoices, currentUser);

                TempData["SuccessMessage"] = $"Duplicate resolved successfully. Member '{mergedMember.FirstName} {mergedMember.LastName}' has been updated.";
                
                // Get batch ID for redirect
                var tempMember = await _tempMemberService.GetByIdAsync(id);
                return RedirectToAction("Queue", new { batchId = tempMember?.ImportBatchId, status = TempMemberStatus.Merged });
            }
            catch (Exception ex)
            {
                TempData["ErrorMessage"] = $"Failed to resolve duplicate: {ex.Message}";
                var tempMember = await _tempMemberService.GetByIdAsync(id);
                return RedirectToAction("Queue", new { batchId = tempMember?.ImportBatchId });
            }
        }

        /// <summary>
        /// POST: Auto-merge identical duplicates in batch
        /// </summary>
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> AutoMergeIdentical(int batchId)
        {
            try
            {
                var currentUser = _userContextService.GetCurrentUser()?.UserName ?? "Unknown";
                var mergedCount = await _duplicateResolutionCoordinator.AutoMergeIdenticalDuplicatesAsync(batchId, currentUser);

                TempData["SuccessMessage"] = $"Successfully auto-merged {mergedCount} identical duplicate records.";
                return RedirectToAction("Queue", new { batchId, status = TempMemberStatus.Merged });
            }
            catch (Exception ex)
            {
                TempData["ErrorMessage"] = $"Failed to auto-merge duplicates: {ex.Message}";
                return RedirectToAction("Queue", new { batchId });
            }
        }
    }

    /// <summary>
    /// View model for temp member queue display
    /// </summary>
    public class TempMemberQueueViewModel
    {
        public int BatchId { get; set; }
        public string BatchFileName { get; set; } = string.Empty;
        public TempMemberStatus? Status { get; set; }
        public List<TempMember> TempMembers { get; set; } = new();
        public int PageNumber { get; set; } = 1;
        public int PageSize { get; set; } = 20;
        public int TotalCount { get; set; }
        public int TotalPages => (int)Math.Ceiling((double)TotalCount / PageSize);
        public bool HasPreviousPage => PageNumber > 1;
        public bool HasNextPage => PageNumber < TotalPages;
        public string? SearchTerm { get; set; }

        // Queue-specific properties
        public string QueueDisplayName => Status?.ToString() ?? "All Statuses";
        public string QueueDescription => Status switch
        {
            TempMemberStatus.Imported => "Members imported from Excel file, awaiting processing",
            TempMemberStatus.ReadyToCreate => "Validated members ready to be created as new members",
            TempMemberStatus.NeedsFix => "Members with validation errors that need manual correction",
            TempMemberStatus.Duplicate => "Potential duplicate members requiring resolution",
            TempMemberStatus.Created => "Members successfully created from import data",
            TempMemberStatus.Merged => "Members merged with existing member records",
            TempMemberStatus.Rejected => "Members rejected during processing",
            null => "All temp members regardless of status",
            _ => $"Members with status: {Status}"
        };

        public string QueueIconClass => Status switch
        {
            TempMemberStatus.Imported => "fas fa-upload",
            TempMemberStatus.ReadyToCreate => "fas fa-plus-circle",
            TempMemberStatus.NeedsFix => "fas fa-exclamation-triangle",
            TempMemberStatus.Duplicate => "fas fa-copy",
            TempMemberStatus.Created => "fas fa-check-circle",
            TempMemberStatus.Merged => "fas fa-compress-arrows-alt",
            TempMemberStatus.Rejected => "fas fa-times-circle",
            null => "fas fa-list",
            _ => "fas fa-question-circle"
        };

        public string QueueColorClass => Status switch
        {
            TempMemberStatus.Imported => "text-info",
            TempMemberStatus.ReadyToCreate => "text-success",
            TempMemberStatus.NeedsFix => "text-warning",
            TempMemberStatus.Duplicate => "text-primary",
            TempMemberStatus.Created => "text-success",
            TempMemberStatus.Merged => "text-info",
            TempMemberStatus.Rejected => "text-danger",
            null => "text-secondary",
            _ => "text-secondary"
        };

        public bool AllowsBulkCreate => Status == TempMemberStatus.ReadyToCreate;
        public bool AllowsBulkReject => Status != TempMemberStatus.Created && Status != TempMemberStatus.Merged && Status != TempMemberStatus.Rejected;
        public bool ShowAutoMerge => Status == TempMemberStatus.Duplicate;
    }
}