@{
    ViewData["Title"] = @Localizer["Import_MembersTitle"];
}

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1 class="h3 mb-0">@Localizer["Import_MembersTitle"]</h1>
                <a href="@Url.Action("History", "Import")" class="btn btn-outline-secondary">
                    <i class="fas fa-history"></i> @Localizer["Import_ViewHistory"]
                </a>
            </div>

            @if (TempData["SuccessMessage"] != null)
            {
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    <i class="fas fa-check-circle"></i> @TempData["SuccessMessage"]
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            }

            @if (TempData["ErrorMessage"] != null)
            {
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    <i class="fas fa-exclamation-circle"></i> @TempData["ErrorMessage"]
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            }

            <div class="row">
                <div class="col-md-8">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-upload"></i> @Localizer["Import_UploadFile"]
                            </h5>
                        </div>
                        <div class="card-body">
                            <form asp-action="Members" method="post" enctype="multipart/form-data" id="importForm">
                                @Html.AntiForgeryToken()
                                
                                <div class="mb-3">
                                    <label for="file" class="form-label">@Localizer["Import_SelectFile"]</label>
                                    <input type="file" 
                                           class="form-control" 
                                           id="file" 
                                           name="file" 
                                           accept=".xlsx,.xls,.csv"
                                           required>
                                    <div class="form-text">
                                        @Localizer["Import_SupportedFormats"]: Excel (.xlsx, .xls) @Localizer["Import_MaxFileSize"]: 10MB
                                    </div>
                                    <div id="fileError" class="text-danger" style="display: none;"></div>
                                </div>

                                <div class="d-grid">
                                    <button type="submit" class="btn btn-primary" id="uploadBtn">
                                        <i class="fas fa-upload"></i> @Localizer["Import_ProcessFile"]
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-4">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-info-circle"></i> @Localizer["Import_Instructions"]
                            </h5>
                        </div>
                        <div class="card-body">
                            <h6>@Localizer["Import_FileFormat"]</h6>
                            <ul class="small">
                                <li>@Localizer["Import_ExcelOrCSV"]</li>
                                <li>@Localizer["Import_HeaderRow"]</li>
                                <li>@Localizer["Import_RequiredFields"]</li>
                            </ul>

                            <h6 class="mt-3">@Localizer["Import_Process"]</h6>
                            <ol class="small">
                                <li>@Localizer["Import_Step1"]</li>
                                <li>@Localizer["Import_Step2"]</li>
                                <li>@Localizer["Import_Step3"]</li>
                                <li>@Localizer["Import_Step4"]</li>
                            </ol>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script>
        document.getElementById('importForm').addEventListener('submit', function(e) {
            const uploadBtn = document.getElementById('uploadBtn');
            const fileInput = document.getElementById('file');
            
            if (!fileInput.files.length) {
                e.preventDefault();
                alert('@Localizer["Import_PleaseSelectFile"]');
                return;
            }
            
            // Show loading state
            uploadBtn.disabled = true;
            uploadBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> @Localizer["Import_Processing"]...';
            
            // Re-enable button after timeout (in case of error)
            setTimeout(function() {
                uploadBtn.disabled = false;
                uploadBtn.innerHTML = '<i class="fas fa-upload"></i> @Localizer["Import_ProcessFile"]';
            }, 30000);
        });
    </script>
}