@model ParaHockeyApp.DTOs.MultiFileImportRequest
@{
    ViewData["Title"] = "Multi-File Import";
}

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2>@ViewData["Title"]</h2>
                <a href="@Url.Action("History")" class="btn btn-outline-secondary">
                    <i class="fas fa-history"></i> Import History
                </a>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-lg-8">
            <form asp-action="MultiFileUpload" method="post" enctype="multipart/form-data" id="multiFileUploadForm">
                @Html.AntiForgeryToken()
                
                <!-- Member File Section -->
                <div class="card mb-4">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0">
                            <i class="fas fa-users"></i> Member Data File (Required)
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="form-group">
                            <label asp-for="MemberFile" class="form-label">
                                Excel file containing member information
                            </label>
                            <input asp-for="MemberFile" class="form-control" type="file" accept=".xlsx,.xls" required>
                            <span asp-validation-for="MemberFile" class="text-danger"></span>
                            <div class="form-text">
                                <i class="fas fa-info-circle"></i>
                                Accepted formats: Excel (.xlsx, .xls). Must contain columns: Numéro HCR, Prénom, Nom, etc.
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Parent File Section -->
                <div class="card mb-4">
                    <div class="card-header bg-success text-white">
                        <h5 class="mb-0">
                            <i class="fas fa-user-friends"></i> Parent Contact File (Optional)
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="form-group">
                            <label asp-for="ParentFile" class="form-label">
                                CSV file containing parent information for junior members
                            </label>
                            <input asp-for="ParentFile" class="form-control" type="file" accept=".csv,.xlsx">
                            <span asp-validation-for="ParentFile" class="text-danger"></span>
                            <div class="form-text">
                                <i class="fas fa-info-circle"></i>
                                Accepted formats: CSV (.csv) or Excel (.xlsx). Will be matched by Numéro HCR.
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Emergency Contact File Section -->
                <div class="card mb-4">
                    <div class="card-header bg-warning text-white">
                        <h5 class="mb-0">
                            <i class="fas fa-phone"></i> Emergency Contact File (Optional)
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="form-group">
                            <label asp-for="EmergencyContactFile" class="form-label">
                                CSV file containing emergency contact information
                            </label>
                            <input asp-for="EmergencyContactFile" class="form-control" type="file" accept=".csv,.xlsx">
                            <span asp-validation-for="EmergencyContactFile" class="text-danger"></span>
                            <div class="form-text">
                                <i class="fas fa-info-circle"></i>
                                Accepted formats: CSV (.csv) or Excel (.xlsx). Will be matched by Numéro HCR.
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Description -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-comment"></i> Import Description
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="form-group">
                            <label asp-for="Description" class="form-label">Description (Optional)</label>
                            <textarea asp-for="Description" class="form-control" rows="3" 
                                      placeholder="Enter a description for this import batch..."></textarea>
                            <span asp-validation-for="Description" class="text-danger"></span>
                        </div>
                    </div>
                </div>

                <!-- Submit Button -->
                <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                    <button type="button" class="btn btn-outline-secondary me-md-2" onclick="validateFiles()">
                        <i class="fas fa-check-circle"></i> Validate Files
                    </button>
                    <button type="submit" class="btn btn-primary" id="uploadBtn">
                        <i class="fas fa-upload"></i> Process Import
                    </button>
                </div>
            </form>
        </div>

        <div class="col-lg-4">
            <!-- Help Card -->
            <div class="card">
                <div class="card-header bg-info text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-question-circle"></i> Multi-File Import Help
                    </h5>
                </div>
                <div class="card-body">
                    <h6>File Relationships</h6>
                    <p class="small">
                        All files are linked by the <strong>Numéro HCR</strong> field. This ensures that parent 
                        and emergency contact information is correctly associated with each member.
                    </p>
                    
                    <h6>File Requirements</h6>
                    <ul class="small">
                        <li><strong>Member File:</strong> Required. Must be Excel format.</li>
                        <li><strong>Parent File:</strong> Optional. CSV or Excel format.</li>
                        <li><strong>Emergency Contact File:</strong> Optional. CSV or Excel format.</li>
                    </ul>
                    
                    <h6>Processing Steps</h6>
                    <ol class="small">
                        <li>Member data is imported first</li>
                        <li>Relationship files are processed and matched</li>
                        <li>Unmatched records are flagged for review</li>
                        <li>All data is staged for validation</li>
                    </ol>
                    
                    <div class="alert alert-warning alert-sm">
                        <i class="fas fa-exclamation-triangle"></i>
                        <strong>Note:</strong> At least one relationship file (parent or emergency contact) 
                        must be provided for multi-file import.
                    </div>
                </div>
            </div>

            <!-- Progress Card (Hidden initially) -->
            <div class="card mt-3" id="progressCard" style="display: none;">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-spinner fa-spin"></i> Processing Files
                    </h5>
                </div>
                <div class="card-body">
                    <div class="progress mb-3">
                        <div class="progress-bar progress-bar-striped progress-bar-animated" 
                             role="progressbar" style="width: 0%" id="progressBar">
                        </div>
                    </div>
                    <div id="progressText">Initializing import...</div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Validation Results Modal -->
<div class="modal fade" id="validationResultsModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">File Validation Results</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="validationResults">
                <!-- Validation results will be loaded here -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                <button type="button" class="btn btn-primary" id="proceedWithImport" style="display: none;">
                    Proceed with Import
                </button>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script>
        function validateFiles() {
            const formData = new FormData(document.getElementById('multiFileUploadForm'));
            
            // Show loading state
            const btn = event.target;
            const originalText = btn.innerHTML;
            btn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Validating...';
            btn.disabled = true;
            
            fetch('@Url.Action("ValidateMultiFileImport")', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                displayValidationResults(data);
                btn.innerHTML = originalText;
                btn.disabled = false;
            })
            .catch(error => {
                console.error('Validation error:', error);
                alert('An error occurred during validation. Please try again.');
                btn.innerHTML = originalText;
                btn.disabled = false;
            });
        }
        
        function displayValidationResults(results) {
            let html = '';
            
            if (results.isValid) {
                html = '<div class="alert alert-success"><i class="fas fa-check-circle"></i> All files are valid and ready for import!</div>';
                document.getElementById('proceedWithImport').style.display = 'inline-block';
            } else {
                html = '<div class="alert alert-danger"><i class="fas fa-exclamation-triangle"></i> Validation errors found:</div>';
                html += '<ul class="list-group list-group-flush">';
                
                results.allErrors.forEach(error => {
                    html += `<li class="list-group-item text-danger"><i class="fas fa-times"></i> ${error}</li>`;
                });
                
                html += '</ul>';
                document.getElementById('proceedWithImport').style.display = 'none';
            }
            
            document.getElementById('validationResults').innerHTML = html;
            new bootstrap.Modal(document.getElementById('validationResultsModal')).show();
        }
        
        document.getElementById('proceedWithImport').addEventListener('click', function() {
            bootstrap.Modal.getInstance(document.getElementById('validationResultsModal')).hide();
            document.getElementById('multiFileUploadForm').submit();
        });
        
        // Show progress when form is submitted
        document.getElementById('multiFileUploadForm').addEventListener('submit', function() {
            document.getElementById('progressCard').style.display = 'block';
            document.getElementById('uploadBtn').disabled = true;
            
            // Simulate progress (in real implementation, you'd get actual progress from server)
            let progress = 0;
            const interval = setInterval(() => {
                progress += Math.random() * 20;
                if (progress > 90) progress = 90;
                
                document.getElementById('progressBar').style.width = progress + '%';
                
                if (progress < 30) {
                    document.getElementById('progressText').textContent = 'Processing member file...';
                } else if (progress < 60) {
                    document.getElementById('progressText').textContent = 'Processing relationship files...';
                } else {
                    document.getElementById('progressText').textContent = 'Matching relationships...';
                }
            }, 500);
            
            // Clear interval when form submission completes (page changes)
            setTimeout(() => clearInterval(interval), 10000);
        });
    </script>
}