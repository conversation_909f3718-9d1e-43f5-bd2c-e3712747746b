﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace ParaHockeyApp.Migrations
{
    /// <inheritdoc />
    public partial class AddPageAuditSystem : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "PageInventories",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    Version = table.Column<int>(type: "int", nullable: false),
                    GeneratedBy = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: false),
                    TotalPages = table.Column<int>(type: "int", nullable: false),
                    HighPriorityPages = table.Column<int>(type: "int", nullable: false),
                    MediumPriorityPages = table.Column<int>(type: "int", nullable: false),
                    LowPriorityPages = table.Column<int>(type: "int", nullable: false),
                    DateCreated = table.Column<DateTime>(type: "datetime2", nullable: false),
                    IsActive = table.Column<bool>(type: "bit", nullable: false),
                    DateModified = table.Column<DateTime>(type: "datetime2", nullable: true),
                    CreatedByMemberId = table.Column<int>(type: "int", nullable: true),
                    CreatedByAdminId = table.Column<int>(type: "int", nullable: true),
                    ModifiedByMemberId = table.Column<int>(type: "int", nullable: true),
                    ModifiedByAdminId = table.Column<int>(type: "int", nullable: true),
                    CreatedBySource = table.Column<int>(type: "int", nullable: false),
                    ModifiedBySource = table.Column<int>(type: "int", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_PageInventories", x => x.Id);
                    table.ForeignKey(
                        name: "FK_PageInventories_AdminUsers_CreatedByAdminId",
                        column: x => x.CreatedByAdminId,
                        principalTable: "AdminUsers",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "FK_PageInventories_AdminUsers_ModifiedByAdminId",
                        column: x => x.ModifiedByAdminId,
                        principalTable: "AdminUsers",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "FK_PageInventories_Members_CreatedByMemberId",
                        column: x => x.CreatedByMemberId,
                        principalTable: "Members",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "FK_PageInventories_Members_ModifiedByMemberId",
                        column: x => x.ModifiedByMemberId,
                        principalTable: "Members",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateTable(
                name: "PageInfos",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    Name = table.Column<string>(type: "nvarchar(200)", maxLength: 200, nullable: false),
                    Controller = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: false),
                    Action = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: false),
                    Routes = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: false),
                    ViewFiles = table.Column<string>(type: "nvarchar(1000)", maxLength: 1000, nullable: false),
                    JavaScriptFiles = table.Column<string>(type: "nvarchar(1000)", maxLength: 1000, nullable: false),
                    StylesheetFiles = table.Column<string>(type: "nvarchar(1000)", maxLength: 1000, nullable: false),
                    Complexity = table.Column<int>(type: "int", nullable: false),
                    Dependencies = table.Column<string>(type: "nvarchar(1000)", maxLength: 1000, nullable: false),
                    Priority = table.Column<int>(type: "int", nullable: false),
                    IsModernized = table.Column<bool>(type: "bit", nullable: false),
                    ModernizedDate = table.Column<DateTime>(type: "datetime2", nullable: true),
                    PageInventoryId = table.Column<int>(type: "int", nullable: false),
                    DateCreated = table.Column<DateTime>(type: "datetime2", nullable: false),
                    IsActive = table.Column<bool>(type: "bit", nullable: false),
                    DateModified = table.Column<DateTime>(type: "datetime2", nullable: true),
                    CreatedByMemberId = table.Column<int>(type: "int", nullable: true),
                    CreatedByAdminId = table.Column<int>(type: "int", nullable: true),
                    ModifiedByMemberId = table.Column<int>(type: "int", nullable: true),
                    ModifiedByAdminId = table.Column<int>(type: "int", nullable: true),
                    CreatedBySource = table.Column<int>(type: "int", nullable: false),
                    ModifiedBySource = table.Column<int>(type: "int", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_PageInfos", x => x.Id);
                    table.ForeignKey(
                        name: "FK_PageInfos_AdminUsers_CreatedByAdminId",
                        column: x => x.CreatedByAdminId,
                        principalTable: "AdminUsers",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "FK_PageInfos_AdminUsers_ModifiedByAdminId",
                        column: x => x.ModifiedByAdminId,
                        principalTable: "AdminUsers",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "FK_PageInfos_Members_CreatedByMemberId",
                        column: x => x.CreatedByMemberId,
                        principalTable: "Members",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "FK_PageInfos_Members_ModifiedByMemberId",
                        column: x => x.ModifiedByMemberId,
                        principalTable: "Members",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "FK_PageInfos_PageInventories_PageInventoryId",
                        column: x => x.PageInventoryId,
                        principalTable: "PageInventories",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "PageAuditResults",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    PageInfoId = table.Column<int>(type: "int", nullable: false),
                    AuditVersion = table.Column<int>(type: "int", nullable: false),
                    AuditedBy = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: false),
                    Status = table.Column<int>(type: "int", nullable: false),
                    OverallScore = table.Column<int>(type: "int", nullable: false),
                    SecurityScore = table.Column<int>(type: "int", nullable: false),
                    AccessibilityScore = table.Column<int>(type: "int", nullable: false),
                    PerformanceScore = table.Column<int>(type: "int", nullable: false),
                    LocalizationScore = table.Column<int>(type: "int", nullable: false),
                    CriticalIssues = table.Column<int>(type: "int", nullable: false),
                    HighIssues = table.Column<int>(type: "int", nullable: false),
                    MediumIssues = table.Column<int>(type: "int", nullable: false),
                    LowIssues = table.Column<int>(type: "int", nullable: false),
                    Notes = table.Column<string>(type: "nvarchar(4000)", maxLength: 4000, nullable: false),
                    DateCreated = table.Column<DateTime>(type: "datetime2", nullable: false),
                    IsActive = table.Column<bool>(type: "bit", nullable: false),
                    DateModified = table.Column<DateTime>(type: "datetime2", nullable: true),
                    CreatedByMemberId = table.Column<int>(type: "int", nullable: true),
                    CreatedByAdminId = table.Column<int>(type: "int", nullable: true),
                    ModifiedByMemberId = table.Column<int>(type: "int", nullable: true),
                    ModifiedByAdminId = table.Column<int>(type: "int", nullable: true),
                    CreatedBySource = table.Column<int>(type: "int", nullable: false),
                    ModifiedBySource = table.Column<int>(type: "int", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_PageAuditResults", x => x.Id);
                    table.ForeignKey(
                        name: "FK_PageAuditResults_AdminUsers_CreatedByAdminId",
                        column: x => x.CreatedByAdminId,
                        principalTable: "AdminUsers",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "FK_PageAuditResults_AdminUsers_ModifiedByAdminId",
                        column: x => x.ModifiedByAdminId,
                        principalTable: "AdminUsers",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "FK_PageAuditResults_Members_CreatedByMemberId",
                        column: x => x.CreatedByMemberId,
                        principalTable: "Members",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "FK_PageAuditResults_Members_ModifiedByMemberId",
                        column: x => x.ModifiedByMemberId,
                        principalTable: "Members",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "FK_PageAuditResults_PageInfos_PageInfoId",
                        column: x => x.PageInfoId,
                        principalTable: "PageInfos",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "AuditFindings",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    PageAuditResultId = table.Column<int>(type: "int", nullable: false),
                    Category = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    Issue = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: false),
                    Severity = table.Column<int>(type: "int", nullable: false),
                    Rationale = table.Column<string>(type: "nvarchar(1000)", maxLength: 1000, nullable: false),
                    FixPlan = table.Column<string>(type: "nvarchar(1000)", maxLength: 1000, nullable: false),
                    CodeLocation = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: false),
                    IsResolved = table.Column<bool>(type: "bit", nullable: false),
                    ResolvedDate = table.Column<DateTime>(type: "datetime2", nullable: true),
                    ResolutionNotes = table.Column<string>(type: "nvarchar(1000)", maxLength: 1000, nullable: false),
                    DateCreated = table.Column<DateTime>(type: "datetime2", nullable: false),
                    IsActive = table.Column<bool>(type: "bit", nullable: false),
                    DateModified = table.Column<DateTime>(type: "datetime2", nullable: true),
                    CreatedByMemberId = table.Column<int>(type: "int", nullable: true),
                    CreatedByAdminId = table.Column<int>(type: "int", nullable: true),
                    ModifiedByMemberId = table.Column<int>(type: "int", nullable: true),
                    ModifiedByAdminId = table.Column<int>(type: "int", nullable: true),
                    CreatedBySource = table.Column<int>(type: "int", nullable: false),
                    ModifiedBySource = table.Column<int>(type: "int", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_AuditFindings", x => x.Id);
                    table.ForeignKey(
                        name: "FK_AuditFindings_AdminUsers_CreatedByAdminId",
                        column: x => x.CreatedByAdminId,
                        principalTable: "AdminUsers",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "FK_AuditFindings_AdminUsers_ModifiedByAdminId",
                        column: x => x.ModifiedByAdminId,
                        principalTable: "AdminUsers",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "FK_AuditFindings_Members_CreatedByMemberId",
                        column: x => x.CreatedByMemberId,
                        principalTable: "Members",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "FK_AuditFindings_Members_ModifiedByMemberId",
                        column: x => x.ModifiedByMemberId,
                        principalTable: "Members",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "FK_AuditFindings_PageAuditResults_PageAuditResultId",
                        column: x => x.PageAuditResultId,
                        principalTable: "PageAuditResults",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateIndex(
                name: "IX_AuditFindings_Category",
                table: "AuditFindings",
                column: "Category");

            migrationBuilder.CreateIndex(
                name: "IX_AuditFindings_CreatedByAdminId",
                table: "AuditFindings",
                column: "CreatedByAdminId");

            migrationBuilder.CreateIndex(
                name: "IX_AuditFindings_CreatedByMemberId",
                table: "AuditFindings",
                column: "CreatedByMemberId");

            migrationBuilder.CreateIndex(
                name: "IX_AuditFindings_IsResolved",
                table: "AuditFindings",
                column: "IsResolved");

            migrationBuilder.CreateIndex(
                name: "IX_AuditFindings_ModifiedByAdminId",
                table: "AuditFindings",
                column: "ModifiedByAdminId");

            migrationBuilder.CreateIndex(
                name: "IX_AuditFindings_ModifiedByMemberId",
                table: "AuditFindings",
                column: "ModifiedByMemberId");

            migrationBuilder.CreateIndex(
                name: "IX_AuditFindings_PageAuditResultId",
                table: "AuditFindings",
                column: "PageAuditResultId");

            migrationBuilder.CreateIndex(
                name: "IX_AuditFindings_Severity",
                table: "AuditFindings",
                column: "Severity");

            migrationBuilder.CreateIndex(
                name: "IX_PageAuditResults_CreatedByAdminId",
                table: "PageAuditResults",
                column: "CreatedByAdminId");

            migrationBuilder.CreateIndex(
                name: "IX_PageAuditResults_CreatedByMemberId",
                table: "PageAuditResults",
                column: "CreatedByMemberId");

            migrationBuilder.CreateIndex(
                name: "IX_PageAuditResults_ModifiedByAdminId",
                table: "PageAuditResults",
                column: "ModifiedByAdminId");

            migrationBuilder.CreateIndex(
                name: "IX_PageAuditResults_ModifiedByMemberId",
                table: "PageAuditResults",
                column: "ModifiedByMemberId");

            migrationBuilder.CreateIndex(
                name: "IX_PageAuditResults_PageInfo_Version",
                table: "PageAuditResults",
                columns: new[] { "PageInfoId", "AuditVersion" },
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_PageAuditResults_Status",
                table: "PageAuditResults",
                column: "Status");

            migrationBuilder.CreateIndex(
                name: "IX_PageInfos_Controller_Action",
                table: "PageInfos",
                columns: new[] { "Controller", "Action" });

            migrationBuilder.CreateIndex(
                name: "IX_PageInfos_CreatedByAdminId",
                table: "PageInfos",
                column: "CreatedByAdminId");

            migrationBuilder.CreateIndex(
                name: "IX_PageInfos_CreatedByMemberId",
                table: "PageInfos",
                column: "CreatedByMemberId");

            migrationBuilder.CreateIndex(
                name: "IX_PageInfos_IsModernized",
                table: "PageInfos",
                column: "IsModernized");

            migrationBuilder.CreateIndex(
                name: "IX_PageInfos_ModifiedByAdminId",
                table: "PageInfos",
                column: "ModifiedByAdminId");

            migrationBuilder.CreateIndex(
                name: "IX_PageInfos_ModifiedByMemberId",
                table: "PageInfos",
                column: "ModifiedByMemberId");

            migrationBuilder.CreateIndex(
                name: "IX_PageInfos_Name",
                table: "PageInfos",
                column: "Name",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_PageInfos_PageInventoryId",
                table: "PageInfos",
                column: "PageInventoryId");

            migrationBuilder.CreateIndex(
                name: "IX_PageInfos_Priority",
                table: "PageInfos",
                column: "Priority");

            migrationBuilder.CreateIndex(
                name: "IX_PageInventories_CreatedByAdminId",
                table: "PageInventories",
                column: "CreatedByAdminId");

            migrationBuilder.CreateIndex(
                name: "IX_PageInventories_CreatedByMemberId",
                table: "PageInventories",
                column: "CreatedByMemberId");

            migrationBuilder.CreateIndex(
                name: "IX_PageInventories_ModifiedByAdminId",
                table: "PageInventories",
                column: "ModifiedByAdminId");

            migrationBuilder.CreateIndex(
                name: "IX_PageInventories_ModifiedByMemberId",
                table: "PageInventories",
                column: "ModifiedByMemberId");

            migrationBuilder.CreateIndex(
                name: "IX_PageInventories_Version",
                table: "PageInventories",
                column: "Version");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "AuditFindings");

            migrationBuilder.DropTable(
                name: "PageAuditResults");

            migrationBuilder.DropTable(
                name: "PageInfos");

            migrationBuilder.DropTable(
                name: "PageInventories");
        }
    }
}
