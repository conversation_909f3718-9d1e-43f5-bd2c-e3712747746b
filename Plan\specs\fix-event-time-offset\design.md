# Design – Admin Event Time Offset Fix

## Overview
Saving events currently subtracts the browser’s UTC offset because we convert local `datetime-local` input values to ISO-8601 **UTC** (`toISOString()`) but fail to convert the stored UTC back to local when repopulating the edit modal. The result is a visible +4 h (or other offset) shift.

The fix preserves the existing “store UTC in DB” model, adding a **UTC→Local conversion on load** plus **client-side change detection** to avoid redundant saves.

## Current Components
1. **View** `Views/Admin/Calendar.cshtml`
   * Contains the event modal and the scripts `openEventModal()` and `saveEvent()`.
2. **Controller** `AdminController.UpdateEvent(int id, [FromBody] Event eventData)` – already expects UTC `DateTime`.
3. **Service Layer** `IEventService.UpdateEventAsync()` – unchanged.

## Proposed Changes
### 1. Utility Conversion Function
Add JS helper:
```js
function utcToLocalForDatetimeInput(utcString) {
    return new Date(utcString).toISOString().slice(0, 16); // YYYY-MM-DDTHH:mm in local TZ
}
```

### 2. Populate Modal
In `openEventModal()` replace
```js
document.getElementById('eventStartDate').value = data.startDate || '';
```
with
```js
document.getElementById('eventStartDate').value = data.startDate ? utcToLocalForDatetimeInput(data.startDate) : '';
```
(Repeat for endDate.)

### 3. Change-Detection Guard
* When the modal opens, serialise all form field values into `originalEventData`.
* On **Save** build `currentEventData`. If `JSON.stringify(originalEventData) === JSON.stringify(currentEventData)` ⇒ show localised toast and abort.

### 4. Optional Server Double-Check
Inside `UpdateEvent`, compare incoming DTO with database row; if no differences, return early (extra safety, not strictly required for MVP).

## Key Decisions
* Continue storing UTC (industry best practice).
* Perform all conversion on the client; server remains agnostic.
* Use simple deep compare rather than PATCH; easier to maintain.

## Risks & Mitigations
| Risk | Impact | Likelihood | Mitigation |
|------|--------|------------|-----------|
| Conversion missed in another admin screen | Time still shifts | Low | Audit all `datetime-local` inputs (Phase 0) |
| String compare falsely detects change due to trimming | Redundant save | Low | Normalise strings: `trim()` before compare |
| Missing localisation keys for new toast | UX inconsistency | Medium | Add to both `.resx` files and test |

## Alternatives Considered
1. **Stop using UTC entirely** – simpler but breaks multi-TZ; rejected.
2. **Send PATCH diff** – cleaner network payload but more server code; postponed. 