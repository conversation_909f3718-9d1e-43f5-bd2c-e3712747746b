using Microsoft.Extensions.Configuration;

namespace ParaHockey.E2E.Tests.Infrastructure
{
    public class TestConfiguration
    {
        public string BaseUrl { get; set; } = "http://localhost:5285";
        public bool HeadlessMode { get; set; } = false;
        public int TimeoutSeconds { get; set; } = 30;
        public bool ScreenshotOnFailure { get; set; } = true;
        public string ScreenshotPath { get; set; } = "./Screenshots";
        public int ImplicitWaitSeconds { get; set; } = 10;
        public int ExplicitWaitSeconds { get; set; } = 20;

        public static TestConfiguration Load()
        {
            var configuration = new ConfigurationBuilder()
                .AddJsonFile("appsettings.Test.json", optional: false)
                .AddEnvironmentVariables()
                .Build();

            var config = new TestConfiguration();
            configuration.GetSection("TestSettings").Bind(config);
            
            // Override BaseUrl with TEST_BASE_URL environment variable if provided
            var envBaseUrl = Environment.GetEnvironmentVariable("TEST_BASE_URL");
            if (!string.IsNullOrEmpty(envBaseUrl))
            {
                config.BaseUrl = envBaseUrl;
            }
            
            return config;
        }
    }

    public class BrowserSettings
    {
        public string DefaultBrowser { get; set; } = "Chrome";
        public string[] SupportedBrowsers { get; set; } = { "Chrome", "Firefox", "Edge" };
        public WindowSize WindowSize { get; set; } = new();
        public MobileViewport[] MobileViewports { get; set; } = Array.Empty<MobileViewport>();
    }

    public class WindowSize
    {
        public int Width { get; set; } = 1920;
        public int Height { get; set; } = 1080;
    }

    public class MobileViewport
    {
        public string Name { get; set; } = "";
        public int Width { get; set; }
        public int Height { get; set; }
    }

}