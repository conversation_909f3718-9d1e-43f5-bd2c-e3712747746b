using System.ComponentModel.DataAnnotations;

namespace ParaHockeyApp.Models.Entities
{
    /// <summary>
    /// Event registration entity for tracking member event participation
    /// </summary>
    public class EventRegistration : BaseEntity
    {
        /// <summary>
        /// Event ID
        /// </summary>
        [Required]
        public int EventId { get; set; }

        /// <summary>
        /// Member ID who registered
        /// </summary>
        [Required]
        public int MemberId { get; set; }

        /// <summary>
        /// Registration status
        /// </summary>
        [Required]
        public RegistrationStatus Status { get; set; } = RegistrationStatus.Pending;

        /// <summary>
        /// Date when registration was submitted
        /// </summary>
        [Required]
        public DateTime RegistrationDate { get; set; } = DateTime.UtcNow;

        /// <summary>
        /// Date when registration was confirmed (if applicable)
        /// </summary>
        public DateTime? ConfirmationDate { get; set; }

        /// <summary>
        /// Date when registration was cancelled (if applicable)
        /// </summary>
        public DateTime? CancellationDate { get; set; }

        /// <summary>
        /// Notes from the member during registration
        /// </summary>
        [StringLength(1000)]
        public string? MemberNotes { get; set; }

        /// <summary>
        /// Admin notes about the registration
        /// </summary>
        [StringLength(1000)]
        public string? AdminNotes { get; set; }

        /// <summary>
        /// Whether the member attended the event
        /// </summary>
        public bool? Attended { get; set; }

        /// <summary>
        /// Number of additional guests registered (if event allows)
        /// </summary>
        public int GuestCount { get; set; } = 0;

        /// <summary>
        /// Names of additional guests (if applicable)
        /// </summary>
        [StringLength(500)]
        public string? GuestNames { get; set; }

        /// <summary>
        /// Emergency contact information for this registration
        /// </summary>
        [StringLength(200)]
        public string? EmergencyContact { get; set; }

        /// <summary>
        /// Emergency contact phone number
        /// </summary>
        [StringLength(20)]
        public string? EmergencyPhone { get; set; }

        /// <summary>
        /// Special requirements or dietary restrictions
        /// </summary>
        [StringLength(1000)]
        public string? SpecialRequirements { get; set; }

        /// <summary>
        /// Navigation property to event
        /// </summary>
        public virtual Event Event { get; set; } = null!;

        /// <summary>
        /// Navigation property to member
        /// </summary>
        public virtual Member Member { get; set; } = null!;

        /// <summary>
        /// Computed property for total participants (member + guests)
        /// </summary>
        public int TotalParticipants => 1 + GuestCount;

        /// <summary>
        /// Computed property for registration status display
        /// </summary>
        public string StatusDisplay => Status switch
        {
            RegistrationStatus.Pending => "Pending",
            RegistrationStatus.Confirmed => "Confirmed",
            RegistrationStatus.Cancelled => "Cancelled",
            RegistrationStatus.Waitlisted => "Waitlisted",
            RegistrationStatus.Rejected => "Rejected",
            _ => "Unknown"
        };

        /// <summary>
        /// Computed property to check if registration can be cancelled
        /// </summary>
        public bool CanBeCancelled => Status == RegistrationStatus.Pending || 
                                     Status == RegistrationStatus.Confirmed || 
                                     Status == RegistrationStatus.Waitlisted;

        /// <summary>
        /// Computed property to check if registration is active
        /// </summary>
        public new bool IsActive => Status == RegistrationStatus.Confirmed || Status == RegistrationStatus.Pending;
    }
}