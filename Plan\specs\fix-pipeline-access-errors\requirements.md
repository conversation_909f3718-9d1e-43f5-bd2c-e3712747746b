# Requirements Document

## Introduction
The Azure DevOps pipeline fails at the **Verify Test Application is Running** step. The PowerShell health-check script reports TLS trust errors when it tries to reach the deployed Test site (`http://localhost:8080`, `https://parahockeytest.complys.com`). As a result, all downstream E2E tests are skipped and the release stops.

Goal: make the pipeline reliably confirm that the Test site is up, regardless of TLS certificates, and proceed to E2E tests.

## Requirement 1 — Reliable Health Endpoint
**User story:** As a release engineer, I want a simple HTTP endpoint that always returns `200 OK` so the pipeline can verify the app is running without TLS issues.

**Acceptance criteria**
1. When the Test deployment completes, calling `http://localhost:8080/health` returns `200` within 10 seconds.
2. The endpoint bypasses authentication so the pipeline can poll it anonymously.
3. The same endpoint exists and works in Prod and Dev but is disabled externally via firewall.

## Requirement 2 — Update Pipeline Health-Check Step
**User story:** As a pipeline maintainer, I need the health-check script to call the new `/health` endpoint and ignore TLS certificate errors so that false negatives do not fail the release.

**Acceptance criteria**
1. PowerShell task `Verify Test Application is Running` calls `Invoke-WebRequest` with `-SkipCertificateCheck` or equivalent to ignore self-signed certs.
2. The script retries for up to 30 seconds and fails only if `/health` never returns `200`.
3. On success the pipeline proceeds to E2E tests.

## Requirement 3 — Remove Legacy Port 8080 Check
**User story:** As a developer, I don’t want hard-coded ports causing future failures.

**Acceptance criteria**
1. The health-check script no longer references `http://localhost:8080` directly; it reads the IIS binding from a variable or uses the canonical host name (`parahockeytest.complys.com`).
2. Script is parameterised so Dev/Test/Prod URLs come from pipeline variables.

## Requirement 4 — Certificate Trust (Nice to Have)
**User story:** As a security-minded admin, I want the internal agent to trust the self-signed cert so `Invoke-WebRequest` succeeds without `-SkipCertificateCheck`.

**Acceptance criteria**
1. The agent VM trusts the Test SSL certificate.
2. If this is implemented, the script can remove the skip-TLS flag.

---
End of Requirements v0.1 