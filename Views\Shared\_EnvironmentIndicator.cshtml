@using Microsoft.Extensions.Options
@using Microsoft.Extensions.Localization
@inject IOptions<ParaHockeyApp.Models.Configuration.EnvironmentSettings> EnvSettings
@inject IStringLocalizer<ParaHockeyApp.Resources.SharedResourceMarker> Localizer

@{
    var envSettings = EnvSettings.Value;
    var shouldShowIndicator = !envSettings.IsProduction;
}

@if (shouldShowIndicator)
{
    @if (envSettings.IsDevelopment)
    {
        <!-- Development Environment Indicator - Blue info bar -->
        <div class="alert alert-info alert-dismissible border-0 rounded-0 mb-0" role="alert">
            <div class="container-fluid">
                <div class="d-flex align-items-center">
                    <i class="@envSettings.GetEnvironmentIconClass() me-2"></i>
                    <strong>@Localizer["Environment_Development"]</strong>
                    <span class="mx-2">|</span>
                    <span>@Localizer["Environment_SqlServer"]</span>
                    @if (envSettings.ShowDevelopmentTools)
                    {
                        <span class="mx-2">|</span>
                        <small class="text-muted">@Localizer["Environment_DevToolsAvailable"]</small>
                    }
                    <button type="button" class="btn-close btn-close-white ms-auto" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
            </div>
        </div>
    }
    else if (envSettings.IsTest)
    {
        <!-- Test Environment Indicator - Red warning with prominent TEST labels -->
        <div class="alert alert-danger border-0 rounded-0 mb-0" role="alert">
            <div class="container-fluid">
                <div class="d-flex align-items-center">
                    <i class="@envSettings.GetEnvironmentIconClass() me-2"></i>
                    <strong class="text-uppercase">@Localizer["Environment_Test"]</strong>
                    <span class="badge bg-warning text-dark mx-2">TEST</span>
                    <span>@Localizer["Environment_SqlServer"]</span>
                    @if (!string.IsNullOrWhiteSpace(envSettings.BannerText))
                    {
                        <span class="mx-2">|</span>
                        <span>@envSettings.BannerText</span>
                    }
                    @if (envSettings.ShowDevelopmentTools)
                    {
                        <span class="mx-2">|</span>
                        <small>@Localizer["Environment_DevToolsAvailable"]</small>
                    }
                </div>
            </div>
        </div>
    }
}

<style>
    .alert-info {
        background-color: #d1ecf1 !important;
        border-color: #bee5eb !important;
        color: #0c5460 !important;
    }
    
    .alert-danger {
        background-color: #f8d7da !important;
        border-color: #f5c6cb !important;
        color: #721c24 !important;
    }
    
    .environment-indicator {
        position: sticky;
        top: 0;
        z-index: 1030;
        box-shadow: 0 2px 4px rgba(0,0,0,.1);
    }
    
    @@media (max-width: 768px) {
        .environment-indicator .d-flex {
            flex-direction: column;
            align-items: flex-start !important;
        }
        
        .environment-indicator .badge,
        .environment-indicator .mx-2 {
            margin: 2px 0 !important;
        }
    }
</style>