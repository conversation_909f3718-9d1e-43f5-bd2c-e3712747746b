using Xunit;
using FluentAssertions;
using System.Net.Http;

namespace ParaHockey.E2E.Tests.Tests
{
    public class SimpleWorkingTests
    {
        private readonly string _baseUrl = "http://localhost:5285";

        [Fact]
        public void Website_ShouldBeRunning_AndRespond()
        {
            // Test that just checks if the website responds
            using var client = new HttpClient();
            client.Timeout = TimeSpan.FromSeconds(10);
            
            var response = client.GetAsync(_baseUrl).Result;
            
            response.IsSuccessStatusCode.Should().BeTrue("Website should be running and respond with success status");
            
            var content = response.Content.ReadAsStringAsync().Result;
            content.Should().Contain("Parahockey", "Homepage should contain Parahockey branding");
        }

        [Fact]
        public void RegistrationPage_ShouldBeAccessible()
        {
            // Test that registration page loads
            using var client = new HttpClient();
            client.Timeout = TimeSpan.FromSeconds(10);
            
            var response = client.GetAsync($"{_baseUrl}/Members/Register").Result;
            
            response.IsSuccessStatusCode.Should().BeTrue("Registration page should be accessible");
            
            var content = response.Content.ReadAsStringAsync().Result;
            content.Should().Contain("FirstName", "Registration page should contain form fields");
            content.Should().Contain("Email", "Registration page should contain email field");
        }

        [Fact]
        public void ApiEndpoint_ShouldBeAccessible()
        {
            // Test that API endpoints work
            using var client = new HttpClient();
            client.Timeout = TimeSpan.FromSeconds(10);
            
            // Test health endpoint or similar if available
            var response = client.GetAsync($"{_baseUrl}/").Result;
            
            response.IsSuccessStatusCode.Should().BeTrue("Base endpoint should be accessible");
        }
    }
}