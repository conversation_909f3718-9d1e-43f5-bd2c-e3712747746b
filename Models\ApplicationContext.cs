using Microsoft.AspNetCore.Identity.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore;
using ParaHockeyApp.Models.Entities;

namespace ParaHockeyApp.Models
{
    /// <summary>
    /// Entity Framework DbContext for Para Hockey application
    /// Supports multi-environment configuration (SQLite for dev, SQL Server for test/prod)
    /// </summary>
    public class ApplicationContext : IdentityDbContext<AppUser>
    {
        public ApplicationContext(DbContextOptions<ApplicationContext> options) : base(options)
        {
        }


        // Real Para Hockey entities
        public DbSet<Member> Members { get; set; } = null!;
        public DbSet<Gender> Genders { get; set; } = null!;
        public DbSet<Province> Provinces { get; set; } = null!;
        public DbSet<PhoneType> PhoneTypes { get; set; } = null!;
        public DbSet<RegistrationType> RegistrationTypes { get; set; } = null!;
        public DbSet<MemberLog> MemberLogs { get; set; } = null!; // Legacy - will be replaced by AuditLogs
        public DbSet<AuditLog> AuditLogs { get; set; } = null!; // New universal audit system
        public DbSet<Parent> Parents { get; set; } = null!;
        public DbSet<EmergencyContact> EmergencyContacts { get; set; } = null!;
        public DbSet<AdminUser> AdminUsers { get; set; } = null!;
        public DbSet<AdminTypeEntity> AdminTypes { get; set; } = null!;

        // Event system entities
        public DbSet<Event> Events { get; set; } = null!;
        public DbSet<EventCategory> EventCategories { get; set; } = null!;
        public DbSet<EventRegistration> EventRegistrations { get; set; } = null!;

        // Search system entities
        public DbSet<SavedSearch> SavedSearches { get; set; } = null!;

        // Member import system entities
        public DbSet<MemberImportBatch> MemberImportBatches { get; set; } = null!;
        public DbSet<TempMember> TempMembers { get; set; } = null!;

        // Page audit system entities
        public DbSet<PageInventory> PageInventories { get; set; } = null!;
        public DbSet<PageInfo> PageInfos { get; set; } = null!;
        public DbSet<PageAuditResult> PageAuditResults { get; set; } = null!;
        public DbSet<AuditFinding> AuditFindings { get; set; } = null!;

        // Future entities will be added here
        // Example: public DbSet<Game> Games { get; set; }
        // Example: public DbSet<Team> Teams { get; set; }

        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            base.OnModelCreating(modelBuilder);

            // This configures the unique constraint for HQc_Id on the Members table.
            // This filtered unique index enforces uniqueness only for non-null values.
            modelBuilder.Entity<Member>()
                .HasIndex(m => m.HQc_Id)
                .IsUnique()
                .HasFilter("[HQc_Id] IS NOT NULL");

            // Add indexes for filter queries performance
            modelBuilder.Entity<Member>()
                .HasIndex(m => m.City)
                .HasDatabaseName("IX_Members_City");

            modelBuilder.Entity<Member>()
                .HasIndex(m => m.IsActive)
                .HasDatabaseName("IX_Members_IsActive");

            // Composite index for common filter combinations
            modelBuilder.Entity<Member>()
                .HasIndex(m => new { m.IsActive, m.City })
                .HasDatabaseName("IX_Members_IsActive_City");

            modelBuilder.Entity<Member>()
                .HasIndex(m => new { m.IsActive, m.RegistrationTypeId })
                .HasDatabaseName("IX_Members_IsActive_RegistrationTypeId");

            // Configure MemberLog relationships and primary key
            modelBuilder.Entity<MemberLog>()
                .HasKey(ml => ml.NoLog); // Set NoLog as primary key
                
            modelBuilder.Entity<MemberLog>()
                .HasOne(ml => ml.Member)
                .WithMany(m => m.MemberLogs)
                .HasForeignKey(ml => ml.MemberId)
                .OnDelete(DeleteBehavior.Cascade); // When a member is deleted, delete all their logs

            // Add index on MemberId for faster log queries
            modelBuilder.Entity<MemberLog>()
                .HasIndex(ml => ml.MemberId);

            // Add index on LogDate for chronological queries
            modelBuilder.Entity<MemberLog>()
                .HasIndex(ml => ml.LogDate);
                
            // Add index on EditorId for faster editor queries
            modelBuilder.Entity<MemberLog>()
                .HasIndex(ml => ml.EditorId);

            // Configure AdminUser unique email constraint
            modelBuilder.Entity<AdminUser>()
                .HasIndex(a => a.Email)
                .IsUnique()
                .HasDatabaseName("IX_AdminUsers_Email");

            // Configure AdminUser relationship to AdminTypes
            modelBuilder.Entity<AdminUser>()
                .HasOne(a => a.AdminTypeEntity)
                .WithMany(at => at.AdminUsers)
                .HasForeignKey(a => a.AdminTypeId)
                .OnDelete(DeleteBehavior.Restrict); // Don't allow deleting AdminTypes if AdminUsers exist

            // Configure AdminTypes unique TypeCode
            modelBuilder.Entity<AdminTypeEntity>()
                .HasIndex(at => at.TypeCode)
                .IsUnique()
                .HasDatabaseName("IX_AdminTypes_TypeCode");

            // Configure Parent relationship
            modelBuilder.Entity<Parent>()
                .HasOne(p => p.Member)
                .WithMany()
                .HasForeignKey(p => p.MemberId)
                .OnDelete(DeleteBehavior.Cascade);

            // Configure EmergencyContact relationship
            modelBuilder.Entity<EmergencyContact>()
                .HasOne(ec => ec.Member)
                .WithMany()
                .HasForeignKey(ec => ec.MemberId)
                .OnDelete(DeleteBehavior.Cascade);

            // Configure AuditLog relationships
            modelBuilder.Entity<AuditLog>()
                .HasOne(al => al.PerformedByMember)
                .WithMany()
                .HasForeignKey(al => al.PerformedByMemberId)
                .OnDelete(DeleteBehavior.Restrict); // Don't delete audit logs if member is deleted

            modelBuilder.Entity<AuditLog>()
                .HasOne(al => al.PerformedByAdmin)
                .WithMany()
                .HasForeignKey(al => al.PerformedByAdminId)
                .OnDelete(DeleteBehavior.Restrict); // Don't delete audit logs if admin is deleted

            // Add indexes on AuditLog for better query performance
            modelBuilder.Entity<AuditLog>()
                .HasIndex(al => new { al.EntityType, al.EntityId })
                .HasDatabaseName("IX_AuditLogs_Entity");

            modelBuilder.Entity<AuditLog>()
                .HasIndex(al => al.Timestamp)
                .HasDatabaseName("IX_AuditLogs_Timestamp");

            modelBuilder.Entity<AuditLog>()
                .HasIndex(al => al.PerformedByMemberId)
                .HasDatabaseName("IX_AuditLogs_PerformedByMember");

            modelBuilder.Entity<AuditLog>()
                .HasIndex(al => al.PerformedByAdminId)
                .HasDatabaseName("IX_AuditLogs_PerformedByAdmin");

            // Configure Event system relationships
            modelBuilder.Entity<Event>()
                .HasOne(e => e.EventCategory)
                .WithMany(ec => ec.Events)
                .HasForeignKey(e => e.EventCategoryId)
                .OnDelete(DeleteBehavior.Restrict);

            modelBuilder.Entity<EventRegistration>()
                .HasOne(er => er.Event)
                .WithMany(e => e.EventRegistrations)
                .HasForeignKey(er => er.EventId)
                .OnDelete(DeleteBehavior.Cascade);

            modelBuilder.Entity<EventRegistration>()
                .HasOne(er => er.Member)
                .WithMany()
                .HasForeignKey(er => er.MemberId)
                .OnDelete(DeleteBehavior.Cascade);

            // Add indexes for Event system
            modelBuilder.Entity<Event>()
                .HasIndex(e => e.StartDate)
                .HasDatabaseName("IX_Events_StartDate");

            modelBuilder.Entity<Event>()
                .HasIndex(e => e.EventCategoryId)
                .HasDatabaseName("IX_Events_EventCategoryId");

            modelBuilder.Entity<Event>()
                .HasIndex(e => e.IsPublished)
                .HasDatabaseName("IX_Events_IsPublished");

            modelBuilder.Entity<EventRegistration>()
                .HasIndex(er => new { er.EventId, er.MemberId })
                .IsUnique()
                .HasDatabaseName("IX_EventRegistrations_EventId_MemberId");

            modelBuilder.Entity<EventRegistration>()
                .HasIndex(er => er.Status)
                .HasDatabaseName("IX_EventRegistrations_Status");

            modelBuilder.Entity<EventCategory>()
                .HasIndex(ec => ec.DisplayOrder)
                .HasDatabaseName("IX_EventCategories_DisplayOrder");

            // Configure Page Audit system relationships
            modelBuilder.Entity<PageInfo>()
                .HasOne(pi => pi.PageInventory)
                .WithMany(pi => pi.Pages)
                .HasForeignKey(pi => pi.PageInventoryId)
                .OnDelete(DeleteBehavior.Cascade);

            modelBuilder.Entity<PageAuditResult>()
                .HasOne(par => par.PageInfo)
                .WithMany(pi => pi.AuditResults)
                .HasForeignKey(par => par.PageInfoId)
                .OnDelete(DeleteBehavior.Cascade);

            modelBuilder.Entity<AuditFinding>()
                .HasOne(af => af.PageAuditResult)
                .WithMany(par => par.Findings)
                .HasForeignKey(af => af.PageAuditResultId)
                .OnDelete(DeleteBehavior.Cascade);

            // Add indexes for Page Audit system
            modelBuilder.Entity<PageInventory>()
                .HasIndex(pi => pi.Version)
                .HasDatabaseName("IX_PageInventories_Version");

            modelBuilder.Entity<PageInfo>()
                .HasIndex(pi => pi.Name)
                .HasDatabaseName("IX_PageInfos_Name");

            modelBuilder.Entity<PageInfo>()
                .HasIndex(pi => new { pi.Controller, pi.Action })
                .HasDatabaseName("IX_PageInfos_Controller_Action");

            modelBuilder.Entity<PageInfo>()
                .HasIndex(pi => pi.Priority)
                .HasDatabaseName("IX_PageInfos_Priority");

            modelBuilder.Entity<PageInfo>()
                .HasIndex(pi => pi.IsModernized)
                .HasDatabaseName("IX_PageInfos_IsModernized");

            modelBuilder.Entity<PageAuditResult>()
                .HasIndex(par => new { par.PageInfoId, par.AuditVersion })
                .IsUnique()
                .HasDatabaseName("IX_PageAuditResults_PageInfo_Version");

            modelBuilder.Entity<PageAuditResult>()
                .HasIndex(par => par.Status)
                .HasDatabaseName("IX_PageAuditResults_Status");

            modelBuilder.Entity<AuditFinding>()
                .HasIndex(af => af.Severity)
                .HasDatabaseName("IX_AuditFindings_Severity");

            modelBuilder.Entity<AuditFinding>()
                .HasIndex(af => af.IsResolved)
                .HasDatabaseName("IX_AuditFindings_IsResolved");

            modelBuilder.Entity<AuditFinding>()
                .HasIndex(af => af.Category)
                .HasDatabaseName("IX_AuditFindings_Category");

            // Configure Member Import system relationships
            modelBuilder.Entity<TempMember>()
                .HasOne(tm => tm.ImportBatch)
                .WithMany(ib => ib.TempMembers)
                .HasForeignKey(tm => tm.ImportBatchId)
                .OnDelete(DeleteBehavior.Cascade);

            modelBuilder.Entity<TempMember>()
                .HasOne(tm => tm.ExistingMember)
                .WithMany()
                .HasForeignKey(tm => tm.ExistingMemberId)
                .OnDelete(DeleteBehavior.Restrict);

            modelBuilder.Entity<TempMember>()
                .HasOne(tm => tm.Gender)
                .WithMany()
                .HasForeignKey(tm => tm.GenderId)
                .OnDelete(DeleteBehavior.Restrict);

            modelBuilder.Entity<TempMember>()
                .HasOne(tm => tm.Province)
                .WithMany()
                .HasForeignKey(tm => tm.ProvinceId)
                .OnDelete(DeleteBehavior.Restrict);

            modelBuilder.Entity<TempMember>()
                .HasOne(tm => tm.PhoneType)
                .WithMany()
                .HasForeignKey(tm => tm.PhoneTypeId)
                .OnDelete(DeleteBehavior.Restrict);

            modelBuilder.Entity<TempMember>()
                .HasOne(tm => tm.RegistrationType)
                .WithMany()
                .HasForeignKey(tm => tm.RegistrationTypeId)
                .OnDelete(DeleteBehavior.Restrict);

            // Add indexes for Member Import system
            modelBuilder.Entity<MemberImportBatch>()
                .HasIndex(ib => ib.ImportBatchId)
                .IsUnique()
                .HasDatabaseName("IX_MemberImportBatches_ImportBatchId");

            modelBuilder.Entity<MemberImportBatch>()
                .HasIndex(ib => ib.UploadedBy)
                .HasDatabaseName("IX_MemberImportBatches_UploadedBy");

            modelBuilder.Entity<MemberImportBatch>()
                .HasIndex(ib => ib.UploadedAtUtc)
                .HasDatabaseName("IX_MemberImportBatches_UploadedAtUtc");

            modelBuilder.Entity<TempMember>()
                .HasIndex(tm => tm.TempMemberId)
                .IsUnique()
                .HasDatabaseName("IX_TempMembers_TempMemberId");

            modelBuilder.Entity<TempMember>()
                .HasIndex(tm => tm.ImportBatchId)
                .HasDatabaseName("IX_TempMembers_ImportBatchId");

            modelBuilder.Entity<TempMember>()
                .HasIndex(tm => tm.Status)
                .HasDatabaseName("IX_TempMembers_Status");

            modelBuilder.Entity<TempMember>()
                .HasIndex(tm => tm.ExistingMemberId)
                .HasDatabaseName("IX_TempMembers_ExistingMemberId");

            modelBuilder.Entity<TempMember>()
                .HasIndex(tm => tm.Email)
                .HasDatabaseName("IX_TempMembers_Email");

            modelBuilder.Entity<TempMember>()
                .HasIndex(tm => new { tm.LastName, tm.FirstName, tm.DateOfBirth })
                .HasDatabaseName("IX_TempMembers_Name_DOB");

            modelBuilder.Entity<TempMember>()
                .HasIndex(tm => tm.HcrNumber)
                .HasDatabaseName("IX_TempMembers_HcrNumber");

            // Configure BaseEntity audit relationships
            // These apply to all entities inheriting from BaseEntity
            foreach (var entityType in modelBuilder.Model.GetEntityTypes())
            {
                if (typeof(BaseEntity).IsAssignableFrom(entityType.ClrType))
                {
                    // Configure CreatedBy relationships
                    modelBuilder.Entity(entityType.ClrType)
                        .HasOne(typeof(Member), "CreatedByMember")
                        .WithMany()
                        .HasForeignKey("CreatedByMemberId")
                        .OnDelete(DeleteBehavior.Restrict);

                    modelBuilder.Entity(entityType.ClrType)
                        .HasOne(typeof(AdminUser), "CreatedByAdmin")
                        .WithMany()
                        .HasForeignKey("CreatedByAdminId")
                        .OnDelete(DeleteBehavior.Restrict);

                    // Configure ModifiedBy relationships
                    modelBuilder.Entity(entityType.ClrType)
                        .HasOne(typeof(Member), "ModifiedByMember")
                        .WithMany()
                        .HasForeignKey("ModifiedByMemberId")
                        .OnDelete(DeleteBehavior.Restrict);

                    modelBuilder.Entity(entityType.ClrType)
                        .HasOne(typeof(AdminUser), "ModifiedByAdmin")
                        .WithMany()
                        .HasForeignKey("ModifiedByAdminId")
                        .OnDelete(DeleteBehavior.Restrict);
                }
            }

            // --- Data Seeding ---
            // MOVED TO MIGRATION: SeedLookupData migration now handles seeding
            // This ensures data is seeded consistently across all environments
            // (SQLite dev, SQL Server test/prod) via migrations

            /*
            // OLD: HasData seeding (replaced by migration-based seeding)
            modelBuilder.Entity<Gender>().HasData(
                new Gender { Id = 1, DisplayNameKey = "Gender_Male" },
                new Gender { Id = 2, DisplayNameKey = "Gender_Female" },
                new Gender { Id = 3, DisplayNameKey = "Gender_Other" }
            );

            modelBuilder.Entity<PhoneType>().HasData(
                new PhoneType { Id = 1, DisplayNameKey = "PhoneType_Mobile" },
                new PhoneType { Id = 2, DisplayNameKey = "PhoneType_Other" }
            );

            modelBuilder.Entity<RegistrationType>().HasData(
                new RegistrationType { Id = 1, DisplayNameKey = "RegType_Junior", DescriptionKey = "JuniorSubtext" },
                new RegistrationType { Id = 2, DisplayNameKey = "RegType_Development", DescriptionKey = "DevelopmentSubtext" },
                new RegistrationType { Id = 3, DisplayNameKey = "RegType_Elite", DescriptionKey = "EliteSubtext" },
                new RegistrationType { Id = 4, DisplayNameKey = "RegType_Coach", DescriptionKey = "CoachSubtext" },
                new RegistrationType { Id = 5, DisplayNameKey = "RegType_Volunteer", DescriptionKey = "VolunteerSubtext" }
            );

            modelBuilder.Entity<Province>().HasData(
                new Province { Id = 1, Code = "QC", DisplayNameKey = "Province_QC" },
                new Province { Id = 2, Code = "ON", DisplayNameKey = "Province_ON" },
                new Province { Id = 3, Code = "NB", DisplayNameKey = "Province_NB" },
                new Province { Id = 4, Code = "AB", DisplayNameKey = "Province_AB" },
                new Province { Id = 5, Code = "BC", DisplayNameKey = "Province_BC" },
                new Province { Id = 6, Code = "MB", DisplayNameKey = "Province_MB" },
                new Province { Id = 7, Code = "NL", DisplayNameKey = "Province_NL" },
                new Province { Id = 8, Code = "NS", DisplayNameKey = "Province_NS" },
                new Province { Id = 9, Code = "NT", DisplayNameKey = "Province_NT" },
                new Province { Id = 10, Code = "NU", DisplayNameKey = "Province_NU" },
                new Province { Id = 11, Code = "PE", DisplayNameKey = "Province_PE" },
                new Province { Id = 12, Code = "SK", DisplayNameKey = "Province_SK" },
                new Province { Id = 13, Code = "YT", DisplayNameKey = "Province_YT" }
            );
            */
        }

        // NOTE: Audit field handling is now done by AuditInterceptor
        // The interceptor automatically handles audit fields and creates audit log entries

        /// <summary>
        /// Seeds the Master Admin user if it doesn't exist
        /// </summary>
        public async Task SeedMasterAdminAsync()
        {
            const string masterAdminEmail = "<EMAIL>";
            
            var existingAdmin = await AdminUsers.FirstOrDefaultAsync(a => a.Email == masterAdminEmail);
            
            if (existingAdmin == null)
            {
                var masterAdmin = new AdminUser
                {
                    Email = masterAdminEmail,
                    Name = "Admin TI",
                    AdminType = AdminType.Master,
                    IsActive = true,
                    DateCreated = DateTime.UtcNow
                };
                
                AdminUsers.Add(masterAdmin);
                await SaveChangesAsync();
                
                Console.WriteLine($"✅ Master Admin created: {masterAdminEmail} with AdminType = 9");
            }
            else if (existingAdmin.AdminType != AdminType.Master || !existingAdmin.IsActive)
            {
                existingAdmin.AdminType = AdminType.Master;
                existingAdmin.Name = "Admin TI";
                existingAdmin.IsActive = true;
                await SaveChangesAsync();
                
                Console.WriteLine($"✅ Master Admin status updated: {masterAdminEmail} with AdminType = 9");
            }
            else
            {
                Console.WriteLine($"ℹ️ Master Admin already exists: {masterAdminEmail}");
            }
        }

        // OLD: UpdateAuditFields method removed - now handled by AuditInterceptor
    }
}